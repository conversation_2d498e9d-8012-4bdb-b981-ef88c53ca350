{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/product.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/autocomplete\";\nimport * as i6 from \"@angular/material/core\";\nfunction SearchPriceComponent_mat_option_34_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 93);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r13.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_34_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 94);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r13.city);\n  }\n}\nconst _c0 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"fa-flag\": a0,\n    \"fa-city\": a1,\n    \"fa-building\": a2,\n    \"fa-home\": a3,\n    \"fa-plane\": a4\n  };\n};\nfunction SearchPriceComponent_mat_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 75)(1, \"div\", 86)(2, \"div\", 87);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 88);\n    i0.ɵɵtemplate(5, SearchPriceComponent_mat_option_34_span_5_Template, 2, 1, \"span\", 89);\n    i0.ɵɵtemplate(6, SearchPriceComponent_mat_option_34_span_6_Template, 2, 1, \"span\", 90);\n    i0.ɵɵelementStart(7, \"span\", 91);\n    i0.ɵɵelement(8, \"i\", 92);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r13);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r13.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r13.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r13.type === 5 && location_r13.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r13.type === 1, location_r13.type === 2, location_r13.type === 3, location_r13.type === 4, location_r13.type === 5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r13.type === 1 ? \"Country\" : location_r13.type === 2 ? \"City\" : location_r13.type === 3 ? \"Town\" : location_r13.type === 4 ? \"Village\" : location_r13.type === 5 ? \"Airport\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95);\n    i0.ɵɵelement(1, \"i\", 96);\n    i0.ɵɵtext(2, \" Please select a departure location \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_mat_option_47_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 93);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r18.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_47_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 94);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r18.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 75)(1, \"div\", 86)(2, \"div\", 87);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 88);\n    i0.ɵɵtemplate(5, SearchPriceComponent_mat_option_47_span_5_Template, 2, 1, \"span\", 89);\n    i0.ɵɵtemplate(6, SearchPriceComponent_mat_option_47_span_6_Template, 2, 1, \"span\", 90);\n    i0.ɵɵelementStart(7, \"span\", 91);\n    i0.ɵɵelement(8, \"i\", 92);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r18);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r18.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r18.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r18.type === 5 && location_r18.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r18.type === 1, location_r18.type === 2, location_r18.type === 3, location_r18.type === 4, location_r18.type === 5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r18.type === 1 ? \"Country\" : location_r18.type === 2 ? \"City\" : location_r18.type === 3 ? \"Town\" : location_r18.type === 4 ? \"Village\" : location_r18.type === 5 ? \"Airport\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95);\n    i0.ɵɵelement(1, \"i\", 96);\n    i0.ɵɵtext(2, \" Please select an arrival location \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95);\n    i0.ɵɵelement(1, \"i\", 96);\n    i0.ɵɵtext(2, \" Please select a date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_option_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r23.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(type_r23.label);\n  }\n}\nfunction SearchPriceComponent_option_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", flightClass_r24.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(flightClass_r24.label);\n  }\n}\nfunction SearchPriceComponent_i_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 97);\n  }\n}\nfunction SearchPriceComponent_span_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Search\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵelement(1, \"div\", 99);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_157_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104)(1, \"div\", 105)(2, \"div\", 106);\n    i0.ɵɵelement(3, \"i\", 107)(4, \"div\", 108)(5, \"div\", 108)(6, \"div\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Searching for the best flights...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SearchPriceComponent_div_157_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"div\", 110);\n    i0.ɵɵelement(2, \"i\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Oops! Something went wrong\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 95);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_157_div_2_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.onSearch());\n    });\n    i0.ɵɵelement(8, \"i\", 112);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r26.errorMessage);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No flights found for your search. Please modify your criteria.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_p_6_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 122);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r35.currentFilter, \")\");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"span\", 120);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" flights found \");\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_157_div_3_p_6_span_4_Template, 2, 1, \"span\", 121);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r31.filteredResults.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.currentFilter !== \"recommended\");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_7_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 126);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_157_div_3_div_7_button_2_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const filter_r37 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r38.applyFilter(filter_r37.value));\n    });\n    i0.ɵɵelement(1, \"i\", 92);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r37 = ctx.$implicit;\n    const ctx_r36 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"active\", ctx_r36.currentFilter === filter_r37.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", filter_r37.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", filter_r37.label, \" \");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"div\", 124);\n    i0.ɵɵtemplate(2, SearchPriceComponent_div_157_div_3_div_7_button_2_Template, 3, 4, \"button\", 125);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r32.filterOptions);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 177);\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", flight_r40.items[0].airline.thumbnailFull, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 178);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 179);\n    i0.ɵɵelement(1, \"i\", 180);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r40.items && flight_r40.items[0] && flight_r40.items[0].flightProvider && flight_r40.items[0].flightProvider.displayName || flight_r40.items && flight_r40.items[0] && flight_r40.items[0].flightProvider && flight_r40.items[0].flightProvider.name || \"Provider \" + flight_r40.provider, \" \");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 181);\n    i0.ɵɵelement(1, \"i\", 182);\n    i0.ɵɵtext(2, \" Direct \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 181);\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r40.items[0].flightClass.name, \" \");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 183);\n    i0.ɵɵelement(1, \"i\", 184);\n    i0.ɵɵtext(2, \" Branded Fare \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"fa-check-circle\": a0,\n    \"fa-exclamation-triangle\": a1\n  };\n};\nfunction SearchPriceComponent_div_157_div_3_div_9_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 185);\n    i0.ɵɵelement(1, \"i\", 92);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    const ctx_r47 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c1, ctx_r47.isFlightAvailable(flight_r40), !ctx_r47.isFlightAvailable(flight_r40)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r47.isFlightAvailable(flight_r40) ? \"Available\" : \"Not available\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 186);\n    i0.ɵɵelement(1, \"i\", 160);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    const ctx_r48 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Expires: \", ctx_r48.formatExpirationDate(flight_r40.offers[0].expiresOn), \" \");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 187)(1, \"span\", 188);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r40.items[0].stopCount);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" stop\", flight_r40.items[0].stopCount > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 189);\n    i0.ɵɵtext(1, \" Direct flight \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_div_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 199);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r68 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r68.weight, \" kg\");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 199);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r68 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r68.piece, \" piece(s)\");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 195);\n    i0.ɵɵelement(1, \"i\", 73);\n    i0.ɵɵelementStart(2, \"div\", 196)(3, \"span\", 197);\n    i0.ɵɵtext(4, \"Checked Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_div_1_span_5_Template, 2, 1, \"span\", 198);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_div_1_span_6_Template, 2, 1, \"span\", 198);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const baggage_r68 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", baggage_r68.weight > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", baggage_r68.piece > 0);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_div_1_Template, 7, 2, \"div\", 194);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r63 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, ctx_r63.filterBaggageByType(flight_r40.items[0].baggageInformations, 2), 0, 2));\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_div_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 199);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r75 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r75.piece, \" piece(s)\");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 199);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r75 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r75.weight, \" kg\");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 201);\n    i0.ɵɵelement(1, \"i\", 202);\n    i0.ɵɵelementStart(2, \"div\", 196)(3, \"span\", 197);\n    i0.ɵɵtext(4, \"Cabin Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_div_1_span_5_Template, 2, 1, \"span\", 198);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_div_1_span_6_Template, 2, 1, \"span\", 198);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const baggage_r75 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", baggage_r75.piece > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", baggage_r75.weight > 0);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_div_1_Template, 7, 2, \"div\", 200);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r64 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, ctx_r64.filterBaggageByType(flight_r40.items[0].baggageInformations, 1), 0, 1));\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_55_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 201);\n    i0.ɵɵelement(1, \"i\", 202);\n    i0.ɵɵelementStart(2, \"div\", 196)(3, \"span\", 197);\n    i0.ɵɵtext(4, \"Cabin Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 199);\n    i0.ɵɵtext(6, \"Included\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_55_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 203)(1, \"span\");\n    i0.ɵɵtext(2, \"No detailed baggage information available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 73);\n    i0.ɵɵtext(3, \" Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 191);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_Template, 3, 5, \"ng-container\", 56);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_Template, 3, 5, \"ng-container\", 56);\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_157_div_3_div_9_div_55_div_7_Template, 7, 0, \"div\", 192);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, SearchPriceComponent_div_157_div_3_div_9_div_55_div_8_Template, 3, 0, \"div\", 193);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    const ctx_r51 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.items[0].baggageInformations && flight_r40.items[0].baggageInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.items[0].baggageInformations && flight_r40.items[0].baggageInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !flight_r40.items[0].baggageInformations || flight_r40.items[0].baggageInformations.length === 0 || ctx_r51.filterBaggageByType(flight_r40.items[0].baggageInformations, 1).length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !flight_r40.items[0].baggageInformations || flight_r40.items[0].baggageInformations.length === 0);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_56_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 203)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const service_r83 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(service_r83.name || \"Service\");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 204);\n    i0.ɵɵtext(3, \" Services\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_157_div_3_div_9_div_56_div_4_Template, 3, 1, \"div\", 205);\n    i0.ɵɵpipe(5, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(5, 1, flight_r40.items[0].services, 0, 3));\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_57_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 203)(1, \"span\");\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"small\", 206);\n    i0.ɵɵtext(5, \"(from API response)\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(flight_r40.offers[0].reservableInfo.reservable ? \"fas fa-check-circle text-success\" : \"fas fa-times-circle text-danger\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r40.offers[0].reservableInfo.reservable ? \"Reservable\" : \"Not reservable\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 190)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 180);\n    i0.ɵɵtext(3, \" Offer Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_157_div_3_div_9_div_57_div_4_Template, 6, 3, \"div\", 193);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.offers[0].reservableInfo);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_63_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 215)(1, \"span\", 216);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 217);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r91 = ctx.$implicit;\n    const ctx_r89 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r89.getPassengerTypeName(item_r91.passengerType), \" (x\", item_r91.passengerCount, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 3, item_r91.price.amount, item_r91.price.currency));\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_63_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 218)(1, \"span\", 219);\n    i0.ɵɵtext(2, \"Service Fee\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 220);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, flight_r40.offers[0].serviceFee.amount, flight_r40.offers[0].serviceFee.currency));\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_63_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 209)(1, \"h4\");\n    i0.ɵɵtext(2, \"Price Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_157_div_3_div_9_div_63_div_1_div_3_Template, 6, 6, \"div\", 210);\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_157_div_3_div_9_div_63_div_1_div_4_Template, 6, 4, \"div\", 211);\n    i0.ɵɵelementStart(5, \"div\", 212)(6, \"span\", 213);\n    i0.ɵɵtext(7, \"Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 214);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", flight_r40.offers[0].priceBreakDown.items);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.offers[0].serviceFee);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 3, flight_r40.offers[0].price.amount, flight_r40.offers[0].price.currency));\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 207);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_157_div_3_div_9_div_63_div_1_Template, 11, 6, \"div\", 208);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.offers[0].priceBreakDown && flight_r40.offers[0].priceBreakDown.items);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_64_div_6_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 236);\n    i0.ɵɵelement(1, \"i\", 237);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r99 = i0.ɵɵnextContext();\n    const segment_r96 = ctx_r99.$implicit;\n    const i_r97 = ctx_r99.index;\n    const flight_r40 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r98 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r98.calculateLayoverTime(segment_r96, flight_r40.items[0].segments[i_r97 + 1]), \" layover in \", segment_r96.arrival && segment_r96.arrival.city && segment_r96.arrival.city.name || \"connecting city\", \"\");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_64_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 226)(1, \"div\", 227)(2, \"span\", 228);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 229);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 230);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 231)(9, \"div\", 232)(10, \"div\", 148);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 149)(13, \"span\", 150);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 151);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 233)(18, \"div\", 153);\n    i0.ɵɵelement(19, \"span\", 154);\n    i0.ɵɵelementStart(20, \"div\", 155);\n    i0.ɵɵelement(21, \"span\", 156);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"span\", 158);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 159);\n    i0.ɵɵelement(24, \"i\", 160);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 234)(27, \"div\", 148);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 149)(30, \"span\", 150);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 151);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(34, SearchPriceComponent_div_157_div_3_div_9_div_64_div_6_div_34_Template, 4, 2, \"div\", 235);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r96 = ctx.$implicit;\n    const i_r97 = ctx.index;\n    const flight_r40 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r95 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Segment \", i_r97 + 1, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r96.airline && segment_r96.airline.name || \"Airline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r96.flightNo);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(segment_r96.departure ? ctx_r95.formatDate(segment_r96.departure.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r96.departure && segment_r96.departure.airport && segment_r96.departure.airport.code || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r96.departure && segment_r96.departure.city && segment_r96.departure.city.name || \"N/A\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r95.formatDuration(segment_r96.duration), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r96.arrival ? ctx_r95.formatDate(segment_r96.arrival.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r96.arrival && segment_r96.arrival.airport && segment_r96.arrival.airport.code || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r96.arrival && segment_r96.arrival.city && segment_r96.arrival.city.name || \"N/A\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r97 < flight_r40.items[0].segments.length - 1);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 221)(1, \"details\", 222)(2, \"summary\", 223);\n    i0.ɵɵelement(3, \"i\", 22);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 224);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_157_div_3_div_9_div_64_div_6_Template, 35, 11, \"div\", 225);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Flight Segments (\", flight_r40.items[0].segments.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", flight_r40.items[0].segments);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_65_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 244);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r40.offers[0].brandedFare.description, \" \");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_65_div_7_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 250);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r107 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", feature_r107.explanations[0].text, \" \");\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_65_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 247)(1, \"div\", 248);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_157_div_3_div_9_div_65_div_7_div_3_div_3_Template, 2, 1, \"div\", 249);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r107 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r107.commercialName || \"Feature\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", feature_r107.explanations && feature_r107.explanations.length > 0);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_65_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 245)(1, \"h4\");\n    i0.ɵɵtext(2, \"Features\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_157_div_3_div_9_div_65_div_7_div_3_Template, 4, 2, \"div\", 246);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", flight_r40.offers[0].brandedFare.features);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 238)(1, \"details\", 239)(2, \"summary\", 240);\n    i0.ɵɵelement(3, \"i\", 184);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 241);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_157_div_3_div_9_div_65_div_6_Template, 2, 1, \"div\", 242);\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_157_div_3_div_9_div_65_div_7_Template, 4, 1, \"div\", 243);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", flight_r40.offers[0].brandedFare.name || \"Branded Fare\", \" Details \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.offers[0].brandedFare.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.offers[0].brandedFare.features && flight_r40.offers[0].brandedFare.features.length > 0);\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r113 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 127)(1, \"div\", 128)(2, \"div\", 129)(3, \"div\", 130);\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_157_div_3_div_9_img_4_Template, 1, 1, \"img\", 131);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_157_div_3_div_9_i_5_Template, 1, 0, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 133)(7, \"span\", 134);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 135);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, SearchPriceComponent_div_157_div_3_div_9_span_11_Template, 3, 1, \"span\", 136);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 137);\n    i0.ɵɵtemplate(13, SearchPriceComponent_div_157_div_3_div_9_span_13_Template, 3, 0, \"span\", 138);\n    i0.ɵɵtemplate(14, SearchPriceComponent_div_157_div_3_div_9_span_14_Template, 3, 1, \"span\", 138);\n    i0.ɵɵtemplate(15, SearchPriceComponent_div_157_div_3_div_9_span_15_Template, 3, 0, \"span\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 140)(17, \"span\", 141);\n    i0.ɵɵtext(18, \"Price per person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 142);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, SearchPriceComponent_div_157_div_3_div_9_span_21_Template, 3, 5, \"span\", 143);\n    i0.ɵɵtemplate(22, SearchPriceComponent_div_157_div_3_div_9_span_22_Template, 3, 1, \"span\", 144);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 145)(24, \"div\", 146)(25, \"div\", 147)(26, \"div\", 148);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 149)(29, \"span\", 150);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 151);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 152)(34, \"div\", 153);\n    i0.ɵɵelement(35, \"span\", 154);\n    i0.ɵɵelementStart(36, \"div\", 155);\n    i0.ɵɵelement(37, \"span\", 156);\n    i0.ɵɵelementStart(38, \"span\", 157);\n    i0.ɵɵelement(39, \"i\", 107);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(40, \"span\", 158);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 159);\n    i0.ɵɵelement(42, \"i\", 160);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(44, SearchPriceComponent_div_157_div_3_div_9_div_44_Template, 4, 2, \"div\", 161);\n    i0.ɵɵtemplate(45, SearchPriceComponent_div_157_div_3_div_9_div_45_Template, 2, 0, \"div\", 162);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 163)(47, \"div\", 148);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 149)(50, \"span\", 150);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"span\", 151);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(54, \"div\", 164);\n    i0.ɵɵtemplate(55, SearchPriceComponent_div_157_div_3_div_9_div_55_Template, 9, 4, \"div\", 165);\n    i0.ɵɵtemplate(56, SearchPriceComponent_div_157_div_3_div_9_div_56_Template, 6, 5, \"div\", 165);\n    i0.ɵɵtemplate(57, SearchPriceComponent_div_157_div_3_div_9_div_57_Template, 5, 1, \"div\", 165);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 166)(59, \"details\", 167)(60, \"summary\", 168);\n    i0.ɵɵelement(61, \"i\", 68);\n    i0.ɵɵtext(62, \" Price Breakdown \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(63, SearchPriceComponent_div_157_div_3_div_9_div_63_Template, 2, 1, \"div\", 169);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(64, SearchPriceComponent_div_157_div_3_div_9_div_64_Template, 7, 2, \"div\", 170);\n    i0.ɵɵtemplate(65, SearchPriceComponent_div_157_div_3_div_9_div_65_Template, 8, 3, \"div\", 171);\n    i0.ɵɵelementStart(66, \"div\", 172)(67, \"button\", 173);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_157_div_3_div_9_Template_button_click_67_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r113);\n      const flight_r40 = restoredCtx.$implicit;\n      const ctx_r112 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r112.showAllDetails(flight_r40));\n    });\n    i0.ɵɵelement(68, \"i\", 174);\n    i0.ɵɵtext(69, \" View All Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"button\", 175);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_157_div_3_div_9_Template_button_click_70_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r113);\n      const flight_r40 = restoredCtx.$implicit;\n      const ctx_r114 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r114.selectThisFlight(flight_r40));\n    });\n    i0.ɵɵelement(71, \"i\", 176);\n    i0.ɵɵtext(72, \" Select This Flight \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r40 = ctx.$implicit;\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"unavailable\", !ctx_r33.isFlightAvailable(flight_r40));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.items && flight_r40.items[0] && flight_r40.items[0].airline && flight_r40.items[0].airline.thumbnailFull);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(flight_r40.items && flight_r40.items[0] && flight_r40.items[0].airline && flight_r40.items[0].airline.thumbnailFull));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r40.items && flight_r40.items[0] && flight_r40.items[0].airline ? flight_r40.items[0].airline.name : \"Airline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r40.items && flight_r40.items[0] ? flight_r40.items[0].flightNo : \"N/A\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.provider || flight_r40.items && flight_r40.items[0] && flight_r40.items[0].flightProvider);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.items && flight_r40.items[0] && flight_r40.items[0].stopCount === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.items && flight_r40.items[0] && flight_r40.items[0].flightClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.offers && flight_r40.offers[0] && flight_r40.offers[0].hasBrand);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r33.getMinPrice(flight_r40));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.offers && flight_r40.offers.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.offers && flight_r40.offers.length > 0 && flight_r40.offers[0].expiresOn);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(flight_r40.items && flight_r40.items[0] && flight_r40.items[0].departure ? ctx_r33.formatDate(flight_r40.items[0].departure.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r40.items && flight_r40.items[0] && flight_r40.items[0].departure && flight_r40.items[0].departure.airport ? flight_r40.items[0].departure.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r40.items && flight_r40.items[0] && flight_r40.items[0].departure && flight_r40.items[0].departure.city ? flight_r40.items[0].departure.city.name : \"N/A\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", flight_r40.items && flight_r40.items[0] ? ctx_r33.formatDuration(flight_r40.items[0].duration) : \"N/A\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.items && flight_r40.items[0] && flight_r40.items[0].stopCount > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.items && flight_r40.items[0] && flight_r40.items[0].stopCount === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r40.items && flight_r40.items[0] && flight_r40.items[0].arrival ? ctx_r33.formatDate(flight_r40.items[0].arrival.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r40.items && flight_r40.items[0] && flight_r40.items[0].arrival && flight_r40.items[0].arrival.airport ? flight_r40.items[0].arrival.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r40.items && flight_r40.items[0] && flight_r40.items[0].arrival && flight_r40.items[0].arrival.city ? flight_r40.items[0].arrival.city.name : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.items && flight_r40.items[0]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.items && flight_r40.items[0] && flight_r40.items[0].services && flight_r40.items[0].services.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.offers && flight_r40.offers.length > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.offers && flight_r40.offers[0]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.items && flight_r40.items[0] && flight_r40.items[0].segments && flight_r40.items[0].segments.length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r40.offers && flight_r40.offers[0] && flight_r40.offers[0].brandedFare);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !flight_r40.offers || flight_r40.offers.length === 0 || !ctx_r33.isFlightAvailable(flight_r40));\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 251)(1, \"div\", 252);\n    i0.ɵɵelement(2, \"i\", 97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No flights found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"We couldn't find any flights matching your search criteria. Try adjusting your search parameters.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 253)(8, \"div\", 254);\n    i0.ɵɵelement(9, \"i\", 36);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Try different dates\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 254);\n    i0.ɵɵelement(13, \"i\", 255);\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Try nearby airports\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 254);\n    i0.ɵɵelement(17, \"i\", 107);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Include flights with stops\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SearchPriceComponent_div_157_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"div\", 114)(2, \"div\", 115)(3, \"h3\");\n    i0.ɵɵtext(4, \"Flight Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_157_div_3_p_5_Template, 2, 0, \"p\", 56);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_157_div_3_p_6_Template, 5, 2, \"p\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_157_div_3_div_7_Template, 3, 1, \"div\", 116);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 117);\n    i0.ɵɵtemplate(9, SearchPriceComponent_div_157_div_3_div_9_Template, 73, 29, \"div\", 118);\n    i0.ɵɵtemplate(10, SearchPriceComponent_div_157_div_3_div_10_Template, 20, 0, \"div\", 119);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.searchResults.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r27.filteredResults);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r27.isLoading && !ctx_r27.errorMessage && ctx_r27.filteredResults.length === 0);\n  }\n}\nfunction SearchPriceComponent_div_157_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_157_div_1_Template, 9, 0, \"div\", 101);\n    i0.ɵɵtemplate(2, SearchPriceComponent_div_157_div_2_Template, 10, 1, \"div\", 102);\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_157_div_3_Template, 11, 5, \"div\", 103);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.isLoading && ctx_r12.errorMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.isLoading && !ctx_r12.errorMessage);\n  }\n}\nexport class SearchPriceComponent {\n  constructor(fb, productService, router) {\n    this.fb = fb;\n    this.productService = productService;\n    this.router = router;\n    this.departureLocations = [];\n    this.arrivalLocations = [];\n    this.isLoading = false;\n    this.searchResults = [];\n    this.filteredResults = [];\n    this.hasSearched = false;\n    this.errorMessage = '';\n    this.lastSearchId = '';\n    // Filter options\n    this.currentFilter = 'recommended';\n    this.filterOptions = [{\n      value: 'recommended',\n      label: 'Recommended',\n      icon: 'fa-star'\n    }, {\n      value: 'cheapest',\n      label: 'Cheapest',\n      icon: 'fa-dollar-sign'\n    }, {\n      value: 'shortest',\n      label: 'Shortest',\n      icon: 'fa-clock'\n    }];\n    // Passenger type options\n    this.passengerTypes = [{\n      value: PassengerType.Adult,\n      label: 'Adult'\n    }, {\n      value: PassengerType.Child,\n      label: 'Child'\n    }, {\n      value: PassengerType.Infant,\n      label: 'Infant'\n    }];\n    // Flight class options\n    this.flightClasses = [{\n      value: FlightClassType.PROMO,\n      label: 'Promo'\n    }, {\n      value: FlightClassType.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClassType.BUSINESS,\n      label: 'Business'\n    }];\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required],\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      arrivalLocation: ['', Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required],\n      // Options de vol\n      flightClass: [0, Validators.required],\n      nonStop: [false],\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n  ngOnInit() {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n  // Méthode pour appliquer le filtre sélectionné\n  applyFilter(filterValue) {\n    this.currentFilter = filterValue;\n    if (!this.searchResults || this.searchResults.length === 0) {\n      this.filteredResults = [];\n      return;\n    }\n    // Créer une copie des résultats pour ne pas modifier l'original\n    const results = [...this.searchResults];\n    switch (filterValue) {\n      case 'cheapest':\n        this.filteredResults = this.sortByPrice(results);\n        break;\n      case 'shortest':\n        this.filteredResults = this.sortByDuration(results);\n        break;\n      case 'recommended':\n      default:\n        this.filteredResults = this.sortByRecommendation(results);\n        break;\n    }\n  }\n  // Trier les vols par prix (du moins cher au plus cher)\n  sortByPrice(flights) {\n    return flights.sort((a, b) => {\n      const priceA = this.getMinPriceAmount(a);\n      const priceB = this.getMinPriceAmount(b);\n      return priceA - priceB;\n    });\n  }\n  // Trier les vols par durée (du plus court au plus long)\n  sortByDuration(flights) {\n    return flights.sort((a, b) => {\n      const durationA = a.items && a.items[0] ? a.items[0].duration : Number.MAX_VALUE;\n      const durationB = b.items && b.items[0] ? b.items[0].duration : Number.MAX_VALUE;\n      return durationA - durationB;\n    });\n  }\n  // Trier les vols par recommandation (combinaison de prix, durée et autres facteurs)\n  sortByRecommendation(flights) {\n    return flights.sort((a, b) => {\n      // Calculer un score pour chaque vol basé sur plusieurs facteurs\n      const scoreA = this.calculateRecommendationScore(a);\n      const scoreB = this.calculateRecommendationScore(b);\n      return scoreB - scoreA; // Ordre décroissant (score plus élevé = meilleur)\n    });\n  }\n  // Calculer un score de recommandation pour un vol\n  calculateRecommendationScore(flight) {\n    if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n      return 0;\n    }\n    const item = flight.items[0];\n    const offer = flight.offers[0];\n    // Facteurs à considérer pour le score\n    const price = this.getMinPriceAmount(flight);\n    const duration = item.duration;\n    const stopCount = item.stopCount || 0;\n    const availability = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    // Normaliser les valeurs (plus le score est élevé, meilleur est le vol)\n    const priceScore = 1000 / (price + 100); // Inverse du prix (moins cher = meilleur score)\n    const durationScore = 1000 / (duration + 100); // Inverse de la durée (plus court = meilleur score)\n    const stopScore = 1 / (stopCount + 1); // Moins d'escales = meilleur score\n    const availabilityScore = Math.min(availability, 10) / 10; // Plus de disponibilité = meilleur score\n    // Pondération des facteurs (ajustable selon les préférences)\n    const weights = {\n      price: 0.4,\n      duration: 0.3,\n      stops: 0.2,\n      availability: 0.1 // 10% importance pour la disponibilité\n    };\n    // Calculer le score final pondéré\n    return priceScore * weights.price + durationScore * weights.duration + stopScore * weights.stops + availabilityScore * weights.availability;\n  }\n  // Obtenir le montant du prix minimum pour un vol\n  getMinPriceAmount(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return Number.MAX_VALUE;\n    }\n    return flight.offers.reduce((min, offer) => offer.price && offer.price.amount < min ? offer.price.amount : min, flight.offers[0].price ? flight.offers[0].price.amount : Number.MAX_VALUE);\n  }\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight) {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n    header.appendChild(logo);\n    header.appendChild(title);\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n        generalInfo.appendChild(airlineInfo);\n      }\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n      routeSection.appendChild(routeVisual);\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n          segmentsList.appendChild(segmentItem);\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n            segmentsList.appendChild(layover);\n          }\n        });\n        routeSection.appendChild(segmentsList);\n      }\n    }\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n          const baggageContainer = document.createElement('div');\n          baggageContainer.style.display = 'flex';\n          baggageContainer.style.flexDirection = 'column';\n          baggageContainer.style.gap = '10px';\n          baggageContainer.style.marginBottom = '15px';\n          // Filtrer et regrouper les bagages par type\n          const checkedBaggage = offer.baggageInformations.filter(b => b.baggageType === 2);\n          const cabinBaggage = offer.baggageInformations.filter(b => b.baggageType === 1);\n          const handBaggage = offer.baggageInformations.filter(b => b.baggageType === 3);\n          // Bagages en soute\n          if (checkedBaggage.length > 0) {\n            checkedBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#e7f5ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #c5e1f9';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-suitcase\" style=\"color: #4a6fa5; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Checked Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n          // Bagages cabine\n          if (cabinBaggage.length > 0) {\n            cabinBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#f3f0ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #e5dbff';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Cabin Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          } else {\n            // Ajouter un bagage cabine par défaut si aucun n'est spécifié\n            const baggageItem = document.createElement('div');\n            baggageItem.style.display = 'flex';\n            baggageItem.style.alignItems = 'center';\n            baggageItem.style.padding = '10px 15px';\n            baggageItem.style.backgroundColor = '#f3f0ff';\n            baggageItem.style.borderRadius = '6px';\n            baggageItem.style.border = '1px solid #e5dbff';\n            const baggageIcon = document.createElement('div');\n            baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n            const baggageInfo = document.createElement('div');\n            baggageInfo.style.display = 'flex';\n            baggageInfo.style.flexDirection = 'column';\n            const baggageType = document.createElement('div');\n            baggageType.textContent = 'Cabin Baggage';\n            baggageType.style.fontWeight = '600';\n            baggageType.style.fontSize = '14px';\n            const baggageDetails = document.createElement('div');\n            baggageDetails.style.fontSize = '12px';\n            baggageDetails.style.color = '#666';\n            baggageDetails.textContent = 'Included';\n            baggageInfo.appendChild(baggageType);\n            baggageInfo.appendChild(baggageDetails);\n            baggageItem.appendChild(baggageIcon);\n            baggageItem.appendChild(baggageInfo);\n            baggageContainer.appendChild(baggageItem);\n          }\n          // Bagages à main\n          if (handBaggage.length > 0) {\n            handBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#fff4e6';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #ffe8cc';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-shopping-bag\" style=\"color: #e8590c; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Hand Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n          offerItem.appendChild(baggageContainer);\n        }\n        offerItem.appendChild(offerDetails);\n        offersList.appendChild(offerItem);\n      });\n      offersSection.appendChild(offersList);\n    }\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n  // Méthode utilitaire pour créer une section\n  createSection(title, iconClass) {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n    return section;\n  }\n  // Méthode utilitaire pour créer une ligne d'information\n  createInfoRow(label, value) {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n    return row;\n  }\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight) {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n  preloadLocations() {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n  setupAutocomplete() {\n    // Charger les locations par type par défaut\n    const departureLocationType = 2; // Type 2 (City) par défaut\n    const arrivalLocationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(departureLocationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(departureLocationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(arrivalLocationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(arrivalLocationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  displayLocation(location) {\n    if (!location) return '';\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n  onSearch() {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n    const formValue = this.searchForm.value;\n    // Créer la requête de recherche entièrement dynamique\n    const request = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [{\n        id: formValue.departureLocation?.id || '',\n        type: 2 // Type 2 (City) par défaut\n      }],\n\n      ArrivalLocations: [{\n        id: formValue.arrivalLocation?.id || '',\n        type: 5 // Type 5 (Airport) par défaut\n      }],\n\n      Passengers: [{\n        type: formValue.passengerType,\n        count: formValue.passengerCount\n      }],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n    this.productService.searchPrice(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.header.success) {\n          this.searchResults = response.body.flights;\n          // Appliquer le filtre actuel aux résultats\n          this.applyFilter(this.currentFilter);\n          // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n          console.log('Full API Response:', JSON.stringify(response, null, 2));\n          // Analyser les données de disponibilité\n          if (response.body && response.body.flights && response.body.flights.length > 0) {\n            console.group('Availability Analysis');\n            console.log('Total flights:', response.body.flights.length);\n            // Compter les vols avec des offres\n            const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n            console.log('Flights with offers:', flightsWithOffers.length);\n            // Analyser les valeurs de disponibilité\n            const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability !== undefined ? o.availability : o.seatInfo ? o.seatInfo.availableSeatCount : 0));\n            console.log('Availability values:', availabilityValues);\n            // Compter les différentes valeurs de disponibilité\n            const availabilityCounts = availabilityValues.reduce((acc, val) => {\n              if (val !== undefined) {\n                acc[val] = (acc[val] || 0) + 1;\n              }\n              return acc;\n            }, {});\n            console.log('Availability counts:', availabilityCounts);\n            // Vérifier les vols réservables\n            const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true));\n            console.log('Reservable flights:', reservableFlights.length);\n            console.groupEnd();\n          }\n          // Vérifier si searchId existe dans le corps de la réponse\n          if (response.body && response.body.searchId) {\n            this.lastSearchId = response.body.searchId;\n            console.log('Search ID found in body.searchId:', this.lastSearchId);\n          }\n          // Vérifier si searchId existe dans l'en-tête de la réponse\n          else if (response.header && response.header.requestId) {\n            this.lastSearchId = response.header.requestId;\n            console.log('Search ID found in header.requestId:', this.lastSearchId);\n          }\n          // Vérifier d'autres emplacements possibles\n          else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n            this.lastSearchId = response.body.flights[0].id;\n            console.log('Using flight ID as search ID:', this.lastSearchId);\n          } else {\n            console.error('No search ID found in the response!');\n            console.log('Response structure:', Object.keys(response));\n            if (response.body) console.log('Body structure:', Object.keys(response.body));\n            if (response.header) console.log('Header structure:', Object.keys(response.header));\n          }\n        } else {\n          this.errorMessage = 'The search could not be completed. Please try again.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred during the search. Please try again.';\n        console.error('Error searching flights:', error);\n      }\n    });\n  }\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  // Formater la durée en heures et minutes\n  formatDuration(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n  // Formater la date pour l'affichage\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n    const minOffer = flight.offers.reduce((min, offer) => offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight) {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    return availabilityValue > 0;\n  }\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations() {\n    const locationType = 2; // Type 2 (City) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations() {\n    const locationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations() {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      arrivalLocation: departureLocation\n    });\n  }\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString) {\n    if (!dateString) return 'N/A';\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType) {\n    switch (baggageType) {\n      case 1:\n        return 'Cabin Baggage';\n      case 2:\n        return 'Checked Baggage';\n      case 3:\n        return 'Hand Baggage';\n      default:\n        return 'Baggage';\n    }\n  }\n  // Filtrer les bagages par type\n  filterBaggageByType(baggageInformations, type) {\n    if (!baggageInformations || !Array.isArray(baggageInformations)) {\n      return [];\n    }\n    return baggageInformations.filter(baggage => baggage.baggageType === type);\n  }\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType) {\n    switch (passengerType) {\n      case 1:\n        return 'Adult';\n      case 2:\n        return 'Child';\n      case 3:\n        return 'Infant';\n      default:\n        return 'Passenger';\n    }\n  }\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment, nextSegment) {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date || !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n  static {\n    this.ɵfac = function SearchPriceComponent_Factory(t) {\n      return new (t || SearchPriceComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchPriceComponent,\n      selectors: [[\"app-search-price\"]],\n      decls: 158,\n      vars: 21,\n      consts: [[1, \"search-price-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-illustration\"], [\"src\", \"assets/images/airplane-banner.jpg\", \"alt\", \"Airplane in the sky\"], [1, \"search-content\"], [1, \"search-form-container\"], [1, \"sidebar-logo\"], [1, \"logo-container\"], [1, \"fas\", \"fa-plane-departure\", \"logo-icon\"], [1, \"logo-text\"], [1, \"search-form-header\"], [1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"search-card\"], [\"type\", \"hidden\", \"formControlName\", \"productType\", \"value\", \"3\"], [\"type\", \"hidden\", \"formControlName\", \"serviceTypes\", \"value\", \"['1']\"], [1, \"single-line-form\"], [1, \"form-group\"], [\"for\", \"departureLocation\"], [1, \"input-with-icon\"], [1, \"fas\", \"fa-plane-departure\"], [\"type\", \"text\", \"id\", \"departureLocation\", \"formControlName\", \"departureLocation\", \"placeholder\", \"City or airport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [3, \"displayWith\"], [\"departureAuto\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"swap-button-container\"], [\"type\", \"button\", 1, \"swap-locations-btn\", 3, \"click\"], [1, \"fas\", \"fa-exchange-alt\"], [\"for\", \"arrivalLocation\"], [1, \"fas\", \"fa-plane-arrival\"], [\"type\", \"text\", \"id\", \"arrivalLocation\", \"formControlName\", \"arrivalLocation\", \"placeholder\", \"City or airport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [\"arrivalAuto\", \"matAutocomplete\"], [\"for\", \"departureDate\"], [1, \"fas\", \"fa-calendar-alt\"], [\"type\", \"date\", \"id\", \"departureDate\", \"formControlName\", \"departureDate\", 1, \"form-control\", 3, \"min\"], [\"for\", \"passengerCount\"], [1, \"fas\", \"fa-user-friends\"], [\"type\", \"number\", \"id\", \"passengerCount\", \"formControlName\", \"passengerCount\", \"min\", \"1\", \"max\", \"9\", 1, \"form-control\"], [\"for\", \"passengerType\"], [1, \"fas\", \"fa-users\"], [\"id\", \"passengerType\", \"formControlName\", \"passengerType\", 1, \"form-control\"], [\"for\", \"flightClass\"], [1, \"fas\", \"fa-chair\"], [\"id\", \"flightClass\", \"formControlName\", \"flightClass\", 1, \"form-control\"], [1, \"form-group\", \"checkbox-group\"], [1, \"toggle-switch\", \"small\"], [\"type\", \"checkbox\", \"id\", \"nonStop\", \"formControlName\", \"nonStop\", 1, \"toggle-input\"], [\"for\", \"nonStop\", 1, \"toggle-label\"], [1, \"toggle-inner\"], [1, \"toggle-switch-label\"], [1, \"search-button-container\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [\"class\", \"fas fa-search\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [1, \"advanced-options-container\"], [1, \"fas\", \"fa-cog\"], [1, \"advanced-options\"], [1, \"form-row\"], [\"for\", \"culture\"], [1, \"fas\", \"fa-language\"], [\"id\", \"culture\", \"formControlName\", \"culture\", 1, \"form-control\"], [\"value\", \"en-US\"], [\"value\", \"fr-FR\"], [\"for\", \"currency\"], [1, \"fas\", \"fa-money-bill-wave\"], [\"id\", \"currency\", \"formControlName\", \"currency\", 1, \"form-control\"], [\"value\", \"EUR\"], [\"value\", \"USD\"], [\"for\", \"flightBaggageGetOption\"], [1, \"fas\", \"fa-suitcase\"], [\"id\", \"flightBaggageGetOption\", \"formControlName\", \"flightBaggageGetOption\", 1, \"form-control\"], [3, \"value\"], [1, \"form-row\", \"checkbox-options\"], [\"type\", \"checkbox\", \"id\", \"acceptPendingProviders\", \"formControlName\", \"acceptPendingProviders\", 1, \"toggle-input\"], [\"for\", \"acceptPendingProviders\", 1, \"toggle-label\"], [\"type\", \"checkbox\", \"id\", \"forceFlightBundlePackage\", \"formControlName\", \"forceFlightBundlePackage\", 1, \"toggle-input\"], [\"for\", \"forceFlightBundlePackage\", 1, \"toggle-label\"], [\"type\", \"checkbox\", \"id\", \"disablePackageOfferTotalPrice\", \"formControlName\", \"disablePackageOfferTotalPrice\", 1, \"toggle-input\"], [\"for\", \"disablePackageOfferTotalPrice\", 1, \"toggle-label\"], [\"type\", \"checkbox\", \"id\", \"calculateFlightFees\", \"formControlName\", \"calculateFlightFees\", 1, \"toggle-input\"], [\"for\", \"calculateFlightFees\", 1, \"toggle-label\"], [\"class\", \"search-results-container\", 4, \"ngIf\"], [1, \"location-option\"], [1, \"location-name\"], [1, \"location-details\"], [\"class\", \"location-code\", 4, \"ngIf\"], [\"class\", \"location-city\", 4, \"ngIf\"], [1, \"location-type\"], [1, \"fas\", 3, \"ngClass\"], [1, \"location-code\"], [1, \"location-city\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"fas\", \"fa-search\"], [1, \"spinner-container\"], [1, \"spinner\"], [1, \"search-results-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"search-results-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-animation\"], [1, \"plane-loader\"], [1, \"fas\", \"fa-plane\"], [1, \"cloud\"], [1, \"error-container\"], [1, \"error-icon\"], [1, \"retry-button\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"search-results-content\"], [1, \"results-header\"], [1, \"results-title\"], [\"class\", \"results-filters\", 4, \"ngIf\"], [1, \"flight-list\"], [\"class\", \"flight-card\", 3, \"unavailable\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"results-count\"], [\"class\", \"filter-label\", 4, \"ngIf\"], [1, \"filter-label\"], [1, \"results-filters\"], [1, \"filter-buttons\"], [\"class\", \"filter-button\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"filter-button\", 3, \"click\"], [1, \"flight-card\"], [1, \"flight-header\"], [1, \"airline-info\"], [1, \"airline-logo-container\"], [\"alt\", \"Airline logo\", \"class\", \"airline-logo\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"fas fa-plane airline-icon\", 4, \"ngIf\"], [1, \"airline-details\"], [1, \"airline-name\"], [1, \"flight-number\"], [\"class\", \"provider-info\", 4, \"ngIf\"], [1, \"flight-badges\"], [\"class\", \"flight-badge\", 4, \"ngIf\"], [\"class\", \"flight-badge branded\", 4, \"ngIf\"], [1, \"flight-price\"], [1, \"price-label\"], [1, \"price\"], [\"class\", \"availability\", 4, \"ngIf\"], [\"class\", \"expiration\", 4, \"ngIf\"], [1, \"flight-details\"], [1, \"flight-route\"], [1, \"departure\"], [1, \"time\"], [1, \"location\"], [1, \"airport-code\"], [1, \"city-name\"], [1, \"flight-duration\"], [1, \"duration-line\"], [1, \"dot\", \"departure-dot\"], [1, \"line-container\"], [1, \"line\"], [1, \"plane-icon\"], [1, \"dot\", \"arrival-dot\"], [1, \"duration-text\"], [1, \"fas\", \"fa-clock\"], [\"class\", \"stops\", 4, \"ngIf\"], [\"class\", \"stops direct\", 4, \"ngIf\"], [1, \"arrival\"], [1, \"flight-features\"], [\"class\", \"feature-group\", 4, \"ngIf\"], [1, \"price-breakdown-section\"], [1, \"price-breakdown-details\"], [1, \"price-breakdown-summary\"], [\"class\", \"price-breakdown-content\", 4, \"ngIf\"], [\"class\", \"segments-section\", 4, \"ngIf\"], [\"class\", \"branded-fare-section\", 4, \"ngIf\"], [1, \"flight-actions\"], [1, \"view-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [1, \"select-button\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-check-circle\"], [\"alt\", \"Airline logo\", 1, \"airline-logo\", 3, \"src\"], [1, \"fas\", \"fa-plane\", \"airline-icon\"], [1, \"provider-info\"], [1, \"fas\", \"fa-tag\"], [1, \"flight-badge\"], [1, \"fas\", \"fa-bolt\"], [1, \"flight-badge\", \"branded\"], [1, \"fas\", \"fa-certificate\"], [1, \"availability\"], [1, \"expiration\"], [1, \"stops\"], [1, \"stop-count\"], [1, \"stops\", \"direct\"], [1, \"feature-group\"], [1, \"baggage-details\"], [\"class\", \"baggage-item cabin\", 4, \"ngIf\"], [\"class\", \"feature\", 4, \"ngIf\"], [\"class\", \"baggage-item checked\", 4, \"ngFor\", \"ngForOf\"], [1, \"baggage-item\", \"checked\"], [1, \"baggage-info\"], [1, \"baggage-type\"], [\"class\", \"baggage-specs\", 4, \"ngIf\"], [1, \"baggage-specs\"], [\"class\", \"baggage-item cabin\", 4, \"ngFor\", \"ngForOf\"], [1, \"baggage-item\", \"cabin\"], [1, \"fas\", \"fa-briefcase\"], [1, \"feature\"], [1, \"fas\", \"fa-concierge-bell\"], [\"class\", \"feature\", 4, \"ngFor\", \"ngForOf\"], [1, \"source-info\"], [1, \"price-breakdown-content\"], [\"class\", \"breakdown-group\", 4, \"ngIf\"], [1, \"breakdown-group\"], [\"class\", \"breakdown-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"breakdown-item service-fee\", 4, \"ngIf\"], [1, \"breakdown-total\"], [1, \"total-label\"], [1, \"total-amount\"], [1, \"breakdown-item\"], [1, \"passenger-type\"], [1, \"item-price\"], [1, \"breakdown-item\", \"service-fee\"], [1, \"fee-label\"], [1, \"fee-amount\"], [1, \"segments-section\"], [1, \"segments-details\"], [1, \"segments-summary\"], [1, \"segments-content\"], [\"class\", \"segment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"segment-item\"], [1, \"segment-header\"], [1, \"segment-number\"], [1, \"segment-airline\"], [1, \"segment-flight\"], [1, \"segment-route\"], [1, \"segment-departure\"], [1, \"segment-duration\"], [1, \"segment-arrival\"], [\"class\", \"layover-info\", 4, \"ngIf\"], [1, \"layover-info\"], [1, \"fas\", \"fa-hourglass-half\"], [1, \"branded-fare-section\"], [1, \"branded-fare-details\"], [1, \"branded-fare-summary\"], [1, \"branded-fare-content\"], [\"class\", \"branded-fare-description\", 4, \"ngIf\"], [\"class\", \"branded-fare-features\", 4, \"ngIf\"], [1, \"branded-fare-description\"], [1, \"branded-fare-features\"], [\"class\", \"feature-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature-item\"], [1, \"feature-name\"], [\"class\", \"feature-description\", 4, \"ngIf\"], [1, \"feature-description\"], [1, \"no-results\"], [1, \"no-results-icon\"], [1, \"no-results-suggestions\"], [1, \"suggestion\"], [1, \"fas\", \"fa-map-marker-alt\"]],\n      template: function SearchPriceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Find Your Perfect Flight\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Search and compare flights to destinations worldwide\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵelement(8, \"img\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10);\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementStart(14, \"span\", 12);\n          i0.ɵɵtext(15, \"TravelEase\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 13)(17, \"h2\");\n          i0.ɵɵtext(18, \"Search Flights\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"p\");\n          i0.ɵɵtext(20, \"Enter your travel details below\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"form\", 14);\n          i0.ɵɵlistener(\"ngSubmit\", function SearchPriceComponent_Template_form_ngSubmit_21_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(22, \"div\", 15);\n          i0.ɵɵelement(23, \"input\", 16)(24, \"input\", 17);\n          i0.ɵɵelementStart(25, \"div\", 18)(26, \"div\", 19)(27, \"label\", 20);\n          i0.ɵɵtext(28, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 21);\n          i0.ɵɵelement(30, \"i\", 22);\n          i0.ɵɵelementStart(31, \"input\", 23);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_31_listener() {\n            return ctx.showAllDepartureLocations();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"mat-autocomplete\", 24, 25);\n          i0.ɵɵtemplate(34, SearchPriceComponent_mat_option_34_Template, 10, 12, \"mat-option\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, SearchPriceComponent_div_35_Template, 3, 0, \"div\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 28)(37, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_37_listener() {\n            return ctx.swapLocations();\n          });\n          i0.ɵɵelement(38, \"i\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 19)(40, \"label\", 31);\n          i0.ɵɵtext(41, \"To\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 21);\n          i0.ɵɵelement(43, \"i\", 32);\n          i0.ɵɵelementStart(44, \"input\", 33);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_44_listener() {\n            return ctx.showAllArrivalLocations();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"mat-autocomplete\", 24, 34);\n          i0.ɵɵtemplate(47, SearchPriceComponent_mat_option_47_Template, 10, 12, \"mat-option\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(48, SearchPriceComponent_div_48_Template, 3, 0, \"div\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 18)(50, \"div\", 19)(51, \"label\", 35);\n          i0.ɵɵtext(52, \"Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 21);\n          i0.ɵɵelement(54, \"i\", 36)(55, \"input\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(56, SearchPriceComponent_div_56_Template, 3, 0, \"div\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 19)(58, \"label\", 38);\n          i0.ɵɵtext(59, \"Passengers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 21);\n          i0.ɵɵelement(61, \"i\", 39)(62, \"input\", 40);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 19)(64, \"label\", 41);\n          i0.ɵɵtext(65, \"Passenger Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 21);\n          i0.ɵɵelement(67, \"i\", 42);\n          i0.ɵɵelementStart(68, \"select\", 43);\n          i0.ɵɵtemplate(69, SearchPriceComponent_option_69_Template, 2, 2, \"option\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(70, \"div\", 19)(71, \"label\", 44);\n          i0.ɵɵtext(72, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 21);\n          i0.ɵɵelement(74, \"i\", 45);\n          i0.ɵɵelementStart(75, \"select\", 46);\n          i0.ɵɵtemplate(76, SearchPriceComponent_option_76_Template, 2, 2, \"option\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(77, \"div\", 47)(78, \"div\", 48);\n          i0.ɵɵelement(79, \"input\", 49);\n          i0.ɵɵelementStart(80, \"label\", 50);\n          i0.ɵɵelement(81, \"span\", 51);\n          i0.ɵɵelementStart(82, \"span\", 52);\n          i0.ɵɵtext(83, \"Non-stop flights only\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(84, \"div\", 53)(85, \"button\", 54);\n          i0.ɵɵtemplate(86, SearchPriceComponent_i_86_Template, 1, 0, \"i\", 55);\n          i0.ɵɵtemplate(87, SearchPriceComponent_span_87_Template, 2, 0, \"span\", 56);\n          i0.ɵɵtemplate(88, SearchPriceComponent_div_88_Template, 2, 0, \"div\", 57);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(89, \"div\", 58)(90, \"details\")(91, \"summary\");\n          i0.ɵɵelement(92, \"i\", 59);\n          i0.ɵɵtext(93, \" Advanced Options \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"div\", 60)(95, \"div\", 61)(96, \"div\", 19)(97, \"label\", 62);\n          i0.ɵɵtext(98, \"Language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"div\", 21);\n          i0.ɵɵelement(100, \"i\", 63);\n          i0.ɵɵelementStart(101, \"select\", 64)(102, \"option\", 65);\n          i0.ɵɵtext(103, \"English (US)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"option\", 66);\n          i0.ɵɵtext(105, \"Fran\\u00E7ais\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(106, \"div\", 19)(107, \"label\", 67);\n          i0.ɵɵtext(108, \"Currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"div\", 21);\n          i0.ɵɵelement(110, \"i\", 68);\n          i0.ɵɵelementStart(111, \"select\", 69)(112, \"option\", 70);\n          i0.ɵɵtext(113, \"Euro (\\u20AC)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(114, \"option\", 71);\n          i0.ɵɵtext(115, \"Dollar ($)\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(116, \"div\", 19)(117, \"label\", 72);\n          i0.ɵɵtext(118, \"Baggage Options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(119, \"div\", 21);\n          i0.ɵɵelement(120, \"i\", 73);\n          i0.ɵɵelementStart(121, \"select\", 74)(122, \"option\", 75);\n          i0.ɵɵtext(123, \"All options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"option\", 75);\n          i0.ɵɵtext(125, \"Baggage included only\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(126, \"option\", 75);\n          i0.ɵɵtext(127, \"No baggage only\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(128, \"div\", 76)(129, \"div\", 47)(130, \"div\", 48);\n          i0.ɵɵelement(131, \"input\", 77);\n          i0.ɵɵelementStart(132, \"label\", 78);\n          i0.ɵɵelement(133, \"span\", 51);\n          i0.ɵɵelementStart(134, \"span\", 52);\n          i0.ɵɵtext(135, \"Accept pending providers\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(136, \"div\", 47)(137, \"div\", 48);\n          i0.ɵɵelement(138, \"input\", 79);\n          i0.ɵɵelementStart(139, \"label\", 80);\n          i0.ɵɵelement(140, \"span\", 51);\n          i0.ɵɵelementStart(141, \"span\", 52);\n          i0.ɵɵtext(142, \"Force flight bundle package\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(143, \"div\", 47)(144, \"div\", 48);\n          i0.ɵɵelement(145, \"input\", 81);\n          i0.ɵɵelementStart(146, \"label\", 82);\n          i0.ɵɵelement(147, \"span\", 51);\n          i0.ɵɵelementStart(148, \"span\", 52);\n          i0.ɵɵtext(149, \"Disable package offer total price\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(150, \"div\", 47)(151, \"div\", 48);\n          i0.ɵɵelement(152, \"input\", 83);\n          i0.ɵɵelementStart(153, \"label\", 84);\n          i0.ɵɵelement(154, \"span\", 51);\n          i0.ɵɵelementStart(155, \"span\", 52);\n          i0.ɵɵtext(156, \"Calculate flight fees\");\n          i0.ɵɵelementEnd()()()()()()()()()();\n          i0.ɵɵtemplate(157, SearchPriceComponent_div_157_Template, 4, 3, \"div\", 85);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(33);\n          const _r3 = i0.ɵɵreference(46);\n          let tmp_4_0;\n          let tmp_8_0;\n          let tmp_10_0;\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.departureLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.searchForm.get(\"departureLocation\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.searchForm.get(\"departureLocation\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"matAutocomplete\", _r3);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrivalLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"min\", ctx.minDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx.searchForm.get(\"departureDate\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.searchForm.get(\"departureDate\")) == null ? null : tmp_10_0.touched));\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.passengerTypes);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"disabled\", ctx.searchForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(31);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasSearched);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i5.MatAutocomplete, i6.MatOption, i5.MatAutocompleteTrigger, i4.SlicePipe, i4.CurrencyPipe],\n      styles: [\"/* Styles globaux pour \\u00E9viter le d\\u00E9filement horizontal */\\n* {\\n  box-sizing: border-box;\\n}\\n\\n/* Conteneur principal - \\u00C9vite le d\\u00E9filement horizontal */\\n.search-price-container {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 10px;\\n  width: 100%;\\n  max-width: 100%; /* Utilisation de toute la largeur de l'\\u00E9cran */\\n  margin: 0;\\n  position: relative;\\n  z-index: 1;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-price-container {\\n    padding: 0;\\n    margin: 0;\\n  }\\n}\\n\\n.search-price-container::before {\\n  content: '';\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background:\\n    linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.03) 0%, rgba(var(--primary-color-rgb), 0) 50%),\\n    radial-gradient(circle at top right, rgba(var(--secondary-color-rgb), 0.03) 0%, rgba(var(--secondary-color-rgb), 0) 70%);\\n  z-index: -1;\\n  pointer-events: none;\\n}\\n\\n/* En-t\\u00EAte de page */\\n.page-header {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  margin-bottom: 30px;\\n  position: relative;\\n  overflow: hidden;\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.page-header::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.1));\\n  z-index: 1;\\n}\\n\\n.header-content {\\n  max-width: 800px;\\n  padding: 40px 20px;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 2;\\n  color: white;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n.page-title {\\n  font-size: 36px;\\n  font-weight: 700;\\n  color: white;\\n  margin-bottom: 15px;\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.page-title::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: white;\\n  border-radius: 3px;\\n}\\n\\n.page-subtitle {\\n  font-size: 18px;\\n  color: rgba(255, 255, 255, 0.9);\\n  line-height: 1.5;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  margin-top: 20px;\\n}\\n\\n.header-illustration {\\n  width: 100%;\\n  height: 300px;\\n  overflow: hidden;\\n}\\n\\n.header-illustration img {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n  transition: transform 10s ease;\\n}\\n\\n.page-header:hover .header-illustration img {\\n  transform: scale(1.1);\\n}\\n\\n/* Disposition pour les \\u00E9crans larges - Carte de recherche horizontale en haut */\\n@media (min-width: 992px) {\\n  .page-header {\\n    display: none;\\n  }\\n\\n  .search-price-container {\\n    flex-direction: column;\\n    align-items: stretch;\\n    padding: 0;\\n    margin: 0;\\n    width: 100%;\\n  }\\n\\n  .search-content {\\n    display: flex;\\n    flex-direction: column;\\n    width: 100%;\\n    gap: 0;\\n  }\\n\\n  .search-form-container {\\n    position: sticky;\\n    top: 0;\\n    width: 100%;\\n    max-height: none;\\n    overflow: visible;\\n    margin-bottom: 20px;\\n    padding: 0;\\n    border-radius: 0;\\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n    z-index: 100;\\n    background-color: white;\\n  }\\n\\n  .search-results-container {\\n    width: 100%;\\n    margin-left: 0;\\n    padding: 20px;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n  }\\n}\\n\\n/* Formulaire de recherche */\\n.search-form-container {\\n  background-color: white;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  padding: 25px;\\n  margin-bottom: 30px;\\n}\\n\\n/* Logo pour la version desktop */\\n.sidebar-logo {\\n  display: none;\\n}\\n\\n/* Style sp\\u00E9cifique pour les \\u00E9crans larges */\\n@media (min-width: 992px) {\\n  .search-form-container {\\n    padding: 0;\\n    margin-bottom: 20px;\\n    border-radius: 0;\\n  }\\n\\n  .sidebar-logo {\\n    display: block;\\n    background-color: var(--primary-color);\\n    color: white;\\n    padding: 15px 20px;\\n    text-align: left;\\n  }\\n\\n  .logo-container {\\n    display: flex;\\n    align-items: center;\\n    gap: 10px;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n    width: 100%;\\n  }\\n\\n  .logo-icon {\\n    font-size: 22px;\\n  }\\n\\n  .logo-text {\\n    font-size: 22px;\\n    font-weight: 700;\\n    letter-spacing: 0.5px;\\n  }\\n}\\n\\n.search-form-header {\\n  margin-bottom: 25px;\\n  text-align: center;\\n  padding: 20px 20px 0 20px;\\n}\\n\\n.search-form-header h2 {\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n  font-size: 26px;\\n  font-weight: 600;\\n}\\n\\n.search-form-header p {\\n  color: #666;\\n  font-size: 15px;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-form-header {\\n    max-width: 1200px;\\n    margin: 0 auto 15px auto;\\n    text-align: left;\\n    padding: 20px 20px 0 20px;\\n  }\\n}\\n\\n.search-form {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.form-group {\\n  margin-bottom: 15px;\\n}\\n\\n.form-row {\\n  display: flex;\\n  gap: 15px;\\n}\\n\\n.half-width {\\n  flex: 1;\\n}\\n\\nlabel {\\n  display: block;\\n  margin-bottom: 5px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 14px;\\n}\\n\\n.form-control {\\n  width: 100%;\\n  padding: 10px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 5px;\\n  font-size: 14px;\\n  transition: border-color 0.3s;\\n}\\n\\n.form-control:focus {\\n  border-color: #2989d8;\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(41, 137, 216, 0.2);\\n}\\n\\n.checkbox-group {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.checkbox-group input {\\n  margin-right: 8px;\\n}\\n\\n.search-button {\\n  padding: 12px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 5px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.3s;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-top: 10px;\\n}\\n\\n.search-button:hover {\\n  background-color: #1e5799;\\n}\\n\\n.search-button:disabled {\\n  background-color: #b3d4f0;\\n  cursor: not-allowed;\\n}\\n\\n.error-message {\\n  color: #e74c3c;\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n\\n/* Options avanc\\u00E9es */\\ndetails {\\n  margin-top: 10px;\\n  margin-bottom: 15px;\\n}\\n\\nsummary {\\n  cursor: pointer;\\n  color: #2989d8;\\n  font-weight: 500;\\n  padding: 5px 0;\\n}\\n\\nsummary:hover {\\n  text-decoration: underline;\\n}\\n\\n.advanced-options {\\n  margin-top: 10px;\\n  padding: 15px;\\n  background-color: #f8f9fa;\\n  border-radius: 5px;\\n  border: 1px solid #eee;\\n}\\n\\n/* Styles pour les listes d\\u00E9roulantes */\\n::ng-deep .mat-autocomplete-panel {\\n  max-height: 300px !important;\\n}\\n\\n::ng-deep .mat-option {\\n  height: auto !important;\\n  line-height: 1.2 !important;\\n  padding: 10px 16px !important;\\n}\\n\\n::ng-deep .mat-option small {\\n  color: #666;\\n  display: block;\\n  margin-top: 2px;\\n}\\n\\n/* R\\u00E9sultats de recherche - Design optimis\\u00E9 pour \\u00E9viter le d\\u00E9filement horizontal */\\n.search-results-container {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  box-shadow:\\n    0 5px 15px rgba(0, 0, 0, 0.08),\\n    0 0 0 1px rgba(var(--primary-color-rgb), 0.05);\\n  padding: 20px;\\n  position: relative;\\n  overflow-x: hidden;\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  box-sizing: border-box;\\n  width: 100%;\\n}\\n\\n.search-results-container::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 6px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\\n  z-index: 1;\\n}\\n\\n.loading-container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px;\\n  text-align: center;\\n}\\n\\n.loading-container p {\\n  margin-top: 20px;\\n  color: var(--primary-color);\\n  font-weight: 500;\\n  animation: pulse 1.5s infinite;\\n}\\n\\n.spinner {\\n  width: 30px;\\n  height: 30px;\\n  border: 3px solid rgba(var(--primary-color-rgb), 0.2);\\n  border-radius: 50%;\\n  border-top-color: var(--primary-color);\\n  animation: spin 1s cubic-bezier(0.6, 0.2, 0.4, 0.8) infinite;\\n  box-shadow: 0 0 10px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.spinner.large {\\n  width: 50px;\\n  height: 50px;\\n  border-width: 4px;\\n}\\n\\n@keyframes spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n.error-container {\\n  padding: 30px;\\n  background-color: rgba(231, 76, 60, 0.05);\\n  border-radius: 16px;\\n  text-align: center;\\n  border: 1px solid rgba(231, 76, 60, 0.1);\\n  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.05);\\n  animation: scaleIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\\n}\\n\\n.error-container h4 {\\n  color: #e74c3c;\\n  margin-bottom: 10px;\\n  font-size: 18px;\\n}\\n\\n.error-container p {\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.results-header {\\n  margin-bottom: 30px;\\n  position: relative;\\n  padding-bottom: 15px;\\n}\\n\\n.results-header::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0.1) 0%,\\n    rgba(var(--primary-color-rgb), 0.05) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n/* Styles pour les filtres */\\n.results-filters {\\n  margin-top: 15px;\\n}\\n\\n.filter-buttons {\\n  display: flex;\\n  gap: 10px;\\n  flex-wrap: wrap;\\n}\\n\\n.filter-button {\\n  padding: 10px 16px;\\n  border-radius: 50px;\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.1);\\n  background-color: white;\\n  color: #666;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\\n}\\n\\n.filter-button i {\\n  font-size: 14px;\\n  color: rgba(var(--primary-color-rgb), 0.7);\\n  transition: all 0.3s ease;\\n}\\n\\n.filter-button:hover {\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  border-color: rgba(var(--primary-color-rgb), 0.2);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);\\n}\\n\\n.filter-button.active {\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  color: white;\\n  border-color: transparent;\\n  box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.filter-button.active i {\\n  color: white;\\n}\\n\\n/* Animation pour les boutons de filtre */\\n@keyframes pulse-filter {\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.05); }\\n  100% { transform: scale(1); }\\n}\\n\\n.filter-button.active {\\n  animation: pulse-filter 2s infinite;\\n}\\n\\n/* Provider information */\\n.provider-info {\\n  display: block;\\n  font-size: 12px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-top: 4px;\\n}\\n\\n/* Availability information */\\n.availability {\\n  font-size: 12px;\\n  display: block;\\n  margin-top: 4px;\\n  font-weight: 500;\\n}\\n\\n.availability i.fa-check-circle {\\n  color: #4CAF50;\\n}\\n\\n.availability i.fa-check-circle + span {\\n  color: #4CAF50;\\n}\\n\\n.availability i.fa-exclamation-triangle {\\n  color: #F44336;\\n}\\n\\n.availability i.fa-exclamation-triangle + span {\\n  color: #F44336;\\n}\\n\\n/* Expiration information */\\n.expiration {\\n  color: #FF9800;\\n  font-size: 12px;\\n  display: block;\\n  margin-top: 4px;\\n}\\n\\n/* Branded fare badge */\\n.flight-badge.branded {\\n  background-color: #9C27B0;\\n}\\n\\n/* Feature groups */\\n.feature-group {\\n  margin-bottom: 15px;\\n}\\n\\n.feature-group h4 {\\n  font-size: 14px;\\n  margin-bottom: 8px;\\n  color: rgba(0, 0, 0, 0.7);\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.feature-group .feature {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 4px;\\n  padding-left: 20px;\\n}\\n\\n/* Offer ID */\\n.offer-id {\\n  font-family: monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n}\\n\\n/* Price breakdown section */\\n.price-breakdown-section {\\n  margin: 15px 0;\\n}\\n\\n.price-breakdown-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.price-breakdown-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.price-breakdown-summary:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.price-breakdown-content {\\n  padding: 15px;\\n}\\n\\n.breakdown-group h4 {\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.breakdown-item {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 8px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.breakdown-item.service-fee {\\n  color: #FF5722;\\n}\\n\\n.breakdown-total {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-top: 10px;\\n  padding-top: 10px;\\n  border-top: 1px dashed rgba(0, 0, 0, 0.1);\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n}\\n\\n/* Segments section */\\n.segments-section {\\n  margin: 15px 0;\\n}\\n\\n.segments-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.segments-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.segments-summary:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.segments-content {\\n  padding: 15px;\\n}\\n\\n.segment-item {\\n  margin-bottom: 20px;\\n  padding-bottom: 15px;\\n  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);\\n}\\n\\n.segment-item:last-child {\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n  border-bottom: none;\\n}\\n\\n.segment-header {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 10px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.segment-number {\\n  font-weight: 600;\\n}\\n\\n.segment-route {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 10px;\\n}\\n\\n.layover-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 13px;\\n  color: #FF9800;\\n  margin-top: 10px;\\n  padding: 8px;\\n  background-color: rgba(255, 152, 0, 0.05);\\n  border-radius: 4px;\\n}\\n\\n/* Branded fare section */\\n.branded-fare-section {\\n  margin: 15px 0;\\n}\\n\\n.branded-fare-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.branded-fare-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(156, 39, 176, 0.05);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: #9C27B0;\\n}\\n\\n.branded-fare-summary:hover {\\n  background-color: rgba(156, 39, 176, 0.1);\\n}\\n\\n.branded-fare-content {\\n  padding: 15px;\\n}\\n\\n.branded-fare-description {\\n  margin-bottom: 15px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.branded-fare-features h4 {\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature-item {\\n  margin-bottom: 10px;\\n}\\n\\n.feature-name {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature-description {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.results-header h3 {\\n  color: var(--primary-dark);\\n  margin-bottom: 8px;\\n  font-size: 24px;\\n  font-weight: 600;\\n}\\n\\n.results-header p {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 14px;\\n}\\n\\n.results-count {\\n  font-weight: 700;\\n  color: var(--primary-color);\\n}\\n\\n.filter-label {\\n  font-size: 0.9em;\\n  font-style: italic;\\n  color: #666;\\n  margin-left: 5px;\\n}\\n\\n.flight-list {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 25px;\\n}\\n\\n.flight-card {\\n  background-color: white;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  box-shadow:\\n    0 2px 8px rgba(0, 0, 0, 0.05),\\n    0 0 0 1px rgba(0, 0, 0, 0.03);\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.flight-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.02) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  pointer-events: none;\\n}\\n\\n.flight-card:hover {\\n  transform: translateY(-8px) scale(1.01);\\n  box-shadow:\\n    0 15px 30px rgba(0, 0, 0, 0.08),\\n    0 0 0 1px rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n.flight-card.unavailable {\\n  opacity: 0.7;\\n  transform: none !important;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03) !important;\\n}\\n\\n.flight-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px;\\n  background-color: #f9f9f9;\\n  border-bottom: 1px solid #eaeaea;\\n  position: relative;\\n  flex-wrap: wrap;\\n}\\n\\n.flight-header::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -1px;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.airline-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.airline-logo {\\n  width: 40px;\\n  height: 40px;\\n  object-fit: contain;\\n  padding: 5px;\\n  background-color: white;\\n  border-radius: 50%;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .airline-logo {\\n  transform: scale(1.1);\\n}\\n\\n.airline-name {\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: var(--primary-dark);\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .airline-name {\\n  color: var(--primary-color);\\n}\\n\\n.flight-price {\\n  text-align: right;\\n  position: relative;\\n}\\n\\n.price {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: var(--primary-color);\\n  display: block;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n\\n.flight-card:hover .price {\\n  color: var(--secondary-color);\\n  transform: scale(1.05);\\n}\\n\\n.price::before {\\n  content: '';\\n  position: absolute;\\n  bottom: -3px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background-color: var(--secondary-color);\\n  transition: width 0.3s ease;\\n}\\n\\n.flight-card:hover .price::before {\\n  width: 100%;\\n}\\n\\n.availability {\\n  font-size: 13px;\\n  color: #e74c3c;\\n  font-weight: 500;\\n  margin-top: 5px;\\n}\\n\\n.flight-details {\\n  padding: 15px;\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.flight-route {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n  position: relative;\\n  width: 100%;\\n}\\n\\n@media (max-width: 768px) {\\n  .flight-route {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n}\\n\\n.departure, .arrival {\\n  flex: 1;\\n  position: relative;\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .departure {\\n  transform: translateX(-5px);\\n}\\n\\n.flight-card:hover .arrival {\\n  transform: translateX(5px);\\n}\\n\\n.time {\\n  font-size: 22px;\\n  font-weight: 700;\\n  margin-bottom: 8px;\\n  color: var(--primary-dark);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.time i {\\n  color: var(--primary-color);\\n  font-size: 18px;\\n  opacity: 0;\\n  transform: translateY(5px);\\n  transition: all 0.3s ease;\\n}\\n\\n.flight-card:hover .departure .time i {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.flight-card:hover .arrival .time i {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.location {\\n  font-size: 15px;\\n  color: rgba(0, 0, 0, 0.6);\\n  font-weight: 500;\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .location {\\n  color: var(--primary-color);\\n}\\n\\n.location small {\\n  display: block;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.4);\\n  margin-top: 3px;\\n}\\n\\n.flight-duration {\\n  flex: 1;\\n  text-align: center;\\n  padding: 0 20px;\\n  position: relative;\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .flight-duration {\\n  transform: translateY(-5px);\\n}\\n\\n.duration-line {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 10px;\\n  position: relative;\\n}\\n\\n.dot {\\n  width: 10px;\\n  height: 10px;\\n  background-color: var(--primary-color);\\n  border-radius: 50%;\\n  position: relative;\\n  z-index: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0 0 4px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.flight-card:hover .dot {\\n  background-color: var(--secondary-color);\\n  transform: scale(1.2);\\n  box-shadow: 0 0 0 6px rgba(var(--secondary-color-rgb), 0.15);\\n}\\n\\n.line {\\n  flex: 1;\\n  height: 2px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));\\n  margin: 0 8px;\\n  position: relative;\\n  transition: height 0.3s ease, background 0.3s ease;\\n}\\n\\n.flight-card:hover .line {\\n  height: 3px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\\n}\\n\\n.duration-text {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n  color: var(--primary-dark);\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .duration-text {\\n  color: var(--primary-color);\\n}\\n\\n.stops {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.5);\\n  font-weight: 500;\\n  padding: 4px 12px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  border-radius: 50px;\\n  display: inline-block;\\n  transition: all 0.3s ease;\\n}\\n\\n.flight-card:hover .stops {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n}\\n\\n.flight-info {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.5);\\n  margin-top: 20px;\\n  padding-top: 15px;\\n  border-top: 1px dashed rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.flight-actions {\\n  padding: 20px 25px;\\n  border-top: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  display: flex;\\n  gap: 15px;\\n  justify-content: flex-end;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.flight-actions::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.view-details-button {\\n  padding: 10px 18px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  color: var(--primary-dark);\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.1);\\n  border-radius: 50px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.view-details-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.1) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.view-details-button i {\\n  font-size: 16px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.view-details-button:hover {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.view-details-button:hover::before {\\n  opacity: 1;\\n}\\n\\n.view-details-button:hover i {\\n  transform: translateX(3px);\\n}\\n\\n.select-button {\\n  padding: 10px 24px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  color: white;\\n  border: none;\\n  border-radius: 50px;\\n  font-size: 15px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.select-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);\\n  z-index: 1;\\n}\\n\\n.select-button::after {\\n  content: '';\\n  position: absolute;\\n  top: -50%;\\n  left: -50%;\\n  width: 200%;\\n  height: 200%;\\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 60%);\\n  opacity: 0;\\n  transform: scale(0.5);\\n  transition: transform 0.8s ease, opacity 0.8s ease;\\n  z-index: 1;\\n}\\n\\n.select-button i {\\n  font-size: 16px;\\n  transition: transform 0.3s ease;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.select-button span {\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.select-button:hover {\\n  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));\\n  transform: translateY(-3px) scale(1.02);\\n  box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.select-button:hover::after {\\n  opacity: 1;\\n  transform: scale(1);\\n}\\n\\n.select-button:hover i {\\n  transform: translateX(3px);\\n  animation: pulse 1s infinite;\\n}\\n\\n.select-button:active {\\n  transform: translateY(-1px) scale(1);\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.select-button:disabled {\\n  background: linear-gradient(135deg, #b0b0b0, #d0d0d0);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  opacity: 0.7;\\n}\\n\\n/* Styles pour les informations de bagages am\\u00E9lior\\u00E9es */\\n.baggage-details {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n  margin-top: 8px;\\n}\\n\\n.baggage-item {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n}\\n\\n.baggage-item.checked {\\n  background-color: #e7f5ff;\\n  border-color: #c5e1f9;\\n}\\n\\n.baggage-item.cabin {\\n  background-color: #f3f0ff;\\n  border-color: #e5dbff;\\n}\\n\\n.baggage-item i {\\n  font-size: 16px;\\n  margin-right: 10px;\\n  color: #4a6fa5;\\n}\\n\\n.baggage-item.cabin i {\\n  color: #6741d9;\\n}\\n\\n.baggage-info {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.baggage-type {\\n  font-weight: 600;\\n  font-size: 14px;\\n  color: #343a40;\\n}\\n\\n.baggage-specs {\\n  font-size: 12px;\\n  color: #6c757d;\\n  margin-top: 2px;\\n}\\n\\n/* Am\\u00E9lioration des groupes de caract\\u00E9ristiques */\\n.feature-group {\\n  margin-bottom: 15px;\\n  padding-bottom: 15px;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n\\n.feature-group:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n}\\n\\n.feature-group h4 {\\n  display: flex;\\n  align-items: center;\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n  color: #495057;\\n}\\n\\n.feature-group h4 i {\\n  margin-right: 8px;\\n  color: #4a6fa5;\\n}\\n\\n.feature {\\n  margin-top: 5px;\\n  font-size: 13px;\\n  color: #495057;\\n}\\n\\n/* Am\\u00E9lioration des ic\\u00F4nes de statut */\\n.text-success {\\n  color: #28a745;\\n}\\n\\n.text-danger {\\n  color: #dc3545;\\n}\\n\\n/* Style pour la note explicative */\\n.source-info {\\n  font-size: 11px;\\n  color: #6c757d;\\n  font-style: italic;\\n  margin-left: 5px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"/* Styles globaux pour \\u00E9viter le d\\u00E9filement horizontal */\\n.search-form * {\\n  box-sizing: border-box;\\n  max-width: 100%;\\n}\\n\\n.search-form input,\\n.search-form select,\\n.search-form button {\\n  max-width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n/* Styles pour la carte de recherche - Design professionnel inspir\\u00E9 des agences de voyage */\\n.search-card {\\n  background-color: var(--surface-color, white);\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n  padding: 20px;\\n  position: relative;\\n  margin-bottom: 20px;\\n  border: none;\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  box-sizing: border-box;\\n  overflow: hidden;\\n}\\n\\n/* Barre sup\\u00E9rieure bleue (style Booking.com) */\\n.search-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 4px;\\n  background-color: var(--primary-color);\\n  z-index: 1;\\n}\\n\\n/* Pas d'effet de survol exag\\u00E9r\\u00E9, juste une ombre l\\u00E9g\\u00E8rement plus prononc\\u00E9e */\\n.search-card:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n\\n/* Formulaire sur une seule ligne */\\n.single-line-form {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n  width: 100%;\\n}\\n\\n/* Disposition pour les \\u00E9crans larges - Carte de recherche horizontale */\\n@media (min-width: 992px) {\\n  .search-card {\\n    flex-direction: row;\\n    flex-wrap: nowrap;\\n    align-items: flex-end;\\n    padding: 15px;\\n    height: auto;\\n    border-radius: 0;\\n    box-shadow: none;\\n    margin-bottom: 0;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n  }\\n\\n  .single-line-form {\\n    flex-direction: row;\\n    align-items: flex-end;\\n    flex-wrap: nowrap;\\n    gap: 10px; /* Ajouter de l'espace entre les champs */\\n  }\\n\\n  /* Ajustement pour les champs From et To */\\n  .single-line-form .form-group:nth-child(1),\\n  .single-line-form .form-group:nth-child(5) {\\n    flex: 1.5;\\n    z-index: 4;\\n  }\\n\\n  /* Ajustement pour les autres champs */\\n  .single-line-form .form-group:nth-child(7),\\n  .single-line-form .form-group:nth-child(8),\\n  .single-line-form .form-group:nth-child(9) {\\n    flex: 0.8;\\n  }\\n\\n  /* Ajustement pour le conteneur du bouton d'\\u00E9change */\\n  .single-line-form .swap-button-container {\\n    flex: 0 0 auto;\\n    margin: 0;\\n    padding: 0 5px;\\n    z-index: 6;\\n  }\\n\\n  .form-group {\\n    min-width: 0; /* Permet aux \\u00E9l\\u00E9ments de r\\u00E9tr\\u00E9cir en dessous de leur largeur minimale */\\n    margin-bottom: 0;\\n    flex: 1;\\n    position: relative;\\n    z-index: 3;\\n    padding: 0 5px; /* Ajouter un peu d'espace de chaque c\\u00F4t\\u00E9 */\\n  }\\n\\n  .form-group.checkbox-group {\\n    flex: 0 0 auto;\\n  }\\n\\n  .search-button-container {\\n    flex: 0 0 auto;\\n    margin-top: 0;\\n    margin-left: 10px;\\n    text-align: center;\\n    padding-left: 5px;\\n  }\\n\\n  .search-button {\\n    width: auto;\\n    white-space: nowrap;\\n    padding: 10px 20px;\\n    height: 40px;\\n  }\\n}\\n\\n/* S\\u00E9lecteur de type de voyage - Style onglets (comme Booking.com) */\\n.trip-type-selector {\\n  display: flex;\\n  margin-bottom: 20px;\\n  position: relative;\\n  border-bottom: 1px solid #e7e7e7;\\n  padding-bottom: 0;\\n}\\n\\n.trip-type-option {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 20px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  font-weight: 500;\\n  color: #333;\\n  background-color: transparent;\\n  border: none;\\n  border-bottom: 3px solid transparent;\\n  position: relative;\\n  min-width: 100px;\\n  justify-content: center;\\n}\\n\\n.trip-type-option i {\\n  color: #666;\\n  font-size: 16px;\\n  transition: color 0.2s ease;\\n}\\n\\n.trip-type-option span {\\n  transition: color 0.2s ease;\\n}\\n\\n.trip-type-option.selected {\\n  color: var(--primary-color);\\n  font-weight: 600;\\n  border-bottom: 3px solid var(--primary-color);\\n  background-color: transparent;\\n}\\n\\n.trip-type-option.selected i {\\n  color: var(--primary-color);\\n}\\n\\n.trip-type-option:not(.selected):hover {\\n  color: var(--primary-color);\\n  border-bottom-color: rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.trip-type-option:not(.selected):hover i {\\n  color: var(--primary-color);\\n}\\n\\n.trip-type-option.disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n/* Rang\\u00E9es de formulaire - Style agences de voyage */\\n.form-row {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n\\n.locations-row {\\n  position: relative;\\n}\\n\\n/* Groupes de formulaire */\\n.form-group {\\n  flex: 1;\\n  position: relative;\\n}\\n\\n.location-type-selector {\\n  display: none; /* Cach\\u00E9 mais fonctionnel */\\n}\\n\\n/* \\u00C9tiquettes */\\nlabel {\\n  display: block;\\n  margin-bottom: 6px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 14px;\\n}\\n\\n/* Champs de saisie avec ic\\u00F4nes */\\n.input-with-icon {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.input-with-icon i {\\n  position: absolute;\\n  left: 12px;\\n  color: #666;\\n  font-size: 16px;\\n  z-index: 2;\\n}\\n\\n.form-control {\\n  width: 100%;\\n  padding: 12px 12px 12px 40px;\\n  border: 1px solid #e7e7e7;\\n  border-radius: 4px;\\n  font-size: 15px;\\n  transition: border-color 0.2s ease, box-shadow 0.2s ease;\\n  background-color: white;\\n  color: #333;\\n  height: 40px;\\n}\\n\\n.form-control:focus {\\n  outline: none;\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.form-control::placeholder {\\n  color: #999;\\n}\\n\\n/* Effet de focus sur le groupe entier */\\n.form-group:focus-within label {\\n  color: var(--primary-color);\\n}\\n\\n.form-group:focus-within .input-with-icon i {\\n  color: var(--primary-color);\\n}\\n\\n/* Ajustements pour le formulaire sur une seule ligne */\\n@media (min-width: 992px) {\\n  .single-line-form .form-group {\\n    margin-bottom: 0;\\n  }\\n\\n  .single-line-form label {\\n    font-size: 12px;\\n    margin-bottom: 4px;\\n  }\\n\\n  .single-line-form .form-control {\\n    font-size: 14px;\\n  }\\n\\n  .single-line-form .input-with-icon i {\\n    font-size: 14px;\\n  }\\n\\n  .single-line-form .error-message {\\n    position: absolute;\\n    font-size: 11px;\\n    bottom: -18px;\\n    left: 0;\\n    white-space: nowrap;\\n  }\\n}\\n\\n/* Conteneur du bouton d'\\u00E9change */\\n.swap-button-container {\\n  display: flex;\\n  align-items: flex-end;\\n  justify-content: center;\\n  padding-bottom: 10px; /* Aligner avec les champs de formulaire */\\n  position: relative;\\n  z-index: 5;\\n}\\n\\n/* Bouton d'\\u00E9change de lieux - Style agences de voyage */\\n.swap-locations-btn {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: var(--primary-color);\\n  color: white;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n\\n.swap-locations-btn:hover {\\n  background-color: var(--primary-dark);\\n  transform: rotate(180deg);\\n}\\n\\n.swap-locations-btn:active {\\n  transform: scale(0.95) rotate(180deg);\\n}\\n\\n.swap-locations-btn i {\\n  font-size: 16px;\\n}\\n\\n@media (min-width: 992px) {\\n  .swap-button-container {\\n    padding-bottom: 10px;\\n  }\\n}\\n\\n/* Options d'emplacement dans l'autocompl\\u00E9tion */\\n.location-option {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.location-name {\\n  font-weight: 500;\\n}\\n\\n.location-details {\\n  display: flex;\\n  gap: 12px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.location-code {\\n  font-weight: 500;\\n}\\n\\n.location-type {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.location-type i {\\n  font-size: 12px;\\n  color: #2989d8;\\n}\\n\\n/* Interrupteurs \\u00E0 bascule */\\n.toggle-switch {\\n  position: relative;\\n  display: inline-flex;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n\\n.toggle-input {\\n  opacity: 0;\\n  width: 0;\\n  height: 0;\\n  position: absolute;\\n}\\n\\n.toggle-label {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n}\\n\\n.toggle-inner {\\n  position: relative;\\n  display: inline-block;\\n  width: 50px;\\n  height: 24px;\\n  background-color: rgba(0, 0, 0, 0.12);\\n  border-radius: 12px;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.toggle-inner:before {\\n  content: '';\\n  position: absolute;\\n  left: 2px;\\n  top: 2px;\\n  width: 20px;\\n  height: 20px;\\n  background-color: white;\\n  border-radius: 50%;\\n  transition: transform 0.2s ease;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\\n}\\n\\n.toggle-input:checked + .toggle-label .toggle-inner {\\n  background-color: #2989d8;\\n}\\n\\n.toggle-input:checked + .toggle-label .toggle-inner:before {\\n  transform: translateX(26px);\\n}\\n\\n.toggle-switch-label {\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.8);\\n}\\n\\n.toggle-switch.small .toggle-inner {\\n  width: 40px;\\n  height: 20px;\\n}\\n\\n.toggle-switch.small .toggle-inner:before {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.toggle-switch.small .toggle-input:checked + .toggle-label .toggle-inner:before {\\n  transform: translateX(20px);\\n}\\n\\n/* Conteneur du bouton de recherche - Style agences de voyage */\\n.search-button-container {\\n  margin-top: 20px;\\n  display: flex;\\n  justify-content: center;\\n  position: relative;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-button-container {\\n    margin-top: 0;\\n  }\\n}\\n\\n/* Bouton de recherche - Style Booking.com */\\n.search-button {\\n  background-color: var(--primary-color);\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 12px 24px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 140px;\\n  justify-content: center;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (min-width: 992px) {\\n  .search-button {\\n    padding: 0 20px;\\n    font-size: 15px;\\n    min-width: 100px;\\n    height: 40px;\\n  }\\n\\n  .single-line-form .search-button-container {\\n    margin-top: 21px; /* Aligner avec les champs de formulaire (hauteur du label + marge) */\\n  }\\n}\\n\\n.search-button:hover:not(:disabled) {\\n  background-color: var(--primary-dark);\\n}\\n\\n.search-button:active:not(:disabled) {\\n  transform: translateY(1px);\\n}\\n\\n.search-button:disabled {\\n  background-color: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n.search-button i {\\n  font-size: 16px;\\n}\\n\\n/* Options avanc\\u00E9es - Design riche */\\n.advanced-options-container {\\n  margin-top: 25px;\\n  position: relative;\\n}\\n\\n.advanced-options-container::before {\\n  content: '';\\n  position: absolute;\\n  top: -10px;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.advanced-options-container summary {\\n  cursor: pointer;\\n  color: var(--primary-color);\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  padding: 12px 20px;\\n  transition: all 0.3s ease;\\n  outline: none;\\n  border-radius: 50px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.05);\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  margin: 0 auto;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.advanced-options-container summary::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.1) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.advanced-options-container summary:hover {\\n  color: var(--secondary-color);\\n  background-color: rgba(var(--primary-color-rgb), 0.08);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.advanced-options-container summary:hover::before {\\n  opacity: 1;\\n}\\n\\n.advanced-options-container summary i {\\n  font-size: 18px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.advanced-options-container[open] summary i {\\n  transform: rotate(180deg);\\n}\\n\\n.advanced-options {\\n  margin-top: 20px;\\n  padding: 25px;\\n  background-color: rgba(var(--primary-color-rgb), 0.03);\\n  border-radius: 16px;\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  box-shadow:\\n    inset 0 1px 8px rgba(var(--primary-color-rgb), 0.05),\\n    0 5px 15px rgba(0, 0, 0, 0.03);\\n  position: relative;\\n  animation: scaleIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\\n  overflow: hidden;\\n}\\n\\n.advanced-options::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background:\\n    radial-gradient(circle at top right, rgba(var(--primary-color-rgb), 0.05) 0%, rgba(var(--primary-color-rgb), 0) 70%),\\n    radial-gradient(circle at bottom left, rgba(var(--secondary-color-rgb), 0.05) 0%, rgba(var(--secondary-color-rgb), 0) 70%);\\n  pointer-events: none;\\n}\\n\\n.checkbox-options {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n}\\n\\n.checkbox-options .form-group {\\n  flex: 1 0 45%;\\n  transition: transform 0.3s ease;\\n}\\n\\n.checkbox-options .form-group:hover {\\n  transform: translateY(-2px);\\n}\\n\\n/* Messages d'erreur */\\n.error-message {\\n  color: #e74c3c;\\n  font-size: 13px;\\n  margin-top: 6px;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.error-message i {\\n  font-size: 14px;\\n}\\n\\n/* Animation de chargement */\\n.spinner-container {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spinner {\\n  width: 20px;\\n  height: 20px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  border-top-color: white;\\n  animation: spin 0.8s linear infinite;\\n}\\n\\n@keyframes spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n/* Styles pour les r\\u00E9sultats de recherche */\\n.search-results-container {\\n  background-color: white;\\n  border-radius: 16px;\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\\n  padding: 24px;\\n  margin-top: 24px;\\n}\\n\\n/* Animation de chargement personnalis\\u00E9e */\\n.loading-animation {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n}\\n\\n.plane-loader {\\n  position: relative;\\n  width: 200px;\\n  height: 100px;\\n  margin-bottom: 24px;\\n}\\n\\n.plane-loader i {\\n  position: absolute;\\n  font-size: 32px;\\n  color: #2989d8;\\n  animation: fly 3s infinite linear;\\n  top: 40%;\\n  left: 0;\\n}\\n\\n.cloud {\\n  position: absolute;\\n  width: 50px;\\n  height: 20px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 20px;\\n}\\n\\n.cloud:nth-child(2) {\\n  top: 20%;\\n  left: 20%;\\n  animation: cloud 8s infinite linear;\\n}\\n\\n.cloud:nth-child(3) {\\n  top: 60%;\\n  left: 40%;\\n  animation: cloud 6s infinite linear;\\n}\\n\\n.cloud:nth-child(4) {\\n  top: 40%;\\n  left: 60%;\\n  animation: cloud 10s infinite linear;\\n}\\n\\n@keyframes fly {\\n  0% {\\n    transform: translateX(0) rotate(0);\\n  }\\n  100% {\\n    transform: translateX(200px) rotate(0);\\n  }\\n}\\n\\n@keyframes cloud {\\n  0% {\\n    transform: translateX(0);\\n  }\\n  100% {\\n    transform: translateX(-200px);\\n  }\\n}\\n\\n.loading-animation p {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n/* Styles pour les r\\u00E9sultats */\\n.results-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.08);\\n}\\n\\n.results-title h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 4px;\\n}\\n\\n.results-count {\\n  font-weight: 600;\\n  color: #2989d8;\\n}\\n\\n.filter-option {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.filter-select {\\n  padding: 8px 12px;\\n  border: 1px solid rgba(0, 0, 0, 0.12);\\n  border-radius: 6px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.8);\\n  background-color: white;\\n}\\n\\n/* Carte de vol */\\n.flight-card {\\n  background-color: white;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n  margin-bottom: 20px;\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.flight-card:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\\n}\\n\\n.flight-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.airline-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.airline-logo-container {\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n.airline-logo {\\n  max-width: 32px;\\n  max-height: 32px;\\n  object-fit: contain;\\n}\\n\\n.airline-icon {\\n  font-size: 20px;\\n  color: #2989d8;\\n}\\n\\n.airline-details {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.airline-name {\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  font-size: 15px;\\n}\\n\\n.flight-number {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.flight-badges {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.flight-badge {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 4px 8px;\\n  background-color: rgba(41, 137, 216, 0.1);\\n  color: #2989d8;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.flight-price {\\n  text-align: right;\\n}\\n\\n.price-label {\\n  display: block;\\n  font-size: 12px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 4px;\\n}\\n\\n.price {\\n  font-size: 22px;\\n  font-weight: 700;\\n  color: #2989d8;\\n}\\n\\n.availability {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  color: #e74c3c;\\n  font-size: 12px;\\n  margin-top: 4px;\\n}\\n\\n.flight-details {\\n  padding: 20px;\\n}\\n\\n.flight-route {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.departure, .arrival {\\n  flex: 1;\\n}\\n\\n.time {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 6px;\\n}\\n\\n.location {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.airport-code {\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.7);\\n  font-size: 15px;\\n}\\n\\n.city-name {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 13px;\\n}\\n\\n.flight-duration {\\n  flex: 1;\\n  text-align: center;\\n  padding: 0 20px;\\n}\\n\\n.duration-line {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 10px;\\n}\\n\\n.dot {\\n  width: 10px;\\n  height: 10px;\\n  background-color: #2989d8;\\n  border-radius: 50%;\\n  z-index: 1;\\n}\\n\\n.departure-dot {\\n  background-color: #4CAF50;\\n}\\n\\n.arrival-dot {\\n  background-color: #F44336;\\n}\\n\\n.line-container {\\n  flex: 1;\\n  position: relative;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.line {\\n  width: 100%;\\n  height: 2px;\\n  background-color: #2989d8;\\n}\\n\\n.plane-icon {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  color: #2989d8;\\n  font-size: 14px;\\n}\\n\\n.duration-text {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.7);\\n  margin-bottom: 6px;\\n}\\n\\n.stops {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.stop-count {\\n  font-weight: 600;\\n  color: #F44336;\\n}\\n\\n.stops.direct {\\n  color: #4CAF50;\\n  font-weight: 500;\\n}\\n\\n.flight-features {\\n  display: flex;\\n  gap: 16px;\\n  margin-top: 16px;\\n  padding-top: 16px;\\n  border-top: 1px dashed rgba(0, 0, 0, 0.1);\\n}\\n\\n.feature {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature i {\\n  color: #2989d8;\\n  font-size: 14px;\\n}\\n\\n.flight-actions {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n  padding: 16px 20px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.view-details-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 10px 16px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: rgba(0, 0, 0, 0.7);\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.view-details-button:hover {\\n  background-color: rgba(0, 0, 0, 0.1);\\n}\\n\\n.select-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 10px 16px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(41, 137, 216, 0.3);\\n}\\n\\n.select-button:hover {\\n  background-color: #1e5799;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(41, 137, 216, 0.4);\\n}\\n\\n.select-button:disabled {\\n  background-color: rgba(0, 0, 0, 0.1);\\n  color: rgba(0, 0, 0, 0.4);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n/* Message pas de r\\u00E9sultats */\\n.no-results {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.no-results-icon {\\n  width: 80px;\\n  height: 80px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n}\\n\\n.no-results-icon i {\\n  font-size: 32px;\\n  color: rgba(0, 0, 0, 0.3);\\n}\\n\\n.no-results h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 8px;\\n}\\n\\n.no-results p {\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 24px;\\n  max-width: 500px;\\n}\\n\\n.no-results-suggestions {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  justify-content: center;\\n}\\n\\n.suggestion {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 10px 16px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 8px;\\n  color: rgba(0, 0, 0, 0.7);\\n  font-size: 14px;\\n}\\n\\n.suggestion i {\\n  color: #2989d8;\\n}\\n\\n/* Message d'erreur */\\n.error-container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.error-icon {\\n  width: 80px;\\n  height: 80px;\\n  background-color: rgba(231, 76, 60, 0.1);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n}\\n\\n.error-icon i {\\n  font-size: 32px;\\n  color: #e74c3c;\\n}\\n\\n.error-container h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 8px;\\n}\\n\\n.error-container .error-message {\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 24px;\\n  font-size: 15px;\\n  justify-content: center;\\n}\\n\\n.retry-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 20px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 15px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(41, 137, 216, 0.3);\\n}\\n\\n.retry-button:hover {\\n  background-color: #1e5799;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(41, 137, 216, 0.4);\\n}\\n\\n/* Styles responsifs */\\n@media (max-width: 768px) {\\n  .form-row {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .swap-locations-btn {\\n    display: none;\\n  }\\n\\n  .flight-route {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n\\n  .departure, .arrival {\\n    text-align: center;\\n  }\\n\\n  .flight-duration {\\n    margin: 16px 0;\\n  }\\n\\n  .flight-header {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .airline-info {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n\\n  .flight-badges {\\n    justify-content: center;\\n  }\\n\\n  .flight-price {\\n    text-align: center;\\n    width: 100%;\\n  }\\n\\n  .flight-features {\\n    flex-direction: column;\\n    gap: 12px;\\n    align-items: center;\\n  }\\n\\n  .flight-actions {\\n    flex-direction: column;\\n  }\\n\\n  .view-details-button, .select-button {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .page-header {\\n    flex-direction: column;\\n    gap: 24px;\\n  }\\n\\n  .header-content {\\n    text-align: center;\\n  }\\n\\n  .page-title {\\n    font-size: 1.75rem;\\n  }\\n\\n  .checkbox-options .form-group {\\n    flex: 1 0 100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "of", "FlightClassType", "LocationType", "PassengerType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "location_r13", "code", "city", "ɵɵtemplate", "SearchPriceComponent_mat_option_34_span_5_Template", "SearchPriceComponent_mat_option_34_span_6_Template", "ɵɵelement", "ɵɵproperty", "name", "type", "ɵɵpureFunction5", "_c0", "ɵɵtextInterpolate1", "location_r18", "SearchPriceComponent_mat_option_47_span_5_Template", "SearchPriceComponent_mat_option_47_span_6_Template", "type_r23", "value", "label", "flightClass_r24", "ɵɵlistener", "SearchPriceComponent_div_157_div_2_Template_button_click_7_listener", "ɵɵrestoreView", "_r29", "ctx_r28", "ɵɵnextContext", "ɵɵresetView", "onSearch", "ctx_r26", "errorMessage", "ctx_r35", "currentFilter", "SearchPriceComponent_div_157_div_3_p_6_span_4_Template", "ctx_r31", "filteredResults", "length", "SearchPriceComponent_div_157_div_3_div_7_button_2_Template_button_click_0_listener", "restoredCtx", "_r39", "filter_r37", "$implicit", "ctx_r38", "applyFilter", "ɵɵclassProp", "ctx_r36", "icon", "SearchPriceComponent_div_157_div_3_div_7_button_2_Template", "ctx_r32", "filterOptions", "flight_r40", "items", "airline", "thumbnailFull", "ɵɵsanitizeUrl", "flightProvider", "displayName", "provider", "flightClass", "ɵɵpureFunction2", "_c1", "ctx_r47", "isFlightAvailable", "ctx_r48", "formatExpirationDate", "offers", "expiresOn", "stopCount", "baggage_r68", "weight", "piece", "SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_div_1_span_5_Template", "SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_div_1_span_6_Template", "ɵɵelementContainerStart", "SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_div_1_Template", "ɵɵelementContainerEnd", "ɵɵpipeBind3", "ctx_r63", "filterBaggageByType", "baggageInformations", "baggage_r75", "SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_div_1_span_5_Template", "SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_div_1_span_6_Template", "SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_div_1_Template", "ctx_r64", "SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_5_Template", "SearchPriceComponent_div_157_div_3_div_9_div_55_ng_container_6_Template", "SearchPriceComponent_div_157_div_3_div_9_div_55_div_7_Template", "SearchPriceComponent_div_157_div_3_div_9_div_55_div_8_Template", "ctx_r51", "service_r83", "SearchPriceComponent_div_157_div_3_div_9_div_56_div_4_Template", "services", "ɵɵclassMap", "reservableInfo", "reservable", "SearchPriceComponent_div_157_div_3_div_9_div_57_div_4_Template", "ɵɵtextInterpolate2", "ctx_r89", "getPassengerTypeName", "item_r91", "passengerType", "passengerCount", "ɵɵpipeBind2", "price", "amount", "currency", "serviceFee", "SearchPriceComponent_div_157_div_3_div_9_div_63_div_1_div_3_Template", "SearchPriceComponent_div_157_div_3_div_9_div_63_div_1_div_4_Template", "priceBreakDown", "SearchPriceComponent_div_157_div_3_div_9_div_63_div_1_Template", "ctx_r98", "calculateLayoverTime", "segment_r96", "segments", "i_r97", "arrival", "SearchPriceComponent_div_157_div_3_div_9_div_64_div_6_div_34_Template", "flightNo", "departure", "ctx_r95", "formatDate", "date", "airport", "formatDuration", "duration", "SearchPriceComponent_div_157_div_3_div_9_div_64_div_6_Template", "brandedFare", "description", "feature_r107", "explanations", "text", "SearchPriceComponent_div_157_div_3_div_9_div_65_div_7_div_3_div_3_Template", "commercialName", "SearchPriceComponent_div_157_div_3_div_9_div_65_div_7_div_3_Template", "features", "SearchPriceComponent_div_157_div_3_div_9_div_65_div_6_Template", "SearchPriceComponent_div_157_div_3_div_9_div_65_div_7_Template", "SearchPriceComponent_div_157_div_3_div_9_img_4_Template", "SearchPriceComponent_div_157_div_3_div_9_i_5_Template", "SearchPriceComponent_div_157_div_3_div_9_span_11_Template", "SearchPriceComponent_div_157_div_3_div_9_span_13_Template", "SearchPriceComponent_div_157_div_3_div_9_span_14_Template", "SearchPriceComponent_div_157_div_3_div_9_span_15_Template", "SearchPriceComponent_div_157_div_3_div_9_span_21_Template", "SearchPriceComponent_div_157_div_3_div_9_span_22_Template", "SearchPriceComponent_div_157_div_3_div_9_div_44_Template", "SearchPriceComponent_div_157_div_3_div_9_div_45_Template", "SearchPriceComponent_div_157_div_3_div_9_div_55_Template", "SearchPriceComponent_div_157_div_3_div_9_div_56_Template", "SearchPriceComponent_div_157_div_3_div_9_div_57_Template", "SearchPriceComponent_div_157_div_3_div_9_div_63_Template", "SearchPriceComponent_div_157_div_3_div_9_div_64_Template", "SearchPriceComponent_div_157_div_3_div_9_div_65_Template", "SearchPriceComponent_div_157_div_3_div_9_Template_button_click_67_listener", "_r113", "ctx_r112", "showAllDetails", "SearchPriceComponent_div_157_div_3_div_9_Template_button_click_70_listener", "ctx_r114", "selectThisFlight", "ctx_r33", "<PERSON><PERSON><PERSON>", "getMinPrice", "SearchPriceComponent_div_157_div_3_p_5_Template", "SearchPriceComponent_div_157_div_3_p_6_Template", "SearchPriceComponent_div_157_div_3_div_7_Template", "SearchPriceComponent_div_157_div_3_div_9_Template", "SearchPriceComponent_div_157_div_3_div_10_Template", "ctx_r27", "searchResults", "isLoading", "SearchPriceComponent_div_157_div_1_Template", "SearchPriceComponent_div_157_div_2_Template", "SearchPriceComponent_div_157_div_3_Template", "ctx_r12", "SearchPriceComponent", "constructor", "fb", "productService", "router", "departureLocations", "arrivalLocations", "hasSearched", "lastSearchId", "passengerTypes", "Adult", "Child", "Infant", "flightClasses", "PROMO", "ECONOMY", "BUSINESS", "minDate", "Date", "toISOString", "split", "searchForm", "group", "productType", "required", "serviceTypes", "departureLocation", "arrivalLocation", "departureDate", "min", "max", "nonStop", "culture", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightBaggageGetOption", "ngOnInit", "setupAutocomplete", "preloadLocations", "console", "log", "filterValue", "results", "sortByPrice", "sortByDuration", "sortByRecommendation", "flights", "sort", "a", "b", "priceA", "getMinPriceAmount", "priceB", "durationA", "Number", "MAX_VALUE", "durationB", "scoreA", "calculateRecommendationScore", "scoreB", "flight", "item", "offer", "availability", "undefined", "seatInfo", "availableSeatCount", "priceScore", "durationScore", "stopScore", "availabilityScore", "Math", "weights", "stops", "reduce", "modalDiv", "document", "createElement", "style", "position", "top", "left", "width", "height", "backgroundColor", "zIndex", "display", "justifyContent", "alignItems", "modalContent", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "color", "fontFamily", "closeButton", "innerHTML", "right", "border", "background", "fontSize", "cursor", "transition", "on<PERSON><PERSON>ver", "onmouseout", "onclick", "body", "<PERSON><PERSON><PERSON><PERSON>", "header", "marginBottom", "paddingBottom", "borderBottom", "logo", "title", "textContent", "margin", "fontWeight", "append<PERSON><PERSON><PERSON>", "detailsContainer", "flexDirection", "gap", "generalInfo", "createSection", "airlineInfo", "airlineLogo", "src", "alt", "marginRight", "airlineIcon", "airlineName", "internationalCode", "flightNumberRow", "createInfoRow", "flightDateRow", "flightDate", "toLocaleDateString", "durationRow", "classRow", "stopsRow", "routeSection", "routeVisual", "textAlign", "flex", "departureTime", "toLocaleTimeString", "hour", "minute", "departureAirport", "marginTop", "departureCity", "connectionLine", "line", "plane", "marginLeft", "arrivalTime", "arrivalAirport", "arrivalCity", "segmentsTitle", "segmentsList", "for<PERSON>ach", "segment", "index", "segmentItem", "segmentHeader", "segmentTitle", "segmentDuration", "segmentRoute", "segmentDeparture", "depTime", "depAirport", "arrow", "segmentArrival", "arrTime", "arrAirport", "layover", "currentArrival", "getTime", "nextDeparture", "layoverTime", "floor", "offersSection", "offersList", "offerItem", "offerHeader", "offerTitle", "offerPrice", "offerDetails", "gridTemplateColumns", "availabilityValue", "expires", "toLocaleString", "baggageTitle", "baggageContainer", "checkedBaggage", "filter", "baggageType", "cabinBaggage", "handBaggage", "baggage", "baggageItem", "baggageIcon", "baggageInfo", "baggageDetails", "detailsText", "servicesSection", "servicesList", "listStyle", "service", "serviceItem", "iconClass", "section", "section<PERSON><PERSON><PERSON>", "className", "sectionTitle", "row", "labelElement", "valueElement", "offerId", "id", "searchId", "navigate", "queryParams", "error", "departureLocationType", "arrivalLocationType", "getLocationsByType", "subscribe", "locations", "get", "valueChanges", "pipe", "location", "toLowerCase", "includes", "displayLocation", "displayText", "Airport", "invalid", "markFormGroupTouched", "formValue", "request", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "ArrivalLocations", "Passengers", "count", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "searchPrice", "next", "response", "success", "JSON", "stringify", "flightsWithOffers", "f", "availabilityValues", "flatMap", "o", "availabilityCounts", "acc", "val", "reservableFlights", "some", "groupEnd", "requestId", "Object", "keys", "messages", "message", "formGroup", "values", "controls", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "minutes", "hours", "mins", "dateString", "weekday", "day", "month", "min<PERSON>ffer", "formattedAmount", "showAllDepartureLocations", "locationType", "input", "getElementById", "focus", "dispatchEvent", "Event", "showAllArrivalLocations", "swapLocations", "patchValue", "getBaggageTypeName", "Array", "isArray", "currentSegment", "nextSegment", "diffMs", "diffMins", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProductService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "SearchPriceComponent_Template", "rf", "ctx", "SearchPriceComponent_Template_form_ngSubmit_21_listener", "SearchPriceComponent_Template_input_click_31_listener", "SearchPriceComponent_mat_option_34_Template", "SearchPriceComponent_div_35_Template", "SearchPriceComponent_Template_button_click_37_listener", "SearchPriceComponent_Template_input_click_44_listener", "SearchPriceComponent_mat_option_47_Template", "SearchPriceComponent_div_48_Template", "SearchPriceComponent_div_56_Template", "SearchPriceComponent_option_69_Template", "SearchPriceComponent_option_76_Template", "SearchPriceComponent_i_86_Template", "SearchPriceComponent_span_87_Template", "SearchPriceComponent_div_88_Template", "SearchPriceComponent_div_157_Template", "_r0", "bind", "tmp_4_0", "touched", "_r3", "tmp_8_0", "tmp_10_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { Observable, of } from 'rxjs';\nimport { ProductService } from '../../../services/product.service';\nimport { LocationOption } from '../../../models/price-search-request.model';\nimport { PriceSearchRequest } from '../../../models/price-search-request.model';\nimport { PriceSearchResponse, Flight } from '../../../models/price-search-response.model';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css'],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchPriceComponent implements OnInit {\n  searchForm: FormGroup;\n  departureLocations: LocationOption[] = [];\n  arrivalLocations: LocationOption[] = [];\n  isLoading = false;\n  searchResults: Flight[] = [];\n  filteredResults: Flight[] = [];\n  hasSearched = false;\n  errorMessage = '';\n  lastSearchId = '';\n\n  // Filter options\n  currentFilter: string = 'recommended';\n  filterOptions = [\n    { value: 'recommended', label: 'Recommended', icon: 'fa-star' },\n    { value: 'cheapest', label: 'Cheapest', icon: 'fa-dollar-sign' },\n    { value: 'shortest', label: 'Shortest', icon: 'fa-clock' }\n  ];\n\n  // Passenger type options\n  passengerTypes = [\n    { value: PassengerType.Adult, label: 'Adult' },\n    { value: PassengerType.Child, label: 'Child' },\n    { value: PassengerType.Infant, label: 'Infant' }\n  ];\n\n  // Flight class options\n  flightClasses = [\n    { value: FlightClassType.PROMO, label: 'Promo' },\n    { value: FlightClassType.ECONOMY, label: 'Economy' },\n    { value: FlightClassType.BUSINESS, label: 'Business' }\n  ];\n\n  // Date minimale (aujourd'hui)\n  minDate: string;\n\n  constructor(\n    private fb: FormBuilder,\n    private productService: ProductService,\n    private router: Router\n  ) {\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required], // Flight par défaut\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      arrivalLocation: ['', Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required], // Adult par défaut\n\n      // Options de vol\n      flightClass: [0, Validators.required], // PROMO par défaut\n      nonStop: [false],\n\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n\n  ngOnInit(): void {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n\n  // Méthode pour appliquer le filtre sélectionné\n  applyFilter(filterValue: string): void {\n    this.currentFilter = filterValue;\n\n    if (!this.searchResults || this.searchResults.length === 0) {\n      this.filteredResults = [];\n      return;\n    }\n\n    // Créer une copie des résultats pour ne pas modifier l'original\n    const results = [...this.searchResults];\n\n    switch (filterValue) {\n      case 'cheapest':\n        this.filteredResults = this.sortByPrice(results);\n        break;\n      case 'shortest':\n        this.filteredResults = this.sortByDuration(results);\n        break;\n      case 'recommended':\n      default:\n        this.filteredResults = this.sortByRecommendation(results);\n        break;\n    }\n  }\n\n  // Trier les vols par prix (du moins cher au plus cher)\n  private sortByPrice(flights: Flight[]): Flight[] {\n    return flights.sort((a, b) => {\n      const priceA = this.getMinPriceAmount(a);\n      const priceB = this.getMinPriceAmount(b);\n      return priceA - priceB;\n    });\n  }\n\n  // Trier les vols par durée (du plus court au plus long)\n  private sortByDuration(flights: Flight[]): Flight[] {\n    return flights.sort((a, b) => {\n      const durationA = a.items && a.items[0] ? a.items[0].duration : Number.MAX_VALUE;\n      const durationB = b.items && b.items[0] ? b.items[0].duration : Number.MAX_VALUE;\n      return durationA - durationB;\n    });\n  }\n\n  // Trier les vols par recommandation (combinaison de prix, durée et autres facteurs)\n  private sortByRecommendation(flights: Flight[]): Flight[] {\n    return flights.sort((a, b) => {\n      // Calculer un score pour chaque vol basé sur plusieurs facteurs\n      const scoreA = this.calculateRecommendationScore(a);\n      const scoreB = this.calculateRecommendationScore(b);\n      return scoreB - scoreA; // Ordre décroissant (score plus élevé = meilleur)\n    });\n  }\n\n  // Calculer un score de recommandation pour un vol\n  private calculateRecommendationScore(flight: Flight): number {\n    if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n      return 0;\n    }\n\n    const item = flight.items[0];\n    const offer = flight.offers[0];\n\n    // Facteurs à considérer pour le score\n    const price = this.getMinPriceAmount(flight);\n    const duration = item.duration;\n    const stopCount = item.stopCount || 0;\n    const availability = offer.availability !== undefined ? offer.availability :\n                        (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n\n    // Normaliser les valeurs (plus le score est élevé, meilleur est le vol)\n    const priceScore = 1000 / (price + 100); // Inverse du prix (moins cher = meilleur score)\n    const durationScore = 1000 / (duration + 100); // Inverse de la durée (plus court = meilleur score)\n    const stopScore = 1 / (stopCount + 1); // Moins d'escales = meilleur score\n    const availabilityScore = Math.min(availability, 10) / 10; // Plus de disponibilité = meilleur score\n\n    // Pondération des facteurs (ajustable selon les préférences)\n    const weights = {\n      price: 0.4,      // 40% importance pour le prix\n      duration: 0.3,   // 30% importance pour la durée\n      stops: 0.2,      // 20% importance pour les escales\n      availability: 0.1 // 10% importance pour la disponibilité\n    };\n\n    // Calculer le score final pondéré\n    return (\n      priceScore * weights.price +\n      durationScore * weights.duration +\n      stopScore * weights.stops +\n      availabilityScore * weights.availability\n    );\n  }\n\n  // Obtenir le montant du prix minimum pour un vol\n  private getMinPriceAmount(flight: Flight): number {\n    if (!flight.offers || flight.offers.length === 0) {\n      return Number.MAX_VALUE;\n    }\n\n    return flight.offers.reduce((min, offer) =>\n      offer.price && offer.price.amount < min ? offer.price.amount : min,\n      flight.offers[0].price ? flight.offers[0].price.amount : Number.MAX_VALUE\n    );\n  }\n\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight: Flight): void {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n\n    header.appendChild(logo);\n    header.appendChild(title);\n\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n\n        generalInfo.appendChild(airlineInfo);\n      }\n\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n\n      routeSection.appendChild(routeVisual);\n\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n\n          segmentsList.appendChild(segmentItem);\n\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n\n            segmentsList.appendChild(layover);\n          }\n        });\n\n        routeSection.appendChild(segmentsList);\n      }\n    }\n\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability :\n                                 (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n\n          const baggageContainer = document.createElement('div');\n          baggageContainer.style.display = 'flex';\n          baggageContainer.style.flexDirection = 'column';\n          baggageContainer.style.gap = '10px';\n          baggageContainer.style.marginBottom = '15px';\n\n          // Filtrer et regrouper les bagages par type\n          const checkedBaggage = offer.baggageInformations.filter(b => b.baggageType === 2);\n          const cabinBaggage = offer.baggageInformations.filter(b => b.baggageType === 1);\n          const handBaggage = offer.baggageInformations.filter(b => b.baggageType === 3);\n\n          // Bagages en soute\n          if (checkedBaggage.length > 0) {\n            checkedBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#e7f5ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #c5e1f9';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-suitcase\" style=\"color: #4a6fa5; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Checked Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n\n          // Bagages cabine\n          if (cabinBaggage.length > 0) {\n            cabinBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#f3f0ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #e5dbff';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Cabin Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          } else {\n            // Ajouter un bagage cabine par défaut si aucun n'est spécifié\n            const baggageItem = document.createElement('div');\n            baggageItem.style.display = 'flex';\n            baggageItem.style.alignItems = 'center';\n            baggageItem.style.padding = '10px 15px';\n            baggageItem.style.backgroundColor = '#f3f0ff';\n            baggageItem.style.borderRadius = '6px';\n            baggageItem.style.border = '1px solid #e5dbff';\n\n            const baggageIcon = document.createElement('div');\n            baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n\n            const baggageInfo = document.createElement('div');\n            baggageInfo.style.display = 'flex';\n            baggageInfo.style.flexDirection = 'column';\n\n            const baggageType = document.createElement('div');\n            baggageType.textContent = 'Cabin Baggage';\n            baggageType.style.fontWeight = '600';\n            baggageType.style.fontSize = '14px';\n\n            const baggageDetails = document.createElement('div');\n            baggageDetails.style.fontSize = '12px';\n            baggageDetails.style.color = '#666';\n            baggageDetails.textContent = 'Included';\n\n            baggageInfo.appendChild(baggageType);\n            baggageInfo.appendChild(baggageDetails);\n\n            baggageItem.appendChild(baggageIcon);\n            baggageItem.appendChild(baggageInfo);\n            baggageContainer.appendChild(baggageItem);\n          }\n\n          // Bagages à main\n          if (handBaggage.length > 0) {\n            handBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#fff4e6';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #ffe8cc';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-shopping-bag\" style=\"color: #e8590c; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Hand Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n\n          offerItem.appendChild(baggageContainer);\n        }\n\n        offerItem.appendChild(offerDetails);\n\n        offersList.appendChild(offerItem);\n      });\n\n      offersSection.appendChild(offersList);\n    }\n\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n\n  // Méthode utilitaire pour créer une section\n  private createSection(title: string, iconClass: string): HTMLElement {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n\n    return section;\n  }\n\n  // Méthode utilitaire pour créer une ligne d'information\n  private createInfoRow(label: string, value: string): HTMLElement {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n\n    return row;\n  }\n\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight: Flight): void {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n\n  preloadLocations(): void {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n\n  setupAutocomplete(): void {\n    // Charger les locations par type par défaut\n    const departureLocationType = 2; // Type 2 (City) par défaut\n    const arrivalLocationType = 5;   // Type 5 (Airport) par défaut\n\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(departureLocationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(departureLocationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.departureLocations = locations;\n      });\n\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(arrivalLocationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(arrivalLocationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n  }\n\n  displayLocation(location: LocationOption | null): string {\n    if (!location) return '';\n\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n\n  onSearch(): void {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n\n    const formValue = this.searchForm.value;\n\n    // Créer la requête de recherche entièrement dynamique\n    const request: PriceSearchRequest = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [\n        {\n          id: formValue.departureLocation?.id || '',\n          type: 2 // Type 2 (City) par défaut\n        }\n      ],\n      ArrivalLocations: [\n        {\n          id: formValue.arrivalLocation?.id || '',\n          type: 5 // Type 5 (Airport) par défaut\n        }\n      ],\n      Passengers: [\n        {\n          type: formValue.passengerType,\n          count: formValue.passengerCount\n        }\n      ],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n\n    this.productService.searchPrice(request)\n      .subscribe({\n        next: (response: PriceSearchResponse) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n\n            // Appliquer le filtre actuel aux résultats\n            this.applyFilter(this.currentFilter);\n\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            console.log('Full API Response:', JSON.stringify(response, null, 2));\n\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o =>\n                o.availability !== undefined ? o.availability : (o.seatInfo ? o.seatInfo.availableSeatCount : 0)\n              ));\n              console.log('Availability values:', availabilityValues);\n\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc, val) => {\n                if (val !== undefined) {\n                  acc[val] = (acc[val] || 0) + 1;\n                }\n                return acc;\n              }, {} as Record<number, number>);\n              console.log('Availability counts:', availabilityCounts);\n\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f =>\n                f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true)\n              );\n              console.log('Reservable flights:', reservableFlights.length);\n\n              console.groupEnd();\n            }\n\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n  }\n\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup: FormGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n\n  // Formater la durée en heures et minutes\n  formatDuration(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n\n  // Formater la date pour l'affichage\n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight: Flight): string {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n\n    const minOffer = flight.offers.reduce((min, offer) =>\n      offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight: Flight): boolean {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability :\n                             (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n    return availabilityValue > 0;\n  }\n\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations(): void {\n    const locationType = 2; // Type 2 (City) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations(): void {\n    const locationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations(): void {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      arrivalLocation: departureLocation\n    });\n  }\n\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType: number): string {\n    switch (baggageType) {\n      case 1: return 'Cabin Baggage';\n      case 2: return 'Checked Baggage';\n      case 3: return 'Hand Baggage';\n      default: return 'Baggage';\n    }\n  }\n\n  // Filtrer les bagages par type\n  filterBaggageByType(baggageInformations: any[], type: number): any[] {\n    if (!baggageInformations || !Array.isArray(baggageInformations)) {\n      return [];\n    }\n    return baggageInformations.filter(baggage => baggage.baggageType === type);\n  }\n\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType: number): string {\n    switch (passengerType) {\n      case 1: return 'Adult';\n      case 2: return 'Child';\n      case 3: return 'Infant';\n      default: return 'Passenger';\n    }\n  }\n\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment: any, nextSegment: any): string {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date ||\n        !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n}\n", "<div class=\"search-price-container\">\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <h1 class=\"page-title\">Find Your Perfect Flight</h1>\n      <p class=\"page-subtitle\">Search and compare flights to destinations worldwide</p>\n    </div>\n    <div class=\"header-illustration\">\n      <img src=\"assets/images/airplane-banner.jpg\" alt=\"Airplane in the sky\">\n    </div>\n  </div>\n\n  <div class=\"search-content\">\n    <div class=\"search-form-container\">\n      <!-- Logo pour la version desktop -->\n      <div class=\"sidebar-logo\">\n        <div class=\"logo-container\">\n          <i class=\"fas fa-plane-departure logo-icon\"></i>\n          <span class=\"logo-text\">TravelEase</span>\n        </div>\n      </div>\n\n      <div class=\"search-form-header\">\n        <h2>Search Flights</h2>\n        <p>Enter your travel details below</p>\n      </div>\n\n      <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSearch()\" class=\"search-form\">\n        <!-- Main Search Fields -->\n        <div class=\"search-card\">\n          <!-- Hidden Product Type and Service Types -->\n          <input type=\"hidden\" formControlName=\"productType\" value=\"3\">\n          <input type=\"hidden\" formControlName=\"serviceTypes\" value=\"['1']\">\n\n          <!-- Formulaire sur une seule ligne -->\n          <div class=\"single-line-form\">\n            <!-- Departure Location -->\n            <div class=\"form-group\">\n              <label for=\"departureLocation\">From</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-plane-departure\"></i>\n                <input\n                  type=\"text\"\n                  id=\"departureLocation\"\n                  formControlName=\"departureLocation\"\n                  placeholder=\"City or airport\"\n                  [matAutocomplete]=\"departureAuto\"\n                  class=\"form-control\"\n                  (click)=\"showAllDepartureLocations()\"\n                >\n              </div>\n              <mat-autocomplete #departureAuto=\"matAutocomplete\" [displayWith]=\"displayLocation.bind(this)\">\n                <mat-option *ngFor=\"let location of departureLocations\" [value]=\"location\">\n                  <div class=\"location-option\">\n                    <div class=\"location-name\">{{ location.name }}</div>\n                    <div class=\"location-details\">\n                      <span *ngIf=\"location.code\" class=\"location-code\">{{ location.code }}</span>\n                      <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">{{ location.city }}</span>\n                      <span class=\"location-type\">\n                        <i class=\"fas\"\n                          [ngClass]=\"{\n                            'fa-flag': location.type === 1,\n                            'fa-city': location.type === 2,\n                            'fa-building': location.type === 3,\n                            'fa-home': location.type === 4,\n                            'fa-plane': location.type === 5\n                          }\"></i>\n                        {{ location.type === 1 ? 'Country' :\n                           location.type === 2 ? 'City' :\n                           location.type === 3 ? 'Town' :\n                           location.type === 4 ? 'Village' :\n                           location.type === 5 ? 'Airport' : '' }}\n                      </span>\n                    </div>\n                  </div>\n                </mat-option>\n              </mat-autocomplete>\n              <div *ngIf=\"searchForm.get('departureLocation')?.invalid && searchForm.get('departureLocation')?.touched\" class=\"error-message\">\n                <i class=\"fas fa-exclamation-circle\"></i> Please select a departure location\n              </div>\n            </div>\n\n\n\n            <!-- Swap Locations Button (Between From and To) -->\n            <div class=\"swap-button-container\">\n              <button type=\"button\" class=\"swap-locations-btn\" (click)=\"swapLocations()\">\n                <i class=\"fas fa-exchange-alt\"></i>\n              </button>\n            </div>\n\n            <!-- Arrival Location -->\n            <div class=\"form-group\">\n              <label for=\"arrivalLocation\">To</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-plane-arrival\"></i>\n                <input\n                  type=\"text\"\n                  id=\"arrivalLocation\"\n                  formControlName=\"arrivalLocation\"\n                  placeholder=\"City or airport\"\n                  [matAutocomplete]=\"arrivalAuto\"\n                  class=\"form-control\"\n                  (click)=\"showAllArrivalLocations()\"\n                >\n              </div>\n              <mat-autocomplete #arrivalAuto=\"matAutocomplete\" [displayWith]=\"displayLocation.bind(this)\">\n                <mat-option *ngFor=\"let location of arrivalLocations\" [value]=\"location\">\n                  <div class=\"location-option\">\n                    <div class=\"location-name\">{{ location.name }}</div>\n                    <div class=\"location-details\">\n                      <span *ngIf=\"location.code\" class=\"location-code\">{{ location.code }}</span>\n                      <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">{{ location.city }}</span>\n                      <span class=\"location-type\">\n                        <i class=\"fas\"\n                          [ngClass]=\"{\n                            'fa-flag': location.type === 1,\n                            'fa-city': location.type === 2,\n                            'fa-building': location.type === 3,\n                            'fa-home': location.type === 4,\n                            'fa-plane': location.type === 5\n                          }\"></i>\n                        {{ location.type === 1 ? 'Country' :\n                           location.type === 2 ? 'City' :\n                           location.type === 3 ? 'Town' :\n                           location.type === 4 ? 'Village' :\n                           location.type === 5 ? 'Airport' : '' }}\n                      </span>\n                    </div>\n                  </div>\n                </mat-option>\n              </mat-autocomplete>\n              <div *ngIf=\"searchForm.get('arrivalLocation')?.invalid && searchForm.get('arrivalLocation')?.touched\" class=\"error-message\">\n                <i class=\"fas fa-exclamation-circle\"></i> Please select an arrival location\n              </div>\n            </div>\n\n\n          </div>\n\n          <!-- Deuxième ligne de formulaire pour les autres champs importants -->\n          <div class=\"single-line-form\">\n            <!-- Departure Date -->\n            <div class=\"form-group\">\n              <label for=\"departureDate\">Date</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-calendar-alt\"></i>\n                <input\n                  type=\"date\"\n                  id=\"departureDate\"\n                  formControlName=\"departureDate\"\n                  [min]=\"minDate\"\n                  class=\"form-control\"\n                >\n              </div>\n              <div *ngIf=\"searchForm.get('departureDate')?.invalid && searchForm.get('departureDate')?.touched\" class=\"error-message\">\n                <i class=\"fas fa-exclamation-circle\"></i> Please select a date\n              </div>\n            </div>\n\n            <!-- Passengers -->\n            <div class=\"form-group\">\n              <label for=\"passengerCount\">Passengers</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-user-friends\"></i>\n                <input\n                  type=\"number\"\n                  id=\"passengerCount\"\n                  formControlName=\"passengerCount\"\n                  min=\"1\"\n                  max=\"9\"\n                  class=\"form-control\"\n                >\n              </div>\n            </div>\n\n            <!-- Passenger Type - Nouveau champ ajouté -->\n            <div class=\"form-group\">\n              <label for=\"passengerType\">Passenger Type</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-users\"></i>\n                <select\n                  id=\"passengerType\"\n                  formControlName=\"passengerType\"\n                  class=\"form-control\"\n                >\n                  <option *ngFor=\"let type of passengerTypes\" [value]=\"type.value\">{{ type.label }}</option>\n                </select>\n              </div>\n            </div>\n\n            <!-- Flight Class -->\n            <div class=\"form-group\">\n              <label for=\"flightClass\">Class</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-chair\"></i>\n                <select\n                  id=\"flightClass\"\n                  formControlName=\"flightClass\"\n                  class=\"form-control\"\n                >\n                  <option *ngFor=\"let flightClass of flightClasses\" [value]=\"flightClass.value\">{{ flightClass.label }}</option>\n                </select>\n              </div>\n            </div>\n\n            <!-- Non-Stop Flight Option - Nouveau champ ajouté -->\n            <div class=\"form-group checkbox-group\">\n              <div class=\"toggle-switch small\">\n                <input\n                  type=\"checkbox\"\n                  id=\"nonStop\"\n                  formControlName=\"nonStop\"\n                  class=\"toggle-input\"\n                >\n                <label for=\"nonStop\" class=\"toggle-label\">\n                  <span class=\"toggle-inner\"></span>\n                  <span class=\"toggle-switch-label\">Non-stop flights only</span>\n                </label>\n              </div>\n            </div>\n\n            <!-- Search Button -->\n            <div class=\"search-button-container\">\n              <button\n                type=\"submit\"\n                class=\"search-button\"\n                [disabled]=\"searchForm.invalid || isLoading\"\n              >\n                <i class=\"fas fa-search\" *ngIf=\"!isLoading\"></i>\n                <span *ngIf=\"!isLoading\">Search</span>\n                <div *ngIf=\"isLoading\" class=\"spinner-container\">\n                  <div class=\"spinner\"></div>\n                </div>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Advanced Options -->\n        <div class=\"advanced-options-container\">\n          <details>\n            <summary>\n              <i class=\"fas fa-cog\"></i> Advanced Options\n            </summary>\n            <div class=\"advanced-options\">\n              <div class=\"form-row\">\n                <!-- Culture -->\n                <div class=\"form-group\">\n                  <label for=\"culture\">Language</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-language\"></i>\n                    <select\n                      id=\"culture\"\n                      formControlName=\"culture\"\n                      class=\"form-control\"\n                    >\n                      <option value=\"en-US\">English (US)</option>\n                      <option value=\"fr-FR\">Français</option>\n                    </select>\n                  </div>\n                </div>\n\n                <!-- Currency -->\n                <div class=\"form-group\">\n                  <label for=\"currency\">Currency</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-money-bill-wave\"></i>\n                    <select\n                      id=\"currency\"\n                      formControlName=\"currency\"\n                      class=\"form-control\"\n                    >\n                      <option value=\"EUR\">Euro (€)</option>\n                      <option value=\"USD\">Dollar ($)</option>\n                    </select>\n                  </div>\n                </div>\n\n                <!-- Baggage Options -->\n                <div class=\"form-group\">\n                  <label for=\"flightBaggageGetOption\">Baggage Options</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-suitcase\"></i>\n                    <select\n                      id=\"flightBaggageGetOption\"\n                      formControlName=\"flightBaggageGetOption\"\n                      class=\"form-control\"\n                    >\n                      <option [value]=\"0\">All options</option>\n                      <option [value]=\"1\">Baggage included only</option>\n                      <option [value]=\"2\">No baggage only</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Additional Options -->\n              <div class=\"form-row checkbox-options\">\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"acceptPendingProviders\"\n                      formControlName=\"acceptPendingProviders\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"acceptPendingProviders\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Accept pending providers</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"forceFlightBundlePackage\"\n                      formControlName=\"forceFlightBundlePackage\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"forceFlightBundlePackage\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Force flight bundle package</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"disablePackageOfferTotalPrice\"\n                      formControlName=\"disablePackageOfferTotalPrice\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"disablePackageOfferTotalPrice\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Disable package offer total price</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"calculateFlightFees\"\n                      formControlName=\"calculateFlightFees\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"calculateFlightFees\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Calculate flight fees</span>\n                    </label>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </details>\n        </div>\n      </form>\n    </div>\n\n    <!-- Search Results -->\n    <div class=\"search-results-container\" *ngIf=\"hasSearched\">\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-animation\">\n          <div class=\"plane-loader\">\n            <i class=\"fas fa-plane\"></i>\n            <div class=\"cloud\"></div>\n            <div class=\"cloud\"></div>\n            <div class=\"cloud\"></div>\n          </div>\n          <p>Searching for the best flights...</p>\n        </div>\n      </div>\n\n      <div *ngIf=\"!isLoading && errorMessage\" class=\"error-container\">\n        <div class=\"error-icon\">\n          <i class=\"fas fa-exclamation-circle\"></i>\n        </div>\n        <h3>Oops! Something went wrong</h3>\n        <p class=\"error-message\">{{ errorMessage }}</p>\n        <button class=\"retry-button\" (click)=\"onSearch()\">\n          <i class=\"fas fa-redo\"></i> Try Again\n        </button>\n      </div>\n\n      <div *ngIf=\"!isLoading && !errorMessage\" class=\"search-results-content\">\n        <div class=\"results-header\">\n          <div class=\"results-title\">\n            <h3>Flight Options</h3>\n            <p *ngIf=\"searchResults.length === 0\">No flights found for your search. Please modify your criteria.</p>\n            <p *ngIf=\"searchResults.length > 0\">\n              <span class=\"results-count\">{{ filteredResults.length }}</span> flights found\n              <span class=\"filter-label\" *ngIf=\"currentFilter !== 'recommended'\"> ({{ currentFilter }})</span>\n            </p>\n          </div>\n\n          <div class=\"results-filters\" *ngIf=\"searchResults.length > 0\">\n            <div class=\"filter-buttons\">\n              <button\n                *ngFor=\"let filter of filterOptions\"\n                class=\"filter-button\"\n                [class.active]=\"currentFilter === filter.value\"\n                (click)=\"applyFilter(filter.value)\">\n                <i class=\"fas\" [ngClass]=\"filter.icon\"></i>\n                {{ filter.label }}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"flight-list\">\n          <div *ngFor=\"let flight of filteredResults\" class=\"flight-card\" [class.unavailable]=\"!isFlightAvailable(flight)\">\n            <div class=\"flight-header\">\n              <div class=\"airline-info\">\n                <div class=\"airline-logo-container\">\n                  <img *ngIf=\"flight.items && flight.items[0] && flight.items[0].airline && flight.items[0].airline.thumbnailFull\"\n                       [src]=\"flight.items[0].airline.thumbnailFull\"\n                       alt=\"Airline logo\"\n                       class=\"airline-logo\">\n                  <i *ngIf=\"!(flight.items && flight.items[0] && flight.items[0].airline && flight.items[0].airline.thumbnailFull)\"\n                     class=\"fas fa-plane airline-icon\"></i>\n                </div>\n                <div class=\"airline-details\">\n                  <span class=\"airline-name\">{{ flight.items && flight.items[0] && flight.items[0].airline ? flight.items[0].airline.name : 'Airline' }}</span>\n                  <span class=\"flight-number\">{{ flight.items && flight.items[0] ? flight.items[0].flightNo : 'N/A' }}</span>\n                  <!-- Provider information -->\n                  <span class=\"provider-info\" *ngIf=\"flight.provider || (flight.items && flight.items[0] && flight.items[0].flightProvider)\">\n                    <i class=\"fas fa-tag\"></i>\n                    {{ (flight.items && flight.items[0] && flight.items[0].flightProvider && flight.items[0].flightProvider.displayName) ||\n                       (flight.items && flight.items[0] && flight.items[0].flightProvider && flight.items[0].flightProvider.name) ||\n                       'Provider ' + flight.provider }}\n                  </span>\n                </div>\n              </div>\n\n              <div class=\"flight-badges\">\n                <span class=\"flight-badge\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount === 0\">\n                  <i class=\"fas fa-bolt\"></i> Direct\n                </span>\n                <span class=\"flight-badge\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].flightClass\">\n                  <i class=\"fas fa-chair\"></i> {{ flight.items[0].flightClass.name }}\n                </span>\n                <!-- Branded fare badge -->\n                <span class=\"flight-badge branded\" *ngIf=\"flight.offers && flight.offers[0] && flight.offers[0].hasBrand\">\n                  <i class=\"fas fa-certificate\"></i> Branded Fare\n                </span>\n              </div>\n\n              <div class=\"flight-price\">\n                <span class=\"price-label\">Price per person</span>\n                <span class=\"price\">{{ getMinPrice(flight) }}</span>\n                <!-- Availability information - Afficher Available ou Not available -->\n                <span class=\"availability\" *ngIf=\"flight.offers && flight.offers.length > 0\">\n                  <i class=\"fas\" [ngClass]=\"{'fa-check-circle': isFlightAvailable(flight), 'fa-exclamation-triangle': !isFlightAvailable(flight)}\"></i>\n                  {{ isFlightAvailable(flight) ? 'Available' : 'Not available' }}\n                </span>\n\n                <!-- Expiration information - Afficher exactement la valeur de l'API -->\n                <span class=\"expiration\" *ngIf=\"flight.offers && flight.offers.length > 0 && flight.offers[0].expiresOn\">\n                  <i class=\"fas fa-clock\"></i> Expires: {{ formatExpirationDate(flight.offers[0].expiresOn) }}\n                </span>\n              </div>\n            </div>\n\n            <div class=\"flight-details\">\n              <div class=\"flight-route\">\n                <div class=\"departure\">\n                  <div class=\"time\">{{ flight.items && flight.items[0] && flight.items[0].departure ? formatDate(flight.items[0].departure.date) : 'N/A' }}</div>\n                  <div class=\"location\">\n                    <span class=\"airport-code\">{{ flight.items && flight.items[0] && flight.items[0].departure && flight.items[0].departure.airport ? flight.items[0].departure.airport.code : 'N/A' }}</span>\n                    <span class=\"city-name\">{{ flight.items && flight.items[0] && flight.items[0].departure && flight.items[0].departure.city ? flight.items[0].departure.city.name : 'N/A' }}</span>\n                  </div>\n                </div>\n\n                <div class=\"flight-duration\">\n                  <div class=\"duration-line\">\n                    <span class=\"dot departure-dot\"></span>\n                    <div class=\"line-container\">\n                      <span class=\"line\"></span>\n                      <span class=\"plane-icon\">\n                        <i class=\"fas fa-plane\"></i>\n                      </span>\n                    </div>\n                    <span class=\"dot arrival-dot\"></span>\n                  </div>\n                  <div class=\"duration-text\">\n                    <i class=\"fas fa-clock\"></i>\n                    {{ flight.items && flight.items[0] ? formatDuration(flight.items[0].duration) : 'N/A' }}\n                  </div>\n                  <div class=\"stops\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount > 0\">\n                    <span class=\"stop-count\">{{ flight.items[0].stopCount }}</span> stop{{ flight.items[0].stopCount > 1 ? 's' : '' }}\n                  </div>\n                  <div class=\"stops direct\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount === 0\">\n                    Direct flight\n                  </div>\n                </div>\n\n                <div class=\"arrival\">\n                  <div class=\"time\">{{ flight.items && flight.items[0] && flight.items[0].arrival ? formatDate(flight.items[0].arrival.date) : 'N/A' }}</div>\n                  <div class=\"location\">\n                    <span class=\"airport-code\">{{ flight.items && flight.items[0] && flight.items[0].arrival && flight.items[0].arrival.airport ? flight.items[0].arrival.airport.code : 'N/A' }}</span>\n                    <span class=\"city-name\">{{ flight.items && flight.items[0] && flight.items[0].arrival && flight.items[0].arrival.city ? flight.items[0].arrival.city.name : 'N/A' }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"flight-features\">\n                <!-- Baggage Information - Amélioré avec plus de détails et meilleure organisation -->\n                <div *ngIf=\"flight.items && flight.items[0]\" class=\"feature-group\">\n                  <h4><i class=\"fas fa-suitcase\"></i> Baggage</h4>\n                  <div class=\"baggage-details\">\n                    <!-- Bagages en soute (type 2) -->\n                    <ng-container *ngIf=\"flight.items[0].baggageInformations && flight.items[0].baggageInformations.length > 0\">\n                      <div class=\"baggage-item checked\"\n                           *ngFor=\"let baggage of filterBaggageByType(flight.items[0].baggageInformations, 2) | slice:0:2\">\n                        <i class=\"fas fa-suitcase\"></i>\n                        <div class=\"baggage-info\">\n                          <span class=\"baggage-type\">Checked Baggage</span>\n                          <span class=\"baggage-specs\" *ngIf=\"baggage.weight > 0\">{{ baggage.weight }} kg</span>\n                          <span class=\"baggage-specs\" *ngIf=\"baggage.piece > 0\">{{ baggage.piece }} piece(s)</span>\n                        </div>\n                      </div>\n                    </ng-container>\n\n                    <!-- Bagages cabine (type 1) -->\n                    <ng-container *ngIf=\"flight.items[0].baggageInformations && flight.items[0].baggageInformations.length > 0\">\n                      <div class=\"baggage-item cabin\"\n                           *ngFor=\"let baggage of filterBaggageByType(flight.items[0].baggageInformations, 1) | slice:0:1\">\n                        <i class=\"fas fa-briefcase\"></i>\n                        <div class=\"baggage-info\">\n                          <span class=\"baggage-type\">Cabin Baggage</span>\n                          <span class=\"baggage-specs\" *ngIf=\"baggage.piece > 0\">{{ baggage.piece }} piece(s)</span>\n                          <span class=\"baggage-specs\" *ngIf=\"baggage.weight > 0\">{{ baggage.weight }} kg</span>\n                        </div>\n                      </div>\n                    </ng-container>\n\n                    <!-- Bagage cabine par défaut si aucun n'est spécifié -->\n                    <div class=\"baggage-item cabin\" *ngIf=\"!flight.items[0].baggageInformations ||\n                                                           flight.items[0].baggageInformations.length === 0 ||\n                                                           filterBaggageByType(flight.items[0].baggageInformations, 1).length === 0\">\n                      <i class=\"fas fa-briefcase\"></i>\n                      <div class=\"baggage-info\">\n                        <span class=\"baggage-type\">Cabin Baggage</span>\n                        <span class=\"baggage-specs\">Included</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Message si aucune information de bagage n'est disponible -->\n                  <div *ngIf=\"!flight.items[0].baggageInformations || flight.items[0].baggageInformations.length === 0\" class=\"feature\">\n                    <span>No detailed baggage information available</span>\n                  </div>\n                </div>\n\n                <!-- Services Information -->\n                <div *ngIf=\"flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0\" class=\"feature-group\">\n                  <h4><i class=\"fas fa-concierge-bell\"></i> Services</h4>\n                  <div *ngFor=\"let service of flight.items[0].services | slice:0:3\" class=\"feature\">\n                    <span>{{ service.name || 'Service' }}</span>\n                  </div>\n                </div>\n\n                <!-- Offer Information - Clarification de la réservabilité -->\n                <div *ngIf=\"flight.offers && flight.offers.length > 0\" class=\"feature-group\">\n                  <h4><i class=\"fas fa-tag\"></i> Offer Details</h4>\n                  <div class=\"feature\" *ngIf=\"flight.offers[0].reservableInfo\">\n                    <span>\n                      <i [class]=\"flight.offers[0].reservableInfo.reservable ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger'\"></i>\n                      {{ flight.offers[0].reservableInfo.reservable ? 'Reservable' : 'Not reservable' }}\n                      <small class=\"source-info\">(from API response)</small>\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Price Breakdown Section (Collapsible) -->\n            <div class=\"price-breakdown-section\">\n              <details class=\"price-breakdown-details\">\n                <summary class=\"price-breakdown-summary\">\n                  <i class=\"fas fa-money-bill-wave\"></i> Price Breakdown\n                </summary>\n                <div class=\"price-breakdown-content\" *ngIf=\"flight.offers && flight.offers[0]\">\n                  <!-- Price Breakdown -->\n                  <div class=\"breakdown-group\" *ngIf=\"flight.offers[0].priceBreakDown && flight.offers[0].priceBreakDown.items\">\n                    <h4>Price Details</h4>\n                    <div class=\"breakdown-item\" *ngFor=\"let item of flight.offers[0].priceBreakDown.items\">\n                      <span class=\"passenger-type\">\n                        {{ getPassengerTypeName(item.passengerType) }} (x{{ item.passengerCount }})\n                      </span>\n                      <span class=\"item-price\">{{ item.price.amount | currency:item.price.currency }}</span>\n                    </div>\n                    <div class=\"breakdown-item service-fee\" *ngIf=\"flight.offers[0].serviceFee\">\n                      <span class=\"fee-label\">Service Fee</span>\n                      <span class=\"fee-amount\">{{ flight.offers[0].serviceFee.amount | currency:flight.offers[0].serviceFee.currency }}</span>\n                    </div>\n                    <div class=\"breakdown-total\">\n                      <span class=\"total-label\">Total</span>\n                      <span class=\"total-amount\">{{ flight.offers[0].price.amount | currency:flight.offers[0].price.currency }}</span>\n                    </div>\n                  </div>\n                </div>\n              </details>\n            </div>\n\n            <!-- Segment Information for Connecting Flights (Collapsible) -->\n            <div class=\"segments-section\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].segments && flight.items[0].segments.length > 1\">\n              <details class=\"segments-details\">\n                <summary class=\"segments-summary\">\n                  <i class=\"fas fa-plane-departure\"></i> Flight Segments ({{ flight.items[0].segments.length }})\n                </summary>\n                <div class=\"segments-content\">\n                  <div class=\"segment-item\" *ngFor=\"let segment of flight.items[0].segments; let i = index\">\n                    <div class=\"segment-header\">\n                      <span class=\"segment-number\">Segment {{ i + 1 }}</span>\n                      <span class=\"segment-airline\">{{ segment.airline && segment.airline.name || 'Airline' }}</span>\n                      <span class=\"segment-flight\">{{ segment.flightNo }}</span>\n                    </div>\n                    <div class=\"segment-route\">\n                      <div class=\"segment-departure\">\n                        <div class=\"time\">{{ segment.departure ? formatDate(segment.departure.date) : 'N/A' }}</div>\n                        <div class=\"location\">\n                          <span class=\"airport-code\">{{ segment.departure && segment.departure.airport && segment.departure.airport.code || 'N/A' }}</span>\n                          <span class=\"city-name\">{{ segment.departure && segment.departure.city && segment.departure.city.name || 'N/A' }}</span>\n                        </div>\n                      </div>\n                      <div class=\"segment-duration\">\n                        <div class=\"duration-line\">\n                          <span class=\"dot departure-dot\"></span>\n                          <div class=\"line-container\">\n                            <span class=\"line\"></span>\n                          </div>\n                          <span class=\"dot arrival-dot\"></span>\n                        </div>\n                        <div class=\"duration-text\">\n                          <i class=\"fas fa-clock\"></i> {{ formatDuration(segment.duration) }}\n                        </div>\n                      </div>\n                      <div class=\"segment-arrival\">\n                        <div class=\"time\">{{ segment.arrival ? formatDate(segment.arrival.date) : 'N/A' }}</div>\n                        <div class=\"location\">\n                          <span class=\"airport-code\">{{ segment.arrival && segment.arrival.airport && segment.arrival.airport.code || 'N/A' }}</span>\n                          <span class=\"city-name\">{{ segment.arrival && segment.arrival.city && segment.arrival.city.name || 'N/A' }}</span>\n                        </div>\n                      </div>\n                    </div>\n                    <!-- Layover information if not the last segment -->\n                    <div class=\"layover-info\" *ngIf=\"i < flight.items[0].segments.length - 1\">\n                      <i class=\"fas fa-hourglass-half\"></i>\n                      <span>{{ calculateLayoverTime(segment, flight.items[0].segments[i+1]) }} layover in {{ segment.arrival && segment.arrival.city && segment.arrival.city.name || 'connecting city' }}</span>\n                    </div>\n                  </div>\n                </div>\n              </details>\n            </div>\n\n            <!-- Branded Fare Information (if available) -->\n            <div class=\"branded-fare-section\" *ngIf=\"flight.offers && flight.offers[0] && flight.offers[0].brandedFare\">\n              <details class=\"branded-fare-details\">\n                <summary class=\"branded-fare-summary\">\n                  <i class=\"fas fa-certificate\"></i> {{ flight.offers[0].brandedFare.name || 'Branded Fare' }} Details\n                </summary>\n                <div class=\"branded-fare-content\">\n                  <div class=\"branded-fare-description\" *ngIf=\"flight.offers[0].brandedFare.description\">\n                    {{ flight.offers[0].brandedFare.description }}\n                  </div>\n                  <div class=\"branded-fare-features\" *ngIf=\"flight.offers[0].brandedFare.features && flight.offers[0].brandedFare.features.length > 0\">\n                    <h4>Features</h4>\n                    <div class=\"feature-item\" *ngFor=\"let feature of flight.offers[0].brandedFare.features\">\n                      <div class=\"feature-name\">{{ feature.commercialName || 'Feature' }}</div>\n                      <div class=\"feature-description\" *ngIf=\"feature.explanations && feature.explanations.length > 0\">\n                        {{ feature.explanations[0].text }}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </details>\n            </div>\n\n            <div class=\"flight-actions\">\n              <button class=\"view-details-button\" (click)=\"showAllDetails(flight)\">\n                <i class=\"fas fa-info-circle\"></i>\n                View All Details\n              </button>\n              <button class=\"select-button\" [disabled]=\"!flight.offers || flight.offers.length === 0 || !isFlightAvailable(flight)\" (click)=\"selectThisFlight(flight)\">\n                <i class=\"fas fa-check-circle\"></i>\n                Select This Flight\n              </button>\n            </div>\n          </div>\n\n          <!-- No Results Message -->\n          <div class=\"no-results\" *ngIf=\"!isLoading && !errorMessage && filteredResults.length === 0\">\n            <div class=\"no-results-icon\">\n              <i class=\"fas fa-search\"></i>\n            </div>\n            <h3>No flights found</h3>\n            <p>We couldn't find any flights matching your search criteria. Try adjusting your search parameters.</p>\n            <div class=\"no-results-suggestions\">\n              <div class=\"suggestion\">\n                <i class=\"fas fa-calendar-alt\"></i>\n                <span>Try different dates</span>\n              </div>\n              <div class=\"suggestion\">\n                <i class=\"fas fa-map-marker-alt\"></i>\n                <span>Try nearby airports</span>\n              </div>\n              <div class=\"suggestion\">\n                <i class=\"fas fa-plane\"></i>\n                <span>Include flights with stops</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnF,SAAqBC,EAAE,QAAQ,MAAM;AAKrC,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;;;;;;;;;;IC8CpEC,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,YAAA,CAAAC,IAAA,CAAmB;;;;;IACrEP,EAAA,CAAAC,cAAA,eAAyE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,YAAA,CAAAE,IAAA,CAAmB;;;;;;;;;;;;;;IALlGR,EAAA,CAAAC,cAAA,qBAA2E;IAE5CD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAS,UAAA,IAAAC,kDAAA,mBAA4E;IAC5EV,EAAA,CAAAS,UAAA,IAAAE,kDAAA,mBAAmG;IACnGX,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAY,SAAA,YAOS;IACTZ,EAAA,CAAAE,MAAA,GAKF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IApB2CH,EAAA,CAAAa,UAAA,UAAAP,YAAA,CAAkB;IAE3CN,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,YAAA,CAAAQ,IAAA,CAAmB;IAErCd,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAa,UAAA,SAAAP,YAAA,CAAAC,IAAA,CAAmB;IACnBP,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAa,UAAA,SAAAP,YAAA,CAAAS,IAAA,UAAAT,YAAA,CAAAE,IAAA,CAA0C;IAG7CR,EAAA,CAAAI,SAAA,GAME;IANFJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAX,YAAA,CAAAS,IAAA,QAAAT,YAAA,CAAAS,IAAA,QAAAT,YAAA,CAAAS,IAAA,QAAAT,YAAA,CAAAS,IAAA,QAAAT,YAAA,CAAAS,IAAA,QAME;IACJf,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAkB,kBAAA,MAAAZ,YAAA,CAAAS,IAAA,qBAAAT,YAAA,CAAAS,IAAA,kBAAAT,YAAA,CAAAS,IAAA,kBAAAT,YAAA,CAAAS,IAAA,qBAAAT,YAAA,CAAAS,IAAA,6BAKF;;;;;IAKRf,EAAA,CAAAC,cAAA,cAAgI;IAC9HD,EAAA,CAAAY,SAAA,YAAyC;IAACZ,EAAA,CAAAE,MAAA,2CAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgCEH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAc,YAAA,CAAAZ,IAAA,CAAmB;;;;;IACrEP,EAAA,CAAAC,cAAA,eAAyE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAc,YAAA,CAAAX,IAAA,CAAmB;;;;;IALlGR,EAAA,CAAAC,cAAA,qBAAyE;IAE1CD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAS,UAAA,IAAAW,kDAAA,mBAA4E;IAC5EpB,EAAA,CAAAS,UAAA,IAAAY,kDAAA,mBAAmG;IACnGrB,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAY,SAAA,YAOS;IACTZ,EAAA,CAAAE,MAAA,GAKF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IApByCH,EAAA,CAAAa,UAAA,UAAAM,YAAA,CAAkB;IAEzCnB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAc,YAAA,CAAAL,IAAA,CAAmB;IAErCd,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAa,UAAA,SAAAM,YAAA,CAAAZ,IAAA,CAAmB;IACnBP,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAa,UAAA,SAAAM,YAAA,CAAAJ,IAAA,UAAAI,YAAA,CAAAX,IAAA,CAA0C;IAG7CR,EAAA,CAAAI,SAAA,GAME;IANFJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAE,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAME;IACJf,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAkB,kBAAA,MAAAC,YAAA,CAAAJ,IAAA,qBAAAI,YAAA,CAAAJ,IAAA,kBAAAI,YAAA,CAAAJ,IAAA,kBAAAI,YAAA,CAAAJ,IAAA,qBAAAI,YAAA,CAAAJ,IAAA,6BAKF;;;;;IAKRf,EAAA,CAAAC,cAAA,cAA4H;IAC1HD,EAAA,CAAAY,SAAA,YAAyC;IAACZ,EAAA,CAAAE,MAAA,0CAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAqBNH,EAAA,CAAAC,cAAA,cAAwH;IACtHD,EAAA,CAAAY,SAAA,YAAyC;IAACZ,EAAA,CAAAE,MAAA,6BAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA6BFH,EAAA,CAAAC,cAAA,iBAAiE;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA9CH,EAAA,CAAAa,UAAA,UAAAS,QAAA,CAAAC,KAAA,CAAoB;IAACvB,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAiB,QAAA,CAAAE,KAAA,CAAgB;;;;;IAejFxB,EAAA,CAAAC,cAAA,iBAA8E;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA5DH,EAAA,CAAAa,UAAA,UAAAY,eAAA,CAAAF,KAAA,CAA2B;IAACvB,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAoB,eAAA,CAAAD,KAAA,CAAuB;;;;;IA4BvGxB,EAAA,CAAAY,SAAA,YAAgD;;;;;IAChDZ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtCH,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAY,SAAA,cAA2B;IAC7BZ,EAAA,CAAAG,YAAA,EAAM;;;;;IAsIhBH,EAAA,CAAAC,cAAA,eAAiD;IAG3CD,EAAA,CAAAY,SAAA,aAA4B;IAI9BZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAI5CH,EAAA,CAAAC,cAAA,eAAgE;IAE5DD,EAAA,CAAAY,SAAA,YAAyC;IAC3CZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/CH,EAAA,CAAAC,cAAA,kBAAkD;IAArBD,EAAA,CAAA0B,UAAA,mBAAAC,oEAAA;MAAA3B,EAAA,CAAA4B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAC/CjC,EAAA,CAAAY,SAAA,aAA2B;IAACZ,EAAA,CAAAE,MAAA,kBAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHgBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAA6B,OAAA,CAAAC,YAAA,CAAkB;;;;;IAUvCnC,EAAA,CAAAC,cAAA,QAAsC;IAAAD,EAAA,CAAAE,MAAA,qEAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAGtGH,EAAA,CAAAC,cAAA,gBAAmE;IAACD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5BH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAkB,kBAAA,OAAAkB,OAAA,CAAAC,aAAA,MAAqB;;;;;IAF3FrC,EAAA,CAAAC,cAAA,QAAoC;IACND,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,sBAChE;IAAAF,EAAA,CAAAS,UAAA,IAAA6B,sDAAA,oBAAgG;IAClGtC,EAAA,CAAAG,YAAA,EAAI;;;;IAF0BH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,iBAAA,CAAAkC,OAAA,CAAAC,eAAA,CAAAC,MAAA,CAA4B;IAC5BzC,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAa,UAAA,SAAA0B,OAAA,CAAAF,aAAA,mBAAqC;;;;;;IAMjErC,EAAA,CAAAC,cAAA,kBAIsC;IAApCD,EAAA,CAAA0B,UAAA,mBAAAgB,mFAAA;MAAA,MAAAC,WAAA,GAAA3C,EAAA,CAAA4B,aAAA,CAAAgB,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA/C,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAe,OAAA,CAAAC,WAAA,CAAAH,UAAA,CAAAtB,KAAA,CAAyB;IAAA,EAAC;IACnCvB,EAAA,CAAAY,SAAA,YAA2C;IAC3CZ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAJPH,EAAA,CAAAiD,WAAA,WAAAC,OAAA,CAAAb,aAAA,KAAAQ,UAAA,CAAAtB,KAAA,CAA+C;IAEhCvB,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAa,UAAA,YAAAgC,UAAA,CAAAM,IAAA,CAAuB;IACtCnD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAA2B,UAAA,CAAArB,KAAA,MACF;;;;;IATJxB,EAAA,CAAAC,cAAA,eAA8D;IAE1DD,EAAA,CAAAS,UAAA,IAAA2C,0DAAA,sBAOS;IACXpD,EAAA,CAAAG,YAAA,EAAM;;;;IAPiBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAa,UAAA,YAAAwC,OAAA,CAAAC,aAAA,CAAgB;;;;;IAgBjCtD,EAAA,CAAAY,SAAA,eAG0B;;;;IAFrBZ,EAAA,CAAAa,UAAA,QAAA0C,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,EAAA1D,EAAA,CAAA2D,aAAA,CAA6C;;;;;IAGlD3D,EAAA,CAAAY,SAAA,aACyC;;;;;IAMzCZ,EAAA,CAAAC,cAAA,gBAA2H;IACzHD,EAAA,CAAAY,SAAA,aAA0B;IAC1BZ,EAAA,CAAAE,MAAA,GAGF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHLH,EAAA,CAAAI,SAAA,GAGF;IAHEJ,EAAA,CAAAkB,kBAAA,MAAAqC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAI,cAAA,IAAAL,UAAA,CAAAC,KAAA,IAAAI,cAAA,CAAAC,WAAA,IAAAN,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAI,cAAA,IAAAL,UAAA,CAAAC,KAAA,IAAAI,cAAA,CAAA9C,IAAA,kBAAAyC,UAAA,CAAAO,QAAA,MAGF;;;;;IAKF9D,EAAA,CAAAC,cAAA,gBAAsG;IACpGD,EAAA,CAAAY,SAAA,aAA2B;IAACZ,EAAA,CAAAE,MAAA,eAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAAkG;IAChGD,EAAA,CAAAY,SAAA,YAA4B;IAACZ,EAAA,CAAAE,MAAA,GAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADwBH,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAAkB,kBAAA,MAAAqC,UAAA,CAAAC,KAAA,IAAAO,WAAA,CAAAjD,IAAA,MAC/B;;;;;IAEAd,EAAA,CAAAC,cAAA,gBAA0G;IACxGD,EAAA,CAAAY,SAAA,aAAkC;IAACZ,EAAA,CAAAE,MAAA,qBACrC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;;;;IAOPH,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAY,SAAA,YAAqI;IACrIZ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFUH,EAAA,CAAAI,SAAA,GAAiH;IAAjHJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAgE,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,iBAAA,CAAAZ,UAAA,IAAAW,OAAA,CAAAC,iBAAA,CAAAZ,UAAA,GAAiH;IAChIvD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAgD,OAAA,CAAAC,iBAAA,CAAAZ,UAAA,uCACF;;;;;IAGAvD,EAAA,CAAAC,cAAA,gBAAyG;IACvGD,EAAA,CAAAY,SAAA,aAA4B;IAACZ,EAAA,CAAAE,MAAA,GAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADwBH,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAAkB,kBAAA,eAAAkD,OAAA,CAAAC,oBAAA,CAAAd,UAAA,CAAAe,MAAA,IAAAC,SAAA,OAC/B;;;;;IA6BEvE,EAAA,CAAAC,cAAA,eAA4F;IACjED,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,GAClE;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADqBH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAAkD,UAAA,CAAAC,KAAA,IAAAgB,SAAA,CAA+B;IAAQxE,EAAA,CAAAI,SAAA,GAClE;IADkEJ,EAAA,CAAAkB,kBAAA,UAAAqC,UAAA,CAAAC,KAAA,IAAAgB,SAAA,qBAClE;;;;;IACAxE,EAAA,CAAAC,cAAA,eAAqG;IACnGD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAwBEH,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9BH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAkB,kBAAA,KAAAuD,WAAA,CAAAC,MAAA,QAAuB;;;;;IAC9E1E,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAnCH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAkB,kBAAA,KAAAuD,WAAA,CAAAE,KAAA,cAA4B;;;;;IANtF3E,EAAA,CAAAC,cAAA,eACqG;IACnGD,EAAA,CAAAY,SAAA,YAA+B;IAC/BZ,EAAA,CAAAC,cAAA,eAA0B;IACGD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAS,UAAA,IAAAmE,oFAAA,oBAAqF;IACrF5E,EAAA,CAAAS,UAAA,IAAAoE,oFAAA,oBAAyF;IAC3F7E,EAAA,CAAAG,YAAA,EAAM;;;;IAFyBH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAa,UAAA,SAAA4D,WAAA,CAAAC,MAAA,KAAwB;IACxB1E,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAa,UAAA,SAAA4D,WAAA,CAAAE,KAAA,KAAuB;;;;;IAP1D3E,EAAA,CAAA8E,uBAAA,GAA4G;IAC1G9E,EAAA,CAAAS,UAAA,IAAAsE,6EAAA,mBAQM;;IACR/E,EAAA,CAAAgF,qBAAA,EAAe;;;;;IARYhF,EAAA,CAAAI,SAAA,GAA0E;IAA1EJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAiF,WAAA,OAAAC,OAAA,CAAAC,mBAAA,CAAA5B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,YAA0E;;;;;IAiB/FpF,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAnCH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAkB,kBAAA,KAAAmE,WAAA,CAAAV,KAAA,cAA4B;;;;;IAClF3E,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9BH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAkB,kBAAA,KAAAmE,WAAA,CAAAX,MAAA,QAAuB;;;;;IANlF1E,EAAA,CAAAC,cAAA,eACqG;IACnGD,EAAA,CAAAY,SAAA,aAAgC;IAChCZ,EAAA,CAAAC,cAAA,eAA0B;IACGD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAS,UAAA,IAAA6E,oFAAA,oBAAyF;IACzFtF,EAAA,CAAAS,UAAA,IAAA8E,oFAAA,oBAAqF;IACvFvF,EAAA,CAAAG,YAAA,EAAM;;;;IAFyBH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAa,UAAA,SAAAwE,WAAA,CAAAV,KAAA,KAAuB;IACvB3E,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAa,UAAA,SAAAwE,WAAA,CAAAX,MAAA,KAAwB;;;;;IAP3D1E,EAAA,CAAA8E,uBAAA,GAA4G;IAC1G9E,EAAA,CAAAS,UAAA,IAAA+E,6EAAA,mBAQM;;IACRxF,EAAA,CAAAgF,qBAAA,EAAe;;;;;IARYhF,EAAA,CAAAI,SAAA,GAA0E;IAA1EJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAiF,WAAA,OAAAQ,OAAA,CAAAN,mBAAA,CAAA5B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,YAA0E;;;;;IAWrGpF,EAAA,CAAAC,cAAA,eAEiH;IAC/GD,EAAA,CAAAY,SAAA,aAAgC;IAChCZ,EAAA,CAAAC,cAAA,eAA0B;IACGD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAMjDH,EAAA,CAAAC,cAAA,eAAsH;IAC9GD,EAAA,CAAAE,MAAA,gDAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA3C1DH,EAAA,CAAAC,cAAA,eAAmE;IAC7DD,EAAA,CAAAY,SAAA,YAA+B;IAACZ,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,eAA6B;IAE3BD,EAAA,CAAAS,UAAA,IAAAiF,uEAAA,2BAUe;IAGf1F,EAAA,CAAAS,UAAA,IAAAkF,uEAAA,2BAUe;IAGf3F,EAAA,CAAAS,UAAA,IAAAmF,8DAAA,mBAQM;IACR5F,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAS,UAAA,IAAAoF,8DAAA,mBAEM;IACR7F,EAAA,CAAAG,YAAA,EAAM;;;;;IAzCaH,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,IAAA7B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,CAAA3C,MAAA,KAA2F;IAa3FzC,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,IAAA7B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,CAAA3C,MAAA,KAA2F;IAazEzC,EAAA,CAAAI,SAAA,GAE8E;IAF9EJ,EAAA,CAAAa,UAAA,UAAA0C,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,IAAA7B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,CAAA3C,MAAA,UAAAqD,OAAA,CAAAX,mBAAA,CAAA5B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,KAAA3C,MAAA,OAE8E;IAU3GzC,EAAA,CAAAI,SAAA,GAA8F;IAA9FJ,EAAA,CAAAa,UAAA,UAAA0C,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,IAAA7B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,CAAA3C,MAAA,OAA8F;;;;;IAQpGzC,EAAA,CAAAC,cAAA,eAAkF;IAC1ED,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtCH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAA0F,WAAA,CAAAjF,IAAA,cAA+B;;;;;IAHzCd,EAAA,CAAAC,cAAA,eAAsI;IAChID,EAAA,CAAAY,SAAA,aAAqC;IAACZ,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAA,CAAAS,UAAA,IAAAuF,8DAAA,mBAEM;;IACRhG,EAAA,CAAAG,YAAA,EAAM;;;;IAHqBH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAiF,WAAA,OAAA1B,UAAA,CAAAC,KAAA,IAAAyC,QAAA,QAAuC;;;;;IAQhEjG,EAAA,CAAAC,cAAA,eAA6D;IAEzDD,EAAA,CAAAY,SAAA,QAAqI;IACrIZ,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAFnDH,EAAA,CAAAI,SAAA,GAA6H;IAA7HJ,EAAA,CAAAkG,UAAA,CAAA3C,UAAA,CAAAe,MAAA,IAAA6B,cAAA,CAAAC,UAAA,0EAA6H;IAChIpG,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAkB,kBAAA,MAAAqC,UAAA,CAAAe,MAAA,IAAA6B,cAAA,CAAAC,UAAA,wCACA;;;;;IANNpG,EAAA,CAAAC,cAAA,eAA6E;IACvED,EAAA,CAAAY,SAAA,aAA0B;IAACZ,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAS,UAAA,IAAA4F,8DAAA,mBAMM;IACRrG,EAAA,CAAAG,YAAA,EAAM;;;;IAPkBH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAe,MAAA,IAAA6B,cAAA,CAAqC;;;;;IAqBzDnG,EAAA,CAAAC,cAAA,eAAuF;IAEnFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAsD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFpFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAsG,kBAAA,MAAAC,OAAA,CAAAC,oBAAA,CAAAC,QAAA,CAAAC,aAAA,UAAAD,QAAA,CAAAE,cAAA,OACF;IACyB3G,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA4G,WAAA,OAAAH,QAAA,CAAAI,KAAA,CAAAC,MAAA,EAAAL,QAAA,CAAAI,KAAA,CAAAE,QAAA,EAAsD;;;;;IAEjF/G,EAAA,CAAAC,cAAA,eAA4E;IAClDD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAwF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA/FH,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA4G,WAAA,OAAArD,UAAA,CAAAe,MAAA,IAAA0C,UAAA,CAAAF,MAAA,EAAAvD,UAAA,CAAAe,MAAA,IAAA0C,UAAA,CAAAD,QAAA,EAAwF;;;;;IAVrH/G,EAAA,CAAAC,cAAA,eAA8G;IACxGD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAS,UAAA,IAAAwG,oEAAA,mBAKM;IACNjH,EAAA,CAAAS,UAAA,IAAAyG,oEAAA,mBAGM;IACNlH,EAAA,CAAAC,cAAA,eAA6B;IACDD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA8E;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAZrEH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAa,UAAA,YAAA0C,UAAA,CAAAe,MAAA,IAAA6C,cAAA,CAAA3D,KAAA,CAAwC;IAM5CxD,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAe,MAAA,IAAA0C,UAAA,CAAiC;IAM7ChH,EAAA,CAAAI,SAAA,GAA8E;IAA9EJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA4G,WAAA,QAAArD,UAAA,CAAAe,MAAA,IAAAuC,KAAA,CAAAC,MAAA,EAAAvD,UAAA,CAAAe,MAAA,IAAAuC,KAAA,CAAAE,QAAA,EAA8E;;;;;IAhB/G/G,EAAA,CAAAC,cAAA,eAA+E;IAE7ED,EAAA,CAAAS,UAAA,IAAA2G,8DAAA,oBAgBM;IACRpH,EAAA,CAAAG,YAAA,EAAM;;;;IAjB0BH,EAAA,CAAAI,SAAA,GAA8E;IAA9EJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAe,MAAA,IAAA6C,cAAA,IAAA5D,UAAA,CAAAe,MAAA,IAAA6C,cAAA,CAAA3D,KAAA,CAA8E;;;;;IA+D1GxD,EAAA,CAAAC,cAAA,eAA0E;IACxED,EAAA,CAAAY,SAAA,aAAqC;IACrCZ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA6K;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;IAApLH,EAAA,CAAAI,SAAA,GAA6K;IAA7KJ,EAAA,CAAAsG,kBAAA,KAAAe,OAAA,CAAAC,oBAAA,CAAAC,WAAA,EAAAhE,UAAA,CAAAC,KAAA,IAAAgE,QAAA,CAAAC,KAAA,wBAAAF,WAAA,CAAAG,OAAA,IAAAH,WAAA,CAAAG,OAAA,CAAAlH,IAAA,IAAA+G,WAAA,CAAAG,OAAA,CAAAlH,IAAA,CAAAM,IAAA,0BAA6K;;;;;IArCvLd,EAAA,CAAAC,cAAA,eAA0F;IAEzDD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/FH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5DH,EAAA,CAAAC,cAAA,eAA2B;IAELD,EAAA,CAAAE,MAAA,IAAoE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5FH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAA+F;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjIH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAyF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG5HH,EAAA,CAAAC,cAAA,gBAA8B;IAE1BD,EAAA,CAAAY,SAAA,iBAAuC;IACvCZ,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAY,SAAA,iBAA0B;IAC5BZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAY,SAAA,iBAAqC;IACvCZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAY,SAAA,cAA4B;IAACZ,EAAA,CAAAE,MAAA,IAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,gBAA6B;IACTD,EAAA,CAAAE,MAAA,IAAgE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxFH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAAyF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3HH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAmF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKxHH,EAAA,CAAAS,UAAA,KAAAkH,qEAAA,mBAGM;IACR3H,EAAA,CAAAG,YAAA,EAAM;;;;;;;IArC2BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAkB,kBAAA,aAAAuG,KAAA,SAAmB;IAClBzH,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,iBAAA,CAAAkH,WAAA,CAAA9D,OAAA,IAAA8D,WAAA,CAAA9D,OAAA,CAAA3C,IAAA,cAA0D;IAC3Dd,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAkH,WAAA,CAAAK,QAAA,CAAsB;IAI/B5H,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAK,iBAAA,CAAAkH,WAAA,CAAAM,SAAA,GAAAC,OAAA,CAAAC,UAAA,CAAAR,WAAA,CAAAM,SAAA,CAAAG,IAAA,UAAoE;IAEzDhI,EAAA,CAAAI,SAAA,GAA+F;IAA/FJ,EAAA,CAAAK,iBAAA,CAAAkH,WAAA,CAAAM,SAAA,IAAAN,WAAA,CAAAM,SAAA,CAAAI,OAAA,IAAAV,WAAA,CAAAM,SAAA,CAAAI,OAAA,CAAA1H,IAAA,UAA+F;IAClGP,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAK,iBAAA,CAAAkH,WAAA,CAAAM,SAAA,IAAAN,WAAA,CAAAM,SAAA,CAAArH,IAAA,IAAA+G,WAAA,CAAAM,SAAA,CAAArH,IAAA,CAAAM,IAAA,UAAyF;IAYpFd,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAAkB,kBAAA,MAAA4G,OAAA,CAAAI,cAAA,CAAAX,WAAA,CAAAY,QAAA,OAC/B;IAGkBnI,EAAA,CAAAI,SAAA,GAAgE;IAAhEJ,EAAA,CAAAK,iBAAA,CAAAkH,WAAA,CAAAG,OAAA,GAAAI,OAAA,CAAAC,UAAA,CAAAR,WAAA,CAAAG,OAAA,CAAAM,IAAA,UAAgE;IAErDhI,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAK,iBAAA,CAAAkH,WAAA,CAAAG,OAAA,IAAAH,WAAA,CAAAG,OAAA,CAAAO,OAAA,IAAAV,WAAA,CAAAG,OAAA,CAAAO,OAAA,CAAA1H,IAAA,UAAyF;IAC5FP,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAK,iBAAA,CAAAkH,WAAA,CAAAG,OAAA,IAAAH,WAAA,CAAAG,OAAA,CAAAlH,IAAA,IAAA+G,WAAA,CAAAG,OAAA,CAAAlH,IAAA,CAAAM,IAAA,UAAmF;IAKtFd,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAa,UAAA,SAAA4G,KAAA,GAAAlE,UAAA,CAAAC,KAAA,IAAAgE,QAAA,CAAA/E,MAAA,KAA6C;;;;;IAzChFzC,EAAA,CAAAC,cAAA,eAAyI;IAGnID,EAAA,CAAAY,SAAA,YAAsC;IAACZ,EAAA,CAAAE,MAAA,GACzC;IAAAF,EAAA,CAAAG,YAAA,EAAU;IACVH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAS,UAAA,IAAA2H,8DAAA,qBAuCM;IACRpI,EAAA,CAAAG,YAAA,EAAM;;;;IA3CmCH,EAAA,CAAAI,SAAA,GACzC;IADyCJ,EAAA,CAAAkB,kBAAA,uBAAAqC,UAAA,CAAAC,KAAA,IAAAgE,QAAA,CAAA/E,MAAA,OACzC;IAEgDzC,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAa,UAAA,YAAA0C,UAAA,CAAAC,KAAA,IAAAgE,QAAA,CAA6B;;;;;IAmD3ExH,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAqC,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAC,WAAA,MACF;;;;;IAKItI,EAAA,CAAAC,cAAA,eAAiG;IAC/FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAqH,YAAA,CAAAC,YAAA,IAAAC,IAAA,MACF;;;;;IAJFzI,EAAA,CAAAC,cAAA,eAAwF;IAC5DD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzEH,EAAA,CAAAS,UAAA,IAAAiI,0EAAA,mBAEM;IACR1I,EAAA,CAAAG,YAAA,EAAM;;;;IAJsBH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,iBAAA,CAAAkI,YAAA,CAAAI,cAAA,cAAyC;IACjC3I,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAa,UAAA,SAAA0H,YAAA,CAAAC,YAAA,IAAAD,YAAA,CAAAC,YAAA,CAAA/F,MAAA,KAA6D;;;;;IAJnGzC,EAAA,CAAAC,cAAA,eAAqI;IAC/HD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAS,UAAA,IAAAmI,oEAAA,mBAKM;IACR5I,EAAA,CAAAG,YAAA,EAAM;;;;IAN0CH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAa,UAAA,YAAA0C,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAQ,QAAA,CAAwC;;;;;IAX9F7I,EAAA,CAAAC,cAAA,eAA4G;IAGtGD,EAAA,CAAAY,SAAA,aAAkC;IAACZ,EAAA,CAAAE,MAAA,GACrC;IAAAF,EAAA,CAAAG,YAAA,EAAU;IACVH,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAS,UAAA,IAAAqI,8DAAA,mBAEM;IACN9I,EAAA,CAAAS,UAAA,IAAAsI,8DAAA,mBAQM;IACR/I,EAAA,CAAAG,YAAA,EAAM;;;;IAf+BH,EAAA,CAAAI,SAAA,GACrC;IADqCJ,EAAA,CAAAkB,kBAAA,MAAAqC,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAvH,IAAA,gCACrC;IAEyCd,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAC,WAAA,CAA8C;IAGjDtI,EAAA,CAAAI,SAAA,GAA+F;IAA/FJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAQ,QAAA,IAAAtF,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAQ,QAAA,CAAApG,MAAA,KAA+F;;;;;;IAhQ3IzC,EAAA,CAAAC,cAAA,eAAiH;IAIzGD,EAAA,CAAAS,UAAA,IAAAuI,uDAAA,mBAG0B;IAC1BhJ,EAAA,CAAAS,UAAA,IAAAwI,qDAAA,iBACyC;IAC3CjJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACAD,EAAA,CAAAE,MAAA,GAA2G;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7IH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAwE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE3GH,EAAA,CAAAS,UAAA,KAAAyI,yDAAA,oBAKO;IACTlJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAS,UAAA,KAAA0I,yDAAA,oBAEO;IACPnJ,EAAA,CAAAS,UAAA,KAAA2I,yDAAA,oBAEO;IAEPpJ,EAAA,CAAAS,UAAA,KAAA4I,yDAAA,oBAEO;IACTrJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAA0B;IACED,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAS,UAAA,KAAA6I,yDAAA,oBAGO;IAGPtJ,EAAA,CAAAS,UAAA,KAAA8I,yDAAA,oBAEO;IACTvJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAA4B;IAGJD,EAAA,CAAAE,MAAA,IAAuH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/IH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAAwJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1LH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAkJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIrLH,EAAA,CAAAC,cAAA,gBAA6B;IAEzBD,EAAA,CAAAY,SAAA,iBAAuC;IACvCZ,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAY,SAAA,iBAA0B;IAC1BZ,EAAA,CAAAC,cAAA,iBAAyB;IACvBD,EAAA,CAAAY,SAAA,cAA4B;IAC9BZ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAY,SAAA,iBAAqC;IACvCZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAY,SAAA,cAA4B;IAC5BZ,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAS,UAAA,KAAA+I,wDAAA,mBAEM;IACNxJ,EAAA,CAAAS,UAAA,KAAAgJ,wDAAA,mBAEM;IACRzJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAAqB;IACDD,EAAA,CAAAE,MAAA,IAAmH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3IH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAAkJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpLH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA4I;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKjLH,EAAA,CAAAC,cAAA,gBAA6B;IAE3BD,EAAA,CAAAS,UAAA,KAAAiJ,wDAAA,mBA6CM;IAGN1J,EAAA,CAAAS,UAAA,KAAAkJ,wDAAA,mBAKM;IAGN3J,EAAA,CAAAS,UAAA,KAAAmJ,wDAAA,mBASM;IACR5J,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,gBAAqC;IAG/BD,EAAA,CAAAY,SAAA,aAAsC;IAACZ,EAAA,CAAAE,MAAA,yBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAU;IACVH,EAAA,CAAAS,UAAA,KAAAoJ,wDAAA,mBAmBM;IACR7J,EAAA,CAAAG,YAAA,EAAU;IAIZH,EAAA,CAAAS,UAAA,KAAAqJ,wDAAA,mBAgDM;IAGN9J,EAAA,CAAAS,UAAA,KAAAsJ,wDAAA,mBAoBM;IAEN/J,EAAA,CAAAC,cAAA,gBAA4B;IACUD,EAAA,CAAA0B,UAAA,mBAAAsI,2EAAA;MAAA,MAAArH,WAAA,GAAA3C,EAAA,CAAA4B,aAAA,CAAAqI,KAAA;MAAA,MAAA1G,UAAA,GAAAZ,WAAA,CAAAG,SAAA;MAAA,MAAAoH,QAAA,GAAAlK,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAkI,QAAA,CAAAC,cAAA,CAAA5G,UAAA,CAAsB;IAAA,EAAC;IAClEvD,EAAA,CAAAY,SAAA,cAAkC;IAClCZ,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAyJ;IAAnCD,EAAA,CAAA0B,UAAA,mBAAA0I,2EAAA;MAAA,MAAAzH,WAAA,GAAA3C,EAAA,CAAA4B,aAAA,CAAAqI,KAAA;MAAA,MAAA1G,UAAA,GAAAZ,WAAA,CAAAG,SAAA;MAAA,MAAAuH,QAAA,GAAArK,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAqI,QAAA,CAAAC,gBAAA,CAAA/G,UAAA,CAAwB;IAAA,EAAC;IACtJvD,EAAA,CAAAY,SAAA,cAAmC;IACnCZ,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IArRmDH,EAAA,CAAAiD,WAAA,iBAAAsH,OAAA,CAAApG,iBAAA,CAAAZ,UAAA,EAAgD;IAIlGvD,EAAA,CAAAI,SAAA,GAAyG;IAAzGJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,IAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,CAAyG;IAI3G1D,EAAA,CAAAI,SAAA,GAA4G;IAA5GJ,EAAA,CAAAa,UAAA,WAAA0C,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,IAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,EAA4G;IAIrF1D,EAAA,CAAAI,SAAA,GAA2G;IAA3GJ,EAAA,CAAAK,iBAAA,CAAAkD,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,GAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAA3C,IAAA,aAA2G;IAC1Gd,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAK,iBAAA,CAAAkD,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,MAAAD,UAAA,CAAAC,KAAA,IAAAoE,QAAA,SAAwE;IAEvE5H,EAAA,CAAAI,SAAA,GAA4F;IAA5FJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAO,QAAA,IAAAP,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAI,cAAA,CAA4F;IAU/F5D,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAgB,SAAA,OAAwE;IAGxExE,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAO,WAAA,CAAoE;IAI5D/D,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,OAAAf,UAAA,CAAAe,MAAA,IAAAkG,QAAA,CAAoE;IAOpFxK,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAkK,OAAA,CAAAE,WAAA,CAAAlH,UAAA,EAAyB;IAEjBvD,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,CAAA7B,MAAA,KAA+C;IAMjDzC,EAAA,CAAAI,SAAA,GAA6E;IAA7EJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,CAAA7B,MAAA,QAAAc,UAAA,CAAAe,MAAA,IAAAC,SAAA,CAA6E;IASnFvE,EAAA,CAAAI,SAAA,GAAuH;IAAvHJ,EAAA,CAAAK,iBAAA,CAAAkD,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAqE,SAAA,GAAA0C,OAAA,CAAAxC,UAAA,CAAAxE,UAAA,CAAAC,KAAA,IAAAqE,SAAA,CAAAG,IAAA,UAAuH;IAE5GhI,EAAA,CAAAI,SAAA,GAAwJ;IAAxJJ,EAAA,CAAAK,iBAAA,CAAAkD,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAqE,SAAA,IAAAtE,UAAA,CAAAC,KAAA,IAAAqE,SAAA,CAAAI,OAAA,GAAA1E,UAAA,CAAAC,KAAA,IAAAqE,SAAA,CAAAI,OAAA,CAAA1H,IAAA,SAAwJ;IAC3JP,EAAA,CAAAI,SAAA,GAAkJ;IAAlJJ,EAAA,CAAAK,iBAAA,CAAAkD,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAqE,SAAA,IAAAtE,UAAA,CAAAC,KAAA,IAAAqE,SAAA,CAAArH,IAAA,GAAA+C,UAAA,CAAAC,KAAA,IAAAqE,SAAA,CAAArH,IAAA,CAAAM,IAAA,SAAkJ;IAiB1Kd,EAAA,CAAAI,SAAA,IACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAqC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,MAAA+G,OAAA,CAAArC,cAAA,CAAA3E,UAAA,CAAAC,KAAA,IAAA2E,QAAA,eACF;IACoBnI,EAAA,CAAAI,SAAA,GAAsE;IAAtEJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAgB,SAAA,KAAsE;IAG/DxE,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAgB,SAAA,OAAwE;IAMjFxE,EAAA,CAAAI,SAAA,GAAmH;IAAnHJ,EAAA,CAAAK,iBAAA,CAAAkD,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAkE,OAAA,GAAA6C,OAAA,CAAAxC,UAAA,CAAAxE,UAAA,CAAAC,KAAA,IAAAkE,OAAA,CAAAM,IAAA,UAAmH;IAExGhI,EAAA,CAAAI,SAAA,GAAkJ;IAAlJJ,EAAA,CAAAK,iBAAA,CAAAkD,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAkE,OAAA,IAAAnE,UAAA,CAAAC,KAAA,IAAAkE,OAAA,CAAAO,OAAA,GAAA1E,UAAA,CAAAC,KAAA,IAAAkE,OAAA,CAAAO,OAAA,CAAA1H,IAAA,SAAkJ;IACrJP,EAAA,CAAAI,SAAA,GAA4I;IAA5IJ,EAAA,CAAAK,iBAAA,CAAAkD,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAkE,OAAA,IAAAnE,UAAA,CAAAC,KAAA,IAAAkE,OAAA,CAAAlH,IAAA,GAAA+C,UAAA,CAAAC,KAAA,IAAAkE,OAAA,CAAAlH,IAAA,CAAAM,IAAA,SAA4I;IAOlKd,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,IAAqC;IAgDrCxD,EAAA,CAAAI,SAAA,GAAwG;IAAxGJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAyC,QAAA,IAAA1C,UAAA,CAAAC,KAAA,IAAAyC,QAAA,CAAAxD,MAAA,KAAwG;IAQxGzC,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,CAAA7B,MAAA,KAA+C;IAmBfzC,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,IAAuC;IAwBlDtE,EAAA,CAAAI,SAAA,GAAwG;IAAxGJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAgE,QAAA,IAAAjE,UAAA,CAAAC,KAAA,IAAAgE,QAAA,CAAA/E,MAAA,KAAwG;IAmDpGzC,EAAA,CAAAI,SAAA,GAAuE;IAAvEJ,EAAA,CAAAa,UAAA,SAAA0C,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,OAAAf,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAuE;IA2B1ErI,EAAA,CAAAI,SAAA,GAAuF;IAAvFJ,EAAA,CAAAa,UAAA,cAAA0C,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,CAAA7B,MAAA,WAAA8H,OAAA,CAAApG,iBAAA,CAAAZ,UAAA,EAAuF;;;;;IAQzHvD,EAAA,CAAAC,cAAA,eAA4F;IAExFD,EAAA,CAAAY,SAAA,YAA6B;IAC/BZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wGAAiG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxGH,EAAA,CAAAC,cAAA,eAAoC;IAEhCD,EAAA,CAAAY,SAAA,YAAmC;IACnCZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElCH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAY,SAAA,cAAqC;IACrCZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElCH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAY,SAAA,cAA4B;IAC5BZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IArUjDH,EAAA,CAAAC,cAAA,eAAwE;IAG9DD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAS,UAAA,IAAAiK,+CAAA,gBAAwG;IACxG1K,EAAA,CAAAS,UAAA,IAAAkK,+CAAA,gBAGI;IACN3K,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAS,UAAA,IAAAmK,iDAAA,mBAWM;IACR5K,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAS,UAAA,IAAAoK,iDAAA,qBAuRM;IAGN7K,EAAA,CAAAS,UAAA,KAAAqK,kDAAA,oBAoBM;IACR9K,EAAA,CAAAG,YAAA,EAAM;;;;IArUEH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAa,UAAA,SAAAkK,OAAA,CAAAC,aAAA,CAAAvI,MAAA,OAAgC;IAChCzC,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAa,UAAA,SAAAkK,OAAA,CAAAC,aAAA,CAAAvI,MAAA,KAA8B;IAMNzC,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAa,UAAA,SAAAkK,OAAA,CAAAC,aAAA,CAAAvI,MAAA,KAA8B;IAepCzC,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAa,UAAA,YAAAkK,OAAA,CAAAvI,eAAA,CAAkB;IA0RjBxC,EAAA,CAAAI,SAAA,GAAiE;IAAjEJ,EAAA,CAAAa,UAAA,UAAAkK,OAAA,CAAAE,SAAA,KAAAF,OAAA,CAAA5I,YAAA,IAAA4I,OAAA,CAAAvI,eAAA,CAAAC,MAAA,OAAiE;;;;;IA5UhGzC,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAAS,UAAA,IAAAyK,2CAAA,mBAUM;IAENlL,EAAA,CAAAS,UAAA,IAAA0K,2CAAA,oBASM;IAENnL,EAAA,CAAAS,UAAA,IAAA2K,2CAAA,oBA0UM;IACRpL,EAAA,CAAAG,YAAA,EAAM;;;;IAlWEH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAa,UAAA,SAAAwK,OAAA,CAAAJ,SAAA,CAAe;IAYfjL,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAa,UAAA,UAAAwK,OAAA,CAAAJ,SAAA,IAAAI,OAAA,CAAAlJ,YAAA,CAAgC;IAWhCnC,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAa,UAAA,UAAAwK,OAAA,CAAAJ,SAAA,KAAAI,OAAA,CAAAlJ,YAAA,CAAiC;;;ADpX7C,OAAM,MAAOmJ,oBAAoB;EAoC/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IArChB,KAAAC,kBAAkB,GAAqB,EAAE;IACzC,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAAX,SAAS,GAAG,KAAK;IACjB,KAAAD,aAAa,GAAa,EAAE;IAC5B,KAAAxI,eAAe,GAAa,EAAE;IAC9B,KAAAqJ,WAAW,GAAG,KAAK;IACnB,KAAA1J,YAAY,GAAG,EAAE;IACjB,KAAA2J,YAAY,GAAG,EAAE;IAEjB;IACA,KAAAzJ,aAAa,GAAW,aAAa;IACrC,KAAAiB,aAAa,GAAG,CACd;MAAE/B,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE,aAAa;MAAE2B,IAAI,EAAE;IAAS,CAAE,EAC/D;MAAE5B,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,UAAU;MAAE2B,IAAI,EAAE;IAAgB,CAAE,EAChE;MAAE5B,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,UAAU;MAAE2B,IAAI,EAAE;IAAU,CAAE,CAC3D;IAED;IACA,KAAA4I,cAAc,GAAG,CACf;MAAExK,KAAK,EAAExB,aAAa,CAACiM,KAAK;MAAExK,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAExB,aAAa,CAACkM,KAAK;MAAEzK,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAExB,aAAa,CAACmM,MAAM;MAAE1K,KAAK,EAAE;IAAQ,CAAE,CACjD;IAED;IACA,KAAA2K,aAAa,GAAG,CACd;MAAE5K,KAAK,EAAE1B,eAAe,CAACuM,KAAK;MAAE5K,KAAK,EAAE;IAAO,CAAE,EAChD;MAAED,KAAK,EAAE1B,eAAe,CAACwM,OAAO;MAAE7K,KAAK,EAAE;IAAS,CAAE,EACpD;MAAED,KAAK,EAAE1B,eAAe,CAACyM,QAAQ;MAAE9K,KAAK,EAAE;IAAU,CAAE,CACvD;IAUC;IACA,IAAI,CAAC+K,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAErD;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACnB,EAAE,CAACoB,KAAK,CAAC;MAC9B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAEtN,UAAU,CAACuN,QAAQ,CAAC;MACrCC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,EAAExN,UAAU,CAACuN,QAAQ,CAAC;MAC1CE,iBAAiB,EAAE,CAAC,EAAE,EAAEzN,UAAU,CAACuN,QAAQ,CAAC;MAC5CG,eAAe,EAAE,CAAC,EAAE,EAAE1N,UAAU,CAACuN,QAAQ,CAAC;MAC1CI,aAAa,EAAE,CAAC,IAAI,CAACX,OAAO,EAAEhN,UAAU,CAACuN,QAAQ,CAAC;MAClDnG,cAAc,EAAE,CAAC,CAAC,EAAE,CAACpH,UAAU,CAACuN,QAAQ,EAAEvN,UAAU,CAAC4N,GAAG,CAAC,CAAC,CAAC,EAAE5N,UAAU,CAAC6N,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChF1G,aAAa,EAAE,CAAC,CAAC,EAAEnH,UAAU,CAACuN,QAAQ,CAAC;MAEvC;MACA/I,WAAW,EAAE,CAAC,CAAC,EAAExE,UAAU,CAACuN,QAAQ,CAAC;MACrCO,OAAO,EAAE,CAAC,KAAK,CAAC;MAEhB;MACAC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClBvG,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBwG,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,wBAAwB,EAAE,CAAC,KAAK,CAAC;MACjCC,6BAA6B,EAAE,CAAC,IAAI,CAAC;MACrCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAE5B;MACAC,sBAAsB,EAAE,CAAC,CAAC;KAC3B,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACAC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EACjF;EAEA;EACAhL,WAAWA,CAACiL,WAAmB;IAC7B,IAAI,CAAC5L,aAAa,GAAG4L,WAAW;IAEhC,IAAI,CAAC,IAAI,CAACjD,aAAa,IAAI,IAAI,CAACA,aAAa,CAACvI,MAAM,KAAK,CAAC,EAAE;MAC1D,IAAI,CAACD,eAAe,GAAG,EAAE;MACzB;;IAGF;IACA,MAAM0L,OAAO,GAAG,CAAC,GAAG,IAAI,CAAClD,aAAa,CAAC;IAEvC,QAAQiD,WAAW;MACjB,KAAK,UAAU;QACb,IAAI,CAACzL,eAAe,GAAG,IAAI,CAAC2L,WAAW,CAACD,OAAO,CAAC;QAChD;MACF,KAAK,UAAU;QACb,IAAI,CAAC1L,eAAe,GAAG,IAAI,CAAC4L,cAAc,CAACF,OAAO,CAAC;QACnD;MACF,KAAK,aAAa;MAClB;QACE,IAAI,CAAC1L,eAAe,GAAG,IAAI,CAAC6L,oBAAoB,CAACH,OAAO,CAAC;QACzD;;EAEN;EAEA;EACQC,WAAWA,CAACG,OAAiB;IACnC,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,MAAMC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACH,CAAC,CAAC;MACxC,MAAMI,MAAM,GAAG,IAAI,CAACD,iBAAiB,CAACF,CAAC,CAAC;MACxC,OAAOC,MAAM,GAAGE,MAAM;IACxB,CAAC,CAAC;EACJ;EAEA;EACQR,cAAcA,CAACE,OAAiB;IACtC,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,MAAMI,SAAS,GAAGL,CAAC,CAAChL,KAAK,IAAIgL,CAAC,CAAChL,KAAK,CAAC,CAAC,CAAC,GAAGgL,CAAC,CAAChL,KAAK,CAAC,CAAC,CAAC,CAAC2E,QAAQ,GAAG2G,MAAM,CAACC,SAAS;MAChF,MAAMC,SAAS,GAAGP,CAAC,CAACjL,KAAK,IAAIiL,CAAC,CAACjL,KAAK,CAAC,CAAC,CAAC,GAAGiL,CAAC,CAACjL,KAAK,CAAC,CAAC,CAAC,CAAC2E,QAAQ,GAAG2G,MAAM,CAACC,SAAS;MAChF,OAAOF,SAAS,GAAGG,SAAS;IAC9B,CAAC,CAAC;EACJ;EAEA;EACQX,oBAAoBA,CAACC,OAAiB;IAC5C,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B;MACA,MAAMQ,MAAM,GAAG,IAAI,CAACC,4BAA4B,CAACV,CAAC,CAAC;MACnD,MAAMW,MAAM,GAAG,IAAI,CAACD,4BAA4B,CAACT,CAAC,CAAC;MACnD,OAAOU,MAAM,GAAGF,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ;EAEA;EACQC,4BAA4BA,CAACE,MAAc;IACjD,IAAI,CAACA,MAAM,CAAC5L,KAAK,IAAI4L,MAAM,CAAC5L,KAAK,CAACf,MAAM,KAAK,CAAC,IAAI,CAAC2M,MAAM,CAAC9K,MAAM,IAAI8K,MAAM,CAAC9K,MAAM,CAAC7B,MAAM,KAAK,CAAC,EAAE;MAC9F,OAAO,CAAC;;IAGV,MAAM4M,IAAI,GAAGD,MAAM,CAAC5L,KAAK,CAAC,CAAC,CAAC;IAC5B,MAAM8L,KAAK,GAAGF,MAAM,CAAC9K,MAAM,CAAC,CAAC,CAAC;IAE9B;IACA,MAAMuC,KAAK,GAAG,IAAI,CAAC8H,iBAAiB,CAACS,MAAM,CAAC;IAC5C,MAAMjH,QAAQ,GAAGkH,IAAI,CAAClH,QAAQ;IAC9B,MAAM3D,SAAS,GAAG6K,IAAI,CAAC7K,SAAS,IAAI,CAAC;IACrC,MAAM+K,YAAY,GAAGD,KAAK,CAACC,YAAY,KAAKC,SAAS,GAAGF,KAAK,CAACC,YAAY,GACrDD,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IAE5E;IACA,MAAMC,UAAU,GAAG,IAAI,IAAI9I,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;IACzC,MAAM+I,aAAa,GAAG,IAAI,IAAIzH,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC;IAC/C,MAAM0H,SAAS,GAAG,CAAC,IAAIrL,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;IACvC,MAAMsL,iBAAiB,GAAGC,IAAI,CAAC5C,GAAG,CAACoC,YAAY,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAE3D;IACA,MAAMS,OAAO,GAAG;MACdnJ,KAAK,EAAE,GAAG;MACVsB,QAAQ,EAAE,GAAG;MACb8H,KAAK,EAAE,GAAG;MACVV,YAAY,EAAE,GAAG,CAAC;KACnB;IAED;IACA,OACEI,UAAU,GAAGK,OAAO,CAACnJ,KAAK,GAC1B+I,aAAa,GAAGI,OAAO,CAAC7H,QAAQ,GAChC0H,SAAS,GAAGG,OAAO,CAACC,KAAK,GACzBH,iBAAiB,GAAGE,OAAO,CAACT,YAAY;EAE5C;EAEA;EACQZ,iBAAiBA,CAACS,MAAc;IACtC,IAAI,CAACA,MAAM,CAAC9K,MAAM,IAAI8K,MAAM,CAAC9K,MAAM,CAAC7B,MAAM,KAAK,CAAC,EAAE;MAChD,OAAOqM,MAAM,CAACC,SAAS;;IAGzB,OAAOK,MAAM,CAAC9K,MAAM,CAAC4L,MAAM,CAAC,CAAC/C,GAAG,EAAEmC,KAAK,KACrCA,KAAK,CAACzI,KAAK,IAAIyI,KAAK,CAACzI,KAAK,CAACC,MAAM,GAAGqG,GAAG,GAAGmC,KAAK,CAACzI,KAAK,CAACC,MAAM,GAAGqG,GAAG,EAClEiC,MAAM,CAAC9K,MAAM,CAAC,CAAC,CAAC,CAACuC,KAAK,GAAGuI,MAAM,CAAC9K,MAAM,CAAC,CAAC,CAAC,CAACuC,KAAK,CAACC,MAAM,GAAGgI,MAAM,CAACC,SAAS,CAC1E;EACH;EAEA;EACA5E,cAAcA,CAACiF,MAAc;IAC3B;IACA,MAAMe,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9CF,QAAQ,CAACG,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCJ,QAAQ,CAACG,KAAK,CAACE,GAAG,GAAG,GAAG;IACxBL,QAAQ,CAACG,KAAK,CAACG,IAAI,GAAG,GAAG;IACzBN,QAAQ,CAACG,KAAK,CAACI,KAAK,GAAG,MAAM;IAC7BP,QAAQ,CAACG,KAAK,CAACK,MAAM,GAAG,MAAM;IAC9BR,QAAQ,CAACG,KAAK,CAACM,eAAe,GAAG,oBAAoB;IACrDT,QAAQ,CAACG,KAAK,CAACO,MAAM,GAAG,MAAM;IAC9BV,QAAQ,CAACG,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC/BX,QAAQ,CAACG,KAAK,CAACS,cAAc,GAAG,QAAQ;IACxCZ,QAAQ,CAACG,KAAK,CAACU,UAAU,GAAG,QAAQ;IAEpC;IACA,MAAMC,YAAY,GAAGb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDY,YAAY,CAACX,KAAK,CAACM,eAAe,GAAG,OAAO;IAC5CK,YAAY,CAACX,KAAK,CAACY,OAAO,GAAG,MAAM;IACnCD,YAAY,CAACX,KAAK,CAACa,YAAY,GAAG,MAAM;IACxCF,YAAY,CAACX,KAAK,CAACc,QAAQ,GAAG,KAAK;IACnCH,YAAY,CAACX,KAAK,CAACe,SAAS,GAAG,KAAK;IACpCJ,YAAY,CAACX,KAAK,CAACgB,QAAQ,GAAG,MAAM;IACpCL,YAAY,CAACX,KAAK,CAACC,QAAQ,GAAG,UAAU;IACxCU,YAAY,CAACX,KAAK,CAACiB,SAAS,GAAG,gCAAgC;IAC/DN,YAAY,CAACX,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCP,YAAY,CAACX,KAAK,CAACmB,UAAU,GAAG,mBAAmB;IAEnD;IACA,MAAMC,WAAW,GAAGtB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACpDqB,WAAW,CAACC,SAAS,GAAG,SAAS;IACjCD,WAAW,CAACpB,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvCmB,WAAW,CAACpB,KAAK,CAACE,GAAG,GAAG,MAAM;IAC9BkB,WAAW,CAACpB,KAAK,CAACsB,KAAK,GAAG,MAAM;IAChCF,WAAW,CAACpB,KAAK,CAACuB,MAAM,GAAG,MAAM;IACjCH,WAAW,CAACpB,KAAK,CAACwB,UAAU,GAAG,MAAM;IACrCJ,WAAW,CAACpB,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACnCL,WAAW,CAACpB,KAAK,CAAC0B,MAAM,GAAG,SAAS;IACpCN,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnCE,WAAW,CAACpB,KAAK,CAAC2B,UAAU,GAAG,YAAY;IAC3CP,WAAW,CAACQ,WAAW,GAAG,MAAMR,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnEE,WAAW,CAACS,UAAU,GAAG,MAAMT,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IAClEE,WAAW,CAACU,OAAO,GAAG,MAAMhC,QAAQ,CAACiC,IAAI,CAACC,WAAW,CAACnC,QAAQ,CAAC;IAE/D;IACA,MAAMoC,MAAM,GAAGnC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5CkC,MAAM,CAACjC,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC7ByB,MAAM,CAACjC,KAAK,CAACU,UAAU,GAAG,QAAQ;IAClCuB,MAAM,CAACjC,KAAK,CAACkC,YAAY,GAAG,MAAM;IAClCD,MAAM,CAACjC,KAAK,CAACmC,aAAa,GAAG,MAAM;IACnCF,MAAM,CAACjC,KAAK,CAACoC,YAAY,GAAG,gBAAgB;IAE5C,MAAMC,IAAI,GAAGvC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CsC,IAAI,CAAChB,SAAS,GAAG,2FAA2F;IAE5G,MAAMiB,KAAK,GAAGxC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IAC1CuC,KAAK,CAACC,WAAW,GAAG,yBAAyB;IAC7CD,KAAK,CAACtC,KAAK,CAACwC,MAAM,GAAG,GAAG;IACxBF,KAAK,CAACtC,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC7Ba,KAAK,CAACtC,KAAK,CAACyC,UAAU,GAAG,KAAK;IAC9BH,KAAK,CAACtC,KAAK,CAACkB,KAAK,GAAG,SAAS;IAE7Be,MAAM,CAACS,WAAW,CAACL,IAAI,CAAC;IACxBJ,MAAM,CAACS,WAAW,CAACJ,KAAK,CAAC;IAEzB;IACA,MAAMK,gBAAgB,GAAG7C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACtD4C,gBAAgB,CAAC3C,KAAK,CAACQ,OAAO,GAAG,MAAM;IACvCmC,gBAAgB,CAAC3C,KAAK,CAAC4C,aAAa,GAAG,QAAQ;IAC/CD,gBAAgB,CAAC3C,KAAK,CAAC6C,GAAG,GAAG,MAAM;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9E;IACA,IAAIjE,MAAM,CAAC5L,KAAK,IAAI4L,MAAM,CAAC5L,KAAK,CAACf,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAM4M,IAAI,GAAGD,MAAM,CAAC5L,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,IAAI6L,IAAI,CAAC5L,OAAO,EAAE;QAChB,MAAM6P,WAAW,GAAGlD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDiD,WAAW,CAAChD,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCwC,WAAW,CAAChD,KAAK,CAACU,UAAU,GAAG,QAAQ;QACvCsC,WAAW,CAAChD,KAAK,CAACkC,YAAY,GAAG,MAAM;QAEvC;QACA,IAAInD,IAAI,CAAC5L,OAAO,CAACC,aAAa,EAAE;UAC9B,MAAM6P,WAAW,GAAGnD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDkD,WAAW,CAACC,GAAG,GAAGnE,IAAI,CAAC5L,OAAO,CAACC,aAAa;UAC5C6P,WAAW,CAACE,GAAG,GAAGpE,IAAI,CAAC5L,OAAO,CAAC3C,IAAI;UACnCyS,WAAW,CAACjD,KAAK,CAACK,MAAM,GAAG,MAAM;UACjC4C,WAAW,CAACjD,KAAK,CAACoD,WAAW,GAAG,MAAM;UACtCJ,WAAW,CAACN,WAAW,CAACO,WAAW,CAAC;SACrC,MAAM;UACL,MAAMI,WAAW,GAAGvD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDsD,WAAW,CAAChC,SAAS,GAAG,wFAAwF;UAChH2B,WAAW,CAACN,WAAW,CAACW,WAAW,CAAC;;QAGtC,MAAMC,WAAW,GAAGxD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDuD,WAAW,CAACjC,SAAS,GAAG,WAAWtC,IAAI,CAAC5L,OAAO,CAAC3C,IAAI,cAAcuO,IAAI,CAAC5L,OAAO,CAACoQ,iBAAiB,GAAG;QACnGD,WAAW,CAACtD,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCuB,WAAW,CAACN,WAAW,CAACY,WAAW,CAAC;QAEpCR,WAAW,CAACJ,WAAW,CAACM,WAAW,CAAC;;MAGtC;MACA,MAAMQ,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC,eAAe,EAAE1E,IAAI,CAACzH,QAAQ,IAAI,KAAK,CAAC;MACnFwL,WAAW,CAACJ,WAAW,CAACc,eAAe,CAAC;MAExC;MACA,MAAME,aAAa,GAAG,IAAI,CAACD,aAAa,CAAC,aAAa,EAAE,IAAIvH,IAAI,CAAC6C,IAAI,CAAC4E,UAAU,CAAC,CAACC,kBAAkB,EAAE,CAAC;MACvGd,WAAW,CAACJ,WAAW,CAACgB,aAAa,CAAC;MAEtC;MACA,MAAMG,WAAW,GAAG,IAAI,CAACJ,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC7L,cAAc,CAACmH,IAAI,CAAClH,QAAQ,CAAC,CAAC;MACtFiL,WAAW,CAACJ,WAAW,CAACmB,WAAW,CAAC;MAEpC;MACA,IAAI9E,IAAI,CAACtL,WAAW,EAAE;QACpB,MAAMqQ,QAAQ,GAAG,IAAI,CAACL,aAAa,CAAC,OAAO,EAAE,GAAG1E,IAAI,CAACtL,WAAW,CAACjD,IAAI,KAAKuO,IAAI,CAACtL,WAAW,CAACxD,IAAI,GAAG,CAAC;QACnG6S,WAAW,CAACJ,WAAW,CAACoB,QAAQ,CAAC;;MAGnC;MACA,MAAMC,QAAQ,GAAG,IAAI,CAACN,aAAa,CAAC,OAAO,EAAE1E,IAAI,CAAC7K,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,GAAG6K,IAAI,CAAC7K,SAAS,UAAU,CAAC;MAClH4O,WAAW,CAACJ,WAAW,CAACqB,QAAQ,CAAC;;IAGnC;IACA,MAAMC,YAAY,GAAG,IAAI,CAACjB,aAAa,CAAC,eAAe,EAAE,UAAU,CAAC;IAEpE,IAAIjE,MAAM,CAAC5L,KAAK,IAAI4L,MAAM,CAAC5L,KAAK,CAACf,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAM4M,IAAI,GAAGD,MAAM,CAAC5L,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,MAAM+Q,WAAW,GAAGnE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACjDkE,WAAW,CAACjE,KAAK,CAACQ,OAAO,GAAG,MAAM;MAClCyD,WAAW,CAACjE,KAAK,CAACU,UAAU,GAAG,QAAQ;MACvCuD,WAAW,CAACjE,KAAK,CAACS,cAAc,GAAG,eAAe;MAClDwD,WAAW,CAACjE,KAAK,CAACwC,MAAM,GAAG,QAAQ;MACnCyB,WAAW,CAACjE,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEvC;MACA,MAAM1I,SAAS,GAAGuI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/CxI,SAAS,CAACyI,KAAK,CAACkE,SAAS,GAAG,QAAQ;MACpC3M,SAAS,CAACyI,KAAK,CAACmE,IAAI,GAAG,GAAG;MAE1B,IAAIpF,IAAI,CAACxH,SAAS,EAAE;QAClB,MAAM6M,aAAa,GAAGtE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnDqE,aAAa,CAACpE,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC2C,aAAa,CAACpE,KAAK,CAACyC,UAAU,GAAG,MAAM;QACvC2B,aAAa,CAAC7B,WAAW,GAAG,IAAIrG,IAAI,CAAC6C,IAAI,CAACxH,SAAS,CAACG,IAAI,CAAC,CAAC2M,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAErH,MAAMC,gBAAgB,GAAG1E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACtDyE,gBAAgB,CAACxE,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACxC+C,gBAAgB,CAACxE,KAAK,CAACyE,SAAS,GAAG,KAAK;QACxCD,gBAAgB,CAACnD,SAAS,GAAG,WAAWtC,IAAI,CAACxH,SAAS,CAACI,OAAO,EAAE1H,IAAI,IAAI,KAAK,WAAW;QAExF,MAAMyU,aAAa,GAAG5E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnD2E,aAAa,CAAC1E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCiD,aAAa,CAAC1E,KAAK,CAACkB,KAAK,GAAG,MAAM;QAClCwD,aAAa,CAACnC,WAAW,GAAGxD,IAAI,CAACxH,SAAS,CAACrH,IAAI,EAAEM,IAAI,IAAI,KAAK;QAE9D+G,SAAS,CAACmL,WAAW,CAAC0B,aAAa,CAAC;QACpC7M,SAAS,CAACmL,WAAW,CAAC8B,gBAAgB,CAAC;QACvCjN,SAAS,CAACmL,WAAW,CAACgC,aAAa,CAAC;;MAGtC;MACA,MAAMC,cAAc,GAAG7E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACpD4E,cAAc,CAAC3E,KAAK,CAACmE,IAAI,GAAG,GAAG;MAC/BQ,cAAc,CAAC3E,KAAK,CAACQ,OAAO,GAAG,MAAM;MACrCmE,cAAc,CAAC3E,KAAK,CAACU,UAAU,GAAG,QAAQ;MAC1CiE,cAAc,CAAC3E,KAAK,CAACS,cAAc,GAAG,QAAQ;MAC9CkE,cAAc,CAAC3E,KAAK,CAACY,OAAO,GAAG,QAAQ;MAEvC,MAAMgE,IAAI,GAAG9E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC1C6E,IAAI,CAAC5E,KAAK,CAACK,MAAM,GAAG,KAAK;MACzBuE,IAAI,CAAC5E,KAAK,CAACM,eAAe,GAAG,MAAM;MACnCsE,IAAI,CAAC5E,KAAK,CAACI,KAAK,GAAG,MAAM;MACzBwE,IAAI,CAAC5E,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEhC,MAAM4E,KAAK,GAAG/E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC3C8E,KAAK,CAACxD,SAAS,GAAG,iGAAiG;MACnHwD,KAAK,CAAC7E,KAAK,CAACC,QAAQ,GAAG,UAAU;MACjC4E,KAAK,CAAC7E,KAAK,CAACE,GAAG,GAAG,MAAM;MACxB2E,KAAK,CAAC7E,KAAK,CAACG,IAAI,GAAG,KAAK;MACxB0E,KAAK,CAAC7E,KAAK,CAAC8E,UAAU,GAAG,MAAM;MAC/BD,KAAK,CAAC7E,KAAK,CAACM,eAAe,GAAG,OAAO;MACrCuE,KAAK,CAAC7E,KAAK,CAACY,OAAO,GAAG,OAAO;MAE7BgE,IAAI,CAAClC,WAAW,CAACmC,KAAK,CAAC;MACvBF,cAAc,CAACjC,WAAW,CAACkC,IAAI,CAAC;MAEhC;MACA,MAAMxN,OAAO,GAAG0I,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7C3I,OAAO,CAAC4I,KAAK,CAACkE,SAAS,GAAG,QAAQ;MAClC9M,OAAO,CAAC4I,KAAK,CAACmE,IAAI,GAAG,GAAG;MAExB,IAAIpF,IAAI,CAAC3H,OAAO,EAAE;QAChB,MAAM2N,WAAW,GAAGjF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDgF,WAAW,CAAC/E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCsD,WAAW,CAAC/E,KAAK,CAACyC,UAAU,GAAG,MAAM;QACrCsC,WAAW,CAACxC,WAAW,GAAG,IAAIrG,IAAI,CAAC6C,IAAI,CAAC3H,OAAO,CAACM,IAAI,CAAC,CAAC2M,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAEjH,MAAMS,cAAc,GAAGlF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACpDiF,cAAc,CAAChF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACtCuD,cAAc,CAAChF,KAAK,CAACyE,SAAS,GAAG,KAAK;QACtCO,cAAc,CAAC3D,SAAS,GAAG,WAAWtC,IAAI,CAAC3H,OAAO,CAACO,OAAO,EAAE1H,IAAI,IAAI,KAAK,WAAW;QAEpF,MAAMgV,WAAW,GAAGnF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDkF,WAAW,CAACjF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCwD,WAAW,CAACjF,KAAK,CAACkB,KAAK,GAAG,MAAM;QAChC+D,WAAW,CAAC1C,WAAW,GAAGxD,IAAI,CAAC3H,OAAO,CAAClH,IAAI,EAAEM,IAAI,IAAI,KAAK;QAE1D4G,OAAO,CAACsL,WAAW,CAACqC,WAAW,CAAC;QAChC3N,OAAO,CAACsL,WAAW,CAACsC,cAAc,CAAC;QACnC5N,OAAO,CAACsL,WAAW,CAACuC,WAAW,CAAC;;MAGlChB,WAAW,CAACvB,WAAW,CAACnL,SAAS,CAAC;MAClC0M,WAAW,CAACvB,WAAW,CAACiC,cAAc,CAAC;MACvCV,WAAW,CAACvB,WAAW,CAACtL,OAAO,CAAC;MAEhC4M,YAAY,CAACtB,WAAW,CAACuB,WAAW,CAAC;MAErC;MACA,IAAIlF,IAAI,CAAC7H,QAAQ,IAAI6H,IAAI,CAAC7H,QAAQ,CAAC/E,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAM+S,aAAa,GAAGpF,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAClDmF,aAAa,CAAC3C,WAAW,GAAG,iBAAiB;QAC7C2C,aAAa,CAAClF,KAAK,CAACyE,SAAS,GAAG,MAAM;QACtCS,aAAa,CAAClF,KAAK,CAACkC,YAAY,GAAG,MAAM;QACzCgD,aAAa,CAAClF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCyD,aAAa,CAAClF,KAAK,CAACyC,UAAU,GAAG,KAAK;QACtCuB,YAAY,CAACtB,WAAW,CAACwC,aAAa,CAAC;QAEvC,MAAMC,YAAY,GAAGrF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClDoF,YAAY,CAACnF,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnC2E,YAAY,CAACnF,KAAK,CAAC4C,aAAa,GAAG,QAAQ;QAC3CuC,YAAY,CAACnF,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B9D,IAAI,CAAC7H,QAAQ,CAACkO,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAI;UACvC,MAAMC,WAAW,GAAGzF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDwF,WAAW,CAACvF,KAAK,CAACY,OAAO,GAAG,MAAM;UAClC2E,WAAW,CAACvF,KAAK,CAACM,eAAe,GAAG,SAAS;UAC7CiF,WAAW,CAACvF,KAAK,CAACa,YAAY,GAAG,KAAK;UACtC0E,WAAW,CAACvF,KAAK,CAACuB,MAAM,GAAG,gBAAgB;UAE3C,MAAMiE,aAAa,GAAG1F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACnDyF,aAAa,CAACxF,KAAK,CAACQ,OAAO,GAAG,MAAM;UACpCgF,aAAa,CAACxF,KAAK,CAACS,cAAc,GAAG,eAAe;UACpD+E,aAAa,CAACxF,KAAK,CAACkC,YAAY,GAAG,MAAM;UACzCsD,aAAa,CAACxF,KAAK,CAACmC,aAAa,GAAG,MAAM;UAC1CqD,aAAa,CAACxF,KAAK,CAACoC,YAAY,GAAG,gBAAgB;UAEnD,MAAMqD,YAAY,GAAG3F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClD0F,YAAY,CAACpE,SAAS,GAAG,mBAAmBiE,KAAK,GAAG,CAAC,cAAcD,OAAO,CAAClS,OAAO,EAAE3C,IAAI,IAAI,SAAS,IAAI6U,OAAO,CAAC/N,QAAQ,EAAE;UAE3H,MAAMoO,eAAe,GAAG5F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACrD2F,eAAe,CAACnD,WAAW,GAAG,IAAI,CAAC3K,cAAc,CAACyN,OAAO,CAACxN,QAAQ,CAAC;UACnE6N,eAAe,CAAC1F,KAAK,CAACkB,KAAK,GAAG,MAAM;UAEpCsE,aAAa,CAAC9C,WAAW,CAAC+C,YAAY,CAAC;UACvCD,aAAa,CAAC9C,WAAW,CAACgD,eAAe,CAAC;UAC1CH,WAAW,CAAC7C,WAAW,CAAC8C,aAAa,CAAC;UAEtC;UACA,MAAMG,YAAY,GAAG7F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClD4F,YAAY,CAAC3F,KAAK,CAACQ,OAAO,GAAG,MAAM;UACnCmF,YAAY,CAAC3F,KAAK,CAACU,UAAU,GAAG,QAAQ;UACxCiF,YAAY,CAAC3F,KAAK,CAACS,cAAc,GAAG,eAAe;UAEnD;UACA,MAAMmF,gBAAgB,GAAG9F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACtD6F,gBAAgB,CAAC5F,KAAK,CAACmE,IAAI,GAAG,GAAG;UAEjC,IAAIkB,OAAO,CAAC9N,SAAS,EAAE;YACrB,MAAMsO,OAAO,GAAG/F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7C8F,OAAO,CAAC7F,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCoD,OAAO,CAACtD,WAAW,GAAG,IAAIrG,IAAI,CAACmJ,OAAO,CAAC9N,SAAS,CAACG,IAAI,CAAC,CAAC2M,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAElH,MAAMuB,UAAU,GAAGhG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChD+F,UAAU,CAAC9F,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClCqE,UAAU,CAACvD,WAAW,GAAG,GAAG8C,OAAO,CAAC9N,SAAS,CAACI,OAAO,EAAE1H,IAAI,IAAI,KAAK,KAAKoV,OAAO,CAAC9N,SAAS,CAACrH,IAAI,EAAEM,IAAI,IAAI,KAAK,GAAG;YAEjHoV,gBAAgB,CAAClD,WAAW,CAACmD,OAAO,CAAC;YACrCD,gBAAgB,CAAClD,WAAW,CAACoD,UAAU,CAAC;;UAG1C;UACA,MAAMC,KAAK,GAAGjG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAC3CgG,KAAK,CAAC1E,SAAS,GAAG,qFAAqF;UAEvG;UACA,MAAM2E,cAAc,GAAGlG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACpDiG,cAAc,CAAChG,KAAK,CAACmE,IAAI,GAAG,GAAG;UAC/B6B,cAAc,CAAChG,KAAK,CAACkE,SAAS,GAAG,OAAO;UAExC,IAAImB,OAAO,CAACjO,OAAO,EAAE;YACnB,MAAM6O,OAAO,GAAGnG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CkG,OAAO,CAACjG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCwD,OAAO,CAAC1D,WAAW,GAAG,IAAIrG,IAAI,CAACmJ,OAAO,CAACjO,OAAO,CAACM,IAAI,CAAC,CAAC2M,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAEhH,MAAM2B,UAAU,GAAGpG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChDmG,UAAU,CAAClG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClCyE,UAAU,CAAC3D,WAAW,GAAG,GAAG8C,OAAO,CAACjO,OAAO,CAACO,OAAO,EAAE1H,IAAI,IAAI,KAAK,KAAKoV,OAAO,CAACjO,OAAO,CAAClH,IAAI,EAAEM,IAAI,IAAI,KAAK,GAAG;YAE7GwV,cAAc,CAACtD,WAAW,CAACuD,OAAO,CAAC;YACnCD,cAAc,CAACtD,WAAW,CAACwD,UAAU,CAAC;;UAGxCP,YAAY,CAACjD,WAAW,CAACkD,gBAAgB,CAAC;UAC1CD,YAAY,CAACjD,WAAW,CAACqD,KAAK,CAAC;UAC/BJ,YAAY,CAACjD,WAAW,CAACsD,cAAc,CAAC;UACxCT,WAAW,CAAC7C,WAAW,CAACiD,YAAY,CAAC;UAErCR,YAAY,CAACzC,WAAW,CAAC6C,WAAW,CAAC;UAErC;UACA,IAAID,KAAK,GAAGvG,IAAI,CAAC7H,QAAQ,CAAC/E,MAAM,GAAG,CAAC,EAAE;YACpC,MAAMgU,OAAO,GAAGrG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CoG,OAAO,CAACnG,KAAK,CAACkE,SAAS,GAAG,QAAQ;YAClCiC,OAAO,CAACnG,KAAK,CAACY,OAAO,GAAG,MAAM;YAC9BuF,OAAO,CAACnG,KAAK,CAACkB,KAAK,GAAG,SAAS;YAC/BiF,OAAO,CAACnG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAE/B;YACA,MAAM2E,cAAc,GAAG,IAAIlK,IAAI,CAACmJ,OAAO,CAACjO,OAAO,EAAEM,IAAI,IAAI,CAAC,CAAC,CAAC2O,OAAO,EAAE;YACrE,MAAMC,aAAa,GAAG,IAAIpK,IAAI,CAAC6C,IAAI,CAAC7H,QAAQ,CAACoO,KAAK,GAAG,CAAC,CAAC,CAAC/N,SAAS,EAAEG,IAAI,IAAI,CAAC,CAAC,CAAC2O,OAAO,EAAE;YACvF,MAAME,WAAW,GAAG9G,IAAI,CAAC+G,KAAK,CAAC,CAACF,aAAa,GAAGF,cAAc,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;YAE9ED,OAAO,CAAC9E,SAAS,GAAG,yCAAyC,IAAI,CAACzJ,cAAc,CAAC2O,WAAW,CAAC,eAAelB,OAAO,CAACjO,OAAO,EAAElH,IAAI,EAAEM,IAAI,IAAI,iBAAiB,EAAE;YAE9J2U,YAAY,CAACzC,WAAW,CAACyD,OAAO,CAAC;;QAErC,CAAC,CAAC;QAEFnC,YAAY,CAACtB,WAAW,CAACyC,YAAY,CAAC;;;IAI1C;IACA,MAAMsB,aAAa,GAAG,IAAI,CAAC1D,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAE5D,IAAIjE,MAAM,CAAC9K,MAAM,IAAI8K,MAAM,CAAC9K,MAAM,CAAC7B,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAMuU,UAAU,GAAG5G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAChD2G,UAAU,CAAC1G,KAAK,CAACQ,OAAO,GAAG,MAAM;MACjCkG,UAAU,CAAC1G,KAAK,CAAC4C,aAAa,GAAG,QAAQ;MACzC8D,UAAU,CAAC1G,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE7B/D,MAAM,CAAC9K,MAAM,CAACoR,OAAO,CAAC,CAACpG,KAAK,EAAEsG,KAAK,KAAI;QACrC,MAAMqB,SAAS,GAAG7G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC/C4G,SAAS,CAAC3G,KAAK,CAACY,OAAO,GAAG,MAAM;QAChC+F,SAAS,CAAC3G,KAAK,CAACM,eAAe,GAAG,SAAS;QAC3CqG,SAAS,CAAC3G,KAAK,CAACa,YAAY,GAAG,KAAK;QACpC8F,SAAS,CAAC3G,KAAK,CAACuB,MAAM,GAAG,gBAAgB;QAEzC;QACA,MAAMqF,WAAW,GAAG9G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjD6G,WAAW,CAAC5G,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCoG,WAAW,CAAC5G,KAAK,CAACS,cAAc,GAAG,eAAe;QAClDmG,WAAW,CAAC5G,KAAK,CAACkC,YAAY,GAAG,MAAM;QACvC0E,WAAW,CAAC5G,KAAK,CAACmC,aAAa,GAAG,MAAM;QACxCyE,WAAW,CAAC5G,KAAK,CAACoC,YAAY,GAAG,gBAAgB;QAEjD,MAAMyE,UAAU,GAAG/G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChD8G,UAAU,CAACxF,SAAS,GAAG,iBAAiBiE,KAAK,GAAG,CAAC,WAAW;QAC5DuB,UAAU,CAAC7G,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAElC,MAAMqF,UAAU,GAAGhH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChD+G,UAAU,CAAC9G,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAClCqF,UAAU,CAAC9G,KAAK,CAACyC,UAAU,GAAG,MAAM;QACpCqE,UAAU,CAAC9G,KAAK,CAACkB,KAAK,GAAG,SAAS;QAClC4F,UAAU,CAACvE,WAAW,GAAG,GAAGvD,KAAK,CAACzI,KAAK,CAACC,MAAM,IAAIwI,KAAK,CAACzI,KAAK,CAACE,QAAQ,EAAE;QAExEmQ,WAAW,CAAClE,WAAW,CAACmE,UAAU,CAAC;QACnCD,WAAW,CAAClE,WAAW,CAACoE,UAAU,CAAC;QACnCH,SAAS,CAACjE,WAAW,CAACkE,WAAW,CAAC;QAElC;QACA,MAAMG,YAAY,GAAGjH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClDgH,YAAY,CAAC/G,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnCuG,YAAY,CAAC/G,KAAK,CAACgH,mBAAmB,GAAG,uCAAuC;QAChFD,YAAY,CAAC/G,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B;QACA,MAAMoE,iBAAiB,GAAGjI,KAAK,CAACC,YAAY,KAAKC,SAAS,GAAGF,KAAK,CAACC,YAAY,GACrDD,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACC,kBAAkB,GAAG,CAAE;QACjF,MAAMH,YAAY,GAAG,IAAI,CAACwE,aAAa,CAAC,cAAc,EAAEwD,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,eAAe,CAAC;QAC9GF,YAAY,CAACrE,WAAW,CAACzD,YAAY,CAAC;QAEtC;QACA,IAAID,KAAK,CAAC/K,SAAS,EAAE;UACnB,MAAMiT,OAAO,GAAG,IAAI,CAACzD,aAAa,CAAC,YAAY,EAAE,IAAIvH,IAAI,CAAC8C,KAAK,CAAC/K,SAAS,CAAC,CAACkT,cAAc,EAAE,CAAC;UAC5FJ,YAAY,CAACrE,WAAW,CAACwE,OAAO,CAAC;;QAGnC;QACA,IAAIlI,KAAK,CAACjH,WAAW,EAAE;UACrB,MAAMA,WAAW,GAAG,IAAI,CAAC0L,aAAa,CAAC,cAAc,EAAEzE,KAAK,CAACjH,WAAW,CAACvH,IAAI,CAAC;UAC9EuW,YAAY,CAACrE,WAAW,CAAC3K,WAAW,CAAC;;QAGvC;QACA,IAAIiH,KAAK,CAACnJ,cAAc,EAAE;UACxB,MAAMC,UAAU,GAAG,IAAI,CAAC2N,aAAa,CAAC,YAAY,EAAEzE,KAAK,CAACnJ,cAAc,CAACC,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC;UACnGiR,YAAY,CAACrE,WAAW,CAAC5M,UAAU,CAAC;;QAGtC;QACA,IAAIkJ,KAAK,CAAClK,mBAAmB,IAAIkK,KAAK,CAAClK,mBAAmB,CAAC3C,MAAM,GAAG,CAAC,EAAE;UACrE,MAAMiV,YAAY,GAAGtH,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;UACjDqH,YAAY,CAAC7E,WAAW,GAAG,qBAAqB;UAChD6E,YAAY,CAACpH,KAAK,CAACyE,SAAS,GAAG,MAAM;UACrC2C,YAAY,CAACpH,KAAK,CAACkC,YAAY,GAAG,MAAM;UACxCkF,YAAY,CAACpH,KAAK,CAACyB,QAAQ,GAAG,MAAM;UACpC2F,YAAY,CAACpH,KAAK,CAACyC,UAAU,GAAG,KAAK;UACrCkE,SAAS,CAACjE,WAAW,CAAC0E,YAAY,CAAC;UAEnC,MAAMC,gBAAgB,GAAGvH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACtDsH,gBAAgB,CAACrH,KAAK,CAACQ,OAAO,GAAG,MAAM;UACvC6G,gBAAgB,CAACrH,KAAK,CAAC4C,aAAa,GAAG,QAAQ;UAC/CyE,gBAAgB,CAACrH,KAAK,CAAC6C,GAAG,GAAG,MAAM;UACnCwE,gBAAgB,CAACrH,KAAK,CAACkC,YAAY,GAAG,MAAM;UAE5C;UACA,MAAMoF,cAAc,GAAGtI,KAAK,CAAClK,mBAAmB,CAACyS,MAAM,CAACpJ,CAAC,IAAIA,CAAC,CAACqJ,WAAW,KAAK,CAAC,CAAC;UACjF,MAAMC,YAAY,GAAGzI,KAAK,CAAClK,mBAAmB,CAACyS,MAAM,CAACpJ,CAAC,IAAIA,CAAC,CAACqJ,WAAW,KAAK,CAAC,CAAC;UAC/E,MAAME,WAAW,GAAG1I,KAAK,CAAClK,mBAAmB,CAACyS,MAAM,CAACpJ,CAAC,IAAIA,CAAC,CAACqJ,WAAW,KAAK,CAAC,CAAC;UAE9E;UACA,IAAIF,cAAc,CAACnV,MAAM,GAAG,CAAC,EAAE;YAC7BmV,cAAc,CAAClC,OAAO,CAACuC,OAAO,IAAG;cAC/B,MAAMC,WAAW,GAAG9H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjD6H,WAAW,CAAC5H,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClCoH,WAAW,CAAC5H,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvCkH,WAAW,CAAC5H,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCgH,WAAW,CAAC5H,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7CsH,WAAW,CAAC5H,KAAK,CAACa,YAAY,GAAG,KAAK;cACtC+G,WAAW,CAAC5H,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAMsG,WAAW,GAAG/H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjD8H,WAAW,CAACxG,SAAS,GAAG,8FAA8F;cAEtH,MAAMyG,WAAW,GAAGhI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjD+H,WAAW,CAAC9H,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClCsH,WAAW,CAAC9H,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAM4E,WAAW,GAAG1H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjDyH,WAAW,CAACjF,WAAW,GAAG,iBAAiB;cAC3CiF,WAAW,CAACxH,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpC+E,WAAW,CAACxH,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAMsG,cAAc,GAAGjI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACpDgI,cAAc,CAAC/H,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtCsG,cAAc,CAAC/H,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAI8G,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACvT,MAAM,GAAG,CAAC,EAAE4T,WAAW,IAAI,GAAGL,OAAO,CAACvT,MAAM,KAAK;cAC7D,IAAIuT,OAAO,CAACtT,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAI2T,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACtT,KAAK,WAAW;;cAE5C0T,cAAc,CAACxF,WAAW,GAAGyF,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAACpF,WAAW,CAAC8E,WAAW,CAAC;cACpCM,WAAW,CAACpF,WAAW,CAACqF,cAAc,CAAC;cAEvCH,WAAW,CAAClF,WAAW,CAACmF,WAAW,CAAC;cACpCD,WAAW,CAAClF,WAAW,CAACoF,WAAW,CAAC;cACpCT,gBAAgB,CAAC3E,WAAW,CAACkF,WAAW,CAAC;YAC3C,CAAC,CAAC;;UAGJ;UACA,IAAIH,YAAY,CAACtV,MAAM,GAAG,CAAC,EAAE;YAC3BsV,YAAY,CAACrC,OAAO,CAACuC,OAAO,IAAG;cAC7B,MAAMC,WAAW,GAAG9H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjD6H,WAAW,CAAC5H,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClCoH,WAAW,CAAC5H,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvCkH,WAAW,CAAC5H,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCgH,WAAW,CAAC5H,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7CsH,WAAW,CAAC5H,KAAK,CAACa,YAAY,GAAG,KAAK;cACtC+G,WAAW,CAAC5H,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAMsG,WAAW,GAAG/H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjD8H,WAAW,CAACxG,SAAS,GAAG,+FAA+F;cAEvH,MAAMyG,WAAW,GAAGhI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjD+H,WAAW,CAAC9H,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClCsH,WAAW,CAAC9H,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAM4E,WAAW,GAAG1H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjDyH,WAAW,CAACjF,WAAW,GAAG,eAAe;cACzCiF,WAAW,CAACxH,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpC+E,WAAW,CAACxH,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAMsG,cAAc,GAAGjI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACpDgI,cAAc,CAAC/H,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtCsG,cAAc,CAAC/H,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAI8G,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACvT,MAAM,GAAG,CAAC,EAAE4T,WAAW,IAAI,GAAGL,OAAO,CAACvT,MAAM,KAAK;cAC7D,IAAIuT,OAAO,CAACtT,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAI2T,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACtT,KAAK,WAAW;;cAE5C0T,cAAc,CAACxF,WAAW,GAAGyF,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAACpF,WAAW,CAAC8E,WAAW,CAAC;cACpCM,WAAW,CAACpF,WAAW,CAACqF,cAAc,CAAC;cAEvCH,WAAW,CAAClF,WAAW,CAACmF,WAAW,CAAC;cACpCD,WAAW,CAAClF,WAAW,CAACoF,WAAW,CAAC;cACpCT,gBAAgB,CAAC3E,WAAW,CAACkF,WAAW,CAAC;YAC3C,CAAC,CAAC;WACH,MAAM;YACL;YACA,MAAMA,WAAW,GAAG9H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YACjD6H,WAAW,CAAC5H,KAAK,CAACQ,OAAO,GAAG,MAAM;YAClCoH,WAAW,CAAC5H,KAAK,CAACU,UAAU,GAAG,QAAQ;YACvCkH,WAAW,CAAC5H,KAAK,CAACY,OAAO,GAAG,WAAW;YACvCgH,WAAW,CAAC5H,KAAK,CAACM,eAAe,GAAG,SAAS;YAC7CsH,WAAW,CAAC5H,KAAK,CAACa,YAAY,GAAG,KAAK;YACtC+G,WAAW,CAAC5H,KAAK,CAACuB,MAAM,GAAG,mBAAmB;YAE9C,MAAMsG,WAAW,GAAG/H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YACjD8H,WAAW,CAACxG,SAAS,GAAG,+FAA+F;YAEvH,MAAMyG,WAAW,GAAGhI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YACjD+H,WAAW,CAAC9H,KAAK,CAACQ,OAAO,GAAG,MAAM;YAClCsH,WAAW,CAAC9H,KAAK,CAAC4C,aAAa,GAAG,QAAQ;YAE1C,MAAM4E,WAAW,GAAG1H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YACjDyH,WAAW,CAACjF,WAAW,GAAG,eAAe;YACzCiF,WAAW,CAACxH,KAAK,CAACyC,UAAU,GAAG,KAAK;YACpC+E,WAAW,CAACxH,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAEnC,MAAMsG,cAAc,GAAGjI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YACpDgI,cAAc,CAAC/H,KAAK,CAACyB,QAAQ,GAAG,MAAM;YACtCsG,cAAc,CAAC/H,KAAK,CAACkB,KAAK,GAAG,MAAM;YACnC6G,cAAc,CAACxF,WAAW,GAAG,UAAU;YAEvCuF,WAAW,CAACpF,WAAW,CAAC8E,WAAW,CAAC;YACpCM,WAAW,CAACpF,WAAW,CAACqF,cAAc,CAAC;YAEvCH,WAAW,CAAClF,WAAW,CAACmF,WAAW,CAAC;YACpCD,WAAW,CAAClF,WAAW,CAACoF,WAAW,CAAC;YACpCT,gBAAgB,CAAC3E,WAAW,CAACkF,WAAW,CAAC;;UAG3C;UACA,IAAIF,WAAW,CAACvV,MAAM,GAAG,CAAC,EAAE;YAC1BuV,WAAW,CAACtC,OAAO,CAACuC,OAAO,IAAG;cAC5B,MAAMC,WAAW,GAAG9H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjD6H,WAAW,CAAC5H,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClCoH,WAAW,CAAC5H,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvCkH,WAAW,CAAC5H,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCgH,WAAW,CAAC5H,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7CsH,WAAW,CAAC5H,KAAK,CAACa,YAAY,GAAG,KAAK;cACtC+G,WAAW,CAAC5H,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAMsG,WAAW,GAAG/H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjD8H,WAAW,CAACxG,SAAS,GAAG,kGAAkG;cAE1H,MAAMyG,WAAW,GAAGhI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjD+H,WAAW,CAAC9H,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClCsH,WAAW,CAAC9H,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAM4E,WAAW,GAAG1H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACjDyH,WAAW,CAACjF,WAAW,GAAG,cAAc;cACxCiF,WAAW,CAACxH,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpC+E,WAAW,CAACxH,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAMsG,cAAc,GAAGjI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cACpDgI,cAAc,CAAC/H,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtCsG,cAAc,CAAC/H,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAI8G,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACvT,MAAM,GAAG,CAAC,EAAE4T,WAAW,IAAI,GAAGL,OAAO,CAACvT,MAAM,KAAK;cAC7D,IAAIuT,OAAO,CAACtT,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAI2T,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACtT,KAAK,WAAW;;cAE5C0T,cAAc,CAACxF,WAAW,GAAGyF,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAACpF,WAAW,CAAC8E,WAAW,CAAC;cACpCM,WAAW,CAACpF,WAAW,CAACqF,cAAc,CAAC;cAEvCH,WAAW,CAAClF,WAAW,CAACmF,WAAW,CAAC;cACpCD,WAAW,CAAClF,WAAW,CAACoF,WAAW,CAAC;cACpCT,gBAAgB,CAAC3E,WAAW,CAACkF,WAAW,CAAC;YAC3C,CAAC,CAAC;;UAGJjB,SAAS,CAACjE,WAAW,CAAC2E,gBAAgB,CAAC;;QAGzCV,SAAS,CAACjE,WAAW,CAACqE,YAAY,CAAC;QAEnCL,UAAU,CAAChE,WAAW,CAACiE,SAAS,CAAC;MACnC,CAAC,CAAC;MAEFF,aAAa,CAAC/D,WAAW,CAACgE,UAAU,CAAC;;IAGvC;IACA,IAAI5H,MAAM,CAAC5L,KAAK,IAAI4L,MAAM,CAAC5L,KAAK,CAAC,CAAC,CAAC,IAAI4L,MAAM,CAAC5L,KAAK,CAAC,CAAC,CAAC,CAACyC,QAAQ,IAAImJ,MAAM,CAAC5L,KAAK,CAAC,CAAC,CAAC,CAACyC,QAAQ,CAACxD,MAAM,GAAG,CAAC,EAAE;MACtG,MAAM8V,eAAe,GAAG,IAAI,CAAClF,aAAa,CAAC,UAAU,EAAE,mBAAmB,CAAC;MAE3E,MAAMmF,YAAY,GAAGpI,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;MACjDmI,YAAY,CAAClI,KAAK,CAACmI,SAAS,GAAG,MAAM;MACrCD,YAAY,CAAClI,KAAK,CAACY,OAAO,GAAG,GAAG;MAChCsH,YAAY,CAAClI,KAAK,CAACwC,MAAM,GAAG,GAAG;MAC/B0F,YAAY,CAAClI,KAAK,CAACQ,OAAO,GAAG,MAAM;MACnC0H,YAAY,CAAClI,KAAK,CAACgH,mBAAmB,GAAG,uCAAuC;MAChFkB,YAAY,CAAClI,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE/B/D,MAAM,CAAC5L,KAAK,CAAC,CAAC,CAAC,CAACyC,QAAQ,CAACyP,OAAO,CAACgD,OAAO,IAAG;QACzC,MAAMC,WAAW,GAAGvI,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAChDsI,WAAW,CAACrI,KAAK,CAACY,OAAO,GAAG,MAAM;QAClCyH,WAAW,CAACrI,KAAK,CAACM,eAAe,GAAG,SAAS;QAC7C+H,WAAW,CAACrI,KAAK,CAACa,YAAY,GAAG,KAAK;QACtCwH,WAAW,CAAChH,SAAS,GAAG,2EAA2E+G,OAAO,CAAC5X,IAAI,IAAI,SAAS,EAAE;QAC9H0X,YAAY,CAACxF,WAAW,CAAC2F,WAAW,CAAC;MACvC,CAAC,CAAC;MAEFJ,eAAe,CAACvF,WAAW,CAACwF,YAAY,CAAC;MACzCvF,gBAAgB,CAACD,WAAW,CAACuF,eAAe,CAAC;;IAG/C;IACAtF,gBAAgB,CAACD,WAAW,CAACI,WAAW,CAAC;IACzCH,gBAAgB,CAACD,WAAW,CAACsB,YAAY,CAAC;IAC1CrB,gBAAgB,CAACD,WAAW,CAAC+D,aAAa,CAAC;IAE3C;IACA9F,YAAY,CAAC+B,WAAW,CAACtB,WAAW,CAAC;IACrCT,YAAY,CAAC+B,WAAW,CAACT,MAAM,CAAC;IAChCtB,YAAY,CAAC+B,WAAW,CAACC,gBAAgB,CAAC;IAC1C9C,QAAQ,CAAC6C,WAAW,CAAC/B,YAAY,CAAC;IAElC;IACAb,QAAQ,CAACiC,IAAI,CAACW,WAAW,CAAC7C,QAAQ,CAAC;EACrC;EAEA;EACQkD,aAAaA,CAACT,KAAa,EAAEgG,SAAiB;IACpD,MAAMC,OAAO,GAAGzI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7CwI,OAAO,CAACvI,KAAK,CAACM,eAAe,GAAG,SAAS;IACzCiI,OAAO,CAACvI,KAAK,CAACa,YAAY,GAAG,KAAK;IAClC0H,OAAO,CAACvI,KAAK,CAACY,OAAO,GAAG,MAAM;IAC9B2H,OAAO,CAACvI,KAAK,CAACiB,SAAS,GAAG,+BAA+B;IAEzD,MAAMuH,aAAa,GAAG1I,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACnDyI,aAAa,CAACxI,KAAK,CAACQ,OAAO,GAAG,MAAM;IACpCgI,aAAa,CAACxI,KAAK,CAACU,UAAU,GAAG,QAAQ;IACzC8H,aAAa,CAACxI,KAAK,CAACkC,YAAY,GAAG,MAAM;IAEzC,MAAMrP,IAAI,GAAGiN,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxClN,IAAI,CAAC4V,SAAS,GAAG,OAAOH,SAAS,EAAE;IACnCzV,IAAI,CAACmN,KAAK,CAACkB,KAAK,GAAG,SAAS;IAC5BrO,IAAI,CAACmN,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC5B5O,IAAI,CAACmN,KAAK,CAACoD,WAAW,GAAG,MAAM;IAE/B,MAAMsF,YAAY,GAAG5I,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IACjD2I,YAAY,CAACnG,WAAW,GAAGD,KAAK;IAChCoG,YAAY,CAAC1I,KAAK,CAACwC,MAAM,GAAG,GAAG;IAC/BkG,YAAY,CAAC1I,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCiH,YAAY,CAAC1I,KAAK,CAACyC,UAAU,GAAG,KAAK;IAErC+F,aAAa,CAAC9F,WAAW,CAAC7P,IAAI,CAAC;IAC/B2V,aAAa,CAAC9F,WAAW,CAACgG,YAAY,CAAC;IACvCH,OAAO,CAAC7F,WAAW,CAAC8F,aAAa,CAAC;IAElC,OAAOD,OAAO;EAChB;EAEA;EACQ9E,aAAaA,CAACvS,KAAa,EAAED,KAAa;IAChD,MAAM0X,GAAG,GAAG7I,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACzC4I,GAAG,CAAC3I,KAAK,CAACkC,YAAY,GAAG,MAAM;IAE/B,MAAM0G,YAAY,GAAG9I,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClD6I,YAAY,CAACrG,WAAW,GAAGrR,KAAK;IAChC0X,YAAY,CAAC5I,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCmH,YAAY,CAAC5I,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjC0H,YAAY,CAAC5I,KAAK,CAACkC,YAAY,GAAG,KAAK;IAEvC,MAAM2G,YAAY,GAAG/I,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClD8I,YAAY,CAACtG,WAAW,GAAGtR,KAAK;IAChC4X,YAAY,CAAC7I,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAEpCkH,GAAG,CAACjG,WAAW,CAACkG,YAAY,CAAC;IAC7BD,GAAG,CAACjG,WAAW,CAACmG,YAAY,CAAC;IAE7B,OAAOF,GAAG;EACZ;EAEA;EACA3O,gBAAgBA,CAAC8E,MAAc;IAC7B,IAAIA,MAAM,IAAIA,MAAM,CAAC9K,MAAM,IAAI8K,MAAM,CAAC9K,MAAM,CAAC7B,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAI2W,OAAO,GAAGhK,MAAM,CAAC9K,MAAM,CAAC,CAAC,CAAC,CAAC8U,OAAO,IAAIhK,MAAM,CAAC9K,MAAM,CAAC,CAAC,CAAC,CAAC+U,EAAE;MAE7D;MACA,IAAIC,QAAQ,GAAG,IAAI,CAACxN,YAAY;MAEhC;MACA,IAAI,CAACwN,QAAQ,EAAE;QACbA,QAAQ,GAAGlK,MAAM,CAACiK,EAAE;;MAGtBtL,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEsL,QAAQ,EAAE,cAAc,EAAEF,OAAO,CAAC;MAExF;MACA,IAAI,CAAC1N,MAAM,CAAC6N,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UACXF,QAAQ,EAAEA,QAAQ;UAClBF,OAAO,EAAEA;;OAEZ,CAAC;KACH,MAAM;MACLrL,OAAO,CAAC0L,KAAK,CAAC,sCAAsC,EAAErK,MAAM,CAAC;;EAEjE;EAEAtB,gBAAgBA,CAAA;IACd;IACA;IACA;EAAA;EAGFD,iBAAiBA,CAAA;IACf;IACA,MAAM6L,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACjC,MAAMC,mBAAmB,GAAG,CAAC,CAAC,CAAG;IAEjC,IAAI,CAAClO,cAAc,CAACmO,kBAAkB,CAACF,qBAAqB,CAAC,CAACG,SAAS,CAACC,SAAS,IAAG;MAClF,IAAI,CAACnO,kBAAkB,GAAGmO,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAACrO,cAAc,CAACmO,kBAAkB,CAACD,mBAAmB,CAAC,CAACE,SAAS,CAACC,SAAS,IAAG;MAChF,IAAI,CAAClO,gBAAgB,GAAGkO,SAAS;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAACnN,UAAU,CAACoN,GAAG,CAAC,mBAAmB,CAAC,EAAEC,YAAY,CACnDC,IAAI,CACHza,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC6B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B;QACA,IAAIA,KAAK,CAACkB,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACgJ,cAAc,CAACmO,kBAAkB,CAACF,qBAAqB,CAAC,CAACO,IAAI,CACvEta,GAAG,CAACma,SAAS,IAAIA,SAAS,CAACjC,MAAM,CAACqC,QAAQ,IACxCA,QAAQ,CAACpZ,IAAI,CAACqZ,WAAW,EAAE,CAACC,QAAQ,CAAC7Y,KAAK,CAAC4Y,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAAC3Z,IAAI,IAAI2Z,QAAQ,CAAC3Z,IAAI,CAAC4Z,WAAW,EAAE,CAACC,QAAQ,CAAC7Y,KAAK,CAAC4Y,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAC1O,cAAc,CAACmO,kBAAkB,CAACF,qBAAqB,CAAC;;;MAGxE,OAAO9Z,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAia,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACnO,kBAAkB,GAAGmO,SAAS;IACrC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACnN,UAAU,CAACoN,GAAG,CAAC,iBAAiB,CAAC,EAAEC,YAAY,CACjDC,IAAI,CACHza,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC6B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B;QACA,IAAIA,KAAK,CAACkB,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACgJ,cAAc,CAACmO,kBAAkB,CAACD,mBAAmB,CAAC,CAACM,IAAI,CACrEta,GAAG,CAACma,SAAS,IAAIA,SAAS,CAACjC,MAAM,CAACqC,QAAQ,IACxCA,QAAQ,CAACpZ,IAAI,CAACqZ,WAAW,EAAE,CAACC,QAAQ,CAAC7Y,KAAK,CAAC4Y,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAAC3Z,IAAI,IAAI2Z,QAAQ,CAAC3Z,IAAI,CAAC4Z,WAAW,EAAE,CAACC,QAAQ,CAAC7Y,KAAK,CAAC4Y,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAC1O,cAAc,CAACmO,kBAAkB,CAACD,mBAAmB,CAAC;;;MAGtE,OAAO/Z,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAia,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAAClO,gBAAgB,GAAGkO,SAAS;IACnC,CAAC,CAAC;EACN;EAEAO,eAAeA,CAACH,QAA+B;IAC7C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAII,WAAW,GAAGJ,QAAQ,CAACpZ,IAAI;IAC/B,IAAIoZ,QAAQ,CAAC3Z,IAAI,EAAE;MACjB+Z,WAAW,IAAI,KAAKJ,QAAQ,CAAC3Z,IAAI,GAAG;;IAEtC,IAAI2Z,QAAQ,CAACnZ,IAAI,KAAKjB,YAAY,CAACya,OAAO,IAAIL,QAAQ,CAAC1Z,IAAI,EAAE;MAC3D8Z,WAAW,IAAI,MAAMJ,QAAQ,CAAC1Z,IAAI,EAAE;;IAEtC,OAAO8Z,WAAW;EACpB;EAEArY,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC0K,UAAU,CAAC6N,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC9N,UAAU,CAAC;MAC1C;;IAGF,IAAI,CAAC1B,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC9I,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC0J,WAAW,GAAG,IAAI;IAEvB,MAAM6O,SAAS,GAAG,IAAI,CAAC/N,UAAU,CAACpL,KAAK;IAEvC;IACA,MAAMoZ,OAAO,GAAuB;MAClCC,WAAW,EAAEF,SAAS,CAAC7N,WAAW;MAClCgO,YAAY,EAAEH,SAAS,CAAC3N,YAAY;MACpC+N,OAAO,EAAEJ,SAAS,CAACxN,aAAa;MAChC6N,kBAAkB,EAAE,CAClB;QACE1B,EAAE,EAAEqB,SAAS,CAAC1N,iBAAiB,EAAEqM,EAAE,IAAI,EAAE;QACzCtY,IAAI,EAAE,CAAC,CAAC;OACT,CACF;;MACDia,gBAAgB,EAAE,CAChB;QACE3B,EAAE,EAAEqB,SAAS,CAACzN,eAAe,EAAEoM,EAAE,IAAI,EAAE;QACvCtY,IAAI,EAAE,CAAC,CAAC;OACT,CACF;;MACDka,UAAU,EAAE,CACV;QACEla,IAAI,EAAE2Z,SAAS,CAAChU,aAAa;QAC7BwU,KAAK,EAAER,SAAS,CAAC/T;OAClB,CACF;MACDwU,qBAAqB,EAAET,SAAS,CAACrN,OAAO;MACxC+N,oBAAoB,EAAE;QACpBC,oBAAoB,EAAE;UACpB1N,sBAAsB,EAAE+M,SAAS,CAAC/M;;OAErC;MACDJ,sBAAsB,EAAEmN,SAAS,CAACnN,sBAAsB;MACxDC,wBAAwB,EAAEkN,SAAS,CAAClN,wBAAwB;MAC5DC,6BAA6B,EAAEiN,SAAS,CAACjN,6BAA6B;MACtEC,mBAAmB,EAAEgN,SAAS,CAAChN,mBAAmB;MAClDvB,aAAa,EAAE,CAACuO,SAAS,CAAC3W,WAAW,CAAC;MACtCuX,OAAO,EAAEZ,SAAS,CAACpN,OAAO;MAC1BiO,QAAQ,EAAEb,SAAS,CAAC3T;KACrB;IAED,IAAI,CAAC0E,cAAc,CAAC+P,WAAW,CAACb,OAAO,CAAC,CACrCd,SAAS,CAAC;MACT4B,IAAI,EAAGC,QAA6B,IAAI;QACtC,IAAI,CAACzQ,SAAS,GAAG,KAAK;QACtB,IAAIyQ,QAAQ,CAACnJ,MAAM,CAACoJ,OAAO,EAAE;UAC3B,IAAI,CAAC3Q,aAAa,GAAG0Q,QAAQ,CAACrJ,IAAI,CAAC/D,OAAO;UAE1C;UACA,IAAI,CAACtL,WAAW,CAAC,IAAI,CAACX,aAAa,CAAC;UAEpC;UACA0L,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4N,IAAI,CAACC,SAAS,CAACH,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEpE;UACA,IAAIA,QAAQ,CAACrJ,IAAI,IAAIqJ,QAAQ,CAACrJ,IAAI,CAAC/D,OAAO,IAAIoN,QAAQ,CAACrJ,IAAI,CAAC/D,OAAO,CAAC7L,MAAM,GAAG,CAAC,EAAE;YAC9EsL,OAAO,CAACnB,KAAK,CAAC,uBAAuB,CAAC;YACtCmB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE0N,QAAQ,CAACrJ,IAAI,CAAC/D,OAAO,CAAC7L,MAAM,CAAC;YAE3D;YACA,MAAMqZ,iBAAiB,GAAGJ,QAAQ,CAACrJ,IAAI,CAAC/D,OAAO,CAACuJ,MAAM,CAACkE,CAAC,IAAIA,CAAC,CAACzX,MAAM,IAAIyX,CAAC,CAACzX,MAAM,CAAC7B,MAAM,GAAG,CAAC,CAAC;YAC5FsL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8N,iBAAiB,CAACrZ,MAAM,CAAC;YAE7D;YACA,MAAMuZ,kBAAkB,GAAGF,iBAAiB,CAACG,OAAO,CAACF,CAAC,IAAIA,CAAC,CAACzX,MAAM,CAAC3E,GAAG,CAACuc,CAAC,IACtEA,CAAC,CAAC3M,YAAY,KAAKC,SAAS,GAAG0M,CAAC,CAAC3M,YAAY,GAAI2M,CAAC,CAACzM,QAAQ,GAAGyM,CAAC,CAACzM,QAAQ,CAACC,kBAAkB,GAAG,CAAE,CACjG,CAAC;YACF3B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgO,kBAAkB,CAAC;YAEvD;YACA,MAAMG,kBAAkB,GAAGH,kBAAkB,CAAC9L,MAAM,CAAC,CAACkM,GAAG,EAAEC,GAAG,KAAI;cAChE,IAAIA,GAAG,KAAK7M,SAAS,EAAE;gBACrB4M,GAAG,CAACC,GAAG,CAAC,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEhC,OAAOD,GAAG;YACZ,CAAC,EAAE,EAA4B,CAAC;YAChCrO,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmO,kBAAkB,CAAC;YAEvD;YACA,MAAMG,iBAAiB,GAAGR,iBAAiB,CAACjE,MAAM,CAACkE,CAAC,IAClDA,CAAC,CAACzX,MAAM,CAACiY,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC/V,cAAc,IAAI+V,CAAC,CAAC/V,cAAc,CAACC,UAAU,KAAK,IAAI,CAAC,CAC7E;YACD2H,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEsO,iBAAiB,CAAC7Z,MAAM,CAAC;YAE5DsL,OAAO,CAACyO,QAAQ,EAAE;;UAGpB;UACA,IAAId,QAAQ,CAACrJ,IAAI,IAAIqJ,QAAQ,CAACrJ,IAAI,CAACiH,QAAQ,EAAE;YAC3C,IAAI,CAACxN,YAAY,GAAG4P,QAAQ,CAACrJ,IAAI,CAACiH,QAAQ;YAC1CvL,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAClC,YAAY,CAAC;;UAErE;UAAA,KACK,IAAI4P,QAAQ,CAACnJ,MAAM,IAAImJ,QAAQ,CAACnJ,MAAM,CAACkK,SAAS,EAAE;YACrD,IAAI,CAAC3Q,YAAY,GAAG4P,QAAQ,CAACnJ,MAAM,CAACkK,SAAS;YAC7C1O,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAClC,YAAY,CAAC;;UAExE;UAAA,KACK,IAAI4P,QAAQ,CAACrJ,IAAI,IAAIqJ,QAAQ,CAACrJ,IAAI,CAAC/D,OAAO,IAAIoN,QAAQ,CAACrJ,IAAI,CAAC/D,OAAO,CAAC7L,MAAM,GAAG,CAAC,IAAIiZ,QAAQ,CAACrJ,IAAI,CAAC/D,OAAO,CAAC,CAAC,CAAC,CAAC+K,EAAE,EAAE;YAClH,IAAI,CAACvN,YAAY,GAAG4P,QAAQ,CAACrJ,IAAI,CAAC/D,OAAO,CAAC,CAAC,CAAC,CAAC+K,EAAE;YAC/CtL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAClC,YAAY,CAAC;WAChE,MAAM;YACLiC,OAAO,CAAC0L,KAAK,CAAC,qCAAqC,CAAC;YACpD1L,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0O,MAAM,CAACC,IAAI,CAACjB,QAAQ,CAAC,CAAC;YACzD,IAAIA,QAAQ,CAACrJ,IAAI,EAAEtE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE0O,MAAM,CAACC,IAAI,CAACjB,QAAQ,CAACrJ,IAAI,CAAC,CAAC;YAC7E,IAAIqJ,QAAQ,CAACnJ,MAAM,EAAExE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE0O,MAAM,CAACC,IAAI,CAACjB,QAAQ,CAACnJ,MAAM,CAAC,CAAC;;SAEtF,MAAM;UACL,IAAI,CAACpQ,YAAY,GAAG,sDAAsD;UAC1E,IAAIuZ,QAAQ,CAACnJ,MAAM,CAACqK,QAAQ,IAAIlB,QAAQ,CAACnJ,MAAM,CAACqK,QAAQ,CAACna,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAACN,YAAY,GAAGuZ,QAAQ,CAACnJ,MAAM,CAACqK,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACDpD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACxO,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC9I,YAAY,GAAG,wDAAwD;QAC5E4L,OAAO,CAAC0L,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEA;EACAgB,oBAAoBA,CAACqC,SAAoB;IACvCJ,MAAM,CAACK,MAAM,CAACD,SAAS,CAACE,QAAQ,CAAC,CAACtH,OAAO,CAACuH,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAY3d,SAAS,EAAE;QAChC,IAAI,CAACmb,oBAAoB,CAACwC,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA;EACA/U,cAAcA,CAACiV,OAAe;IAC5B,MAAMC,KAAK,GAAGrN,IAAI,CAAC+G,KAAK,CAACqG,OAAO,GAAG,EAAE,CAAC;IACtC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,KAAK,KAAKC,IAAI,KAAK;EAC/B;EAEA;EACAtV,UAAUA,CAACuV,UAAkB;IAC3B,MAAMtV,IAAI,GAAG,IAAIwE,IAAI,CAAC8Q,UAAU,CAAC;IACjC,OAAOtV,IAAI,CAACkM,kBAAkB,CAAC,OAAO,EAAE;MACtCqJ,OAAO,EAAE,OAAO;MAChBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACd7I,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACApK,WAAWA,CAAC2E,MAAc;IACxB,IAAI,CAACA,MAAM,CAAC9K,MAAM,IAAI8K,MAAM,CAAC9K,MAAM,CAAC7B,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd,MAAMib,QAAQ,GAAGtO,MAAM,CAAC9K,MAAM,CAAC4L,MAAM,CAAC,CAAC/C,GAAG,EAAEmC,KAAK,KAC/CA,KAAK,CAACzI,KAAK,CAACC,MAAM,GAAGqG,GAAG,CAACtG,KAAK,CAACC,MAAM,GAAGwI,KAAK,GAAGnC,GAAG,EAAEiC,MAAM,CAAC9K,MAAM,CAAC,CAAC,CAAC,CAAC;IAExE,OAAOoZ,QAAQ,CAAC7W,KAAK,CAAC8W,eAAe,IAAI,GAAGD,QAAQ,CAAC7W,KAAK,CAACC,MAAM,IAAI4W,QAAQ,CAAC7W,KAAK,CAACE,QAAQ,EAAE;EAChG;EAEA;EACA5C,iBAAiBA,CAACiL,MAAc;IAC9B;IACA,IAAI,CAACA,MAAM,CAAC9K,MAAM,IAAI8K,MAAM,CAAC9K,MAAM,CAAC7B,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd;IACA,MAAM6M,KAAK,GAAGF,MAAM,CAAC9K,MAAM,CAAC,CAAC,CAAC;IAC9B,MAAMiT,iBAAiB,GAAGjI,KAAK,CAACC,YAAY,KAAKC,SAAS,GAAGF,KAAK,CAACC,YAAY,GACrDD,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IACjF,OAAO6H,iBAAiB,GAAG,CAAC;EAC9B;EAEA;EACAqG,yBAAyBA,CAAA;IACvB,MAAMC,YAAY,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,CAACpS,cAAc,CAACmO,kBAAkB,CAACiE,YAAY,CAAC,CAAChE,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACnO,kBAAkB,GAAGmO,SAAS;MACnC;MACA,MAAMgE,KAAK,GAAG1N,QAAQ,CAAC2N,cAAc,CAAC,mBAAmB,CAAqB;MAC9E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAC,uBAAuBA,CAAA;IACrB,MAAMN,YAAY,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,CAACpS,cAAc,CAACmO,kBAAkB,CAACiE,YAAY,CAAC,CAAChE,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAAClO,gBAAgB,GAAGkO,SAAS;MACjC;MACA,MAAMgE,KAAK,GAAG1N,QAAQ,CAAC2N,cAAc,CAAC,iBAAiB,CAAqB;MAC5E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAE,aAAaA,CAAA;IACX,MAAMpR,iBAAiB,GAAG,IAAI,CAACL,UAAU,CAACoN,GAAG,CAAC,mBAAmB,CAAC,EAAExY,KAAK;IACzE,MAAM0L,eAAe,GAAG,IAAI,CAACN,UAAU,CAACoN,GAAG,CAAC,iBAAiB,CAAC,EAAExY,KAAK;IAErE,IAAI,CAACoL,UAAU,CAAC0R,UAAU,CAAC;MACzBrR,iBAAiB,EAAEC,eAAe;MAClCA,eAAe,EAAED;KAClB,CAAC;EACJ;EAEA;EACA3I,oBAAoBA,CAACiZ,UAAkB;IACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B;IACA,MAAMtV,IAAI,GAAG,IAAIwE,IAAI,CAAC8Q,UAAU,CAAC;IACjC,OAAOtV,IAAI,CAACyP,cAAc,EAAE;EAC9B;EAEA;EACA6G,kBAAkBA,CAACxG,WAAmB;IACpC,QAAQA,WAAW;MACjB,KAAK,CAAC;QAAE,OAAO,eAAe;MAC9B,KAAK,CAAC;QAAE,OAAO,iBAAiB;MAChC,KAAK,CAAC;QAAE,OAAO,cAAc;MAC7B;QAAS,OAAO,SAAS;;EAE7B;EAEA;EACA3S,mBAAmBA,CAACC,mBAA0B,EAAErE,IAAY;IAC1D,IAAI,CAACqE,mBAAmB,IAAI,CAACmZ,KAAK,CAACC,OAAO,CAACpZ,mBAAmB,CAAC,EAAE;MAC/D,OAAO,EAAE;;IAEX,OAAOA,mBAAmB,CAACyS,MAAM,CAACI,OAAO,IAAIA,OAAO,CAACH,WAAW,KAAK/W,IAAI,CAAC;EAC5E;EAEA;EACAyF,oBAAoBA,CAACE,aAAqB;IACxC,QAAQA,aAAa;MACnB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,WAAW;;EAE/B;EAEA;EACAY,oBAAoBA,CAACmX,cAAmB,EAAEC,WAAgB;IACxD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAAC/W,OAAO,IAAI,CAAC+W,cAAc,CAAC/W,OAAO,CAACM,IAAI,IAC1E,CAAC0W,WAAW,IAAI,CAACA,WAAW,CAAC7W,SAAS,IAAI,CAAC6W,WAAW,CAAC7W,SAAS,CAACG,IAAI,EAAE;MACzE,OAAO,SAAS;;IAGlB,MAAMqN,WAAW,GAAG,IAAI7I,IAAI,CAACiS,cAAc,CAAC/W,OAAO,CAACM,IAAI,CAAC,CAAC2O,OAAO,EAAE;IACnE,MAAMjC,aAAa,GAAG,IAAIlI,IAAI,CAACkS,WAAW,CAAC7W,SAAS,CAACG,IAAI,CAAC,CAAC2O,OAAO,EAAE;IACpE,MAAMgI,MAAM,GAAGjK,aAAa,GAAGW,WAAW;IAC1C,MAAMuJ,QAAQ,GAAG7O,IAAI,CAAC+G,KAAK,CAAC6H,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIC,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,KAAK;KACxB,MAAM;MACL,MAAMxB,KAAK,GAAGrN,IAAI,CAAC+G,KAAK,CAAC8H,QAAQ,GAAG,EAAE,CAAC;MACvC,MAAMvB,IAAI,GAAGuB,QAAQ,GAAG,EAAE;MAC1B,OAAO,GAAGxB,KAAK,KAAKC,IAAI,KAAK;;EAEjC;;;uBAvwCW/R,oBAAoB,EAAAtL,EAAA,CAAA6e,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/e,EAAA,CAAA6e,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAjf,EAAA,CAAA6e,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApB7T,oBAAoB;MAAA8T,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjC1f,EAAA,CAAAC,cAAA,aAAoC;UAGPD,EAAA,CAAAE,MAAA,+BAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpDH,EAAA,CAAAC,cAAA,WAAyB;UAAAD,EAAA,CAAAE,MAAA,2DAAoD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEnFH,EAAA,CAAAC,cAAA,aAAiC;UAC/BD,EAAA,CAAAY,SAAA,aAAuE;UACzEZ,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,aAA4B;UAKpBD,EAAA,CAAAY,SAAA,aAAgD;UAChDZ,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI7CH,EAAA,CAAAC,cAAA,eAAgC;UAC1BD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,uCAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGxCH,EAAA,CAAAC,cAAA,gBAA2E;UAA5CD,EAAA,CAAA0B,UAAA,sBAAAke,wDAAA;YAAA,OAAYD,GAAA,CAAA1d,QAAA,EAAU;UAAA,EAAC;UAEpDjC,EAAA,CAAAC,cAAA,eAAyB;UAEvBD,EAAA,CAAAY,SAAA,iBAA6D;UAI7DZ,EAAA,CAAAC,cAAA,eAA8B;UAGKD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,aAAsC;UACtCZ,EAAA,CAAAC,cAAA,iBAQC;UADCD,EAAA,CAAA0B,UAAA,mBAAAme,sDAAA;YAAA,OAASF,GAAA,CAAA/B,yBAAA,EAA2B;UAAA,EAAC;UAPvC5d,EAAA,CAAAG,YAAA,EAQC;UAEHH,EAAA,CAAAC,cAAA,gCAA8F;UAC5FD,EAAA,CAAAS,UAAA,KAAAqf,2CAAA,2BAuBa;UACf9f,EAAA,CAAAG,YAAA,EAAmB;UACnBH,EAAA,CAAAS,UAAA,KAAAsf,oCAAA,kBAEM;UACR/f,EAAA,CAAAG,YAAA,EAAM;UAKNH,EAAA,CAAAC,cAAA,eAAmC;UACgBD,EAAA,CAAA0B,UAAA,mBAAAse,uDAAA;YAAA,OAASL,GAAA,CAAAvB,aAAA,EAAe;UAAA,EAAC;UACxEpe,EAAA,CAAAY,SAAA,aAAmC;UACrCZ,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAAwB;UACOD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvCH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,aAAoC;UACpCZ,EAAA,CAAAC,cAAA,iBAQC;UADCD,EAAA,CAAA0B,UAAA,mBAAAue,sDAAA;YAAA,OAASN,GAAA,CAAAxB,uBAAA,EAAyB;UAAA,EAAC;UAPrCne,EAAA,CAAAG,YAAA,EAQC;UAEHH,EAAA,CAAAC,cAAA,gCAA4F;UAC1FD,EAAA,CAAAS,UAAA,KAAAyf,2CAAA,2BAuBa;UACflgB,EAAA,CAAAG,YAAA,EAAmB;UACnBH,EAAA,CAAAS,UAAA,KAAA0f,oCAAA,kBAEM;UACRngB,EAAA,CAAAG,YAAA,EAAM;UAMRH,EAAA,CAAAC,cAAA,eAA8B;UAGCD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvCH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,aAAmC;UAQrCZ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAS,UAAA,KAAA2f,oCAAA,kBAEM;UACRpgB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAwB;UACMD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9CH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,aAAmC;UASrCZ,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAwB;UACKD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,aAA4B;UAC5BZ,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAS,UAAA,KAAA4f,uCAAA,qBAA0F;UAC5FrgB,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,eAAwB;UACGD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,aAA4B;UAC5BZ,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAS,UAAA,KAAA6f,uCAAA,qBAA8G;UAChHtgB,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,eAAuC;UAEnCD,EAAA,CAAAY,SAAA,iBAKC;UACDZ,EAAA,CAAAC,cAAA,iBAA0C;UACxCD,EAAA,CAAAY,SAAA,gBAAkC;UAClCZ,EAAA,CAAAC,cAAA,gBAAkC;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAMpEH,EAAA,CAAAC,cAAA,eAAqC;UAMjCD,EAAA,CAAAS,UAAA,KAAA8f,kCAAA,gBAAgD;UAChDvgB,EAAA,CAAAS,UAAA,KAAA+f,qCAAA,mBAAsC;UACtCxgB,EAAA,CAAAS,UAAA,KAAAggB,oCAAA,kBAEM;UACRzgB,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAAwC;UAGlCD,EAAA,CAAAY,SAAA,aAA0B;UAACZ,EAAA,CAAAE,MAAA,0BAC7B;UAAAF,EAAA,CAAAG,YAAA,EAAU;UACVH,EAAA,CAAAC,cAAA,eAA8B;UAIHD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrCH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,cAA+B;UAC/BZ,EAAA,CAAAC,cAAA,mBAIC;UACuBD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,mBAAsB;UAAAD,EAAA,CAAAE,MAAA,sBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM7CH,EAAA,CAAAC,cAAA,gBAAwB;UACAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAY,SAAA,cAAsC;UACtCZ,EAAA,CAAAC,cAAA,mBAIC;UACqBD,EAAA,CAAAE,MAAA,sBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM7CH,EAAA,CAAAC,cAAA,gBAAwB;UACcD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3DH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAY,SAAA,cAA+B;UAC/BZ,EAAA,CAAAC,cAAA,mBAIC;UACqBD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClDH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOpDH,EAAA,CAAAC,cAAA,gBAAuC;UAGjCD,EAAA,CAAAY,SAAA,kBAKC;UACDZ,EAAA,CAAAC,cAAA,kBAAyD;UACvDD,EAAA,CAAAY,SAAA,iBAAkC;UAClCZ,EAAA,CAAAC,cAAA,iBAAkC;UAAAD,EAAA,CAAAE,MAAA,iCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKvEH,EAAA,CAAAC,cAAA,gBAAuC;UAEnCD,EAAA,CAAAY,SAAA,kBAKC;UACDZ,EAAA,CAAAC,cAAA,kBAA2D;UACzDD,EAAA,CAAAY,SAAA,iBAAkC;UAClCZ,EAAA,CAAAC,cAAA,iBAAkC;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK1EH,EAAA,CAAAC,cAAA,gBAAuC;UAEnCD,EAAA,CAAAY,SAAA,kBAKC;UACDZ,EAAA,CAAAC,cAAA,kBAAgE;UAC9DD,EAAA,CAAAY,SAAA,iBAAkC;UAClCZ,EAAA,CAAAC,cAAA,iBAAkC;UAAAD,EAAA,CAAAE,MAAA,0CAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKhFH,EAAA,CAAAC,cAAA,gBAAuC;UAEnCD,EAAA,CAAAY,SAAA,kBAKC;UACDZ,EAAA,CAAAC,cAAA,kBAAsD;UACpDD,EAAA,CAAAY,SAAA,iBAAkC;UAClCZ,EAAA,CAAAC,cAAA,iBAAkC;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAYhFH,EAAA,CAAAS,UAAA,MAAAigB,qCAAA,kBAmWM;UACR1gB,EAAA,CAAAG,YAAA,EAAM;;;;;;;;UAvrBIH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAa,UAAA,cAAA8e,GAAA,CAAAhT,UAAA,CAAwB;UAmBlB3M,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAAa,UAAA,oBAAA8f,GAAA,CAAiC;UAKc3gB,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAa,UAAA,gBAAA8e,GAAA,CAAAtF,eAAA,CAAAuG,IAAA,CAAAjB,GAAA,EAA0C;UAC1D3f,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAa,UAAA,YAAA8e,GAAA,CAAAhU,kBAAA,CAAqB;UAyBlD3L,EAAA,CAAAI,SAAA,GAAkG;UAAlGJ,EAAA,CAAAa,UAAA,WAAAggB,OAAA,GAAAlB,GAAA,CAAAhT,UAAA,CAAAoN,GAAA,wCAAA8G,OAAA,CAAArG,OAAA,OAAAqG,OAAA,GAAAlB,GAAA,CAAAhT,UAAA,CAAAoN,GAAA,wCAAA8G,OAAA,CAAAC,OAAA,EAAkG;UAwBpG9gB,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAa,UAAA,oBAAAkgB,GAAA,CAA+B;UAKc/gB,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAa,UAAA,gBAAA8e,GAAA,CAAAtF,eAAA,CAAAuG,IAAA,CAAAjB,GAAA,EAA0C;UACxD3f,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAa,UAAA,YAAA8e,GAAA,CAAA/T,gBAAA,CAAmB;UAyBhD5L,EAAA,CAAAI,SAAA,GAA8F;UAA9FJ,EAAA,CAAAa,UAAA,WAAAmgB,OAAA,GAAArB,GAAA,CAAAhT,UAAA,CAAAoN,GAAA,sCAAAiH,OAAA,CAAAxG,OAAA,OAAAwG,OAAA,GAAArB,GAAA,CAAAhT,UAAA,CAAAoN,GAAA,sCAAAiH,OAAA,CAAAF,OAAA,EAA8F;UAmBhG9gB,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAa,UAAA,QAAA8e,GAAA,CAAApT,OAAA,CAAe;UAIbvM,EAAA,CAAAI,SAAA,GAA0F;UAA1FJ,EAAA,CAAAa,UAAA,WAAAogB,QAAA,GAAAtB,GAAA,CAAAhT,UAAA,CAAAoN,GAAA,oCAAAkH,QAAA,CAAAzG,OAAA,OAAAyG,QAAA,GAAAtB,GAAA,CAAAhT,UAAA,CAAAoN,GAAA,oCAAAkH,QAAA,CAAAH,OAAA,EAA0F;UA+BnE9gB,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAa,UAAA,YAAA8e,GAAA,CAAA5T,cAAA,CAAiB;UAeV/L,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAa,UAAA,YAAA8e,GAAA,CAAAxT,aAAA,CAAgB;UA0BlDnM,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAa,UAAA,aAAA8e,GAAA,CAAAhT,UAAA,CAAA6N,OAAA,IAAAmF,GAAA,CAAA1U,SAAA,CAA4C;UAElBjL,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAa,UAAA,UAAA8e,GAAA,CAAA1U,SAAA,CAAgB;UACnCjL,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAa,UAAA,UAAA8e,GAAA,CAAA1U,SAAA,CAAgB;UACjBjL,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAa,UAAA,SAAA8e,GAAA,CAAA1U,SAAA,CAAe;UA0DPjL,EAAA,CAAAI,SAAA,IAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UA2EEb,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAa,UAAA,SAAA8e,GAAA,CAAA9T,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}