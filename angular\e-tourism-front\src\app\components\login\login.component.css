.login-page {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--ocean-blue) 0%, var(--primary-color) 60%, var(--sky-blue) 100%);
  z-index: -1;
}

.login-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.shape {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.shape-1 {
  width: 600px;
  height: 600px;
  top: -300px;
  right: -200px;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 70%, transparent 100%);
  animation: float 20s infinite alternate ease-in-out;
}

.shape-2 {
  width: 400px;
  height: 400px;
  bottom: -200px;
  left: -100px;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 70%, transparent 100%);
  animation: float 25s infinite alternate-reverse ease-in-out;
}

.shape-3 {
  width: 300px;
  height: 300px;
  top: 20%;
  right: 10%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 70%, transparent 100%);
  animation: pulse 15s infinite alternate ease-in-out;
}

.shape-4 {
  width: 200px;
  height: 200px;
  bottom: 15%;
  right: 20%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 70%, transparent 100%);
  animation: float 18s infinite alternate-reverse ease-in-out;
}

.shape-5 {
  width: 150px;
  height: 150px;
  top: 30%;
  left: 15%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 70%, transparent 100%);
  animation: pulse 12s infinite alternate ease-in-out;
}

.login-illustration {
  position: absolute;
  bottom: 5%;
  right: 5%;
  width: 40%;
  max-width: 500px;
  opacity: 0.9;
  animation: float 6s infinite alternate ease-in-out;
  display: none; /* Hidden on mobile by default */
}

.login-illustration img {
  width: 100%;
  height: auto;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  100% {
    transform: translateY(20px) rotate(3deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.2);
    opacity: 0.2;
  }
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: var(--spacing-md);
  z-index: 1;
}

.login-card {
  width: 100%;
  max-width: 450px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-large);
  box-shadow: var(--elevation-4), 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: var(--spacing-xl);
  overflow: hidden;
  animation: cardAppear 0.8s var(--transition-bounce) forwards;
}

@keyframes cardAppear {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.login-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50%;
  margin-bottom: var(--spacing-md);
  box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.3);
  position: relative;
  overflow: hidden;
}

.logo-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

.logo-icon i {
  font-size: 2.5rem;
  color: white;
}

.login-logo h1 {
  font-size: 1.75rem;
  font-weight: 600;
  background: linear-gradient(135deg, var(--primary-dark), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.login-header h2 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-size: 1.75rem;
  font-weight: 600;
}

.login-header p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: color var(--transition-fast);
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-icon i {
  position: absolute;
  left: 16px;
  color: var(--primary-color);
  font-size: 1rem;
  transition: all var(--transition-fast);
}

.form-control {
  width: 100%;
  padding: 14px 14px 14px 45px;
  border: 1px solid var(--divider-color);
  border-radius: var(--border-radius-medium);
  font-size: 1rem;
  transition: all var(--transition-fast);
  background-color: var(--surface-color);
  color: var(--text-primary);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

input[type="password"].form-control,
input[type="text"].form-control[name="password"] {
  padding-right: 45px; /* Espace supplémentaire pour l'icône d'œil */
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
}

.form-control:focus + .input-focus-border {
  transform: scaleX(1);
}

.input-focus-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-medium);
}

.form-control::placeholder {
  color: var(--text-hint);
}

.input-with-icon:focus-within i {
  color: var(--accent-color);
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  font-size: 1rem;
  transition: all var(--transition-fast);
  border-radius: 50%;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  width: 36px;
}

.password-toggle:hover {
  color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.password-toggle:active {
  transform: translateY(-50%) scale(0.95);
}

.password-toggle i {
  transition: all 0.2s ease;
}

.password-toggle:hover i {
  transform: scale(1.1);
}

.error-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--error-color);
  background-color: rgba(244, 67, 54, 0.1);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-medium);
  margin-bottom: var(--spacing-sm);
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
  40%, 60% { transform: translate3d(3px, 0, 0); }
}

.error-message i {
  font-size: 1.1rem;
}

.login-button {
  width: 100%;
  height: 50px;
  font-size: 1rem;
  font-weight: 600;
  margin-top: var(--spacing-md);
  position: relative;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  border: none;
  border-radius: var(--border-radius-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.3);
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s;
}

.login-button:hover:not(:disabled)::before {
  left: 100%;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(var(--primary-color-rgb), 0.4);
}

.login-button:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(var(--primary-color-rgb), 0.3);
}

.login-button:disabled {
  background: linear-gradient(135deg, #ccc, #ddd);
  cursor: not-allowed;
  box-shadow: none;
}

.spinner-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.login-footer {
  margin-top: var(--spacing-xl);
  text-align: center;
}

.footer-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
}

.decoration-line {
  height: 1px;
  width: 60px;
  background: linear-gradient(to right, transparent, var(--divider-color));
}

.decoration-line:last-child {
  background: linear-gradient(to left, transparent, var(--divider-color));
}

.footer-decoration i {
  color: var(--primary-color);
  font-size: 1.2rem;
  margin: 0 var(--spacing-sm);
}

.login-footer p {
  color: var(--text-hint);
  font-size: 0.75rem;
  margin: 0;
}

/* Responsive styles */
@media (min-width: 768px) {
  .login-illustration {
    display: block;
  }

  .login-container {
    margin-left: -10%;
  }
}

@media (max-width: 767px) {
  .login-card {
    max-width: 90%;
  }
}

@media (max-width: 500px) {
  .login-card {
    padding: var(--spacing-lg);
  }

  .logo-icon {
    width: 70px;
    height: 70px;
  }

  .logo-icon i {
    font-size: 2rem;
  }

  .login-logo h1 {
    font-size: 1.5rem;
  }

  .login-header h2 {
    font-size: 1.5rem;
  }

  .form-control {
    padding: 12px 12px 12px 40px;
  }
}

/* Animation pour le body */
:host-context(body.login-page-active) {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
