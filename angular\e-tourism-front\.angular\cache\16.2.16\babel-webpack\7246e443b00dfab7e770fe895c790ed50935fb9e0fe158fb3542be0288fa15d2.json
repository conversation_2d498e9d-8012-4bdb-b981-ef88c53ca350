{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/product.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/autocomplete\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/datepicker\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nfunction SearchPriceComponent_mat_option_33_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", location_r11.city, \" \");\n  }\n}\nfunction SearchPriceComponent_mat_option_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, SearchPriceComponent_mat_option_33_span_2_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r11);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", location_r11.name, \" \", location_r11.code ? \"(\" + location_r11.code + \")\" : \"\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r11.type === 5 && location_r11.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_56_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", location_r14.city, \" \");\n  }\n}\nfunction SearchPriceComponent_mat_option_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, SearchPriceComponent_mat_option_56_span_2_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r14);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", location_r14.name, \" \", location_r14.code ? \"(\" + location_r14.code + \")\" : \"\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r14.type === 5 && location_r14.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r17.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r17.label, \" \");\n  }\n}\nfunction SearchPriceComponent_mat_option_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", flightClass_r18.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flightClass_r18.label, \" \");\n  }\n}\nfunction SearchPriceComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"mat-spinner\", 57);\n    i0.ɵɵelementStart(2, \"p\", 58);\n    i0.ɵɵtext(3, \"Recherche des meilleurs vols...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵelement(2, \"i\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.errorMessage);\n  }\n}\nfunction SearchPriceComponent_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"i\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Aucun vol trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Essayez de modifier vos crit\\u00E8res de recherche\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1, \" escale(s) \");\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"i\", 67);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Vol direct\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r20.offers[0] == null ? null : flight_r20.offers[0].flightBrandInfo == null ? null : flight_r20.offers[0].flightBrandInfo.name);\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"div\", 72)(3, \"div\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 74)(6, \"span\", 75);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 76);\n    i0.ɵɵelementStart(9, \"span\", 77);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 78)(12, \"div\", 79);\n    i0.ɵɵtext(13, \"\\u00E0 partir de\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 80);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 81)(17, \"div\", 82)(18, \"div\", 83)(19, \"div\", 84)(20, \"div\", 85);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 86);\n    i0.ɵɵelement(24, \"i\", 87);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 88);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 89);\n    i0.ɵɵelement(30, \"div\", 90);\n    i0.ɵɵelementStart(31, \"div\", 91);\n    i0.ɵɵtemplate(32, SearchPriceComponent_div_102_div_5_div_32_Template, 2, 1, \"div\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"div\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 93)(35, \"div\", 94);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 95);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 96)(40, \"div\", 97);\n    i0.ɵɵelement(41, \"i\", 98);\n    i0.ɵɵelementStart(42, \"span\");\n    i0.ɵɵtext(43, \"Bagages inclus\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(44, SearchPriceComponent_div_102_div_5_div_44_Template, 4, 0, \"div\", 99);\n    i0.ɵɵtemplate(45, SearchPriceComponent_div_102_div_5_div_45_Template, 4, 1, \"div\", 99);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 100)(47, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_102_div_5_Template_button_click_47_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const flight_r20 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.showFlightDetails(flight_r20));\n    });\n    i0.ɵɵelement(48, \"i\", 102);\n    i0.ɵɵtext(49, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_102_div_5_Template_button_click_50_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const flight_r20 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.selectThisFlight(flight_r20));\n    });\n    i0.ɵɵelement(51, \"i\", 104);\n    i0.ɵɵtext(52, \" S\\u00E9lectionner \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const flight_r20 = ctx.$implicit;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(flight_r20.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((flight_r20.items[0] == null ? null : flight_r20.items[0].segments[0] == null ? null : flight_r20.items[0].segments[0].departure == null ? null : flight_r20.items[0].segments[0].departure.city == null ? null : flight_r20.items[0].segments[0].departure.city.name) || \"D\\u00E9part\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((flight_r20.items[0] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival.city == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival.city.name) || \"Arriv\\u00E9e\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r19.getMinPrice(flight_r20));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(22, 14, flight_r20.items[0] == null ? null : flight_r20.items[0].segments[0] == null ? null : flight_r20.items[0].segments[0].departure == null ? null : flight_r20.items[0].segments[0].departure.date, \"HH:mm\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.formatDuration((flight_r20.items[0] == null ? null : flight_r20.items[0].duration) || 0), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(28, 17, flight_r20.items[0] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival.date, \"HH:mm\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"has-stops\", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) > 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments[0] == null ? null : flight_r20.items[0].segments[0].departure == null ? null : flight_r20.items[0].segments[0].departure.airport == null ? null : flight_r20.items[0].segments[0].departure.airport.code) || \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival.airport == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival.airport.code) || \"\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r20.offers[0] == null ? null : flight_r20.offers[0].flightBrandInfo == null ? null : flight_r20.offers[0].flightBrandInfo.name);\n  }\n}\nfunction SearchPriceComponent_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"h3\", 66);\n    i0.ɵɵelement(2, \"i\", 67);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 68);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_102_div_5_Template, 53, 20, \"div\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Vols disponibles (\", ctx_r10.searchResults.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.searchResults);\n  }\n}\nexport class SearchPriceComponent {\n  constructor(fb, productService, router) {\n    this.fb = fb;\n    this.productService = productService;\n    this.router = router;\n    this.departureLocations = [];\n    this.arrivalLocations = [];\n    this.isLoading = false;\n    this.searchResults = [];\n    this.hasSearched = false;\n    this.errorMessage = '';\n    this.lastSearchId = '';\n    // Passenger type options\n    this.passengerTypes = [{\n      value: PassengerType.Adult,\n      label: 'Adult'\n    }, {\n      value: PassengerType.Child,\n      label: 'Child'\n    }, {\n      value: PassengerType.Infant,\n      label: 'Infant'\n    }];\n    // Flight class options\n    this.flightClasses = [{\n      value: FlightClassType.PROMO,\n      label: 'Promo'\n    }, {\n      value: FlightClassType.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClassType.BUSINESS,\n      label: 'Business'\n    }];\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required],\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required],\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required],\n      // Options de vol\n      flightClass: [0, Validators.required],\n      nonStop: [false],\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n  ngOnInit() {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n  // Méthode pour afficher les détails du vol\n  showFlightDetails(flight) {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n    header.appendChild(logo);\n    header.appendChild(title);\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n        generalInfo.appendChild(airlineInfo);\n      }\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n      routeSection.appendChild(routeVisual);\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n          segmentsList.appendChild(segmentItem);\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n            segmentsList.appendChild(layover);\n          }\n        });\n        routeSection.appendChild(segmentsList);\n      }\n    }\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n        // ID de l'offre\n        const offerId = this.createInfoRow('Offer ID', offer.offerId || offer.id || 'N/A');\n        offerId.style.gridColumn = '1 / -1';\n        offerDetails.appendChild(offerId);\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n        offerItem.appendChild(offerDetails);\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n          const baggageList = document.createElement('ul');\n          baggageList.style.listStyle = 'none';\n          baggageList.style.padding = '0';\n          baggageList.style.margin = '0';\n          offer.baggageInformations.forEach(baggage => {\n            const baggageItem = document.createElement('li');\n            baggageItem.style.marginBottom = '5px';\n            baggageItem.innerHTML = `<i class=\"fas fa-suitcase\" style=\"color: #666; margin-right: 8px;\"></i> ${this.getBaggageTypeName(baggage.baggageType)}`;\n            baggageList.appendChild(baggageItem);\n          });\n          offerItem.appendChild(baggageList);\n        }\n        offersList.appendChild(offerItem);\n      });\n      offersSection.appendChild(offersList);\n    }\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n  // Méthode utilitaire pour créer une section\n  createSection(title, iconClass) {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n    return section;\n  }\n  // Méthode utilitaire pour créer une ligne d'information\n  createInfoRow(label, value) {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n    return row;\n  }\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight) {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n  preloadLocations() {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n  setupAutocomplete() {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.departureLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('departureLocation')?.setValue('');\n      });\n    });\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.arrivalLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('arrivalLocation')?.setValue('');\n      });\n    });\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  displayLocation(location) {\n    if (!location) return '';\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n  onSearch() {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n    const formValue = this.searchForm.value;\n    // Créer la requête de recherche entièrement dynamique\n    const request = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [{\n        id: formValue.departureLocation?.id || '',\n        type: formValue.departureLocationType\n      }],\n      ArrivalLocations: [{\n        id: formValue.arrivalLocation?.id || '',\n        type: formValue.arrivalLocationType\n      }],\n      Passengers: [{\n        type: formValue.passengerType,\n        count: formValue.passengerCount\n      }],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n    this.productService.searchPrice(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.header.success) {\n          this.searchResults = response.body.flights;\n          // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n          console.log('Full API Response:', JSON.stringify(response, null, 2));\n          // Analyser les données de disponibilité\n          if (response.body && response.body.flights && response.body.flights.length > 0) {\n            console.group('Availability Analysis');\n            console.log('Total flights:', response.body.flights.length);\n            // Compter les vols avec des offres\n            const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n            console.log('Flights with offers:', flightsWithOffers.length);\n            // Analyser les valeurs de disponibilité\n            const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability !== undefined ? o.availability : o.seatInfo ? o.seatInfo.availableSeatCount : 0));\n            console.log('Availability values:', availabilityValues);\n            // Compter les différentes valeurs de disponibilité\n            const availabilityCounts = availabilityValues.reduce((acc, val) => {\n              if (val !== undefined) {\n                acc[val] = (acc[val] || 0) + 1;\n              }\n              return acc;\n            }, {});\n            console.log('Availability counts:', availabilityCounts);\n            // Vérifier les vols réservables\n            const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true));\n            console.log('Reservable flights:', reservableFlights.length);\n            console.groupEnd();\n          }\n          // Vérifier si searchId existe dans le corps de la réponse\n          if (response.body && response.body.searchId) {\n            this.lastSearchId = response.body.searchId;\n            console.log('Search ID found in body.searchId:', this.lastSearchId);\n          }\n          // Vérifier si searchId existe dans l'en-tête de la réponse\n          else if (response.header && response.header.requestId) {\n            this.lastSearchId = response.header.requestId;\n            console.log('Search ID found in header.requestId:', this.lastSearchId);\n          }\n          // Vérifier d'autres emplacements possibles\n          else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n            this.lastSearchId = response.body.flights[0].id;\n            console.log('Using flight ID as search ID:', this.lastSearchId);\n          } else {\n            console.error('No search ID found in the response!');\n            console.log('Response structure:', Object.keys(response));\n            if (response.body) console.log('Body structure:', Object.keys(response.body));\n            if (response.header) console.log('Header structure:', Object.keys(response.header));\n          }\n        } else {\n          this.errorMessage = 'The search could not be completed. Please try again.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred during the search. Please try again.';\n        console.error('Error searching flights:', error);\n      }\n    });\n  }\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  // Formater la durée en heures et minutes\n  formatDuration(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n  // Formater la date pour l'affichage\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n    const minOffer = flight.offers.reduce((min, offer) => offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight) {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    return availabilityValue > 0;\n  }\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations() {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations() {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations() {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString) {\n    if (!dateString) return 'N/A';\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType) {\n    switch (baggageType) {\n      case 1:\n        return 'Cabin Baggage';\n      case 2:\n        return 'Checked Baggage';\n      case 3:\n        return 'Hand Baggage';\n      default:\n        return 'Baggage';\n    }\n  }\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType) {\n    switch (passengerType) {\n      case 1:\n        return 'Adult';\n      case 2:\n        return 'Child';\n      case 3:\n        return 'Infant';\n      default:\n        return 'Passenger';\n    }\n  }\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment, nextSegment) {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date || !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n  static {\n    this.ɵfac = function SearchPriceComponent_Factory(t) {\n      return new (t || SearchPriceComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchPriceComponent,\n      selectors: [[\"app-search-price\"]],\n      decls: 103,\n      vars: 23,\n      consts: [[1, \"search-price-container\"], [1, \"search-layout\"], [1, \"search-card-container\"], [1, \"search-card\"], [1, \"search-card-header\"], [1, \"search-card-title\"], [1, \"fas\", \"fa-search\"], [1, \"search-card-subtitle\"], [1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"location-container\"], [1, \"form-group\", \"location-group\"], [\"for\", \"departureLocation\"], [1, \"location-input-container\"], [1, \"location-type-selector\"], [\"appearance\", \"outline\"], [\"formControlName\", \"departureLocationType\"], [3, \"value\"], [1, \"location-autocomplete\"], [\"type\", \"text\", \"id\", \"departureLocation\", \"matInput\", \"\", \"formControlName\", \"departureLocation\", 3, \"matAutocomplete\", \"click\"], [\"matPrefix\", \"\"], [3, \"displayWith\"], [\"departureAuto\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"swap-button\", 3, \"click\"], [1, \"fas\", \"fa-exchange-alt\"], [\"for\", \"arrivalLocation\"], [\"formControlName\", \"arrivalLocationType\"], [\"type\", \"text\", \"id\", \"arrivalLocation\", \"matInput\", \"\", \"formControlName\", \"arrivalLocation\", 3, \"matAutocomplete\", \"click\"], [\"arrivalAuto\", \"matAutocomplete\"], [1, \"date-passengers-container\"], [1, \"form-group\", \"date-group\"], [\"for\", \"departureDate\"], [\"matInput\", \"\", \"formControlName\", \"departureDate\", \"id\", \"departureDate\", 3, \"min\", \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"picker\", \"\"], [1, \"form-group\", \"passenger-group\"], [\"for\", \"passengerCount\"], [1, \"passenger-inputs\"], [\"formControlName\", \"passengerType\", \"id\", \"passengerType\"], [\"matInput\", \"\", \"type\", \"number\", \"min\", \"1\", \"max\", \"9\", \"formControlName\", \"passengerCount\", \"id\", \"passengerCount\"], [1, \"flight-options-container\"], [1, \"form-group\", \"class-group\"], [\"for\", \"flightClass\"], [\"formControlName\", \"flightClass\", \"id\", \"flightClass\"], [1, \"form-group\", \"nonstop-group\"], [1, \"checkbox-container\"], [\"formControlName\", \"nonStop\", \"id\", \"nonStop\", \"color\", \"primary\"], [1, \"search-button-container\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [1, \"search-results-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"no-results-container\", 4, \"ngIf\"], [\"class\", \"results-list\", 4, \"ngIf\"], [\"class\", \"location-city\", 4, \"ngIf\"], [1, \"location-city\"], [1, \"loading-container\"], [\"diameter\", \"50\", \"color\", \"accent\"], [1, \"loading-text\"], [1, \"error-container\"], [1, \"error-icon\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"error-message\"], [1, \"no-results-container\"], [1, \"no-results-icon\"], [1, \"results-list\"], [1, \"results-title\"], [1, \"fas\", \"fa-plane\"], [1, \"flight-cards\"], [\"class\", \"flight-card animate-fade-in\", 4, \"ngFor\", \"ngForOf\"], [1, \"flight-card\", \"animate-fade-in\"], [1, \"flight-card-header\"], [1, \"flight-info\"], [1, \"flight-id\"], [1, \"flight-route\"], [1, \"departure\"], [1, \"fas\", \"fa-long-arrow-alt-right\"], [1, \"arrival\"], [1, \"flight-price\"], [1, \"price-label\"], [1, \"price-amount\"], [1, \"flight-card-content\"], [1, \"flight-details\"], [1, \"segment-info\"], [1, \"time-info\"], [1, \"departure-time\"], [1, \"flight-duration\"], [1, \"fas\", \"fa-clock\"], [1, \"arrival-time\"], [1, \"route-line\"], [1, \"route-point\"], [1, \"route-path\"], [\"class\", \"stops-indicator\", 4, \"ngIf\"], [1, \"location-info\"], [1, \"departure-location\"], [1, \"arrival-location\"], [1, \"flight-features\"], [1, \"feature\"], [1, \"fas\", \"fa-suitcase\"], [\"class\", \"feature\", 4, \"ngIf\"], [1, \"flight-actions\"], [1, \"view-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [1, \"select-flight-button\", 3, \"click\"], [1, \"fas\", \"fa-check-circle\"], [1, \"stops-indicator\"], [1, \"fas\", \"fa-tag\"]],\n      template: function SearchPriceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementStart(7, \"h2\");\n          i0.ɵɵtext(8, \"Rechercher un vol\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7);\n          i0.ɵɵtext(10, \" Trouvez les meilleurs tarifs pour votre prochain voyage \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function SearchPriceComponent_Template_form_ngSubmit_11_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"label\", 11);\n          i0.ɵɵtext(15, \"D\\u00E9part\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 12)(17, \"div\", 13)(18, \"mat-form-field\", 14)(19, \"mat-select\", 15)(20, \"mat-option\", 16);\n          i0.ɵɵtext(21, \"Ville\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-option\", 16);\n          i0.ɵɵtext(23, \"A\\u00E9roport\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(24, \"div\", 17)(25, \"mat-form-field\", 14)(26, \"mat-label\");\n          i0.ɵɵtext(27, \"Ville ou a\\u00E9roport de d\\u00E9part\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"input\", 18);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_28_listener() {\n            return ctx.showAllDepartureLocations();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"mat-icon\", 19);\n          i0.ɵɵtext(30, \"flight_takeoff\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"mat-autocomplete\", 20, 21);\n          i0.ɵɵtemplate(33, SearchPriceComponent_mat_option_33_Template, 3, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(34, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_34_listener() {\n            return ctx.swapLocations();\n          });\n          i0.ɵɵelement(35, \"i\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 10)(37, \"label\", 25);\n          i0.ɵɵtext(38, \"Arriv\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 12)(40, \"div\", 13)(41, \"mat-form-field\", 14)(42, \"mat-select\", 26)(43, \"mat-option\", 16);\n          i0.ɵɵtext(44, \"Ville\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"mat-option\", 16);\n          i0.ɵɵtext(46, \"A\\u00E9roport\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"div\", 17)(48, \"mat-form-field\", 14)(49, \"mat-label\");\n          i0.ɵɵtext(50, \"Ville ou a\\u00E9roport d'arriv\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"input\", 27);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_51_listener() {\n            return ctx.showAllArrivalLocations();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"mat-icon\", 19);\n          i0.ɵɵtext(53, \"flight_land\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-autocomplete\", 20, 28);\n          i0.ɵɵtemplate(56, SearchPriceComponent_mat_option_56_Template, 3, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(57, \"div\", 29)(58, \"div\", 30)(59, \"label\", 31);\n          i0.ɵɵtext(60, \"Date de d\\u00E9part\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 14)(62, \"mat-label\");\n          i0.ɵɵtext(63, \"Choisir une date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 32)(65, \"mat-datepicker-toggle\", 33)(66, \"mat-datepicker\", null, 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 35)(69, \"label\", 36);\n          i0.ɵɵtext(70, \"Passagers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 37)(72, \"mat-form-field\", 14)(73, \"mat-label\");\n          i0.ɵɵtext(74, \"Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"mat-select\", 38);\n          i0.ɵɵtemplate(76, SearchPriceComponent_mat_option_76_Template, 2, 2, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"mat-form-field\", 14)(78, \"mat-label\");\n          i0.ɵɵtext(79, \"Nombre\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(80, \"input\", 39);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(81, \"div\", 40)(82, \"div\", 41)(83, \"label\", 42);\n          i0.ɵɵtext(84, \"Classe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"mat-form-field\", 14)(86, \"mat-label\");\n          i0.ɵɵtext(87, \"Classe de vol\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"mat-select\", 43);\n          i0.ɵɵtemplate(89, SearchPriceComponent_mat_option_89_Template, 2, 2, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"div\", 44)(91, \"div\", 45)(92, \"mat-checkbox\", 46);\n          i0.ɵɵtext(93, \" Vol direct uniquement \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(94, \"div\", 47)(95, \"button\", 48);\n          i0.ɵɵelement(96, \"i\", 6);\n          i0.ɵɵtext(97, \" Rechercher \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(98, \"div\", 49);\n          i0.ɵɵtemplate(99, SearchPriceComponent_div_99_Template, 4, 0, \"div\", 50);\n          i0.ɵɵtemplate(100, SearchPriceComponent_div_100_Template, 5, 1, \"div\", 51);\n          i0.ɵɵtemplate(101, SearchPriceComponent_div_101_Template, 7, 0, \"div\", 52);\n          i0.ɵɵtemplate(102, SearchPriceComponent_div_102_Template, 6, 2, \"div\", 53);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(32);\n          const _r2 = i0.ɵɵreference(55);\n          const _r4 = i0.ɵɵreference(67);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.departureLocations);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matAutocomplete\", _r2);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrivalLocations);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"min\", ctx.minDate)(\"matDatepicker\", _r4);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.passengerTypes);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"has-results\", ctx.hasSearched);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.searchResults.length === 0 && ctx.hasSearched && !ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.searchResults.length > 0);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i5.MatAutocomplete, i6.MatOption, i5.MatAutocompleteTrigger, i7.MatInput, i8.MatFormField, i8.MatLabel, i8.MatPrefix, i8.MatSuffix, i9.MatSelect, i10.MatDatepicker, i10.MatDatepickerInput, i10.MatDatepickerToggle, i11.MatIcon, i12.MatProgressSpinner, i4.DatePipe],\n      styles: [\"/* Styles pour le composant search-price */\\n.search-price-container {\\n  width: 100%;\\n  min-height: calc(100vh - 64px);\\n  background-color: var(--background-color);\\n  padding: var(--spacing-lg) 0;\\n}\\n\\n.search-layout {\\n  display: flex;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  gap: var(--spacing-xl);\\n  padding: 0 var(--spacing-lg);\\n}\\n\\n/* Styles pour la carte de recherche */\\n.search-card-container {\\n  width: 360px;\\n  flex-shrink: 0;\\n  position: sticky;\\n  top: 84px; /* 64px (navbar) + 20px */\\n  height: -moz-fit-content;\\n  height: fit-content;\\n}\\n\\n.search-card {\\n  background-color: var(--surface-color);\\n  border-radius: var(--border-radius-large);\\n  box-shadow: var(--elevation-2);\\n  overflow: hidden;\\n  transition: box-shadow var(--transition-medium);\\n}\\n\\n.search-card:hover {\\n  box-shadow: var(--elevation-3);\\n}\\n\\n.search-card-header {\\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\\n  color: white;\\n  padding: var(--spacing-lg);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.search-card-header::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: url('/assets/images/pattern-dots.svg');\\n  opacity: 0.1;\\n}\\n\\n.search-card-title {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  margin-bottom: var(--spacing-xs);\\n}\\n\\n.search-card-title i {\\n  font-size: 1.2rem;\\n}\\n\\n.search-card-title h2 {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.search-card-subtitle {\\n  font-size: 0.9rem;\\n  opacity: 0.9;\\n}\\n\\n.search-form {\\n  padding: var(--spacing-lg);\\n}\\n\\n/* Styles pour les groupes de formulaire */\\n.form-group {\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.form-group label {\\n  display: block;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  margin-bottom: var(--spacing-xs);\\n  color: var(--text-primary);\\n}\\n\\n/* Styles pour les emplacements */\\n.location-container {\\n  position: relative;\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.location-group {\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.location-input-container {\\n  display: flex;\\n  gap: var(--spacing-xs);\\n}\\n\\n.location-type-selector {\\n  width: 100px;\\n  flex-shrink: 0;\\n}\\n\\n.location-autocomplete {\\n  flex-grow: 1;\\n}\\n\\n.location-city {\\n  color: var(--text-secondary);\\n  font-size: 0.85rem;\\n}\\n\\n.swap-button {\\n  position: absolute;\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\\n  color: white;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  box-shadow: var(--elevation-2);\\n  transition: all var(--transition-medium);\\n  z-index: 1;\\n}\\n\\n.swap-button:hover {\\n  transform: translate(-50%, -50%) rotate(180deg);\\n  box-shadow: var(--elevation-3);\\n}\\n\\n/* Styles pour la date et les passagers */\\n.date-passengers-container {\\n  display: flex;\\n  gap: var(--spacing-md);\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.date-group {\\n  flex: 1;\\n}\\n\\n.passenger-group {\\n  flex: 1;\\n}\\n\\n.passenger-inputs {\\n  display: flex;\\n  gap: var(--spacing-xs);\\n}\\n\\n.passenger-inputs mat-form-field {\\n  flex: 1;\\n}\\n\\n/* Styles pour les options de vol */\\n.flight-options-container {\\n  display: flex;\\n  gap: var(--spacing-md);\\n  margin-bottom: var(--spacing-lg);\\n}\\n\\n.class-group {\\n  flex: 1;\\n}\\n\\n.nonstop-group {\\n  flex: 1;\\n  display: flex;\\n  align-items: flex-end;\\n}\\n\\n.checkbox-container {\\n  padding-bottom: 1.34375em; /* Aligner avec les autres champs */\\n}\\n\\n/* Styles pour le bouton de recherche */\\n.search-button-container {\\n  margin-top: var(--spacing-lg);\\n}\\n\\n.search-button {\\n  width: 100%;\\n  height: 48px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\\n  color: white;\\n  border: none;\\n  border-radius: var(--border-radius-medium);\\n  font-size: 1rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: var(--spacing-sm);\\n  cursor: pointer;\\n  transition: all var(--transition-medium);\\n  box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.3);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.search-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: all 0.6s;\\n}\\n\\n.search-button:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 15px rgba(var(--primary-color-rgb), 0.4);\\n}\\n\\n.search-button:hover::before {\\n  left: 100%;\\n}\\n\\n.search-button:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n/* Styles pour les r\\u00E9sultats de recherche */\\n.search-results-container {\\n  flex: 1;\\n  min-height: 300px;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: var(--surface-color);\\n  border-radius: var(--border-radius-large);\\n  box-shadow: var(--elevation-2);\\n  overflow: hidden;\\n  transition: all var(--transition-medium);\\n  opacity: 0;\\n  transform: translateY(20px);\\n}\\n\\n.search-results-container.has-results {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n/* Styles pour le chargement */\\n.loading-container {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--spacing-xxl);\\n  gap: var(--spacing-md);\\n}\\n\\n.loading-text {\\n  color: var(--text-secondary);\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n\\n/* Styles pour les erreurs */\\n.error-container {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--spacing-xxl);\\n  gap: var(--spacing-md);\\n  text-align: center;\\n}\\n\\n.error-icon {\\n  font-size: 3rem;\\n  color: var(--error-color);\\n}\\n\\n.error-message {\\n  color: var(--text-primary);\\n  font-size: 1rem;\\n  max-width: 500px;\\n  margin: 0;\\n}\\n\\n/* Styles pour aucun r\\u00E9sultat */\\n.no-results-container {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--spacing-xxl);\\n  gap: var(--spacing-sm);\\n  text-align: center;\\n}\\n\\n.no-results-icon {\\n  font-size: 3rem;\\n  color: var(--text-secondary);\\n  opacity: 0.5;\\n}\\n\\n.no-results-container h3 {\\n  color: var(--text-primary);\\n  font-size: 1.5rem;\\n  margin: var(--spacing-sm) 0 0 0;\\n}\\n\\n.no-results-container p {\\n  color: var(--text-secondary);\\n  font-size: 1rem;\\n  margin: var(--spacing-xs) 0 0 0;\\n}\\n\\n/* Styles pour la liste des r\\u00E9sultats */\\n.results-list {\\n  flex: 1;\\n  padding: var(--spacing-lg);\\n  overflow-y: auto;\\n}\\n\\n.results-title {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  margin: 0 0 var(--spacing-lg) 0;\\n  color: var(--text-primary);\\n}\\n\\n.results-title i {\\n  color: var(--primary-color);\\n}\\n\\n/* Styles pour les cartes de vol */\\n.flight-cards {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-md);\\n}\\n\\n.flight-card {\\n  background-color: white;\\n  border-radius: var(--border-radius-medium);\\n  box-shadow: var(--elevation-1);\\n  overflow: hidden;\\n  transition: all var(--transition-medium);\\n  border: 1px solid var(--divider-color);\\n}\\n\\n.flight-card:hover {\\n  transform: translateY(-4px);\\n  box-shadow: var(--elevation-3);\\n}\\n\\n.flight-card-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spacing-md);\\n  background-color: var(--primary-light);\\n  color: white;\\n}\\n\\n.flight-info {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-xs);\\n}\\n\\n.flight-id {\\n  font-size: 0.9rem;\\n  opacity: 0.9;\\n}\\n\\n.flight-route {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.flight-price {\\n  text-align: right;\\n}\\n\\n.price-label {\\n  font-size: 0.8rem;\\n  opacity: 0.9;\\n}\\n\\n.price-amount {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n}\\n\\n.flight-card-content {\\n  padding: var(--spacing-md);\\n}\\n\\n.flight-details {\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.segment-info {\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.time-info {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: var(--spacing-xs);\\n}\\n\\n.departure-time, .arrival-time {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n}\\n\\n.flight-duration {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n  font-size: 0.9rem;\\n  color: var(--text-secondary);\\n}\\n\\n.route-line {\\n  display: flex;\\n  align-items: center;\\n  margin: var(--spacing-sm) 0;\\n  position: relative;\\n}\\n\\n.route-point {\\n  width: 10px;\\n  height: 10px;\\n  border-radius: 50%;\\n  background-color: var(--primary-color);\\n  z-index: 1;\\n}\\n\\n.route-path {\\n  flex: 1;\\n  height: 2px;\\n  background-color: var(--primary-color);\\n  position: relative;\\n}\\n\\n.route-path.has-stops {\\n  background: repeating-linear-gradient(\\n    90deg,\\n    var(--primary-color),\\n    var(--primary-color) 6px,\\n    transparent 6px,\\n    transparent 12px\\n  );\\n}\\n\\n.stops-indicator {\\n  position: absolute;\\n  top: -12px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background-color: var(--warning-color);\\n  color: white;\\n  font-size: 0.7rem;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  white-space: nowrap;\\n}\\n\\n.location-info {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 0.85rem;\\n  color: var(--text-secondary);\\n}\\n\\n.flight-features {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: var(--spacing-sm);\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.feature {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n  padding: 4px 10px;\\n  border-radius: 20px;\\n  font-size: 0.8rem;\\n}\\n\\n.flight-actions {\\n  display: flex;\\n  gap: var(--spacing-sm);\\n}\\n\\n.view-details-button, .select-flight-button {\\n  flex: 1;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: var(--spacing-xs);\\n  border-radius: var(--border-radius-medium);\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all var(--transition-medium);\\n  border: none;\\n}\\n\\n.view-details-button {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n}\\n\\n.view-details-button:hover {\\n  background-color: rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.select-flight-button {\\n  background-color: var(--primary-color);\\n  color: white;\\n}\\n\\n.select-flight-button:hover {\\n  background-color: var(--primary-dark);\\n}\\n\\n/* Responsive styles */\\n@media (max-width: 1200px) {\\n  .search-layout {\\n    gap: var(--spacing-lg);\\n  }\\n}\\n\\n@media (max-width: 992px) {\\n  .search-layout {\\n    flex-direction: column;\\n  }\\n\\n  .search-card-container {\\n    width: 100%;\\n    position: static;\\n  }\\n\\n  .search-results-container {\\n    width: 100%;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .date-passengers-container,\\n  .flight-options-container {\\n    flex-direction: column;\\n    gap: var(--spacing-sm);\\n  }\\n\\n  .passenger-inputs {\\n    flex-direction: column;\\n  }\\n\\n  .flight-card-header {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: var(--spacing-sm);\\n  }\\n\\n  .flight-price {\\n    text-align: left;\\n  }\\n\\n  .flight-actions {\\n    flex-direction: column;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .search-price-container {\\n    padding: var(--spacing-md) 0;\\n  }\\n\\n  .search-layout {\\n    padding: 0 var(--spacing-md);\\n  }\\n\\n  .location-input-container {\\n    flex-direction: column;\\n    gap: var(--spacing-xs);\\n  }\\n\\n  .location-type-selector {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"/* Styles sp\\u00E9cifiques pour les cartes de recherche */\\n\\n/* Styles pour les champs Material */\\n::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {\\n  color: rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {\\n  color: var(--primary-color);\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {\\n  padding: 0.5em 0;\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline .mat-form-field-wrapper {\\n  margin: 0;\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {\\n  top: 1.5em;\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {\\n  transform: translateY(-1.1em) scale(0.75);\\n}\\n\\n::ng-deep .mat-form-field-prefix {\\n  color: var(--primary-color);\\n  opacity: 0.8;\\n  margin-right: 8px;\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline .mat-select-arrow-wrapper {\\n  transform: translateY(0);\\n}\\n\\n/* Styles pour l'autocompl\\u00E9tion */\\n::ng-deep .mat-autocomplete-panel {\\n  border-radius: var(--border-radius-medium);\\n  box-shadow: var(--elevation-3);\\n}\\n\\n::ng-deep .mat-option {\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n  padding: 8px 16px;\\n}\\n\\n::ng-deep .mat-option:hover:not(.mat-option-disabled) {\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n::ng-deep .mat-option.mat-selected:not(.mat-option-multiple):not(.mat-option-disabled) {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n}\\n\\n/* Styles pour le datepicker */\\n::ng-deep .mat-datepicker-toggle {\\n  color: var(--primary-color);\\n}\\n\\n::ng-deep .mat-calendar-body-selected {\\n  background-color: var(--primary-color);\\n}\\n\\n::ng-deep .mat-calendar-body-today:not(.mat-calendar-body-selected) {\\n  border-color: var(--primary-color);\\n}\\n\\n/* Styles pour les checkbox */\\n::ng-deep .mat-checkbox-checked.mat-primary .mat-checkbox-background {\\n  background-color: var(--primary-color);\\n}\\n\\n::ng-deep .mat-checkbox:not(.mat-checkbox-disabled).mat-primary .mat-checkbox-ripple .mat-ripple-element {\\n  background-color: rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n/* Styles pour le spinner */\\n::ng-deep .mat-progress-spinner circle, .mat-spinner circle {\\n  stroke: var(--accent-color);\\n}\\n\\n/* Styles pour les inputs num\\u00E9riques */\\n::ng-deep input[type=number]::-webkit-inner-spin-button,\\n::ng-deep input[type=number]::-webkit-outer-spin-button {\\n  opacity: 1;\\n  margin-left: 8px;\\n}\\n\\n/* Animation pour les cartes de vol */\\n@keyframes fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in {\\n  animation: fadeInUp 0.5s ease-out forwards;\\n}\\n\\n/* D\\u00E9lai d'animation pour les cartes de vol */\\n.flight-card:nth-child(1) {\\n  animation-delay: 0.1s;\\n}\\n\\n.flight-card:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n\\n.flight-card:nth-child(3) {\\n  animation-delay: 0.3s;\\n}\\n\\n.flight-card:nth-child(4) {\\n  animation-delay: 0.4s;\\n}\\n\\n.flight-card:nth-child(5) {\\n  animation-delay: 0.5s;\\n}\\n\\n.flight-card:nth-child(n+6) {\\n  animation-delay: 0.6s;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "of", "FlightClassType", "LocationType", "PassengerType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "location_r11", "city", "ɵɵtemplate", "SearchPriceComponent_mat_option_33_span_2_Template", "ɵɵproperty", "ɵɵtextInterpolate2", "name", "code", "type", "location_r14", "SearchPriceComponent_mat_option_56_span_2_Template", "type_r17", "value", "label", "flightClass_r18", "ɵɵelement", "ɵɵtextInterpolate", "ctx_r8", "errorMessage", "flight_r20", "items", "segments", "length", "offers", "flightBrandInfo", "SearchPriceComponent_div_102_div_5_div_32_Template", "SearchPriceComponent_div_102_div_5_div_44_Template", "SearchPriceComponent_div_102_div_5_div_45_Template", "ɵɵlistener", "SearchPriceComponent_div_102_div_5_Template_button_click_47_listener", "restoredCtx", "ɵɵrestoreView", "_r27", "$implicit", "ctx_r26", "ɵɵnextContext", "ɵɵresetView", "showFlightDetails", "SearchPriceComponent_div_102_div_5_Template_button_click_50_listener", "ctx_r28", "selectThisFlight", "id", "departure", "arrival", "ctx_r19", "getMinPrice", "ɵɵpipeBind2", "date", "formatDuration", "duration", "ɵɵclassProp", "airport", "SearchPriceComponent_div_102_div_5_Template", "ctx_r10", "searchResults", "SearchPriceComponent", "constructor", "fb", "productService", "router", "departureLocations", "arrivalLocations", "isLoading", "hasSearched", "lastSearchId", "passengerTypes", "Adult", "Child", "Infant", "flightClasses", "PROMO", "ECONOMY", "BUSINESS", "minDate", "Date", "toISOString", "split", "searchForm", "group", "productType", "required", "serviceTypes", "departureLocation", "departureLocationType", "arrivalLocation", "arrivalLocationType", "departureDate", "passengerCount", "min", "max", "passengerType", "flightClass", "nonStop", "culture", "currency", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightBaggageGetOption", "ngOnInit", "setupAutocomplete", "preloadLocations", "console", "log", "flight", "modalDiv", "document", "createElement", "style", "position", "top", "left", "width", "height", "backgroundColor", "zIndex", "display", "justifyContent", "alignItems", "modalContent", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "color", "fontFamily", "closeButton", "innerHTML", "right", "border", "background", "fontSize", "cursor", "transition", "on<PERSON><PERSON>ver", "onmouseout", "onclick", "body", "<PERSON><PERSON><PERSON><PERSON>", "header", "marginBottom", "paddingBottom", "borderBottom", "logo", "title", "textContent", "margin", "fontWeight", "append<PERSON><PERSON><PERSON>", "detailsContainer", "flexDirection", "gap", "generalInfo", "createSection", "item", "airline", "airlineInfo", "thumbnailFull", "airlineLogo", "src", "alt", "marginRight", "airlineIcon", "airlineName", "internationalCode", "flightNumberRow", "createInfoRow", "flightNo", "flightDateRow", "flightDate", "toLocaleDateString", "durationRow", "classRow", "stopsRow", "stopCount", "routeSection", "routeVisual", "textAlign", "flex", "departureTime", "toLocaleTimeString", "hour", "minute", "departureAirport", "marginTop", "departureCity", "connectionLine", "line", "plane", "marginLeft", "arrivalTime", "arrivalAirport", "arrivalCity", "segmentsTitle", "segmentsList", "for<PERSON>ach", "segment", "index", "segmentItem", "segmentHeader", "segmentTitle", "segmentDuration", "segmentRoute", "segmentDeparture", "depTime", "depAirport", "arrow", "segmentArrival", "arrTime", "arrAirport", "layover", "currentArrival", "getTime", "nextDeparture", "layoverTime", "Math", "floor", "offersSection", "offersList", "offer", "offerItem", "offerHeader", "offerTitle", "offerPrice", "price", "amount", "offerDetails", "gridTemplateColumns", "offerId", "gridColumn", "availabilityValue", "availability", "undefined", "seatInfo", "availableSeatCount", "expiresOn", "expires", "toLocaleString", "brandedFare", "reservableInfo", "reservable", "baggageInformations", "baggageTitle", "baggageList", "listStyle", "baggage", "baggageItem", "getBaggageTypeName", "baggageType", "services", "servicesSection", "servicesList", "service", "serviceItem", "iconClass", "section", "section<PERSON><PERSON><PERSON>", "icon", "className", "sectionTitle", "row", "labelElement", "valueElement", "searchId", "navigate", "queryParams", "error", "get", "getLocationsByType", "subscribe", "locations", "valueChanges", "locationType", "setValue", "pipe", "filter", "location", "toLowerCase", "includes", "displayLocation", "displayText", "Airport", "onSearch", "invalid", "markFormGroupTouched", "formValue", "request", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "ArrivalLocations", "Passengers", "count", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "searchPrice", "next", "response", "success", "flights", "JSON", "stringify", "flightsWithOffers", "f", "availabilityValues", "flatMap", "o", "availabilityCounts", "reduce", "acc", "val", "reservableFlights", "some", "groupEnd", "requestId", "Object", "keys", "messages", "message", "formGroup", "values", "controls", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "minutes", "hours", "mins", "formatDate", "dateString", "weekday", "day", "month", "min<PERSON>ffer", "formattedAmount", "isFlightAvailable", "showAllDepartureLocations", "input", "getElementById", "focus", "dispatchEvent", "Event", "showAllArrivalLocations", "swapLocations", "patchValue", "formatExpirationDate", "getPassengerTypeName", "calculateLayoverTime", "currentSegment", "nextSegment", "diffMs", "diffMins", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProductService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "SearchPriceComponent_Template", "rf", "ctx", "SearchPriceComponent_Template_form_ngSubmit_11_listener", "SearchPriceComponent_Template_input_click_28_listener", "SearchPriceComponent_mat_option_33_Template", "SearchPriceComponent_Template_button_click_34_listener", "SearchPriceComponent_Template_input_click_51_listener", "SearchPriceComponent_mat_option_56_Template", "SearchPriceComponent_mat_option_76_Template", "SearchPriceComponent_mat_option_89_Template", "SearchPriceComponent_div_99_Template", "SearchPriceComponent_div_100_Template", "SearchPriceComponent_div_101_Template", "SearchPriceComponent_div_102_Template", "_r0", "_r2", "_r4"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { Observable, of } from 'rxjs';\nimport { ProductService } from '../../../services/product.service';\nimport { LocationOption } from '../../../models/price-search-request.model';\nimport { PriceSearchRequest } from '../../../models/price-search-request.model';\nimport { PriceSearchResponse, Flight } from '../../../models/price-search-response.model';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css'],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchPriceComponent implements OnInit {\n  searchForm: FormGroup;\n  departureLocations: LocationOption[] = [];\n  arrivalLocations: LocationOption[] = [];\n  isLoading = false;\n  searchResults: Flight[] = [];\n  hasSearched = false;\n  errorMessage = '';\n  lastSearchId = '';\n\n  // Passenger type options\n  passengerTypes = [\n    { value: PassengerType.Adult, label: 'Adult' },\n    { value: PassengerType.Child, label: 'Child' },\n    { value: PassengerType.Infant, label: 'Infant' }\n  ];\n\n  // Flight class options\n  flightClasses = [\n    { value: FlightClassType.PROMO, label: 'Promo' },\n    { value: FlightClassType.ECONOMY, label: 'Economy' },\n    { value: FlightClassType.BUSINESS, label: 'Business' }\n  ];\n\n  // Date minimale (aujourd'hui)\n  minDate: string;\n\n  constructor(\n    private fb: FormBuilder,\n    private productService: ProductService,\n    private router: Router\n  ) {\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required], // Flight par défaut\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required], // Type 2 (City) par défaut\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required], // Type 5 (Airport) par défaut\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required], // Adult par défaut\n\n      // Options de vol\n      flightClass: [0, Validators.required], // PROMO par défaut\n      nonStop: [false],\n\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n\n  ngOnInit(): void {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n\n  // Méthode pour afficher les détails du vol\n  showFlightDetails(flight: Flight): void {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n\n    header.appendChild(logo);\n    header.appendChild(title);\n\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n\n        generalInfo.appendChild(airlineInfo);\n      }\n\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n\n      routeSection.appendChild(routeVisual);\n\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n\n          segmentsList.appendChild(segmentItem);\n\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n\n            segmentsList.appendChild(layover);\n          }\n        });\n\n        routeSection.appendChild(segmentsList);\n      }\n    }\n\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n\n        // ID de l'offre\n        const offerId = this.createInfoRow('Offer ID', offer.offerId || offer.id || 'N/A');\n        offerId.style.gridColumn = '1 / -1';\n        offerDetails.appendChild(offerId);\n\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability :\n                                 (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n\n        offerItem.appendChild(offerDetails);\n\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n\n          const baggageList = document.createElement('ul');\n          baggageList.style.listStyle = 'none';\n          baggageList.style.padding = '0';\n          baggageList.style.margin = '0';\n\n          offer.baggageInformations.forEach(baggage => {\n            const baggageItem = document.createElement('li');\n            baggageItem.style.marginBottom = '5px';\n            baggageItem.innerHTML = `<i class=\"fas fa-suitcase\" style=\"color: #666; margin-right: 8px;\"></i> ${this.getBaggageTypeName(baggage.baggageType)}`;\n            baggageList.appendChild(baggageItem);\n          });\n\n          offerItem.appendChild(baggageList);\n        }\n\n        offersList.appendChild(offerItem);\n      });\n\n      offersSection.appendChild(offersList);\n    }\n\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n\n  // Méthode utilitaire pour créer une section\n  private createSection(title: string, iconClass: string): HTMLElement {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n\n    return section;\n  }\n\n  // Méthode utilitaire pour créer une ligne d'information\n  private createInfoRow(label: string, value: string): HTMLElement {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n\n    return row;\n  }\n\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight: Flight): void {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n\n  preloadLocations(): void {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n\n  setupAutocomplete(): void {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.departureLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('departureLocation')?.setValue('');\n        });\n      });\n\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.arrivalLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('arrivalLocation')?.setValue('');\n        });\n      });\n\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.departureLocations = locations;\n      });\n\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n  }\n\n  displayLocation(location: LocationOption | null): string {\n    if (!location) return '';\n\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n\n  onSearch(): void {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n\n    const formValue = this.searchForm.value;\n\n    // Créer la requête de recherche entièrement dynamique\n    const request: PriceSearchRequest = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [\n        {\n          id: formValue.departureLocation?.id || '',\n          type: formValue.departureLocationType\n        }\n      ],\n      ArrivalLocations: [\n        {\n          id: formValue.arrivalLocation?.id || '',\n          type: formValue.arrivalLocationType\n        }\n      ],\n      Passengers: [\n        {\n          type: formValue.passengerType,\n          count: formValue.passengerCount\n        }\n      ],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n\n    this.productService.searchPrice(request)\n      .subscribe({\n        next: (response: PriceSearchResponse) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            console.log('Full API Response:', JSON.stringify(response, null, 2));\n\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o =>\n                o.availability !== undefined ? o.availability : (o.seatInfo ? o.seatInfo.availableSeatCount : 0)\n              ));\n              console.log('Availability values:', availabilityValues);\n\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc, val) => {\n                if (val !== undefined) {\n                  acc[val] = (acc[val] || 0) + 1;\n                }\n                return acc;\n              }, {} as Record<number, number>);\n              console.log('Availability counts:', availabilityCounts);\n\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f =>\n                f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true)\n              );\n              console.log('Reservable flights:', reservableFlights.length);\n\n              console.groupEnd();\n            }\n\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n  }\n\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup: FormGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n\n  // Formater la durée en heures et minutes\n  formatDuration(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n\n  // Formater la date pour l'affichage\n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight: Flight): string {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n\n    const minOffer = flight.offers.reduce((min, offer) =>\n      offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight: Flight): boolean {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability :\n                             (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n    return availabilityValue > 0;\n  }\n\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations(): void {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations(): void {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations(): void {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType: number): string {\n    switch (baggageType) {\n      case 1: return 'Cabin Baggage';\n      case 2: return 'Checked Baggage';\n      case 3: return 'Hand Baggage';\n      default: return 'Baggage';\n    }\n  }\n\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType: number): string {\n    switch (passengerType) {\n      case 1: return 'Adult';\n      case 2: return 'Child';\n      case 3: return 'Infant';\n      default: return 'Passenger';\n    }\n  }\n\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment: any, nextSegment: any): string {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date ||\n        !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n}\n", "<div class=\"search-price-container\">\n  <div class=\"search-layout\">\n    <!-- Carte de recherche -->\n    <div class=\"search-card-container\">\n      <div class=\"search-card\">\n        <div class=\"search-card-header\">\n          <div class=\"search-card-title\">\n            <i class=\"fas fa-search\"></i>\n            <h2>Rechercher un vol</h2>\n          </div>\n          <div class=\"search-card-subtitle\">\n            Trouvez les meilleurs tarifs pour votre prochain voyage\n          </div>\n        </div>\n\n        <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSearch()\" class=\"search-form\">\n          <!-- Emplacements de départ et d'arrivée -->\n          <div class=\"location-container\">\n            <div class=\"form-group location-group\">\n              <label for=\"departureLocation\">Départ</label>\n              <div class=\"location-input-container\">\n                <div class=\"location-type-selector\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-select formControlName=\"departureLocationType\">\n                      <mat-option [value]=\"2\">Ville</mat-option>\n                      <mat-option [value]=\"5\">Aéroport</mat-option>\n                    </mat-select>\n                  </mat-form-field>\n                </div>\n                <div class=\"location-autocomplete\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Ville ou aéroport de départ</mat-label>\n                    <input type=\"text\"\n                           id=\"departureLocation\"\n                           matInput\n                           [matAutocomplete]=\"departureAuto\"\n                           formControlName=\"departureLocation\"\n                           (click)=\"showAllDepartureLocations()\">\n                    <mat-icon matPrefix>flight_takeoff</mat-icon>\n                    <mat-autocomplete #departureAuto=\"matAutocomplete\" [displayWith]=\"displayLocation\">\n                      <mat-option *ngFor=\"let location of departureLocations\" [value]=\"location\">\n                        {{ location.name }} {{ location.code ? '(' + location.code + ')' : '' }}\n                        <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">\n                          - {{ location.city }}\n                        </span>\n                      </mat-option>\n                    </mat-autocomplete>\n                  </mat-form-field>\n                </div>\n              </div>\n            </div>\n\n            <button type=\"button\" class=\"swap-button\" (click)=\"swapLocations()\">\n              <i class=\"fas fa-exchange-alt\"></i>\n            </button>\n\n            <div class=\"form-group location-group\">\n              <label for=\"arrivalLocation\">Arrivée</label>\n              <div class=\"location-input-container\">\n                <div class=\"location-type-selector\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-select formControlName=\"arrivalLocationType\">\n                      <mat-option [value]=\"2\">Ville</mat-option>\n                      <mat-option [value]=\"5\">Aéroport</mat-option>\n                    </mat-select>\n                  </mat-form-field>\n                </div>\n                <div class=\"location-autocomplete\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Ville ou aéroport d'arrivée</mat-label>\n                    <input type=\"text\"\n                           id=\"arrivalLocation\"\n                           matInput\n                           [matAutocomplete]=\"arrivalAuto\"\n                           formControlName=\"arrivalLocation\"\n                           (click)=\"showAllArrivalLocations()\">\n                    <mat-icon matPrefix>flight_land</mat-icon>\n                    <mat-autocomplete #arrivalAuto=\"matAutocomplete\" [displayWith]=\"displayLocation\">\n                      <mat-option *ngFor=\"let location of arrivalLocations\" [value]=\"location\">\n                        {{ location.name }} {{ location.code ? '(' + location.code + ')' : '' }}\n                        <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">\n                          - {{ location.city }}\n                        </span>\n                      </mat-option>\n                    </mat-autocomplete>\n                  </mat-form-field>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Date et passagers -->\n          <div class=\"date-passengers-container\">\n            <div class=\"form-group date-group\">\n              <label for=\"departureDate\">Date de départ</label>\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Choisir une date</mat-label>\n                <input matInput [min]=\"minDate\" [matDatepicker]=\"picker\" formControlName=\"departureDate\" id=\"departureDate\">\n                <mat-datepicker-toggle matSuffix [for]=\"picker\"></mat-datepicker-toggle>\n                <mat-datepicker #picker></mat-datepicker>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-group passenger-group\">\n              <label for=\"passengerCount\">Passagers</label>\n              <div class=\"passenger-inputs\">\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Type</mat-label>\n                  <mat-select formControlName=\"passengerType\" id=\"passengerType\">\n                    <mat-option *ngFor=\"let type of passengerTypes\" [value]=\"type.value\">\n                      {{ type.label }}\n                    </mat-option>\n                  </mat-select>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Nombre</mat-label>\n                  <input matInput type=\"number\" min=\"1\" max=\"9\" formControlName=\"passengerCount\" id=\"passengerCount\">\n                </mat-form-field>\n              </div>\n            </div>\n          </div>\n\n          <!-- Options de vol -->\n          <div class=\"flight-options-container\">\n            <div class=\"form-group class-group\">\n              <label for=\"flightClass\">Classe</label>\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Classe de vol</mat-label>\n                <mat-select formControlName=\"flightClass\" id=\"flightClass\">\n                  <mat-option *ngFor=\"let flightClass of flightClasses\" [value]=\"flightClass.value\">\n                    {{ flightClass.label }}\n                  </mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-group nonstop-group\">\n              <div class=\"checkbox-container\">\n                <mat-checkbox formControlName=\"nonStop\" id=\"nonStop\" color=\"primary\">\n                  Vol direct uniquement\n                </mat-checkbox>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton de recherche -->\n          <div class=\"search-button-container\">\n            <button type=\"submit\" class=\"search-button\" [disabled]=\"isLoading\">\n              <i class=\"fas fa-search\"></i>\n              Rechercher\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n\n    <!-- Résultats de recherche -->\n    <div class=\"search-results-container\" [class.has-results]=\"hasSearched\">\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <mat-spinner diameter=\"50\" color=\"accent\"></mat-spinner>\n        <p class=\"loading-text\">Recherche des meilleurs vols...</p>\n      </div>\n\n      <div *ngIf=\"errorMessage && !isLoading\" class=\"error-container\">\n        <div class=\"error-icon\">\n          <i class=\"fas fa-exclamation-circle\"></i>\n        </div>\n        <p class=\"error-message\">{{ errorMessage }}</p>\n      </div>\n\n      <div *ngIf=\"!isLoading && searchResults.length === 0 && hasSearched && !errorMessage\" class=\"no-results-container\">\n        <div class=\"no-results-icon\">\n          <i class=\"fas fa-search\"></i>\n        </div>\n        <h3>Aucun vol trouvé</h3>\n        <p>Essayez de modifier vos critères de recherche</p>\n      </div>\n\n      <div *ngIf=\"!isLoading && searchResults.length > 0\" class=\"results-list\">\n        <h3 class=\"results-title\">\n          <i class=\"fas fa-plane\"></i>\n          Vols disponibles ({{ searchResults.length }})\n        </h3>\n\n        <div class=\"flight-cards\">\n          <div *ngFor=\"let flight of searchResults\" class=\"flight-card animate-fade-in\">\n            <div class=\"flight-card-header\">\n              <div class=\"flight-info\">\n                <div class=\"flight-id\">{{ flight.id }}</div>\n                <div class=\"flight-route\">\n                  <span class=\"departure\">{{ flight.items[0]?.segments[0]?.departure?.city?.name || 'Départ' }}</span>\n                  <i class=\"fas fa-long-arrow-alt-right\"></i>\n                  <span class=\"arrival\">{{ flight.items[0]?.segments[flight.items[0]?.segments.length - 1]?.arrival?.city?.name || 'Arrivée' }}</span>\n                </div>\n              </div>\n              <div class=\"flight-price\">\n                <div class=\"price-label\">à partir de</div>\n                <div class=\"price-amount\">{{ getMinPrice(flight) }}</div>\n              </div>\n            </div>\n\n            <div class=\"flight-card-content\">\n              <div class=\"flight-details\">\n                <div class=\"segment-info\">\n                  <div class=\"time-info\">\n                    <div class=\"departure-time\">\n                      {{ flight.items[0]?.segments[0]?.departure?.date | date:'HH:mm' }}\n                    </div>\n                    <div class=\"flight-duration\">\n                      <i class=\"fas fa-clock\"></i>\n                      {{ formatDuration(flight.items[0]?.duration || 0) }}\n                    </div>\n                    <div class=\"arrival-time\">\n                      {{ flight.items[0]?.segments[flight.items[0]?.segments.length - 1]?.arrival?.date | date:'HH:mm' }}\n                    </div>\n                  </div>\n                  <div class=\"route-line\">\n                    <div class=\"route-point\"></div>\n                    <div class=\"route-path\" [class.has-stops]=\"flight.items[0]?.segments.length > 1\">\n                      <div *ngIf=\"flight.items[0]?.segments.length > 1\" class=\"stops-indicator\">\n                        {{ flight.items[0]?.segments.length - 1 }} escale(s)\n                      </div>\n                    </div>\n                    <div class=\"route-point\"></div>\n                  </div>\n                  <div class=\"location-info\">\n                    <div class=\"departure-location\">\n                      {{ flight.items[0]?.segments[0]?.departure?.airport?.code || '' }}\n                    </div>\n                    <div class=\"arrival-location\">\n                      {{ flight.items[0]?.segments[flight.items[0]?.segments.length - 1]?.arrival?.airport?.code || '' }}\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"flight-features\">\n                  <div class=\"feature\">\n                    <i class=\"fas fa-suitcase\"></i>\n                    <span>Bagages inclus</span>\n                  </div>\n                  <div class=\"feature\" *ngIf=\"flight.items[0]?.segments.length === 1\">\n                    <i class=\"fas fa-plane\"></i>\n                    <span>Vol direct</span>\n                  </div>\n                  <div class=\"feature\" *ngIf=\"flight.offers[0]?.flightBrandInfo?.name\">\n                    <i class=\"fas fa-tag\"></i>\n                    <span>{{ flight.offers[0]?.flightBrandInfo?.name }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"flight-actions\">\n                <button class=\"view-details-button\" (click)=\"showFlightDetails(flight)\">\n                  <i class=\"fas fa-info-circle\"></i>\n                  Détails\n                </button>\n                <button class=\"select-flight-button\" (click)=\"selectThisFlight(flight)\">\n                  <i class=\"fas fa-check-circle\"></i>\n                  Sélectionner\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnF,SAAqBC,EAAE,QAAQ,MAAM;AAKrC,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;;;;;;;;;;;;;;;;ICiClEC,EAAA,CAAAC,cAAA,eAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,QAAAC,YAAA,CAAAC,IAAA,MACF;;;;;IAJFP,EAAA,CAAAC,cAAA,qBAA2E;IACzED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAQ,UAAA,IAAAC,kDAAA,mBAEO;IACTT,EAAA,CAAAG,YAAA,EAAa;;;;IAL2CH,EAAA,CAAAU,UAAA,UAAAJ,YAAA,CAAkB;IACxEN,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAW,kBAAA,MAAAL,YAAA,CAAAM,IAAA,OAAAN,YAAA,CAAAO,IAAA,SAAAP,YAAA,CAAAO,IAAA,iBACA;IAAOb,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAU,UAAA,SAAAJ,YAAA,CAAAQ,IAAA,UAAAR,YAAA,CAAAC,IAAA,CAA0C;;;;;IAsCjDP,EAAA,CAAAC,cAAA,eAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,QAAAU,YAAA,CAAAR,IAAA,MACF;;;;;IAJFP,EAAA,CAAAC,cAAA,qBAAyE;IACvED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAQ,UAAA,IAAAQ,kDAAA,mBAEO;IACThB,EAAA,CAAAG,YAAA,EAAa;;;;IALyCH,EAAA,CAAAU,UAAA,UAAAK,YAAA,CAAkB;IACtEf,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAW,kBAAA,MAAAI,YAAA,CAAAH,IAAA,OAAAG,YAAA,CAAAF,IAAA,SAAAE,YAAA,CAAAF,IAAA,iBACA;IAAOb,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAU,UAAA,SAAAK,YAAA,CAAAD,IAAA,UAAAC,YAAA,CAAAR,IAAA,CAA0C;;;;;IA6BrDP,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAU,UAAA,UAAAO,QAAA,CAAAC,KAAA,CAAoB;IAClElB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAY,QAAA,CAAAE,KAAA,MACF;;;;;IAmBFnB,EAAA,CAAAC,cAAA,qBAAkF;IAChFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFyCH,EAAA,CAAAU,UAAA,UAAAU,eAAA,CAAAF,KAAA,CAA2B;IAC/ElB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAe,eAAA,CAAAD,KAAA,MACF;;;;;IA2BZnB,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAqB,SAAA,sBAAwD;IACxDrB,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAG7DH,EAAA,CAAAC,cAAA,cAAgE;IAE5DD,EAAA,CAAAqB,SAAA,YAAyC;IAC3CrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAtBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAsB,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;IAG7CxB,EAAA,CAAAC,cAAA,cAAmH;IAE/GD,EAAA,CAAAqB,SAAA,WAA6B;IAC/BrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,4BAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yDAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IA4CtCH,EAAA,CAAAC,cAAA,eAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,OAAAoB,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,qBACF;;;;;IAmBJ5B,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAqB,SAAA,YAA4B;IAC5BrB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEzBH,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAqB,SAAA,aAA0B;IAC1BrB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApDH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAsB,iBAAA,CAAAG,UAAA,CAAAI,MAAA,qBAAAJ,UAAA,CAAAI,MAAA,IAAAC,eAAA,kBAAAL,UAAA,CAAAI,MAAA,IAAAC,eAAA,CAAAlB,IAAA,CAA6C;;;;;;IA7D7DZ,EAAA,CAAAC,cAAA,cAA8E;IAGjDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAA0B;IACAD,EAAA,CAAAE,MAAA,GAAqE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpGH,EAAA,CAAAqB,SAAA,YAA2C;IAC3CrB,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAuG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGxIH,EAAA,CAAAC,cAAA,eAA0B;IACCD,EAAA,CAAAE,MAAA,wBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAI7DH,EAAA,CAAAC,cAAA,eAAiC;IAKvBD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAqB,SAAA,aAA4B;IAC5BrB,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAqB,SAAA,eAA+B;IAC/BrB,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAQ,UAAA,KAAAuB,kDAAA,kBAEM;IACR/B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAqB,SAAA,eAA+B;IACjCrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAEvBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIVH,EAAA,CAAAC,cAAA,eAA6B;IAEzBD,EAAA,CAAAqB,SAAA,aAA+B;IAC/BrB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7BH,EAAA,CAAAQ,UAAA,KAAAwB,kDAAA,kBAGM;IACNhC,EAAA,CAAAQ,UAAA,KAAAyB,kDAAA,kBAGM;IACRjC,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAA4B;IACUD,EAAA,CAAAkC,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,WAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA;MAAA,MAAAb,UAAA,GAAAW,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAyC,aAAA;MAAA,OAASzC,EAAA,CAAA0C,WAAA,CAAAF,OAAA,CAAAG,iBAAA,CAAAlB,UAAA,CAAyB;IAAA,EAAC;IACrEzB,EAAA,CAAAqB,SAAA,cAAkC;IAClCrB,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAwE;IAAnCD,EAAA,CAAAkC,UAAA,mBAAAU,qEAAA;MAAA,MAAAR,WAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA;MAAA,MAAAb,UAAA,GAAAW,WAAA,CAAAG,SAAA;MAAA,MAAAM,OAAA,GAAA7C,EAAA,CAAAyC,aAAA;MAAA,OAASzC,EAAA,CAAA0C,WAAA,CAAAG,OAAA,CAAAC,gBAAA,CAAArB,UAAA,CAAwB;IAAA,EAAC;IACrEzB,EAAA,CAAAqB,SAAA,cAAmC;IACnCrB,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAvEcH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAsB,iBAAA,CAAAG,UAAA,CAAAsB,EAAA,CAAe;IAEZ/C,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAsB,iBAAA,EAAAG,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,qBAAAF,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,kBAAAvB,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,CAAAzC,IAAA,kBAAAkB,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,CAAAzC,IAAA,CAAAK,IAAA,mBAAqE;IAEvEZ,EAAA,CAAAI,SAAA,GAAuG;IAAvGJ,EAAA,CAAAsB,iBAAA,EAAAG,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,wBAAAH,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,kBAAAxB,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,CAAA1C,IAAA,kBAAAkB,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,CAAA1C,IAAA,CAAAK,IAAA,oBAAuG;IAKrGZ,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAsB,iBAAA,CAAA4B,OAAA,CAAAC,WAAA,CAAA1B,UAAA,EAAyB;IAS7CzB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAoD,WAAA,SAAA3B,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,qBAAAF,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,kBAAAvB,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,CAAAK,IAAA,gBACF;IAGErD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA6C,OAAA,CAAAI,cAAA,EAAA7B,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAA6B,QAAA,aACF;IAEEvD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAoD,WAAA,SAAA3B,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,wBAAAH,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,kBAAAxB,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,CAAAI,IAAA,gBACF;IAIwBrD,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAwD,WAAA,eAAA/B,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,MAAwD;IACxE5B,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAU,UAAA,UAAAe,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,MAA0C;IAQhD5B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,OAAAoB,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,qBAAAF,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,kBAAAvB,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,CAAAS,OAAA,kBAAAhC,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,CAAAS,OAAA,CAAA5C,IAAA,aACF;IAEEb,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,OAAAoB,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,wBAAAH,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,kBAAAxB,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,CAAAQ,OAAA,kBAAAhC,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,CAAAQ,OAAA,CAAA5C,IAAA,aACF;IASoBb,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAU,UAAA,UAAAe,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,QAA4C;IAI5C5B,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAU,UAAA,SAAAe,UAAA,CAAAI,MAAA,qBAAAJ,UAAA,CAAAI,MAAA,IAAAC,eAAA,kBAAAL,UAAA,CAAAI,MAAA,IAAAC,eAAA,CAAAlB,IAAA,CAA6C;;;;;IAlE/EZ,EAAA,CAAAC,cAAA,cAAyE;IAErED,EAAA,CAAAqB,SAAA,YAA4B;IAC5BrB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAQ,UAAA,IAAAkD,2CAAA,oBA6EM;IACR1D,EAAA,CAAAG,YAAA,EAAM;;;;IAlFJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,wBAAAsD,OAAA,CAAAC,aAAA,CAAAhC,MAAA,OACF;IAG0B5B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAU,UAAA,YAAAiD,OAAA,CAAAC,aAAA,CAAgB;;;ADzKlD,OAAM,MAAOC,oBAAoB;EA2B/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IA5BhB,KAAAC,kBAAkB,GAAqB,EAAE;IACzC,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAR,aAAa,GAAa,EAAE;IAC5B,KAAAS,WAAW,GAAG,KAAK;IACnB,KAAA7C,YAAY,GAAG,EAAE;IACjB,KAAA8C,YAAY,GAAG,EAAE;IAEjB;IACA,KAAAC,cAAc,GAAG,CACf;MAAErD,KAAK,EAAEnB,aAAa,CAACyE,KAAK;MAAErD,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAEnB,aAAa,CAAC0E,KAAK;MAAEtD,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAEnB,aAAa,CAAC2E,MAAM;MAAEvD,KAAK,EAAE;IAAQ,CAAE,CACjD;IAED;IACA,KAAAwD,aAAa,GAAG,CACd;MAAEzD,KAAK,EAAErB,eAAe,CAAC+E,KAAK;MAAEzD,KAAK,EAAE;IAAO,CAAE,EAChD;MAAED,KAAK,EAAErB,eAAe,CAACgF,OAAO;MAAE1D,KAAK,EAAE;IAAS,CAAE,EACpD;MAAED,KAAK,EAAErB,eAAe,CAACiF,QAAQ;MAAE3D,KAAK,EAAE;IAAU,CAAE,CACvD;IAUC;IACA,IAAI,CAAC4D,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAErD;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACpB,EAAE,CAACqB,KAAK,CAAC;MAC9B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAE9F,UAAU,CAAC+F,QAAQ,CAAC;MACrCC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,EAAEhG,UAAU,CAAC+F,QAAQ,CAAC;MAC1CE,iBAAiB,EAAE,CAAC,EAAE,EAAEjG,UAAU,CAAC+F,QAAQ,CAAC;MAC5CG,qBAAqB,EAAE,CAAC,CAAC,EAAElG,UAAU,CAAC+F,QAAQ,CAAC;MAC/CI,eAAe,EAAE,CAAC,EAAE,EAAEnG,UAAU,CAAC+F,QAAQ,CAAC;MAC1CK,mBAAmB,EAAE,CAAC,CAAC,EAAEpG,UAAU,CAAC+F,QAAQ,CAAC;MAC7CM,aAAa,EAAE,CAAC,IAAI,CAACb,OAAO,EAAExF,UAAU,CAAC+F,QAAQ,CAAC;MAClDO,cAAc,EAAE,CAAC,CAAC,EAAE,CAACtG,UAAU,CAAC+F,QAAQ,EAAE/F,UAAU,CAACuG,GAAG,CAAC,CAAC,CAAC,EAAEvG,UAAU,CAACwG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChFC,aAAa,EAAE,CAAC,CAAC,EAAEzG,UAAU,CAAC+F,QAAQ,CAAC;MAEvC;MACAW,WAAW,EAAE,CAAC,CAAC,EAAE1G,UAAU,CAAC+F,QAAQ,CAAC;MACrCY,OAAO,EAAE,CAAC,KAAK,CAAC;MAEhB;MACAC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBC,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,wBAAwB,EAAE,CAAC,KAAK,CAAC;MACjCC,6BAA6B,EAAE,CAAC,IAAI,CAAC;MACrCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAE5B;MACAC,sBAAsB,EAAE,CAAC,CAAC;KAC3B,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACAC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EACjF;EAEA;EACAnE,iBAAiBA,CAACoE,MAAc;IAC9B;IACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9CF,QAAQ,CAACG,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCJ,QAAQ,CAACG,KAAK,CAACE,GAAG,GAAG,GAAG;IACxBL,QAAQ,CAACG,KAAK,CAACG,IAAI,GAAG,GAAG;IACzBN,QAAQ,CAACG,KAAK,CAACI,KAAK,GAAG,MAAM;IAC7BP,QAAQ,CAACG,KAAK,CAACK,MAAM,GAAG,MAAM;IAC9BR,QAAQ,CAACG,KAAK,CAACM,eAAe,GAAG,oBAAoB;IACrDT,QAAQ,CAACG,KAAK,CAACO,MAAM,GAAG,MAAM;IAC9BV,QAAQ,CAACG,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC/BX,QAAQ,CAACG,KAAK,CAACS,cAAc,GAAG,QAAQ;IACxCZ,QAAQ,CAACG,KAAK,CAACU,UAAU,GAAG,QAAQ;IAEpC;IACA,MAAMC,YAAY,GAAGb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDY,YAAY,CAACX,KAAK,CAACM,eAAe,GAAG,OAAO;IAC5CK,YAAY,CAACX,KAAK,CAACY,OAAO,GAAG,MAAM;IACnCD,YAAY,CAACX,KAAK,CAACa,YAAY,GAAG,MAAM;IACxCF,YAAY,CAACX,KAAK,CAACc,QAAQ,GAAG,KAAK;IACnCH,YAAY,CAACX,KAAK,CAACe,SAAS,GAAG,KAAK;IACpCJ,YAAY,CAACX,KAAK,CAACgB,QAAQ,GAAG,MAAM;IACpCL,YAAY,CAACX,KAAK,CAACC,QAAQ,GAAG,UAAU;IACxCU,YAAY,CAACX,KAAK,CAACiB,SAAS,GAAG,gCAAgC;IAC/DN,YAAY,CAACX,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCP,YAAY,CAACX,KAAK,CAACmB,UAAU,GAAG,mBAAmB;IAEnD;IACA,MAAMC,WAAW,GAAGtB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACpDqB,WAAW,CAACC,SAAS,GAAG,SAAS;IACjCD,WAAW,CAACpB,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvCmB,WAAW,CAACpB,KAAK,CAACE,GAAG,GAAG,MAAM;IAC9BkB,WAAW,CAACpB,KAAK,CAACsB,KAAK,GAAG,MAAM;IAChCF,WAAW,CAACpB,KAAK,CAACuB,MAAM,GAAG,MAAM;IACjCH,WAAW,CAACpB,KAAK,CAACwB,UAAU,GAAG,MAAM;IACrCJ,WAAW,CAACpB,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACnCL,WAAW,CAACpB,KAAK,CAAC0B,MAAM,GAAG,SAAS;IACpCN,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnCE,WAAW,CAACpB,KAAK,CAAC2B,UAAU,GAAG,YAAY;IAC3CP,WAAW,CAACQ,WAAW,GAAG,MAAMR,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnEE,WAAW,CAACS,UAAU,GAAG,MAAMT,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IAClEE,WAAW,CAACU,OAAO,GAAG,MAAMhC,QAAQ,CAACiC,IAAI,CAACC,WAAW,CAACnC,QAAQ,CAAC;IAE/D;IACA,MAAMoC,MAAM,GAAGnC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5CkC,MAAM,CAACjC,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC7ByB,MAAM,CAACjC,KAAK,CAACU,UAAU,GAAG,QAAQ;IAClCuB,MAAM,CAACjC,KAAK,CAACkC,YAAY,GAAG,MAAM;IAClCD,MAAM,CAACjC,KAAK,CAACmC,aAAa,GAAG,MAAM;IACnCF,MAAM,CAACjC,KAAK,CAACoC,YAAY,GAAG,gBAAgB;IAE5C,MAAMC,IAAI,GAAGvC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CsC,IAAI,CAAChB,SAAS,GAAG,2FAA2F;IAE5G,MAAMiB,KAAK,GAAGxC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IAC1CuC,KAAK,CAACC,WAAW,GAAG,yBAAyB;IAC7CD,KAAK,CAACtC,KAAK,CAACwC,MAAM,GAAG,GAAG;IACxBF,KAAK,CAACtC,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC7Ba,KAAK,CAACtC,KAAK,CAACyC,UAAU,GAAG,KAAK;IAC9BH,KAAK,CAACtC,KAAK,CAACkB,KAAK,GAAG,SAAS;IAE7Be,MAAM,CAACS,WAAW,CAACL,IAAI,CAAC;IACxBJ,MAAM,CAACS,WAAW,CAACJ,KAAK,CAAC;IAEzB;IACA,MAAMK,gBAAgB,GAAG7C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACtD4C,gBAAgB,CAAC3C,KAAK,CAACQ,OAAO,GAAG,MAAM;IACvCmC,gBAAgB,CAAC3C,KAAK,CAAC4C,aAAa,GAAG,QAAQ;IAC/CD,gBAAgB,CAAC3C,KAAK,CAAC6C,GAAG,GAAG,MAAM;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9E;IACA,IAAInD,MAAM,CAACrF,KAAK,IAAIqF,MAAM,CAACrF,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMuI,IAAI,GAAGpD,MAAM,CAACrF,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,IAAIyI,IAAI,CAACC,OAAO,EAAE;QAChB,MAAMC,WAAW,GAAGpD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDmD,WAAW,CAAClD,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClC0C,WAAW,CAAClD,KAAK,CAACU,UAAU,GAAG,QAAQ;QACvCwC,WAAW,CAAClD,KAAK,CAACkC,YAAY,GAAG,MAAM;QAEvC;QACA,IAAIc,IAAI,CAACC,OAAO,CAACE,aAAa,EAAE;UAC9B,MAAMC,WAAW,GAAGtD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDqD,WAAW,CAACC,GAAG,GAAGL,IAAI,CAACC,OAAO,CAACE,aAAa;UAC5CC,WAAW,CAACE,GAAG,GAAGN,IAAI,CAACC,OAAO,CAACxJ,IAAI;UACnC2J,WAAW,CAACpD,KAAK,CAACK,MAAM,GAAG,MAAM;UACjC+C,WAAW,CAACpD,KAAK,CAACuD,WAAW,GAAG,MAAM;UACtCL,WAAW,CAACR,WAAW,CAACU,WAAW,CAAC;SACrC,MAAM;UACL,MAAMI,WAAW,GAAG1D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDyD,WAAW,CAACnC,SAAS,GAAG,wFAAwF;UAChH6B,WAAW,CAACR,WAAW,CAACc,WAAW,CAAC;;QAGtC,MAAMC,WAAW,GAAG3D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjD0D,WAAW,CAACpC,SAAS,GAAG,WAAW2B,IAAI,CAACC,OAAO,CAACxJ,IAAI,cAAcuJ,IAAI,CAACC,OAAO,CAACS,iBAAiB,GAAG;QACnGD,WAAW,CAACzD,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCyB,WAAW,CAACR,WAAW,CAACe,WAAW,CAAC;QAEpCX,WAAW,CAACJ,WAAW,CAACQ,WAAW,CAAC;;MAGtC;MACA,MAAMS,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC,eAAe,EAAEZ,IAAI,CAACa,QAAQ,IAAI,KAAK,CAAC;MACnFf,WAAW,CAACJ,WAAW,CAACiB,eAAe,CAAC;MAExC;MACA,MAAMG,aAAa,GAAG,IAAI,CAACF,aAAa,CAAC,aAAa,EAAE,IAAI/F,IAAI,CAACmF,IAAI,CAACe,UAAU,CAAC,CAACC,kBAAkB,EAAE,CAAC;MACvGlB,WAAW,CAACJ,WAAW,CAACoB,aAAa,CAAC;MAEtC;MACA,MAAMG,WAAW,GAAG,IAAI,CAACL,aAAa,CAAC,UAAU,EAAE,IAAI,CAACzH,cAAc,CAAC6G,IAAI,CAAC5G,QAAQ,CAAC,CAAC;MACtF0G,WAAW,CAACJ,WAAW,CAACuB,WAAW,CAAC;MAEpC;MACA,IAAIjB,IAAI,CAAClE,WAAW,EAAE;QACpB,MAAMoF,QAAQ,GAAG,IAAI,CAACN,aAAa,CAAC,OAAO,EAAE,GAAGZ,IAAI,CAAClE,WAAW,CAACrF,IAAI,KAAKuJ,IAAI,CAAClE,WAAW,CAACpF,IAAI,GAAG,CAAC;QACnGoJ,WAAW,CAACJ,WAAW,CAACwB,QAAQ,CAAC;;MAGnC;MACA,MAAMC,QAAQ,GAAG,IAAI,CAACP,aAAa,CAAC,OAAO,EAAEZ,IAAI,CAACoB,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,GAAGpB,IAAI,CAACoB,SAAS,UAAU,CAAC;MAClHtB,WAAW,CAACJ,WAAW,CAACyB,QAAQ,CAAC;;IAGnC;IACA,MAAME,YAAY,GAAG,IAAI,CAACtB,aAAa,CAAC,eAAe,EAAE,UAAU,CAAC;IAEpE,IAAInD,MAAM,CAACrF,KAAK,IAAIqF,MAAM,CAACrF,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMuI,IAAI,GAAGpD,MAAM,CAACrF,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,MAAM+J,WAAW,GAAGxE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACjDuE,WAAW,CAACtE,KAAK,CAACQ,OAAO,GAAG,MAAM;MAClC8D,WAAW,CAACtE,KAAK,CAACU,UAAU,GAAG,QAAQ;MACvC4D,WAAW,CAACtE,KAAK,CAACS,cAAc,GAAG,eAAe;MAClD6D,WAAW,CAACtE,KAAK,CAACwC,MAAM,GAAG,QAAQ;MACnC8B,WAAW,CAACtE,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEvC;MACA,MAAMpE,SAAS,GAAGiE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/ClE,SAAS,CAACmE,KAAK,CAACuE,SAAS,GAAG,QAAQ;MACpC1I,SAAS,CAACmE,KAAK,CAACwE,IAAI,GAAG,GAAG;MAE1B,IAAIxB,IAAI,CAACnH,SAAS,EAAE;QAClB,MAAM4I,aAAa,GAAG3E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnD0E,aAAa,CAACzE,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCgD,aAAa,CAACzE,KAAK,CAACyC,UAAU,GAAG,MAAM;QACvCgC,aAAa,CAAClC,WAAW,GAAG,IAAI1E,IAAI,CAACmF,IAAI,CAACnH,SAAS,CAACK,IAAI,CAAC,CAACwI,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAErH,MAAMC,gBAAgB,GAAG/E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACtD8E,gBAAgB,CAAC7E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACxCoD,gBAAgB,CAAC7E,KAAK,CAAC8E,SAAS,GAAG,KAAK;QACxCD,gBAAgB,CAACxD,SAAS,GAAG,WAAW2B,IAAI,CAACnH,SAAS,CAACS,OAAO,EAAE5C,IAAI,IAAI,KAAK,WAAW;QAExF,MAAMqL,aAAa,GAAGjF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnDgF,aAAa,CAAC/E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCsD,aAAa,CAAC/E,KAAK,CAACkB,KAAK,GAAG,MAAM;QAClC6D,aAAa,CAACxC,WAAW,GAAGS,IAAI,CAACnH,SAAS,CAACzC,IAAI,EAAEK,IAAI,IAAI,KAAK;QAE9DoC,SAAS,CAAC6G,WAAW,CAAC+B,aAAa,CAAC;QACpC5I,SAAS,CAAC6G,WAAW,CAACmC,gBAAgB,CAAC;QACvChJ,SAAS,CAAC6G,WAAW,CAACqC,aAAa,CAAC;;MAGtC;MACA,MAAMC,cAAc,GAAGlF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACpDiF,cAAc,CAAChF,KAAK,CAACwE,IAAI,GAAG,GAAG;MAC/BQ,cAAc,CAAChF,KAAK,CAACQ,OAAO,GAAG,MAAM;MACrCwE,cAAc,CAAChF,KAAK,CAACU,UAAU,GAAG,QAAQ;MAC1CsE,cAAc,CAAChF,KAAK,CAACS,cAAc,GAAG,QAAQ;MAC9CuE,cAAc,CAAChF,KAAK,CAACY,OAAO,GAAG,QAAQ;MAEvC,MAAMqE,IAAI,GAAGnF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC1CkF,IAAI,CAACjF,KAAK,CAACK,MAAM,GAAG,KAAK;MACzB4E,IAAI,CAACjF,KAAK,CAACM,eAAe,GAAG,MAAM;MACnC2E,IAAI,CAACjF,KAAK,CAACI,KAAK,GAAG,MAAM;MACzB6E,IAAI,CAACjF,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEhC,MAAMiF,KAAK,GAAGpF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC3CmF,KAAK,CAAC7D,SAAS,GAAG,iGAAiG;MACnH6D,KAAK,CAAClF,KAAK,CAACC,QAAQ,GAAG,UAAU;MACjCiF,KAAK,CAAClF,KAAK,CAACE,GAAG,GAAG,MAAM;MACxBgF,KAAK,CAAClF,KAAK,CAACG,IAAI,GAAG,KAAK;MACxB+E,KAAK,CAAClF,KAAK,CAACmF,UAAU,GAAG,MAAM;MAC/BD,KAAK,CAAClF,KAAK,CAACM,eAAe,GAAG,OAAO;MACrC4E,KAAK,CAAClF,KAAK,CAACY,OAAO,GAAG,OAAO;MAE7BqE,IAAI,CAACvC,WAAW,CAACwC,KAAK,CAAC;MACvBF,cAAc,CAACtC,WAAW,CAACuC,IAAI,CAAC;MAEhC;MACA,MAAMnJ,OAAO,GAAGgE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CjE,OAAO,CAACkE,KAAK,CAACuE,SAAS,GAAG,QAAQ;MAClCzI,OAAO,CAACkE,KAAK,CAACwE,IAAI,GAAG,GAAG;MAExB,IAAIxB,IAAI,CAAClH,OAAO,EAAE;QAChB,MAAMsJ,WAAW,GAAGtF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDqF,WAAW,CAACpF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnC2D,WAAW,CAACpF,KAAK,CAACyC,UAAU,GAAG,MAAM;QACrC2C,WAAW,CAAC7C,WAAW,GAAG,IAAI1E,IAAI,CAACmF,IAAI,CAAClH,OAAO,CAACI,IAAI,CAAC,CAACwI,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAEjH,MAAMS,cAAc,GAAGvF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACpDsF,cAAc,CAACrF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACtC4D,cAAc,CAACrF,KAAK,CAAC8E,SAAS,GAAG,KAAK;QACtCO,cAAc,CAAChE,SAAS,GAAG,WAAW2B,IAAI,CAAClH,OAAO,CAACQ,OAAO,EAAE5C,IAAI,IAAI,KAAK,WAAW;QAEpF,MAAM4L,WAAW,GAAGxF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDuF,WAAW,CAACtF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnC6D,WAAW,CAACtF,KAAK,CAACkB,KAAK,GAAG,MAAM;QAChCoE,WAAW,CAAC/C,WAAW,GAAGS,IAAI,CAAClH,OAAO,CAAC1C,IAAI,EAAEK,IAAI,IAAI,KAAK;QAE1DqC,OAAO,CAAC4G,WAAW,CAAC0C,WAAW,CAAC;QAChCtJ,OAAO,CAAC4G,WAAW,CAAC2C,cAAc,CAAC;QACnCvJ,OAAO,CAAC4G,WAAW,CAAC4C,WAAW,CAAC;;MAGlChB,WAAW,CAAC5B,WAAW,CAAC7G,SAAS,CAAC;MAClCyI,WAAW,CAAC5B,WAAW,CAACsC,cAAc,CAAC;MACvCV,WAAW,CAAC5B,WAAW,CAAC5G,OAAO,CAAC;MAEhCuI,YAAY,CAAC3B,WAAW,CAAC4B,WAAW,CAAC;MAErC;MACA,IAAItB,IAAI,CAACxI,QAAQ,IAAIwI,IAAI,CAACxI,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAM8K,aAAa,GAAGzF,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAClDwF,aAAa,CAAChD,WAAW,GAAG,iBAAiB;QAC7CgD,aAAa,CAACvF,KAAK,CAAC8E,SAAS,GAAG,MAAM;QACtCS,aAAa,CAACvF,KAAK,CAACkC,YAAY,GAAG,MAAM;QACzCqD,aAAa,CAACvF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC8D,aAAa,CAACvF,KAAK,CAACyC,UAAU,GAAG,KAAK;QACtC4B,YAAY,CAAC3B,WAAW,CAAC6C,aAAa,CAAC;QAEvC,MAAMC,YAAY,GAAG1F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClDyF,YAAY,CAACxF,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnCgF,YAAY,CAACxF,KAAK,CAAC4C,aAAa,GAAG,QAAQ;QAC3C4C,YAAY,CAACxF,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/BG,IAAI,CAACxI,QAAQ,CAACiL,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAI;UACvC,MAAMC,WAAW,GAAG9F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjD6F,WAAW,CAAC5F,KAAK,CAACY,OAAO,GAAG,MAAM;UAClCgF,WAAW,CAAC5F,KAAK,CAACM,eAAe,GAAG,SAAS;UAC7CsF,WAAW,CAAC5F,KAAK,CAACa,YAAY,GAAG,KAAK;UACtC+E,WAAW,CAAC5F,KAAK,CAACuB,MAAM,GAAG,gBAAgB;UAE3C,MAAMsE,aAAa,GAAG/F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACnD8F,aAAa,CAAC7F,KAAK,CAACQ,OAAO,GAAG,MAAM;UACpCqF,aAAa,CAAC7F,KAAK,CAACS,cAAc,GAAG,eAAe;UACpDoF,aAAa,CAAC7F,KAAK,CAACkC,YAAY,GAAG,MAAM;UACzC2D,aAAa,CAAC7F,KAAK,CAACmC,aAAa,GAAG,MAAM;UAC1C0D,aAAa,CAAC7F,KAAK,CAACoC,YAAY,GAAG,gBAAgB;UAEnD,MAAM0D,YAAY,GAAGhG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClD+F,YAAY,CAACzE,SAAS,GAAG,mBAAmBsE,KAAK,GAAG,CAAC,cAAcD,OAAO,CAACzC,OAAO,EAAExJ,IAAI,IAAI,SAAS,IAAIiM,OAAO,CAAC7B,QAAQ,EAAE;UAE3H,MAAMkC,eAAe,GAAGjG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACrDgG,eAAe,CAACxD,WAAW,GAAG,IAAI,CAACpG,cAAc,CAACuJ,OAAO,CAACtJ,QAAQ,CAAC;UACnE2J,eAAe,CAAC/F,KAAK,CAACkB,KAAK,GAAG,MAAM;UAEpC2E,aAAa,CAACnD,WAAW,CAACoD,YAAY,CAAC;UACvCD,aAAa,CAACnD,WAAW,CAACqD,eAAe,CAAC;UAC1CH,WAAW,CAAClD,WAAW,CAACmD,aAAa,CAAC;UAEtC;UACA,MAAMG,YAAY,GAAGlG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClDiG,YAAY,CAAChG,KAAK,CAACQ,OAAO,GAAG,MAAM;UACnCwF,YAAY,CAAChG,KAAK,CAACU,UAAU,GAAG,QAAQ;UACxCsF,YAAY,CAAChG,KAAK,CAACS,cAAc,GAAG,eAAe;UAEnD;UACA,MAAMwF,gBAAgB,GAAGnG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACtDkG,gBAAgB,CAACjG,KAAK,CAACwE,IAAI,GAAG,GAAG;UAEjC,IAAIkB,OAAO,CAAC7J,SAAS,EAAE;YACrB,MAAMqK,OAAO,GAAGpG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CmG,OAAO,CAAClG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCyD,OAAO,CAAC3D,WAAW,GAAG,IAAI1E,IAAI,CAAC6H,OAAO,CAAC7J,SAAS,CAACK,IAAI,CAAC,CAACwI,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAElH,MAAMuB,UAAU,GAAGrG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChDoG,UAAU,CAACnG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClC0E,UAAU,CAAC5D,WAAW,GAAG,GAAGmD,OAAO,CAAC7J,SAAS,CAACS,OAAO,EAAE5C,IAAI,IAAI,KAAK,KAAKgM,OAAO,CAAC7J,SAAS,CAACzC,IAAI,EAAEK,IAAI,IAAI,KAAK,GAAG;YAEjHwM,gBAAgB,CAACvD,WAAW,CAACwD,OAAO,CAAC;YACrCD,gBAAgB,CAACvD,WAAW,CAACyD,UAAU,CAAC;;UAG1C;UACA,MAAMC,KAAK,GAAGtG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAC3CqG,KAAK,CAAC/E,SAAS,GAAG,qFAAqF;UAEvG;UACA,MAAMgF,cAAc,GAAGvG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACpDsG,cAAc,CAACrG,KAAK,CAACwE,IAAI,GAAG,GAAG;UAC/B6B,cAAc,CAACrG,KAAK,CAACuE,SAAS,GAAG,OAAO;UAExC,IAAImB,OAAO,CAAC5J,OAAO,EAAE;YACnB,MAAMwK,OAAO,GAAGxG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CuG,OAAO,CAACtG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjC6D,OAAO,CAAC/D,WAAW,GAAG,IAAI1E,IAAI,CAAC6H,OAAO,CAAC5J,OAAO,CAACI,IAAI,CAAC,CAACwI,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAEhH,MAAM2B,UAAU,GAAGzG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChDwG,UAAU,CAACvG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClC8E,UAAU,CAAChE,WAAW,GAAG,GAAGmD,OAAO,CAAC5J,OAAO,CAACQ,OAAO,EAAE5C,IAAI,IAAI,KAAK,KAAKgM,OAAO,CAAC5J,OAAO,CAAC1C,IAAI,EAAEK,IAAI,IAAI,KAAK,GAAG;YAE7G4M,cAAc,CAAC3D,WAAW,CAAC4D,OAAO,CAAC;YACnCD,cAAc,CAAC3D,WAAW,CAAC6D,UAAU,CAAC;;UAGxCP,YAAY,CAACtD,WAAW,CAACuD,gBAAgB,CAAC;UAC1CD,YAAY,CAACtD,WAAW,CAAC0D,KAAK,CAAC;UAC/BJ,YAAY,CAACtD,WAAW,CAAC2D,cAAc,CAAC;UACxCT,WAAW,CAAClD,WAAW,CAACsD,YAAY,CAAC;UAErCR,YAAY,CAAC9C,WAAW,CAACkD,WAAW,CAAC;UAErC;UACA,IAAID,KAAK,GAAG3C,IAAI,CAACxI,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACpC,MAAM+L,OAAO,GAAG1G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CyG,OAAO,CAACxG,KAAK,CAACuE,SAAS,GAAG,QAAQ;YAClCiC,OAAO,CAACxG,KAAK,CAACY,OAAO,GAAG,MAAM;YAC9B4F,OAAO,CAACxG,KAAK,CAACkB,KAAK,GAAG,SAAS;YAC/BsF,OAAO,CAACxG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAE/B;YACA,MAAMgF,cAAc,GAAG,IAAI5I,IAAI,CAAC6H,OAAO,CAAC5J,OAAO,EAAEI,IAAI,IAAI,CAAC,CAAC,CAACwK,OAAO,EAAE;YACrE,MAAMC,aAAa,GAAG,IAAI9I,IAAI,CAACmF,IAAI,CAACxI,QAAQ,CAACmL,KAAK,GAAG,CAAC,CAAC,CAAC9J,SAAS,EAAEK,IAAI,IAAI,CAAC,CAAC,CAACwK,OAAO,EAAE;YACvF,MAAME,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,aAAa,GAAGF,cAAc,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;YAE9ED,OAAO,CAACnF,SAAS,GAAG,yCAAyC,IAAI,CAAClF,cAAc,CAACyK,WAAW,CAAC,eAAelB,OAAO,CAAC5J,OAAO,EAAE1C,IAAI,EAAEK,IAAI,IAAI,iBAAiB,EAAE;YAE9J+L,YAAY,CAAC9C,WAAW,CAAC8D,OAAO,CAAC;;QAErC,CAAC,CAAC;QAEFnC,YAAY,CAAC3B,WAAW,CAAC8C,YAAY,CAAC;;;IAI1C;IACA,MAAMuB,aAAa,GAAG,IAAI,CAAChE,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAE5D,IAAInD,MAAM,CAAClF,MAAM,IAAIkF,MAAM,CAAClF,MAAM,CAACD,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAMuM,UAAU,GAAGlH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAChDiH,UAAU,CAAChH,KAAK,CAACQ,OAAO,GAAG,MAAM;MACjCwG,UAAU,CAAChH,KAAK,CAAC4C,aAAa,GAAG,QAAQ;MACzCoE,UAAU,CAAChH,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE7BjD,MAAM,CAAClF,MAAM,CAAC+K,OAAO,CAAC,CAACwB,KAAK,EAAEtB,KAAK,KAAI;QACrC,MAAMuB,SAAS,GAAGpH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC/CmH,SAAS,CAAClH,KAAK,CAACY,OAAO,GAAG,MAAM;QAChCsG,SAAS,CAAClH,KAAK,CAACM,eAAe,GAAG,SAAS;QAC3C4G,SAAS,CAAClH,KAAK,CAACa,YAAY,GAAG,KAAK;QACpCqG,SAAS,CAAClH,KAAK,CAACuB,MAAM,GAAG,gBAAgB;QAEzC;QACA,MAAM4F,WAAW,GAAGrH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDoH,WAAW,CAACnH,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClC2G,WAAW,CAACnH,KAAK,CAACS,cAAc,GAAG,eAAe;QAClD0G,WAAW,CAACnH,KAAK,CAACkC,YAAY,GAAG,MAAM;QACvCiF,WAAW,CAACnH,KAAK,CAACmC,aAAa,GAAG,MAAM;QACxCgF,WAAW,CAACnH,KAAK,CAACoC,YAAY,GAAG,gBAAgB;QAEjD,MAAMgF,UAAU,GAAGtH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChDqH,UAAU,CAAC/F,SAAS,GAAG,iBAAiBsE,KAAK,GAAG,CAAC,WAAW;QAC5DyB,UAAU,CAACpH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAElC,MAAM4F,UAAU,GAAGvH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChDsH,UAAU,CAACrH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAClC4F,UAAU,CAACrH,KAAK,CAACyC,UAAU,GAAG,MAAM;QACpC4E,UAAU,CAACrH,KAAK,CAACkB,KAAK,GAAG,SAAS;QAClCmG,UAAU,CAAC9E,WAAW,GAAG,GAAG0E,KAAK,CAACK,KAAK,CAACC,MAAM,IAAIN,KAAK,CAACK,KAAK,CAACrI,QAAQ,EAAE;QAExEkI,WAAW,CAACzE,WAAW,CAAC0E,UAAU,CAAC;QACnCD,WAAW,CAACzE,WAAW,CAAC2E,UAAU,CAAC;QACnCH,SAAS,CAACxE,WAAW,CAACyE,WAAW,CAAC;QAElC;QACA,MAAMK,YAAY,GAAG1H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClDyH,YAAY,CAACxH,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnCgH,YAAY,CAACxH,KAAK,CAACyH,mBAAmB,GAAG,uCAAuC;QAChFD,YAAY,CAACxH,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B;QACA,MAAM6E,OAAO,GAAG,IAAI,CAAC9D,aAAa,CAAC,UAAU,EAAEqD,KAAK,CAACS,OAAO,IAAIT,KAAK,CAACrL,EAAE,IAAI,KAAK,CAAC;QAClF8L,OAAO,CAAC1H,KAAK,CAAC2H,UAAU,GAAG,QAAQ;QACnCH,YAAY,CAAC9E,WAAW,CAACgF,OAAO,CAAC;QAEjC;QACA,MAAME,iBAAiB,GAAGX,KAAK,CAACY,YAAY,KAAKC,SAAS,GAAGb,KAAK,CAACY,YAAY,GACrDZ,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACC,kBAAkB,GAAG,CAAE;QACjF,MAAMH,YAAY,GAAG,IAAI,CAACjE,aAAa,CAAC,cAAc,EAAEgE,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,eAAe,CAAC;QAC9GJ,YAAY,CAAC9E,WAAW,CAACmF,YAAY,CAAC;QAEtC;QACA,IAAIZ,KAAK,CAACgB,SAAS,EAAE;UACnB,MAAMC,OAAO,GAAG,IAAI,CAACtE,aAAa,CAAC,YAAY,EAAE,IAAI/F,IAAI,CAACoJ,KAAK,CAACgB,SAAS,CAAC,CAACE,cAAc,EAAE,CAAC;UAC5FX,YAAY,CAAC9E,WAAW,CAACwF,OAAO,CAAC;;QAGnC;QACA,IAAIjB,KAAK,CAACmB,WAAW,EAAE;UACrB,MAAMA,WAAW,GAAG,IAAI,CAACxE,aAAa,CAAC,cAAc,EAAEqD,KAAK,CAACmB,WAAW,CAAC3O,IAAI,CAAC;UAC9E+N,YAAY,CAAC9E,WAAW,CAAC0F,WAAW,CAAC;;QAGvC;QACA,IAAInB,KAAK,CAACoB,cAAc,EAAE;UACxB,MAAMC,UAAU,GAAG,IAAI,CAAC1E,aAAa,CAAC,YAAY,EAAEqD,KAAK,CAACoB,cAAc,CAACC,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC;UACnGd,YAAY,CAAC9E,WAAW,CAAC4F,UAAU,CAAC;;QAGtCpB,SAAS,CAACxE,WAAW,CAAC8E,YAAY,CAAC;QAEnC;QACA,IAAIP,KAAK,CAACsB,mBAAmB,IAAItB,KAAK,CAACsB,mBAAmB,CAAC9N,MAAM,GAAG,CAAC,EAAE;UACrE,MAAM+N,YAAY,GAAG1I,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;UACjDyI,YAAY,CAACjG,WAAW,GAAG,qBAAqB;UAChDiG,YAAY,CAACxI,KAAK,CAAC8E,SAAS,GAAG,MAAM;UACrC0D,YAAY,CAACxI,KAAK,CAACkC,YAAY,GAAG,MAAM;UACxCsG,YAAY,CAACxI,KAAK,CAACyB,QAAQ,GAAG,MAAM;UACpC+G,YAAY,CAACxI,KAAK,CAACyC,UAAU,GAAG,KAAK;UACrCyE,SAAS,CAACxE,WAAW,CAAC8F,YAAY,CAAC;UAEnC,MAAMC,WAAW,GAAG3I,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;UAChD0I,WAAW,CAACzI,KAAK,CAAC0I,SAAS,GAAG,MAAM;UACpCD,WAAW,CAACzI,KAAK,CAACY,OAAO,GAAG,GAAG;UAC/B6H,WAAW,CAACzI,KAAK,CAACwC,MAAM,GAAG,GAAG;UAE9ByE,KAAK,CAACsB,mBAAmB,CAAC9C,OAAO,CAACkD,OAAO,IAAG;YAC1C,MAAMC,WAAW,GAAG9I,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;YAChD6I,WAAW,CAAC5I,KAAK,CAACkC,YAAY,GAAG,KAAK;YACtC0G,WAAW,CAACvH,SAAS,GAAG,2EAA2E,IAAI,CAACwH,kBAAkB,CAACF,OAAO,CAACG,WAAW,CAAC,EAAE;YACjJL,WAAW,CAAC/F,WAAW,CAACkG,WAAW,CAAC;UACtC,CAAC,CAAC;UAEF1B,SAAS,CAACxE,WAAW,CAAC+F,WAAW,CAAC;;QAGpCzB,UAAU,CAACtE,WAAW,CAACwE,SAAS,CAAC;MACnC,CAAC,CAAC;MAEFH,aAAa,CAACrE,WAAW,CAACsE,UAAU,CAAC;;IAGvC;IACA,IAAIpH,MAAM,CAACrF,KAAK,IAAIqF,MAAM,CAACrF,KAAK,CAAC,CAAC,CAAC,IAAIqF,MAAM,CAACrF,KAAK,CAAC,CAAC,CAAC,CAACwO,QAAQ,IAAInJ,MAAM,CAACrF,KAAK,CAAC,CAAC,CAAC,CAACwO,QAAQ,CAACtO,MAAM,GAAG,CAAC,EAAE;MACtG,MAAMuO,eAAe,GAAG,IAAI,CAACjG,aAAa,CAAC,UAAU,EAAE,mBAAmB,CAAC;MAE3E,MAAMkG,YAAY,GAAGnJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;MACjDkJ,YAAY,CAACjJ,KAAK,CAAC0I,SAAS,GAAG,MAAM;MACrCO,YAAY,CAACjJ,KAAK,CAACY,OAAO,GAAG,GAAG;MAChCqI,YAAY,CAACjJ,KAAK,CAACwC,MAAM,GAAG,GAAG;MAC/ByG,YAAY,CAACjJ,KAAK,CAACQ,OAAO,GAAG,MAAM;MACnCyI,YAAY,CAACjJ,KAAK,CAACyH,mBAAmB,GAAG,uCAAuC;MAChFwB,YAAY,CAACjJ,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE/BjD,MAAM,CAACrF,KAAK,CAAC,CAAC,CAAC,CAACwO,QAAQ,CAACtD,OAAO,CAACyD,OAAO,IAAG;QACzC,MAAMC,WAAW,GAAGrJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAChDoJ,WAAW,CAACnJ,KAAK,CAACY,OAAO,GAAG,MAAM;QAClCuI,WAAW,CAACnJ,KAAK,CAACM,eAAe,GAAG,SAAS;QAC7C6I,WAAW,CAACnJ,KAAK,CAACa,YAAY,GAAG,KAAK;QACtCsI,WAAW,CAAC9H,SAAS,GAAG,2EAA2E6H,OAAO,CAACzP,IAAI,IAAI,SAAS,EAAE;QAC9HwP,YAAY,CAACvG,WAAW,CAACyG,WAAW,CAAC;MACvC,CAAC,CAAC;MAEFH,eAAe,CAACtG,WAAW,CAACuG,YAAY,CAAC;MACzCtG,gBAAgB,CAACD,WAAW,CAACsG,eAAe,CAAC;;IAG/C;IACArG,gBAAgB,CAACD,WAAW,CAACI,WAAW,CAAC;IACzCH,gBAAgB,CAACD,WAAW,CAAC2B,YAAY,CAAC;IAC1C1B,gBAAgB,CAACD,WAAW,CAACqE,aAAa,CAAC;IAE3C;IACApG,YAAY,CAAC+B,WAAW,CAACtB,WAAW,CAAC;IACrCT,YAAY,CAAC+B,WAAW,CAACT,MAAM,CAAC;IAChCtB,YAAY,CAAC+B,WAAW,CAACC,gBAAgB,CAAC;IAC1C9C,QAAQ,CAAC6C,WAAW,CAAC/B,YAAY,CAAC;IAElC;IACAb,QAAQ,CAACiC,IAAI,CAACW,WAAW,CAAC7C,QAAQ,CAAC;EACrC;EAEA;EACQkD,aAAaA,CAACT,KAAa,EAAE8G,SAAiB;IACpD,MAAMC,OAAO,GAAGvJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7CsJ,OAAO,CAACrJ,KAAK,CAACM,eAAe,GAAG,SAAS;IACzC+I,OAAO,CAACrJ,KAAK,CAACa,YAAY,GAAG,KAAK;IAClCwI,OAAO,CAACrJ,KAAK,CAACY,OAAO,GAAG,MAAM;IAC9ByI,OAAO,CAACrJ,KAAK,CAACiB,SAAS,GAAG,+BAA+B;IAEzD,MAAMqI,aAAa,GAAGxJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACnDuJ,aAAa,CAACtJ,KAAK,CAACQ,OAAO,GAAG,MAAM;IACpC8I,aAAa,CAACtJ,KAAK,CAACU,UAAU,GAAG,QAAQ;IACzC4I,aAAa,CAACtJ,KAAK,CAACkC,YAAY,GAAG,MAAM;IAEzC,MAAMqH,IAAI,GAAGzJ,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCwJ,IAAI,CAACC,SAAS,GAAG,OAAOJ,SAAS,EAAE;IACnCG,IAAI,CAACvJ,KAAK,CAACkB,KAAK,GAAG,SAAS;IAC5BqI,IAAI,CAACvJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC5B8H,IAAI,CAACvJ,KAAK,CAACuD,WAAW,GAAG,MAAM;IAE/B,MAAMkG,YAAY,GAAG3J,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IACjD0J,YAAY,CAAClH,WAAW,GAAGD,KAAK;IAChCmH,YAAY,CAACzJ,KAAK,CAACwC,MAAM,GAAG,GAAG;IAC/BiH,YAAY,CAACzJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCgI,YAAY,CAACzJ,KAAK,CAACyC,UAAU,GAAG,KAAK;IAErC6G,aAAa,CAAC5G,WAAW,CAAC6G,IAAI,CAAC;IAC/BD,aAAa,CAAC5G,WAAW,CAAC+G,YAAY,CAAC;IACvCJ,OAAO,CAAC3G,WAAW,CAAC4G,aAAa,CAAC;IAElC,OAAOD,OAAO;EAChB;EAEA;EACQzF,aAAaA,CAAC5J,KAAa,EAAED,KAAa;IAChD,MAAM2P,GAAG,GAAG5J,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACzC2J,GAAG,CAAC1J,KAAK,CAACkC,YAAY,GAAG,MAAM;IAE/B,MAAMyH,YAAY,GAAG7J,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClD4J,YAAY,CAACpH,WAAW,GAAGvI,KAAK;IAChC2P,YAAY,CAAC3J,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCkI,YAAY,CAAC3J,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCyI,YAAY,CAAC3J,KAAK,CAACkC,YAAY,GAAG,KAAK;IAEvC,MAAM0H,YAAY,GAAG9J,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClD6J,YAAY,CAACrH,WAAW,GAAGxI,KAAK;IAChC6P,YAAY,CAAC5J,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAEpCiI,GAAG,CAAChH,WAAW,CAACiH,YAAY,CAAC;IAC7BD,GAAG,CAAChH,WAAW,CAACkH,YAAY,CAAC;IAE7B,OAAOF,GAAG;EACZ;EAEA;EACA/N,gBAAgBA,CAACiE,MAAc;IAC7B,IAAIA,MAAM,IAAIA,MAAM,CAAClF,MAAM,IAAIkF,MAAM,CAAClF,MAAM,CAACD,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAIiN,OAAO,GAAG9H,MAAM,CAAClF,MAAM,CAAC,CAAC,CAAC,CAACgN,OAAO,IAAI9H,MAAM,CAAClF,MAAM,CAAC,CAAC,CAAC,CAACkB,EAAE;MAE7D;MACA,IAAIiO,QAAQ,GAAG,IAAI,CAAC1M,YAAY;MAEhC;MACA,IAAI,CAAC0M,QAAQ,EAAE;QACbA,QAAQ,GAAGjK,MAAM,CAAChE,EAAE;;MAGtB8D,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEkK,QAAQ,EAAE,cAAc,EAAEnC,OAAO,CAAC;MAExF;MACA,IAAI,CAAC5K,MAAM,CAACgN,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UACXF,QAAQ,EAAEA,QAAQ;UAClBnC,OAAO,EAAEA;;OAEZ,CAAC;KACH,MAAM;MACLhI,OAAO,CAACsK,KAAK,CAAC,sCAAsC,EAAEpK,MAAM,CAAC;;EAEjE;EAEAH,gBAAgBA,CAAA;IACd;IACA;IACA;EAAA;EAGFD,iBAAiBA,CAAA;IACf;IACA,MAAMlB,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAACiM,GAAG,CAAC,uBAAuB,CAAC,EAAElQ,KAAK,IAAI,CAAC;IACtF,MAAMyE,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAACiM,GAAG,CAAC,qBAAqB,CAAC,EAAElQ,KAAK,IAAI,CAAC;IAElF,IAAI,CAAC8C,cAAc,CAACqN,kBAAkB,CAAC5L,qBAAqB,CAAC,CAAC6L,SAAS,CAACC,SAAS,IAAG;MAClF,IAAI,CAACrN,kBAAkB,GAAGqN,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAACvN,cAAc,CAACqN,kBAAkB,CAAC1L,mBAAmB,CAAC,CAAC2L,SAAS,CAACC,SAAS,IAAG;MAChF,IAAI,CAACpN,gBAAgB,GAAGoN,SAAS;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAACpM,UAAU,CAACiM,GAAG,CAAC,uBAAuB,CAAC,EAAEI,YAAY,CACvDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAACzN,cAAc,CAACqN,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAACrN,kBAAkB,GAAGqN,SAAS;QACnC;QACA,IAAI,CAACpM,UAAU,CAACiM,GAAG,CAAC,mBAAmB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAACvM,UAAU,CAACiM,GAAG,CAAC,qBAAqB,CAAC,EAAEI,YAAY,CACrDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAACzN,cAAc,CAACqN,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAACpN,gBAAgB,GAAGoN,SAAS;QACjC;QACA,IAAI,CAACpM,UAAU,CAACiM,GAAG,CAAC,iBAAiB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACtD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAACvM,UAAU,CAACiM,GAAG,CAAC,mBAAmB,CAAC,EAAEI,YAAY,CACnDG,IAAI,CACHnS,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACwB,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMuQ,YAAY,GAAG,IAAI,CAACtM,UAAU,CAACiM,GAAG,CAAC,uBAAuB,CAAC,EAAElQ,KAAK,IAAI,CAAC;QAC7E;QACA,IAAIA,KAAK,CAACU,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACoC,cAAc,CAACqN,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9DhS,GAAG,CAAC4R,SAAS,IAAIA,SAAS,CAACK,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAACjR,IAAI,CAACkR,WAAW,EAAE,CAACC,QAAQ,CAAC7Q,KAAK,CAAC4Q,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAAChR,IAAI,IAAIgR,QAAQ,CAAChR,IAAI,CAACiR,WAAW,EAAE,CAACC,QAAQ,CAAC7Q,KAAK,CAAC4Q,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAC9N,cAAc,CAACqN,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAO7R,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA0R,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACrN,kBAAkB,GAAGqN,SAAS;IACrC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACpM,UAAU,CAACiM,GAAG,CAAC,iBAAiB,CAAC,EAAEI,YAAY,CACjDG,IAAI,CACHnS,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACwB,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMuQ,YAAY,GAAG,IAAI,CAACtM,UAAU,CAACiM,GAAG,CAAC,qBAAqB,CAAC,EAAElQ,KAAK,IAAI,CAAC;QAC3E;QACA,IAAIA,KAAK,CAACU,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACoC,cAAc,CAACqN,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9DhS,GAAG,CAAC4R,SAAS,IAAIA,SAAS,CAACK,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAACjR,IAAI,CAACkR,WAAW,EAAE,CAACC,QAAQ,CAAC7Q,KAAK,CAAC4Q,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAAChR,IAAI,IAAIgR,QAAQ,CAAChR,IAAI,CAACiR,WAAW,EAAE,CAACC,QAAQ,CAAC7Q,KAAK,CAAC4Q,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAC9N,cAAc,CAACqN,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAO7R,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA0R,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACpN,gBAAgB,GAAGoN,SAAS;IACnC,CAAC,CAAC;EACN;EAEAS,eAAeA,CAACH,QAA+B;IAC7C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAII,WAAW,GAAGJ,QAAQ,CAACjR,IAAI;IAC/B,IAAIiR,QAAQ,CAAChR,IAAI,EAAE;MACjBoR,WAAW,IAAI,KAAKJ,QAAQ,CAAChR,IAAI,GAAG;;IAEtC,IAAIgR,QAAQ,CAAC/Q,IAAI,KAAKhB,YAAY,CAACoS,OAAO,IAAIL,QAAQ,CAACtR,IAAI,EAAE;MAC3D0R,WAAW,IAAI,MAAMJ,QAAQ,CAACtR,IAAI,EAAE;;IAEtC,OAAO0R,WAAW;EACpB;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAAChN,UAAU,CAACiN,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAClN,UAAU,CAAC;MAC1C;;IAGF,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC5C,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC6C,WAAW,GAAG,IAAI;IAEvB,MAAMiO,SAAS,GAAG,IAAI,CAACnN,UAAU,CAACjE,KAAK;IAEvC;IACA,MAAMqR,OAAO,GAAuB;MAClCC,WAAW,EAAEF,SAAS,CAACjN,WAAW;MAClCoN,YAAY,EAAEH,SAAS,CAAC/M,YAAY;MACpCmN,OAAO,EAAEJ,SAAS,CAAC1M,aAAa;MAChC+M,kBAAkB,EAAE,CAClB;QACE5P,EAAE,EAAEuP,SAAS,CAAC9M,iBAAiB,EAAEzC,EAAE,IAAI,EAAE;QACzCjC,IAAI,EAAEwR,SAAS,CAAC7M;OACjB,CACF;MACDmN,gBAAgB,EAAE,CAChB;QACE7P,EAAE,EAAEuP,SAAS,CAAC5M,eAAe,EAAE3C,EAAE,IAAI,EAAE;QACvCjC,IAAI,EAAEwR,SAAS,CAAC3M;OACjB,CACF;MACDkN,UAAU,EAAE,CACV;QACE/R,IAAI,EAAEwR,SAAS,CAACtM,aAAa;QAC7B8M,KAAK,EAAER,SAAS,CAACzM;OAClB,CACF;MACDkN,qBAAqB,EAAET,SAAS,CAACpM,OAAO;MACxC8M,oBAAoB,EAAE;QACpBC,oBAAoB,EAAE;UACpBxM,sBAAsB,EAAE6L,SAAS,CAAC7L;;OAErC;MACDJ,sBAAsB,EAAEiM,SAAS,CAACjM,sBAAsB;MACxDC,wBAAwB,EAAEgM,SAAS,CAAChM,wBAAwB;MAC5DC,6BAA6B,EAAE+L,SAAS,CAAC/L,6BAA6B;MACtEC,mBAAmB,EAAE8L,SAAS,CAAC9L,mBAAmB;MAClD7B,aAAa,EAAE,CAAC2N,SAAS,CAACrM,WAAW,CAAC;MACtCiN,OAAO,EAAEZ,SAAS,CAACnM,OAAO;MAC1BgN,QAAQ,EAAEb,SAAS,CAAClM;KACrB;IAED,IAAI,CAACpC,cAAc,CAACoP,WAAW,CAACb,OAAO,CAAC,CACrCjB,SAAS,CAAC;MACT+B,IAAI,EAAGC,QAA6B,IAAI;QACtC,IAAI,CAAClP,SAAS,GAAG,KAAK;QACtB,IAAIkP,QAAQ,CAAClK,MAAM,CAACmK,OAAO,EAAE;UAC3B,IAAI,CAAC3P,aAAa,GAAG0P,QAAQ,CAACpK,IAAI,CAACsK,OAAO;UAE1C;UACA3M,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2M,IAAI,CAACC,SAAS,CAACJ,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEpE;UACA,IAAIA,QAAQ,CAACpK,IAAI,IAAIoK,QAAQ,CAACpK,IAAI,CAACsK,OAAO,IAAIF,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC5R,MAAM,GAAG,CAAC,EAAE;YAC9EiF,OAAO,CAACzB,KAAK,CAAC,uBAAuB,CAAC;YACtCyB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEwM,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC5R,MAAM,CAAC;YAE3D;YACA,MAAM+R,iBAAiB,GAAGL,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC5B,MAAM,CAACgC,CAAC,IAAIA,CAAC,CAAC/R,MAAM,IAAI+R,CAAC,CAAC/R,MAAM,CAACD,MAAM,GAAG,CAAC,CAAC;YAC5FiF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE6M,iBAAiB,CAAC/R,MAAM,CAAC;YAE7D;YACA,MAAMiS,kBAAkB,GAAGF,iBAAiB,CAACG,OAAO,CAACF,CAAC,IAAIA,CAAC,CAAC/R,MAAM,CAAClC,GAAG,CAACoU,CAAC,IACtEA,CAAC,CAAC/E,YAAY,KAAKC,SAAS,GAAG8E,CAAC,CAAC/E,YAAY,GAAI+E,CAAC,CAAC7E,QAAQ,GAAG6E,CAAC,CAAC7E,QAAQ,CAACC,kBAAkB,GAAG,CAAE,CACjG,CAAC;YACFtI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+M,kBAAkB,CAAC;YAEvD;YACA,MAAMG,kBAAkB,GAAGH,kBAAkB,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAI;cAChE,IAAIA,GAAG,KAAKlF,SAAS,EAAE;gBACrBiF,GAAG,CAACC,GAAG,CAAC,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEhC,OAAOD,GAAG;YACZ,CAAC,EAAE,EAA4B,CAAC;YAChCrN,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEkN,kBAAkB,CAAC;YAEvD;YACA,MAAMI,iBAAiB,GAAGT,iBAAiB,CAAC/B,MAAM,CAACgC,CAAC,IAClDA,CAAC,CAAC/R,MAAM,CAACwS,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACvE,cAAc,IAAIuE,CAAC,CAACvE,cAAc,CAACC,UAAU,KAAK,IAAI,CAAC,CAC7E;YACD5I,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEsN,iBAAiB,CAACxS,MAAM,CAAC;YAE5DiF,OAAO,CAACyN,QAAQ,EAAE;;UAGpB;UACA,IAAIhB,QAAQ,CAACpK,IAAI,IAAIoK,QAAQ,CAACpK,IAAI,CAAC8H,QAAQ,EAAE;YAC3C,IAAI,CAAC1M,YAAY,GAAGgP,QAAQ,CAACpK,IAAI,CAAC8H,QAAQ;YAC1CnK,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACxC,YAAY,CAAC;;UAErE;UAAA,KACK,IAAIgP,QAAQ,CAAClK,MAAM,IAAIkK,QAAQ,CAAClK,MAAM,CAACmL,SAAS,EAAE;YACrD,IAAI,CAACjQ,YAAY,GAAGgP,QAAQ,CAAClK,MAAM,CAACmL,SAAS;YAC7C1N,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACxC,YAAY,CAAC;;UAExE;UAAA,KACK,IAAIgP,QAAQ,CAACpK,IAAI,IAAIoK,QAAQ,CAACpK,IAAI,CAACsK,OAAO,IAAIF,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC5R,MAAM,GAAG,CAAC,IAAI0R,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC,CAAC,CAAC,CAACzQ,EAAE,EAAE;YAClH,IAAI,CAACuB,YAAY,GAAGgP,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC,CAAC,CAAC,CAACzQ,EAAE;YAC/C8D,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACxC,YAAY,CAAC;WAChE,MAAM;YACLuC,OAAO,CAACsK,KAAK,CAAC,qCAAqC,CAAC;YACpDtK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0N,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAC,CAAC;YACzD,IAAIA,QAAQ,CAACpK,IAAI,EAAErC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE0N,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAACpK,IAAI,CAAC,CAAC;YAC7E,IAAIoK,QAAQ,CAAClK,MAAM,EAAEvC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE0N,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAClK,MAAM,CAAC,CAAC;;SAEtF,MAAM;UACL,IAAI,CAAC5H,YAAY,GAAG,sDAAsD;UAC1E,IAAI8R,QAAQ,CAAClK,MAAM,CAACsL,QAAQ,IAAIpB,QAAQ,CAAClK,MAAM,CAACsL,QAAQ,CAAC9S,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAACJ,YAAY,GAAG8R,QAAQ,CAAClK,MAAM,CAACsL,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACDxD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/M,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC5C,YAAY,GAAG,wDAAwD;QAC5EqF,OAAO,CAACsK,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEA;EACAkB,oBAAoBA,CAACuC,SAAoB;IACvCJ,MAAM,CAACK,MAAM,CAACD,SAAS,CAACE,QAAQ,CAAC,CAAClI,OAAO,CAACmI,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAYzV,SAAS,EAAE;QAChC,IAAI,CAAC+S,oBAAoB,CAAC0C,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA;EACAzR,cAAcA,CAAC2R,OAAe;IAC5B,MAAMC,KAAK,GAAGlH,IAAI,CAACC,KAAK,CAACgH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,KAAK,KAAKC,IAAI,KAAK;EAC/B;EAEA;EACAC,UAAUA,CAACC,UAAkB;IAC3B,MAAMhS,IAAI,GAAG,IAAI2B,IAAI,CAACqQ,UAAU,CAAC;IACjC,OAAOhS,IAAI,CAAC8H,kBAAkB,CAAC,OAAO,EAAE;MACtCmK,OAAO,EAAE,OAAO;MAChBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACd1J,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACA5I,WAAWA,CAAC4D,MAAc;IACxB,IAAI,CAACA,MAAM,CAAClF,MAAM,IAAIkF,MAAM,CAAClF,MAAM,CAACD,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd,MAAM6T,QAAQ,GAAG1O,MAAM,CAAClF,MAAM,CAACoS,MAAM,CAAC,CAACnO,GAAG,EAAEsI,KAAK,KAC/CA,KAAK,CAACK,KAAK,CAACC,MAAM,GAAG5I,GAAG,CAAC2I,KAAK,CAACC,MAAM,GAAGN,KAAK,GAAGtI,GAAG,EAAEiB,MAAM,CAAClF,MAAM,CAAC,CAAC,CAAC,CAAC;IAExE,OAAO4T,QAAQ,CAAChH,KAAK,CAACiH,eAAe,IAAI,GAAGD,QAAQ,CAAChH,KAAK,CAACC,MAAM,IAAI+G,QAAQ,CAAChH,KAAK,CAACrI,QAAQ,EAAE;EAChG;EAEA;EACAuP,iBAAiBA,CAAC5O,MAAc;IAC9B;IACA,IAAI,CAACA,MAAM,CAAClF,MAAM,IAAIkF,MAAM,CAAClF,MAAM,CAACD,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd;IACA,MAAMwM,KAAK,GAAGrH,MAAM,CAAClF,MAAM,CAAC,CAAC,CAAC;IAC9B,MAAMkN,iBAAiB,GAAGX,KAAK,CAACY,YAAY,KAAKC,SAAS,GAAGb,KAAK,CAACY,YAAY,GACrDZ,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IACjF,OAAOJ,iBAAiB,GAAG,CAAC;EAC9B;EAEA;EACA6G,yBAAyBA,CAAA;IACvB,MAAMnE,YAAY,GAAG,IAAI,CAACtM,UAAU,CAACiM,GAAG,CAAC,uBAAuB,CAAC,EAAElQ,KAAK,IAAI,CAAC;IAC7E,IAAI,CAAC8C,cAAc,CAACqN,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACrN,kBAAkB,GAAGqN,SAAS;MACnC;MACA,MAAMsE,KAAK,GAAG5O,QAAQ,CAAC6O,cAAc,CAAC,mBAAmB,CAAqB;MAC9E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAC,uBAAuBA,CAAA;IACrB,MAAMzE,YAAY,GAAG,IAAI,CAACtM,UAAU,CAACiM,GAAG,CAAC,qBAAqB,CAAC,EAAElQ,KAAK,IAAI,CAAC;IAC3E,IAAI,CAAC8C,cAAc,CAACqN,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACpN,gBAAgB,GAAGoN,SAAS;MACjC;MACA,MAAMsE,KAAK,GAAG5O,QAAQ,CAAC6O,cAAc,CAAC,iBAAiB,CAAqB;MAC5E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAE,aAAaA,CAAA;IACX,MAAM3Q,iBAAiB,GAAG,IAAI,CAACL,UAAU,CAACiM,GAAG,CAAC,mBAAmB,CAAC,EAAElQ,KAAK;IACzE,MAAMuE,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAACiM,GAAG,CAAC,uBAAuB,CAAC,EAAElQ,KAAK;IACjF,MAAMwE,eAAe,GAAG,IAAI,CAACP,UAAU,CAACiM,GAAG,CAAC,iBAAiB,CAAC,EAAElQ,KAAK;IACrE,MAAMyE,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAACiM,GAAG,CAAC,qBAAqB,CAAC,EAAElQ,KAAK;IAE7E,IAAI,CAACiE,UAAU,CAACiR,UAAU,CAAC;MACzB5Q,iBAAiB,EAAEE,eAAe;MAClCD,qBAAqB,EAAEE,mBAAmB;MAC1CD,eAAe,EAAEF,iBAAiB;MAClCG,mBAAmB,EAAEF;KACtB,CAAC;EACJ;EAEA;EACA4Q,oBAAoBA,CAAChB,UAAkB;IACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B;IACA,MAAMhS,IAAI,GAAG,IAAI2B,IAAI,CAACqQ,UAAU,CAAC;IACjC,OAAOhS,IAAI,CAACiM,cAAc,EAAE;EAC9B;EAEA;EACAU,kBAAkBA,CAACC,WAAmB;IACpC,QAAQA,WAAW;MACjB,KAAK,CAAC;QAAE,OAAO,eAAe;MAC9B,KAAK,CAAC;QAAE,OAAO,iBAAiB;MAChC,KAAK,CAAC;QAAE,OAAO,cAAc;MAC7B;QAAS,OAAO,SAAS;;EAE7B;EAEA;EACAqG,oBAAoBA,CAACtQ,aAAqB;IACxC,QAAQA,aAAa;MACnB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,WAAW;;EAE/B;EAEA;EACAuQ,oBAAoBA,CAACC,cAAmB,EAAEC,WAAgB;IACxD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAACvT,OAAO,IAAI,CAACuT,cAAc,CAACvT,OAAO,CAACI,IAAI,IAC1E,CAACoT,WAAW,IAAI,CAACA,WAAW,CAACzT,SAAS,IAAI,CAACyT,WAAW,CAACzT,SAAS,CAACK,IAAI,EAAE;MACzE,OAAO,SAAS;;IAGlB,MAAMkJ,WAAW,GAAG,IAAIvH,IAAI,CAACwR,cAAc,CAACvT,OAAO,CAACI,IAAI,CAAC,CAACwK,OAAO,EAAE;IACnE,MAAMjC,aAAa,GAAG,IAAI5G,IAAI,CAACyR,WAAW,CAACzT,SAAS,CAACK,IAAI,CAAC,CAACwK,OAAO,EAAE;IACpE,MAAM6I,MAAM,GAAG9K,aAAa,GAAGW,WAAW;IAC1C,MAAMoK,QAAQ,GAAG3I,IAAI,CAACC,KAAK,CAACyI,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIC,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,KAAK;KACxB,MAAM;MACL,MAAMzB,KAAK,GAAGlH,IAAI,CAACC,KAAK,CAAC0I,QAAQ,GAAG,EAAE,CAAC;MACvC,MAAMxB,IAAI,GAAGwB,QAAQ,GAAG,EAAE;MAC1B,OAAO,GAAGzB,KAAK,KAAKC,IAAI,KAAK;;EAEjC;;;uBAvgCWtR,oBAAoB,EAAA7D,EAAA,CAAA4W,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9W,EAAA,CAAA4W,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhX,EAAA,CAAA4W,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApBrT,oBAAoB;MAAAsT,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjCzX,EAAA,CAAAC,cAAA,aAAoC;UAOxBD,EAAA,CAAAqB,SAAA,WAA6B;UAC7BrB,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5BH,EAAA,CAAAC,cAAA,aAAkC;UAChCD,EAAA,CAAAE,MAAA,iEACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA2E;UAA5CD,EAAA,CAAAkC,UAAA,sBAAAyV,wDAAA;YAAA,OAAYD,GAAA,CAAAvF,QAAA,EAAU;UAAA,EAAC;UAEpDnS,EAAA,CAAAC,cAAA,cAAgC;UAEGD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAAC,cAAA,eAAsC;UAIND,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC1CH,EAAA,CAAAC,cAAA,sBAAwB;UAAAD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAInDH,EAAA,CAAAC,cAAA,eAAmC;UAEpBD,EAAA,CAAAE,MAAA,6CAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClDH,EAAA,CAAAC,cAAA,iBAK6C;UAAtCD,EAAA,CAAAkC,UAAA,mBAAA0V,sDAAA;YAAA,OAASF,GAAA,CAAA9B,yBAAA,EAA2B;UAAA,EAAC;UAL5C5V,EAAA,CAAAG,YAAA,EAK6C;UAC7CH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gCAAmF;UACjFD,EAAA,CAAAQ,UAAA,KAAAqX,2CAAA,yBAKa;UACf7X,EAAA,CAAAG,YAAA,EAAmB;UAM3BH,EAAA,CAAAC,cAAA,kBAAoE;UAA1BD,EAAA,CAAAkC,UAAA,mBAAA4V,uDAAA;YAAA,OAASJ,GAAA,CAAAvB,aAAA,EAAe;UAAA,EAAC;UACjEnW,EAAA,CAAAqB,SAAA,aAAmC;UACrCrB,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAuC;UACRD,EAAA,CAAAE,MAAA,oBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,eAAsC;UAIND,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC1CH,EAAA,CAAAC,cAAA,sBAAwB;UAAAD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAInDH,EAAA,CAAAC,cAAA,eAAmC;UAEpBD,EAAA,CAAAE,MAAA,6CAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClDH,EAAA,CAAAC,cAAA,iBAK2C;UAApCD,EAAA,CAAAkC,UAAA,mBAAA6V,sDAAA;YAAA,OAASL,GAAA,CAAAxB,uBAAA,EAAyB;UAAA,EAAC;UAL1ClW,EAAA,CAAAG,YAAA,EAK2C;UAC3CH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAC,cAAA,gCAAiF;UAC/ED,EAAA,CAAAQ,UAAA,KAAAwX,2CAAA,yBAKa;UACfhY,EAAA,CAAAG,YAAA,EAAmB;UAQ7BH,EAAA,CAAAC,cAAA,eAAuC;UAERD,EAAA,CAAAE,MAAA,2BAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,0BAAqC;UACxBD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAqB,SAAA,iBAA4G;UAG9GrB,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAC,cAAA,eAAwC;UACVD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAAC,cAAA,eAA8B;UAEfD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC3BH,EAAA,CAAAC,cAAA,sBAA+D;UAC7DD,EAAA,CAAAQ,UAAA,KAAAyX,2CAAA,yBAEa;UACfjY,EAAA,CAAAG,YAAA,EAAa;UAGfH,EAAA,CAAAC,cAAA,0BAAqC;UACxBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAqB,SAAA,iBAAmG;UACrGrB,EAAA,CAAAG,YAAA,EAAiB;UAMvBH,EAAA,CAAAC,cAAA,eAAsC;UAETD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvCH,EAAA,CAAAC,cAAA,0BAAqC;UACxBD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAC,cAAA,sBAA2D;UACzDD,EAAA,CAAAQ,UAAA,KAAA0X,2CAAA,yBAEa;UACflY,EAAA,CAAAG,YAAA,EAAa;UAIjBH,EAAA,CAAAC,cAAA,eAAsC;UAGhCD,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAe;UAMrBH,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAqB,SAAA,YAA6B;UAC7BrB,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOjBH,EAAA,CAAAC,cAAA,eAAwE;UACtED,EAAA,CAAAQ,UAAA,KAAA2X,oCAAA,kBAGM;UAENnY,EAAA,CAAAQ,UAAA,MAAA4X,qCAAA,kBAKM;UAENpY,EAAA,CAAAQ,UAAA,MAAA6X,qCAAA,kBAMM;UAENrY,EAAA,CAAAQ,UAAA,MAAA8X,qCAAA,kBAsFM;UACRtY,EAAA,CAAAG,YAAA,EAAM;;;;;;UA3PIH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAU,UAAA,cAAAgX,GAAA,CAAAvS,UAAA,CAAwB;UASJnF,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UACXV,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UAUlBV,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAU,UAAA,oBAAA6X,GAAA,CAAiC;UAIWvY,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAU,UAAA,gBAAAgX,GAAA,CAAA1F,eAAA,CAA+B;UAC/ChS,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAU,UAAA,YAAAgX,GAAA,CAAAxT,kBAAA,CAAqB;UAsB1ClE,EAAA,CAAAI,SAAA,IAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UACXV,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UAUlBV,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAU,UAAA,oBAAA8X,GAAA,CAA+B;UAIWxY,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAU,UAAA,gBAAAgX,GAAA,CAAA1F,eAAA,CAA+B;UAC7ChS,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAU,UAAA,YAAAgX,GAAA,CAAAvT,gBAAA,CAAmB;UAmB1CnE,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAU,UAAA,QAAAgX,GAAA,CAAA3S,OAAA,CAAe,kBAAA0T,GAAA;UACEzY,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAU,UAAA,QAAA+X,GAAA,CAAc;UAWdzY,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAU,UAAA,YAAAgX,GAAA,CAAAnT,cAAA,CAAiB;UAqBZvE,EAAA,CAAAI,SAAA,IAAgB;UAAhBJ,EAAA,CAAAU,UAAA,YAAAgX,GAAA,CAAA/S,aAAA,CAAgB;UAkBd3E,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAU,UAAA,aAAAgX,GAAA,CAAAtT,SAAA,CAAsB;UAUpCpE,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAwD,WAAA,gBAAAkU,GAAA,CAAArT,WAAA,CAAiC;UAC/DrE,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAU,UAAA,SAAAgX,GAAA,CAAAtT,SAAA,CAAe;UAKfpE,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAU,UAAA,SAAAgX,GAAA,CAAAlW,YAAA,KAAAkW,GAAA,CAAAtT,SAAA,CAAgC;UAOhCpE,EAAA,CAAAI,SAAA,GAA8E;UAA9EJ,EAAA,CAAAU,UAAA,UAAAgX,GAAA,CAAAtT,SAAA,IAAAsT,GAAA,CAAA9T,aAAA,CAAAhC,MAAA,UAAA8V,GAAA,CAAArT,WAAA,KAAAqT,GAAA,CAAAlW,YAAA,CAA8E;UAQ9ExB,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAU,UAAA,UAAAgX,GAAA,CAAAtT,SAAA,IAAAsT,GAAA,CAAA9T,aAAA,CAAAhC,MAAA,KAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}