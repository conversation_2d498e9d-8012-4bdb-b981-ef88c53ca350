{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewEncapsulation, HostListener } from '@angular/core';\nimport { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nexport let SearchPriceComponent = class SearchPriceComponent {\n  constructor(fb, productService, router, sharedDataService) {\n    this.fb = fb;\n    this.productService = productService;\n    this.router = router;\n    this.sharedDataService = sharedDataService;\n    this.departureLocations = [];\n    this.arrivalLocations = [];\n    this.isLoading = false;\n    this.searchResults = [];\n    this.filteredResults = [];\n    this.hasSearched = false;\n    this.errorMessage = '';\n    this.lastSearchId = '';\n    // Types de recherche de vol\n    this.SEARCH_TYPE_ONE_WAY = 1;\n    this.SEARCH_TYPE_ROUND_TRIP = 2;\n    this.SEARCH_TYPE_MULTI_CITY = 3;\n    // Type de recherche actuel (par défaut: aller simple)\n    this.currentSearchType = this.SEARCH_TYPE_ONE_WAY;\n    // Passenger selector properties\n    this.showPassengerDropdown = false;\n    this.passengerCounts = {\n      [PassengerType.Adult]: 1,\n      [PassengerType.Child]: 0,\n      [PassengerType.Infant]: 0\n    };\n    // Filter options\n    this.currentFilter = 'recommended';\n    this.filterOptions = [{\n      value: 'recommended',\n      label: 'Recommended',\n      icon: 'fa-star'\n    }, {\n      value: 'cheapest',\n      label: 'Cheapest',\n      icon: 'fa-dollar-sign'\n    }, {\n      value: 'shortest',\n      label: 'Shortest',\n      icon: 'fa-clock'\n    }];\n    // Sidebar filter options\n    this.sidebarFilters = {\n      stops: {\n        direct: false,\n        oneStop: false,\n        multiStop: false\n      },\n      departureTime: {\n        earlyMorning: false,\n        morning: false,\n        afternoon: false,\n        evening: false // 18:00 - 00:00\n      },\n\n      arrivalTime: {\n        earlyMorning: false,\n        morning: false,\n        afternoon: false,\n        evening: false // 18:00 - 00:00\n      },\n\n      airlines: {} // Will be populated dynamically based on search results\n    };\n    // Expanded sections in sidebar\n    this.expandedSections = {\n      stops: true,\n      departureTime: true,\n      arrivalTime: true,\n      airlines: true\n    };\n    // Price ranges for each filter option (will be calculated from results)\n    this.filterPrices = {\n      stops: {\n        direct: 0,\n        oneStop: 0,\n        multiStop: 0\n      },\n      departureTime: {\n        earlyMorning: 0,\n        morning: 0,\n        afternoon: 0,\n        evening: 0\n      },\n      arrivalTime: {\n        earlyMorning: 0,\n        morning: 0,\n        afternoon: 0,\n        evening: 0\n      },\n      airlines: {}\n    };\n    // Passenger type options\n    this.passengerTypes = [{\n      value: PassengerType.Adult,\n      label: 'Adult'\n    }, {\n      value: PassengerType.Child,\n      label: 'Child'\n    }, {\n      value: PassengerType.Infant,\n      label: 'Infant'\n    }];\n    // Flight class options\n    this.flightClasses = [{\n      value: FlightClassType.PROMO,\n      label: 'Promo'\n    }, {\n      value: FlightClassType.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClassType.BUSINESS,\n      label: 'Business'\n    }];\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required],\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      arrivalLocation: ['', Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      returnDate: [''],\n      // Segments pour multi-city (sera initialisé dynamiquement)\n      segments: this.fb.array([]),\n      // Options de vol\n      flightClass: [1, Validators.required],\n      nonStop: [false],\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n    // Initialiser les compteurs de passagers dans le service partagé\n    this.sharedDataService.setPassengerCounts(this.passengerCounts);\n  }\n  // Close dropdown when clicking outside\n  onDocumentClick(event) {\n    // Check if click is outside the passenger dropdown\n    const clickedElement = event.target;\n    const passengerSelector = document.querySelector('.passengers-selector');\n    if (passengerSelector && !passengerSelector.contains(clickedElement)) {\n      this.showPassengerDropdown = false;\n    }\n  }\n  // Toggle passenger dropdown\n  togglePassengerDropdown(event) {\n    event.stopPropagation();\n    this.showPassengerDropdown = !this.showPassengerDropdown;\n  }\n  // Close passenger dropdown\n  closePassengerDropdown() {\n    this.showPassengerDropdown = false;\n  }\n  // Get passenger count for a specific type\n  getPassengerCount(type) {\n    return this.passengerCounts[type] || 0;\n  }\n  // Increase passenger count\n  increasePassengerCount(type) {\n    if (this.getTotalPassengers() < 9) {\n      this.passengerCounts[type] = (this.passengerCounts[type] || 0) + 1;\n      // Mettre à jour le service partagé\n      this.sharedDataService.setPassengerCounts(this.passengerCounts);\n    }\n  }\n  // Decrease passenger count\n  decreasePassengerCount(type) {\n    // Pour les adultes, ne pas permettre de descendre en dessous de 1\n    if (type === PassengerType.Adult) {\n      if (this.passengerCounts[type] > 1) {\n        this.passengerCounts[type] -= 1;\n      }\n    } else if (this.passengerCounts[type] > 0) {\n      this.passengerCounts[type] -= 1;\n    }\n    // Mettre à jour le service partagé\n    this.sharedDataService.setPassengerCounts(this.passengerCounts);\n  }\n  // Get total number of passengers\n  getTotalPassengers() {\n    return Object.values(this.passengerCounts).reduce((sum, count) => sum + count, 0);\n  }\n  // Get passengers array for API request\n  getPassengersArray() {\n    // Utiliser le service partagé pour obtenir les passagers\n    return this.sharedDataService.getPassengersArray();\n  }\n  ngOnInit() {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n  // Méthode pour appliquer le filtre sélectionné\n  applyFilter(filterValue) {\n    this.currentFilter = filterValue;\n    this.applyAllFilters();\n  }\n  // Méthode pour vérifier si un vol correspond à la classe sélectionnée\n  isFlightMatchingClass(flight, selectedClass) {\n    // Vérifier si le vol a des items\n    if (!flight.items || flight.items.length === 0) {\n      console.log(`Flight ${flight.id} has no items`);\n      return false;\n    }\n    console.log(`Checking flight ${flight.id} for class ${selectedClass}`);\n    // Vérifier la classe de vol dans les items\n    for (const item of flight.items) {\n      if (item.flightClass) {\n        console.log(`Item class: ${item.flightClass.name} (type: ${item.flightClass.type})`);\n        if (item.flightClass.type === selectedClass) {\n          console.log(`Match found in item flightClass`);\n          return true;\n        }\n      }\n      // Vérifier également dans les segments si disponibles\n      if (item.segments) {\n        console.log(`Checking ${item.segments.length} segments`);\n        for (const segment of item.segments) {\n          if (segment.flightClass) {\n            console.log(`Segment class: ${segment.flightClass.name} (type: ${segment.flightClass.type})`);\n            if (segment.flightClass.type === selectedClass) {\n              console.log(`Match found in segment flightClass`);\n              return true;\n            }\n          }\n        }\n      }\n    }\n    // Vérifier également dans les offres si disponibles\n    if (flight.offers && flight.offers.length > 0) {\n      console.log(`Checking ${flight.offers.length} offers`);\n      for (const offer of flight.offers) {\n        if (offer.flightClassInformations && offer.flightClassInformations.length > 0) {\n          console.log(`Offer has ${offer.flightClassInformations.length} class infos`);\n          for (const classInfo of offer.flightClassInformations) {\n            console.log(`Offer class info: ${classInfo.name} (type: ${classInfo.type})`);\n            if (classInfo.type === selectedClass) {\n              console.log(`Match found in offer flightClassInformations`);\n              return true;\n            }\n          }\n        } else {\n          console.log(`Offer has no flightClassInformations`);\n        }\n      }\n    }\n    console.log(`No match found for flight ${flight.id}`);\n    return false;\n  }\n  // Méthode pour appliquer tous les filtres (top et sidebar)\n  applyAllFilters() {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      this.filteredResults = [];\n      return;\n    }\n    // Étape 1: Appliquer les filtres de la sidebar\n    let results = [...this.searchResults];\n    // Récupérer la classe de vol sélectionnée\n    const selectedClass = this.searchForm.get('flightClass')?.value;\n    // Filtrer par classe de vol\n    if (selectedClass !== undefined) {\n      console.log('Filtering by flight class:', selectedClass);\n      // Afficher le nombre total de vols avant filtrage\n      console.log('Total flights before class filtering:', results.length);\n      // Vérifier combien de vols correspondent à chaque classe\n      const classCounts = {\n        [FlightClassType.PROMO]: 0,\n        [FlightClassType.ECONOMY]: 0,\n        [FlightClassType.BUSINESS]: 0\n      };\n      results.forEach(flight => {\n        if (this.isFlightMatchingClass(flight, FlightClassType.PROMO)) classCounts[FlightClassType.PROMO]++;\n        if (this.isFlightMatchingClass(flight, FlightClassType.ECONOMY)) classCounts[FlightClassType.ECONOMY]++;\n        if (this.isFlightMatchingClass(flight, FlightClassType.BUSINESS)) classCounts[FlightClassType.BUSINESS]++;\n      });\n      console.log('Flights by class (before filtering):', classCounts);\n      // Option pour désactiver temporairement le filtrage par classe (pour débogage)\n      // Mettre à true pour voir tous les vols disponibles sans filtrage par classe\n      const disableClassFiltering = true;\n      if (!disableClassFiltering) {\n        results = results.filter(flight => this.isFlightMatchingClass(flight, selectedClass));\n        console.log('Flights after class filtering:', results.length);\n      } else {\n        console.log('Class filtering disabled for debugging');\n      }\n    }\n    // Filtrer par nombre d'escales\n    if (this.sidebarFilters.stops.direct || this.sidebarFilters.stops.oneStop || this.sidebarFilters.stops.multiStop) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0) return false;\n        const stopCount = flight.items[0].stopCount || 0;\n        return this.sidebarFilters.stops.direct && stopCount === 0 || this.sidebarFilters.stops.oneStop && stopCount === 1 || this.sidebarFilters.stops.multiStop && stopCount >= 2;\n      });\n    }\n    // Filtrer par horaire de départ\n    if (this.sidebarFilters.departureTime.earlyMorning || this.sidebarFilters.departureTime.morning || this.sidebarFilters.departureTime.afternoon || this.sidebarFilters.departureTime.evening) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].departure || !flight.items[0].departure.date) {\n          return false;\n        }\n        const departureDate = new Date(flight.items[0].departure.date);\n        const hours = departureDate.getHours();\n        return this.sidebarFilters.departureTime.earlyMorning && hours >= 0 && hours < 8 || this.sidebarFilters.departureTime.morning && hours >= 8 && hours < 12 || this.sidebarFilters.departureTime.afternoon && hours >= 12 && hours < 18 || this.sidebarFilters.departureTime.evening && hours >= 18 && hours < 24;\n      });\n    }\n    // Filtrer par horaire d'arrivée\n    if (this.sidebarFilters.arrivalTime.earlyMorning || this.sidebarFilters.arrivalTime.morning || this.sidebarFilters.arrivalTime.afternoon || this.sidebarFilters.arrivalTime.evening) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].arrival || !flight.items[0].arrival.date) {\n          return false;\n        }\n        const arrivalDate = new Date(flight.items[0].arrival.date);\n        const hours = arrivalDate.getHours();\n        return this.sidebarFilters.arrivalTime.earlyMorning && hours >= 0 && hours < 8 || this.sidebarFilters.arrivalTime.morning && hours >= 8 && hours < 12 || this.sidebarFilters.arrivalTime.afternoon && hours >= 12 && hours < 18 || this.sidebarFilters.arrivalTime.evening && hours >= 18 && hours < 24;\n      });\n    }\n    // Filtrer par compagnie aérienne\n    const selectedAirlines = Object.entries(this.sidebarFilters.airlines).filter(([_, selected]) => selected).map(([airline, _]) => airline);\n    if (selectedAirlines.length > 0) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].airline || !flight.items[0].airline.name) {\n          return false;\n        }\n        return selectedAirlines.includes(flight.items[0].airline.name);\n      });\n    }\n    // Étape 2: Appliquer le tri selon le filtre sélectionné en haut\n    switch (this.currentFilter) {\n      case 'cheapest':\n        this.filteredResults = this.sortByPrice(results);\n        break;\n      case 'shortest':\n        this.filteredResults = this.sortByDuration(results);\n        break;\n      case 'recommended':\n      default:\n        this.filteredResults = this.sortByRecommendation(results);\n        break;\n    }\n  }\n  // Trier les vols par prix (du moins cher au plus cher)\n  sortByPrice(flights) {\n    return flights.sort((a, b) => {\n      const priceA = this.getMinPriceAmount(a);\n      const priceB = this.getMinPriceAmount(b);\n      return priceA - priceB;\n    });\n  }\n  // Trier les vols par durée (du plus court au plus long)\n  sortByDuration(flights) {\n    return flights.sort((a, b) => {\n      const durationA = a.items && a.items[0] ? a.items[0].duration : Number.MAX_VALUE;\n      const durationB = b.items && b.items[0] ? b.items[0].duration : Number.MAX_VALUE;\n      return durationA - durationB;\n    });\n  }\n  // Trier les vols par recommandation (combinaison de prix, durée et autres facteurs)\n  sortByRecommendation(flights) {\n    return flights.sort((a, b) => {\n      // Calculer un score pour chaque vol basé sur plusieurs facteurs\n      const scoreA = this.calculateRecommendationScore(a);\n      const scoreB = this.calculateRecommendationScore(b);\n      return scoreB - scoreA; // Ordre décroissant (score plus élevé = meilleur)\n    });\n  }\n  // Calculer un score de recommandation pour un vol\n  calculateRecommendationScore(flight) {\n    if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n      return 0;\n    }\n    const item = flight.items[0];\n    const offer = flight.offers[0];\n    // Facteurs à considérer pour le score\n    const price = this.getMinPriceAmount(flight);\n    const duration = item.duration;\n    const stopCount = item.stopCount || 0;\n    const availability = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    // Normaliser les valeurs (plus le score est élevé, meilleur est le vol)\n    const priceScore = 1000 / (price + 100); // Inverse du prix (moins cher = meilleur score)\n    const durationScore = 1000 / (duration + 100); // Inverse de la durée (plus court = meilleur score)\n    const stopScore = 1 / (stopCount + 1); // Moins d'escales = meilleur score\n    const availabilityScore = Math.min(availability, 10) / 10; // Plus de disponibilité = meilleur score\n    // Pondération des facteurs (ajustable selon les préférences)\n    const weights = {\n      price: 0.4,\n      duration: 0.3,\n      stops: 0.2,\n      availability: 0.1 // 10% importance pour la disponibilité\n    };\n    // Calculer le score final pondéré\n    return priceScore * weights.price + durationScore * weights.duration + stopScore * weights.stops + availabilityScore * weights.availability;\n  }\n  // Obtenir le montant du prix minimum pour un vol\n  getMinPriceAmount(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return Number.MAX_VALUE;\n    }\n    return flight.offers.reduce((min, offer) => offer.price && offer.price.amount < min ? offer.price.amount : min, flight.offers[0].price ? flight.offers[0].price.amount : Number.MAX_VALUE);\n  }\n  // Calculer les prix minimums pour chaque option de filtre\n  calculateFilterPrices() {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      return;\n    }\n    // Réinitialiser les prix\n    this.resetFilterPrices();\n    // Collecter toutes les compagnies aériennes\n    const airlines = new Set();\n    // Parcourir tous les vols pour calculer les prix minimums\n    this.searchResults.forEach(flight => {\n      if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n        return;\n      }\n      const item = flight.items[0];\n      const price = this.getMinPriceAmount(flight);\n      // Ajouter la compagnie aérienne à la liste\n      if (item.airline && item.airline.name) {\n        airlines.add(item.airline.name);\n        // Initialiser le prix pour cette compagnie si nécessaire\n        if (!(item.airline.name in this.filterPrices.airlines)) {\n          this.filterPrices.airlines[item.airline.name] = Number.MAX_VALUE;\n        }\n        // Mettre à jour le prix minimum pour cette compagnie\n        this.filterPrices.airlines[item.airline.name] = Math.min(this.filterPrices.airlines[item.airline.name], price);\n      }\n      // Mettre à jour les prix par nombre d'escales\n      const stopCount = item.stopCount || 0;\n      if (stopCount === 0) {\n        this.filterPrices.stops.direct = Math.min(this.filterPrices.stops.direct, price);\n      } else if (stopCount === 1) {\n        this.filterPrices.stops.oneStop = Math.min(this.filterPrices.stops.oneStop, price);\n      } else {\n        this.filterPrices.stops.multiStop = Math.min(this.filterPrices.stops.multiStop, price);\n      }\n      // Mettre à jour les prix par horaire de départ\n      if (item.departure && item.departure.date) {\n        const departureDate = new Date(item.departure.date);\n        const departureHours = departureDate.getHours();\n        if (departureHours >= 0 && departureHours < 8) {\n          this.filterPrices.departureTime.earlyMorning = Math.min(this.filterPrices.departureTime.earlyMorning, price);\n        } else if (departureHours >= 8 && departureHours < 12) {\n          this.filterPrices.departureTime.morning = Math.min(this.filterPrices.departureTime.morning, price);\n        } else if (departureHours >= 12 && departureHours < 18) {\n          this.filterPrices.departureTime.afternoon = Math.min(this.filterPrices.departureTime.afternoon, price);\n        } else {\n          this.filterPrices.departureTime.evening = Math.min(this.filterPrices.departureTime.evening, price);\n        }\n      }\n      // Mettre à jour les prix par horaire d'arrivée\n      if (item.arrival && item.arrival.date) {\n        const arrivalDate = new Date(item.arrival.date);\n        const arrivalHours = arrivalDate.getHours();\n        if (arrivalHours >= 0 && arrivalHours < 8) {\n          this.filterPrices.arrivalTime.earlyMorning = Math.min(this.filterPrices.arrivalTime.earlyMorning, price);\n        } else if (arrivalHours >= 8 && arrivalHours < 12) {\n          this.filterPrices.arrivalTime.morning = Math.min(this.filterPrices.arrivalTime.morning, price);\n        } else if (arrivalHours >= 12 && arrivalHours < 18) {\n          this.filterPrices.arrivalTime.afternoon = Math.min(this.filterPrices.arrivalTime.afternoon, price);\n        } else {\n          this.filterPrices.arrivalTime.evening = Math.min(this.filterPrices.arrivalTime.evening, price);\n        }\n      }\n    });\n    // Initialiser les filtres de compagnies aériennes\n    airlines.forEach(airline => {\n      if (!(airline in this.sidebarFilters.airlines)) {\n        this.sidebarFilters.airlines[airline] = false;\n      }\n    });\n    // Remplacer les valeurs MAX_VALUE par 0 pour les options sans vols\n    this.cleanupFilterPrices();\n  }\n  // Réinitialiser les prix des filtres\n  resetFilterPrices() {\n    this.filterPrices = {\n      stops: {\n        direct: Number.MAX_VALUE,\n        oneStop: Number.MAX_VALUE,\n        multiStop: Number.MAX_VALUE\n      },\n      departureTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      arrivalTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      airlines: {}\n    };\n  }\n  // Nettoyer les prix des filtres (remplacer MAX_VALUE par 0)\n  cleanupFilterPrices() {\n    // Escales\n    if (this.filterPrices.stops.direct === Number.MAX_VALUE) this.filterPrices.stops.direct = 0;\n    if (this.filterPrices.stops.oneStop === Number.MAX_VALUE) this.filterPrices.stops.oneStop = 0;\n    if (this.filterPrices.stops.multiStop === Number.MAX_VALUE) this.filterPrices.stops.multiStop = 0;\n    // Horaires de départ\n    if (this.filterPrices.departureTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.departureTime.earlyMorning = 0;\n    if (this.filterPrices.departureTime.morning === Number.MAX_VALUE) this.filterPrices.departureTime.morning = 0;\n    if (this.filterPrices.departureTime.afternoon === Number.MAX_VALUE) this.filterPrices.departureTime.afternoon = 0;\n    if (this.filterPrices.departureTime.evening === Number.MAX_VALUE) this.filterPrices.departureTime.evening = 0;\n    // Horaires d'arrivée\n    if (this.filterPrices.arrivalTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.arrivalTime.earlyMorning = 0;\n    if (this.filterPrices.arrivalTime.morning === Number.MAX_VALUE) this.filterPrices.arrivalTime.morning = 0;\n    if (this.filterPrices.arrivalTime.afternoon === Number.MAX_VALUE) this.filterPrices.arrivalTime.afternoon = 0;\n    if (this.filterPrices.arrivalTime.evening === Number.MAX_VALUE) this.filterPrices.arrivalTime.evening = 0;\n    // Compagnies aériennes\n    Object.keys(this.filterPrices.airlines).forEach(airline => {\n      if (this.filterPrices.airlines[airline] === Number.MAX_VALUE) {\n        this.filterPrices.airlines[airline] = 0;\n      }\n    });\n  }\n  // Basculer l'état d'expansion d'une section\n  toggleSection(section) {\n    this.expandedSections[section] = !this.expandedSections[section];\n  }\n  // Basculer un filtre d'escale\n  toggleStopFilter(filter) {\n    this.sidebarFilters.stops[filter] = !this.sidebarFilters.stops[filter];\n    this.applyAllFilters();\n  }\n  // Basculer un filtre d'horaire de départ\n  toggleDepartureTimeFilter(filter) {\n    this.sidebarFilters.departureTime[filter] = !this.sidebarFilters.departureTime[filter];\n    this.applyAllFilters();\n  }\n  // Basculer un filtre d'horaire d'arrivée\n  toggleArrivalTimeFilter(filter) {\n    this.sidebarFilters.arrivalTime[filter] = !this.sidebarFilters.arrivalTime[filter];\n    this.applyAllFilters();\n  }\n  // Basculer un filtre de compagnie aérienne\n  toggleAirlineFilter(airline) {\n    this.sidebarFilters.airlines[airline] = !this.sidebarFilters.airlines[airline];\n    this.applyAllFilters();\n  }\n  // Effacer tous les filtres\n  clearAllFilters() {\n    // Réinitialiser les filtres d'escales\n    this.sidebarFilters.stops.direct = false;\n    this.sidebarFilters.stops.oneStop = false;\n    this.sidebarFilters.stops.multiStop = false;\n    // Réinitialiser les filtres d'horaires de départ\n    this.sidebarFilters.departureTime.earlyMorning = false;\n    this.sidebarFilters.departureTime.morning = false;\n    this.sidebarFilters.departureTime.afternoon = false;\n    this.sidebarFilters.departureTime.evening = false;\n    // Réinitialiser les filtres d'horaires d'arrivée\n    this.sidebarFilters.arrivalTime.earlyMorning = false;\n    this.sidebarFilters.arrivalTime.morning = false;\n    this.sidebarFilters.arrivalTime.afternoon = false;\n    this.sidebarFilters.arrivalTime.evening = false;\n    // Réinitialiser les filtres de compagnies aériennes\n    Object.keys(this.sidebarFilters.airlines).forEach(airline => {\n      this.sidebarFilters.airlines[airline] = false;\n    });\n    // Appliquer les filtres (qui seront tous désactivés)\n    this.applyAllFilters();\n  }\n  // Formater le prix pour l'affichage\n  formatPrice(price) {\n    if (price === 0) return '-';\n    return price.toFixed(0) + ' €';\n  }\n  // Obtenir les clés des compagnies aériennes\n  getAirlineKeys() {\n    return Object.keys(this.sidebarFilters.airlines);\n  }\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight) {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n    header.appendChild(logo);\n    header.appendChild(title);\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n        generalInfo.appendChild(airlineInfo);\n      }\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n      routeSection.appendChild(routeVisual);\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n          segmentsList.appendChild(segmentItem);\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n            segmentsList.appendChild(layover);\n          }\n        });\n        routeSection.appendChild(segmentsList);\n      }\n    }\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n          const baggageContainer = document.createElement('div');\n          baggageContainer.style.display = 'flex';\n          baggageContainer.style.flexDirection = 'column';\n          baggageContainer.style.gap = '10px';\n          baggageContainer.style.marginBottom = '15px';\n          // Filtrer et regrouper les bagages par type\n          const checkedBaggage = offer.baggageInformations.filter(b => b.baggageType === 2);\n          const cabinBaggage = offer.baggageInformations.filter(b => b.baggageType === 1);\n          const handBaggage = offer.baggageInformations.filter(b => b.baggageType === 3);\n          // Bagages en soute\n          if (checkedBaggage.length > 0) {\n            checkedBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#e7f5ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #c5e1f9';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-suitcase\" style=\"color: #4a6fa5; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Checked Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight && baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n          // Bagages cabine\n          if (cabinBaggage.length > 0) {\n            cabinBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#f3f0ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #e5dbff';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Cabin Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight && baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          } else {\n            // Ajouter un bagage cabine par défaut si aucun n'est spécifié\n            const baggageItem = document.createElement('div');\n            baggageItem.style.display = 'flex';\n            baggageItem.style.alignItems = 'center';\n            baggageItem.style.padding = '10px 15px';\n            baggageItem.style.backgroundColor = '#f3f0ff';\n            baggageItem.style.borderRadius = '6px';\n            baggageItem.style.border = '1px solid #e5dbff';\n            const baggageIcon = document.createElement('div');\n            baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n            const baggageInfo = document.createElement('div');\n            baggageInfo.style.display = 'flex';\n            baggageInfo.style.flexDirection = 'column';\n            const baggageType = document.createElement('div');\n            baggageType.textContent = 'Cabin Baggage';\n            baggageType.style.fontWeight = '600';\n            baggageType.style.fontSize = '14px';\n            const baggageDetails = document.createElement('div');\n            baggageDetails.style.fontSize = '12px';\n            baggageDetails.style.color = '#666';\n            baggageDetails.textContent = 'Included';\n            baggageInfo.appendChild(baggageType);\n            baggageInfo.appendChild(baggageDetails);\n            baggageItem.appendChild(baggageIcon);\n            baggageItem.appendChild(baggageInfo);\n            baggageContainer.appendChild(baggageItem);\n          }\n          // Bagages à main\n          if (handBaggage.length > 0) {\n            handBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#fff4e6';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #ffe8cc';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-shopping-bag\" style=\"color: #e8590c; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Hand Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight && baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n          offerItem.appendChild(baggageContainer);\n        }\n        offerItem.appendChild(offerDetails);\n        offersList.appendChild(offerItem);\n      });\n      offersSection.appendChild(offersList);\n    }\n    // 4. Section des services\n    // Note: services might be in different locations depending on the flight type\n    const flightItem = flight.items && flight.items[0];\n    const services = flightItem && (\n    // Try to get services from different possible locations\n    flightItem.services || flightItem.segments && flightItem.segments[0] && flightItem.segments[0].services);\n    if (services && services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n      services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n  // Méthode utilitaire pour créer une section\n  createSection(title, iconClass) {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n    return section;\n  }\n  // Méthode utilitaire pour créer une ligne d'information\n  createInfoRow(label, value) {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n    return row;\n  }\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight) {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId && flight.id) {\n        searchId = flight.id;\n      } else if (!searchId) {\n        // Utiliser un ID par défaut si aucun ID n'est disponible\n        searchId = 'default-search-id';\n      }\n      // Obtenir les informations de passagers\n      const passengerCounts = this.sharedDataService.getPassengerCounts();\n      // Sérialiser les informations de passagers pour les passer dans l'URL\n      const passengerInfo = JSON.stringify(passengerCounts);\n      console.log('Navigating to get-offer with searchId:', searchId, 'offerId:', offerId, 'and passengers:', passengerInfo);\n      // Rediriger vers la page get-offer avec les informations de passagers\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId,\n          passengers: passengerInfo\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n  preloadLocations() {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n  setupAutocomplete() {\n    // Charger les locations par type par défaut\n    const departureLocationType = 2; // Type 2 (City) par défaut\n    const arrivalLocationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(departureLocationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(departureLocationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(arrivalLocationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(arrivalLocationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  displayLocation(location) {\n    if (!location) return '';\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n  // Méthode pour changer le type de recherche (aller simple, aller-retour, multi-city)\n  setSearchType(type) {\n    this.currentSearchType = type;\n    // Mettre à jour le type de service dans le formulaire\n    if (type === this.SEARCH_TYPE_ONE_WAY) {\n      this.searchForm.get('serviceTypes')?.setValue(['1']);\n      // Rendre le champ de date de retour optionnel\n      this.searchForm.get('returnDate')?.clearValidators();\n      // Réinitialiser les segments pour multi-city\n      this.getSegmentsArray().clear();\n    } else if (type === this.SEARCH_TYPE_ROUND_TRIP) {\n      this.searchForm.get('serviceTypes')?.setValue(['2']);\n      // Rendre le champ de date de retour obligatoire\n      this.searchForm.get('returnDate')?.setValidators([Validators.required]);\n      // Réinitialiser les segments pour multi-city\n      this.getSegmentsArray().clear();\n    } else if (type === this.SEARCH_TYPE_MULTI_CITY) {\n      this.searchForm.get('serviceTypes')?.setValue(['3']);\n      // Rendre le champ de date de retour optionnel\n      this.searchForm.get('returnDate')?.clearValidators();\n      // Initialiser au moins un segment pour multi-city\n      if (this.getSegmentsArray().length === 0) {\n        this.addSegment();\n      }\n    }\n    // Mettre à jour les validateurs\n    this.searchForm.get('returnDate')?.updateValueAndValidity();\n  }\n  // Méthode pour obtenir le FormArray des segments\n  getSegmentsArray() {\n    return this.searchForm.get('segments');\n  }\n  // Méthode pour créer un nouveau segment\n  createSegment() {\n    return this.fb.group({\n      departureLocation: ['', Validators.required],\n      arrivalLocation: ['', Validators.required],\n      departureDate: [this.minDate, Validators.required]\n    });\n  }\n  // Méthode pour ajouter un segment\n  addSegment() {\n    // Récupérer les valeurs des champs standards\n    const standardDepartureLocation = this.searchForm.get('departureLocation')?.value;\n    const standardArrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const standardDepartureDate = this.searchForm.get('departureDate')?.value;\n    // Créer un nouveau segment avec les valeurs des champs standards\n    const newSegment = this.fb.group({\n      departureLocation: [standardDepartureLocation, Validators.required],\n      arrivalLocation: [standardArrivalLocation, Validators.required],\n      departureDate: [standardDepartureDate, Validators.required]\n    });\n    // Ajouter le segment au FormArray\n    this.getSegmentsArray().push(newSegment);\n  }\n  // Méthode pour supprimer un segment\n  removeSegment(index) {\n    // Ne pas supprimer le dernier segment\n    if (this.getSegmentsArray().length > 1) {\n      this.getSegmentsArray().removeAt(index);\n    }\n  }\n  onSearch() {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n    const formValue = this.searchForm.value;\n    // Vérifier la valeur de la classe de vol et le type de recherche\n    console.log('Form values:', formValue);\n    console.log('Selected flight class:', formValue.flightClass);\n    console.log('Current search type:', this.currentSearchType);\n    // Créer la requête en fonction du type de recherche (aller simple ou aller-retour)\n    let request;\n    if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n      // Requête pour vol aller simple\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['1'],\n        CheckIn: formValue.departureDate,\n        DepartureLocations: [{\n          id: formValue.departureLocation?.id || '',\n          type: 2 // Type 2 (City) par défaut\n        }],\n\n        ArrivalLocations: [{\n          id: formValue.arrivalLocation?.id || '',\n          type: 5 // Type 5 (Airport) par défaut\n        }],\n\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n      console.log('Sending one-way search request with ServiceTypes:', request.ServiceTypes);\n    } else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      // Requête pour vol aller-retour\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['2'],\n        CheckIn: formValue.departureDate,\n        ReturnDate: formValue.returnDate,\n        DepartureLocations: [{\n          id: formValue.departureLocation?.id || '',\n          type: 2 // Type 2 (City) par défaut\n        }],\n\n        ArrivalLocations: [{\n          id: formValue.arrivalLocation?.id || '',\n          type: 5 // Type 5 (Airport) par défaut\n        }],\n\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n      console.log('Sending round-trip search request with ServiceTypes:', request.ServiceTypes);\n    }\n    // Créer la requête appropriée selon le type de recherche\n    if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n      // Requête pour vol aller simple\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['1'],\n        CheckIn: formValue.departureDate,\n        DepartureLocations: [{\n          id: formValue.departureLocation?.id || '',\n          type: 2 // Type 2 (City) par défaut\n        }],\n\n        ArrivalLocations: [{\n          id: formValue.arrivalLocation?.id || '',\n          type: 5 // Type 5 (Airport) par défaut\n        }],\n\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n      console.log('Sending one-way search request with ServiceTypes:', request.ServiceTypes);\n    } else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      // Requête pour vol aller-retour\n      // Calculer le nombre de nuits entre la date de départ et de retour\n      const departureDate = new Date(formValue.departureDate);\n      const returnDate = new Date(formValue.returnDate);\n      // Calculer le nombre de nuits (différence en jours)\n      const diffTime = Math.abs(returnDate.getTime() - departureDate.getTime());\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      console.log(`Round-trip search: ${diffDays} nights between departure and return`);\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['2'],\n        CheckIn: formValue.departureDate,\n        Night: diffDays,\n        DepartureLocations: [{\n          id: formValue.departureLocation?.id || '',\n          type: 2 // Type 2 (City) par défaut\n        }],\n\n        ArrivalLocations: [{\n          id: formValue.arrivalLocation?.id || '',\n          type: 5 // Type 5 (Airport) par défaut\n        }],\n\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n      console.log('Sending round-trip search request with ServiceTypes:', request.ServiceTypes);\n    } else if (this.currentSearchType === this.SEARCH_TYPE_MULTI_CITY) {\n      // Requête pour vol multi-destinations\n      const segments = this.getSegmentsArray().value;\n      // Créer les segments de vol\n      const flightSegments = segments.map(segment => ({\n        CheckIn: segment.departureDate,\n        DepartureLocations: [{\n          id: segment.departureLocation?.id || '',\n          type: 2 // Type 2 (City) par défaut\n        }],\n\n        ArrivalLocations: [{\n          id: segment.arrivalLocation?.id || '',\n          type: 5 // Type 5 (Airport) par défaut\n        }]\n      }));\n      // Créer le tableau des dates de départ (CheckIns)\n      const checkIns = segments.map(segment => segment.departureDate);\n      // Si aucun segment n'est défini, utiliser les valeurs par défaut du formulaire principal\n      if (flightSegments.length === 0) {\n        flightSegments.push({\n          CheckIn: formValue.departureDate,\n          DepartureLocations: [{\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }],\n\n          ArrivalLocations: [{\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }]\n        });\n\n        checkIns.push(formValue.departureDate);\n      }\n      console.log('Multi-city segments:', flightSegments);\n      console.log('Multi-city CheckIns:', checkIns);\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['3'],\n        FlightSegments: flightSegments,\n        // Ajouter les paramètres CheckIn et CheckIns requis par l'API\n        CheckIn: checkIns[0],\n        CheckIns: checkIns,\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n      console.log('Sending multi-city search request with ServiceTypes:', request.ServiceTypes);\n    }\n    // Utiliser la méthode searchPrice pour envoyer la requête\n    this.productService.searchPrice(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.header.success) {\n          this.searchResults = response.body.flights;\n          // Analyser les classes de vol dans les résultats\n          console.group('Flight Class Analysis');\n          console.log('Selected flight class:', formValue.flightClass);\n          console.log('Total flights received:', response.body.flights.length);\n          let responseType = 'One Way';\n          if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n            responseType = 'Round Trip';\n          } else if (this.currentSearchType === this.SEARCH_TYPE_MULTI_CITY) {\n            responseType = 'Multi-City';\n          }\n          console.log('Response type:', responseType);\n          // Compter les vols par classe\n          const flightsByClass = {\n            [FlightClassType.PROMO.toString()]: 0,\n            [FlightClassType.ECONOMY.toString()]: 0,\n            [FlightClassType.BUSINESS.toString()]: 0,\n            'unknown': 0\n          };\n          response.body.flights.forEach((flight, index) => {\n            if (flight.items && flight.items.length > 0 && flight.items[0].flightClass) {\n              const classType = flight.items[0].flightClass.type.toString();\n              if (flightsByClass[classType] !== undefined) {\n                flightsByClass[classType]++;\n              } else {\n                flightsByClass['unknown']++;\n              }\n              // Afficher les détails de classe pour chaque vol\n              console.log(`Flight ${flight.id} class:`, flight.items[0].flightClass ? `${flight.items[0].flightClass.name} (type: ${flight.items[0].flightClass.type})` : 'No class info');\n              // Afficher la structure complète du premier vol pour analyse\n              if (index === 0) {\n                console.group('First Flight Structure');\n                console.log('Flight ID:', flight.id);\n                console.log('Flight Items:', flight.items);\n                if (flight.items && flight.items.length > 0) {\n                  console.log('First Item FlightClass:', flight.items[0].flightClass);\n                  if (flight.items[0].segments) {\n                    console.log('Segments:', flight.items[0].segments);\n                    flight.items[0].segments.forEach((segment, segIndex) => {\n                      console.log(`Segment ${segIndex} FlightClass:`, segment.flightClass);\n                    });\n                  }\n                }\n                if (flight.offers && flight.offers.length > 0) {\n                  console.log('First Offer:', flight.offers[0]);\n                  console.log('FlightClassInformations:', flight.offers[0].flightClassInformations);\n                }\n                console.groupEnd();\n              }\n            } else {\n              flightsByClass['unknown']++;\n            }\n          });\n          console.log('Flights by class:', flightsByClass);\n          console.groupEnd();\n          // Calculer les prix minimums pour chaque option de filtre\n          this.calculateFilterPrices();\n          // Appliquer le filtre actuel aux résultats\n          this.applyAllFilters();\n          // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n          // console.log('Full API Response:', JSON.stringify(response, null, 2));\n          // Analyser les données de disponibilité\n          if (response.body && response.body.flights && response.body.flights.length > 0) {\n            console.group('Availability Analysis');\n            console.log('Total flights:', response.body.flights.length);\n            // Compter les vols avec des offres\n            const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n            console.log('Flights with offers:', flightsWithOffers.length);\n            // Analyser les valeurs de disponibilité\n            const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => {\n              const offer = o;\n              return offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n            }));\n            console.log('Availability values:', availabilityValues);\n            // Compter les différentes valeurs de disponibilité\n            const availabilityCounts = availabilityValues.reduce((acc, val) => {\n              if (val !== undefined) {\n                acc[val] = (acc[val] || 0) + 1;\n              }\n              return acc;\n            }, {});\n            console.log('Availability counts:', availabilityCounts);\n            // Vérifier les vols réservables\n            const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => {\n              const offer = o;\n              return offer.reservableInfo && offer.reservableInfo.reservable === true;\n            }));\n            console.log('Reservable flights:', reservableFlights.length);\n            console.groupEnd();\n          }\n          // Vérifier si searchId existe dans le corps de la réponse\n          if (response.body && response.body.searchId) {\n            this.lastSearchId = response.body.searchId;\n            console.log('Search ID found in body.searchId:', this.lastSearchId);\n          }\n          // Vérifier si searchId existe dans l'en-tête de la réponse\n          else if (response.header && response.header.requestId) {\n            this.lastSearchId = response.header.requestId;\n            console.log('Search ID found in header.requestId:', this.lastSearchId);\n          }\n          // Vérifier d'autres emplacements possibles\n          else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n            this.lastSearchId = response.body.flights[0].id;\n            console.log('Using flight ID as search ID:', this.lastSearchId);\n          } else {\n            console.error('No search ID found in the response!');\n            console.log('Response structure:', Object.keys(response));\n            if (response.body) console.log('Body structure:', Object.keys(response.body));\n            if (response.header) console.log('Header structure:', Object.keys(response.header));\n          }\n        } else {\n          this.errorMessage = 'The search could not be completed. Please try again.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred during the search. Please try again.';\n        console.error('Error searching flights:', error);\n      }\n    });\n  }\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  // Formater la durée en heures et minutes\n  formatDuration(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n  // Formater la date pour l'affichage\n  formatDate(dateString) {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // Formater la date pour l'affichage avec indication si elle diffère de la date demandée\n  formatDateWithRequestComparison(dateString, requestedDate) {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    const formatted = date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n    // Si une date demandée est fournie, vérifier si la date réelle est différente\n    if (requestedDate) {\n      const requested = new Date(requestedDate);\n      // Comparer seulement les dates (sans l'heure)\n      const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n      const requestedOnly = new Date(requested.getFullYear(), requested.getMonth(), requested.getDate());\n      if (dateOnly.getTime() !== requestedOnly.getTime()) {\n        // Ajouter une indication visuelle que la date diffère de celle demandée\n        return `${formatted} <span class=\"date-differs\" title=\"Differs from requested date: ${requestedOnly.toLocaleDateString('fr-FR')}\">*</span>`;\n      }\n    }\n    return formatted;\n  }\n  // Obtenir les dates de départ et de retour d'un vol aller-retour\n  getFlightDates(flight) {\n    const result = {\n      outbound: 'N/A',\n      inbound: 'N/A'\n    };\n    if (!flight || !flight.items || flight.items.length === 0) {\n      return result;\n    }\n    // Chercher les segments aller (outbound)\n    const outboundItems = flight.items.filter(item => item.segmentNumber === 1 || item.route === 1);\n    if (outboundItems.length > 0 && outboundItems[0].departure && outboundItems[0].departure.date) {\n      result.outbound = outboundItems[0].departure.date;\n    } else if (flight.items[0] && flight.items[0].departure && flight.items[0].departure.date) {\n      result.outbound = flight.items[0].departure.date;\n    }\n    // Chercher les segments retour (inbound)\n    const inboundItems = flight.items.filter(item => item.segmentNumber === 2 || item.route === 2);\n    if (inboundItems.length > 0 && inboundItems[0].departure && inboundItems[0].departure.date) {\n      result.inbound = inboundItems[0].departure.date;\n    } else if (flight.items.length > 1 && flight.items[1] && flight.items[1].departure && flight.items[1].departure.date) {\n      result.inbound = flight.items[1].departure.date;\n    }\n    return result;\n  }\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n    const minOffer = flight.offers.reduce((min, offer) => offer.price && min.price && offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight) {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    return availabilityValue > 0;\n  }\n  // Vérifier si les dates réelles du vol correspondent aux dates demandées\n  checkFlightDatesMatch(flight, requestedDepartureDate, requestedReturnDate) {\n    const result = {\n      match: true,\n      message: ''\n    };\n    if (!flight || !flight.items || flight.items.length === 0) {\n      return result;\n    }\n    // Obtenir les dates réelles du vol\n    const flightDates = this.getFlightDates(flight);\n    // Comparer la date de départ\n    if (requestedDepartureDate && flightDates.outbound !== 'N/A') {\n      const requestedDepDate = new Date(requestedDepartureDate);\n      const actualDepDate = new Date(flightDates.outbound);\n      // Comparer seulement les dates (sans l'heure)\n      const requestedDepDay = new Date(requestedDepDate.getFullYear(), requestedDepDate.getMonth(), requestedDepDate.getDate());\n      const actualDepDay = new Date(actualDepDate.getFullYear(), actualDepDate.getMonth(), actualDepDate.getDate());\n      if (requestedDepDay.getTime() !== actualDepDay.getTime()) {\n        result.match = false;\n        result.message += `Departure date differs from requested: ${actualDepDay.toLocaleDateString()} instead of ${requestedDepDay.toLocaleDateString()}. `;\n      }\n    }\n    // Comparer la date de retour (si c'est un vol aller-retour)\n    if (requestedReturnDate && flightDates.inbound !== 'N/A') {\n      const requestedRetDate = new Date(requestedReturnDate);\n      const actualRetDate = new Date(flightDates.inbound);\n      // Comparer seulement les dates (sans l'heure)\n      const requestedRetDay = new Date(requestedRetDate.getFullYear(), requestedRetDate.getMonth(), requestedRetDate.getDate());\n      const actualRetDay = new Date(actualRetDate.getFullYear(), actualRetDate.getMonth(), actualRetDate.getDate());\n      if (requestedRetDay.getTime() !== actualRetDay.getTime()) {\n        result.match = false;\n        result.message += `Return date differs from requested: ${actualRetDay.toLocaleDateString()} instead of ${requestedRetDay.toLocaleDateString()}.`;\n      }\n    }\n    return result;\n  }\n  // Afficher un message d'information sur les dates des vols\n  getFlightDateInfoMessage(flight, requestedDepartureDate, requestedReturnDate) {\n    // Vérifier si c'est un vol aller-retour\n    const isRoundTrip = this.isRoundTripFlight(flight);\n    // Obtenir les dates réelles du vol\n    const flightDates = this.getFlightDates(flight);\n    // Vérifier si les dates correspondent\n    const dateCheck = this.checkFlightDatesMatch(flight, requestedDepartureDate, requestedReturnDate);\n    if (!dateCheck.match) {\n      // Si les dates ne correspondent pas, afficher un message d'information\n      return `<div class=\"date-info-message\">\n                <i class=\"fas fa-info-circle\"></i>\n                <span>The actual flight dates differ from your search criteria. ${dateCheck.message}</span>\n              </div>`;\n    } else if (isRoundTrip) {\n      // Si c'est un vol aller-retour avec des dates correspondantes\n      return `<div class=\"date-info-message date-info-success\">\n                <i class=\"fas fa-check-circle\"></i>\n                <span>Round-trip flight: Outbound on ${new Date(flightDates.outbound).toLocaleDateString()}, Return on ${new Date(flightDates.inbound).toLocaleDateString()}</span>\n              </div>`;\n    }\n    return '';\n  }\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations() {\n    const locationType = 2; // Type 2 (City) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations() {\n    const locationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options de départ pour un segment spécifique\n  showDepartureLocationsForSegment(index) {\n    const locationType = 2; // Type 2 (City) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion pour ce segment spécifique\n      const input = document.getElementById(`departureLocation${index}`);\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options d'arrivée pour un segment spécifique\n  showArrivalLocationsForSegment(index) {\n    const locationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion pour ce segment spécifique\n      const input = document.getElementById(`arrivalLocation${index}`);\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations() {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      arrivalLocation: departureLocation\n    });\n  }\n  // Échanger les emplacements de départ et d'arrivée pour un segment spécifique\n  swapSegmentLocations(index) {\n    const segments = this.getSegmentsArray();\n    const segment = segments.at(index);\n    if (segment) {\n      const departureLocation = segment.get('departureLocation')?.value;\n      const arrivalLocation = segment.get('arrivalLocation')?.value;\n      segment.patchValue({\n        departureLocation: arrivalLocation,\n        arrivalLocation: departureLocation\n      });\n    }\n  }\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString) {\n    if (!dateString) return 'N/A';\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType) {\n    switch (baggageType) {\n      case 1:\n        return 'Cabin Baggage';\n      case 2:\n        return 'Checked Baggage';\n      case 3:\n        return 'Hand Baggage';\n      default:\n        return 'Baggage';\n    }\n  }\n  // Filtrer les bagages par type\n  filterBaggageByType(baggageInformations, type) {\n    if (!baggageInformations || !Array.isArray(baggageInformations)) {\n      return [];\n    }\n    return baggageInformations.filter(baggage => baggage.baggageType === type);\n  }\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType) {\n    switch (passengerType) {\n      case 1:\n        return 'Adult';\n      case 2:\n        return 'Child';\n      case 3:\n        return 'Infant';\n      default:\n        return 'Passenger';\n    }\n  }\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment, nextSegment) {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date || !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n  // Calculer la durée du séjour entre deux dates\n  calculateStayDuration(outboundArrivalDate, inboundDepartureDate) {\n    if (!outboundArrivalDate || !inboundDepartureDate) {\n      return 'N/A';\n    }\n    const arrival = new Date(outboundArrivalDate);\n    const departure = new Date(inboundDepartureDate);\n    // Calculer la différence en jours\n    const diffTime = Math.abs(departure.getTime() - arrival.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 0) {\n      return 'Same day';\n    } else if (diffDays === 1) {\n      return '1 day';\n    } else {\n      return `${diffDays} days`;\n    }\n  }\n  // Détecter si un vol est un aller-retour\n  isRoundTripFlight(flight) {\n    if (!flight || !flight.items || flight.items.length === 0) {\n      return false;\n    }\n    // Méthode 1: Vérifier le segmentNumber (1 = aller, 2 = retour)\n    // Dans la réponse API, les vols aller-retour ont souvent un item avec segmentNumber = 2\n    if (flight.items.some(item => item.segmentNumber === 2)) {\n      console.log(`Flight ${flight.id} identified as round-trip by segmentNumber`);\n      return true;\n    }\n    // Méthode 2: Vérifier si le vol a plusieurs items (aller et retour)\n    if (flight.items.length > 1) {\n      console.log(`Flight ${flight.id} identified as round-trip by multiple items`);\n      return true;\n    }\n    // Méthode 3: Vérifier si le premier item a des segments qui forment un aller-retour\n    if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      const firstSegment = flight.items[0].segments[0];\n      const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n      // Vérifier si le premier segment part de A vers B et le dernier segment part de B vers A\n      if (firstSegment && firstSegment.departure && firstSegment.arrival && lastSegment && lastSegment.departure && lastSegment.arrival) {\n        const firstDeparture = firstSegment.departure.airport?.code || firstSegment.departure.city?.name;\n        const firstArrival = firstSegment.arrival.airport?.code || firstSegment.arrival.city?.name;\n        const lastDeparture = lastSegment.departure.airport?.code || lastSegment.departure.city?.name;\n        const lastArrival = lastSegment.arrival.airport?.code || lastSegment.arrival.city?.name;\n        // Si le dernier segment revient au point de départ du premier segment (boucle)\n        if (firstDeparture && lastArrival && firstDeparture === lastArrival) {\n          console.log(`Flight ${flight.id} identified as round-trip by segment loop`);\n          return true;\n        }\n        // Vérifier si c'est un aller-retour classique (A→B, B→A)\n        if (firstDeparture && firstArrival && lastDeparture && lastArrival && firstDeparture === lastArrival && firstArrival === lastDeparture) {\n          console.log(`Flight ${flight.id} identified as round-trip by segment pattern`);\n          return true;\n        }\n      }\n    }\n    // Méthode 4: Vérifier si le vol a été recherché avec ServiceTypes = 2 (aller-retour)\n    if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      console.log(`Flight ${flight.id} identified as round-trip by search type`);\n      return true;\n    }\n    return false;\n  }\n  // Obtenir les segments aller d'un vol aller-retour\n  getOutboundSegments(flight) {\n    if (!flight.items) return [];\n    // Méthode 1: Utiliser le segmentNumber pour identifier les segments aller (segmentNumber = 1)\n    const outboundItems = flight.items.filter(item => item.segmentNumber === 1);\n    if (outboundItems.length > 0) {\n      console.log(`Flight ${flight.id} outbound segments identified by segmentNumber`);\n      return outboundItems[0].segments || [outboundItems[0]];\n    }\n    // Méthode 2: Si le vol a plusieurs items, le premier est généralement l'aller\n    if (flight.items.length > 1) {\n      console.log(`Flight ${flight.id} outbound segments identified by multiple items`);\n      return flight.items[0].segments || [flight.items[0]];\n    }\n    // Méthode 3: Si le vol a un seul item avec plusieurs segments\n    if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      // Pour un aller-retour, on considère la première moitié des segments comme l'aller\n      const halfLength = Math.ceil(flight.items[0].segments.length / 2);\n      console.log(`Flight ${flight.id} outbound segments identified by splitting segments`);\n      return flight.items[0].segments.slice(0, halfLength);\n    }\n    // Par défaut, retourner tous les segments du premier item\n    return flight.items[0].segments || [flight.items[0]];\n  }\n  // Obtenir les segments retour d'un vol aller-retour\n  getInboundSegments(flight) {\n    if (!flight.items) return [];\n    // Méthode 1: Utiliser le segmentNumber pour identifier les segments retour (segmentNumber = 2)\n    const inboundItems = flight.items.filter(item => item.segmentNumber === 2);\n    if (inboundItems.length > 0) {\n      console.log(`Flight ${flight.id} inbound segments identified by segmentNumber`);\n      return inboundItems[0].segments || [inboundItems[0]];\n    }\n    // Méthode 2: Si le vol a plusieurs items, le second est généralement le retour\n    if (flight.items.length > 1) {\n      console.log(`Flight ${flight.id} inbound segments identified by multiple items`);\n      return flight.items[1].segments || [flight.items[1]];\n    }\n    // Méthode 3: Si le vol a un seul item avec plusieurs segments\n    if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      // Pour un aller-retour, on considère la seconde moitié des segments comme le retour\n      const halfLength = Math.ceil(flight.items[0].segments.length / 2);\n      console.log(`Flight ${flight.id} inbound segments identified by splitting segments`);\n      return flight.items[0].segments.slice(halfLength);\n    }\n    // Si aucun segment retour n'est trouvé\n    return [];\n  }\n  // Détecter si un vol est un multi-city\n  isMultiCityFlight(flight) {\n    if (!flight || !flight.items || flight.items.length === 0) {\n      return false;\n    }\n    // Méthode 1: Vérifier si le vol a un segmentNumber > 2\n    if (flight.items.some(item => item.segmentNumber > 2)) {\n      console.log(`Flight ${flight.id} identified as multi-city by segmentNumber > 2`);\n      return true;\n    }\n    // Méthode 2: Vérifier si le vol a plus de 2 items (plus que aller et retour)\n    if (flight.items.length > 2) {\n      console.log(`Flight ${flight.id} identified as multi-city by having more than 2 items`);\n      return true;\n    }\n    // Méthode 3: Vérifier si le vol a été recherché avec ServiceTypes = 3 (multi-city)\n    if (this.currentSearchType === this.SEARCH_TYPE_MULTI_CITY) {\n      console.log(`Flight ${flight.id} identified as multi-city by search type`);\n      return true;\n    }\n    return false;\n  }\n};\n__decorate([HostListener('document:click', ['$event'])], SearchPriceComponent.prototype, \"onDocumentClick\", null);\nSearchPriceComponent = __decorate([Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css', './flight-type-tabs.css', './round-trip-styles.css', './multi-city-styles.css'],\n  encapsulation: ViewEncapsulation.None\n})], SearchPriceComponent);", "map": {"version": 3, "names": ["Component", "ViewEncapsulation", "HostListener", "FormGroup", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "of", "FlightClassType", "LocationType", "PassengerType", "SearchPriceComponent", "constructor", "fb", "productService", "router", "sharedDataService", "departureLocations", "arrivalLocations", "isLoading", "searchResults", "filteredResults", "hasSearched", "errorMessage", "lastSearchId", "SEARCH_TYPE_ONE_WAY", "SEARCH_TYPE_ROUND_TRIP", "SEARCH_TYPE_MULTI_CITY", "currentSearchType", "showPassengerDropdown", "passengerCounts", "Adult", "Child", "Infant", "currentFilter", "filterOptions", "value", "label", "icon", "sidebarFilters", "stops", "direct", "oneStop", "multiStop", "departureTime", "earlyMorning", "morning", "afternoon", "evening", "arrivalTime", "airlines", "expandedSections", "filterPrices", "passengerTypes", "flightClasses", "PROMO", "ECONOMY", "BUSINESS", "minDate", "Date", "toISOString", "split", "searchForm", "group", "productType", "required", "serviceTypes", "departureLocation", "arrivalLocation", "departureDate", "returnDate", "segments", "array", "flightClass", "nonStop", "culture", "currency", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightBaggageGetOption", "set<PERSON>assengerCounts", "onDocumentClick", "event", "clickedElement", "target", "passengerSelector", "document", "querySelector", "contains", "togglePassengerDropdown", "stopPropagation", "closePassengerDropdown", "get<PERSON>assengerCount", "type", "increasePassengerCount", "getTotalPassengers", "decreasePassengerCount", "Object", "values", "reduce", "sum", "count", "getPassengersArray", "ngOnInit", "setupAutocomplete", "preloadLocations", "console", "log", "applyFilter", "filterValue", "applyAllFilters", "isFlightMatchingClass", "flight", "selectedClass", "items", "length", "id", "item", "name", "segment", "offers", "offer", "flightClassInformations", "classInfo", "results", "get", "undefined", "classCounts", "for<PERSON>ach", "disableClassFiltering", "filter", "stopCount", "departure", "date", "hours", "getHours", "arrival", "arrivalDate", "selectedAirlines", "entries", "_", "selected", "airline", "includes", "sortByPrice", "sortByDuration", "sortByRecommendation", "flights", "sort", "a", "b", "priceA", "getMinPriceAmount", "priceB", "durationA", "duration", "Number", "MAX_VALUE", "durationB", "scoreA", "calculateRecommendationScore", "scoreB", "price", "availability", "seatInfo", "availableSeatCount", "priceScore", "durationScore", "stopScore", "availabilityScore", "Math", "min", "weights", "amount", "calculateFilterPrices", "resetFilterPrices", "Set", "add", "departureHours", "arrivalHours", "cleanupFilterPrices", "keys", "toggleSection", "section", "toggleStopFilter", "toggleDepartureTimeFilter", "toggleArrivalTimeFilter", "toggleAirlineFilter", "clearAllFilters", "formatPrice", "toFixed", "getAirlineKeys", "showAllDetails", "modalDiv", "createElement", "style", "position", "top", "left", "width", "height", "backgroundColor", "zIndex", "display", "justifyContent", "alignItems", "modalContent", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "color", "fontFamily", "closeButton", "innerHTML", "right", "border", "background", "fontSize", "cursor", "transition", "on<PERSON><PERSON>ver", "onmouseout", "onclick", "body", "<PERSON><PERSON><PERSON><PERSON>", "header", "marginBottom", "paddingBottom", "borderBottom", "logo", "title", "textContent", "margin", "fontWeight", "append<PERSON><PERSON><PERSON>", "detailsContainer", "flexDirection", "gap", "generalInfo", "createSection", "airlineInfo", "thumbnailFull", "airlineLogo", "src", "alt", "marginRight", "airlineIcon", "airlineName", "internationalCode", "flightNumberRow", "createInfoRow", "flightNo", "flightDateRow", "flightDate", "toLocaleDateString", "durationRow", "formatDuration", "classRow", "code", "stopsRow", "routeSection", "routeVisual", "textAlign", "flex", "toLocaleTimeString", "hour", "minute", "departureAirport", "marginTop", "airport", "departureCity", "city", "connectionLine", "line", "plane", "marginLeft", "arrivalAirport", "arrivalCity", "segmentsTitle", "segmentsList", "index", "segmentItem", "segmentHeader", "segmentTitle", "segmentDuration", "segmentRoute", "segmentDeparture", "depTime", "depAirport", "arrow", "segmentArrival", "arrTime", "arrAirport", "layover", "currentArrival", "getTime", "nextDeparture", "layoverTime", "floor", "offersSection", "offersList", "offerItem", "offerHeader", "offerTitle", "offerPrice", "offerDetails", "gridTemplateColumns", "availabilityValue", "expiresOn", "expires", "toLocaleString", "brandedFare", "reservableInfo", "reservable", "baggageInformations", "baggageTitle", "baggageContainer", "checkedBaggage", "baggageType", "cabinBaggage", "handBaggage", "baggage", "baggageItem", "baggageIcon", "baggageInfo", "baggageDetails", "detailsText", "weight", "piece", "flightItem", "services", "servicesSection", "servicesList", "listStyle", "service", "serviceItem", "iconClass", "section<PERSON><PERSON><PERSON>", "className", "sectionTitle", "row", "labelElement", "valueElement", "selectThisFlight", "offerId", "searchId", "getPassengerCounts", "passengerInfo", "JSON", "stringify", "navigate", "queryParams", "passengers", "error", "departureLocationType", "arrivalLocationType", "getLocationsByType", "subscribe", "locations", "valueChanges", "pipe", "location", "toLowerCase", "displayLocation", "displayText", "Airport", "setSearchType", "setValue", "clearValidators", "getSegmentsArray", "clear", "setValidators", "addSegment", "updateValueAndValidity", "createSegment", "standardDepartureLocation", "standardArrivalLocation", "standardDepartureDate", "newSegment", "push", "removeSegment", "removeAt", "onSearch", "invalid", "markFormGroupTouched", "formValue", "request", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "ArrivalLocations", "Passengers", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "ReturnDate", "diffTime", "abs", "diffDays", "ceil", "Night", "flightSegments", "checkIns", "FlightSegments", "CheckIns", "searchPrice", "next", "response", "success", "responseType", "flightsByClass", "toString", "classType", "segIndex", "groupEnd", "flightsWithOffers", "f", "availabilityValues", "flatMap", "o", "availabilityCounts", "acc", "val", "reservableFlights", "some", "requestId", "messages", "message", "formGroup", "controls", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "minutes", "mins", "formatDate", "dateString", "weekday", "day", "month", "formatDateWithRequestComparison", "requestedDate", "formatted", "requested", "dateOnly", "getFullYear", "getMonth", "getDate", "requestedOnly", "getFlightDates", "result", "outbound", "inbound", "outboundItems", "segmentNumber", "route", "inboundItems", "getMinPrice", "min<PERSON>ffer", "formattedAmount", "isFlightAvailable", "checkFlightDatesMatch", "requestedDepartureDate", "requestedReturnDate", "match", "flightDates", "requestedDepDate", "actualDepDate", "requestedDepDay", "actualDepDay", "requestedRetDate", "actualRetDate", "requestedRetDay", "actualRetDay", "getFlightDateInfoMessage", "isRoundTrip", "isRoundTripFlight", "date<PERSON><PERSON><PERSON>", "showAllDepartureLocations", "locationType", "input", "getElementById", "focus", "dispatchEvent", "Event", "showAllArrivalLocations", "showDepartureLocationsForSegment", "showArrivalLocationsForSegment", "swapLocations", "patchValue", "swapSegmentLocations", "at", "formatExpirationDate", "getBaggageTypeName", "filterBaggageByType", "Array", "isArray", "getPassengerTypeName", "passengerType", "calculateLayoverTime", "currentSegment", "nextSegment", "diffMs", "diffMins", "calculateStayDuration", "outboundArrivalDate", "inboundDepartureDate", "firstSegment", "lastSegment", "firstDeparture", "firstArrival", "lastDeparture", "lastArrival", "getOutboundSegments", "<PERSON><PERSON><PERSON><PERSON>", "slice", "getInboundSegments", "isMultiCityFlight", "__decorate", "selector", "templateUrl", "styleUrls", "encapsulation", "None"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation, HostListener } from '@angular/core';\nimport { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { ProductService } from '../../../services/product.service';\nimport { SharedDataService } from '../../../services/shared-data.service';\nimport { LocationOption } from '../../../models/one-way-request.model';\nimport { CommonFlight, CommonResponse } from '../../../models/common-flight.model';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: [\n    './search-price.component.css',\n    './search-card.css',\n    './flight-type-tabs.css',\n    './round-trip-styles.css',\n    './multi-city-styles.css'\n  ],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchPriceComponent implements OnInit {\n  searchForm: FormGroup;\n  departureLocations: LocationOption[] = [];\n  arrivalLocations: LocationOption[] = [];\n  isLoading = false;\n  searchResults: CommonFlight[] = [];\n  filteredResults: CommonFlight[] = [];\n  hasSearched = false;\n  errorMessage = '';\n  lastSearchId = '';\n\n  // Types de recherche de vol\n  readonly SEARCH_TYPE_ONE_WAY = 1;\n  readonly SEARCH_TYPE_ROUND_TRIP = 2;\n  readonly SEARCH_TYPE_MULTI_CITY = 3;\n\n  // Type de recherche actuel (par défaut: aller simple)\n  currentSearchType: number = this.SEARCH_TYPE_ONE_WAY;\n\n  // Passenger selector properties\n  showPassengerDropdown = false;\n  passengerCounts: { [key: number]: number } = {\n    [PassengerType.Adult]: 1,\n    [PassengerType.Child]: 0,\n    [PassengerType.Infant]: 0\n  };\n\n  // Filter options\n  currentFilter: string = 'recommended';\n  filterOptions = [\n    { value: 'recommended', label: 'Recommended', icon: 'fa-star' },\n    { value: 'cheapest', label: 'Cheapest', icon: 'fa-dollar-sign' },\n    { value: 'shortest', label: 'Shortest', icon: 'fa-clock' }\n  ];\n\n  // Sidebar filter options\n  sidebarFilters = {\n    stops: {\n      direct: false,\n      oneStop: false,\n      multiStop: false\n    },\n    departureTime: {\n      earlyMorning: false, // 00:00 - 08:00\n      morning: false,      // 08:00 - 12:00\n      afternoon: false,    // 12:00 - 18:00\n      evening: false       // 18:00 - 00:00\n    },\n    arrivalTime: {\n      earlyMorning: false, // 00:00 - 08:00\n      morning: false,      // 08:00 - 12:00\n      afternoon: false,    // 12:00 - 18:00\n      evening: false       // 18:00 - 00:00\n    },\n    airlines: {} as { [key: string]: boolean }  // Will be populated dynamically based on search results\n  };\n\n  // Expanded sections in sidebar\n  expandedSections: { [key: string]: boolean } = {\n    stops: true,\n    departureTime: true,\n    arrivalTime: true,\n    airlines: true\n  };\n\n  // Price ranges for each filter option (will be calculated from results)\n  filterPrices = {\n    stops: {\n      direct: 0,\n      oneStop: 0,\n      multiStop: 0\n    },\n    departureTime: {\n      earlyMorning: 0,\n      morning: 0,\n      afternoon: 0,\n      evening: 0\n    },\n    arrivalTime: {\n      earlyMorning: 0,\n      morning: 0,\n      afternoon: 0,\n      evening: 0\n    },\n    airlines: {} as { [key: string]: number }\n  };\n\n  // Passenger type options\n  passengerTypes = [\n    { value: PassengerType.Adult, label: 'Adult' },\n    { value: PassengerType.Child, label: 'Child' },\n    { value: PassengerType.Infant, label: 'Infant' }\n  ];\n\n  // Flight class options\n  flightClasses = [\n    { value: FlightClassType.PROMO, label: 'Promo' },\n    { value: FlightClassType.ECONOMY, label: 'Economy' },\n    { value: FlightClassType.BUSINESS, label: 'Business' }\n  ];\n\n  // Date minimale (aujourd'hui)\n  minDate: string;\n\n  constructor(\n    private fb: FormBuilder,\n    private productService: ProductService,\n    private router: Router,\n    private sharedDataService: SharedDataService\n  ) {\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required], // Flight par défaut\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      arrivalLocation: ['', Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      returnDate: [''],  // Date de retour pour les vols aller-retour\n\n      // Segments pour multi-city (sera initialisé dynamiquement)\n      segments: this.fb.array([]),\n\n      // Options de vol\n      flightClass: [1, Validators.required], // ECONOMY par défaut\n      nonStop: [false],\n\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n\n    // Initialiser les compteurs de passagers dans le service partagé\n    this.sharedDataService.setPassengerCounts(this.passengerCounts);\n  }\n\n  // Close dropdown when clicking outside\n  @HostListener('document:click', ['$event'])\n  onDocumentClick(event: MouseEvent): void {\n    // Check if click is outside the passenger dropdown\n    const clickedElement = event.target as HTMLElement;\n    const passengerSelector = document.querySelector('.passengers-selector');\n\n    if (passengerSelector && !passengerSelector.contains(clickedElement)) {\n      this.showPassengerDropdown = false;\n    }\n  }\n\n  // Toggle passenger dropdown\n  togglePassengerDropdown(event: Event): void {\n    event.stopPropagation();\n    this.showPassengerDropdown = !this.showPassengerDropdown;\n  }\n\n  // Close passenger dropdown\n  closePassengerDropdown(): void {\n    this.showPassengerDropdown = false;\n  }\n\n  // Get passenger count for a specific type\n  getPassengerCount(type: number): number {\n    return this.passengerCounts[type] || 0;\n  }\n\n  // Increase passenger count\n  increasePassengerCount(type: number): void {\n    if (this.getTotalPassengers() < 9) {\n      this.passengerCounts[type] = (this.passengerCounts[type] || 0) + 1;\n      // Mettre à jour le service partagé\n      this.sharedDataService.setPassengerCounts(this.passengerCounts);\n    }\n  }\n\n  // Decrease passenger count\n  decreasePassengerCount(type: number): void {\n    // Pour les adultes, ne pas permettre de descendre en dessous de 1\n    if (type === PassengerType.Adult) {\n      if (this.passengerCounts[type] > 1) {\n        this.passengerCounts[type] -= 1;\n      }\n    } else if (this.passengerCounts[type] > 0) {\n      this.passengerCounts[type] -= 1;\n    }\n    // Mettre à jour le service partagé\n    this.sharedDataService.setPassengerCounts(this.passengerCounts);\n  }\n\n  // Get total number of passengers\n  getTotalPassengers(): number {\n    return Object.values(this.passengerCounts).reduce((sum, count) => sum + count, 0);\n  }\n\n  // Get passengers array for API request\n  getPassengersArray(): any[] {\n    // Utiliser le service partagé pour obtenir les passagers\n    return this.sharedDataService.getPassengersArray();\n  }\n\n  ngOnInit(): void {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n\n  // Méthode pour appliquer le filtre sélectionné\n  applyFilter(filterValue: string): void {\n    this.currentFilter = filterValue;\n    this.applyAllFilters();\n  }\n\n  // Méthode pour vérifier si un vol correspond à la classe sélectionnée\n  private isFlightMatchingClass(flight: any, selectedClass: FlightClassType): boolean {\n    // Vérifier si le vol a des items\n    if (!flight.items || flight.items.length === 0) {\n      console.log(`Flight ${flight.id} has no items`);\n      return false;\n    }\n\n    console.log(`Checking flight ${flight.id} for class ${selectedClass}`);\n\n    // Vérifier la classe de vol dans les items\n    for (const item of flight.items) {\n      if (item.flightClass) {\n        console.log(`Item class: ${item.flightClass.name} (type: ${item.flightClass.type})`);\n        if (item.flightClass.type === selectedClass) {\n          console.log(`Match found in item flightClass`);\n          return true;\n        }\n      }\n\n      // Vérifier également dans les segments si disponibles\n      if (item.segments) {\n        console.log(`Checking ${item.segments.length} segments`);\n        for (const segment of item.segments) {\n          if (segment.flightClass) {\n            console.log(`Segment class: ${segment.flightClass.name} (type: ${segment.flightClass.type})`);\n            if (segment.flightClass.type === selectedClass) {\n              console.log(`Match found in segment flightClass`);\n              return true;\n            }\n          }\n        }\n      }\n    }\n\n    // Vérifier également dans les offres si disponibles\n    if (flight.offers && flight.offers.length > 0) {\n      console.log(`Checking ${flight.offers.length} offers`);\n      for (const offer of flight.offers) {\n        if (offer.flightClassInformations && offer.flightClassInformations.length > 0) {\n          console.log(`Offer has ${offer.flightClassInformations.length} class infos`);\n          for (const classInfo of offer.flightClassInformations) {\n            console.log(`Offer class info: ${classInfo.name} (type: ${classInfo.type})`);\n            if (classInfo.type === selectedClass) {\n              console.log(`Match found in offer flightClassInformations`);\n              return true;\n            }\n          }\n        } else {\n          console.log(`Offer has no flightClassInformations`);\n        }\n      }\n    }\n\n    console.log(`No match found for flight ${flight.id}`);\n    return false;\n  }\n\n  // Méthode pour appliquer tous les filtres (top et sidebar)\n  applyAllFilters(): void {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      this.filteredResults = [];\n      return;\n    }\n\n    // Étape 1: Appliquer les filtres de la sidebar\n    let results = [...this.searchResults];\n\n    // Récupérer la classe de vol sélectionnée\n    const selectedClass = this.searchForm.get('flightClass')?.value;\n\n    // Filtrer par classe de vol\n    if (selectedClass !== undefined) {\n      console.log('Filtering by flight class:', selectedClass);\n\n      // Afficher le nombre total de vols avant filtrage\n      console.log('Total flights before class filtering:', results.length);\n\n      // Vérifier combien de vols correspondent à chaque classe\n      const classCounts = {\n        [FlightClassType.PROMO]: 0,\n        [FlightClassType.ECONOMY]: 0,\n        [FlightClassType.BUSINESS]: 0\n      };\n\n      results.forEach(flight => {\n        if (this.isFlightMatchingClass(flight, FlightClassType.PROMO)) classCounts[FlightClassType.PROMO]++;\n        if (this.isFlightMatchingClass(flight, FlightClassType.ECONOMY)) classCounts[FlightClassType.ECONOMY]++;\n        if (this.isFlightMatchingClass(flight, FlightClassType.BUSINESS)) classCounts[FlightClassType.BUSINESS]++;\n      });\n\n      console.log('Flights by class (before filtering):', classCounts);\n\n      // Option pour désactiver temporairement le filtrage par classe (pour débogage)\n      // Mettre à true pour voir tous les vols disponibles sans filtrage par classe\n      const disableClassFiltering = true;\n\n      if (!disableClassFiltering) {\n        results = results.filter(flight => this.isFlightMatchingClass(flight, selectedClass));\n        console.log('Flights after class filtering:', results.length);\n      } else {\n        console.log('Class filtering disabled for debugging');\n      }\n    }\n\n    // Filtrer par nombre d'escales\n    if (this.sidebarFilters.stops.direct || this.sidebarFilters.stops.oneStop || this.sidebarFilters.stops.multiStop) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0) return false;\n\n        const stopCount = flight.items[0].stopCount || 0;\n\n        return (this.sidebarFilters.stops.direct && stopCount === 0) ||\n               (this.sidebarFilters.stops.oneStop && stopCount === 1) ||\n               (this.sidebarFilters.stops.multiStop && stopCount >= 2);\n      });\n    }\n\n    // Filtrer par horaire de départ\n    if (this.sidebarFilters.departureTime.earlyMorning ||\n        this.sidebarFilters.departureTime.morning ||\n        this.sidebarFilters.departureTime.afternoon ||\n        this.sidebarFilters.departureTime.evening) {\n\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].departure || !flight.items[0].departure.date) {\n          return false;\n        }\n\n        const departureDate = new Date(flight.items[0].departure.date);\n        const hours = departureDate.getHours();\n\n        return (this.sidebarFilters.departureTime.earlyMorning && hours >= 0 && hours < 8) ||\n               (this.sidebarFilters.departureTime.morning && hours >= 8 && hours < 12) ||\n               (this.sidebarFilters.departureTime.afternoon && hours >= 12 && hours < 18) ||\n               (this.sidebarFilters.departureTime.evening && hours >= 18 && hours < 24);\n      });\n    }\n\n    // Filtrer par horaire d'arrivée\n    if (this.sidebarFilters.arrivalTime.earlyMorning ||\n        this.sidebarFilters.arrivalTime.morning ||\n        this.sidebarFilters.arrivalTime.afternoon ||\n        this.sidebarFilters.arrivalTime.evening) {\n\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].arrival || !flight.items[0].arrival.date) {\n          return false;\n        }\n\n        const arrivalDate = new Date(flight.items[0].arrival.date);\n        const hours = arrivalDate.getHours();\n\n        return (this.sidebarFilters.arrivalTime.earlyMorning && hours >= 0 && hours < 8) ||\n               (this.sidebarFilters.arrivalTime.morning && hours >= 8 && hours < 12) ||\n               (this.sidebarFilters.arrivalTime.afternoon && hours >= 12 && hours < 18) ||\n               (this.sidebarFilters.arrivalTime.evening && hours >= 18 && hours < 24);\n      });\n    }\n\n    // Filtrer par compagnie aérienne\n    const selectedAirlines = Object.entries(this.sidebarFilters.airlines)\n      .filter(([_, selected]) => selected)\n      .map(([airline, _]) => airline);\n\n    if (selectedAirlines.length > 0) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].airline || !flight.items[0].airline.name) {\n          return false;\n        }\n\n        return selectedAirlines.includes(flight.items[0].airline.name);\n      });\n    }\n\n    // Étape 2: Appliquer le tri selon le filtre sélectionné en haut\n    switch (this.currentFilter) {\n      case 'cheapest':\n        this.filteredResults = this.sortByPrice(results);\n        break;\n      case 'shortest':\n        this.filteredResults = this.sortByDuration(results);\n        break;\n      case 'recommended':\n      default:\n        this.filteredResults = this.sortByRecommendation(results);\n        break;\n    }\n  }\n\n  // Trier les vols par prix (du moins cher au plus cher)\n  private sortByPrice(flights: any[]): any[] {\n    return flights.sort((a, b) => {\n      const priceA = this.getMinPriceAmount(a);\n      const priceB = this.getMinPriceAmount(b);\n      return priceA - priceB;\n    });\n  }\n\n  // Trier les vols par durée (du plus court au plus long)\n  private sortByDuration(flights: any[]): any[] {\n    return flights.sort((a, b) => {\n      const durationA = a.items && a.items[0] ? a.items[0].duration : Number.MAX_VALUE;\n      const durationB = b.items && b.items[0] ? b.items[0].duration : Number.MAX_VALUE;\n      return durationA - durationB;\n    });\n  }\n\n  // Trier les vols par recommandation (combinaison de prix, durée et autres facteurs)\n  private sortByRecommendation(flights: any[]): any[] {\n    return flights.sort((a, b) => {\n      // Calculer un score pour chaque vol basé sur plusieurs facteurs\n      const scoreA = this.calculateRecommendationScore(a);\n      const scoreB = this.calculateRecommendationScore(b);\n      return scoreB - scoreA; // Ordre décroissant (score plus élevé = meilleur)\n    });\n  }\n\n  // Calculer un score de recommandation pour un vol\n  private calculateRecommendationScore(flight: any): number {\n    if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n      return 0;\n    }\n\n    const item = flight.items[0];\n    const offer = flight.offers[0];\n\n    // Facteurs à considérer pour le score\n    const price = this.getMinPriceAmount(flight);\n    const duration = item.duration;\n    const stopCount = item.stopCount || 0;\n    const availability = offer.availability !== undefined ? offer.availability :\n                        (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n\n    // Normaliser les valeurs (plus le score est élevé, meilleur est le vol)\n    const priceScore = 1000 / (price + 100); // Inverse du prix (moins cher = meilleur score)\n    const durationScore = 1000 / (duration + 100); // Inverse de la durée (plus court = meilleur score)\n    const stopScore = 1 / (stopCount + 1); // Moins d'escales = meilleur score\n    const availabilityScore = Math.min(availability, 10) / 10; // Plus de disponibilité = meilleur score\n\n    // Pondération des facteurs (ajustable selon les préférences)\n    const weights = {\n      price: 0.4,      // 40% importance pour le prix\n      duration: 0.3,   // 30% importance pour la durée\n      stops: 0.2,      // 20% importance pour les escales\n      availability: 0.1 // 10% importance pour la disponibilité\n    };\n\n    // Calculer le score final pondéré\n    return (\n      priceScore * weights.price +\n      durationScore * weights.duration +\n      stopScore * weights.stops +\n      availabilityScore * weights.availability\n    );\n  }\n\n  // Obtenir le montant du prix minimum pour un vol\n  private getMinPriceAmount(flight: any): number {\n    if (!flight.offers || flight.offers.length === 0) {\n      return Number.MAX_VALUE;\n    }\n\n    return flight.offers.reduce((min: number, offer: any) =>\n      offer.price && offer.price.amount < min ? offer.price.amount : min,\n      flight.offers[0].price ? flight.offers[0].price.amount : Number.MAX_VALUE\n    );\n  }\n\n  // Calculer les prix minimums pour chaque option de filtre\n  calculateFilterPrices(): void {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      return;\n    }\n\n    // Réinitialiser les prix\n    this.resetFilterPrices();\n\n    // Collecter toutes les compagnies aériennes\n    const airlines = new Set<string>();\n\n    // Parcourir tous les vols pour calculer les prix minimums\n    this.searchResults.forEach(flight => {\n      if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n        return;\n      }\n\n      const item = flight.items[0];\n      const price = this.getMinPriceAmount(flight);\n\n      // Ajouter la compagnie aérienne à la liste\n      if (item.airline && item.airline.name) {\n        airlines.add(item.airline.name);\n\n        // Initialiser le prix pour cette compagnie si nécessaire\n        if (!(item.airline.name in this.filterPrices.airlines)) {\n          this.filterPrices.airlines[item.airline.name] = Number.MAX_VALUE;\n        }\n\n        // Mettre à jour le prix minimum pour cette compagnie\n        this.filterPrices.airlines[item.airline.name] = Math.min(\n          this.filterPrices.airlines[item.airline.name],\n          price\n        );\n      }\n\n      // Mettre à jour les prix par nombre d'escales\n      const stopCount = item.stopCount || 0;\n      if (stopCount === 0) {\n        this.filterPrices.stops.direct = Math.min(this.filterPrices.stops.direct, price);\n      } else if (stopCount === 1) {\n        this.filterPrices.stops.oneStop = Math.min(this.filterPrices.stops.oneStop, price);\n      } else {\n        this.filterPrices.stops.multiStop = Math.min(this.filterPrices.stops.multiStop, price);\n      }\n\n      // Mettre à jour les prix par horaire de départ\n      if (item.departure && item.departure.date) {\n        const departureDate = new Date(item.departure.date);\n        const departureHours = departureDate.getHours();\n\n        if (departureHours >= 0 && departureHours < 8) {\n          this.filterPrices.departureTime.earlyMorning = Math.min(this.filterPrices.departureTime.earlyMorning, price);\n        } else if (departureHours >= 8 && departureHours < 12) {\n          this.filterPrices.departureTime.morning = Math.min(this.filterPrices.departureTime.morning, price);\n        } else if (departureHours >= 12 && departureHours < 18) {\n          this.filterPrices.departureTime.afternoon = Math.min(this.filterPrices.departureTime.afternoon, price);\n        } else {\n          this.filterPrices.departureTime.evening = Math.min(this.filterPrices.departureTime.evening, price);\n        }\n      }\n\n      // Mettre à jour les prix par horaire d'arrivée\n      if (item.arrival && item.arrival.date) {\n        const arrivalDate = new Date(item.arrival.date);\n        const arrivalHours = arrivalDate.getHours();\n\n        if (arrivalHours >= 0 && arrivalHours < 8) {\n          this.filterPrices.arrivalTime.earlyMorning = Math.min(this.filterPrices.arrivalTime.earlyMorning, price);\n        } else if (arrivalHours >= 8 && arrivalHours < 12) {\n          this.filterPrices.arrivalTime.morning = Math.min(this.filterPrices.arrivalTime.morning, price);\n        } else if (arrivalHours >= 12 && arrivalHours < 18) {\n          this.filterPrices.arrivalTime.afternoon = Math.min(this.filterPrices.arrivalTime.afternoon, price);\n        } else {\n          this.filterPrices.arrivalTime.evening = Math.min(this.filterPrices.arrivalTime.evening, price);\n        }\n      }\n    });\n\n    // Initialiser les filtres de compagnies aériennes\n    airlines.forEach(airline => {\n      if (!(airline in this.sidebarFilters.airlines)) {\n        this.sidebarFilters.airlines[airline] = false;\n      }\n    });\n\n    // Remplacer les valeurs MAX_VALUE par 0 pour les options sans vols\n    this.cleanupFilterPrices();\n  }\n\n  // Réinitialiser les prix des filtres\n  private resetFilterPrices(): void {\n    this.filterPrices = {\n      stops: {\n        direct: Number.MAX_VALUE,\n        oneStop: Number.MAX_VALUE,\n        multiStop: Number.MAX_VALUE\n      },\n      departureTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      arrivalTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      airlines: {}\n    };\n  }\n\n  // Nettoyer les prix des filtres (remplacer MAX_VALUE par 0)\n  private cleanupFilterPrices(): void {\n    // Escales\n    if (this.filterPrices.stops.direct === Number.MAX_VALUE) this.filterPrices.stops.direct = 0;\n    if (this.filterPrices.stops.oneStop === Number.MAX_VALUE) this.filterPrices.stops.oneStop = 0;\n    if (this.filterPrices.stops.multiStop === Number.MAX_VALUE) this.filterPrices.stops.multiStop = 0;\n\n    // Horaires de départ\n    if (this.filterPrices.departureTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.departureTime.earlyMorning = 0;\n    if (this.filterPrices.departureTime.morning === Number.MAX_VALUE) this.filterPrices.departureTime.morning = 0;\n    if (this.filterPrices.departureTime.afternoon === Number.MAX_VALUE) this.filterPrices.departureTime.afternoon = 0;\n    if (this.filterPrices.departureTime.evening === Number.MAX_VALUE) this.filterPrices.departureTime.evening = 0;\n\n    // Horaires d'arrivée\n    if (this.filterPrices.arrivalTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.arrivalTime.earlyMorning = 0;\n    if (this.filterPrices.arrivalTime.morning === Number.MAX_VALUE) this.filterPrices.arrivalTime.morning = 0;\n    if (this.filterPrices.arrivalTime.afternoon === Number.MAX_VALUE) this.filterPrices.arrivalTime.afternoon = 0;\n    if (this.filterPrices.arrivalTime.evening === Number.MAX_VALUE) this.filterPrices.arrivalTime.evening = 0;\n\n    // Compagnies aériennes\n    Object.keys(this.filterPrices.airlines).forEach(airline => {\n      if (this.filterPrices.airlines[airline] === Number.MAX_VALUE) {\n        this.filterPrices.airlines[airline] = 0;\n      }\n    });\n  }\n\n  // Basculer l'état d'expansion d'une section\n  toggleSection(section: string): void {\n    this.expandedSections[section] = !this.expandedSections[section];\n  }\n\n  // Basculer un filtre d'escale\n  toggleStopFilter(filter: 'direct' | 'oneStop' | 'multiStop'): void {\n    this.sidebarFilters.stops[filter] = !this.sidebarFilters.stops[filter];\n    this.applyAllFilters();\n  }\n\n  // Basculer un filtre d'horaire de départ\n  toggleDepartureTimeFilter(filter: 'earlyMorning' | 'morning' | 'afternoon' | 'evening'): void {\n    this.sidebarFilters.departureTime[filter] = !this.sidebarFilters.departureTime[filter];\n    this.applyAllFilters();\n  }\n\n  // Basculer un filtre d'horaire d'arrivée\n  toggleArrivalTimeFilter(filter: 'earlyMorning' | 'morning' | 'afternoon' | 'evening'): void {\n    this.sidebarFilters.arrivalTime[filter] = !this.sidebarFilters.arrivalTime[filter];\n    this.applyAllFilters();\n  }\n\n  // Basculer un filtre de compagnie aérienne\n  toggleAirlineFilter(airline: string): void {\n    this.sidebarFilters.airlines[airline] = !this.sidebarFilters.airlines[airline];\n    this.applyAllFilters();\n  }\n\n  // Effacer tous les filtres\n  clearAllFilters(): void {\n    // Réinitialiser les filtres d'escales\n    this.sidebarFilters.stops.direct = false;\n    this.sidebarFilters.stops.oneStop = false;\n    this.sidebarFilters.stops.multiStop = false;\n\n    // Réinitialiser les filtres d'horaires de départ\n    this.sidebarFilters.departureTime.earlyMorning = false;\n    this.sidebarFilters.departureTime.morning = false;\n    this.sidebarFilters.departureTime.afternoon = false;\n    this.sidebarFilters.departureTime.evening = false;\n\n    // Réinitialiser les filtres d'horaires d'arrivée\n    this.sidebarFilters.arrivalTime.earlyMorning = false;\n    this.sidebarFilters.arrivalTime.morning = false;\n    this.sidebarFilters.arrivalTime.afternoon = false;\n    this.sidebarFilters.arrivalTime.evening = false;\n\n    // Réinitialiser les filtres de compagnies aériennes\n    Object.keys(this.sidebarFilters.airlines).forEach(airline => {\n      this.sidebarFilters.airlines[airline] = false;\n    });\n\n    // Appliquer les filtres (qui seront tous désactivés)\n    this.applyAllFilters();\n  }\n\n  // Formater le prix pour l'affichage\n  formatPrice(price: number): string {\n    if (price === 0) return '-';\n    return price.toFixed(0) + ' €';\n  }\n\n  // Obtenir les clés des compagnies aériennes\n  getAirlineKeys(): string[] {\n    return Object.keys(this.sidebarFilters.airlines);\n  }\n\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight: CommonFlight): void {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n\n    header.appendChild(logo);\n    header.appendChild(title);\n\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n\n        generalInfo.appendChild(airlineInfo);\n      }\n\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n\n      routeSection.appendChild(routeVisual);\n\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n\n          segmentsList.appendChild(segmentItem);\n\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n\n            segmentsList.appendChild(layover);\n          }\n        });\n\n        routeSection.appendChild(segmentsList);\n      }\n    }\n\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability :\n                                 (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n\n          const baggageContainer = document.createElement('div');\n          baggageContainer.style.display = 'flex';\n          baggageContainer.style.flexDirection = 'column';\n          baggageContainer.style.gap = '10px';\n          baggageContainer.style.marginBottom = '15px';\n\n          // Filtrer et regrouper les bagages par type\n          const checkedBaggage = offer.baggageInformations.filter(b => b.baggageType === 2);\n          const cabinBaggage = offer.baggageInformations.filter(b => b.baggageType === 1);\n          const handBaggage = offer.baggageInformations.filter(b => b.baggageType === 3);\n\n          // Bagages en soute\n          if (checkedBaggage.length > 0) {\n            checkedBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#e7f5ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #c5e1f9';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-suitcase\" style=\"color: #4a6fa5; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Checked Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight && baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n\n          // Bagages cabine\n          if (cabinBaggage.length > 0) {\n            cabinBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#f3f0ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #e5dbff';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Cabin Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight && baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          } else {\n            // Ajouter un bagage cabine par défaut si aucun n'est spécifié\n            const baggageItem = document.createElement('div');\n            baggageItem.style.display = 'flex';\n            baggageItem.style.alignItems = 'center';\n            baggageItem.style.padding = '10px 15px';\n            baggageItem.style.backgroundColor = '#f3f0ff';\n            baggageItem.style.borderRadius = '6px';\n            baggageItem.style.border = '1px solid #e5dbff';\n\n            const baggageIcon = document.createElement('div');\n            baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n\n            const baggageInfo = document.createElement('div');\n            baggageInfo.style.display = 'flex';\n            baggageInfo.style.flexDirection = 'column';\n\n            const baggageType = document.createElement('div');\n            baggageType.textContent = 'Cabin Baggage';\n            baggageType.style.fontWeight = '600';\n            baggageType.style.fontSize = '14px';\n\n            const baggageDetails = document.createElement('div');\n            baggageDetails.style.fontSize = '12px';\n            baggageDetails.style.color = '#666';\n            baggageDetails.textContent = 'Included';\n\n            baggageInfo.appendChild(baggageType);\n            baggageInfo.appendChild(baggageDetails);\n\n            baggageItem.appendChild(baggageIcon);\n            baggageItem.appendChild(baggageInfo);\n            baggageContainer.appendChild(baggageItem);\n          }\n\n          // Bagages à main\n          if (handBaggage.length > 0) {\n            handBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#fff4e6';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #ffe8cc';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-shopping-bag\" style=\"color: #e8590c; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Hand Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight && baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n\n          offerItem.appendChild(baggageContainer);\n        }\n\n        offerItem.appendChild(offerDetails);\n\n        offersList.appendChild(offerItem);\n      });\n\n      offersSection.appendChild(offersList);\n    }\n\n    // 4. Section des services\n    // Note: services might be in different locations depending on the flight type\n    const flightItem = flight.items && flight.items[0];\n    const services = flightItem && (\n      // Try to get services from different possible locations\n      (flightItem as any).services ||\n      (flightItem.segments && flightItem.segments[0] && flightItem.segments[0].services)\n    );\n\n    if (services && services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n\n      services.forEach((service: any) => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n\n  // Méthode utilitaire pour créer une section\n  private createSection(title: string, iconClass: string): HTMLElement {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n\n    return section;\n  }\n\n  // Méthode utilitaire pour créer une ligne d'information\n  private createInfoRow(label: string, value: string): HTMLElement {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n\n    return row;\n  }\n\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight: CommonFlight): void {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId && flight.id) {\n        searchId = flight.id;\n      } else if (!searchId) {\n        // Utiliser un ID par défaut si aucun ID n'est disponible\n        searchId = 'default-search-id';\n      }\n\n      // Obtenir les informations de passagers\n      const passengerCounts = this.sharedDataService.getPassengerCounts();\n\n      // Sérialiser les informations de passagers pour les passer dans l'URL\n      const passengerInfo = JSON.stringify(passengerCounts);\n\n      console.log('Navigating to get-offer with searchId:', searchId, 'offerId:', offerId, 'and passengers:', passengerInfo);\n\n      // Rediriger vers la page get-offer avec les informations de passagers\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId,\n          passengers: passengerInfo\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n\n  preloadLocations(): void {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n\n  setupAutocomplete(): void {\n    // Charger les locations par type par défaut\n    const departureLocationType = 2; // Type 2 (City) par défaut\n    const arrivalLocationType = 5;   // Type 5 (Airport) par défaut\n\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(departureLocationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(departureLocationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.departureLocations = locations;\n      });\n\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(arrivalLocationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(arrivalLocationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n  }\n\n  displayLocation(location: LocationOption | null): string {\n    if (!location) return '';\n\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n\n  // Méthode pour changer le type de recherche (aller simple, aller-retour, multi-city)\n  setSearchType(type: number): void {\n    this.currentSearchType = type;\n\n    // Mettre à jour le type de service dans le formulaire\n    if (type === this.SEARCH_TYPE_ONE_WAY) {\n      this.searchForm.get('serviceTypes')?.setValue(['1']);\n      // Rendre le champ de date de retour optionnel\n      this.searchForm.get('returnDate')?.clearValidators();\n\n      // Réinitialiser les segments pour multi-city\n      this.getSegmentsArray().clear();\n    } else if (type === this.SEARCH_TYPE_ROUND_TRIP) {\n      this.searchForm.get('serviceTypes')?.setValue(['2']);\n      // Rendre le champ de date de retour obligatoire\n      this.searchForm.get('returnDate')?.setValidators([Validators.required]);\n\n      // Réinitialiser les segments pour multi-city\n      this.getSegmentsArray().clear();\n    } else if (type === this.SEARCH_TYPE_MULTI_CITY) {\n      this.searchForm.get('serviceTypes')?.setValue(['3']);\n      // Rendre le champ de date de retour optionnel\n      this.searchForm.get('returnDate')?.clearValidators();\n\n      // Initialiser au moins un segment pour multi-city\n      if (this.getSegmentsArray().length === 0) {\n        this.addSegment();\n      }\n    }\n\n    // Mettre à jour les validateurs\n    this.searchForm.get('returnDate')?.updateValueAndValidity();\n  }\n\n  // Méthode pour obtenir le FormArray des segments\n  getSegmentsArray(): FormArray {\n    return this.searchForm.get('segments') as FormArray;\n  }\n\n  // Méthode pour créer un nouveau segment\n  createSegment(): FormGroup {\n    return this.fb.group({\n      departureLocation: ['', Validators.required],\n      arrivalLocation: ['', Validators.required],\n      departureDate: [this.minDate, Validators.required]\n    });\n  }\n\n  // Méthode pour ajouter un segment\n  addSegment(): void {\n    // Récupérer les valeurs des champs standards\n    const standardDepartureLocation = this.searchForm.get('departureLocation')?.value;\n    const standardArrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const standardDepartureDate = this.searchForm.get('departureDate')?.value;\n\n    // Créer un nouveau segment avec les valeurs des champs standards\n    const newSegment = this.fb.group({\n      departureLocation: [standardDepartureLocation, Validators.required],\n      arrivalLocation: [standardArrivalLocation, Validators.required],\n      departureDate: [standardDepartureDate, Validators.required]\n    });\n\n    // Ajouter le segment au FormArray\n    this.getSegmentsArray().push(newSegment);\n  }\n\n  // Méthode pour supprimer un segment\n  removeSegment(index: number): void {\n    // Ne pas supprimer le dernier segment\n    if (this.getSegmentsArray().length > 1) {\n      this.getSegmentsArray().removeAt(index);\n    }\n  }\n\n  onSearch(): void {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n\n    const formValue = this.searchForm.value;\n\n    // Vérifier la valeur de la classe de vol et le type de recherche\n    console.log('Form values:', formValue);\n    console.log('Selected flight class:', formValue.flightClass);\n    console.log('Current search type:', this.currentSearchType);\n\n    // Créer la requête en fonction du type de recherche (aller simple ou aller-retour)\n    let request: any;\n\n    if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n      // Requête pour vol aller simple\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['1'], // Important: \"1\" pour aller simple\n        CheckIn: formValue.departureDate,\n        DepartureLocations: [\n          {\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }\n        ],\n        ArrivalLocations: [\n          {\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }\n        ],\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n\n      console.log('Sending one-way search request with ServiceTypes:', request.ServiceTypes);\n    } else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      // Requête pour vol aller-retour\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['2'], // Important: \"2\" pour aller-retour\n        CheckIn: formValue.departureDate,\n        ReturnDate: formValue.returnDate,\n        DepartureLocations: [\n          {\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }\n        ],\n        ArrivalLocations: [\n          {\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }\n        ],\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n\n      console.log('Sending round-trip search request with ServiceTypes:', request.ServiceTypes);\n    }\n\n    // Créer la requête appropriée selon le type de recherche\n    if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n      // Requête pour vol aller simple\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['1'], // Important: \"1\" pour aller simple\n        CheckIn: formValue.departureDate,\n        DepartureLocations: [\n          {\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }\n        ],\n        ArrivalLocations: [\n          {\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }\n        ],\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n\n      console.log('Sending one-way search request with ServiceTypes:', request.ServiceTypes);\n    } else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      // Requête pour vol aller-retour\n      // Calculer le nombre de nuits entre la date de départ et de retour\n      const departureDate = new Date(formValue.departureDate);\n      const returnDate = new Date(formValue.returnDate);\n\n      // Calculer le nombre de nuits (différence en jours)\n      const diffTime = Math.abs(returnDate.getTime() - departureDate.getTime());\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n      console.log(`Round-trip search: ${diffDays} nights between departure and return`);\n\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['2'], // Important: \"2\" pour aller-retour\n        CheckIn: formValue.departureDate,\n        Night: diffDays, // Utiliser Night au lieu de ReturnDate pour les vols aller-retour\n        DepartureLocations: [\n          {\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }\n        ],\n        ArrivalLocations: [\n          {\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }\n        ],\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n\n      console.log('Sending round-trip search request with ServiceTypes:', request.ServiceTypes);\n    } else if (this.currentSearchType === this.SEARCH_TYPE_MULTI_CITY) {\n      // Requête pour vol multi-destinations\n      const segments = this.getSegmentsArray().value;\n\n      // Créer les segments de vol\n      const flightSegments = segments.map((segment: any) => ({\n        CheckIn: segment.departureDate,\n        DepartureLocations: [\n          {\n            id: segment.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }\n        ],\n        ArrivalLocations: [\n          {\n            id: segment.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }\n        ]\n      }));\n\n      // Créer le tableau des dates de départ (CheckIns)\n      const checkIns = segments.map((segment: any) => segment.departureDate);\n\n      // Si aucun segment n'est défini, utiliser les valeurs par défaut du formulaire principal\n      if (flightSegments.length === 0) {\n        flightSegments.push({\n          CheckIn: formValue.departureDate,\n          DepartureLocations: [\n            {\n              id: formValue.departureLocation?.id || '',\n              type: 2 // Type 2 (City) par défaut\n            }\n          ],\n          ArrivalLocations: [\n            {\n              id: formValue.arrivalLocation?.id || '',\n              type: 5 // Type 5 (Airport) par défaut\n            }\n          ]\n        });\n\n        checkIns.push(formValue.departureDate);\n      }\n\n      console.log('Multi-city segments:', flightSegments);\n      console.log('Multi-city CheckIns:', checkIns);\n\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['3'], // Important: \"3\" pour multi-destinations\n        FlightSegments: flightSegments,\n        // Ajouter les paramètres CheckIn et CheckIns requis par l'API\n        CheckIn: checkIns[0], // Première date de départ\n        CheckIns: checkIns,   // Toutes les dates de départ\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n\n      console.log('Sending multi-city search request with ServiceTypes:', request.ServiceTypes);\n    }\n\n    // Utiliser la méthode searchPrice pour envoyer la requête\n    this.productService.searchPrice(request)\n        .subscribe({\n          next: (response: CommonResponse) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n\n            // Analyser les classes de vol dans les résultats\n            console.group('Flight Class Analysis');\n            console.log('Selected flight class:', formValue.flightClass);\n            console.log('Total flights received:', response.body.flights.length);\n            let responseType = 'One Way';\n            if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n              responseType = 'Round Trip';\n            } else if (this.currentSearchType === this.SEARCH_TYPE_MULTI_CITY) {\n              responseType = 'Multi-City';\n            }\n            console.log('Response type:', responseType);\n\n            // Compter les vols par classe\n            const flightsByClass: Record<string, number> = {\n              [FlightClassType.PROMO.toString()]: 0,\n              [FlightClassType.ECONOMY.toString()]: 0,\n              [FlightClassType.BUSINESS.toString()]: 0,\n              'unknown': 0\n            };\n\n            response.body.flights.forEach((flight, index) => {\n              if (flight.items && flight.items.length > 0 && flight.items[0].flightClass) {\n                const classType = flight.items[0].flightClass.type.toString();\n                if (flightsByClass[classType] !== undefined) {\n                  flightsByClass[classType]++;\n                } else {\n                  flightsByClass['unknown']++;\n                }\n\n                // Afficher les détails de classe pour chaque vol\n                console.log(`Flight ${flight.id} class:`,\n                  flight.items[0].flightClass ?\n                  `${flight.items[0].flightClass.name} (type: ${flight.items[0].flightClass.type})` :\n                  'No class info');\n\n                // Afficher la structure complète du premier vol pour analyse\n                if (index === 0) {\n                  console.group('First Flight Structure');\n                  console.log('Flight ID:', flight.id);\n                  console.log('Flight Items:', flight.items);\n\n                  if (flight.items && flight.items.length > 0) {\n                    console.log('First Item FlightClass:', flight.items[0].flightClass);\n\n                    if (flight.items[0].segments) {\n                      console.log('Segments:', flight.items[0].segments);\n                      flight.items[0].segments.forEach((segment, segIndex) => {\n                        console.log(`Segment ${segIndex} FlightClass:`, segment.flightClass);\n                      });\n                    }\n                  }\n\n                  if (flight.offers && flight.offers.length > 0) {\n                    console.log('First Offer:', flight.offers[0]);\n                    console.log('FlightClassInformations:', flight.offers[0].flightClassInformations);\n                  }\n\n                  console.groupEnd();\n                }\n              } else {\n                flightsByClass['unknown']++;\n              }\n            });\n\n            console.log('Flights by class:', flightsByClass);\n            console.groupEnd();\n\n            // Calculer les prix minimums pour chaque option de filtre\n            this.calculateFilterPrices();\n\n            // Appliquer le filtre actuel aux résultats\n            this.applyAllFilters();\n\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            // console.log('Full API Response:', JSON.stringify(response, null, 2));\n\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f =>\n                f.offers.map(o => {\n                  const offer = o as any;\n                  return offer.availability !== undefined ? offer.availability :\n                         (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n                })\n              );\n              console.log('Availability values:', availabilityValues);\n\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc: Record<number, number>, val: number) => {\n                if (val !== undefined) {\n                  acc[val] = (acc[val] || 0) + 1;\n                }\n                return acc;\n              }, {} as Record<number, number>);\n              console.log('Availability counts:', availabilityCounts);\n\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f =>\n                f.offers.some(o => {\n                  const offer = o as any;\n                  return offer.reservableInfo && offer.reservableInfo.reservable === true;\n                })\n              );\n              console.log('Reservable flights:', reservableFlights.length);\n\n              console.groupEnd();\n            }\n\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n  }\n\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup: FormGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n\n  // Formater la durée en heures et minutes\n  formatDuration(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n\n  // Formater la date pour l'affichage\n  formatDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Formater la date pour l'affichage avec indication si elle diffère de la date demandée\n  formatDateWithRequestComparison(dateString: string, requestedDate: string): string {\n    if (!dateString) return 'N/A';\n\n    const date = new Date(dateString);\n    const formatted = date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n\n    // Si une date demandée est fournie, vérifier si la date réelle est différente\n    if (requestedDate) {\n      const requested = new Date(requestedDate);\n\n      // Comparer seulement les dates (sans l'heure)\n      const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n      const requestedOnly = new Date(requested.getFullYear(), requested.getMonth(), requested.getDate());\n\n      if (dateOnly.getTime() !== requestedOnly.getTime()) {\n        // Ajouter une indication visuelle que la date diffère de celle demandée\n        return `${formatted} <span class=\"date-differs\" title=\"Differs from requested date: ${requestedOnly.toLocaleDateString('fr-FR')}\">*</span>`;\n      }\n    }\n\n    return formatted;\n  }\n\n  // Obtenir les dates de départ et de retour d'un vol aller-retour\n  getFlightDates(flight: any): { outbound: string, inbound: string } {\n    const result = { outbound: 'N/A', inbound: 'N/A' };\n\n    if (!flight || !flight.items || flight.items.length === 0) {\n      return result;\n    }\n\n    // Chercher les segments aller (outbound)\n    const outboundItems = flight.items.filter((item: any) => item.segmentNumber === 1 || item.route === 1);\n    if (outboundItems.length > 0 && outboundItems[0].departure && outboundItems[0].departure.date) {\n      result.outbound = outboundItems[0].departure.date;\n    } else if (flight.items[0] && flight.items[0].departure && flight.items[0].departure.date) {\n      result.outbound = flight.items[0].departure.date;\n    }\n\n    // Chercher les segments retour (inbound)\n    const inboundItems = flight.items.filter((item: any) => item.segmentNumber === 2 || item.route === 2);\n    if (inboundItems.length > 0 && inboundItems[0].departure && inboundItems[0].departure.date) {\n      result.inbound = inboundItems[0].departure.date;\n    } else if (flight.items.length > 1 && flight.items[1] && flight.items[1].departure && flight.items[1].departure.date) {\n      result.inbound = flight.items[1].departure.date;\n    }\n\n    return result;\n  }\n\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight: CommonFlight): string {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n\n    const minOffer = flight.offers.reduce((min: any, offer: any) =>\n      (offer.price && min.price && offer.price.amount < min.price.amount) ? offer : min, flight.offers[0]);\n\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight: CommonFlight): boolean {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability :\n                             (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n    return availabilityValue > 0;\n  }\n\n  // Vérifier si les dates réelles du vol correspondent aux dates demandées\n  checkFlightDatesMatch(flight: any, requestedDepartureDate: string, requestedReturnDate?: string): {\n    match: boolean,\n    message: string\n  } {\n    const result = { match: true, message: '' };\n\n    if (!flight || !flight.items || flight.items.length === 0) {\n      return result;\n    }\n\n    // Obtenir les dates réelles du vol\n    const flightDates = this.getFlightDates(flight);\n\n    // Comparer la date de départ\n    if (requestedDepartureDate && flightDates.outbound !== 'N/A') {\n      const requestedDepDate = new Date(requestedDepartureDate);\n      const actualDepDate = new Date(flightDates.outbound);\n\n      // Comparer seulement les dates (sans l'heure)\n      const requestedDepDay = new Date(requestedDepDate.getFullYear(), requestedDepDate.getMonth(), requestedDepDate.getDate());\n      const actualDepDay = new Date(actualDepDate.getFullYear(), actualDepDate.getMonth(), actualDepDate.getDate());\n\n      if (requestedDepDay.getTime() !== actualDepDay.getTime()) {\n        result.match = false;\n        result.message += `Departure date differs from requested: ${actualDepDay.toLocaleDateString()} instead of ${requestedDepDay.toLocaleDateString()}. `;\n      }\n    }\n\n    // Comparer la date de retour (si c'est un vol aller-retour)\n    if (requestedReturnDate && flightDates.inbound !== 'N/A') {\n      const requestedRetDate = new Date(requestedReturnDate);\n      const actualRetDate = new Date(flightDates.inbound);\n\n      // Comparer seulement les dates (sans l'heure)\n      const requestedRetDay = new Date(requestedRetDate.getFullYear(), requestedRetDate.getMonth(), requestedRetDate.getDate());\n      const actualRetDay = new Date(actualRetDate.getFullYear(), actualRetDate.getMonth(), actualRetDate.getDate());\n\n      if (requestedRetDay.getTime() !== actualRetDay.getTime()) {\n        result.match = false;\n        result.message += `Return date differs from requested: ${actualRetDay.toLocaleDateString()} instead of ${requestedRetDay.toLocaleDateString()}.`;\n      }\n    }\n\n    return result;\n  }\n\n  // Afficher un message d'information sur les dates des vols\n  getFlightDateInfoMessage(flight: any, requestedDepartureDate: string, requestedReturnDate?: string): string {\n    // Vérifier si c'est un vol aller-retour\n    const isRoundTrip = this.isRoundTripFlight(flight);\n\n    // Obtenir les dates réelles du vol\n    const flightDates = this.getFlightDates(flight);\n\n    // Vérifier si les dates correspondent\n    const dateCheck = this.checkFlightDatesMatch(flight, requestedDepartureDate, requestedReturnDate);\n\n    if (!dateCheck.match) {\n      // Si les dates ne correspondent pas, afficher un message d'information\n      return `<div class=\"date-info-message\">\n                <i class=\"fas fa-info-circle\"></i>\n                <span>The actual flight dates differ from your search criteria. ${dateCheck.message}</span>\n              </div>`;\n    } else if (isRoundTrip) {\n      // Si c'est un vol aller-retour avec des dates correspondantes\n      return `<div class=\"date-info-message date-info-success\">\n                <i class=\"fas fa-check-circle\"></i>\n                <span>Round-trip flight: Outbound on ${new Date(flightDates.outbound).toLocaleDateString()}, Return on ${new Date(flightDates.inbound).toLocaleDateString()}</span>\n              </div>`;\n    }\n\n    return '';\n  }\n\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations(): void {\n    const locationType = 2; // Type 2 (City) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations(): void {\n    const locationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options de départ pour un segment spécifique\n  showDepartureLocationsForSegment(index: number): void {\n    const locationType = 2; // Type 2 (City) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion pour ce segment spécifique\n      const input = document.getElementById(`departureLocation${index}`) as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options d'arrivée pour un segment spécifique\n  showArrivalLocationsForSegment(index: number): void {\n    const locationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion pour ce segment spécifique\n      const input = document.getElementById(`arrivalLocation${index}`) as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations(): void {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      arrivalLocation: departureLocation\n    });\n  }\n\n  // Échanger les emplacements de départ et d'arrivée pour un segment spécifique\n  swapSegmentLocations(index: number): void {\n    const segments = this.getSegmentsArray();\n    const segment = segments.at(index) as FormGroup;\n\n    if (segment) {\n      const departureLocation = segment.get('departureLocation')?.value;\n      const arrivalLocation = segment.get('arrivalLocation')?.value;\n\n      segment.patchValue({\n        departureLocation: arrivalLocation,\n        arrivalLocation: departureLocation\n      });\n    }\n  }\n\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType: number): string {\n    switch (baggageType) {\n      case 1: return 'Cabin Baggage';\n      case 2: return 'Checked Baggage';\n      case 3: return 'Hand Baggage';\n      default: return 'Baggage';\n    }\n  }\n\n  // Filtrer les bagages par type\n  filterBaggageByType(baggageInformations: any[], type: number): any[] {\n    if (!baggageInformations || !Array.isArray(baggageInformations)) {\n      return [];\n    }\n    return baggageInformations.filter(baggage => baggage.baggageType === type);\n  }\n\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType: number): string {\n    switch (passengerType) {\n      case 1: return 'Adult';\n      case 2: return 'Child';\n      case 3: return 'Infant';\n      default: return 'Passenger';\n    }\n  }\n\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment: any, nextSegment: any): string {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date ||\n        !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n\n  // Calculer la durée du séjour entre deux dates\n  calculateStayDuration(outboundArrivalDate: string, inboundDepartureDate: string): string {\n    if (!outboundArrivalDate || !inboundDepartureDate) {\n      return 'N/A';\n    }\n\n    const arrival = new Date(outboundArrivalDate);\n    const departure = new Date(inboundDepartureDate);\n\n    // Calculer la différence en jours\n    const diffTime = Math.abs(departure.getTime() - arrival.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 0) {\n      return 'Same day';\n    } else if (diffDays === 1) {\n      return '1 day';\n    } else {\n      return `${diffDays} days`;\n    }\n  }\n\n  // Détecter si un vol est un aller-retour\n  isRoundTripFlight(flight: any): boolean {\n    if (!flight || !flight.items || flight.items.length === 0) {\n      return false;\n    }\n\n    // Méthode 1: Vérifier le segmentNumber (1 = aller, 2 = retour)\n    // Dans la réponse API, les vols aller-retour ont souvent un item avec segmentNumber = 2\n    if (flight.items.some((item: any) => item.segmentNumber === 2)) {\n      console.log(`Flight ${flight.id} identified as round-trip by segmentNumber`);\n      return true;\n    }\n\n    // Méthode 2: Vérifier si le vol a plusieurs items (aller et retour)\n    if (flight.items.length > 1) {\n      console.log(`Flight ${flight.id} identified as round-trip by multiple items`);\n      return true;\n    }\n\n    // Méthode 3: Vérifier si le premier item a des segments qui forment un aller-retour\n    if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      const firstSegment = flight.items[0].segments[0];\n      const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n\n      // Vérifier si le premier segment part de A vers B et le dernier segment part de B vers A\n      if (firstSegment && firstSegment.departure && firstSegment.arrival &&\n          lastSegment && lastSegment.departure && lastSegment.arrival) {\n\n        const firstDeparture = firstSegment.departure.airport?.code || firstSegment.departure.city?.name;\n        const firstArrival = firstSegment.arrival.airport?.code || firstSegment.arrival.city?.name;\n        const lastDeparture = lastSegment.departure.airport?.code || lastSegment.departure.city?.name;\n        const lastArrival = lastSegment.arrival.airport?.code || lastSegment.arrival.city?.name;\n\n        // Si le dernier segment revient au point de départ du premier segment (boucle)\n        if (firstDeparture && lastArrival && firstDeparture === lastArrival) {\n          console.log(`Flight ${flight.id} identified as round-trip by segment loop`);\n          return true;\n        }\n\n        // Vérifier si c'est un aller-retour classique (A→B, B→A)\n        if (firstDeparture && firstArrival && lastDeparture && lastArrival &&\n            firstDeparture === lastArrival && firstArrival === lastDeparture) {\n          console.log(`Flight ${flight.id} identified as round-trip by segment pattern`);\n          return true;\n        }\n      }\n    }\n\n    // Méthode 4: Vérifier si le vol a été recherché avec ServiceTypes = 2 (aller-retour)\n    if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      console.log(`Flight ${flight.id} identified as round-trip by search type`);\n      return true;\n    }\n\n    return false;\n  }\n\n  // Obtenir les segments aller d'un vol aller-retour\n  getOutboundSegments(flight: any): any[] {\n    if (!flight.items) return [];\n\n    // Méthode 1: Utiliser le segmentNumber pour identifier les segments aller (segmentNumber = 1)\n    const outboundItems = flight.items.filter((item: any) => item.segmentNumber === 1);\n    if (outboundItems.length > 0) {\n      console.log(`Flight ${flight.id} outbound segments identified by segmentNumber`);\n      return outboundItems[0].segments || [outboundItems[0]];\n    }\n\n    // Méthode 2: Si le vol a plusieurs items, le premier est généralement l'aller\n    if (flight.items.length > 1) {\n      console.log(`Flight ${flight.id} outbound segments identified by multiple items`);\n      return flight.items[0].segments || [flight.items[0]];\n    }\n\n    // Méthode 3: Si le vol a un seul item avec plusieurs segments\n    if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      // Pour un aller-retour, on considère la première moitié des segments comme l'aller\n      const halfLength = Math.ceil(flight.items[0].segments.length / 2);\n      console.log(`Flight ${flight.id} outbound segments identified by splitting segments`);\n      return flight.items[0].segments.slice(0, halfLength);\n    }\n\n    // Par défaut, retourner tous les segments du premier item\n    return flight.items[0].segments || [flight.items[0]];\n  }\n\n  // Obtenir les segments retour d'un vol aller-retour\n  getInboundSegments(flight: any): any[] {\n    if (!flight.items) return [];\n\n    // Méthode 1: Utiliser le segmentNumber pour identifier les segments retour (segmentNumber = 2)\n    const inboundItems = flight.items.filter((item: any) => item.segmentNumber === 2);\n    if (inboundItems.length > 0) {\n      console.log(`Flight ${flight.id} inbound segments identified by segmentNumber`);\n      return inboundItems[0].segments || [inboundItems[0]];\n    }\n\n    // Méthode 2: Si le vol a plusieurs items, le second est généralement le retour\n    if (flight.items.length > 1) {\n      console.log(`Flight ${flight.id} inbound segments identified by multiple items`);\n      return flight.items[1].segments || [flight.items[1]];\n    }\n\n    // Méthode 3: Si le vol a un seul item avec plusieurs segments\n    if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      // Pour un aller-retour, on considère la seconde moitié des segments comme le retour\n      const halfLength = Math.ceil(flight.items[0].segments.length / 2);\n      console.log(`Flight ${flight.id} inbound segments identified by splitting segments`);\n      return flight.items[0].segments.slice(halfLength);\n    }\n\n    // Si aucun segment retour n'est trouvé\n    return [];\n  }\n\n  // Détecter si un vol est un multi-city\n  isMultiCityFlight(flight: any): boolean {\n    if (!flight || !flight.items || flight.items.length === 0) {\n      return false;\n    }\n\n    // Méthode 1: Vérifier si le vol a un segmentNumber > 2\n    if (flight.items.some((item: any) => item.segmentNumber > 2)) {\n      console.log(`Flight ${flight.id} identified as multi-city by segmentNumber > 2`);\n      return true;\n    }\n\n    // Méthode 2: Vérifier si le vol a plus de 2 items (plus que aller et retour)\n    if (flight.items.length > 2) {\n      console.log(`Flight ${flight.id} identified as multi-city by having more than 2 items`);\n      return true;\n    }\n\n    // Méthode 3: Vérifier si le vol a été recherché avec ServiceTypes = 3 (multi-city)\n    if (this.currentSearchType === this.SEARCH_TYPE_MULTI_CITY) {\n      console.log(`Flight ${flight.id} identified as multi-city by search type`);\n      return true;\n    }\n\n    return false;\n  }\n\n  // Les méthodes calculateStayDuration, formatDateWithRequestComparison et getFlightDateInfoMessage\n  // sont déjà implémentées plus haut dans le fichier\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,iBAAiB,EAAEC,YAAY,QAAQ,eAAe;AAClF,SAAsBC,SAAS,EAAaC,UAAU,QAAQ,gBAAgB;AAE9E,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnF,SAASC,EAAE,QAAQ,MAAM;AAKzB,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;AAcnF,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAwG/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,MAAc,EACdC,iBAAoC;IAHpC,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IA1G3B,KAAAC,kBAAkB,GAAqB,EAAE;IACzC,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,eAAe,GAAmB,EAAE;IACpC,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,YAAY,GAAG,EAAE;IAEjB;IACS,KAAAC,mBAAmB,GAAG,CAAC;IACvB,KAAAC,sBAAsB,GAAG,CAAC;IAC1B,KAAAC,sBAAsB,GAAG,CAAC;IAEnC;IACA,KAAAC,iBAAiB,GAAW,IAAI,CAACH,mBAAmB;IAEpD;IACA,KAAAI,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,eAAe,GAA8B;MAC3C,CAACpB,aAAa,CAACqB,KAAK,GAAG,CAAC;MACxB,CAACrB,aAAa,CAACsB,KAAK,GAAG,CAAC;MACxB,CAACtB,aAAa,CAACuB,MAAM,GAAG;KACzB;IAED;IACA,KAAAC,aAAa,GAAW,aAAa;IACrC,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAS,CAAE,EAC/D;MAAEF,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAgB,CAAE,EAChE;MAAEF,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAU,CAAE,CAC3D;IAED;IACA,KAAAC,cAAc,GAAG;MACfC,KAAK,EAAE;QACLC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE;OACZ;MACDC,aAAa,EAAE;QACbC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,KAAK,CAAO;OACtB;;MACDC,WAAW,EAAE;QACXJ,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,KAAK,CAAO;OACtB;;MACDE,QAAQ,EAAE,EAAgC,CAAE;KAC7C;IAED;IACA,KAAAC,gBAAgB,GAA+B;MAC7CX,KAAK,EAAE,IAAI;MACXI,aAAa,EAAE,IAAI;MACnBK,WAAW,EAAE,IAAI;MACjBC,QAAQ,EAAE;KACX;IAED;IACA,KAAAE,YAAY,GAAG;MACbZ,KAAK,EAAE;QACLC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE;OACZ;MACDC,aAAa,EAAE;QACbC,YAAY,EAAE,CAAC;QACfC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE;OACV;MACDC,WAAW,EAAE;QACXJ,YAAY,EAAE,CAAC;QACfC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE;OACV;MACDE,QAAQ,EAAE;KACX;IAED;IACA,KAAAG,cAAc,GAAG,CACf;MAAEjB,KAAK,EAAE1B,aAAa,CAACqB,KAAK;MAAEM,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAE1B,aAAa,CAACsB,KAAK;MAAEK,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAE1B,aAAa,CAACuB,MAAM;MAAEI,KAAK,EAAE;IAAQ,CAAE,CACjD;IAED;IACA,KAAAiB,aAAa,GAAG,CACd;MAAElB,KAAK,EAAE5B,eAAe,CAAC+C,KAAK;MAAElB,KAAK,EAAE;IAAO,CAAE,EAChD;MAAED,KAAK,EAAE5B,eAAe,CAACgD,OAAO;MAAEnB,KAAK,EAAE;IAAS,CAAE,EACpD;MAAED,KAAK,EAAE5B,eAAe,CAACiD,QAAQ;MAAEpB,KAAK,EAAE;IAAU,CAAE,CACvD;IAWC;IACA,IAAI,CAACqB,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAErD;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACjD,EAAE,CAACkD,KAAK,CAAC;MAC9B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAE9D,UAAU,CAAC+D,QAAQ,CAAC;MACrCC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,EAAEhE,UAAU,CAAC+D,QAAQ,CAAC;MAC1CE,iBAAiB,EAAE,CAAC,EAAE,EAAEjE,UAAU,CAAC+D,QAAQ,CAAC;MAC5CG,eAAe,EAAE,CAAC,EAAE,EAAElE,UAAU,CAAC+D,QAAQ,CAAC;MAC1CI,aAAa,EAAE,CAAC,IAAI,CAACX,OAAO,EAAExD,UAAU,CAAC+D,QAAQ,CAAC;MAClDK,UAAU,EAAE,CAAC,EAAE,CAAC;MAEhB;MACAC,QAAQ,EAAE,IAAI,CAAC1D,EAAE,CAAC2D,KAAK,CAAC,EAAE,CAAC;MAE3B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAEvE,UAAU,CAAC+D,QAAQ,CAAC;MACrCS,OAAO,EAAE,CAAC,KAAK,CAAC;MAEhB;MACAC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBC,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,wBAAwB,EAAE,CAAC,KAAK,CAAC;MACjCC,6BAA6B,EAAE,CAAC,IAAI,CAAC;MACrCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAE5B;MACAC,sBAAsB,EAAE,CAAC,CAAC;KAC3B,CAAC;IAEF;IACA,IAAI,CAACjE,iBAAiB,CAACkE,kBAAkB,CAAC,IAAI,CAACpD,eAAe,CAAC;EACjE;EAEA;EAEAqD,eAAeA,CAACC,KAAiB;IAC/B;IACA,MAAMC,cAAc,GAAGD,KAAK,CAACE,MAAqB;IAClD,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC;IAExE,IAAIF,iBAAiB,IAAI,CAACA,iBAAiB,CAACG,QAAQ,CAACL,cAAc,CAAC,EAAE;MACpE,IAAI,CAACxD,qBAAqB,GAAG,KAAK;;EAEtC;EAEA;EACA8D,uBAAuBA,CAACP,KAAY;IAClCA,KAAK,CAACQ,eAAe,EAAE;IACvB,IAAI,CAAC/D,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEA;EACAgE,sBAAsBA,CAAA;IACpB,IAAI,CAAChE,qBAAqB,GAAG,KAAK;EACpC;EAEA;EACAiE,iBAAiBA,CAACC,IAAY;IAC5B,OAAO,IAAI,CAACjE,eAAe,CAACiE,IAAI,CAAC,IAAI,CAAC;EACxC;EAEA;EACAC,sBAAsBA,CAACD,IAAY;IACjC,IAAI,IAAI,CAACE,kBAAkB,EAAE,GAAG,CAAC,EAAE;MACjC,IAAI,CAACnE,eAAe,CAACiE,IAAI,CAAC,GAAG,CAAC,IAAI,CAACjE,eAAe,CAACiE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;MAClE;MACA,IAAI,CAAC/E,iBAAiB,CAACkE,kBAAkB,CAAC,IAAI,CAACpD,eAAe,CAAC;;EAEnE;EAEA;EACAoE,sBAAsBA,CAACH,IAAY;IACjC;IACA,IAAIA,IAAI,KAAKrF,aAAa,CAACqB,KAAK,EAAE;MAChC,IAAI,IAAI,CAACD,eAAe,CAACiE,IAAI,CAAC,GAAG,CAAC,EAAE;QAClC,IAAI,CAACjE,eAAe,CAACiE,IAAI,CAAC,IAAI,CAAC;;KAElC,MAAM,IAAI,IAAI,CAACjE,eAAe,CAACiE,IAAI,CAAC,GAAG,CAAC,EAAE;MACzC,IAAI,CAACjE,eAAe,CAACiE,IAAI,CAAC,IAAI,CAAC;;IAEjC;IACA,IAAI,CAAC/E,iBAAiB,CAACkE,kBAAkB,CAAC,IAAI,CAACpD,eAAe,CAAC;EACjE;EAEA;EACAmE,kBAAkBA,CAAA;IAChB,OAAOE,MAAM,CAACC,MAAM,CAAC,IAAI,CAACtE,eAAe,CAAC,CAACuE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;EACnF;EAEA;EACAC,kBAAkBA,CAAA;IAChB;IACA,OAAO,IAAI,CAACxF,iBAAiB,CAACwF,kBAAkB,EAAE;EACpD;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACAC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EACjF;EAEA;EACAC,WAAWA,CAACC,WAAmB;IAC7B,IAAI,CAAC7E,aAAa,GAAG6E,WAAW;IAChC,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;EACQC,qBAAqBA,CAACC,MAAW,EAAEC,aAA8B;IACvE;IACA,IAAI,CAACD,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9CT,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,eAAe,CAAC;MAC/C,OAAO,KAAK;;IAGdV,OAAO,CAACC,GAAG,CAAC,mBAAmBK,MAAM,CAACI,EAAE,cAAcH,aAAa,EAAE,CAAC;IAEtE;IACA,KAAK,MAAMI,IAAI,IAAIL,MAAM,CAACE,KAAK,EAAE;MAC/B,IAAIG,IAAI,CAAC9C,WAAW,EAAE;QACpBmC,OAAO,CAACC,GAAG,CAAC,eAAeU,IAAI,CAAC9C,WAAW,CAAC+C,IAAI,WAAWD,IAAI,CAAC9C,WAAW,CAACsB,IAAI,GAAG,CAAC;QACpF,IAAIwB,IAAI,CAAC9C,WAAW,CAACsB,IAAI,KAAKoB,aAAa,EAAE;UAC3CP,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C,OAAO,IAAI;;;MAIf;MACA,IAAIU,IAAI,CAAChD,QAAQ,EAAE;QACjBqC,OAAO,CAACC,GAAG,CAAC,YAAYU,IAAI,CAAChD,QAAQ,CAAC8C,MAAM,WAAW,CAAC;QACxD,KAAK,MAAMI,OAAO,IAAIF,IAAI,CAAChD,QAAQ,EAAE;UACnC,IAAIkD,OAAO,CAAChD,WAAW,EAAE;YACvBmC,OAAO,CAACC,GAAG,CAAC,kBAAkBY,OAAO,CAAChD,WAAW,CAAC+C,IAAI,WAAWC,OAAO,CAAChD,WAAW,CAACsB,IAAI,GAAG,CAAC;YAC7F,IAAI0B,OAAO,CAAChD,WAAW,CAACsB,IAAI,KAAKoB,aAAa,EAAE;cAC9CP,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;cACjD,OAAO,IAAI;;;;;;IAOrB;IACA,IAAIK,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACQ,MAAM,CAACL,MAAM,GAAG,CAAC,EAAE;MAC7CT,OAAO,CAACC,GAAG,CAAC,YAAYK,MAAM,CAACQ,MAAM,CAACL,MAAM,SAAS,CAAC;MACtD,KAAK,MAAMM,KAAK,IAAIT,MAAM,CAACQ,MAAM,EAAE;QACjC,IAAIC,KAAK,CAACC,uBAAuB,IAAID,KAAK,CAACC,uBAAuB,CAACP,MAAM,GAAG,CAAC,EAAE;UAC7ET,OAAO,CAACC,GAAG,CAAC,aAAac,KAAK,CAACC,uBAAuB,CAACP,MAAM,cAAc,CAAC;UAC5E,KAAK,MAAMQ,SAAS,IAAIF,KAAK,CAACC,uBAAuB,EAAE;YACrDhB,OAAO,CAACC,GAAG,CAAC,qBAAqBgB,SAAS,CAACL,IAAI,WAAWK,SAAS,CAAC9B,IAAI,GAAG,CAAC;YAC5E,IAAI8B,SAAS,CAAC9B,IAAI,KAAKoB,aAAa,EAAE;cACpCP,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;cAC3D,OAAO,IAAI;;;SAGhB,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;;;IAKzDD,OAAO,CAACC,GAAG,CAAC,6BAA6BK,MAAM,CAACI,EAAE,EAAE,CAAC;IACrD,OAAO,KAAK;EACd;EAEA;EACAN,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC5F,aAAa,IAAI,IAAI,CAACA,aAAa,CAACiG,MAAM,KAAK,CAAC,EAAE;MAC1D,IAAI,CAAChG,eAAe,GAAG,EAAE;MACzB;;IAGF;IACA,IAAIyG,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC1G,aAAa,CAAC;IAErC;IACA,MAAM+F,aAAa,GAAG,IAAI,CAACrD,UAAU,CAACiE,GAAG,CAAC,aAAa,CAAC,EAAE3F,KAAK;IAE/D;IACA,IAAI+E,aAAa,KAAKa,SAAS,EAAE;MAC/BpB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEM,aAAa,CAAC;MAExD;MACAP,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEiB,OAAO,CAACT,MAAM,CAAC;MAEpE;MACA,MAAMY,WAAW,GAAG;QAClB,CAACzH,eAAe,CAAC+C,KAAK,GAAG,CAAC;QAC1B,CAAC/C,eAAe,CAACgD,OAAO,GAAG,CAAC;QAC5B,CAAChD,eAAe,CAACiD,QAAQ,GAAG;OAC7B;MAEDqE,OAAO,CAACI,OAAO,CAAChB,MAAM,IAAG;QACvB,IAAI,IAAI,CAACD,qBAAqB,CAACC,MAAM,EAAE1G,eAAe,CAAC+C,KAAK,CAAC,EAAE0E,WAAW,CAACzH,eAAe,CAAC+C,KAAK,CAAC,EAAE;QACnG,IAAI,IAAI,CAAC0D,qBAAqB,CAACC,MAAM,EAAE1G,eAAe,CAACgD,OAAO,CAAC,EAAEyE,WAAW,CAACzH,eAAe,CAACgD,OAAO,CAAC,EAAE;QACvG,IAAI,IAAI,CAACyD,qBAAqB,CAACC,MAAM,EAAE1G,eAAe,CAACiD,QAAQ,CAAC,EAAEwE,WAAW,CAACzH,eAAe,CAACiD,QAAQ,CAAC,EAAE;MAC3G,CAAC,CAAC;MAEFmD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEoB,WAAW,CAAC;MAEhE;MACA;MACA,MAAME,qBAAqB,GAAG,IAAI;MAElC,IAAI,CAACA,qBAAqB,EAAE;QAC1BL,OAAO,GAAGA,OAAO,CAACM,MAAM,CAAClB,MAAM,IAAI,IAAI,CAACD,qBAAqB,CAACC,MAAM,EAAEC,aAAa,CAAC,CAAC;QACrFP,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEiB,OAAO,CAACT,MAAM,CAAC;OAC9D,MAAM;QACLT,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;;IAIzD;IACA,IAAI,IAAI,CAACtE,cAAc,CAACC,KAAK,CAACC,MAAM,IAAI,IAAI,CAACF,cAAc,CAACC,KAAK,CAACE,OAAO,IAAI,IAAI,CAACH,cAAc,CAACC,KAAK,CAACG,SAAS,EAAE;MAChHmF,OAAO,GAAGA,OAAO,CAACM,MAAM,CAAClB,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;QAE5D,MAAMgB,SAAS,GAAGnB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACiB,SAAS,IAAI,CAAC;QAEhD,OAAQ,IAAI,CAAC9F,cAAc,CAACC,KAAK,CAACC,MAAM,IAAI4F,SAAS,KAAK,CAAC,IACnD,IAAI,CAAC9F,cAAc,CAACC,KAAK,CAACE,OAAO,IAAI2F,SAAS,KAAK,CAAE,IACrD,IAAI,CAAC9F,cAAc,CAACC,KAAK,CAACG,SAAS,IAAI0F,SAAS,IAAI,CAAE;MAChE,CAAC,CAAC;;IAGJ;IACA,IAAI,IAAI,CAAC9F,cAAc,CAACK,aAAa,CAACC,YAAY,IAC9C,IAAI,CAACN,cAAc,CAACK,aAAa,CAACE,OAAO,IACzC,IAAI,CAACP,cAAc,CAACK,aAAa,CAACG,SAAS,IAC3C,IAAI,CAACR,cAAc,CAACK,aAAa,CAACI,OAAO,EAAE;MAE7C8E,OAAO,GAAGA,OAAO,CAACM,MAAM,CAAClB,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,IAAI,CAACpB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,CAACC,IAAI,EAAE;UAC/G,OAAO,KAAK;;QAGd,MAAMlE,aAAa,GAAG,IAAIV,IAAI,CAACuD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,CAACC,IAAI,CAAC;QAC9D,MAAMC,KAAK,GAAGnE,aAAa,CAACoE,QAAQ,EAAE;QAEtC,OAAQ,IAAI,CAAClG,cAAc,CAACK,aAAa,CAACC,YAAY,IAAI2F,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,CAAC,IACzE,IAAI,CAACjG,cAAc,CAACK,aAAa,CAACE,OAAO,IAAI0F,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,EAAG,IACtE,IAAI,CAACjG,cAAc,CAACK,aAAa,CAACG,SAAS,IAAIyF,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG,IACzE,IAAI,CAACjG,cAAc,CAACK,aAAa,CAACI,OAAO,IAAIwF,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG;MACjF,CAAC,CAAC;;IAGJ;IACA,IAAI,IAAI,CAACjG,cAAc,CAACU,WAAW,CAACJ,YAAY,IAC5C,IAAI,CAACN,cAAc,CAACU,WAAW,CAACH,OAAO,IACvC,IAAI,CAACP,cAAc,CAACU,WAAW,CAACF,SAAS,IACzC,IAAI,CAACR,cAAc,CAACU,WAAW,CAACD,OAAO,EAAE;MAE3C8E,OAAO,GAAGA,OAAO,CAACM,MAAM,CAAClB,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACsB,OAAO,IAAI,CAACxB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACsB,OAAO,CAACH,IAAI,EAAE;UAC3G,OAAO,KAAK;;QAGd,MAAMI,WAAW,GAAG,IAAIhF,IAAI,CAACuD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACsB,OAAO,CAACH,IAAI,CAAC;QAC1D,MAAMC,KAAK,GAAGG,WAAW,CAACF,QAAQ,EAAE;QAEpC,OAAQ,IAAI,CAAClG,cAAc,CAACU,WAAW,CAACJ,YAAY,IAAI2F,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,CAAC,IACvE,IAAI,CAACjG,cAAc,CAACU,WAAW,CAACH,OAAO,IAAI0F,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,EAAG,IACpE,IAAI,CAACjG,cAAc,CAACU,WAAW,CAACF,SAAS,IAAIyF,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG,IACvE,IAAI,CAACjG,cAAc,CAACU,WAAW,CAACD,OAAO,IAAIwF,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG;MAC/E,CAAC,CAAC;;IAGJ;IACA,MAAMI,gBAAgB,GAAGzC,MAAM,CAAC0C,OAAO,CAAC,IAAI,CAACtG,cAAc,CAACW,QAAQ,CAAC,CAClEkF,MAAM,CAAC,CAAC,CAACU,CAAC,EAAEC,QAAQ,CAAC,KAAKA,QAAQ,CAAC,CACnCzI,GAAG,CAAC,CAAC,CAAC0I,OAAO,EAAEF,CAAC,CAAC,KAAKE,OAAO,CAAC;IAEjC,IAAIJ,gBAAgB,CAACvB,MAAM,GAAG,CAAC,EAAE;MAC/BS,OAAO,GAAGA,OAAO,CAACM,MAAM,CAAClB,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC4B,OAAO,IAAI,CAAC9B,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC4B,OAAO,CAACxB,IAAI,EAAE;UAC3G,OAAO,KAAK;;QAGd,OAAOoB,gBAAgB,CAACK,QAAQ,CAAC/B,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC4B,OAAO,CAACxB,IAAI,CAAC;MAChE,CAAC,CAAC;;IAGJ;IACA,QAAQ,IAAI,CAACtF,aAAa;MACxB,KAAK,UAAU;QACb,IAAI,CAACb,eAAe,GAAG,IAAI,CAAC6H,WAAW,CAACpB,OAAO,CAAC;QAChD;MACF,KAAK,UAAU;QACb,IAAI,CAACzG,eAAe,GAAG,IAAI,CAAC8H,cAAc,CAACrB,OAAO,CAAC;QACnD;MACF,KAAK,aAAa;MAClB;QACE,IAAI,CAACzG,eAAe,GAAG,IAAI,CAAC+H,oBAAoB,CAACtB,OAAO,CAAC;QACzD;;EAEN;EAEA;EACQoB,WAAWA,CAACG,OAAc;IAChC,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,MAAMC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACH,CAAC,CAAC;MACxC,MAAMI,MAAM,GAAG,IAAI,CAACD,iBAAiB,CAACF,CAAC,CAAC;MACxC,OAAOC,MAAM,GAAGE,MAAM;IACxB,CAAC,CAAC;EACJ;EAEA;EACQR,cAAcA,CAACE,OAAc;IACnC,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,MAAMI,SAAS,GAAGL,CAAC,CAACnC,KAAK,IAAImC,CAAC,CAACnC,KAAK,CAAC,CAAC,CAAC,GAAGmC,CAAC,CAACnC,KAAK,CAAC,CAAC,CAAC,CAACyC,QAAQ,GAAGC,MAAM,CAACC,SAAS;MAChF,MAAMC,SAAS,GAAGR,CAAC,CAACpC,KAAK,IAAIoC,CAAC,CAACpC,KAAK,CAAC,CAAC,CAAC,GAAGoC,CAAC,CAACpC,KAAK,CAAC,CAAC,CAAC,CAACyC,QAAQ,GAAGC,MAAM,CAACC,SAAS;MAChF,OAAOH,SAAS,GAAGI,SAAS;IAC9B,CAAC,CAAC;EACJ;EAEA;EACQZ,oBAAoBA,CAACC,OAAc;IACzC,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B;MACA,MAAMS,MAAM,GAAG,IAAI,CAACC,4BAA4B,CAACX,CAAC,CAAC;MACnD,MAAMY,MAAM,GAAG,IAAI,CAACD,4BAA4B,CAACV,CAAC,CAAC;MACnD,OAAOW,MAAM,GAAGF,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ;EAEA;EACQC,4BAA4BA,CAAChD,MAAW;IAC9C,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACQ,MAAM,CAACL,MAAM,KAAK,CAAC,EAAE;MAC9F,OAAO,CAAC;;IAGV,MAAME,IAAI,GAAGL,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IAC5B,MAAMO,KAAK,GAAGT,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC;IAE9B;IACA,MAAM0C,KAAK,GAAG,IAAI,CAACV,iBAAiB,CAACxC,MAAM,CAAC;IAC5C,MAAM2C,QAAQ,GAAGtC,IAAI,CAACsC,QAAQ;IAC9B,MAAMxB,SAAS,GAAGd,IAAI,CAACc,SAAS,IAAI,CAAC;IACrC,MAAMgC,YAAY,GAAG1C,KAAK,CAAC0C,YAAY,KAAKrC,SAAS,GAAGL,KAAK,CAAC0C,YAAY,GACrD1C,KAAK,CAAC2C,QAAQ,GAAG3C,KAAK,CAAC2C,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IAE5E;IACA,MAAMC,UAAU,GAAG,IAAI,IAAIJ,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;IACzC,MAAMK,aAAa,GAAG,IAAI,IAAIZ,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC;IAC/C,MAAMa,SAAS,GAAG,CAAC,IAAIrC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;IACvC,MAAMsC,iBAAiB,GAAGC,IAAI,CAACC,GAAG,CAACR,YAAY,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAE3D;IACA,MAAMS,OAAO,GAAG;MACdV,KAAK,EAAE,GAAG;MACVP,QAAQ,EAAE,GAAG;MACbrH,KAAK,EAAE,GAAG;MACV6H,YAAY,EAAE,GAAG,CAAC;KACnB;IAED;IACA,OACEG,UAAU,GAAGM,OAAO,CAACV,KAAK,GAC1BK,aAAa,GAAGK,OAAO,CAACjB,QAAQ,GAChCa,SAAS,GAAGI,OAAO,CAACtI,KAAK,GACzBmI,iBAAiB,GAAGG,OAAO,CAACT,YAAY;EAE5C;EAEA;EACQX,iBAAiBA,CAACxC,MAAW;IACnC,IAAI,CAACA,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACQ,MAAM,CAACL,MAAM,KAAK,CAAC,EAAE;MAChD,OAAOyC,MAAM,CAACC,SAAS;;IAGzB,OAAO7C,MAAM,CAACQ,MAAM,CAACrB,MAAM,CAAC,CAACwE,GAAW,EAAElD,KAAU,KAClDA,KAAK,CAACyC,KAAK,IAAIzC,KAAK,CAACyC,KAAK,CAACW,MAAM,GAAGF,GAAG,GAAGlD,KAAK,CAACyC,KAAK,CAACW,MAAM,GAAGF,GAAG,EAClE3D,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC,CAAC0C,KAAK,GAAGlD,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC,CAAC0C,KAAK,CAACW,MAAM,GAAGjB,MAAM,CAACC,SAAS,CAC1E;EACH;EAEA;EACAiB,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC5J,aAAa,IAAI,IAAI,CAACA,aAAa,CAACiG,MAAM,KAAK,CAAC,EAAE;MAC1D;;IAGF;IACA,IAAI,CAAC4D,iBAAiB,EAAE;IAExB;IACA,MAAM/H,QAAQ,GAAG,IAAIgI,GAAG,EAAU;IAElC;IACA,IAAI,CAAC9J,aAAa,CAAC8G,OAAO,CAAChB,MAAM,IAAG;MAClC,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACQ,MAAM,CAACL,MAAM,KAAK,CAAC,EAAE;QAC9F;;MAGF,MAAME,IAAI,GAAGL,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;MAC5B,MAAMgD,KAAK,GAAG,IAAI,CAACV,iBAAiB,CAACxC,MAAM,CAAC;MAE5C;MACA,IAAIK,IAAI,CAACyB,OAAO,IAAIzB,IAAI,CAACyB,OAAO,CAACxB,IAAI,EAAE;QACrCtE,QAAQ,CAACiI,GAAG,CAAC5D,IAAI,CAACyB,OAAO,CAACxB,IAAI,CAAC;QAE/B;QACA,IAAI,EAAED,IAAI,CAACyB,OAAO,CAACxB,IAAI,IAAI,IAAI,CAACpE,YAAY,CAACF,QAAQ,CAAC,EAAE;UACtD,IAAI,CAACE,YAAY,CAACF,QAAQ,CAACqE,IAAI,CAACyB,OAAO,CAACxB,IAAI,CAAC,GAAGsC,MAAM,CAACC,SAAS;;QAGlE;QACA,IAAI,CAAC3G,YAAY,CAACF,QAAQ,CAACqE,IAAI,CAACyB,OAAO,CAACxB,IAAI,CAAC,GAAGoD,IAAI,CAACC,GAAG,CACtD,IAAI,CAACzH,YAAY,CAACF,QAAQ,CAACqE,IAAI,CAACyB,OAAO,CAACxB,IAAI,CAAC,EAC7C4C,KAAK,CACN;;MAGH;MACA,MAAM/B,SAAS,GAAGd,IAAI,CAACc,SAAS,IAAI,CAAC;MACrC,IAAIA,SAAS,KAAK,CAAC,EAAE;QACnB,IAAI,CAACjF,YAAY,CAACZ,KAAK,CAACC,MAAM,GAAGmI,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzH,YAAY,CAACZ,KAAK,CAACC,MAAM,EAAE2H,KAAK,CAAC;OACjF,MAAM,IAAI/B,SAAS,KAAK,CAAC,EAAE;QAC1B,IAAI,CAACjF,YAAY,CAACZ,KAAK,CAACE,OAAO,GAAGkI,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzH,YAAY,CAACZ,KAAK,CAACE,OAAO,EAAE0H,KAAK,CAAC;OACnF,MAAM;QACL,IAAI,CAAChH,YAAY,CAACZ,KAAK,CAACG,SAAS,GAAGiI,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzH,YAAY,CAACZ,KAAK,CAACG,SAAS,EAAEyH,KAAK,CAAC;;MAGxF;MACA,IAAI7C,IAAI,CAACe,SAAS,IAAIf,IAAI,CAACe,SAAS,CAACC,IAAI,EAAE;QACzC,MAAMlE,aAAa,GAAG,IAAIV,IAAI,CAAC4D,IAAI,CAACe,SAAS,CAACC,IAAI,CAAC;QACnD,MAAM6C,cAAc,GAAG/G,aAAa,CAACoE,QAAQ,EAAE;QAE/C,IAAI2C,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAG,CAAC,EAAE;UAC7C,IAAI,CAAChI,YAAY,CAACR,aAAa,CAACC,YAAY,GAAG+H,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzH,YAAY,CAACR,aAAa,CAACC,YAAY,EAAEuH,KAAK,CAAC;SAC7G,MAAM,IAAIgB,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAG,EAAE,EAAE;UACrD,IAAI,CAAChI,YAAY,CAACR,aAAa,CAACE,OAAO,GAAG8H,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzH,YAAY,CAACR,aAAa,CAACE,OAAO,EAAEsH,KAAK,CAAC;SACnG,MAAM,IAAIgB,cAAc,IAAI,EAAE,IAAIA,cAAc,GAAG,EAAE,EAAE;UACtD,IAAI,CAAChI,YAAY,CAACR,aAAa,CAACG,SAAS,GAAG6H,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzH,YAAY,CAACR,aAAa,CAACG,SAAS,EAAEqH,KAAK,CAAC;SACvG,MAAM;UACL,IAAI,CAAChH,YAAY,CAACR,aAAa,CAACI,OAAO,GAAG4H,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzH,YAAY,CAACR,aAAa,CAACI,OAAO,EAAEoH,KAAK,CAAC;;;MAItG;MACA,IAAI7C,IAAI,CAACmB,OAAO,IAAInB,IAAI,CAACmB,OAAO,CAACH,IAAI,EAAE;QACrC,MAAMI,WAAW,GAAG,IAAIhF,IAAI,CAAC4D,IAAI,CAACmB,OAAO,CAACH,IAAI,CAAC;QAC/C,MAAM8C,YAAY,GAAG1C,WAAW,CAACF,QAAQ,EAAE;QAE3C,IAAI4C,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,CAAC,EAAE;UACzC,IAAI,CAACjI,YAAY,CAACH,WAAW,CAACJ,YAAY,GAAG+H,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzH,YAAY,CAACH,WAAW,CAACJ,YAAY,EAAEuH,KAAK,CAAC;SACzG,MAAM,IAAIiB,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,EAAE,EAAE;UACjD,IAAI,CAACjI,YAAY,CAACH,WAAW,CAACH,OAAO,GAAG8H,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzH,YAAY,CAACH,WAAW,CAACH,OAAO,EAAEsH,KAAK,CAAC;SAC/F,MAAM,IAAIiB,YAAY,IAAI,EAAE,IAAIA,YAAY,GAAG,EAAE,EAAE;UAClD,IAAI,CAACjI,YAAY,CAACH,WAAW,CAACF,SAAS,GAAG6H,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzH,YAAY,CAACH,WAAW,CAACF,SAAS,EAAEqH,KAAK,CAAC;SACnG,MAAM;UACL,IAAI,CAAChH,YAAY,CAACH,WAAW,CAACD,OAAO,GAAG4H,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzH,YAAY,CAACH,WAAW,CAACD,OAAO,EAAEoH,KAAK,CAAC;;;IAGpG,CAAC,CAAC;IAEF;IACAlH,QAAQ,CAACgF,OAAO,CAACc,OAAO,IAAG;MACzB,IAAI,EAAEA,OAAO,IAAI,IAAI,CAACzG,cAAc,CAACW,QAAQ,CAAC,EAAE;QAC9C,IAAI,CAACX,cAAc,CAACW,QAAQ,CAAC8F,OAAO,CAAC,GAAG,KAAK;;IAEjD,CAAC,CAAC;IAEF;IACA,IAAI,CAACsC,mBAAmB,EAAE;EAC5B;EAEA;EACQL,iBAAiBA,CAAA;IACvB,IAAI,CAAC7H,YAAY,GAAG;MAClBZ,KAAK,EAAE;QACLC,MAAM,EAAEqH,MAAM,CAACC,SAAS;QACxBrH,OAAO,EAAEoH,MAAM,CAACC,SAAS;QACzBpH,SAAS,EAAEmH,MAAM,CAACC;OACnB;MACDnH,aAAa,EAAE;QACbC,YAAY,EAAEiH,MAAM,CAACC,SAAS;QAC9BjH,OAAO,EAAEgH,MAAM,CAACC,SAAS;QACzBhH,SAAS,EAAE+G,MAAM,CAACC,SAAS;QAC3B/G,OAAO,EAAE8G,MAAM,CAACC;OACjB;MACD9G,WAAW,EAAE;QACXJ,YAAY,EAAEiH,MAAM,CAACC,SAAS;QAC9BjH,OAAO,EAAEgH,MAAM,CAACC,SAAS;QACzBhH,SAAS,EAAE+G,MAAM,CAACC,SAAS;QAC3B/G,OAAO,EAAE8G,MAAM,CAACC;OACjB;MACD7G,QAAQ,EAAE;KACX;EACH;EAEA;EACQoI,mBAAmBA,CAAA;IACzB;IACA,IAAI,IAAI,CAAClI,YAAY,CAACZ,KAAK,CAACC,MAAM,KAAKqH,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC3G,YAAY,CAACZ,KAAK,CAACC,MAAM,GAAG,CAAC;IAC3F,IAAI,IAAI,CAACW,YAAY,CAACZ,KAAK,CAACE,OAAO,KAAKoH,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC3G,YAAY,CAACZ,KAAK,CAACE,OAAO,GAAG,CAAC;IAC7F,IAAI,IAAI,CAACU,YAAY,CAACZ,KAAK,CAACG,SAAS,KAAKmH,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC3G,YAAY,CAACZ,KAAK,CAACG,SAAS,GAAG,CAAC;IAEjG;IACA,IAAI,IAAI,CAACS,YAAY,CAACR,aAAa,CAACC,YAAY,KAAKiH,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC3G,YAAY,CAACR,aAAa,CAACC,YAAY,GAAG,CAAC;IACvH,IAAI,IAAI,CAACO,YAAY,CAACR,aAAa,CAACE,OAAO,KAAKgH,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC3G,YAAY,CAACR,aAAa,CAACE,OAAO,GAAG,CAAC;IAC7G,IAAI,IAAI,CAACM,YAAY,CAACR,aAAa,CAACG,SAAS,KAAK+G,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC3G,YAAY,CAACR,aAAa,CAACG,SAAS,GAAG,CAAC;IACjH,IAAI,IAAI,CAACK,YAAY,CAACR,aAAa,CAACI,OAAO,KAAK8G,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC3G,YAAY,CAACR,aAAa,CAACI,OAAO,GAAG,CAAC;IAE7G;IACA,IAAI,IAAI,CAACI,YAAY,CAACH,WAAW,CAACJ,YAAY,KAAKiH,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC3G,YAAY,CAACH,WAAW,CAACJ,YAAY,GAAG,CAAC;IACnH,IAAI,IAAI,CAACO,YAAY,CAACH,WAAW,CAACH,OAAO,KAAKgH,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC3G,YAAY,CAACH,WAAW,CAACH,OAAO,GAAG,CAAC;IACzG,IAAI,IAAI,CAACM,YAAY,CAACH,WAAW,CAACF,SAAS,KAAK+G,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC3G,YAAY,CAACH,WAAW,CAACF,SAAS,GAAG,CAAC;IAC7G,IAAI,IAAI,CAACK,YAAY,CAACH,WAAW,CAACD,OAAO,KAAK8G,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC3G,YAAY,CAACH,WAAW,CAACD,OAAO,GAAG,CAAC;IAEzG;IACAmD,MAAM,CAACoF,IAAI,CAAC,IAAI,CAACnI,YAAY,CAACF,QAAQ,CAAC,CAACgF,OAAO,CAACc,OAAO,IAAG;MACxD,IAAI,IAAI,CAAC5F,YAAY,CAACF,QAAQ,CAAC8F,OAAO,CAAC,KAAKc,MAAM,CAACC,SAAS,EAAE;QAC5D,IAAI,CAAC3G,YAAY,CAACF,QAAQ,CAAC8F,OAAO,CAAC,GAAG,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAwC,aAAaA,CAACC,OAAe;IAC3B,IAAI,CAACtI,gBAAgB,CAACsI,OAAO,CAAC,GAAG,CAAC,IAAI,CAACtI,gBAAgB,CAACsI,OAAO,CAAC;EAClE;EAEA;EACAC,gBAAgBA,CAACtD,MAA0C;IACzD,IAAI,CAAC7F,cAAc,CAACC,KAAK,CAAC4F,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC7F,cAAc,CAACC,KAAK,CAAC4F,MAAM,CAAC;IACtE,IAAI,CAACpB,eAAe,EAAE;EACxB;EAEA;EACA2E,yBAAyBA,CAACvD,MAA4D;IACpF,IAAI,CAAC7F,cAAc,CAACK,aAAa,CAACwF,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC7F,cAAc,CAACK,aAAa,CAACwF,MAAM,CAAC;IACtF,IAAI,CAACpB,eAAe,EAAE;EACxB;EAEA;EACA4E,uBAAuBA,CAACxD,MAA4D;IAClF,IAAI,CAAC7F,cAAc,CAACU,WAAW,CAACmF,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC7F,cAAc,CAACU,WAAW,CAACmF,MAAM,CAAC;IAClF,IAAI,CAACpB,eAAe,EAAE;EACxB;EAEA;EACA6E,mBAAmBA,CAAC7C,OAAe;IACjC,IAAI,CAACzG,cAAc,CAACW,QAAQ,CAAC8F,OAAO,CAAC,GAAG,CAAC,IAAI,CAACzG,cAAc,CAACW,QAAQ,CAAC8F,OAAO,CAAC;IAC9E,IAAI,CAAChC,eAAe,EAAE;EACxB;EAEA;EACA8E,eAAeA,CAAA;IACb;IACA,IAAI,CAACvJ,cAAc,CAACC,KAAK,CAACC,MAAM,GAAG,KAAK;IACxC,IAAI,CAACF,cAAc,CAACC,KAAK,CAACE,OAAO,GAAG,KAAK;IACzC,IAAI,CAACH,cAAc,CAACC,KAAK,CAACG,SAAS,GAAG,KAAK;IAE3C;IACA,IAAI,CAACJ,cAAc,CAACK,aAAa,CAACC,YAAY,GAAG,KAAK;IACtD,IAAI,CAACN,cAAc,CAACK,aAAa,CAACE,OAAO,GAAG,KAAK;IACjD,IAAI,CAACP,cAAc,CAACK,aAAa,CAACG,SAAS,GAAG,KAAK;IACnD,IAAI,CAACR,cAAc,CAACK,aAAa,CAACI,OAAO,GAAG,KAAK;IAEjD;IACA,IAAI,CAACT,cAAc,CAACU,WAAW,CAACJ,YAAY,GAAG,KAAK;IACpD,IAAI,CAACN,cAAc,CAACU,WAAW,CAACH,OAAO,GAAG,KAAK;IAC/C,IAAI,CAACP,cAAc,CAACU,WAAW,CAACF,SAAS,GAAG,KAAK;IACjD,IAAI,CAACR,cAAc,CAACU,WAAW,CAACD,OAAO,GAAG,KAAK;IAE/C;IACAmD,MAAM,CAACoF,IAAI,CAAC,IAAI,CAAChJ,cAAc,CAACW,QAAQ,CAAC,CAACgF,OAAO,CAACc,OAAO,IAAG;MAC1D,IAAI,CAACzG,cAAc,CAACW,QAAQ,CAAC8F,OAAO,CAAC,GAAG,KAAK;IAC/C,CAAC,CAAC;IAEF;IACA,IAAI,CAAChC,eAAe,EAAE;EACxB;EAEA;EACA+E,WAAWA,CAAC3B,KAAa;IACvB,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,GAAG;IAC3B,OAAOA,KAAK,CAAC4B,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;EAChC;EAEA;EACAC,cAAcA,CAAA;IACZ,OAAO9F,MAAM,CAACoF,IAAI,CAAC,IAAI,CAAChJ,cAAc,CAACW,QAAQ,CAAC;EAClD;EAEA;EACAgJ,cAAcA,CAAChF,MAAoB;IACjC;IACA,MAAMiF,QAAQ,GAAG3G,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;IAC9CD,QAAQ,CAACE,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCH,QAAQ,CAACE,KAAK,CAACE,GAAG,GAAG,GAAG;IACxBJ,QAAQ,CAACE,KAAK,CAACG,IAAI,GAAG,GAAG;IACzBL,QAAQ,CAACE,KAAK,CAACI,KAAK,GAAG,MAAM;IAC7BN,QAAQ,CAACE,KAAK,CAACK,MAAM,GAAG,MAAM;IAC9BP,QAAQ,CAACE,KAAK,CAACM,eAAe,GAAG,oBAAoB;IACrDR,QAAQ,CAACE,KAAK,CAACO,MAAM,GAAG,MAAM;IAC9BT,QAAQ,CAACE,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC/BV,QAAQ,CAACE,KAAK,CAACS,cAAc,GAAG,QAAQ;IACxCX,QAAQ,CAACE,KAAK,CAACU,UAAU,GAAG,QAAQ;IAEpC;IACA,MAAMC,YAAY,GAAGxH,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;IAClDY,YAAY,CAACX,KAAK,CAACM,eAAe,GAAG,OAAO;IAC5CK,YAAY,CAACX,KAAK,CAACY,OAAO,GAAG,MAAM;IACnCD,YAAY,CAACX,KAAK,CAACa,YAAY,GAAG,MAAM;IACxCF,YAAY,CAACX,KAAK,CAACc,QAAQ,GAAG,KAAK;IACnCH,YAAY,CAACX,KAAK,CAACe,SAAS,GAAG,KAAK;IACpCJ,YAAY,CAACX,KAAK,CAACgB,QAAQ,GAAG,MAAM;IACpCL,YAAY,CAACX,KAAK,CAACC,QAAQ,GAAG,UAAU;IACxCU,YAAY,CAACX,KAAK,CAACiB,SAAS,GAAG,gCAAgC;IAC/DN,YAAY,CAACX,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCP,YAAY,CAACX,KAAK,CAACmB,UAAU,GAAG,mBAAmB;IAEnD;IACA,MAAMC,WAAW,GAAGjI,QAAQ,CAAC4G,aAAa,CAAC,QAAQ,CAAC;IACpDqB,WAAW,CAACC,SAAS,GAAG,SAAS;IACjCD,WAAW,CAACpB,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvCmB,WAAW,CAACpB,KAAK,CAACE,GAAG,GAAG,MAAM;IAC9BkB,WAAW,CAACpB,KAAK,CAACsB,KAAK,GAAG,MAAM;IAChCF,WAAW,CAACpB,KAAK,CAACuB,MAAM,GAAG,MAAM;IACjCH,WAAW,CAACpB,KAAK,CAACwB,UAAU,GAAG,MAAM;IACrCJ,WAAW,CAACpB,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACnCL,WAAW,CAACpB,KAAK,CAAC0B,MAAM,GAAG,SAAS;IACpCN,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnCE,WAAW,CAACpB,KAAK,CAAC2B,UAAU,GAAG,YAAY;IAC3CP,WAAW,CAACQ,WAAW,GAAG,MAAMR,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnEE,WAAW,CAACS,UAAU,GAAG,MAAMT,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IAClEE,WAAW,CAACU,OAAO,GAAG,MAAM3I,QAAQ,CAAC4I,IAAI,CAACC,WAAW,CAAClC,QAAQ,CAAC;IAE/D;IACA,MAAMmC,MAAM,GAAG9I,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;IAC5CkC,MAAM,CAACjC,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC7ByB,MAAM,CAACjC,KAAK,CAACU,UAAU,GAAG,QAAQ;IAClCuB,MAAM,CAACjC,KAAK,CAACkC,YAAY,GAAG,MAAM;IAClCD,MAAM,CAACjC,KAAK,CAACmC,aAAa,GAAG,MAAM;IACnCF,MAAM,CAACjC,KAAK,CAACoC,YAAY,GAAG,gBAAgB;IAE5C,MAAMC,IAAI,GAAGlJ,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;IAC1CsC,IAAI,CAAChB,SAAS,GAAG,2FAA2F;IAE5G,MAAMiB,KAAK,GAAGnJ,QAAQ,CAAC4G,aAAa,CAAC,IAAI,CAAC;IAC1CuC,KAAK,CAACC,WAAW,GAAG,yBAAyB;IAC7CD,KAAK,CAACtC,KAAK,CAACwC,MAAM,GAAG,GAAG;IACxBF,KAAK,CAACtC,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC7Ba,KAAK,CAACtC,KAAK,CAACyC,UAAU,GAAG,KAAK;IAC9BH,KAAK,CAACtC,KAAK,CAACkB,KAAK,GAAG,SAAS;IAE7Be,MAAM,CAACS,WAAW,CAACL,IAAI,CAAC;IACxBJ,MAAM,CAACS,WAAW,CAACJ,KAAK,CAAC;IAEzB;IACA,MAAMK,gBAAgB,GAAGxJ,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;IACtD4C,gBAAgB,CAAC3C,KAAK,CAACQ,OAAO,GAAG,MAAM;IACvCmC,gBAAgB,CAAC3C,KAAK,CAAC4C,aAAa,GAAG,QAAQ;IAC/CD,gBAAgB,CAAC3C,KAAK,CAAC6C,GAAG,GAAG,MAAM;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9E;IACA,IAAIlI,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAME,IAAI,GAAGL,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,IAAIG,IAAI,CAACyB,OAAO,EAAE;QAChB,MAAMqG,WAAW,GAAG7J,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QACjDiD,WAAW,CAAChD,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCwC,WAAW,CAAChD,KAAK,CAACU,UAAU,GAAG,QAAQ;QACvCsC,WAAW,CAAChD,KAAK,CAACkC,YAAY,GAAG,MAAM;QAEvC;QACA,IAAIhH,IAAI,CAACyB,OAAO,CAACsG,aAAa,EAAE;UAC9B,MAAMC,WAAW,GAAG/J,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;UACjDmD,WAAW,CAACC,GAAG,GAAGjI,IAAI,CAACyB,OAAO,CAACsG,aAAa;UAC5CC,WAAW,CAACE,GAAG,GAAGlI,IAAI,CAACyB,OAAO,CAACxB,IAAI;UACnC+H,WAAW,CAAClD,KAAK,CAACK,MAAM,GAAG,MAAM;UACjC6C,WAAW,CAAClD,KAAK,CAACqD,WAAW,GAAG,MAAM;UACtCL,WAAW,CAACN,WAAW,CAACQ,WAAW,CAAC;SACrC,MAAM;UACL,MAAMI,WAAW,GAAGnK,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;UACjDuD,WAAW,CAACjC,SAAS,GAAG,wFAAwF;UAChH2B,WAAW,CAACN,WAAW,CAACY,WAAW,CAAC;;QAGtC,MAAMC,WAAW,GAAGpK,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QACjDwD,WAAW,CAAClC,SAAS,GAAG,WAAWnG,IAAI,CAACyB,OAAO,CAACxB,IAAI,cAAcD,IAAI,CAACyB,OAAO,CAAC6G,iBAAiB,GAAG;QACnGD,WAAW,CAACvD,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCuB,WAAW,CAACN,WAAW,CAACa,WAAW,CAAC;QAEpCT,WAAW,CAACJ,WAAW,CAACM,WAAW,CAAC;;MAGtC;MACA,MAAMS,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC,eAAe,EAAExI,IAAI,CAACyI,QAAQ,IAAI,KAAK,CAAC;MACnFb,WAAW,CAACJ,WAAW,CAACe,eAAe,CAAC;MAExC;MACA,MAAMG,aAAa,GAAG,IAAI,CAACF,aAAa,CAAC,aAAa,EAAE,IAAIpM,IAAI,CAAC4D,IAAI,CAAC2I,UAAU,CAAC,CAACC,kBAAkB,EAAE,CAAC;MACvGhB,WAAW,CAACJ,WAAW,CAACkB,aAAa,CAAC;MAEtC;MACA,MAAMG,WAAW,GAAG,IAAI,CAACL,aAAa,CAAC,UAAU,EAAE,IAAI,CAACM,cAAc,CAAC9I,IAAI,CAACsC,QAAQ,CAAC,CAAC;MACtFsF,WAAW,CAACJ,WAAW,CAACqB,WAAW,CAAC;MAEpC;MACA,IAAI7I,IAAI,CAAC9C,WAAW,EAAE;QACpB,MAAM6L,QAAQ,GAAG,IAAI,CAACP,aAAa,CAAC,OAAO,EAAE,GAAGxI,IAAI,CAAC9C,WAAW,CAAC+C,IAAI,KAAKD,IAAI,CAAC9C,WAAW,CAAC8L,IAAI,GAAG,CAAC;QACnGpB,WAAW,CAACJ,WAAW,CAACuB,QAAQ,CAAC;;MAGnC;MACA,MAAME,QAAQ,GAAG,IAAI,CAACT,aAAa,CAAC,OAAO,EAAExI,IAAI,CAACc,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,GAAGd,IAAI,CAACc,SAAS,UAAU,CAAC;MAClH8G,WAAW,CAACJ,WAAW,CAACyB,QAAQ,CAAC;;IAGnC;IACA,MAAMC,YAAY,GAAG,IAAI,CAACrB,aAAa,CAAC,eAAe,EAAE,UAAU,CAAC;IAEpE,IAAIlI,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAME,IAAI,GAAGL,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,MAAMsJ,WAAW,GAAGlL,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;MACjDsE,WAAW,CAACrE,KAAK,CAACQ,OAAO,GAAG,MAAM;MAClC6D,WAAW,CAACrE,KAAK,CAACU,UAAU,GAAG,QAAQ;MACvC2D,WAAW,CAACrE,KAAK,CAACS,cAAc,GAAG,eAAe;MAClD4D,WAAW,CAACrE,KAAK,CAACwC,MAAM,GAAG,QAAQ;MACnC6B,WAAW,CAACrE,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEvC;MACA,MAAMhE,SAAS,GAAG9C,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;MAC/C9D,SAAS,CAAC+D,KAAK,CAACsE,SAAS,GAAG,QAAQ;MACpCrI,SAAS,CAAC+D,KAAK,CAACuE,IAAI,GAAG,GAAG;MAE1B,IAAIrJ,IAAI,CAACe,SAAS,EAAE;QAClB,MAAM1F,aAAa,GAAG4C,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QACnDxJ,aAAa,CAACyJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrClL,aAAa,CAACyJ,KAAK,CAACyC,UAAU,GAAG,MAAM;QACvClM,aAAa,CAACgM,WAAW,GAAG,IAAIjL,IAAI,CAAC4D,IAAI,CAACe,SAAS,CAACC,IAAI,CAAC,CAACsI,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAErH,MAAMC,gBAAgB,GAAGxL,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QACtD4E,gBAAgB,CAAC3E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACxCkD,gBAAgB,CAAC3E,KAAK,CAAC4E,SAAS,GAAG,KAAK;QACxCD,gBAAgB,CAACtD,SAAS,GAAG,WAAWnG,IAAI,CAACe,SAAS,CAAC4I,OAAO,EAAEX,IAAI,IAAI,KAAK,WAAW;QAExF,MAAMY,aAAa,GAAG3L,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QACnD+E,aAAa,CAAC9E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCqD,aAAa,CAAC9E,KAAK,CAACkB,KAAK,GAAG,MAAM;QAClC4D,aAAa,CAACvC,WAAW,GAAGrH,IAAI,CAACe,SAAS,CAAC8I,IAAI,EAAE5J,IAAI,IAAI,KAAK;QAE9Dc,SAAS,CAACyG,WAAW,CAACnM,aAAa,CAAC;QACpC0F,SAAS,CAACyG,WAAW,CAACiC,gBAAgB,CAAC;QACvC1I,SAAS,CAACyG,WAAW,CAACoC,aAAa,CAAC;;MAGtC;MACA,MAAME,cAAc,GAAG7L,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;MACpDiF,cAAc,CAAChF,KAAK,CAACuE,IAAI,GAAG,GAAG;MAC/BS,cAAc,CAAChF,KAAK,CAACQ,OAAO,GAAG,MAAM;MACrCwE,cAAc,CAAChF,KAAK,CAACU,UAAU,GAAG,QAAQ;MAC1CsE,cAAc,CAAChF,KAAK,CAACS,cAAc,GAAG,QAAQ;MAC9CuE,cAAc,CAAChF,KAAK,CAACY,OAAO,GAAG,QAAQ;MAEvC,MAAMqE,IAAI,GAAG9L,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;MAC1CkF,IAAI,CAACjF,KAAK,CAACK,MAAM,GAAG,KAAK;MACzB4E,IAAI,CAACjF,KAAK,CAACM,eAAe,GAAG,MAAM;MACnC2E,IAAI,CAACjF,KAAK,CAACI,KAAK,GAAG,MAAM;MACzB6E,IAAI,CAACjF,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEhC,MAAMiF,KAAK,GAAG/L,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;MAC3CmF,KAAK,CAAC7D,SAAS,GAAG,iGAAiG;MACnH6D,KAAK,CAAClF,KAAK,CAACC,QAAQ,GAAG,UAAU;MACjCiF,KAAK,CAAClF,KAAK,CAACE,GAAG,GAAG,MAAM;MACxBgF,KAAK,CAAClF,KAAK,CAACG,IAAI,GAAG,KAAK;MACxB+E,KAAK,CAAClF,KAAK,CAACmF,UAAU,GAAG,MAAM;MAC/BD,KAAK,CAAClF,KAAK,CAACM,eAAe,GAAG,OAAO;MACrC4E,KAAK,CAAClF,KAAK,CAACY,OAAO,GAAG,OAAO;MAE7BqE,IAAI,CAACvC,WAAW,CAACwC,KAAK,CAAC;MACvBF,cAAc,CAACtC,WAAW,CAACuC,IAAI,CAAC;MAEhC;MACA,MAAM5I,OAAO,GAAGlD,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;MAC7C1D,OAAO,CAAC2D,KAAK,CAACsE,SAAS,GAAG,QAAQ;MAClCjI,OAAO,CAAC2D,KAAK,CAACuE,IAAI,GAAG,GAAG;MAExB,IAAIrJ,IAAI,CAACmB,OAAO,EAAE;QAChB,MAAMzF,WAAW,GAAGuC,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QACjDnJ,WAAW,CAACoJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnC7K,WAAW,CAACoJ,KAAK,CAACyC,UAAU,GAAG,MAAM;QACrC7L,WAAW,CAAC2L,WAAW,GAAG,IAAIjL,IAAI,CAAC4D,IAAI,CAACmB,OAAO,CAACH,IAAI,CAAC,CAACsI,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAEjH,MAAMU,cAAc,GAAGjM,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QACpDqF,cAAc,CAACpF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACtC2D,cAAc,CAACpF,KAAK,CAAC4E,SAAS,GAAG,KAAK;QACtCQ,cAAc,CAAC/D,SAAS,GAAG,WAAWnG,IAAI,CAACmB,OAAO,CAACwI,OAAO,EAAEX,IAAI,IAAI,KAAK,WAAW;QAEpF,MAAMmB,WAAW,GAAGlM,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QACjDsF,WAAW,CAACrF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnC4D,WAAW,CAACrF,KAAK,CAACkB,KAAK,GAAG,MAAM;QAChCmE,WAAW,CAAC9C,WAAW,GAAGrH,IAAI,CAACmB,OAAO,CAAC0I,IAAI,EAAE5J,IAAI,IAAI,KAAK;QAE1DkB,OAAO,CAACqG,WAAW,CAAC9L,WAAW,CAAC;QAChCyF,OAAO,CAACqG,WAAW,CAAC0C,cAAc,CAAC;QACnC/I,OAAO,CAACqG,WAAW,CAAC2C,WAAW,CAAC;;MAGlChB,WAAW,CAAC3B,WAAW,CAACzG,SAAS,CAAC;MAClCoI,WAAW,CAAC3B,WAAW,CAACsC,cAAc,CAAC;MACvCX,WAAW,CAAC3B,WAAW,CAACrG,OAAO,CAAC;MAEhC+H,YAAY,CAAC1B,WAAW,CAAC2B,WAAW,CAAC;MAErC;MACA,IAAInJ,IAAI,CAAChD,QAAQ,IAAIgD,IAAI,CAAChD,QAAQ,CAAC8C,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMsK,aAAa,GAAGnM,QAAQ,CAAC4G,aAAa,CAAC,IAAI,CAAC;QAClDuF,aAAa,CAAC/C,WAAW,GAAG,iBAAiB;QAC7C+C,aAAa,CAACtF,KAAK,CAAC4E,SAAS,GAAG,MAAM;QACtCU,aAAa,CAACtF,KAAK,CAACkC,YAAY,GAAG,MAAM;QACzCoD,aAAa,CAACtF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC6D,aAAa,CAACtF,KAAK,CAACyC,UAAU,GAAG,KAAK;QACtC2B,YAAY,CAAC1B,WAAW,CAAC4C,aAAa,CAAC;QAEvC,MAAMC,YAAY,GAAGpM,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QAClDwF,YAAY,CAACvF,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnC+E,YAAY,CAACvF,KAAK,CAAC4C,aAAa,GAAG,QAAQ;QAC3C2C,YAAY,CAACvF,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B3H,IAAI,CAAChD,QAAQ,CAAC2D,OAAO,CAAC,CAACT,OAAO,EAAEoK,KAAK,KAAI;UACvC,MAAMC,WAAW,GAAGtM,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;UACjD0F,WAAW,CAACzF,KAAK,CAACY,OAAO,GAAG,MAAM;UAClC6E,WAAW,CAACzF,KAAK,CAACM,eAAe,GAAG,SAAS;UAC7CmF,WAAW,CAACzF,KAAK,CAACa,YAAY,GAAG,KAAK;UACtC4E,WAAW,CAACzF,KAAK,CAACuB,MAAM,GAAG,gBAAgB;UAE3C,MAAMmE,aAAa,GAAGvM,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;UACnD2F,aAAa,CAAC1F,KAAK,CAACQ,OAAO,GAAG,MAAM;UACpCkF,aAAa,CAAC1F,KAAK,CAACS,cAAc,GAAG,eAAe;UACpDiF,aAAa,CAAC1F,KAAK,CAACkC,YAAY,GAAG,MAAM;UACzCwD,aAAa,CAAC1F,KAAK,CAACmC,aAAa,GAAG,MAAM;UAC1CuD,aAAa,CAAC1F,KAAK,CAACoC,YAAY,GAAG,gBAAgB;UAEnD,MAAMuD,YAAY,GAAGxM,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;UAClD4F,YAAY,CAACtE,SAAS,GAAG,mBAAmBmE,KAAK,GAAG,CAAC,cAAcpK,OAAO,CAACuB,OAAO,EAAExB,IAAI,IAAI,SAAS,IAAIC,OAAO,CAACuI,QAAQ,EAAE;UAE3H,MAAMiC,eAAe,GAAGzM,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;UACrD6F,eAAe,CAACrD,WAAW,GAAG,IAAI,CAACyB,cAAc,CAAC5I,OAAO,CAACoC,QAAQ,CAAC;UACnEoI,eAAe,CAAC5F,KAAK,CAACkB,KAAK,GAAG,MAAM;UAEpCwE,aAAa,CAAChD,WAAW,CAACiD,YAAY,CAAC;UACvCD,aAAa,CAAChD,WAAW,CAACkD,eAAe,CAAC;UAC1CH,WAAW,CAAC/C,WAAW,CAACgD,aAAa,CAAC;UAEtC;UACA,MAAMG,YAAY,GAAG1M,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;UAClD8F,YAAY,CAAC7F,KAAK,CAACQ,OAAO,GAAG,MAAM;UACnCqF,YAAY,CAAC7F,KAAK,CAACU,UAAU,GAAG,QAAQ;UACxCmF,YAAY,CAAC7F,KAAK,CAACS,cAAc,GAAG,eAAe;UAEnD;UACA,MAAMqF,gBAAgB,GAAG3M,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;UACtD+F,gBAAgB,CAAC9F,KAAK,CAACuE,IAAI,GAAG,GAAG;UAEjC,IAAInJ,OAAO,CAACa,SAAS,EAAE;YACrB,MAAM8J,OAAO,GAAG5M,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;YAC7CgG,OAAO,CAAC/F,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCsD,OAAO,CAACxD,WAAW,GAAG,IAAIjL,IAAI,CAAC8D,OAAO,CAACa,SAAS,CAACC,IAAI,CAAC,CAACsI,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAElH,MAAMsB,UAAU,GAAG7M,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;YAChDiG,UAAU,CAAChG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClCuE,UAAU,CAACzD,WAAW,GAAG,GAAGnH,OAAO,CAACa,SAAS,CAAC4I,OAAO,EAAEX,IAAI,IAAI,KAAK,KAAK9I,OAAO,CAACa,SAAS,CAAC8I,IAAI,EAAE5J,IAAI,IAAI,KAAK,GAAG;YAEjH2K,gBAAgB,CAACpD,WAAW,CAACqD,OAAO,CAAC;YACrCD,gBAAgB,CAACpD,WAAW,CAACsD,UAAU,CAAC;;UAG1C;UACA,MAAMC,KAAK,GAAG9M,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;UAC3CkG,KAAK,CAAC5E,SAAS,GAAG,qFAAqF;UAEvG;UACA,MAAM6E,cAAc,GAAG/M,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;UACpDmG,cAAc,CAAClG,KAAK,CAACuE,IAAI,GAAG,GAAG;UAC/B2B,cAAc,CAAClG,KAAK,CAACsE,SAAS,GAAG,OAAO;UAExC,IAAIlJ,OAAO,CAACiB,OAAO,EAAE;YACnB,MAAM8J,OAAO,GAAGhN,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;YAC7CoG,OAAO,CAACnG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjC0D,OAAO,CAAC5D,WAAW,GAAG,IAAIjL,IAAI,CAAC8D,OAAO,CAACiB,OAAO,CAACH,IAAI,CAAC,CAACsI,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAEhH,MAAM0B,UAAU,GAAGjN,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;YAChDqG,UAAU,CAACpG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClC2E,UAAU,CAAC7D,WAAW,GAAG,GAAGnH,OAAO,CAACiB,OAAO,CAACwI,OAAO,EAAEX,IAAI,IAAI,KAAK,KAAK9I,OAAO,CAACiB,OAAO,CAAC0I,IAAI,EAAE5J,IAAI,IAAI,KAAK,GAAG;YAE7G+K,cAAc,CAACxD,WAAW,CAACyD,OAAO,CAAC;YACnCD,cAAc,CAACxD,WAAW,CAAC0D,UAAU,CAAC;;UAGxCP,YAAY,CAACnD,WAAW,CAACoD,gBAAgB,CAAC;UAC1CD,YAAY,CAACnD,WAAW,CAACuD,KAAK,CAAC;UAC/BJ,YAAY,CAACnD,WAAW,CAACwD,cAAc,CAAC;UACxCT,WAAW,CAAC/C,WAAW,CAACmD,YAAY,CAAC;UAErCN,YAAY,CAAC7C,WAAW,CAAC+C,WAAW,CAAC;UAErC;UACA,IAAID,KAAK,GAAGtK,IAAI,CAAChD,QAAQ,CAAC8C,MAAM,GAAG,CAAC,EAAE;YACpC,MAAMqL,OAAO,GAAGlN,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;YAC7CsG,OAAO,CAACrG,KAAK,CAACsE,SAAS,GAAG,QAAQ;YAClC+B,OAAO,CAACrG,KAAK,CAACY,OAAO,GAAG,MAAM;YAC9ByF,OAAO,CAACrG,KAAK,CAACkB,KAAK,GAAG,SAAS;YAC/BmF,OAAO,CAACrG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAE/B;YACA,MAAM6E,cAAc,GAAG,IAAIhP,IAAI,CAAC8D,OAAO,CAACiB,OAAO,EAAEH,IAAI,IAAI,CAAC,CAAC,CAACqK,OAAO,EAAE;YACrE,MAAMC,aAAa,GAAG,IAAIlP,IAAI,CAAC4D,IAAI,CAAChD,QAAQ,CAACsN,KAAK,GAAG,CAAC,CAAC,CAACvJ,SAAS,EAAEC,IAAI,IAAI,CAAC,CAAC,CAACqK,OAAO,EAAE;YACvF,MAAME,WAAW,GAAGlI,IAAI,CAACmI,KAAK,CAAC,CAACF,aAAa,GAAGF,cAAc,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;YAE9ED,OAAO,CAAChF,SAAS,GAAG,yCAAyC,IAAI,CAAC2C,cAAc,CAACyC,WAAW,CAAC,eAAerL,OAAO,CAACiB,OAAO,EAAE0I,IAAI,EAAE5J,IAAI,IAAI,iBAAiB,EAAE;YAE9JoK,YAAY,CAAC7C,WAAW,CAAC2D,OAAO,CAAC;;QAErC,CAAC,CAAC;QAEFjC,YAAY,CAAC1B,WAAW,CAAC6C,YAAY,CAAC;;;IAI1C;IACA,MAAMoB,aAAa,GAAG,IAAI,CAAC5D,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAE5D,IAAIlI,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACQ,MAAM,CAACL,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAM4L,UAAU,GAAGzN,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;MAChD6G,UAAU,CAAC5G,KAAK,CAACQ,OAAO,GAAG,MAAM;MACjCoG,UAAU,CAAC5G,KAAK,CAAC4C,aAAa,GAAG,QAAQ;MACzCgE,UAAU,CAAC5G,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE7BhI,MAAM,CAACQ,MAAM,CAACQ,OAAO,CAAC,CAACP,KAAK,EAAEkK,KAAK,KAAI;QACrC,MAAMqB,SAAS,GAAG1N,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QAC/C8G,SAAS,CAAC7G,KAAK,CAACY,OAAO,GAAG,MAAM;QAChCiG,SAAS,CAAC7G,KAAK,CAACM,eAAe,GAAG,SAAS;QAC3CuG,SAAS,CAAC7G,KAAK,CAACa,YAAY,GAAG,KAAK;QACpCgG,SAAS,CAAC7G,KAAK,CAACuB,MAAM,GAAG,gBAAgB;QAEzC;QACA,MAAMuF,WAAW,GAAG3N,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QACjD+G,WAAW,CAAC9G,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCsG,WAAW,CAAC9G,KAAK,CAACS,cAAc,GAAG,eAAe;QAClDqG,WAAW,CAAC9G,KAAK,CAACkC,YAAY,GAAG,MAAM;QACvC4E,WAAW,CAAC9G,KAAK,CAACmC,aAAa,GAAG,MAAM;QACxC2E,WAAW,CAAC9G,KAAK,CAACoC,YAAY,GAAG,gBAAgB;QAEjD,MAAM2E,UAAU,GAAG5N,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QAChDgH,UAAU,CAAC1F,SAAS,GAAG,iBAAiBmE,KAAK,GAAG,CAAC,WAAW;QAC5DuB,UAAU,CAAC/G,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAElC,MAAMuF,UAAU,GAAG7N,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QAChDiH,UAAU,CAAChH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAClCuF,UAAU,CAAChH,KAAK,CAACyC,UAAU,GAAG,MAAM;QACpCuE,UAAU,CAAChH,KAAK,CAACkB,KAAK,GAAG,SAAS;QAClC8F,UAAU,CAACzE,WAAW,GAAG,GAAGjH,KAAK,CAACyC,KAAK,CAACW,MAAM,IAAIpD,KAAK,CAACyC,KAAK,CAACxF,QAAQ,EAAE;QAExEuO,WAAW,CAACpE,WAAW,CAACqE,UAAU,CAAC;QACnCD,WAAW,CAACpE,WAAW,CAACsE,UAAU,CAAC;QACnCH,SAAS,CAACnE,WAAW,CAACoE,WAAW,CAAC;QAElC;QACA,MAAMG,YAAY,GAAG9N,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;QAClDkH,YAAY,CAACjH,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnCyG,YAAY,CAACjH,KAAK,CAACkH,mBAAmB,GAAG,uCAAuC;QAChFD,YAAY,CAACjH,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B;QACA,MAAMsE,iBAAiB,GAAG7L,KAAK,CAAC0C,YAAY,KAAKrC,SAAS,GAAGL,KAAK,CAAC0C,YAAY,GACrD1C,KAAK,CAAC2C,QAAQ,GAAG3C,KAAK,CAAC2C,QAAQ,CAACC,kBAAkB,GAAG,CAAE;QACjF,MAAMF,YAAY,GAAG,IAAI,CAAC0F,aAAa,CAAC,cAAc,EAAEyD,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,eAAe,CAAC;QAC9GF,YAAY,CAACvE,WAAW,CAAC1E,YAAY,CAAC;QAEtC;QACA,IAAI1C,KAAK,CAAC8L,SAAS,EAAE;UACnB,MAAMC,OAAO,GAAG,IAAI,CAAC3D,aAAa,CAAC,YAAY,EAAE,IAAIpM,IAAI,CAACgE,KAAK,CAAC8L,SAAS,CAAC,CAACE,cAAc,EAAE,CAAC;UAC5FL,YAAY,CAACvE,WAAW,CAAC2E,OAAO,CAAC;;QAGnC;QACA,IAAI/L,KAAK,CAACiM,WAAW,EAAE;UACrB,MAAMA,WAAW,GAAG,IAAI,CAAC7D,aAAa,CAAC,cAAc,EAAEpI,KAAK,CAACiM,WAAW,CAACpM,IAAI,CAAC;UAC9E8L,YAAY,CAACvE,WAAW,CAAC6E,WAAW,CAAC;;QAGvC;QACA,IAAIjM,KAAK,CAACkM,cAAc,EAAE;UACxB,MAAMC,UAAU,GAAG,IAAI,CAAC/D,aAAa,CAAC,YAAY,EAAEpI,KAAK,CAACkM,cAAc,CAACC,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC;UACnGR,YAAY,CAACvE,WAAW,CAAC+E,UAAU,CAAC;;QAGtC;QACA,IAAInM,KAAK,CAACoM,mBAAmB,IAAIpM,KAAK,CAACoM,mBAAmB,CAAC1M,MAAM,GAAG,CAAC,EAAE;UACrE,MAAM2M,YAAY,GAAGxO,QAAQ,CAAC4G,aAAa,CAAC,IAAI,CAAC;UACjD4H,YAAY,CAACpF,WAAW,GAAG,qBAAqB;UAChDoF,YAAY,CAAC3H,KAAK,CAAC4E,SAAS,GAAG,MAAM;UACrC+C,YAAY,CAAC3H,KAAK,CAACkC,YAAY,GAAG,MAAM;UACxCyF,YAAY,CAAC3H,KAAK,CAACyB,QAAQ,GAAG,MAAM;UACpCkG,YAAY,CAAC3H,KAAK,CAACyC,UAAU,GAAG,KAAK;UACrCoE,SAAS,CAACnE,WAAW,CAACiF,YAAY,CAAC;UAEnC,MAAMC,gBAAgB,GAAGzO,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;UACtD6H,gBAAgB,CAAC5H,KAAK,CAACQ,OAAO,GAAG,MAAM;UACvCoH,gBAAgB,CAAC5H,KAAK,CAAC4C,aAAa,GAAG,QAAQ;UAC/CgF,gBAAgB,CAAC5H,KAAK,CAAC6C,GAAG,GAAG,MAAM;UACnC+E,gBAAgB,CAAC5H,KAAK,CAACkC,YAAY,GAAG,MAAM;UAE5C;UACA,MAAM2F,cAAc,GAAGvM,KAAK,CAACoM,mBAAmB,CAAC3L,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAAC2K,WAAW,KAAK,CAAC,CAAC;UACjF,MAAMC,YAAY,GAAGzM,KAAK,CAACoM,mBAAmB,CAAC3L,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAAC2K,WAAW,KAAK,CAAC,CAAC;UAC/E,MAAME,WAAW,GAAG1M,KAAK,CAACoM,mBAAmB,CAAC3L,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAAC2K,WAAW,KAAK,CAAC,CAAC;UAE9E;UACA,IAAID,cAAc,CAAC7M,MAAM,GAAG,CAAC,EAAE;YAC7B6M,cAAc,CAAChM,OAAO,CAACoM,OAAO,IAAG;cAC/B,MAAMC,WAAW,GAAG/O,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjDmI,WAAW,CAAClI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC0H,WAAW,CAAClI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvCwH,WAAW,CAAClI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCsH,WAAW,CAAClI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C4H,WAAW,CAAClI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCqH,WAAW,CAAClI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM4G,WAAW,GAAGhP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjDoI,WAAW,CAAC9G,SAAS,GAAG,8FAA8F;cAEtH,MAAM+G,WAAW,GAAGjP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC4H,WAAW,CAACpI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMkF,WAAW,GAAG3O,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjD+H,WAAW,CAACvF,WAAW,GAAG,iBAAiB;cAC3CuF,WAAW,CAAC9H,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCqF,WAAW,CAAC9H,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM4G,cAAc,GAAGlP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACpDsI,cAAc,CAACrI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC4G,cAAc,CAACrI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAIoH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACM,MAAM,IAAIN,OAAO,CAACM,MAAM,GAAG,CAAC,EAAED,WAAW,IAAI,GAAGL,OAAO,CAACM,MAAM,KAAK;cAC/E,IAAIN,OAAO,CAACO,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIF,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACO,KAAK,WAAW;;cAE5CH,cAAc,CAAC9F,WAAW,GAAG+F,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAAC1F,WAAW,CAACoF,WAAW,CAAC;cACpCM,WAAW,CAAC1F,WAAW,CAAC2F,cAAc,CAAC;cAEvCH,WAAW,CAACxF,WAAW,CAACyF,WAAW,CAAC;cACpCD,WAAW,CAACxF,WAAW,CAAC0F,WAAW,CAAC;cACpCR,gBAAgB,CAAClF,WAAW,CAACwF,WAAW,CAAC;YAC3C,CAAC,CAAC;;UAGJ;UACA,IAAIH,YAAY,CAAC/M,MAAM,GAAG,CAAC,EAAE;YAC3B+M,YAAY,CAAClM,OAAO,CAACoM,OAAO,IAAG;cAC7B,MAAMC,WAAW,GAAG/O,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjDmI,WAAW,CAAClI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC0H,WAAW,CAAClI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvCwH,WAAW,CAAClI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCsH,WAAW,CAAClI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C4H,WAAW,CAAClI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCqH,WAAW,CAAClI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM4G,WAAW,GAAGhP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjDoI,WAAW,CAAC9G,SAAS,GAAG,+FAA+F;cAEvH,MAAM+G,WAAW,GAAGjP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC4H,WAAW,CAACpI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMkF,WAAW,GAAG3O,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjD+H,WAAW,CAACvF,WAAW,GAAG,eAAe;cACzCuF,WAAW,CAAC9H,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCqF,WAAW,CAAC9H,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM4G,cAAc,GAAGlP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACpDsI,cAAc,CAACrI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC4G,cAAc,CAACrI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAIoH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACM,MAAM,IAAIN,OAAO,CAACM,MAAM,GAAG,CAAC,EAAED,WAAW,IAAI,GAAGL,OAAO,CAACM,MAAM,KAAK;cAC/E,IAAIN,OAAO,CAACO,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIF,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACO,KAAK,WAAW;;cAE5CH,cAAc,CAAC9F,WAAW,GAAG+F,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAAC1F,WAAW,CAACoF,WAAW,CAAC;cACpCM,WAAW,CAAC1F,WAAW,CAAC2F,cAAc,CAAC;cAEvCH,WAAW,CAACxF,WAAW,CAACyF,WAAW,CAAC;cACpCD,WAAW,CAACxF,WAAW,CAAC0F,WAAW,CAAC;cACpCR,gBAAgB,CAAClF,WAAW,CAACwF,WAAW,CAAC;YAC3C,CAAC,CAAC;WACH,MAAM;YACL;YACA,MAAMA,WAAW,GAAG/O,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;YACjDmI,WAAW,CAAClI,KAAK,CAACQ,OAAO,GAAG,MAAM;YAClC0H,WAAW,CAAClI,KAAK,CAACU,UAAU,GAAG,QAAQ;YACvCwH,WAAW,CAAClI,KAAK,CAACY,OAAO,GAAG,WAAW;YACvCsH,WAAW,CAAClI,KAAK,CAACM,eAAe,GAAG,SAAS;YAC7C4H,WAAW,CAAClI,KAAK,CAACa,YAAY,GAAG,KAAK;YACtCqH,WAAW,CAAClI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;YAE9C,MAAM4G,WAAW,GAAGhP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;YACjDoI,WAAW,CAAC9G,SAAS,GAAG,+FAA+F;YAEvH,MAAM+G,WAAW,GAAGjP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;YACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;YAClC4H,WAAW,CAACpI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;YAE1C,MAAMkF,WAAW,GAAG3O,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;YACjD+H,WAAW,CAACvF,WAAW,GAAG,eAAe;YACzCuF,WAAW,CAAC9H,KAAK,CAACyC,UAAU,GAAG,KAAK;YACpCqF,WAAW,CAAC9H,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAEnC,MAAM4G,cAAc,GAAGlP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;YACpDsI,cAAc,CAACrI,KAAK,CAACyB,QAAQ,GAAG,MAAM;YACtC4G,cAAc,CAACrI,KAAK,CAACkB,KAAK,GAAG,MAAM;YACnCmH,cAAc,CAAC9F,WAAW,GAAG,UAAU;YAEvC6F,WAAW,CAAC1F,WAAW,CAACoF,WAAW,CAAC;YACpCM,WAAW,CAAC1F,WAAW,CAAC2F,cAAc,CAAC;YAEvCH,WAAW,CAACxF,WAAW,CAACyF,WAAW,CAAC;YACpCD,WAAW,CAACxF,WAAW,CAAC0F,WAAW,CAAC;YACpCR,gBAAgB,CAAClF,WAAW,CAACwF,WAAW,CAAC;;UAG3C;UACA,IAAIF,WAAW,CAAChN,MAAM,GAAG,CAAC,EAAE;YAC1BgN,WAAW,CAACnM,OAAO,CAACoM,OAAO,IAAG;cAC5B,MAAMC,WAAW,GAAG/O,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjDmI,WAAW,CAAClI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC0H,WAAW,CAAClI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvCwH,WAAW,CAAClI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCsH,WAAW,CAAClI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C4H,WAAW,CAAClI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCqH,WAAW,CAAClI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM4G,WAAW,GAAGhP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjDoI,WAAW,CAAC9G,SAAS,GAAG,kGAAkG;cAE1H,MAAM+G,WAAW,GAAGjP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC4H,WAAW,CAACpI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMkF,WAAW,GAAG3O,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACjD+H,WAAW,CAACvF,WAAW,GAAG,cAAc;cACxCuF,WAAW,CAAC9H,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCqF,WAAW,CAAC9H,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM4G,cAAc,GAAGlP,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;cACpDsI,cAAc,CAACrI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC4G,cAAc,CAACrI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAIoH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACM,MAAM,IAAIN,OAAO,CAACM,MAAM,GAAG,CAAC,EAAED,WAAW,IAAI,GAAGL,OAAO,CAACM,MAAM,KAAK;cAC/E,IAAIN,OAAO,CAACO,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIF,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACO,KAAK,WAAW;;cAE5CH,cAAc,CAAC9F,WAAW,GAAG+F,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAAC1F,WAAW,CAACoF,WAAW,CAAC;cACpCM,WAAW,CAAC1F,WAAW,CAAC2F,cAAc,CAAC;cAEvCH,WAAW,CAACxF,WAAW,CAACyF,WAAW,CAAC;cACpCD,WAAW,CAACxF,WAAW,CAAC0F,WAAW,CAAC;cACpCR,gBAAgB,CAAClF,WAAW,CAACwF,WAAW,CAAC;YAC3C,CAAC,CAAC;;UAGJrB,SAAS,CAACnE,WAAW,CAACkF,gBAAgB,CAAC;;QAGzCf,SAAS,CAACnE,WAAW,CAACuE,YAAY,CAAC;QAEnCL,UAAU,CAAClE,WAAW,CAACmE,SAAS,CAAC;MACnC,CAAC,CAAC;MAEFF,aAAa,CAACjE,WAAW,CAACkE,UAAU,CAAC;;IAGvC;IACA;IACA,MAAM6B,UAAU,GAAG5N,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IAClD,MAAM2N,QAAQ,GAAGD,UAAU;IACzB;IACCA,UAAkB,CAACC,QAAQ,IAC3BD,UAAU,CAACvQ,QAAQ,IAAIuQ,UAAU,CAACvQ,QAAQ,CAAC,CAAC,CAAC,IAAIuQ,UAAU,CAACvQ,QAAQ,CAAC,CAAC,CAAC,CAACwQ,QAAS,CACnF;IAED,IAAIA,QAAQ,IAAIA,QAAQ,CAAC1N,MAAM,GAAG,CAAC,EAAE;MACnC,MAAM2N,eAAe,GAAG,IAAI,CAAC5F,aAAa,CAAC,UAAU,EAAE,mBAAmB,CAAC;MAE3E,MAAM6F,YAAY,GAAGzP,QAAQ,CAAC4G,aAAa,CAAC,IAAI,CAAC;MACjD6I,YAAY,CAAC5I,KAAK,CAAC6I,SAAS,GAAG,MAAM;MACrCD,YAAY,CAAC5I,KAAK,CAACY,OAAO,GAAG,GAAG;MAChCgI,YAAY,CAAC5I,KAAK,CAACwC,MAAM,GAAG,GAAG;MAC/BoG,YAAY,CAAC5I,KAAK,CAACQ,OAAO,GAAG,MAAM;MACnCoI,YAAY,CAAC5I,KAAK,CAACkH,mBAAmB,GAAG,uCAAuC;MAChF0B,YAAY,CAAC5I,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE/B6F,QAAQ,CAAC7M,OAAO,CAAEiN,OAAY,IAAI;QAChC,MAAMC,WAAW,GAAG5P,QAAQ,CAAC4G,aAAa,CAAC,IAAI,CAAC;QAChDgJ,WAAW,CAAC/I,KAAK,CAACY,OAAO,GAAG,MAAM;QAClCmI,WAAW,CAAC/I,KAAK,CAACM,eAAe,GAAG,SAAS;QAC7CyI,WAAW,CAAC/I,KAAK,CAACa,YAAY,GAAG,KAAK;QACtCkI,WAAW,CAAC1H,SAAS,GAAG,2EAA2EyH,OAAO,CAAC3N,IAAI,IAAI,SAAS,EAAE;QAC9HyN,YAAY,CAAClG,WAAW,CAACqG,WAAW,CAAC;MACvC,CAAC,CAAC;MAEFJ,eAAe,CAACjG,WAAW,CAACkG,YAAY,CAAC;MACzCjG,gBAAgB,CAACD,WAAW,CAACiG,eAAe,CAAC;;IAG/C;IACAhG,gBAAgB,CAACD,WAAW,CAACI,WAAW,CAAC;IACzCH,gBAAgB,CAACD,WAAW,CAAC0B,YAAY,CAAC;IAC1CzB,gBAAgB,CAACD,WAAW,CAACiE,aAAa,CAAC;IAE3C;IACAhG,YAAY,CAAC+B,WAAW,CAACtB,WAAW,CAAC;IACrCT,YAAY,CAAC+B,WAAW,CAACT,MAAM,CAAC;IAChCtB,YAAY,CAAC+B,WAAW,CAACC,gBAAgB,CAAC;IAC1C7C,QAAQ,CAAC4C,WAAW,CAAC/B,YAAY,CAAC;IAElC;IACAxH,QAAQ,CAAC4I,IAAI,CAACW,WAAW,CAAC5C,QAAQ,CAAC;EACrC;EAEA;EACQiD,aAAaA,CAACT,KAAa,EAAE0G,SAAiB;IACpD,MAAM5J,OAAO,GAAGjG,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;IAC7CX,OAAO,CAACY,KAAK,CAACM,eAAe,GAAG,SAAS;IACzClB,OAAO,CAACY,KAAK,CAACa,YAAY,GAAG,KAAK;IAClCzB,OAAO,CAACY,KAAK,CAACY,OAAO,GAAG,MAAM;IAC9BxB,OAAO,CAACY,KAAK,CAACiB,SAAS,GAAG,+BAA+B;IAEzD,MAAMgI,aAAa,GAAG9P,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;IACnDkJ,aAAa,CAACjJ,KAAK,CAACQ,OAAO,GAAG,MAAM;IACpCyI,aAAa,CAACjJ,KAAK,CAACU,UAAU,GAAG,QAAQ;IACzCuI,aAAa,CAACjJ,KAAK,CAACkC,YAAY,GAAG,MAAM;IAEzC,MAAMjM,IAAI,GAAGkD,QAAQ,CAAC4G,aAAa,CAAC,GAAG,CAAC;IACxC9J,IAAI,CAACiT,SAAS,GAAG,OAAOF,SAAS,EAAE;IACnC/S,IAAI,CAAC+J,KAAK,CAACkB,KAAK,GAAG,SAAS;IAC5BjL,IAAI,CAAC+J,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC5BxL,IAAI,CAAC+J,KAAK,CAACqD,WAAW,GAAG,MAAM;IAE/B,MAAM8F,YAAY,GAAGhQ,QAAQ,CAAC4G,aAAa,CAAC,IAAI,CAAC;IACjDoJ,YAAY,CAAC5G,WAAW,GAAGD,KAAK;IAChC6G,YAAY,CAACnJ,KAAK,CAACwC,MAAM,GAAG,GAAG;IAC/B2G,YAAY,CAACnJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpC0H,YAAY,CAACnJ,KAAK,CAACyC,UAAU,GAAG,KAAK;IAErCwG,aAAa,CAACvG,WAAW,CAACzM,IAAI,CAAC;IAC/BgT,aAAa,CAACvG,WAAW,CAACyG,YAAY,CAAC;IACvC/J,OAAO,CAACsD,WAAW,CAACuG,aAAa,CAAC;IAElC,OAAO7J,OAAO;EAChB;EAEA;EACQsE,aAAaA,CAAC1N,KAAa,EAAED,KAAa;IAChD,MAAMqT,GAAG,GAAGjQ,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;IACzCqJ,GAAG,CAACpJ,KAAK,CAACkC,YAAY,GAAG,MAAM;IAE/B,MAAMmH,YAAY,GAAGlQ,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;IAClDsJ,YAAY,CAAC9G,WAAW,GAAGvM,KAAK;IAChCqT,YAAY,CAACrJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpC4H,YAAY,CAACrJ,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCmI,YAAY,CAACrJ,KAAK,CAACkC,YAAY,GAAG,KAAK;IAEvC,MAAMoH,YAAY,GAAGnQ,QAAQ,CAAC4G,aAAa,CAAC,KAAK,CAAC;IAClDuJ,YAAY,CAAC/G,WAAW,GAAGxM,KAAK;IAChCuT,YAAY,CAACtJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAEpC2H,GAAG,CAAC1G,WAAW,CAAC2G,YAAY,CAAC;IAC7BD,GAAG,CAAC1G,WAAW,CAAC4G,YAAY,CAAC;IAE7B,OAAOF,GAAG;EACZ;EAEA;EACAG,gBAAgBA,CAAC1O,MAAoB;IACnC,IAAIA,MAAM,IAAIA,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACQ,MAAM,CAACL,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAIwO,OAAO,GAAG3O,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACmO,OAAO,IAAI3O,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACJ,EAAE;MAE7D;MACA,IAAIwO,QAAQ,GAAG,IAAI,CAACtU,YAAY;MAEhC;MACA,IAAI,CAACsU,QAAQ,IAAI5O,MAAM,CAACI,EAAE,EAAE;QAC1BwO,QAAQ,GAAG5O,MAAM,CAACI,EAAE;OACrB,MAAM,IAAI,CAACwO,QAAQ,EAAE;QACpB;QACAA,QAAQ,GAAG,mBAAmB;;MAGhC;MACA,MAAMhU,eAAe,GAAG,IAAI,CAACd,iBAAiB,CAAC+U,kBAAkB,EAAE;MAEnE;MACA,MAAMC,aAAa,GAAGC,IAAI,CAACC,SAAS,CAACpU,eAAe,CAAC;MAErD8E,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEiP,QAAQ,EAAE,UAAU,EAAED,OAAO,EAAE,iBAAiB,EAAEG,aAAa,CAAC;MAEtH;MACA,IAAI,CAACjV,MAAM,CAACoV,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UACXN,QAAQ,EAAEA,QAAQ;UAClBD,OAAO,EAAEA,OAAO;UAChBQ,UAAU,EAAEL;;OAEf,CAAC;KACH,MAAM;MACLpP,OAAO,CAAC0P,KAAK,CAAC,sCAAsC,EAAEpP,MAAM,CAAC;;EAEjE;EAEAP,gBAAgBA,CAAA;IACd;IACA;IACA;EAAA;EAGFD,iBAAiBA,CAAA;IACf;IACA,MAAM6P,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACjC,MAAMC,mBAAmB,GAAG,CAAC,CAAC,CAAG;IAEjC,IAAI,CAAC1V,cAAc,CAAC2V,kBAAkB,CAACF,qBAAqB,CAAC,CAACG,SAAS,CAACC,SAAS,IAAG;MAClF,IAAI,CAAC1V,kBAAkB,GAAG0V,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAAC7V,cAAc,CAAC2V,kBAAkB,CAACD,mBAAmB,CAAC,CAACE,SAAS,CAACC,SAAS,IAAG;MAChF,IAAI,CAACzV,gBAAgB,GAAGyV,SAAS;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC7S,UAAU,CAACiE,GAAG,CAAC,mBAAmB,CAAC,EAAE6O,YAAY,CACnDC,IAAI,CACH1W,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC+B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B;QACA,IAAIA,KAAK,CAACiF,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACvG,cAAc,CAAC2V,kBAAkB,CAACF,qBAAqB,CAAC,CAACM,IAAI,CACvEvW,GAAG,CAACqW,SAAS,IAAIA,SAAS,CAACvO,MAAM,CAAC0O,QAAQ,IACxCA,QAAQ,CAACtP,IAAI,CAACuP,WAAW,EAAE,CAAC9N,QAAQ,CAAC7G,KAAK,CAAC2U,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACvG,IAAI,IAAIuG,QAAQ,CAACvG,IAAI,CAACwG,WAAW,EAAE,CAAC9N,QAAQ,CAAC7G,KAAK,CAAC2U,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAACjW,cAAc,CAAC2V,kBAAkB,CAACF,qBAAqB,CAAC;;;MAGxE,OAAOhW,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAmW,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAAC1V,kBAAkB,GAAG0V,SAAS;IACrC,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC7S,UAAU,CAACiE,GAAG,CAAC,iBAAiB,CAAC,EAAE6O,YAAY,CACjDC,IAAI,CACH1W,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC+B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B;QACA,IAAIA,KAAK,CAACiF,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACvG,cAAc,CAAC2V,kBAAkB,CAACD,mBAAmB,CAAC,CAACK,IAAI,CACrEvW,GAAG,CAACqW,SAAS,IAAIA,SAAS,CAACvO,MAAM,CAAC0O,QAAQ,IACxCA,QAAQ,CAACtP,IAAI,CAACuP,WAAW,EAAE,CAAC9N,QAAQ,CAAC7G,KAAK,CAAC2U,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACvG,IAAI,IAAIuG,QAAQ,CAACvG,IAAI,CAACwG,WAAW,EAAE,CAAC9N,QAAQ,CAAC7G,KAAK,CAAC2U,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAACjW,cAAc,CAAC2V,kBAAkB,CAACD,mBAAmB,CAAC;;;MAGtE,OAAOjW,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAmW,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACzV,gBAAgB,GAAGyV,SAAS;IACnC,CAAC,CAAC;EACN;EAEAK,eAAeA,CAACF,QAA+B;IAC7C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAIG,WAAW,GAAGH,QAAQ,CAACtP,IAAI;IAC/B,IAAIsP,QAAQ,CAACvG,IAAI,EAAE;MACjB0G,WAAW,IAAI,KAAKH,QAAQ,CAACvG,IAAI,GAAG;;IAEtC,IAAIuG,QAAQ,CAAC/Q,IAAI,KAAKtF,YAAY,CAACyW,OAAO,IAAIJ,QAAQ,CAAC1F,IAAI,EAAE;MAC3D6F,WAAW,IAAI,MAAMH,QAAQ,CAAC1F,IAAI,EAAE;;IAEtC,OAAO6F,WAAW;EACpB;EAEA;EACAE,aAAaA,CAACpR,IAAY;IACxB,IAAI,CAACnE,iBAAiB,GAAGmE,IAAI;IAE7B;IACA,IAAIA,IAAI,KAAK,IAAI,CAACtE,mBAAmB,EAAE;MACrC,IAAI,CAACqC,UAAU,CAACiE,GAAG,CAAC,cAAc,CAAC,EAAEqP,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MACpD;MACA,IAAI,CAACtT,UAAU,CAACiE,GAAG,CAAC,YAAY,CAAC,EAAEsP,eAAe,EAAE;MAEpD;MACA,IAAI,CAACC,gBAAgB,EAAE,CAACC,KAAK,EAAE;KAChC,MAAM,IAAIxR,IAAI,KAAK,IAAI,CAACrE,sBAAsB,EAAE;MAC/C,IAAI,CAACoC,UAAU,CAACiE,GAAG,CAAC,cAAc,CAAC,EAAEqP,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MACpD;MACA,IAAI,CAACtT,UAAU,CAACiE,GAAG,CAAC,YAAY,CAAC,EAAEyP,aAAa,CAAC,CAACtX,UAAU,CAAC+D,QAAQ,CAAC,CAAC;MAEvE;MACA,IAAI,CAACqT,gBAAgB,EAAE,CAACC,KAAK,EAAE;KAChC,MAAM,IAAIxR,IAAI,KAAK,IAAI,CAACpE,sBAAsB,EAAE;MAC/C,IAAI,CAACmC,UAAU,CAACiE,GAAG,CAAC,cAAc,CAAC,EAAEqP,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MACpD;MACA,IAAI,CAACtT,UAAU,CAACiE,GAAG,CAAC,YAAY,CAAC,EAAEsP,eAAe,EAAE;MAEpD;MACA,IAAI,IAAI,CAACC,gBAAgB,EAAE,CAACjQ,MAAM,KAAK,CAAC,EAAE;QACxC,IAAI,CAACoQ,UAAU,EAAE;;;IAIrB;IACA,IAAI,CAAC3T,UAAU,CAACiE,GAAG,CAAC,YAAY,CAAC,EAAE2P,sBAAsB,EAAE;EAC7D;EAEA;EACAJ,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACxT,UAAU,CAACiE,GAAG,CAAC,UAAU,CAAc;EACrD;EAEA;EACA4P,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC9W,EAAE,CAACkD,KAAK,CAAC;MACnBI,iBAAiB,EAAE,CAAC,EAAE,EAAEjE,UAAU,CAAC+D,QAAQ,CAAC;MAC5CG,eAAe,EAAE,CAAC,EAAE,EAAElE,UAAU,CAAC+D,QAAQ,CAAC;MAC1CI,aAAa,EAAE,CAAC,IAAI,CAACX,OAAO,EAAExD,UAAU,CAAC+D,QAAQ;KAClD,CAAC;EACJ;EAEA;EACAwT,UAAUA,CAAA;IACR;IACA,MAAMG,yBAAyB,GAAG,IAAI,CAAC9T,UAAU,CAACiE,GAAG,CAAC,mBAAmB,CAAC,EAAE3F,KAAK;IACjF,MAAMyV,uBAAuB,GAAG,IAAI,CAAC/T,UAAU,CAACiE,GAAG,CAAC,iBAAiB,CAAC,EAAE3F,KAAK;IAC7E,MAAM0V,qBAAqB,GAAG,IAAI,CAAChU,UAAU,CAACiE,GAAG,CAAC,eAAe,CAAC,EAAE3F,KAAK;IAEzE;IACA,MAAM2V,UAAU,GAAG,IAAI,CAAClX,EAAE,CAACkD,KAAK,CAAC;MAC/BI,iBAAiB,EAAE,CAACyT,yBAAyB,EAAE1X,UAAU,CAAC+D,QAAQ,CAAC;MACnEG,eAAe,EAAE,CAACyT,uBAAuB,EAAE3X,UAAU,CAAC+D,QAAQ,CAAC;MAC/DI,aAAa,EAAE,CAACyT,qBAAqB,EAAE5X,UAAU,CAAC+D,QAAQ;KAC3D,CAAC;IAEF;IACA,IAAI,CAACqT,gBAAgB,EAAE,CAACU,IAAI,CAACD,UAAU,CAAC;EAC1C;EAEA;EACAE,aAAaA,CAACpG,KAAa;IACzB;IACA,IAAI,IAAI,CAACyF,gBAAgB,EAAE,CAACjQ,MAAM,GAAG,CAAC,EAAE;MACtC,IAAI,CAACiQ,gBAAgB,EAAE,CAACY,QAAQ,CAACrG,KAAK,CAAC;;EAE3C;EAEAsG,QAAQA,CAAA;IACN,IAAI,IAAI,CAACrU,UAAU,CAACsU,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACvU,UAAU,CAAC;MAC1C;;IAGF,IAAI,CAAC3C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACI,YAAY,GAAG,EAAE;IACtB,IAAI,CAACD,WAAW,GAAG,IAAI;IAEvB,MAAMgX,SAAS,GAAG,IAAI,CAACxU,UAAU,CAAC1B,KAAK;IAEvC;IACAwE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEyR,SAAS,CAAC;IACtC1R,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyR,SAAS,CAAC7T,WAAW,CAAC;IAC5DmC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACjF,iBAAiB,CAAC;IAE3D;IACA,IAAI2W,OAAY;IAEhB,IAAI,IAAI,CAAC3W,iBAAiB,KAAK,IAAI,CAACH,mBAAmB,EAAE;MACvD;MACA8W,OAAO,GAAG;QACRC,WAAW,EAAEF,SAAS,CAACtU,WAAW;QAClCyU,YAAY,EAAE,CAAC,GAAG,CAAC;QACnBC,OAAO,EAAEJ,SAAS,CAACjU,aAAa;QAChCsU,kBAAkB,EAAE,CAClB;UACErR,EAAE,EAAEgR,SAAS,CAACnU,iBAAiB,EAAEmD,EAAE,IAAI,EAAE;UACzCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD6S,gBAAgB,EAAE,CAChB;UACEtR,EAAE,EAAEgR,SAAS,CAAClU,eAAe,EAAEkD,EAAE,IAAI,EAAE;UACvCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD8S,UAAU,EAAE,IAAI,CAACrS,kBAAkB,EAAE;QACrCsS,qBAAqB,EAAER,SAAS,CAAC5T,OAAO;QACxCqU,oBAAoB,EAAE;UACpBC,oBAAoB,EAAE;YACpB/T,sBAAsB,EAAEqT,SAAS,CAACrT;;SAErC;QACDJ,sBAAsB,EAAEyT,SAAS,CAACzT,sBAAsB;QACxDC,wBAAwB,EAAEwT,SAAS,CAACxT,wBAAwB;QAC5DC,6BAA6B,EAAEuT,SAAS,CAACvT,6BAA6B;QACtEC,mBAAmB,EAAEsT,SAAS,CAACtT,mBAAmB;QAClD1B,aAAa,EAAE,CAACgV,SAAS,CAAC7T,WAAW,CAAC;QACtCwU,OAAO,EAAEX,SAAS,CAAC3T,OAAO;QAC1BuU,QAAQ,EAAEZ,SAAS,CAAC1T;OACrB;MAEDgC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE0R,OAAO,CAACE,YAAY,CAAC;KACvF,MAAM,IAAI,IAAI,CAAC7W,iBAAiB,KAAK,IAAI,CAACF,sBAAsB,EAAE;MACjE;MACA6W,OAAO,GAAG;QACRC,WAAW,EAAEF,SAAS,CAACtU,WAAW;QAClCyU,YAAY,EAAE,CAAC,GAAG,CAAC;QACnBC,OAAO,EAAEJ,SAAS,CAACjU,aAAa;QAChC8U,UAAU,EAAEb,SAAS,CAAChU,UAAU;QAChCqU,kBAAkB,EAAE,CAClB;UACErR,EAAE,EAAEgR,SAAS,CAACnU,iBAAiB,EAAEmD,EAAE,IAAI,EAAE;UACzCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD6S,gBAAgB,EAAE,CAChB;UACEtR,EAAE,EAAEgR,SAAS,CAAClU,eAAe,EAAEkD,EAAE,IAAI,EAAE;UACvCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD8S,UAAU,EAAE,IAAI,CAACrS,kBAAkB,EAAE;QACrCsS,qBAAqB,EAAER,SAAS,CAAC5T,OAAO;QACxCqU,oBAAoB,EAAE;UACpBC,oBAAoB,EAAE;YACpB/T,sBAAsB,EAAEqT,SAAS,CAACrT;;SAErC;QACDJ,sBAAsB,EAAEyT,SAAS,CAACzT,sBAAsB;QACxDC,wBAAwB,EAAEwT,SAAS,CAACxT,wBAAwB;QAC5DC,6BAA6B,EAAEuT,SAAS,CAACvT,6BAA6B;QACtEC,mBAAmB,EAAEsT,SAAS,CAACtT,mBAAmB;QAClD1B,aAAa,EAAE,CAACgV,SAAS,CAAC7T,WAAW,CAAC;QACtCwU,OAAO,EAAEX,SAAS,CAAC3T,OAAO;QAC1BuU,QAAQ,EAAEZ,SAAS,CAAC1T;OACrB;MAEDgC,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE0R,OAAO,CAACE,YAAY,CAAC;;IAG3F;IACA,IAAI,IAAI,CAAC7W,iBAAiB,KAAK,IAAI,CAACH,mBAAmB,EAAE;MACvD;MACA8W,OAAO,GAAG;QACRC,WAAW,EAAEF,SAAS,CAACtU,WAAW;QAClCyU,YAAY,EAAE,CAAC,GAAG,CAAC;QACnBC,OAAO,EAAEJ,SAAS,CAACjU,aAAa;QAChCsU,kBAAkB,EAAE,CAClB;UACErR,EAAE,EAAEgR,SAAS,CAACnU,iBAAiB,EAAEmD,EAAE,IAAI,EAAE;UACzCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD6S,gBAAgB,EAAE,CAChB;UACEtR,EAAE,EAAEgR,SAAS,CAAClU,eAAe,EAAEkD,EAAE,IAAI,EAAE;UACvCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD8S,UAAU,EAAE,IAAI,CAACrS,kBAAkB,EAAE;QACrCsS,qBAAqB,EAAER,SAAS,CAAC5T,OAAO;QACxCqU,oBAAoB,EAAE;UACpBC,oBAAoB,EAAE;YACpB/T,sBAAsB,EAAEqT,SAAS,CAACrT;;SAErC;QACDJ,sBAAsB,EAAEyT,SAAS,CAACzT,sBAAsB;QACxDC,wBAAwB,EAAEwT,SAAS,CAACxT,wBAAwB;QAC5DC,6BAA6B,EAAEuT,SAAS,CAACvT,6BAA6B;QACtEC,mBAAmB,EAAEsT,SAAS,CAACtT,mBAAmB;QAClD1B,aAAa,EAAE,CAACgV,SAAS,CAAC7T,WAAW,CAAC;QACtCwU,OAAO,EAAEX,SAAS,CAAC3T,OAAO;QAC1BuU,QAAQ,EAAEZ,SAAS,CAAC1T;OACrB;MAEDgC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE0R,OAAO,CAACE,YAAY,CAAC;KACvF,MAAM,IAAI,IAAI,CAAC7W,iBAAiB,KAAK,IAAI,CAACF,sBAAsB,EAAE;MACjE;MACA;MACA,MAAM2C,aAAa,GAAG,IAAIV,IAAI,CAAC2U,SAAS,CAACjU,aAAa,CAAC;MACvD,MAAMC,UAAU,GAAG,IAAIX,IAAI,CAAC2U,SAAS,CAAChU,UAAU,CAAC;MAEjD;MACA,MAAM8U,QAAQ,GAAGxO,IAAI,CAACyO,GAAG,CAAC/U,UAAU,CAACsO,OAAO,EAAE,GAAGvO,aAAa,CAACuO,OAAO,EAAE,CAAC;MACzE,MAAM0G,QAAQ,GAAG1O,IAAI,CAAC2O,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAE5DxS,OAAO,CAACC,GAAG,CAAC,sBAAsByS,QAAQ,sCAAsC,CAAC;MAEjFf,OAAO,GAAG;QACRC,WAAW,EAAEF,SAAS,CAACtU,WAAW;QAClCyU,YAAY,EAAE,CAAC,GAAG,CAAC;QACnBC,OAAO,EAAEJ,SAAS,CAACjU,aAAa;QAChCmV,KAAK,EAAEF,QAAQ;QACfX,kBAAkB,EAAE,CAClB;UACErR,EAAE,EAAEgR,SAAS,CAACnU,iBAAiB,EAAEmD,EAAE,IAAI,EAAE;UACzCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD6S,gBAAgB,EAAE,CAChB;UACEtR,EAAE,EAAEgR,SAAS,CAAClU,eAAe,EAAEkD,EAAE,IAAI,EAAE;UACvCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD8S,UAAU,EAAE,IAAI,CAACrS,kBAAkB,EAAE;QACrCsS,qBAAqB,EAAER,SAAS,CAAC5T,OAAO;QACxCqU,oBAAoB,EAAE;UACpBC,oBAAoB,EAAE;YACpB/T,sBAAsB,EAAEqT,SAAS,CAACrT;;SAErC;QACDJ,sBAAsB,EAAEyT,SAAS,CAACzT,sBAAsB;QACxDC,wBAAwB,EAAEwT,SAAS,CAACxT,wBAAwB;QAC5DC,6BAA6B,EAAEuT,SAAS,CAACvT,6BAA6B;QACtEC,mBAAmB,EAAEsT,SAAS,CAACtT,mBAAmB;QAClD1B,aAAa,EAAE,CAACgV,SAAS,CAAC7T,WAAW,CAAC;QACtCwU,OAAO,EAAEX,SAAS,CAAC3T,OAAO;QAC1BuU,QAAQ,EAAEZ,SAAS,CAAC1T;OACrB;MAEDgC,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE0R,OAAO,CAACE,YAAY,CAAC;KAC1F,MAAM,IAAI,IAAI,CAAC7W,iBAAiB,KAAK,IAAI,CAACD,sBAAsB,EAAE;MACjE;MACA,MAAM4C,QAAQ,GAAG,IAAI,CAAC+S,gBAAgB,EAAE,CAAClV,KAAK;MAE9C;MACA,MAAMqX,cAAc,GAAGlV,QAAQ,CAACjE,GAAG,CAAEmH,OAAY,KAAM;QACrDiR,OAAO,EAAEjR,OAAO,CAACpD,aAAa;QAC9BsU,kBAAkB,EAAE,CAClB;UACErR,EAAE,EAAEG,OAAO,CAACtD,iBAAiB,EAAEmD,EAAE,IAAI,EAAE;UACvCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD6S,gBAAgB,EAAE,CAChB;UACEtR,EAAE,EAAEG,OAAO,CAACrD,eAAe,EAAEkD,EAAE,IAAI,EAAE;UACrCvB,IAAI,EAAE,CAAC,CAAC;SACT;OAEJ,CAAC,CAAC;MAEH;MACA,MAAM2T,QAAQ,GAAGnV,QAAQ,CAACjE,GAAG,CAAEmH,OAAY,IAAKA,OAAO,CAACpD,aAAa,CAAC;MAEtE;MACA,IAAIoV,cAAc,CAACpS,MAAM,KAAK,CAAC,EAAE;QAC/BoS,cAAc,CAACzB,IAAI,CAAC;UAClBU,OAAO,EAAEJ,SAAS,CAACjU,aAAa;UAChCsU,kBAAkB,EAAE,CAClB;YACErR,EAAE,EAAEgR,SAAS,CAACnU,iBAAiB,EAAEmD,EAAE,IAAI,EAAE;YACzCvB,IAAI,EAAE,CAAC,CAAC;WACT,CACF;;UACD6S,gBAAgB,EAAE,CAChB;YACEtR,EAAE,EAAEgR,SAAS,CAAClU,eAAe,EAAEkD,EAAE,IAAI,EAAE;YACvCvB,IAAI,EAAE,CAAC,CAAC;WACT;SAEJ,CAAC;;QAEF2T,QAAQ,CAAC1B,IAAI,CAACM,SAAS,CAACjU,aAAa,CAAC;;MAGxCuC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE4S,cAAc,CAAC;MACnD7S,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE6S,QAAQ,CAAC;MAE7CnB,OAAO,GAAG;QACRC,WAAW,EAAEF,SAAS,CAACtU,WAAW;QAClCyU,YAAY,EAAE,CAAC,GAAG,CAAC;QACnBkB,cAAc,EAAEF,cAAc;QAC9B;QACAf,OAAO,EAAEgB,QAAQ,CAAC,CAAC,CAAC;QACpBE,QAAQ,EAAEF,QAAQ;QAClBb,UAAU,EAAE,IAAI,CAACrS,kBAAkB,EAAE;QACrCsS,qBAAqB,EAAER,SAAS,CAAC5T,OAAO;QACxCqU,oBAAoB,EAAE;UACpBC,oBAAoB,EAAE;YACpB/T,sBAAsB,EAAEqT,SAAS,CAACrT;;SAErC;QACDJ,sBAAsB,EAAEyT,SAAS,CAACzT,sBAAsB;QACxDC,wBAAwB,EAAEwT,SAAS,CAACxT,wBAAwB;QAC5DC,6BAA6B,EAAEuT,SAAS,CAACvT,6BAA6B;QACtEC,mBAAmB,EAAEsT,SAAS,CAACtT,mBAAmB;QAClD1B,aAAa,EAAE,CAACgV,SAAS,CAAC7T,WAAW,CAAC;QACtCwU,OAAO,EAAEX,SAAS,CAAC3T,OAAO;QAC1BuU,QAAQ,EAAEZ,SAAS,CAAC1T;OACrB;MAEDgC,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE0R,OAAO,CAACE,YAAY,CAAC;;IAG3F;IACA,IAAI,CAAC3X,cAAc,CAAC+Y,WAAW,CAACtB,OAAO,CAAC,CACnC7B,SAAS,CAAC;MACToD,IAAI,EAAGC,QAAwB,IAAI;QACnC,IAAI,CAAC5Y,SAAS,GAAG,KAAK;QACtB,IAAI4Y,QAAQ,CAACzL,MAAM,CAAC0L,OAAO,EAAE;UAC3B,IAAI,CAAC5Y,aAAa,GAAG2Y,QAAQ,CAAC3L,IAAI,CAAC/E,OAAO;UAE1C;UACAzC,OAAO,CAAC7C,KAAK,CAAC,uBAAuB,CAAC;UACtC6C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyR,SAAS,CAAC7T,WAAW,CAAC;UAC5DmC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkT,QAAQ,CAAC3L,IAAI,CAAC/E,OAAO,CAAChC,MAAM,CAAC;UACpE,IAAI4S,YAAY,GAAG,SAAS;UAC5B,IAAI,IAAI,CAACrY,iBAAiB,KAAK,IAAI,CAACF,sBAAsB,EAAE;YAC1DuY,YAAY,GAAG,YAAY;WAC5B,MAAM,IAAI,IAAI,CAACrY,iBAAiB,KAAK,IAAI,CAACD,sBAAsB,EAAE;YACjEsY,YAAY,GAAG,YAAY;;UAE7BrT,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEoT,YAAY,CAAC;UAE3C;UACA,MAAMC,cAAc,GAA2B;YAC7C,CAAC1Z,eAAe,CAAC+C,KAAK,CAAC4W,QAAQ,EAAE,GAAG,CAAC;YACrC,CAAC3Z,eAAe,CAACgD,OAAO,CAAC2W,QAAQ,EAAE,GAAG,CAAC;YACvC,CAAC3Z,eAAe,CAACiD,QAAQ,CAAC0W,QAAQ,EAAE,GAAG,CAAC;YACxC,SAAS,EAAE;WACZ;UAEDJ,QAAQ,CAAC3L,IAAI,CAAC/E,OAAO,CAACnB,OAAO,CAAC,CAAChB,MAAM,EAAE2K,KAAK,KAAI;YAC9C,IAAI3K,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,IAAIH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,EAAE;cAC1E,MAAM2V,SAAS,GAAGlT,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,CAACsB,IAAI,CAACoU,QAAQ,EAAE;cAC7D,IAAID,cAAc,CAACE,SAAS,CAAC,KAAKpS,SAAS,EAAE;gBAC3CkS,cAAc,CAACE,SAAS,CAAC,EAAE;eAC5B,MAAM;gBACLF,cAAc,CAAC,SAAS,CAAC,EAAE;;cAG7B;cACAtT,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,SAAS,EACtCJ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,GAC3B,GAAGyC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,CAAC+C,IAAI,WAAWN,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,CAACsB,IAAI,GAAG,GACjF,eAAe,CAAC;cAElB;cACA,IAAI8L,KAAK,KAAK,CAAC,EAAE;gBACfjL,OAAO,CAAC7C,KAAK,CAAC,wBAAwB,CAAC;gBACvC6C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEK,MAAM,CAACI,EAAE,CAAC;gBACpCV,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEK,MAAM,CAACE,KAAK,CAAC;gBAE1C,IAAIF,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;kBAC3CT,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEK,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,CAAC;kBAEnE,IAAIyC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,EAAE;oBAC5BqC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAAC;oBAClD2C,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAAC2D,OAAO,CAAC,CAACT,OAAO,EAAE4S,QAAQ,KAAI;sBACrDzT,OAAO,CAACC,GAAG,CAAC,WAAWwT,QAAQ,eAAe,EAAE5S,OAAO,CAAChD,WAAW,CAAC;oBACtE,CAAC,CAAC;;;gBAIN,IAAIyC,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACQ,MAAM,CAACL,MAAM,GAAG,CAAC,EAAE;kBAC7CT,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEK,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC,CAAC;kBAC7Cd,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACE,uBAAuB,CAAC;;gBAGnFhB,OAAO,CAAC0T,QAAQ,EAAE;;aAErB,MAAM;cACLJ,cAAc,CAAC,SAAS,CAAC,EAAE;;UAE/B,CAAC,CAAC;UAEFtT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEqT,cAAc,CAAC;UAChDtT,OAAO,CAAC0T,QAAQ,EAAE;UAElB;UACA,IAAI,CAACtP,qBAAqB,EAAE;UAE5B;UACA,IAAI,CAAChE,eAAe,EAAE;UAEtB;UACA;UAEA;UACA,IAAI+S,QAAQ,CAAC3L,IAAI,IAAI2L,QAAQ,CAAC3L,IAAI,CAAC/E,OAAO,IAAI0Q,QAAQ,CAAC3L,IAAI,CAAC/E,OAAO,CAAChC,MAAM,GAAG,CAAC,EAAE;YAC9ET,OAAO,CAAC7C,KAAK,CAAC,uBAAuB,CAAC;YACtC6C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEkT,QAAQ,CAAC3L,IAAI,CAAC/E,OAAO,CAAChC,MAAM,CAAC;YAE3D;YACA,MAAMkT,iBAAiB,GAAGR,QAAQ,CAAC3L,IAAI,CAAC/E,OAAO,CAACjB,MAAM,CAACoS,CAAC,IAAIA,CAAC,CAAC9S,MAAM,IAAI8S,CAAC,CAAC9S,MAAM,CAACL,MAAM,GAAG,CAAC,CAAC;YAC5FT,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0T,iBAAiB,CAAClT,MAAM,CAAC;YAE7D;YACA,MAAMoT,kBAAkB,GAAGF,iBAAiB,CAACG,OAAO,CAACF,CAAC,IACpDA,CAAC,CAAC9S,MAAM,CAACpH,GAAG,CAACqa,CAAC,IAAG;cACf,MAAMhT,KAAK,GAAGgT,CAAQ;cACtB,OAAOhT,KAAK,CAAC0C,YAAY,KAAKrC,SAAS,GAAGL,KAAK,CAAC0C,YAAY,GACpD1C,KAAK,CAAC2C,QAAQ,GAAG3C,KAAK,CAAC2C,QAAQ,CAACC,kBAAkB,GAAG,CAAE;YACjE,CAAC,CAAC,CACH;YACD3D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE4T,kBAAkB,CAAC;YAEvD;YACA,MAAMG,kBAAkB,GAAGH,kBAAkB,CAACpU,MAAM,CAAC,CAACwU,GAA2B,EAAEC,GAAW,KAAI;cAChG,IAAIA,GAAG,KAAK9S,SAAS,EAAE;gBACrB6S,GAAG,CAACC,GAAG,CAAC,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEhC,OAAOD,GAAG;YACZ,CAAC,EAAE,EAA4B,CAAC;YAChCjU,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+T,kBAAkB,CAAC;YAEvD;YACA,MAAMG,iBAAiB,GAAGR,iBAAiB,CAACnS,MAAM,CAACoS,CAAC,IAClDA,CAAC,CAAC9S,MAAM,CAACsT,IAAI,CAACL,CAAC,IAAG;cAChB,MAAMhT,KAAK,GAAGgT,CAAQ;cACtB,OAAOhT,KAAK,CAACkM,cAAc,IAAIlM,KAAK,CAACkM,cAAc,CAACC,UAAU,KAAK,IAAI;YACzE,CAAC,CAAC,CACH;YACDlN,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkU,iBAAiB,CAAC1T,MAAM,CAAC;YAE5DT,OAAO,CAAC0T,QAAQ,EAAE;;UAGpB;UACA,IAAIP,QAAQ,CAAC3L,IAAI,IAAI2L,QAAQ,CAAC3L,IAAI,CAAC0H,QAAQ,EAAE;YAC3C,IAAI,CAACtU,YAAY,GAAGuY,QAAQ,CAAC3L,IAAI,CAAC0H,QAAQ;YAC1ClP,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACrF,YAAY,CAAC;;UAErE;UAAA,KACK,IAAIuY,QAAQ,CAACzL,MAAM,IAAIyL,QAAQ,CAACzL,MAAM,CAAC2M,SAAS,EAAE;YACrD,IAAI,CAACzZ,YAAY,GAAGuY,QAAQ,CAACzL,MAAM,CAAC2M,SAAS;YAC7CrU,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACrF,YAAY,CAAC;;UAExE;UAAA,KACK,IAAIuY,QAAQ,CAAC3L,IAAI,IAAI2L,QAAQ,CAAC3L,IAAI,CAAC/E,OAAO,IAAI0Q,QAAQ,CAAC3L,IAAI,CAAC/E,OAAO,CAAChC,MAAM,GAAG,CAAC,IAAI0S,QAAQ,CAAC3L,IAAI,CAAC/E,OAAO,CAAC,CAAC,CAAC,CAAC/B,EAAE,EAAE;YAClH,IAAI,CAAC9F,YAAY,GAAGuY,QAAQ,CAAC3L,IAAI,CAAC/E,OAAO,CAAC,CAAC,CAAC,CAAC/B,EAAE;YAC/CV,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACrF,YAAY,CAAC;WAChE,MAAM;YACLoF,OAAO,CAAC0P,KAAK,CAAC,qCAAqC,CAAC;YACpD1P,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEV,MAAM,CAACoF,IAAI,CAACwO,QAAQ,CAAC,CAAC;YACzD,IAAIA,QAAQ,CAAC3L,IAAI,EAAExH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEV,MAAM,CAACoF,IAAI,CAACwO,QAAQ,CAAC3L,IAAI,CAAC,CAAC;YAC7E,IAAI2L,QAAQ,CAACzL,MAAM,EAAE1H,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,MAAM,CAACoF,IAAI,CAACwO,QAAQ,CAACzL,MAAM,CAAC,CAAC;;SAEtF,MAAM;UACL,IAAI,CAAC/M,YAAY,GAAG,sDAAsD;UAC1E,IAAIwY,QAAQ,CAACzL,MAAM,CAAC4M,QAAQ,IAAInB,QAAQ,CAACzL,MAAM,CAAC4M,QAAQ,CAAC7T,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAAC9F,YAAY,GAAGwY,QAAQ,CAACzL,MAAM,CAAC4M,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACD7E,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnV,SAAS,GAAG,KAAK;QACtB,IAAI,CAACI,YAAY,GAAG,wDAAwD;QAC5EqF,OAAO,CAAC0P,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEA;EACA+B,oBAAoBA,CAAC+C,SAAoB;IACvCjV,MAAM,CAACC,MAAM,CAACgV,SAAS,CAACC,QAAQ,CAAC,CAACnT,OAAO,CAACoT,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAYrb,SAAS,EAAE;QAChC,IAAI,CAACoY,oBAAoB,CAACiD,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA;EACAjL,cAAcA,CAACmL,OAAe;IAC5B,MAAMhT,KAAK,GAAGoC,IAAI,CAACmI,KAAK,CAACyI,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMC,IAAI,GAAGD,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGhT,KAAK,KAAKiT,IAAI,KAAK;EAC/B;EAEA;EACAC,UAAUA,CAACC,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B,MAAMpT,IAAI,GAAG,IAAI5E,IAAI,CAACgY,UAAU,CAAC;IACjC,OAAOpT,IAAI,CAAC4H,kBAAkB,CAAC,OAAO,EAAE;MACtCyL,OAAO,EAAE,OAAO;MAChBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACdhL,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACAgL,+BAA+BA,CAACJ,UAAkB,EAAEK,aAAqB;IACvE,IAAI,CAACL,UAAU,EAAE,OAAO,KAAK;IAE7B,MAAMpT,IAAI,GAAG,IAAI5E,IAAI,CAACgY,UAAU,CAAC;IACjC,MAAMM,SAAS,GAAG1T,IAAI,CAAC4H,kBAAkB,CAAC,OAAO,EAAE;MACjDyL,OAAO,EAAE,OAAO;MAChBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACdhL,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;IAEF;IACA,IAAIiL,aAAa,EAAE;MACjB,MAAME,SAAS,GAAG,IAAIvY,IAAI,CAACqY,aAAa,CAAC;MAEzC;MACA,MAAMG,QAAQ,GAAG,IAAIxY,IAAI,CAAC4E,IAAI,CAAC6T,WAAW,EAAE,EAAE7T,IAAI,CAAC8T,QAAQ,EAAE,EAAE9T,IAAI,CAAC+T,OAAO,EAAE,CAAC;MAC9E,MAAMC,aAAa,GAAG,IAAI5Y,IAAI,CAACuY,SAAS,CAACE,WAAW,EAAE,EAAEF,SAAS,CAACG,QAAQ,EAAE,EAAEH,SAAS,CAACI,OAAO,EAAE,CAAC;MAElG,IAAIH,QAAQ,CAACvJ,OAAO,EAAE,KAAK2J,aAAa,CAAC3J,OAAO,EAAE,EAAE;QAClD;QACA,OAAO,GAAGqJ,SAAS,mEAAmEM,aAAa,CAACpM,kBAAkB,CAAC,OAAO,CAAC,YAAY;;;IAI/I,OAAO8L,SAAS;EAClB;EAEA;EACAO,cAAcA,CAACtV,MAAW;IACxB,MAAMuV,MAAM,GAAG;MAAEC,QAAQ,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAK,CAAE;IAElD,IAAI,CAACzV,MAAM,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACzD,OAAOoV,MAAM;;IAGf;IACA,MAAMG,aAAa,GAAG1V,MAAM,CAACE,KAAK,CAACgB,MAAM,CAAEb,IAAS,IAAKA,IAAI,CAACsV,aAAa,KAAK,CAAC,IAAItV,IAAI,CAACuV,KAAK,KAAK,CAAC,CAAC;IACtG,IAAIF,aAAa,CAACvV,MAAM,GAAG,CAAC,IAAIuV,aAAa,CAAC,CAAC,CAAC,CAACtU,SAAS,IAAIsU,aAAa,CAAC,CAAC,CAAC,CAACtU,SAAS,CAACC,IAAI,EAAE;MAC7FkU,MAAM,CAACC,QAAQ,GAAGE,aAAa,CAAC,CAAC,CAAC,CAACtU,SAAS,CAACC,IAAI;KAClD,MAAM,IAAIrB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,IAAIpB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,CAACC,IAAI,EAAE;MACzFkU,MAAM,CAACC,QAAQ,GAAGxV,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,CAACC,IAAI;;IAGlD;IACA,MAAMwU,YAAY,GAAG7V,MAAM,CAACE,KAAK,CAACgB,MAAM,CAAEb,IAAS,IAAKA,IAAI,CAACsV,aAAa,KAAK,CAAC,IAAItV,IAAI,CAACuV,KAAK,KAAK,CAAC,CAAC;IACrG,IAAIC,YAAY,CAAC1V,MAAM,GAAG,CAAC,IAAI0V,YAAY,CAAC,CAAC,CAAC,CAACzU,SAAS,IAAIyU,YAAY,CAAC,CAAC,CAAC,CAACzU,SAAS,CAACC,IAAI,EAAE;MAC1FkU,MAAM,CAACE,OAAO,GAAGI,YAAY,CAAC,CAAC,CAAC,CAACzU,SAAS,CAACC,IAAI;KAChD,MAAM,IAAIrB,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,IAAIH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,IAAIpB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,CAACC,IAAI,EAAE;MACpHkU,MAAM,CAACE,OAAO,GAAGzV,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,CAACC,IAAI;;IAGjD,OAAOkU,MAAM;EACf;EAEA;EACAO,WAAWA,CAAC9V,MAAoB;IAC9B,IAAI,CAACA,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACQ,MAAM,CAACL,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd,MAAM4V,QAAQ,GAAG/V,MAAM,CAACQ,MAAM,CAACrB,MAAM,CAAC,CAACwE,GAAQ,EAAElD,KAAU,KACxDA,KAAK,CAACyC,KAAK,IAAIS,GAAG,CAACT,KAAK,IAAIzC,KAAK,CAACyC,KAAK,CAACW,MAAM,GAAGF,GAAG,CAACT,KAAK,CAACW,MAAM,GAAIpD,KAAK,GAAGkD,GAAG,EAAE3D,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC,CAAC;IAEtG,OAAOuV,QAAQ,CAAC7S,KAAK,CAAC8S,eAAe,IAAI,GAAGD,QAAQ,CAAC7S,KAAK,CAACW,MAAM,IAAIkS,QAAQ,CAAC7S,KAAK,CAACxF,QAAQ,EAAE;EAChG;EAEA;EACAuY,iBAAiBA,CAACjW,MAAoB;IACpC;IACA,IAAI,CAACA,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACQ,MAAM,CAACL,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd;IACA,MAAMM,KAAK,GAAGT,MAAM,CAACQ,MAAM,CAAC,CAAC,CAAC;IAC9B,MAAM8L,iBAAiB,GAAG7L,KAAK,CAAC0C,YAAY,KAAKrC,SAAS,GAAGL,KAAK,CAAC0C,YAAY,GACrD1C,KAAK,CAAC2C,QAAQ,GAAG3C,KAAK,CAAC2C,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IACjF,OAAOiJ,iBAAiB,GAAG,CAAC;EAC9B;EAEA;EACA4J,qBAAqBA,CAAClW,MAAW,EAAEmW,sBAA8B,EAAEC,mBAA4B;IAI7F,MAAMb,MAAM,GAAG;MAAEc,KAAK,EAAE,IAAI;MAAEpC,OAAO,EAAE;IAAE,CAAE;IAE3C,IAAI,CAACjU,MAAM,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACzD,OAAOoV,MAAM;;IAGf;IACA,MAAMe,WAAW,GAAG,IAAI,CAAChB,cAAc,CAACtV,MAAM,CAAC;IAE/C;IACA,IAAImW,sBAAsB,IAAIG,WAAW,CAACd,QAAQ,KAAK,KAAK,EAAE;MAC5D,MAAMe,gBAAgB,GAAG,IAAI9Z,IAAI,CAAC0Z,sBAAsB,CAAC;MACzD,MAAMK,aAAa,GAAG,IAAI/Z,IAAI,CAAC6Z,WAAW,CAACd,QAAQ,CAAC;MAEpD;MACA,MAAMiB,eAAe,GAAG,IAAIha,IAAI,CAAC8Z,gBAAgB,CAACrB,WAAW,EAAE,EAAEqB,gBAAgB,CAACpB,QAAQ,EAAE,EAAEoB,gBAAgB,CAACnB,OAAO,EAAE,CAAC;MACzH,MAAMsB,YAAY,GAAG,IAAIja,IAAI,CAAC+Z,aAAa,CAACtB,WAAW,EAAE,EAAEsB,aAAa,CAACrB,QAAQ,EAAE,EAAEqB,aAAa,CAACpB,OAAO,EAAE,CAAC;MAE7G,IAAIqB,eAAe,CAAC/K,OAAO,EAAE,KAAKgL,YAAY,CAAChL,OAAO,EAAE,EAAE;QACxD6J,MAAM,CAACc,KAAK,GAAG,KAAK;QACpBd,MAAM,CAACtB,OAAO,IAAI,0CAA0CyC,YAAY,CAACzN,kBAAkB,EAAE,eAAewN,eAAe,CAACxN,kBAAkB,EAAE,IAAI;;;IAIxJ;IACA,IAAImN,mBAAmB,IAAIE,WAAW,CAACb,OAAO,KAAK,KAAK,EAAE;MACxD,MAAMkB,gBAAgB,GAAG,IAAIla,IAAI,CAAC2Z,mBAAmB,CAAC;MACtD,MAAMQ,aAAa,GAAG,IAAIna,IAAI,CAAC6Z,WAAW,CAACb,OAAO,CAAC;MAEnD;MACA,MAAMoB,eAAe,GAAG,IAAIpa,IAAI,CAACka,gBAAgB,CAACzB,WAAW,EAAE,EAAEyB,gBAAgB,CAACxB,QAAQ,EAAE,EAAEwB,gBAAgB,CAACvB,OAAO,EAAE,CAAC;MACzH,MAAM0B,YAAY,GAAG,IAAIra,IAAI,CAACma,aAAa,CAAC1B,WAAW,EAAE,EAAE0B,aAAa,CAACzB,QAAQ,EAAE,EAAEyB,aAAa,CAACxB,OAAO,EAAE,CAAC;MAE7G,IAAIyB,eAAe,CAACnL,OAAO,EAAE,KAAKoL,YAAY,CAACpL,OAAO,EAAE,EAAE;QACxD6J,MAAM,CAACc,KAAK,GAAG,KAAK;QACpBd,MAAM,CAACtB,OAAO,IAAI,uCAAuC6C,YAAY,CAAC7N,kBAAkB,EAAE,eAAe4N,eAAe,CAAC5N,kBAAkB,EAAE,GAAG;;;IAIpJ,OAAOsM,MAAM;EACf;EAEA;EACAwB,wBAAwBA,CAAC/W,MAAW,EAAEmW,sBAA8B,EAAEC,mBAA4B;IAChG;IACA,MAAMY,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACjX,MAAM,CAAC;IAElD;IACA,MAAMsW,WAAW,GAAG,IAAI,CAAChB,cAAc,CAACtV,MAAM,CAAC;IAE/C;IACA,MAAMkX,SAAS,GAAG,IAAI,CAAChB,qBAAqB,CAAClW,MAAM,EAAEmW,sBAAsB,EAAEC,mBAAmB,CAAC;IAEjG,IAAI,CAACc,SAAS,CAACb,KAAK,EAAE;MACpB;MACA,OAAO;;kFAEqEa,SAAS,CAACjD,OAAO;qBAC9E;KAChB,MAAM,IAAI+C,WAAW,EAAE;MACtB;MACA,OAAO;;uDAE0C,IAAIva,IAAI,CAAC6Z,WAAW,CAACd,QAAQ,CAAC,CAACvM,kBAAkB,EAAE,eAAe,IAAIxM,IAAI,CAAC6Z,WAAW,CAACb,OAAO,CAAC,CAACxM,kBAAkB,EAAE;qBACtJ;;IAGjB,OAAO,EAAE;EACX;EAEA;EACAkO,yBAAyBA,CAAA;IACvB,MAAMC,YAAY,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,CAACxd,cAAc,CAAC2V,kBAAkB,CAAC6H,YAAY,CAAC,CAAC5H,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAAC1V,kBAAkB,GAAG0V,SAAS;MACnC;MACA,MAAM4H,KAAK,GAAG/Y,QAAQ,CAACgZ,cAAc,CAAC,mBAAmB,CAAqB;MAC9E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAC,uBAAuBA,CAAA;IACrB,MAAMN,YAAY,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,CAACxd,cAAc,CAAC2V,kBAAkB,CAAC6H,YAAY,CAAC,CAAC5H,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACzV,gBAAgB,GAAGyV,SAAS;MACjC;MACA,MAAM4H,KAAK,GAAG/Y,QAAQ,CAACgZ,cAAc,CAAC,iBAAiB,CAAqB;MAC5E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAE,gCAAgCA,CAAChN,KAAa;IAC5C,MAAMyM,YAAY,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,CAACxd,cAAc,CAAC2V,kBAAkB,CAAC6H,YAAY,CAAC,CAAC5H,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAAC1V,kBAAkB,GAAG0V,SAAS;MACnC;MACA,MAAM4H,KAAK,GAAG/Y,QAAQ,CAACgZ,cAAc,CAAC,oBAAoB3M,KAAK,EAAE,CAAqB;MACtF,IAAI0M,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAG,8BAA8BA,CAACjN,KAAa;IAC1C,MAAMyM,YAAY,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,CAACxd,cAAc,CAAC2V,kBAAkB,CAAC6H,YAAY,CAAC,CAAC5H,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACzV,gBAAgB,GAAGyV,SAAS;MACjC;MACA,MAAM4H,KAAK,GAAG/Y,QAAQ,CAACgZ,cAAc,CAAC,kBAAkB3M,KAAK,EAAE,CAAqB;MACpF,IAAI0M,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAI,aAAaA,CAAA;IACX,MAAM5a,iBAAiB,GAAG,IAAI,CAACL,UAAU,CAACiE,GAAG,CAAC,mBAAmB,CAAC,EAAE3F,KAAK;IACzE,MAAMgC,eAAe,GAAG,IAAI,CAACN,UAAU,CAACiE,GAAG,CAAC,iBAAiB,CAAC,EAAE3F,KAAK;IAErE,IAAI,CAAC0B,UAAU,CAACkb,UAAU,CAAC;MACzB7a,iBAAiB,EAAEC,eAAe;MAClCA,eAAe,EAAED;KAClB,CAAC;EACJ;EAEA;EACA8a,oBAAoBA,CAACpN,KAAa;IAChC,MAAMtN,QAAQ,GAAG,IAAI,CAAC+S,gBAAgB,EAAE;IACxC,MAAM7P,OAAO,GAAGlD,QAAQ,CAAC2a,EAAE,CAACrN,KAAK,CAAc;IAE/C,IAAIpK,OAAO,EAAE;MACX,MAAMtD,iBAAiB,GAAGsD,OAAO,CAACM,GAAG,CAAC,mBAAmB,CAAC,EAAE3F,KAAK;MACjE,MAAMgC,eAAe,GAAGqD,OAAO,CAACM,GAAG,CAAC,iBAAiB,CAAC,EAAE3F,KAAK;MAE7DqF,OAAO,CAACuX,UAAU,CAAC;QACjB7a,iBAAiB,EAAEC,eAAe;QAClCA,eAAe,EAAED;OAClB,CAAC;;EAEN;EAEA;EACAgb,oBAAoBA,CAACxD,UAAkB;IACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B;IACA,MAAMpT,IAAI,GAAG,IAAI5E,IAAI,CAACgY,UAAU,CAAC;IACjC,OAAOpT,IAAI,CAACoL,cAAc,EAAE;EAC9B;EAEA;EACAyL,kBAAkBA,CAACjL,WAAmB;IACpC,QAAQA,WAAW;MACjB,KAAK,CAAC;QAAE,OAAO,eAAe;MAC9B,KAAK,CAAC;QAAE,OAAO,iBAAiB;MAChC,KAAK,CAAC;QAAE,OAAO,cAAc;MAC7B;QAAS,OAAO,SAAS;;EAE7B;EAEA;EACAkL,mBAAmBA,CAACtL,mBAA0B,EAAEhO,IAAY;IAC1D,IAAI,CAACgO,mBAAmB,IAAI,CAACuL,KAAK,CAACC,OAAO,CAACxL,mBAAmB,CAAC,EAAE;MAC/D,OAAO,EAAE;;IAEX,OAAOA,mBAAmB,CAAC3L,MAAM,CAACkM,OAAO,IAAIA,OAAO,CAACH,WAAW,KAAKpO,IAAI,CAAC;EAC5E;EAEA;EACAyZ,oBAAoBA,CAACC,aAAqB;IACxC,QAAQA,aAAa;MACnB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,WAAW;;EAE/B;EAEA;EACAC,oBAAoBA,CAACC,cAAmB,EAAEC,WAAgB;IACxD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAACjX,OAAO,IAAI,CAACiX,cAAc,CAACjX,OAAO,CAACH,IAAI,IAC1E,CAACqX,WAAW,IAAI,CAACA,WAAW,CAACtX,SAAS,IAAI,CAACsX,WAAW,CAACtX,SAAS,CAACC,IAAI,EAAE;MACzE,OAAO,SAAS;;IAGlB,MAAMtF,WAAW,GAAG,IAAIU,IAAI,CAACgc,cAAc,CAACjX,OAAO,CAACH,IAAI,CAAC,CAACqK,OAAO,EAAE;IACnE,MAAMhQ,aAAa,GAAG,IAAIe,IAAI,CAACic,WAAW,CAACtX,SAAS,CAACC,IAAI,CAAC,CAACqK,OAAO,EAAE;IACpE,MAAMiN,MAAM,GAAGjd,aAAa,GAAGK,WAAW;IAC1C,MAAM6c,QAAQ,GAAGlV,IAAI,CAACmI,KAAK,CAAC8M,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIC,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,KAAK;KACxB,MAAM;MACL,MAAMtX,KAAK,GAAGoC,IAAI,CAACmI,KAAK,CAAC+M,QAAQ,GAAG,EAAE,CAAC;MACvC,MAAMrE,IAAI,GAAGqE,QAAQ,GAAG,EAAE;MAC1B,OAAO,GAAGtX,KAAK,KAAKiT,IAAI,KAAK;;EAEjC;EAEA;EACAsE,qBAAqBA,CAACC,mBAA2B,EAAEC,oBAA4B;IAC7E,IAAI,CAACD,mBAAmB,IAAI,CAACC,oBAAoB,EAAE;MACjD,OAAO,KAAK;;IAGd,MAAMvX,OAAO,GAAG,IAAI/E,IAAI,CAACqc,mBAAmB,CAAC;IAC7C,MAAM1X,SAAS,GAAG,IAAI3E,IAAI,CAACsc,oBAAoB,CAAC;IAEhD;IACA,MAAM7G,QAAQ,GAAGxO,IAAI,CAACyO,GAAG,CAAC/Q,SAAS,CAACsK,OAAO,EAAE,GAAGlK,OAAO,CAACkK,OAAO,EAAE,CAAC;IAClE,MAAM0G,QAAQ,GAAG1O,IAAI,CAAC2O,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIE,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAO,UAAU;KAClB,MAAM,IAAIA,QAAQ,KAAK,CAAC,EAAE;MACzB,OAAO,OAAO;KACf,MAAM;MACL,OAAO,GAAGA,QAAQ,OAAO;;EAE7B;EAEA;EACA6E,iBAAiBA,CAACjX,MAAW;IAC3B,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACzD,OAAO,KAAK;;IAGd;IACA;IACA,IAAIH,MAAM,CAACE,KAAK,CAAC4T,IAAI,CAAEzT,IAAS,IAAKA,IAAI,CAACsV,aAAa,KAAK,CAAC,CAAC,EAAE;MAC9DjW,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,4CAA4C,CAAC;MAC5E,OAAO,IAAI;;IAGb;IACA,IAAIJ,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3BT,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,6CAA6C,CAAC;MAC7E,OAAO,IAAI;;IAGb;IACA,IAAIJ,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAIH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,IAAI2C,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAAC8C,MAAM,GAAG,CAAC,EAAE;MAChG,MAAM6Y,YAAY,GAAGhZ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAAC,CAAC,CAAC;MAChD,MAAM4b,WAAW,GAAGjZ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAAC2C,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAAC8C,MAAM,GAAG,CAAC,CAAC;MAEjF;MACA,IAAI6Y,YAAY,IAAIA,YAAY,CAAC5X,SAAS,IAAI4X,YAAY,CAACxX,OAAO,IAC9DyX,WAAW,IAAIA,WAAW,CAAC7X,SAAS,IAAI6X,WAAW,CAACzX,OAAO,EAAE;QAE/D,MAAM0X,cAAc,GAAGF,YAAY,CAAC5X,SAAS,CAAC4I,OAAO,EAAEX,IAAI,IAAI2P,YAAY,CAAC5X,SAAS,CAAC8I,IAAI,EAAE5J,IAAI;QAChG,MAAM6Y,YAAY,GAAGH,YAAY,CAACxX,OAAO,CAACwI,OAAO,EAAEX,IAAI,IAAI2P,YAAY,CAACxX,OAAO,CAAC0I,IAAI,EAAE5J,IAAI;QAC1F,MAAM8Y,aAAa,GAAGH,WAAW,CAAC7X,SAAS,CAAC4I,OAAO,EAAEX,IAAI,IAAI4P,WAAW,CAAC7X,SAAS,CAAC8I,IAAI,EAAE5J,IAAI;QAC7F,MAAM+Y,WAAW,GAAGJ,WAAW,CAACzX,OAAO,CAACwI,OAAO,EAAEX,IAAI,IAAI4P,WAAW,CAACzX,OAAO,CAAC0I,IAAI,EAAE5J,IAAI;QAEvF;QACA,IAAI4Y,cAAc,IAAIG,WAAW,IAAIH,cAAc,KAAKG,WAAW,EAAE;UACnE3Z,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,2CAA2C,CAAC;UAC3E,OAAO,IAAI;;QAGb;QACA,IAAI8Y,cAAc,IAAIC,YAAY,IAAIC,aAAa,IAAIC,WAAW,IAC9DH,cAAc,KAAKG,WAAW,IAAIF,YAAY,KAAKC,aAAa,EAAE;UACpE1Z,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,8CAA8C,CAAC;UAC9E,OAAO,IAAI;;;;IAKjB;IACA,IAAI,IAAI,CAAC1F,iBAAiB,KAAK,IAAI,CAACF,sBAAsB,EAAE;MAC1DkF,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,0CAA0C,CAAC;MAC1E,OAAO,IAAI;;IAGb,OAAO,KAAK;EACd;EAEA;EACAkZ,mBAAmBA,CAACtZ,MAAW;IAC7B,IAAI,CAACA,MAAM,CAACE,KAAK,EAAE,OAAO,EAAE;IAE5B;IACA,MAAMwV,aAAa,GAAG1V,MAAM,CAACE,KAAK,CAACgB,MAAM,CAAEb,IAAS,IAAKA,IAAI,CAACsV,aAAa,KAAK,CAAC,CAAC;IAClF,IAAID,aAAa,CAACvV,MAAM,GAAG,CAAC,EAAE;MAC5BT,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,gDAAgD,CAAC;MAChF,OAAOsV,aAAa,CAAC,CAAC,CAAC,CAACrY,QAAQ,IAAI,CAACqY,aAAa,CAAC,CAAC,CAAC,CAAC;;IAGxD;IACA,IAAI1V,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3BT,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,iDAAiD,CAAC;MACjF,OAAOJ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,IAAI,CAAC2C,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;;IAGtD;IACA,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAIH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,IAAI2C,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAAC8C,MAAM,GAAG,CAAC,EAAE;MAChG;MACA,MAAMoZ,UAAU,GAAG7V,IAAI,CAAC2O,IAAI,CAACrS,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAAC8C,MAAM,GAAG,CAAC,CAAC;MACjET,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,qDAAqD,CAAC;MACrF,OAAOJ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAACmc,KAAK,CAAC,CAAC,EAAED,UAAU,CAAC;;IAGtD;IACA,OAAOvZ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,IAAI,CAAC2C,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;EACtD;EAEA;EACAuZ,kBAAkBA,CAACzZ,MAAW;IAC5B,IAAI,CAACA,MAAM,CAACE,KAAK,EAAE,OAAO,EAAE;IAE5B;IACA,MAAM2V,YAAY,GAAG7V,MAAM,CAACE,KAAK,CAACgB,MAAM,CAAEb,IAAS,IAAKA,IAAI,CAACsV,aAAa,KAAK,CAAC,CAAC;IACjF,IAAIE,YAAY,CAAC1V,MAAM,GAAG,CAAC,EAAE;MAC3BT,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,+CAA+C,CAAC;MAC/E,OAAOyV,YAAY,CAAC,CAAC,CAAC,CAACxY,QAAQ,IAAI,CAACwY,YAAY,CAAC,CAAC,CAAC,CAAC;;IAGtD;IACA,IAAI7V,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3BT,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,gDAAgD,CAAC;MAChF,OAAOJ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,IAAI,CAAC2C,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;;IAGtD;IACA,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAIH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,IAAI2C,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAAC8C,MAAM,GAAG,CAAC,EAAE;MAChG;MACA,MAAMoZ,UAAU,GAAG7V,IAAI,CAAC2O,IAAI,CAACrS,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAAC8C,MAAM,GAAG,CAAC,CAAC;MACjET,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,oDAAoD,CAAC;MACpF,OAAOJ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC7C,QAAQ,CAACmc,KAAK,CAACD,UAAU,CAAC;;IAGnD;IACA,OAAO,EAAE;EACX;EAEA;EACAG,iBAAiBA,CAAC1Z,MAAW;IAC3B,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACzD,OAAO,KAAK;;IAGd;IACA,IAAIH,MAAM,CAACE,KAAK,CAAC4T,IAAI,CAAEzT,IAAS,IAAKA,IAAI,CAACsV,aAAa,GAAG,CAAC,CAAC,EAAE;MAC5DjW,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,gDAAgD,CAAC;MAChF,OAAO,IAAI;;IAGb;IACA,IAAIJ,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3BT,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,uDAAuD,CAAC;MACvF,OAAO,IAAI;;IAGb;IACA,IAAI,IAAI,CAAC1F,iBAAiB,KAAK,IAAI,CAACD,sBAAsB,EAAE;MAC1DiF,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,0CAA0C,CAAC;MAC1E,OAAO,IAAI;;IAGb,OAAO,KAAK;EACd;CAID;AAl0ECuZ,UAAA,EADC7gB,YAAY,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,0DAS1C;AA5JUW,oBAAoB,GAAAkgB,UAAA,EAZhC/gB,SAAS,CAAC;EACTghB,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CACT,8BAA8B,EAC9B,mBAAmB,EACnB,wBAAwB,EACxB,yBAAyB,EACzB,yBAAyB,CAC1B;EACDC,aAAa,EAAElhB,iBAAiB,CAACmhB;CAClC,CAAC,C,EACWvgB,oBAAoB,CAs9EhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}