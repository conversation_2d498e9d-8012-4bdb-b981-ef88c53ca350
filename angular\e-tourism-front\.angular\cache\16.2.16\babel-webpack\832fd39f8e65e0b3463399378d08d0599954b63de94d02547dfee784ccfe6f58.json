{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewEncapsulation, HostListener } from '@angular/core';\nimport { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nexport let SearchPriceComponent = class SearchPriceComponent {\n  constructor(fb, productService, router) {\n    this.fb = fb;\n    this.productService = productService;\n    this.router = router;\n    this.departureLocations = [];\n    this.arrivalLocations = [];\n    this.isLoading = false;\n    this.searchResults = [];\n    this.filteredResults = [];\n    this.hasSearched = false;\n    this.errorMessage = '';\n    this.lastSearchId = '';\n    // Passenger selector properties\n    this.showPassengerDropdown = false;\n    this.passengerCounts = {\n      [PassengerType.Adult]: 1,\n      [PassengerType.Child]: 0,\n      [PassengerType.Infant]: 0\n    };\n    // Filter options\n    this.currentFilter = 'recommended';\n    this.filterOptions = [{\n      value: 'recommended',\n      label: 'Recommended',\n      icon: 'fa-star'\n    }, {\n      value: 'cheapest',\n      label: 'Cheapest',\n      icon: 'fa-dollar-sign'\n    }, {\n      value: 'shortest',\n      label: 'Shortest',\n      icon: 'fa-clock'\n    }];\n    // Sidebar filter options\n    this.sidebarFilters = {\n      stops: {\n        direct: false,\n        oneStop: false,\n        multiStop: false\n      },\n      departureTime: {\n        earlyMorning: false,\n        morning: false,\n        afternoon: false,\n        evening: false // 18:00 - 00:00\n      },\n\n      arrivalTime: {\n        earlyMorning: false,\n        morning: false,\n        afternoon: false,\n        evening: false // 18:00 - 00:00\n      },\n\n      airlines: {} // Will be populated dynamically based on search results\n    };\n    // Expanded sections in sidebar\n    this.expandedSections = {\n      stops: true,\n      departureTime: true,\n      arrivalTime: true,\n      airlines: true\n    };\n    // Price ranges for each filter option (will be calculated from results)\n    this.filterPrices = {\n      stops: {\n        direct: 0,\n        oneStop: 0,\n        multiStop: 0\n      },\n      departureTime: {\n        earlyMorning: 0,\n        morning: 0,\n        afternoon: 0,\n        evening: 0\n      },\n      arrivalTime: {\n        earlyMorning: 0,\n        morning: 0,\n        afternoon: 0,\n        evening: 0\n      },\n      airlines: {}\n    };\n    // Passenger type options\n    this.passengerTypes = [{\n      value: PassengerType.Adult,\n      label: 'Adult'\n    }, {\n      value: PassengerType.Child,\n      label: 'Child'\n    }, {\n      value: PassengerType.Infant,\n      label: 'Infant'\n    }];\n    // Flight class options\n    this.flightClasses = [{\n      value: FlightClassType.PROMO,\n      label: 'Promo'\n    }, {\n      value: FlightClassType.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClassType.BUSINESS,\n      label: 'Business'\n    }];\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required],\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      arrivalLocation: ['', Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      // Options de vol\n      flightClass: [0, Validators.required],\n      nonStop: [false],\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n  // Close dropdown when clicking outside\n  onDocumentClick(event) {\n    // Check if click is outside the passenger dropdown\n    const clickedElement = event.target;\n    const passengerSelector = document.querySelector('.passengers-selector');\n    if (passengerSelector && !passengerSelector.contains(clickedElement)) {\n      this.showPassengerDropdown = false;\n    }\n  }\n  // Toggle passenger dropdown\n  togglePassengerDropdown(event) {\n    event.stopPropagation();\n    this.showPassengerDropdown = !this.showPassengerDropdown;\n  }\n  // Close passenger dropdown\n  closePassengerDropdown() {\n    this.showPassengerDropdown = false;\n  }\n  // Get passenger count for a specific type\n  getPassengerCount(type) {\n    return this.passengerCounts[type] || 0;\n  }\n  // Increase passenger count\n  increasePassengerCount(type) {\n    if (this.getTotalPassengers() < 9) {\n      this.passengerCounts[type] = (this.passengerCounts[type] || 0) + 1;\n    }\n  }\n  // Decrease passenger count\n  decreasePassengerCount(type) {\n    if (this.passengerCounts[type] > 0) {\n      this.passengerCounts[type] -= 1;\n    }\n  }\n  // Get total number of passengers\n  getTotalPassengers() {\n    return Object.values(this.passengerCounts).reduce((sum, count) => sum + count, 0);\n  }\n  // Get passengers array for API request\n  getPassengersArray() {\n    const passengers = [];\n    // Add each passenger type with count > 0\n    Object.entries(this.passengerCounts).forEach(([type, count]) => {\n      if (count > 0) {\n        passengers.push({\n          type: parseInt(type),\n          count: count\n        });\n      }\n    });\n    // Ensure at least one passenger is included\n    if (passengers.length === 0) {\n      passengers.push({\n        type: PassengerType.Adult,\n        count: 1\n      });\n    }\n    return passengers;\n  }\n  ngOnInit() {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n  // Méthode pour appliquer le filtre sélectionné\n  applyFilter(filterValue) {\n    this.currentFilter = filterValue;\n    this.applyAllFilters();\n  }\n  // Méthode pour appliquer tous les filtres (top et sidebar)\n  applyAllFilters() {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      this.filteredResults = [];\n      return;\n    }\n    // Étape 1: Appliquer les filtres de la sidebar\n    let results = [...this.searchResults];\n    // Filtrer par nombre d'escales\n    if (this.sidebarFilters.stops.direct || this.sidebarFilters.stops.oneStop || this.sidebarFilters.stops.multiStop) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0) return false;\n        const stopCount = flight.items[0].stopCount || 0;\n        return this.sidebarFilters.stops.direct && stopCount === 0 || this.sidebarFilters.stops.oneStop && stopCount === 1 || this.sidebarFilters.stops.multiStop && stopCount >= 2;\n      });\n    }\n    // Filtrer par horaire de départ\n    if (this.sidebarFilters.departureTime.earlyMorning || this.sidebarFilters.departureTime.morning || this.sidebarFilters.departureTime.afternoon || this.sidebarFilters.departureTime.evening) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].departure || !flight.items[0].departure.date) {\n          return false;\n        }\n        const departureDate = new Date(flight.items[0].departure.date);\n        const hours = departureDate.getHours();\n        return this.sidebarFilters.departureTime.earlyMorning && hours >= 0 && hours < 8 || this.sidebarFilters.departureTime.morning && hours >= 8 && hours < 12 || this.sidebarFilters.departureTime.afternoon && hours >= 12 && hours < 18 || this.sidebarFilters.departureTime.evening && hours >= 18 && hours < 24;\n      });\n    }\n    // Filtrer par horaire d'arrivée\n    if (this.sidebarFilters.arrivalTime.earlyMorning || this.sidebarFilters.arrivalTime.morning || this.sidebarFilters.arrivalTime.afternoon || this.sidebarFilters.arrivalTime.evening) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].arrival || !flight.items[0].arrival.date) {\n          return false;\n        }\n        const arrivalDate = new Date(flight.items[0].arrival.date);\n        const hours = arrivalDate.getHours();\n        return this.sidebarFilters.arrivalTime.earlyMorning && hours >= 0 && hours < 8 || this.sidebarFilters.arrivalTime.morning && hours >= 8 && hours < 12 || this.sidebarFilters.arrivalTime.afternoon && hours >= 12 && hours < 18 || this.sidebarFilters.arrivalTime.evening && hours >= 18 && hours < 24;\n      });\n    }\n    // Filtrer par compagnie aérienne\n    const selectedAirlines = Object.entries(this.sidebarFilters.airlines).filter(([_, selected]) => selected).map(([airline, _]) => airline);\n    if (selectedAirlines.length > 0) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].airline || !flight.items[0].airline.name) {\n          return false;\n        }\n        return selectedAirlines.includes(flight.items[0].airline.name);\n      });\n    }\n    // Étape 2: Appliquer le tri selon le filtre sélectionné en haut\n    switch (this.currentFilter) {\n      case 'cheapest':\n        this.filteredResults = this.sortByPrice(results);\n        break;\n      case 'shortest':\n        this.filteredResults = this.sortByDuration(results);\n        break;\n      case 'recommended':\n      default:\n        this.filteredResults = this.sortByRecommendation(results);\n        break;\n    }\n  }\n  // Trier les vols par prix (du moins cher au plus cher)\n  sortByPrice(flights) {\n    return flights.sort((a, b) => {\n      const priceA = this.getMinPriceAmount(a);\n      const priceB = this.getMinPriceAmount(b);\n      return priceA - priceB;\n    });\n  }\n  // Trier les vols par durée (du plus court au plus long)\n  sortByDuration(flights) {\n    return flights.sort((a, b) => {\n      const durationA = a.items && a.items[0] ? a.items[0].duration : Number.MAX_VALUE;\n      const durationB = b.items && b.items[0] ? b.items[0].duration : Number.MAX_VALUE;\n      return durationA - durationB;\n    });\n  }\n  // Trier les vols par recommandation (combinaison de prix, durée et autres facteurs)\n  sortByRecommendation(flights) {\n    return flights.sort((a, b) => {\n      // Calculer un score pour chaque vol basé sur plusieurs facteurs\n      const scoreA = this.calculateRecommendationScore(a);\n      const scoreB = this.calculateRecommendationScore(b);\n      return scoreB - scoreA; // Ordre décroissant (score plus élevé = meilleur)\n    });\n  }\n  // Calculer un score de recommandation pour un vol\n  calculateRecommendationScore(flight) {\n    if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n      return 0;\n    }\n    const item = flight.items[0];\n    const offer = flight.offers[0];\n    // Facteurs à considérer pour le score\n    const price = this.getMinPriceAmount(flight);\n    const duration = item.duration;\n    const stopCount = item.stopCount || 0;\n    const availability = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    // Normaliser les valeurs (plus le score est élevé, meilleur est le vol)\n    const priceScore = 1000 / (price + 100); // Inverse du prix (moins cher = meilleur score)\n    const durationScore = 1000 / (duration + 100); // Inverse de la durée (plus court = meilleur score)\n    const stopScore = 1 / (stopCount + 1); // Moins d'escales = meilleur score\n    const availabilityScore = Math.min(availability, 10) / 10; // Plus de disponibilité = meilleur score\n    // Pondération des facteurs (ajustable selon les préférences)\n    const weights = {\n      price: 0.4,\n      duration: 0.3,\n      stops: 0.2,\n      availability: 0.1 // 10% importance pour la disponibilité\n    };\n    // Calculer le score final pondéré\n    return priceScore * weights.price + durationScore * weights.duration + stopScore * weights.stops + availabilityScore * weights.availability;\n  }\n  // Obtenir le montant du prix minimum pour un vol\n  getMinPriceAmount(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return Number.MAX_VALUE;\n    }\n    return flight.offers.reduce((min, offer) => offer.price && offer.price.amount < min ? offer.price.amount : min, flight.offers[0].price ? flight.offers[0].price.amount : Number.MAX_VALUE);\n  }\n  // Calculer les prix minimums pour chaque option de filtre\n  calculateFilterPrices() {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      return;\n    }\n    // Réinitialiser les prix\n    this.resetFilterPrices();\n    // Collecter toutes les compagnies aériennes\n    const airlines = new Set();\n    // Parcourir tous les vols pour calculer les prix minimums\n    this.searchResults.forEach(flight => {\n      if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n        return;\n      }\n      const item = flight.items[0];\n      const price = this.getMinPriceAmount(flight);\n      // Ajouter la compagnie aérienne à la liste\n      if (item.airline && item.airline.name) {\n        airlines.add(item.airline.name);\n        // Initialiser le prix pour cette compagnie si nécessaire\n        if (!(item.airline.name in this.filterPrices.airlines)) {\n          this.filterPrices.airlines[item.airline.name] = Number.MAX_VALUE;\n        }\n        // Mettre à jour le prix minimum pour cette compagnie\n        this.filterPrices.airlines[item.airline.name] = Math.min(this.filterPrices.airlines[item.airline.name], price);\n      }\n      // Mettre à jour les prix par nombre d'escales\n      const stopCount = item.stopCount || 0;\n      if (stopCount === 0) {\n        this.filterPrices.stops.direct = Math.min(this.filterPrices.stops.direct, price);\n      } else if (stopCount === 1) {\n        this.filterPrices.stops.oneStop = Math.min(this.filterPrices.stops.oneStop, price);\n      } else {\n        this.filterPrices.stops.multiStop = Math.min(this.filterPrices.stops.multiStop, price);\n      }\n      // Mettre à jour les prix par horaire de départ\n      if (item.departure && item.departure.date) {\n        const departureDate = new Date(item.departure.date);\n        const departureHours = departureDate.getHours();\n        if (departureHours >= 0 && departureHours < 8) {\n          this.filterPrices.departureTime.earlyMorning = Math.min(this.filterPrices.departureTime.earlyMorning, price);\n        } else if (departureHours >= 8 && departureHours < 12) {\n          this.filterPrices.departureTime.morning = Math.min(this.filterPrices.departureTime.morning, price);\n        } else if (departureHours >= 12 && departureHours < 18) {\n          this.filterPrices.departureTime.afternoon = Math.min(this.filterPrices.departureTime.afternoon, price);\n        } else {\n          this.filterPrices.departureTime.evening = Math.min(this.filterPrices.departureTime.evening, price);\n        }\n      }\n      // Mettre à jour les prix par horaire d'arrivée\n      if (item.arrival && item.arrival.date) {\n        const arrivalDate = new Date(item.arrival.date);\n        const arrivalHours = arrivalDate.getHours();\n        if (arrivalHours >= 0 && arrivalHours < 8) {\n          this.filterPrices.arrivalTime.earlyMorning = Math.min(this.filterPrices.arrivalTime.earlyMorning, price);\n        } else if (arrivalHours >= 8 && arrivalHours < 12) {\n          this.filterPrices.arrivalTime.morning = Math.min(this.filterPrices.arrivalTime.morning, price);\n        } else if (arrivalHours >= 12 && arrivalHours < 18) {\n          this.filterPrices.arrivalTime.afternoon = Math.min(this.filterPrices.arrivalTime.afternoon, price);\n        } else {\n          this.filterPrices.arrivalTime.evening = Math.min(this.filterPrices.arrivalTime.evening, price);\n        }\n      }\n    });\n    // Initialiser les filtres de compagnies aériennes\n    airlines.forEach(airline => {\n      if (!(airline in this.sidebarFilters.airlines)) {\n        this.sidebarFilters.airlines[airline] = false;\n      }\n    });\n    // Remplacer les valeurs MAX_VALUE par 0 pour les options sans vols\n    this.cleanupFilterPrices();\n  }\n  // Réinitialiser les prix des filtres\n  resetFilterPrices() {\n    this.filterPrices = {\n      stops: {\n        direct: Number.MAX_VALUE,\n        oneStop: Number.MAX_VALUE,\n        multiStop: Number.MAX_VALUE\n      },\n      departureTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      arrivalTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      airlines: {}\n    };\n  }\n  // Nettoyer les prix des filtres (remplacer MAX_VALUE par 0)\n  cleanupFilterPrices() {\n    // Escales\n    if (this.filterPrices.stops.direct === Number.MAX_VALUE) this.filterPrices.stops.direct = 0;\n    if (this.filterPrices.stops.oneStop === Number.MAX_VALUE) this.filterPrices.stops.oneStop = 0;\n    if (this.filterPrices.stops.multiStop === Number.MAX_VALUE) this.filterPrices.stops.multiStop = 0;\n    // Horaires de départ\n    if (this.filterPrices.departureTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.departureTime.earlyMorning = 0;\n    if (this.filterPrices.departureTime.morning === Number.MAX_VALUE) this.filterPrices.departureTime.morning = 0;\n    if (this.filterPrices.departureTime.afternoon === Number.MAX_VALUE) this.filterPrices.departureTime.afternoon = 0;\n    if (this.filterPrices.departureTime.evening === Number.MAX_VALUE) this.filterPrices.departureTime.evening = 0;\n    // Horaires d'arrivée\n    if (this.filterPrices.arrivalTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.arrivalTime.earlyMorning = 0;\n    if (this.filterPrices.arrivalTime.morning === Number.MAX_VALUE) this.filterPrices.arrivalTime.morning = 0;\n    if (this.filterPrices.arrivalTime.afternoon === Number.MAX_VALUE) this.filterPrices.arrivalTime.afternoon = 0;\n    if (this.filterPrices.arrivalTime.evening === Number.MAX_VALUE) this.filterPrices.arrivalTime.evening = 0;\n    // Compagnies aériennes\n    Object.keys(this.filterPrices.airlines).forEach(airline => {\n      if (this.filterPrices.airlines[airline] === Number.MAX_VALUE) {\n        this.filterPrices.airlines[airline] = 0;\n      }\n    });\n  }\n  // Basculer l'état d'expansion d'une section\n  toggleSection(section) {\n    this.expandedSections[section] = !this.expandedSections[section];\n  }\n  // Basculer un filtre d'escale\n  toggleStopFilter(filter) {\n    this.sidebarFilters.stops[filter] = !this.sidebarFilters.stops[filter];\n    this.applyAllFilters();\n  }\n  // Basculer un filtre d'horaire de départ\n  toggleDepartureTimeFilter(filter) {\n    this.sidebarFilters.departureTime[filter] = !this.sidebarFilters.departureTime[filter];\n    this.applyAllFilters();\n  }\n  // Basculer un filtre d'horaire d'arrivée\n  toggleArrivalTimeFilter(filter) {\n    this.sidebarFilters.arrivalTime[filter] = !this.sidebarFilters.arrivalTime[filter];\n    this.applyAllFilters();\n  }\n  // Basculer un filtre de compagnie aérienne\n  toggleAirlineFilter(airline) {\n    this.sidebarFilters.airlines[airline] = !this.sidebarFilters.airlines[airline];\n    this.applyAllFilters();\n  }\n  // Effacer tous les filtres\n  clearAllFilters() {\n    // Réinitialiser les filtres d'escales\n    this.sidebarFilters.stops.direct = false;\n    this.sidebarFilters.stops.oneStop = false;\n    this.sidebarFilters.stops.multiStop = false;\n    // Réinitialiser les filtres d'horaires de départ\n    this.sidebarFilters.departureTime.earlyMorning = false;\n    this.sidebarFilters.departureTime.morning = false;\n    this.sidebarFilters.departureTime.afternoon = false;\n    this.sidebarFilters.departureTime.evening = false;\n    // Réinitialiser les filtres d'horaires d'arrivée\n    this.sidebarFilters.arrivalTime.earlyMorning = false;\n    this.sidebarFilters.arrivalTime.morning = false;\n    this.sidebarFilters.arrivalTime.afternoon = false;\n    this.sidebarFilters.arrivalTime.evening = false;\n    // Réinitialiser les filtres de compagnies aériennes\n    Object.keys(this.sidebarFilters.airlines).forEach(airline => {\n      this.sidebarFilters.airlines[airline] = false;\n    });\n    // Appliquer les filtres (qui seront tous désactivés)\n    this.applyAllFilters();\n  }\n  // Formater le prix pour l'affichage\n  formatPrice(price) {\n    if (price === 0) return '-';\n    return price.toFixed(0) + ' €';\n  }\n  // Obtenir les clés des compagnies aériennes\n  getAirlineKeys() {\n    return Object.keys(this.sidebarFilters.airlines);\n  }\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight) {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n    header.appendChild(logo);\n    header.appendChild(title);\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n        generalInfo.appendChild(airlineInfo);\n      }\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n      routeSection.appendChild(routeVisual);\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n          segmentsList.appendChild(segmentItem);\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n            segmentsList.appendChild(layover);\n          }\n        });\n        routeSection.appendChild(segmentsList);\n      }\n    }\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n          const baggageContainer = document.createElement('div');\n          baggageContainer.style.display = 'flex';\n          baggageContainer.style.flexDirection = 'column';\n          baggageContainer.style.gap = '10px';\n          baggageContainer.style.marginBottom = '15px';\n          // Filtrer et regrouper les bagages par type\n          const checkedBaggage = offer.baggageInformations.filter(b => b.baggageType === 2);\n          const cabinBaggage = offer.baggageInformations.filter(b => b.baggageType === 1);\n          const handBaggage = offer.baggageInformations.filter(b => b.baggageType === 3);\n          // Bagages en soute\n          if (checkedBaggage.length > 0) {\n            checkedBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#e7f5ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #c5e1f9';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-suitcase\" style=\"color: #4a6fa5; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Checked Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n          // Bagages cabine\n          if (cabinBaggage.length > 0) {\n            cabinBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#f3f0ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #e5dbff';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Cabin Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          } else {\n            // Ajouter un bagage cabine par défaut si aucun n'est spécifié\n            const baggageItem = document.createElement('div');\n            baggageItem.style.display = 'flex';\n            baggageItem.style.alignItems = 'center';\n            baggageItem.style.padding = '10px 15px';\n            baggageItem.style.backgroundColor = '#f3f0ff';\n            baggageItem.style.borderRadius = '6px';\n            baggageItem.style.border = '1px solid #e5dbff';\n            const baggageIcon = document.createElement('div');\n            baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n            const baggageInfo = document.createElement('div');\n            baggageInfo.style.display = 'flex';\n            baggageInfo.style.flexDirection = 'column';\n            const baggageType = document.createElement('div');\n            baggageType.textContent = 'Cabin Baggage';\n            baggageType.style.fontWeight = '600';\n            baggageType.style.fontSize = '14px';\n            const baggageDetails = document.createElement('div');\n            baggageDetails.style.fontSize = '12px';\n            baggageDetails.style.color = '#666';\n            baggageDetails.textContent = 'Included';\n            baggageInfo.appendChild(baggageType);\n            baggageInfo.appendChild(baggageDetails);\n            baggageItem.appendChild(baggageIcon);\n            baggageItem.appendChild(baggageInfo);\n            baggageContainer.appendChild(baggageItem);\n          }\n          // Bagages à main\n          if (handBaggage.length > 0) {\n            handBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#fff4e6';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #ffe8cc';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-shopping-bag\" style=\"color: #e8590c; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Hand Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n          offerItem.appendChild(baggageContainer);\n        }\n        offerItem.appendChild(offerDetails);\n        offersList.appendChild(offerItem);\n      });\n      offersSection.appendChild(offersList);\n    }\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n  // Méthode utilitaire pour créer une section\n  createSection(title, iconClass) {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n    return section;\n  }\n  // Méthode utilitaire pour créer une ligne d'information\n  createInfoRow(label, value) {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n    return row;\n  }\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight) {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n  preloadLocations() {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n  setupAutocomplete() {\n    // Charger les locations par type par défaut\n    const departureLocationType = 2; // Type 2 (City) par défaut\n    const arrivalLocationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(departureLocationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(departureLocationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(arrivalLocationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(arrivalLocationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  displayLocation(location) {\n    if (!location) return '';\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n  onSearch() {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n    const formValue = this.searchForm.value;\n    // Créer la requête de recherche entièrement dynamique\n    const request = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [{\n        id: formValue.departureLocation?.id || '',\n        type: 2 // Type 2 (City) par défaut\n      }],\n\n      ArrivalLocations: [{\n        id: formValue.arrivalLocation?.id || '',\n        type: 5 // Type 5 (Airport) par défaut\n      }],\n\n      Passengers: this.getPassengersArray(),\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n    this.productService.searchPrice(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.header.success) {\n          this.searchResults = response.body.flights;\n          // Calculer les prix minimums pour chaque option de filtre\n          this.calculateFilterPrices();\n          // Appliquer le filtre actuel aux résultats\n          this.applyAllFilters();\n          // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n          console.log('Full API Response:', JSON.stringify(response, null, 2));\n          // Analyser les données de disponibilité\n          if (response.body && response.body.flights && response.body.flights.length > 0) {\n            console.group('Availability Analysis');\n            console.log('Total flights:', response.body.flights.length);\n            // Compter les vols avec des offres\n            const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n            console.log('Flights with offers:', flightsWithOffers.length);\n            // Analyser les valeurs de disponibilité\n            const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability !== undefined ? o.availability : o.seatInfo ? o.seatInfo.availableSeatCount : 0));\n            console.log('Availability values:', availabilityValues);\n            // Compter les différentes valeurs de disponibilité\n            const availabilityCounts = availabilityValues.reduce((acc, val) => {\n              if (val !== undefined) {\n                acc[val] = (acc[val] || 0) + 1;\n              }\n              return acc;\n            }, {});\n            console.log('Availability counts:', availabilityCounts);\n            // Vérifier les vols réservables\n            const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true));\n            console.log('Reservable flights:', reservableFlights.length);\n            console.groupEnd();\n          }\n          // Vérifier si searchId existe dans le corps de la réponse\n          if (response.body && response.body.searchId) {\n            this.lastSearchId = response.body.searchId;\n            console.log('Search ID found in body.searchId:', this.lastSearchId);\n          }\n          // Vérifier si searchId existe dans l'en-tête de la réponse\n          else if (response.header && response.header.requestId) {\n            this.lastSearchId = response.header.requestId;\n            console.log('Search ID found in header.requestId:', this.lastSearchId);\n          }\n          // Vérifier d'autres emplacements possibles\n          else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n            this.lastSearchId = response.body.flights[0].id;\n            console.log('Using flight ID as search ID:', this.lastSearchId);\n          } else {\n            console.error('No search ID found in the response!');\n            console.log('Response structure:', Object.keys(response));\n            if (response.body) console.log('Body structure:', Object.keys(response.body));\n            if (response.header) console.log('Header structure:', Object.keys(response.header));\n          }\n        } else {\n          this.errorMessage = 'The search could not be completed. Please try again.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred during the search. Please try again.';\n        console.error('Error searching flights:', error);\n      }\n    });\n  }\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  // Formater la durée en heures et minutes\n  formatDuration(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n  // Formater la date pour l'affichage\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n    const minOffer = flight.offers.reduce((min, offer) => offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight) {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    return availabilityValue > 0;\n  }\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations() {\n    const locationType = 2; // Type 2 (City) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations() {\n    const locationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations() {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      arrivalLocation: departureLocation\n    });\n  }\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString) {\n    if (!dateString) return 'N/A';\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType) {\n    switch (baggageType) {\n      case 1:\n        return 'Cabin Baggage';\n      case 2:\n        return 'Checked Baggage';\n      case 3:\n        return 'Hand Baggage';\n      default:\n        return 'Baggage';\n    }\n  }\n  // Filtrer les bagages par type\n  filterBaggageByType(baggageInformations, type) {\n    if (!baggageInformations || !Array.isArray(baggageInformations)) {\n      return [];\n    }\n    return baggageInformations.filter(baggage => baggage.baggageType === type);\n  }\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType) {\n    switch (passengerType) {\n      case 1:\n        return 'Adult';\n      case 2:\n        return 'Child';\n      case 3:\n        return 'Infant';\n      default:\n        return 'Passenger';\n    }\n  }\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment, nextSegment) {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date || !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n};\n__decorate([HostListener('document:click', ['$event'])], SearchPriceComponent.prototype, \"onDocumentClick\", null);\nSearchPriceComponent = __decorate([Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css'],\n  encapsulation: ViewEncapsulation.None\n})], SearchPriceComponent);", "map": {"version": 3, "names": ["Component", "ViewEncapsulation", "HostListener", "FormGroup", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "of", "FlightClassType", "LocationType", "PassengerType", "SearchPriceComponent", "constructor", "fb", "productService", "router", "departureLocations", "arrivalLocations", "isLoading", "searchResults", "filteredResults", "hasSearched", "errorMessage", "lastSearchId", "showPassengerDropdown", "passengerCounts", "Adult", "Child", "Infant", "currentFilter", "filterOptions", "value", "label", "icon", "sidebarFilters", "stops", "direct", "oneStop", "multiStop", "departureTime", "earlyMorning", "morning", "afternoon", "evening", "arrivalTime", "airlines", "expandedSections", "filterPrices", "passengerTypes", "flightClasses", "PROMO", "ECONOMY", "BUSINESS", "minDate", "Date", "toISOString", "split", "searchForm", "group", "productType", "required", "serviceTypes", "departureLocation", "arrivalLocation", "departureDate", "flightClass", "nonStop", "culture", "currency", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightBaggageGetOption", "onDocumentClick", "event", "clickedElement", "target", "passengerSelector", "document", "querySelector", "contains", "togglePassengerDropdown", "stopPropagation", "closePassengerDropdown", "get<PERSON>assengerCount", "type", "increasePassengerCount", "getTotalPassengers", "decreasePassengerCount", "Object", "values", "reduce", "sum", "count", "getPassengersArray", "passengers", "entries", "for<PERSON>ach", "push", "parseInt", "length", "ngOnInit", "setupAutocomplete", "preloadLocations", "console", "log", "applyFilter", "filterValue", "applyAllFilters", "results", "filter", "flight", "items", "stopCount", "departure", "date", "hours", "getHours", "arrival", "arrivalDate", "selectedAirlines", "_", "selected", "airline", "name", "includes", "sortByPrice", "sortByDuration", "sortByRecommendation", "flights", "sort", "a", "b", "priceA", "getMinPriceAmount", "priceB", "durationA", "duration", "Number", "MAX_VALUE", "durationB", "scoreA", "calculateRecommendationScore", "scoreB", "offers", "item", "offer", "price", "availability", "undefined", "seatInfo", "availableSeatCount", "priceScore", "durationScore", "stopScore", "availabilityScore", "Math", "min", "weights", "amount", "calculateFilterPrices", "resetFilterPrices", "Set", "add", "departureHours", "arrivalHours", "cleanupFilterPrices", "keys", "toggleSection", "section", "toggleStopFilter", "toggleDepartureTimeFilter", "toggleArrivalTimeFilter", "toggleAirlineFilter", "clearAllFilters", "formatPrice", "toFixed", "getAirlineKeys", "showAllDetails", "modalDiv", "createElement", "style", "position", "top", "left", "width", "height", "backgroundColor", "zIndex", "display", "justifyContent", "alignItems", "modalContent", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "color", "fontFamily", "closeButton", "innerHTML", "right", "border", "background", "fontSize", "cursor", "transition", "on<PERSON><PERSON>ver", "onmouseout", "onclick", "body", "<PERSON><PERSON><PERSON><PERSON>", "header", "marginBottom", "paddingBottom", "borderBottom", "logo", "title", "textContent", "margin", "fontWeight", "append<PERSON><PERSON><PERSON>", "detailsContainer", "flexDirection", "gap", "generalInfo", "createSection", "airlineInfo", "thumbnailFull", "airlineLogo", "src", "alt", "marginRight", "airlineIcon", "airlineName", "internationalCode", "flightNumberRow", "createInfoRow", "flightNo", "flightDateRow", "flightDate", "toLocaleDateString", "durationRow", "formatDuration", "classRow", "code", "stopsRow", "routeSection", "routeVisual", "textAlign", "flex", "toLocaleTimeString", "hour", "minute", "departureAirport", "marginTop", "airport", "departureCity", "city", "connectionLine", "line", "plane", "marginLeft", "arrivalAirport", "arrivalCity", "segments", "segmentsTitle", "segmentsList", "segment", "index", "segmentItem", "segmentHeader", "segmentTitle", "segmentDuration", "segmentRoute", "segmentDeparture", "depTime", "depAirport", "arrow", "segmentArrival", "arrTime", "arrAirport", "layover", "currentArrival", "getTime", "nextDeparture", "layoverTime", "floor", "offersSection", "offersList", "offerItem", "offerHeader", "offerTitle", "offerPrice", "offerDetails", "gridTemplateColumns", "availabilityValue", "expiresOn", "expires", "toLocaleString", "brandedFare", "reservableInfo", "reservable", "baggageInformations", "baggageTitle", "baggageContainer", "checkedBaggage", "baggageType", "cabinBaggage", "handBaggage", "baggage", "baggageItem", "baggageIcon", "baggageInfo", "baggageDetails", "detailsText", "weight", "piece", "services", "servicesSection", "servicesList", "listStyle", "service", "serviceItem", "iconClass", "section<PERSON><PERSON><PERSON>", "className", "sectionTitle", "row", "labelElement", "valueElement", "selectThisFlight", "offerId", "id", "searchId", "navigate", "queryParams", "error", "departureLocationType", "arrivalLocationType", "getLocationsByType", "subscribe", "locations", "get", "valueChanges", "pipe", "location", "toLowerCase", "displayLocation", "displayText", "Airport", "onSearch", "invalid", "markFormGroupTouched", "formValue", "request", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "ArrivalLocations", "Passengers", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "searchPrice", "next", "response", "success", "JSON", "stringify", "flightsWithOffers", "f", "availabilityValues", "flatMap", "o", "availabilityCounts", "acc", "val", "reservableFlights", "some", "groupEnd", "requestId", "messages", "message", "formGroup", "controls", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "minutes", "mins", "formatDate", "dateString", "weekday", "day", "month", "getMinPrice", "min<PERSON>ffer", "formattedAmount", "isFlightAvailable", "showAllDepartureLocations", "locationType", "input", "getElementById", "focus", "dispatchEvent", "Event", "showAllArrivalLocations", "swapLocations", "patchValue", "formatExpirationDate", "getBaggageTypeName", "filterBaggageByType", "Array", "isArray", "getPassengerTypeName", "passengerType", "calculateLayoverTime", "currentSegment", "nextSegment", "diffMs", "diffMins", "__decorate", "selector", "templateUrl", "styleUrls", "encapsulation", "None"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation, HostListener } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { Observable, of } from 'rxjs';\nimport { ProductService } from '../../../services/product.service';\nimport { LocationOption } from '../../../models/price-search-request.model';\nimport { PriceSearchRequest, Passenger } from '../../../models/price-search-request.model';\nimport { PriceSearchResponse, Flight } from '../../../models/price-search-response.model';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css'],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchPriceComponent implements OnInit {\n  searchForm: FormGroup;\n  departureLocations: LocationOption[] = [];\n  arrivalLocations: LocationOption[] = [];\n  isLoading = false;\n  searchResults: Flight[] = [];\n  filteredResults: Flight[] = [];\n  hasSearched = false;\n  errorMessage = '';\n  lastSearchId = '';\n\n  // Passenger selector properties\n  showPassengerDropdown = false;\n  passengerCounts: { [key: number]: number } = {\n    [PassengerType.Adult]: 1,\n    [PassengerType.Child]: 0,\n    [PassengerType.Infant]: 0\n  };\n\n  // Filter options\n  currentFilter: string = 'recommended';\n  filterOptions = [\n    { value: 'recommended', label: 'Recommended', icon: 'fa-star' },\n    { value: 'cheapest', label: 'Cheapest', icon: 'fa-dollar-sign' },\n    { value: 'shortest', label: 'Shortest', icon: 'fa-clock' }\n  ];\n\n  // Sidebar filter options\n  sidebarFilters = {\n    stops: {\n      direct: false,\n      oneStop: false,\n      multiStop: false\n    },\n    departureTime: {\n      earlyMorning: false, // 00:00 - 08:00\n      morning: false,      // 08:00 - 12:00\n      afternoon: false,    // 12:00 - 18:00\n      evening: false       // 18:00 - 00:00\n    },\n    arrivalTime: {\n      earlyMorning: false, // 00:00 - 08:00\n      morning: false,      // 08:00 - 12:00\n      afternoon: false,    // 12:00 - 18:00\n      evening: false       // 18:00 - 00:00\n    },\n    airlines: {} as { [key: string]: boolean }  // Will be populated dynamically based on search results\n  };\n\n  // Expanded sections in sidebar\n  expandedSections: { [key: string]: boolean } = {\n    stops: true,\n    departureTime: true,\n    arrivalTime: true,\n    airlines: true\n  };\n\n  // Price ranges for each filter option (will be calculated from results)\n  filterPrices = {\n    stops: {\n      direct: 0,\n      oneStop: 0,\n      multiStop: 0\n    },\n    departureTime: {\n      earlyMorning: 0,\n      morning: 0,\n      afternoon: 0,\n      evening: 0\n    },\n    arrivalTime: {\n      earlyMorning: 0,\n      morning: 0,\n      afternoon: 0,\n      evening: 0\n    },\n    airlines: {} as { [key: string]: number }\n  };\n\n  // Passenger type options\n  passengerTypes = [\n    { value: PassengerType.Adult, label: 'Adult' },\n    { value: PassengerType.Child, label: 'Child' },\n    { value: PassengerType.Infant, label: 'Infant' }\n  ];\n\n  // Flight class options\n  flightClasses = [\n    { value: FlightClassType.PROMO, label: 'Promo' },\n    { value: FlightClassType.ECONOMY, label: 'Economy' },\n    { value: FlightClassType.BUSINESS, label: 'Business' }\n  ];\n\n  // Date minimale (aujourd'hui)\n  minDate: string;\n\n  constructor(\n    private fb: FormBuilder,\n    private productService: ProductService,\n    private router: Router\n  ) {\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required], // Flight par défaut\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      arrivalLocation: ['', Validators.required],\n      departureDate: [this.minDate, Validators.required],\n\n      // Options de vol\n      flightClass: [0, Validators.required], // PROMO par défaut\n      nonStop: [false],\n\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n\n  // Close dropdown when clicking outside\n  @HostListener('document:click', ['$event'])\n  onDocumentClick(event: MouseEvent): void {\n    // Check if click is outside the passenger dropdown\n    const clickedElement = event.target as HTMLElement;\n    const passengerSelector = document.querySelector('.passengers-selector');\n\n    if (passengerSelector && !passengerSelector.contains(clickedElement)) {\n      this.showPassengerDropdown = false;\n    }\n  }\n\n  // Toggle passenger dropdown\n  togglePassengerDropdown(event: Event): void {\n    event.stopPropagation();\n    this.showPassengerDropdown = !this.showPassengerDropdown;\n  }\n\n  // Close passenger dropdown\n  closePassengerDropdown(): void {\n    this.showPassengerDropdown = false;\n  }\n\n  // Get passenger count for a specific type\n  getPassengerCount(type: number): number {\n    return this.passengerCounts[type] || 0;\n  }\n\n  // Increase passenger count\n  increasePassengerCount(type: number): void {\n    if (this.getTotalPassengers() < 9) {\n      this.passengerCounts[type] = (this.passengerCounts[type] || 0) + 1;\n    }\n  }\n\n  // Decrease passenger count\n  decreasePassengerCount(type: number): void {\n    if (this.passengerCounts[type] > 0) {\n      this.passengerCounts[type] -= 1;\n    }\n  }\n\n  // Get total number of passengers\n  getTotalPassengers(): number {\n    return Object.values(this.passengerCounts).reduce((sum, count) => sum + count, 0);\n  }\n\n  // Get passengers array for API request\n  getPassengersArray(): any[] {\n    const passengers = [];\n\n    // Add each passenger type with count > 0\n    Object.entries(this.passengerCounts).forEach(([type, count]) => {\n      if (count > 0) {\n        passengers.push({\n          type: parseInt(type),\n          count: count\n        });\n      }\n    });\n\n    // Ensure at least one passenger is included\n    if (passengers.length === 0) {\n      passengers.push({\n        type: PassengerType.Adult,\n        count: 1\n      });\n    }\n\n    return passengers;\n  }\n\n  ngOnInit(): void {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n\n  // Méthode pour appliquer le filtre sélectionné\n  applyFilter(filterValue: string): void {\n    this.currentFilter = filterValue;\n    this.applyAllFilters();\n  }\n\n  // Méthode pour appliquer tous les filtres (top et sidebar)\n  applyAllFilters(): void {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      this.filteredResults = [];\n      return;\n    }\n\n    // Étape 1: Appliquer les filtres de la sidebar\n    let results = [...this.searchResults];\n\n    // Filtrer par nombre d'escales\n    if (this.sidebarFilters.stops.direct || this.sidebarFilters.stops.oneStop || this.sidebarFilters.stops.multiStop) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0) return false;\n\n        const stopCount = flight.items[0].stopCount || 0;\n\n        return (this.sidebarFilters.stops.direct && stopCount === 0) ||\n               (this.sidebarFilters.stops.oneStop && stopCount === 1) ||\n               (this.sidebarFilters.stops.multiStop && stopCount >= 2);\n      });\n    }\n\n    // Filtrer par horaire de départ\n    if (this.sidebarFilters.departureTime.earlyMorning ||\n        this.sidebarFilters.departureTime.morning ||\n        this.sidebarFilters.departureTime.afternoon ||\n        this.sidebarFilters.departureTime.evening) {\n\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].departure || !flight.items[0].departure.date) {\n          return false;\n        }\n\n        const departureDate = new Date(flight.items[0].departure.date);\n        const hours = departureDate.getHours();\n\n        return (this.sidebarFilters.departureTime.earlyMorning && hours >= 0 && hours < 8) ||\n               (this.sidebarFilters.departureTime.morning && hours >= 8 && hours < 12) ||\n               (this.sidebarFilters.departureTime.afternoon && hours >= 12 && hours < 18) ||\n               (this.sidebarFilters.departureTime.evening && hours >= 18 && hours < 24);\n      });\n    }\n\n    // Filtrer par horaire d'arrivée\n    if (this.sidebarFilters.arrivalTime.earlyMorning ||\n        this.sidebarFilters.arrivalTime.morning ||\n        this.sidebarFilters.arrivalTime.afternoon ||\n        this.sidebarFilters.arrivalTime.evening) {\n\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].arrival || !flight.items[0].arrival.date) {\n          return false;\n        }\n\n        const arrivalDate = new Date(flight.items[0].arrival.date);\n        const hours = arrivalDate.getHours();\n\n        return (this.sidebarFilters.arrivalTime.earlyMorning && hours >= 0 && hours < 8) ||\n               (this.sidebarFilters.arrivalTime.morning && hours >= 8 && hours < 12) ||\n               (this.sidebarFilters.arrivalTime.afternoon && hours >= 12 && hours < 18) ||\n               (this.sidebarFilters.arrivalTime.evening && hours >= 18 && hours < 24);\n      });\n    }\n\n    // Filtrer par compagnie aérienne\n    const selectedAirlines = Object.entries(this.sidebarFilters.airlines)\n      .filter(([_, selected]) => selected)\n      .map(([airline, _]) => airline);\n\n    if (selectedAirlines.length > 0) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].airline || !flight.items[0].airline.name) {\n          return false;\n        }\n\n        return selectedAirlines.includes(flight.items[0].airline.name);\n      });\n    }\n\n    // Étape 2: Appliquer le tri selon le filtre sélectionné en haut\n    switch (this.currentFilter) {\n      case 'cheapest':\n        this.filteredResults = this.sortByPrice(results);\n        break;\n      case 'shortest':\n        this.filteredResults = this.sortByDuration(results);\n        break;\n      case 'recommended':\n      default:\n        this.filteredResults = this.sortByRecommendation(results);\n        break;\n    }\n  }\n\n  // Trier les vols par prix (du moins cher au plus cher)\n  private sortByPrice(flights: Flight[]): Flight[] {\n    return flights.sort((a, b) => {\n      const priceA = this.getMinPriceAmount(a);\n      const priceB = this.getMinPriceAmount(b);\n      return priceA - priceB;\n    });\n  }\n\n  // Trier les vols par durée (du plus court au plus long)\n  private sortByDuration(flights: Flight[]): Flight[] {\n    return flights.sort((a, b) => {\n      const durationA = a.items && a.items[0] ? a.items[0].duration : Number.MAX_VALUE;\n      const durationB = b.items && b.items[0] ? b.items[0].duration : Number.MAX_VALUE;\n      return durationA - durationB;\n    });\n  }\n\n  // Trier les vols par recommandation (combinaison de prix, durée et autres facteurs)\n  private sortByRecommendation(flights: Flight[]): Flight[] {\n    return flights.sort((a, b) => {\n      // Calculer un score pour chaque vol basé sur plusieurs facteurs\n      const scoreA = this.calculateRecommendationScore(a);\n      const scoreB = this.calculateRecommendationScore(b);\n      return scoreB - scoreA; // Ordre décroissant (score plus élevé = meilleur)\n    });\n  }\n\n  // Calculer un score de recommandation pour un vol\n  private calculateRecommendationScore(flight: Flight): number {\n    if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n      return 0;\n    }\n\n    const item = flight.items[0];\n    const offer = flight.offers[0];\n\n    // Facteurs à considérer pour le score\n    const price = this.getMinPriceAmount(flight);\n    const duration = item.duration;\n    const stopCount = item.stopCount || 0;\n    const availability = offer.availability !== undefined ? offer.availability :\n                        (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n\n    // Normaliser les valeurs (plus le score est élevé, meilleur est le vol)\n    const priceScore = 1000 / (price + 100); // Inverse du prix (moins cher = meilleur score)\n    const durationScore = 1000 / (duration + 100); // Inverse de la durée (plus court = meilleur score)\n    const stopScore = 1 / (stopCount + 1); // Moins d'escales = meilleur score\n    const availabilityScore = Math.min(availability, 10) / 10; // Plus de disponibilité = meilleur score\n\n    // Pondération des facteurs (ajustable selon les préférences)\n    const weights = {\n      price: 0.4,      // 40% importance pour le prix\n      duration: 0.3,   // 30% importance pour la durée\n      stops: 0.2,      // 20% importance pour les escales\n      availability: 0.1 // 10% importance pour la disponibilité\n    };\n\n    // Calculer le score final pondéré\n    return (\n      priceScore * weights.price +\n      durationScore * weights.duration +\n      stopScore * weights.stops +\n      availabilityScore * weights.availability\n    );\n  }\n\n  // Obtenir le montant du prix minimum pour un vol\n  private getMinPriceAmount(flight: Flight): number {\n    if (!flight.offers || flight.offers.length === 0) {\n      return Number.MAX_VALUE;\n    }\n\n    return flight.offers.reduce((min, offer) =>\n      offer.price && offer.price.amount < min ? offer.price.amount : min,\n      flight.offers[0].price ? flight.offers[0].price.amount : Number.MAX_VALUE\n    );\n  }\n\n  // Calculer les prix minimums pour chaque option de filtre\n  calculateFilterPrices(): void {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      return;\n    }\n\n    // Réinitialiser les prix\n    this.resetFilterPrices();\n\n    // Collecter toutes les compagnies aériennes\n    const airlines = new Set<string>();\n\n    // Parcourir tous les vols pour calculer les prix minimums\n    this.searchResults.forEach(flight => {\n      if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n        return;\n      }\n\n      const item = flight.items[0];\n      const price = this.getMinPriceAmount(flight);\n\n      // Ajouter la compagnie aérienne à la liste\n      if (item.airline && item.airline.name) {\n        airlines.add(item.airline.name);\n\n        // Initialiser le prix pour cette compagnie si nécessaire\n        if (!(item.airline.name in this.filterPrices.airlines)) {\n          this.filterPrices.airlines[item.airline.name] = Number.MAX_VALUE;\n        }\n\n        // Mettre à jour le prix minimum pour cette compagnie\n        this.filterPrices.airlines[item.airline.name] = Math.min(\n          this.filterPrices.airlines[item.airline.name],\n          price\n        );\n      }\n\n      // Mettre à jour les prix par nombre d'escales\n      const stopCount = item.stopCount || 0;\n      if (stopCount === 0) {\n        this.filterPrices.stops.direct = Math.min(this.filterPrices.stops.direct, price);\n      } else if (stopCount === 1) {\n        this.filterPrices.stops.oneStop = Math.min(this.filterPrices.stops.oneStop, price);\n      } else {\n        this.filterPrices.stops.multiStop = Math.min(this.filterPrices.stops.multiStop, price);\n      }\n\n      // Mettre à jour les prix par horaire de départ\n      if (item.departure && item.departure.date) {\n        const departureDate = new Date(item.departure.date);\n        const departureHours = departureDate.getHours();\n\n        if (departureHours >= 0 && departureHours < 8) {\n          this.filterPrices.departureTime.earlyMorning = Math.min(this.filterPrices.departureTime.earlyMorning, price);\n        } else if (departureHours >= 8 && departureHours < 12) {\n          this.filterPrices.departureTime.morning = Math.min(this.filterPrices.departureTime.morning, price);\n        } else if (departureHours >= 12 && departureHours < 18) {\n          this.filterPrices.departureTime.afternoon = Math.min(this.filterPrices.departureTime.afternoon, price);\n        } else {\n          this.filterPrices.departureTime.evening = Math.min(this.filterPrices.departureTime.evening, price);\n        }\n      }\n\n      // Mettre à jour les prix par horaire d'arrivée\n      if (item.arrival && item.arrival.date) {\n        const arrivalDate = new Date(item.arrival.date);\n        const arrivalHours = arrivalDate.getHours();\n\n        if (arrivalHours >= 0 && arrivalHours < 8) {\n          this.filterPrices.arrivalTime.earlyMorning = Math.min(this.filterPrices.arrivalTime.earlyMorning, price);\n        } else if (arrivalHours >= 8 && arrivalHours < 12) {\n          this.filterPrices.arrivalTime.morning = Math.min(this.filterPrices.arrivalTime.morning, price);\n        } else if (arrivalHours >= 12 && arrivalHours < 18) {\n          this.filterPrices.arrivalTime.afternoon = Math.min(this.filterPrices.arrivalTime.afternoon, price);\n        } else {\n          this.filterPrices.arrivalTime.evening = Math.min(this.filterPrices.arrivalTime.evening, price);\n        }\n      }\n    });\n\n    // Initialiser les filtres de compagnies aériennes\n    airlines.forEach(airline => {\n      if (!(airline in this.sidebarFilters.airlines)) {\n        this.sidebarFilters.airlines[airline] = false;\n      }\n    });\n\n    // Remplacer les valeurs MAX_VALUE par 0 pour les options sans vols\n    this.cleanupFilterPrices();\n  }\n\n  // Réinitialiser les prix des filtres\n  private resetFilterPrices(): void {\n    this.filterPrices = {\n      stops: {\n        direct: Number.MAX_VALUE,\n        oneStop: Number.MAX_VALUE,\n        multiStop: Number.MAX_VALUE\n      },\n      departureTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      arrivalTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      airlines: {}\n    };\n  }\n\n  // Nettoyer les prix des filtres (remplacer MAX_VALUE par 0)\n  private cleanupFilterPrices(): void {\n    // Escales\n    if (this.filterPrices.stops.direct === Number.MAX_VALUE) this.filterPrices.stops.direct = 0;\n    if (this.filterPrices.stops.oneStop === Number.MAX_VALUE) this.filterPrices.stops.oneStop = 0;\n    if (this.filterPrices.stops.multiStop === Number.MAX_VALUE) this.filterPrices.stops.multiStop = 0;\n\n    // Horaires de départ\n    if (this.filterPrices.departureTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.departureTime.earlyMorning = 0;\n    if (this.filterPrices.departureTime.morning === Number.MAX_VALUE) this.filterPrices.departureTime.morning = 0;\n    if (this.filterPrices.departureTime.afternoon === Number.MAX_VALUE) this.filterPrices.departureTime.afternoon = 0;\n    if (this.filterPrices.departureTime.evening === Number.MAX_VALUE) this.filterPrices.departureTime.evening = 0;\n\n    // Horaires d'arrivée\n    if (this.filterPrices.arrivalTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.arrivalTime.earlyMorning = 0;\n    if (this.filterPrices.arrivalTime.morning === Number.MAX_VALUE) this.filterPrices.arrivalTime.morning = 0;\n    if (this.filterPrices.arrivalTime.afternoon === Number.MAX_VALUE) this.filterPrices.arrivalTime.afternoon = 0;\n    if (this.filterPrices.arrivalTime.evening === Number.MAX_VALUE) this.filterPrices.arrivalTime.evening = 0;\n\n    // Compagnies aériennes\n    Object.keys(this.filterPrices.airlines).forEach(airline => {\n      if (this.filterPrices.airlines[airline] === Number.MAX_VALUE) {\n        this.filterPrices.airlines[airline] = 0;\n      }\n    });\n  }\n\n  // Basculer l'état d'expansion d'une section\n  toggleSection(section: string): void {\n    this.expandedSections[section] = !this.expandedSections[section];\n  }\n\n  // Basculer un filtre d'escale\n  toggleStopFilter(filter: 'direct' | 'oneStop' | 'multiStop'): void {\n    this.sidebarFilters.stops[filter] = !this.sidebarFilters.stops[filter];\n    this.applyAllFilters();\n  }\n\n  // Basculer un filtre d'horaire de départ\n  toggleDepartureTimeFilter(filter: 'earlyMorning' | 'morning' | 'afternoon' | 'evening'): void {\n    this.sidebarFilters.departureTime[filter] = !this.sidebarFilters.departureTime[filter];\n    this.applyAllFilters();\n  }\n\n  // Basculer un filtre d'horaire d'arrivée\n  toggleArrivalTimeFilter(filter: 'earlyMorning' | 'morning' | 'afternoon' | 'evening'): void {\n    this.sidebarFilters.arrivalTime[filter] = !this.sidebarFilters.arrivalTime[filter];\n    this.applyAllFilters();\n  }\n\n  // Basculer un filtre de compagnie aérienne\n  toggleAirlineFilter(airline: string): void {\n    this.sidebarFilters.airlines[airline] = !this.sidebarFilters.airlines[airline];\n    this.applyAllFilters();\n  }\n\n  // Effacer tous les filtres\n  clearAllFilters(): void {\n    // Réinitialiser les filtres d'escales\n    this.sidebarFilters.stops.direct = false;\n    this.sidebarFilters.stops.oneStop = false;\n    this.sidebarFilters.stops.multiStop = false;\n\n    // Réinitialiser les filtres d'horaires de départ\n    this.sidebarFilters.departureTime.earlyMorning = false;\n    this.sidebarFilters.departureTime.morning = false;\n    this.sidebarFilters.departureTime.afternoon = false;\n    this.sidebarFilters.departureTime.evening = false;\n\n    // Réinitialiser les filtres d'horaires d'arrivée\n    this.sidebarFilters.arrivalTime.earlyMorning = false;\n    this.sidebarFilters.arrivalTime.morning = false;\n    this.sidebarFilters.arrivalTime.afternoon = false;\n    this.sidebarFilters.arrivalTime.evening = false;\n\n    // Réinitialiser les filtres de compagnies aériennes\n    Object.keys(this.sidebarFilters.airlines).forEach(airline => {\n      this.sidebarFilters.airlines[airline] = false;\n    });\n\n    // Appliquer les filtres (qui seront tous désactivés)\n    this.applyAllFilters();\n  }\n\n  // Formater le prix pour l'affichage\n  formatPrice(price: number): string {\n    if (price === 0) return '-';\n    return price.toFixed(0) + ' €';\n  }\n\n  // Obtenir les clés des compagnies aériennes\n  getAirlineKeys(): string[] {\n    return Object.keys(this.sidebarFilters.airlines);\n  }\n\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight: Flight): void {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n\n    header.appendChild(logo);\n    header.appendChild(title);\n\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n\n        generalInfo.appendChild(airlineInfo);\n      }\n\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n\n      routeSection.appendChild(routeVisual);\n\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n\n          segmentsList.appendChild(segmentItem);\n\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n\n            segmentsList.appendChild(layover);\n          }\n        });\n\n        routeSection.appendChild(segmentsList);\n      }\n    }\n\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability :\n                                 (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n\n          const baggageContainer = document.createElement('div');\n          baggageContainer.style.display = 'flex';\n          baggageContainer.style.flexDirection = 'column';\n          baggageContainer.style.gap = '10px';\n          baggageContainer.style.marginBottom = '15px';\n\n          // Filtrer et regrouper les bagages par type\n          const checkedBaggage = offer.baggageInformations.filter(b => b.baggageType === 2);\n          const cabinBaggage = offer.baggageInformations.filter(b => b.baggageType === 1);\n          const handBaggage = offer.baggageInformations.filter(b => b.baggageType === 3);\n\n          // Bagages en soute\n          if (checkedBaggage.length > 0) {\n            checkedBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#e7f5ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #c5e1f9';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-suitcase\" style=\"color: #4a6fa5; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Checked Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n\n          // Bagages cabine\n          if (cabinBaggage.length > 0) {\n            cabinBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#f3f0ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #e5dbff';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Cabin Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          } else {\n            // Ajouter un bagage cabine par défaut si aucun n'est spécifié\n            const baggageItem = document.createElement('div');\n            baggageItem.style.display = 'flex';\n            baggageItem.style.alignItems = 'center';\n            baggageItem.style.padding = '10px 15px';\n            baggageItem.style.backgroundColor = '#f3f0ff';\n            baggageItem.style.borderRadius = '6px';\n            baggageItem.style.border = '1px solid #e5dbff';\n\n            const baggageIcon = document.createElement('div');\n            baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n\n            const baggageInfo = document.createElement('div');\n            baggageInfo.style.display = 'flex';\n            baggageInfo.style.flexDirection = 'column';\n\n            const baggageType = document.createElement('div');\n            baggageType.textContent = 'Cabin Baggage';\n            baggageType.style.fontWeight = '600';\n            baggageType.style.fontSize = '14px';\n\n            const baggageDetails = document.createElement('div');\n            baggageDetails.style.fontSize = '12px';\n            baggageDetails.style.color = '#666';\n            baggageDetails.textContent = 'Included';\n\n            baggageInfo.appendChild(baggageType);\n            baggageInfo.appendChild(baggageDetails);\n\n            baggageItem.appendChild(baggageIcon);\n            baggageItem.appendChild(baggageInfo);\n            baggageContainer.appendChild(baggageItem);\n          }\n\n          // Bagages à main\n          if (handBaggage.length > 0) {\n            handBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#fff4e6';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #ffe8cc';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-shopping-bag\" style=\"color: #e8590c; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Hand Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n\n          offerItem.appendChild(baggageContainer);\n        }\n\n        offerItem.appendChild(offerDetails);\n\n        offersList.appendChild(offerItem);\n      });\n\n      offersSection.appendChild(offersList);\n    }\n\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n\n  // Méthode utilitaire pour créer une section\n  private createSection(title: string, iconClass: string): HTMLElement {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n\n    return section;\n  }\n\n  // Méthode utilitaire pour créer une ligne d'information\n  private createInfoRow(label: string, value: string): HTMLElement {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n\n    return row;\n  }\n\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight: Flight): void {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n\n  preloadLocations(): void {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n\n  setupAutocomplete(): void {\n    // Charger les locations par type par défaut\n    const departureLocationType = 2; // Type 2 (City) par défaut\n    const arrivalLocationType = 5;   // Type 5 (Airport) par défaut\n\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(departureLocationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(departureLocationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.departureLocations = locations;\n      });\n\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(arrivalLocationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(arrivalLocationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n  }\n\n  displayLocation(location: LocationOption | null): string {\n    if (!location) return '';\n\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n\n  onSearch(): void {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n\n    const formValue = this.searchForm.value;\n\n    // Créer la requête de recherche entièrement dynamique\n    const request: PriceSearchRequest = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [\n        {\n          id: formValue.departureLocation?.id || '',\n          type: 2 // Type 2 (City) par défaut\n        }\n      ],\n      ArrivalLocations: [\n        {\n          id: formValue.arrivalLocation?.id || '',\n          type: 5 // Type 5 (Airport) par défaut\n        }\n      ],\n      Passengers: this.getPassengersArray(),\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n\n    this.productService.searchPrice(request)\n      .subscribe({\n        next: (response: PriceSearchResponse) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n\n            // Calculer les prix minimums pour chaque option de filtre\n            this.calculateFilterPrices();\n\n            // Appliquer le filtre actuel aux résultats\n            this.applyAllFilters();\n\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            console.log('Full API Response:', JSON.stringify(response, null, 2));\n\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o =>\n                o.availability !== undefined ? o.availability : (o.seatInfo ? o.seatInfo.availableSeatCount : 0)\n              ));\n              console.log('Availability values:', availabilityValues);\n\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc, val) => {\n                if (val !== undefined) {\n                  acc[val] = (acc[val] || 0) + 1;\n                }\n                return acc;\n              }, {} as Record<number, number>);\n              console.log('Availability counts:', availabilityCounts);\n\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f =>\n                f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true)\n              );\n              console.log('Reservable flights:', reservableFlights.length);\n\n              console.groupEnd();\n            }\n\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n  }\n\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup: FormGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n\n  // Formater la durée en heures et minutes\n  formatDuration(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n\n  // Formater la date pour l'affichage\n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight: Flight): string {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n\n    const minOffer = flight.offers.reduce((min, offer) =>\n      offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight: Flight): boolean {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability :\n                             (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n    return availabilityValue > 0;\n  }\n\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations(): void {\n    const locationType = 2; // Type 2 (City) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations(): void {\n    const locationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations(): void {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      arrivalLocation: departureLocation\n    });\n  }\n\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType: number): string {\n    switch (baggageType) {\n      case 1: return 'Cabin Baggage';\n      case 2: return 'Checked Baggage';\n      case 3: return 'Hand Baggage';\n      default: return 'Baggage';\n    }\n  }\n\n  // Filtrer les bagages par type\n  filterBaggageByType(baggageInformations: any[], type: number): any[] {\n    if (!baggageInformations || !Array.isArray(baggageInformations)) {\n      return [];\n    }\n    return baggageInformations.filter(baggage => baggage.baggageType === type);\n  }\n\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType: number): string {\n    switch (passengerType) {\n      case 1: return 'Adult';\n      case 2: return 'Child';\n      case 3: return 'Infant';\n      default: return 'Passenger';\n    }\n  }\n\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment: any, nextSegment: any): string {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date ||\n        !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,iBAAiB,EAAEC,YAAY,QAAQ,eAAe;AAClF,SAAsBC,SAAS,EAAEC,UAAU,QAAmB,gBAAgB;AAE9E,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnF,SAAqBC,EAAE,QAAQ,MAAM;AAKrC,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;AAQnF,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAgG/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAjGhB,KAAAC,kBAAkB,GAAqB,EAAE;IACzC,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAa,EAAE;IAC5B,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,YAAY,GAAG,EAAE;IAEjB;IACA,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,eAAe,GAA8B;MAC3C,CAACf,aAAa,CAACgB,KAAK,GAAG,CAAC;MACxB,CAAChB,aAAa,CAACiB,KAAK,GAAG,CAAC;MACxB,CAACjB,aAAa,CAACkB,MAAM,GAAG;KACzB;IAED;IACA,KAAAC,aAAa,GAAW,aAAa;IACrC,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAS,CAAE,EAC/D;MAAEF,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAgB,CAAE,EAChE;MAAEF,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAU,CAAE,CAC3D;IAED;IACA,KAAAC,cAAc,GAAG;MACfC,KAAK,EAAE;QACLC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE;OACZ;MACDC,aAAa,EAAE;QACbC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,KAAK,CAAO;OACtB;;MACDC,WAAW,EAAE;QACXJ,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,KAAK,CAAO;OACtB;;MACDE,QAAQ,EAAE,EAAgC,CAAE;KAC7C;IAED;IACA,KAAAC,gBAAgB,GAA+B;MAC7CX,KAAK,EAAE,IAAI;MACXI,aAAa,EAAE,IAAI;MACnBK,WAAW,EAAE,IAAI;MACjBC,QAAQ,EAAE;KACX;IAED;IACA,KAAAE,YAAY,GAAG;MACbZ,KAAK,EAAE;QACLC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE;OACZ;MACDC,aAAa,EAAE;QACbC,YAAY,EAAE,CAAC;QACfC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE;OACV;MACDC,WAAW,EAAE;QACXJ,YAAY,EAAE,CAAC;QACfC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE;OACV;MACDE,QAAQ,EAAE;KACX;IAED;IACA,KAAAG,cAAc,GAAG,CACf;MAAEjB,KAAK,EAAErB,aAAa,CAACgB,KAAK;MAAEM,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAErB,aAAa,CAACiB,KAAK;MAAEK,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAErB,aAAa,CAACkB,MAAM;MAAEI,KAAK,EAAE;IAAQ,CAAE,CACjD;IAED;IACA,KAAAiB,aAAa,GAAG,CACd;MAAElB,KAAK,EAAEvB,eAAe,CAAC0C,KAAK;MAAElB,KAAK,EAAE;IAAO,CAAE,EAChD;MAAED,KAAK,EAAEvB,eAAe,CAAC2C,OAAO;MAAEnB,KAAK,EAAE;IAAS,CAAE,EACpD;MAAED,KAAK,EAAEvB,eAAe,CAAC4C,QAAQ;MAAEpB,KAAK,EAAE;IAAU,CAAE,CACvD;IAUC;IACA,IAAI,CAACqB,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAErD;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC5C,EAAE,CAAC6C,KAAK,CAAC;MAC9B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAEzD,UAAU,CAAC0D,QAAQ,CAAC;MACrCC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE3D,UAAU,CAAC0D,QAAQ,CAAC;MAC1CE,iBAAiB,EAAE,CAAC,EAAE,EAAE5D,UAAU,CAAC0D,QAAQ,CAAC;MAC5CG,eAAe,EAAE,CAAC,EAAE,EAAE7D,UAAU,CAAC0D,QAAQ,CAAC;MAC1CI,aAAa,EAAE,CAAC,IAAI,CAACX,OAAO,EAAEnD,UAAU,CAAC0D,QAAQ,CAAC;MAElD;MACAK,WAAW,EAAE,CAAC,CAAC,EAAE/D,UAAU,CAAC0D,QAAQ,CAAC;MACrCM,OAAO,EAAE,CAAC,KAAK,CAAC;MAEhB;MACAC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBC,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,wBAAwB,EAAE,CAAC,KAAK,CAAC;MACjCC,6BAA6B,EAAE,CAAC,IAAI,CAAC;MACrCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAE5B;MACAC,sBAAsB,EAAE,CAAC,CAAC;KAC3B,CAAC;EACJ;EAEA;EAEAC,eAAeA,CAACC,KAAiB;IAC/B;IACA,MAAMC,cAAc,GAAGD,KAAK,CAACE,MAAqB;IAClD,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC;IAExE,IAAIF,iBAAiB,IAAI,CAACA,iBAAiB,CAACG,QAAQ,CAACL,cAAc,CAAC,EAAE;MACpE,IAAI,CAACpD,qBAAqB,GAAG,KAAK;;EAEtC;EAEA;EACA0D,uBAAuBA,CAACP,KAAY;IAClCA,KAAK,CAACQ,eAAe,EAAE;IACvB,IAAI,CAAC3D,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEA;EACA4D,sBAAsBA,CAAA;IACpB,IAAI,CAAC5D,qBAAqB,GAAG,KAAK;EACpC;EAEA;EACA6D,iBAAiBA,CAACC,IAAY;IAC5B,OAAO,IAAI,CAAC7D,eAAe,CAAC6D,IAAI,CAAC,IAAI,CAAC;EACxC;EAEA;EACAC,sBAAsBA,CAACD,IAAY;IACjC,IAAI,IAAI,CAACE,kBAAkB,EAAE,GAAG,CAAC,EAAE;MACjC,IAAI,CAAC/D,eAAe,CAAC6D,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC7D,eAAe,CAAC6D,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;;EAEtE;EAEA;EACAG,sBAAsBA,CAACH,IAAY;IACjC,IAAI,IAAI,CAAC7D,eAAe,CAAC6D,IAAI,CAAC,GAAG,CAAC,EAAE;MAClC,IAAI,CAAC7D,eAAe,CAAC6D,IAAI,CAAC,IAAI,CAAC;;EAEnC;EAEA;EACAE,kBAAkBA,CAAA;IAChB,OAAOE,MAAM,CAACC,MAAM,CAAC,IAAI,CAAClE,eAAe,CAAC,CAACmE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;EACnF;EAEA;EACAC,kBAAkBA,CAAA;IAChB,MAAMC,UAAU,GAAG,EAAE;IAErB;IACAN,MAAM,CAACO,OAAO,CAAC,IAAI,CAACxE,eAAe,CAAC,CAACyE,OAAO,CAAC,CAAC,CAACZ,IAAI,EAAEQ,KAAK,CAAC,KAAI;MAC7D,IAAIA,KAAK,GAAG,CAAC,EAAE;QACbE,UAAU,CAACG,IAAI,CAAC;UACdb,IAAI,EAAEc,QAAQ,CAACd,IAAI,CAAC;UACpBQ,KAAK,EAAEA;SACR,CAAC;;IAEN,CAAC,CAAC;IAEF;IACA,IAAIE,UAAU,CAACK,MAAM,KAAK,CAAC,EAAE;MAC3BL,UAAU,CAACG,IAAI,CAAC;QACdb,IAAI,EAAE5E,aAAa,CAACgB,KAAK;QACzBoE,KAAK,EAAE;OACR,CAAC;;IAGJ,OAAOE,UAAU;EACnB;EAEAM,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACAC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EACjF;EAEA;EACAC,WAAWA,CAACC,WAAmB;IAC7B,IAAI,CAAC/E,aAAa,GAAG+E,WAAW;IAChC,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;EACAA,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC1F,aAAa,IAAI,IAAI,CAACA,aAAa,CAACkF,MAAM,KAAK,CAAC,EAAE;MAC1D,IAAI,CAACjF,eAAe,GAAG,EAAE;MACzB;;IAGF;IACA,IAAI0F,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC3F,aAAa,CAAC;IAErC;IACA,IAAI,IAAI,CAACe,cAAc,CAACC,KAAK,CAACC,MAAM,IAAI,IAAI,CAACF,cAAc,CAACC,KAAK,CAACE,OAAO,IAAI,IAAI,CAACH,cAAc,CAACC,KAAK,CAACG,SAAS,EAAE;MAChHwE,OAAO,GAAGA,OAAO,CAACC,MAAM,CAACC,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACZ,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;QAE5D,MAAMa,SAAS,GAAGF,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,SAAS,IAAI,CAAC;QAEhD,OAAQ,IAAI,CAAChF,cAAc,CAACC,KAAK,CAACC,MAAM,IAAI8E,SAAS,KAAK,CAAC,IACnD,IAAI,CAAChF,cAAc,CAACC,KAAK,CAACE,OAAO,IAAI6E,SAAS,KAAK,CAAE,IACrD,IAAI,CAAChF,cAAc,CAACC,KAAK,CAACG,SAAS,IAAI4E,SAAS,IAAI,CAAE;MAChE,CAAC,CAAC;;IAGJ;IACA,IAAI,IAAI,CAAChF,cAAc,CAACK,aAAa,CAACC,YAAY,IAC9C,IAAI,CAACN,cAAc,CAACK,aAAa,CAACE,OAAO,IACzC,IAAI,CAACP,cAAc,CAACK,aAAa,CAACG,SAAS,IAC3C,IAAI,CAACR,cAAc,CAACK,aAAa,CAACI,OAAO,EAAE;MAE7CmE,OAAO,GAAGA,OAAO,CAACC,MAAM,CAACC,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACZ,MAAM,KAAK,CAAC,IAAI,CAACW,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACE,SAAS,IAAI,CAACH,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACE,SAAS,CAACC,IAAI,EAAE;UAC/G,OAAO,KAAK;;QAGd,MAAMpD,aAAa,GAAG,IAAIV,IAAI,CAAC0D,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACE,SAAS,CAACC,IAAI,CAAC;QAC9D,MAAMC,KAAK,GAAGrD,aAAa,CAACsD,QAAQ,EAAE;QAEtC,OAAQ,IAAI,CAACpF,cAAc,CAACK,aAAa,CAACC,YAAY,IAAI6E,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,CAAC,IACzE,IAAI,CAACnF,cAAc,CAACK,aAAa,CAACE,OAAO,IAAI4E,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,EAAG,IACtE,IAAI,CAACnF,cAAc,CAACK,aAAa,CAACG,SAAS,IAAI2E,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG,IACzE,IAAI,CAACnF,cAAc,CAACK,aAAa,CAACI,OAAO,IAAI0E,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG;MACjF,CAAC,CAAC;;IAGJ;IACA,IAAI,IAAI,CAACnF,cAAc,CAACU,WAAW,CAACJ,YAAY,IAC5C,IAAI,CAACN,cAAc,CAACU,WAAW,CAACH,OAAO,IACvC,IAAI,CAACP,cAAc,CAACU,WAAW,CAACF,SAAS,IACzC,IAAI,CAACR,cAAc,CAACU,WAAW,CAACD,OAAO,EAAE;MAE3CmE,OAAO,GAAGA,OAAO,CAACC,MAAM,CAACC,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACZ,MAAM,KAAK,CAAC,IAAI,CAACW,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACM,OAAO,IAAI,CAACP,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACM,OAAO,CAACH,IAAI,EAAE;UAC3G,OAAO,KAAK;;QAGd,MAAMI,WAAW,GAAG,IAAIlE,IAAI,CAAC0D,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACM,OAAO,CAACH,IAAI,CAAC;QAC1D,MAAMC,KAAK,GAAGG,WAAW,CAACF,QAAQ,EAAE;QAEpC,OAAQ,IAAI,CAACpF,cAAc,CAACU,WAAW,CAACJ,YAAY,IAAI6E,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,CAAC,IACvE,IAAI,CAACnF,cAAc,CAACU,WAAW,CAACH,OAAO,IAAI4E,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,EAAG,IACpE,IAAI,CAACnF,cAAc,CAACU,WAAW,CAACF,SAAS,IAAI2E,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG,IACvE,IAAI,CAACnF,cAAc,CAACU,WAAW,CAACD,OAAO,IAAI0E,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG;MAC/E,CAAC,CAAC;;IAGJ;IACA,MAAMI,gBAAgB,GAAG/B,MAAM,CAACO,OAAO,CAAC,IAAI,CAAC/D,cAAc,CAACW,QAAQ,CAAC,CAClEkE,MAAM,CAAC,CAAC,CAACW,CAAC,EAAEC,QAAQ,CAAC,KAAKA,QAAQ,CAAC,CACnCrH,GAAG,CAAC,CAAC,CAACsH,OAAO,EAAEF,CAAC,CAAC,KAAKE,OAAO,CAAC;IAEjC,IAAIH,gBAAgB,CAACpB,MAAM,GAAG,CAAC,EAAE;MAC/BS,OAAO,GAAGA,OAAO,CAACC,MAAM,CAACC,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACZ,MAAM,KAAK,CAAC,IAAI,CAACW,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACW,OAAO,IAAI,CAACZ,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACW,OAAO,CAACC,IAAI,EAAE;UAC3G,OAAO,KAAK;;QAGd,OAAOJ,gBAAgB,CAACK,QAAQ,CAACd,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACW,OAAO,CAACC,IAAI,CAAC;MAChE,CAAC,CAAC;;IAGJ;IACA,QAAQ,IAAI,CAAChG,aAAa;MACxB,KAAK,UAAU;QACb,IAAI,CAACT,eAAe,GAAG,IAAI,CAAC2G,WAAW,CAACjB,OAAO,CAAC;QAChD;MACF,KAAK,UAAU;QACb,IAAI,CAAC1F,eAAe,GAAG,IAAI,CAAC4G,cAAc,CAAClB,OAAO,CAAC;QACnD;MACF,KAAK,aAAa;MAClB;QACE,IAAI,CAAC1F,eAAe,GAAG,IAAI,CAAC6G,oBAAoB,CAACnB,OAAO,CAAC;QACzD;;EAEN;EAEA;EACQiB,WAAWA,CAACG,OAAiB;IACnC,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,MAAMC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACH,CAAC,CAAC;MACxC,MAAMI,MAAM,GAAG,IAAI,CAACD,iBAAiB,CAACF,CAAC,CAAC;MACxC,OAAOC,MAAM,GAAGE,MAAM;IACxB,CAAC,CAAC;EACJ;EAEA;EACQR,cAAcA,CAACE,OAAiB;IACtC,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,MAAMI,SAAS,GAAGL,CAAC,CAACnB,KAAK,IAAImB,CAAC,CAACnB,KAAK,CAAC,CAAC,CAAC,GAAGmB,CAAC,CAACnB,KAAK,CAAC,CAAC,CAAC,CAACyB,QAAQ,GAAGC,MAAM,CAACC,SAAS;MAChF,MAAMC,SAAS,GAAGR,CAAC,CAACpB,KAAK,IAAIoB,CAAC,CAACpB,KAAK,CAAC,CAAC,CAAC,GAAGoB,CAAC,CAACpB,KAAK,CAAC,CAAC,CAAC,CAACyB,QAAQ,GAAGC,MAAM,CAACC,SAAS;MAChF,OAAOH,SAAS,GAAGI,SAAS;IAC9B,CAAC,CAAC;EACJ;EAEA;EACQZ,oBAAoBA,CAACC,OAAiB;IAC5C,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B;MACA,MAAMS,MAAM,GAAG,IAAI,CAACC,4BAA4B,CAACX,CAAC,CAAC;MACnD,MAAMY,MAAM,GAAG,IAAI,CAACD,4BAA4B,CAACV,CAAC,CAAC;MACnD,OAAOW,MAAM,GAAGF,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ;EAEA;EACQC,4BAA4BA,CAAC/B,MAAc;IACjD,IAAI,CAACA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACZ,MAAM,KAAK,CAAC,IAAI,CAACW,MAAM,CAACiC,MAAM,IAAIjC,MAAM,CAACiC,MAAM,CAAC5C,MAAM,KAAK,CAAC,EAAE;MAC9F,OAAO,CAAC;;IAGV,MAAM6C,IAAI,GAAGlC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC5B,MAAMkC,KAAK,GAAGnC,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC;IAE9B;IACA,MAAMG,KAAK,GAAG,IAAI,CAACb,iBAAiB,CAACvB,MAAM,CAAC;IAC5C,MAAM0B,QAAQ,GAAGQ,IAAI,CAACR,QAAQ;IAC9B,MAAMxB,SAAS,GAAGgC,IAAI,CAAChC,SAAS,IAAI,CAAC;IACrC,MAAMmC,YAAY,GAAGF,KAAK,CAACE,YAAY,KAAKC,SAAS,GAAGH,KAAK,CAACE,YAAY,GACrDF,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACI,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IAE5E;IACA,MAAMC,UAAU,GAAG,IAAI,IAAIL,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;IACzC,MAAMM,aAAa,GAAG,IAAI,IAAIhB,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC;IAC/C,MAAMiB,SAAS,GAAG,CAAC,IAAIzC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;IACvC,MAAM0C,iBAAiB,GAAGC,IAAI,CAACC,GAAG,CAACT,YAAY,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAE3D;IACA,MAAMU,OAAO,GAAG;MACdX,KAAK,EAAE,GAAG;MACVV,QAAQ,EAAE,GAAG;MACbvG,KAAK,EAAE,GAAG;MACVkH,YAAY,EAAE,GAAG,CAAC;KACnB;IAED;IACA,OACEI,UAAU,GAAGM,OAAO,CAACX,KAAK,GAC1BM,aAAa,GAAGK,OAAO,CAACrB,QAAQ,GAChCiB,SAAS,GAAGI,OAAO,CAAC5H,KAAK,GACzByH,iBAAiB,GAAGG,OAAO,CAACV,YAAY;EAE5C;EAEA;EACQd,iBAAiBA,CAACvB,MAAc;IACtC,IAAI,CAACA,MAAM,CAACiC,MAAM,IAAIjC,MAAM,CAACiC,MAAM,CAAC5C,MAAM,KAAK,CAAC,EAAE;MAChD,OAAOsC,MAAM,CAACC,SAAS;;IAGzB,OAAO5B,MAAM,CAACiC,MAAM,CAACrD,MAAM,CAAC,CAACkE,GAAG,EAAEX,KAAK,KACrCA,KAAK,CAACC,KAAK,IAAID,KAAK,CAACC,KAAK,CAACY,MAAM,GAAGF,GAAG,GAAGX,KAAK,CAACC,KAAK,CAACY,MAAM,GAAGF,GAAG,EAClE9C,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,CAACG,KAAK,GAAGpC,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,CAACG,KAAK,CAACY,MAAM,GAAGrB,MAAM,CAACC,SAAS,CAC1E;EACH;EAEA;EACAqB,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC9I,aAAa,IAAI,IAAI,CAACA,aAAa,CAACkF,MAAM,KAAK,CAAC,EAAE;MAC1D;;IAGF;IACA,IAAI,CAAC6D,iBAAiB,EAAE;IAExB;IACA,MAAMrH,QAAQ,GAAG,IAAIsH,GAAG,EAAU;IAElC;IACA,IAAI,CAAChJ,aAAa,CAAC+E,OAAO,CAACc,MAAM,IAAG;MAClC,IAAI,CAACA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACZ,MAAM,KAAK,CAAC,IAAI,CAACW,MAAM,CAACiC,MAAM,IAAIjC,MAAM,CAACiC,MAAM,CAAC5C,MAAM,KAAK,CAAC,EAAE;QAC9F;;MAGF,MAAM6C,IAAI,GAAGlC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MAC5B,MAAMmC,KAAK,GAAG,IAAI,CAACb,iBAAiB,CAACvB,MAAM,CAAC;MAE5C;MACA,IAAIkC,IAAI,CAACtB,OAAO,IAAIsB,IAAI,CAACtB,OAAO,CAACC,IAAI,EAAE;QACrChF,QAAQ,CAACuH,GAAG,CAAClB,IAAI,CAACtB,OAAO,CAACC,IAAI,CAAC;QAE/B;QACA,IAAI,EAAEqB,IAAI,CAACtB,OAAO,CAACC,IAAI,IAAI,IAAI,CAAC9E,YAAY,CAACF,QAAQ,CAAC,EAAE;UACtD,IAAI,CAACE,YAAY,CAACF,QAAQ,CAACqG,IAAI,CAACtB,OAAO,CAACC,IAAI,CAAC,GAAGc,MAAM,CAACC,SAAS;;QAGlE;QACA,IAAI,CAAC7F,YAAY,CAACF,QAAQ,CAACqG,IAAI,CAACtB,OAAO,CAACC,IAAI,CAAC,GAAGgC,IAAI,CAACC,GAAG,CACtD,IAAI,CAAC/G,YAAY,CAACF,QAAQ,CAACqG,IAAI,CAACtB,OAAO,CAACC,IAAI,CAAC,EAC7CuB,KAAK,CACN;;MAGH;MACA,MAAMlC,SAAS,GAAGgC,IAAI,CAAChC,SAAS,IAAI,CAAC;MACrC,IAAIA,SAAS,KAAK,CAAC,EAAE;QACnB,IAAI,CAACnE,YAAY,CAACZ,KAAK,CAACC,MAAM,GAAGyH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/G,YAAY,CAACZ,KAAK,CAACC,MAAM,EAAEgH,KAAK,CAAC;OACjF,MAAM,IAAIlC,SAAS,KAAK,CAAC,EAAE;QAC1B,IAAI,CAACnE,YAAY,CAACZ,KAAK,CAACE,OAAO,GAAGwH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/G,YAAY,CAACZ,KAAK,CAACE,OAAO,EAAE+G,KAAK,CAAC;OACnF,MAAM;QACL,IAAI,CAACrG,YAAY,CAACZ,KAAK,CAACG,SAAS,GAAGuH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/G,YAAY,CAACZ,KAAK,CAACG,SAAS,EAAE8G,KAAK,CAAC;;MAGxF;MACA,IAAIF,IAAI,CAAC/B,SAAS,IAAI+B,IAAI,CAAC/B,SAAS,CAACC,IAAI,EAAE;QACzC,MAAMpD,aAAa,GAAG,IAAIV,IAAI,CAAC4F,IAAI,CAAC/B,SAAS,CAACC,IAAI,CAAC;QACnD,MAAMiD,cAAc,GAAGrG,aAAa,CAACsD,QAAQ,EAAE;QAE/C,IAAI+C,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAG,CAAC,EAAE;UAC7C,IAAI,CAACtH,YAAY,CAACR,aAAa,CAACC,YAAY,GAAGqH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/G,YAAY,CAACR,aAAa,CAACC,YAAY,EAAE4G,KAAK,CAAC;SAC7G,MAAM,IAAIiB,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAG,EAAE,EAAE;UACrD,IAAI,CAACtH,YAAY,CAACR,aAAa,CAACE,OAAO,GAAGoH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/G,YAAY,CAACR,aAAa,CAACE,OAAO,EAAE2G,KAAK,CAAC;SACnG,MAAM,IAAIiB,cAAc,IAAI,EAAE,IAAIA,cAAc,GAAG,EAAE,EAAE;UACtD,IAAI,CAACtH,YAAY,CAACR,aAAa,CAACG,SAAS,GAAGmH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/G,YAAY,CAACR,aAAa,CAACG,SAAS,EAAE0G,KAAK,CAAC;SACvG,MAAM;UACL,IAAI,CAACrG,YAAY,CAACR,aAAa,CAACI,OAAO,GAAGkH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/G,YAAY,CAACR,aAAa,CAACI,OAAO,EAAEyG,KAAK,CAAC;;;MAItG;MACA,IAAIF,IAAI,CAAC3B,OAAO,IAAI2B,IAAI,CAAC3B,OAAO,CAACH,IAAI,EAAE;QACrC,MAAMI,WAAW,GAAG,IAAIlE,IAAI,CAAC4F,IAAI,CAAC3B,OAAO,CAACH,IAAI,CAAC;QAC/C,MAAMkD,YAAY,GAAG9C,WAAW,CAACF,QAAQ,EAAE;QAE3C,IAAIgD,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,CAAC,EAAE;UACzC,IAAI,CAACvH,YAAY,CAACH,WAAW,CAACJ,YAAY,GAAGqH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/G,YAAY,CAACH,WAAW,CAACJ,YAAY,EAAE4G,KAAK,CAAC;SACzG,MAAM,IAAIkB,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,EAAE,EAAE;UACjD,IAAI,CAACvH,YAAY,CAACH,WAAW,CAACH,OAAO,GAAGoH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/G,YAAY,CAACH,WAAW,CAACH,OAAO,EAAE2G,KAAK,CAAC;SAC/F,MAAM,IAAIkB,YAAY,IAAI,EAAE,IAAIA,YAAY,GAAG,EAAE,EAAE;UAClD,IAAI,CAACvH,YAAY,CAACH,WAAW,CAACF,SAAS,GAAGmH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/G,YAAY,CAACH,WAAW,CAACF,SAAS,EAAE0G,KAAK,CAAC;SACnG,MAAM;UACL,IAAI,CAACrG,YAAY,CAACH,WAAW,CAACD,OAAO,GAAGkH,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/G,YAAY,CAACH,WAAW,CAACD,OAAO,EAAEyG,KAAK,CAAC;;;IAGpG,CAAC,CAAC;IAEF;IACAvG,QAAQ,CAACqD,OAAO,CAAC0B,OAAO,IAAG;MACzB,IAAI,EAAEA,OAAO,IAAI,IAAI,CAAC1F,cAAc,CAACW,QAAQ,CAAC,EAAE;QAC9C,IAAI,CAACX,cAAc,CAACW,QAAQ,CAAC+E,OAAO,CAAC,GAAG,KAAK;;IAEjD,CAAC,CAAC;IAEF;IACA,IAAI,CAAC2C,mBAAmB,EAAE;EAC5B;EAEA;EACQL,iBAAiBA,CAAA;IACvB,IAAI,CAACnH,YAAY,GAAG;MAClBZ,KAAK,EAAE;QACLC,MAAM,EAAEuG,MAAM,CAACC,SAAS;QACxBvG,OAAO,EAAEsG,MAAM,CAACC,SAAS;QACzBtG,SAAS,EAAEqG,MAAM,CAACC;OACnB;MACDrG,aAAa,EAAE;QACbC,YAAY,EAAEmG,MAAM,CAACC,SAAS;QAC9BnG,OAAO,EAAEkG,MAAM,CAACC,SAAS;QACzBlG,SAAS,EAAEiG,MAAM,CAACC,SAAS;QAC3BjG,OAAO,EAAEgG,MAAM,CAACC;OACjB;MACDhG,WAAW,EAAE;QACXJ,YAAY,EAAEmG,MAAM,CAACC,SAAS;QAC9BnG,OAAO,EAAEkG,MAAM,CAACC,SAAS;QACzBlG,SAAS,EAAEiG,MAAM,CAACC,SAAS;QAC3BjG,OAAO,EAAEgG,MAAM,CAACC;OACjB;MACD/F,QAAQ,EAAE;KACX;EACH;EAEA;EACQ0H,mBAAmBA,CAAA;IACzB;IACA,IAAI,IAAI,CAACxH,YAAY,CAACZ,KAAK,CAACC,MAAM,KAAKuG,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC7F,YAAY,CAACZ,KAAK,CAACC,MAAM,GAAG,CAAC;IAC3F,IAAI,IAAI,CAACW,YAAY,CAACZ,KAAK,CAACE,OAAO,KAAKsG,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC7F,YAAY,CAACZ,KAAK,CAACE,OAAO,GAAG,CAAC;IAC7F,IAAI,IAAI,CAACU,YAAY,CAACZ,KAAK,CAACG,SAAS,KAAKqG,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC7F,YAAY,CAACZ,KAAK,CAACG,SAAS,GAAG,CAAC;IAEjG;IACA,IAAI,IAAI,CAACS,YAAY,CAACR,aAAa,CAACC,YAAY,KAAKmG,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC7F,YAAY,CAACR,aAAa,CAACC,YAAY,GAAG,CAAC;IACvH,IAAI,IAAI,CAACO,YAAY,CAACR,aAAa,CAACE,OAAO,KAAKkG,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC7F,YAAY,CAACR,aAAa,CAACE,OAAO,GAAG,CAAC;IAC7G,IAAI,IAAI,CAACM,YAAY,CAACR,aAAa,CAACG,SAAS,KAAKiG,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC7F,YAAY,CAACR,aAAa,CAACG,SAAS,GAAG,CAAC;IACjH,IAAI,IAAI,CAACK,YAAY,CAACR,aAAa,CAACI,OAAO,KAAKgG,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC7F,YAAY,CAACR,aAAa,CAACI,OAAO,GAAG,CAAC;IAE7G;IACA,IAAI,IAAI,CAACI,YAAY,CAACH,WAAW,CAACJ,YAAY,KAAKmG,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC7F,YAAY,CAACH,WAAW,CAACJ,YAAY,GAAG,CAAC;IACnH,IAAI,IAAI,CAACO,YAAY,CAACH,WAAW,CAACH,OAAO,KAAKkG,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC7F,YAAY,CAACH,WAAW,CAACH,OAAO,GAAG,CAAC;IACzG,IAAI,IAAI,CAACM,YAAY,CAACH,WAAW,CAACF,SAAS,KAAKiG,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC7F,YAAY,CAACH,WAAW,CAACF,SAAS,GAAG,CAAC;IAC7G,IAAI,IAAI,CAACK,YAAY,CAACH,WAAW,CAACD,OAAO,KAAKgG,MAAM,CAACC,SAAS,EAAE,IAAI,CAAC7F,YAAY,CAACH,WAAW,CAACD,OAAO,GAAG,CAAC;IAEzG;IACA+C,MAAM,CAAC8E,IAAI,CAAC,IAAI,CAACzH,YAAY,CAACF,QAAQ,CAAC,CAACqD,OAAO,CAAC0B,OAAO,IAAG;MACxD,IAAI,IAAI,CAAC7E,YAAY,CAACF,QAAQ,CAAC+E,OAAO,CAAC,KAAKe,MAAM,CAACC,SAAS,EAAE;QAC5D,IAAI,CAAC7F,YAAY,CAACF,QAAQ,CAAC+E,OAAO,CAAC,GAAG,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACA6C,aAAaA,CAACC,OAAe;IAC3B,IAAI,CAAC5H,gBAAgB,CAAC4H,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC5H,gBAAgB,CAAC4H,OAAO,CAAC;EAClE;EAEA;EACAC,gBAAgBA,CAAC5D,MAA0C;IACzD,IAAI,CAAC7E,cAAc,CAACC,KAAK,CAAC4E,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC7E,cAAc,CAACC,KAAK,CAAC4E,MAAM,CAAC;IACtE,IAAI,CAACF,eAAe,EAAE;EACxB;EAEA;EACA+D,yBAAyBA,CAAC7D,MAA4D;IACpF,IAAI,CAAC7E,cAAc,CAACK,aAAa,CAACwE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC7E,cAAc,CAACK,aAAa,CAACwE,MAAM,CAAC;IACtF,IAAI,CAACF,eAAe,EAAE;EACxB;EAEA;EACAgE,uBAAuBA,CAAC9D,MAA4D;IAClF,IAAI,CAAC7E,cAAc,CAACU,WAAW,CAACmE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC7E,cAAc,CAACU,WAAW,CAACmE,MAAM,CAAC;IAClF,IAAI,CAACF,eAAe,EAAE;EACxB;EAEA;EACAiE,mBAAmBA,CAAClD,OAAe;IACjC,IAAI,CAAC1F,cAAc,CAACW,QAAQ,CAAC+E,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC1F,cAAc,CAACW,QAAQ,CAAC+E,OAAO,CAAC;IAC9E,IAAI,CAACf,eAAe,EAAE;EACxB;EAEA;EACAkE,eAAeA,CAAA;IACb;IACA,IAAI,CAAC7I,cAAc,CAACC,KAAK,CAACC,MAAM,GAAG,KAAK;IACxC,IAAI,CAACF,cAAc,CAACC,KAAK,CAACE,OAAO,GAAG,KAAK;IACzC,IAAI,CAACH,cAAc,CAACC,KAAK,CAACG,SAAS,GAAG,KAAK;IAE3C;IACA,IAAI,CAACJ,cAAc,CAACK,aAAa,CAACC,YAAY,GAAG,KAAK;IACtD,IAAI,CAACN,cAAc,CAACK,aAAa,CAACE,OAAO,GAAG,KAAK;IACjD,IAAI,CAACP,cAAc,CAACK,aAAa,CAACG,SAAS,GAAG,KAAK;IACnD,IAAI,CAACR,cAAc,CAACK,aAAa,CAACI,OAAO,GAAG,KAAK;IAEjD;IACA,IAAI,CAACT,cAAc,CAACU,WAAW,CAACJ,YAAY,GAAG,KAAK;IACpD,IAAI,CAACN,cAAc,CAACU,WAAW,CAACH,OAAO,GAAG,KAAK;IAC/C,IAAI,CAACP,cAAc,CAACU,WAAW,CAACF,SAAS,GAAG,KAAK;IACjD,IAAI,CAACR,cAAc,CAACU,WAAW,CAACD,OAAO,GAAG,KAAK;IAE/C;IACA+C,MAAM,CAAC8E,IAAI,CAAC,IAAI,CAACtI,cAAc,CAACW,QAAQ,CAAC,CAACqD,OAAO,CAAC0B,OAAO,IAAG;MAC1D,IAAI,CAAC1F,cAAc,CAACW,QAAQ,CAAC+E,OAAO,CAAC,GAAG,KAAK;IAC/C,CAAC,CAAC;IAEF;IACA,IAAI,CAACf,eAAe,EAAE;EACxB;EAEA;EACAmE,WAAWA,CAAC5B,KAAa;IACvB,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,GAAG;IAC3B,OAAOA,KAAK,CAAC6B,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;EAChC;EAEA;EACAC,cAAcA,CAAA;IACZ,OAAOxF,MAAM,CAAC8E,IAAI,CAAC,IAAI,CAACtI,cAAc,CAACW,QAAQ,CAAC;EAClD;EAEA;EACAsI,cAAcA,CAACnE,MAAc;IAC3B;IACA,MAAMoE,QAAQ,GAAGrG,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;IAC9CD,QAAQ,CAACE,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCH,QAAQ,CAACE,KAAK,CAACE,GAAG,GAAG,GAAG;IACxBJ,QAAQ,CAACE,KAAK,CAACG,IAAI,GAAG,GAAG;IACzBL,QAAQ,CAACE,KAAK,CAACI,KAAK,GAAG,MAAM;IAC7BN,QAAQ,CAACE,KAAK,CAACK,MAAM,GAAG,MAAM;IAC9BP,QAAQ,CAACE,KAAK,CAACM,eAAe,GAAG,oBAAoB;IACrDR,QAAQ,CAACE,KAAK,CAACO,MAAM,GAAG,MAAM;IAC9BT,QAAQ,CAACE,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC/BV,QAAQ,CAACE,KAAK,CAACS,cAAc,GAAG,QAAQ;IACxCX,QAAQ,CAACE,KAAK,CAACU,UAAU,GAAG,QAAQ;IAEpC;IACA,MAAMC,YAAY,GAAGlH,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;IAClDY,YAAY,CAACX,KAAK,CAACM,eAAe,GAAG,OAAO;IAC5CK,YAAY,CAACX,KAAK,CAACY,OAAO,GAAG,MAAM;IACnCD,YAAY,CAACX,KAAK,CAACa,YAAY,GAAG,MAAM;IACxCF,YAAY,CAACX,KAAK,CAACc,QAAQ,GAAG,KAAK;IACnCH,YAAY,CAACX,KAAK,CAACe,SAAS,GAAG,KAAK;IACpCJ,YAAY,CAACX,KAAK,CAACgB,QAAQ,GAAG,MAAM;IACpCL,YAAY,CAACX,KAAK,CAACC,QAAQ,GAAG,UAAU;IACxCU,YAAY,CAACX,KAAK,CAACiB,SAAS,GAAG,gCAAgC;IAC/DN,YAAY,CAACX,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCP,YAAY,CAACX,KAAK,CAACmB,UAAU,GAAG,mBAAmB;IAEnD;IACA,MAAMC,WAAW,GAAG3H,QAAQ,CAACsG,aAAa,CAAC,QAAQ,CAAC;IACpDqB,WAAW,CAACC,SAAS,GAAG,SAAS;IACjCD,WAAW,CAACpB,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvCmB,WAAW,CAACpB,KAAK,CAACE,GAAG,GAAG,MAAM;IAC9BkB,WAAW,CAACpB,KAAK,CAACsB,KAAK,GAAG,MAAM;IAChCF,WAAW,CAACpB,KAAK,CAACuB,MAAM,GAAG,MAAM;IACjCH,WAAW,CAACpB,KAAK,CAACwB,UAAU,GAAG,MAAM;IACrCJ,WAAW,CAACpB,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACnCL,WAAW,CAACpB,KAAK,CAAC0B,MAAM,GAAG,SAAS;IACpCN,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnCE,WAAW,CAACpB,KAAK,CAAC2B,UAAU,GAAG,YAAY;IAC3CP,WAAW,CAACQ,WAAW,GAAG,MAAMR,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnEE,WAAW,CAACS,UAAU,GAAG,MAAMT,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IAClEE,WAAW,CAACU,OAAO,GAAG,MAAMrI,QAAQ,CAACsI,IAAI,CAACC,WAAW,CAAClC,QAAQ,CAAC;IAE/D;IACA,MAAMmC,MAAM,GAAGxI,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;IAC5CkC,MAAM,CAACjC,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC7ByB,MAAM,CAACjC,KAAK,CAACU,UAAU,GAAG,QAAQ;IAClCuB,MAAM,CAACjC,KAAK,CAACkC,YAAY,GAAG,MAAM;IAClCD,MAAM,CAACjC,KAAK,CAACmC,aAAa,GAAG,MAAM;IACnCF,MAAM,CAACjC,KAAK,CAACoC,YAAY,GAAG,gBAAgB;IAE5C,MAAMC,IAAI,GAAG5I,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;IAC1CsC,IAAI,CAAChB,SAAS,GAAG,2FAA2F;IAE5G,MAAMiB,KAAK,GAAG7I,QAAQ,CAACsG,aAAa,CAAC,IAAI,CAAC;IAC1CuC,KAAK,CAACC,WAAW,GAAG,yBAAyB;IAC7CD,KAAK,CAACtC,KAAK,CAACwC,MAAM,GAAG,GAAG;IACxBF,KAAK,CAACtC,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC7Ba,KAAK,CAACtC,KAAK,CAACyC,UAAU,GAAG,KAAK;IAC9BH,KAAK,CAACtC,KAAK,CAACkB,KAAK,GAAG,SAAS;IAE7Be,MAAM,CAACS,WAAW,CAACL,IAAI,CAAC;IACxBJ,MAAM,CAACS,WAAW,CAACJ,KAAK,CAAC;IAEzB;IACA,MAAMK,gBAAgB,GAAGlJ,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;IACtD4C,gBAAgB,CAAC3C,KAAK,CAACQ,OAAO,GAAG,MAAM;IACvCmC,gBAAgB,CAAC3C,KAAK,CAAC4C,aAAa,GAAG,QAAQ;IAC/CD,gBAAgB,CAAC3C,KAAK,CAAC6C,GAAG,GAAG,MAAM;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9E;IACA,IAAIrH,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACZ,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAM6C,IAAI,GAAGlC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,IAAIiC,IAAI,CAACtB,OAAO,EAAE;QAChB,MAAM0G,WAAW,GAAGvJ,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QACjDiD,WAAW,CAAChD,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCwC,WAAW,CAAChD,KAAK,CAACU,UAAU,GAAG,QAAQ;QACvCsC,WAAW,CAAChD,KAAK,CAACkC,YAAY,GAAG,MAAM;QAEvC;QACA,IAAItE,IAAI,CAACtB,OAAO,CAAC2G,aAAa,EAAE;UAC9B,MAAMC,WAAW,GAAGzJ,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;UACjDmD,WAAW,CAACC,GAAG,GAAGvF,IAAI,CAACtB,OAAO,CAAC2G,aAAa;UAC5CC,WAAW,CAACE,GAAG,GAAGxF,IAAI,CAACtB,OAAO,CAACC,IAAI;UACnC2G,WAAW,CAAClD,KAAK,CAACK,MAAM,GAAG,MAAM;UACjC6C,WAAW,CAAClD,KAAK,CAACqD,WAAW,GAAG,MAAM;UACtCL,WAAW,CAACN,WAAW,CAACQ,WAAW,CAAC;SACrC,MAAM;UACL,MAAMI,WAAW,GAAG7J,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;UACjDuD,WAAW,CAACjC,SAAS,GAAG,wFAAwF;UAChH2B,WAAW,CAACN,WAAW,CAACY,WAAW,CAAC;;QAGtC,MAAMC,WAAW,GAAG9J,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QACjDwD,WAAW,CAAClC,SAAS,GAAG,WAAWzD,IAAI,CAACtB,OAAO,CAACC,IAAI,cAAcqB,IAAI,CAACtB,OAAO,CAACkH,iBAAiB,GAAG;QACnGD,WAAW,CAACvD,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCuB,WAAW,CAACN,WAAW,CAACa,WAAW,CAAC;QAEpCT,WAAW,CAACJ,WAAW,CAACM,WAAW,CAAC;;MAGtC;MACA,MAAMS,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC,eAAe,EAAE9F,IAAI,CAAC+F,QAAQ,IAAI,KAAK,CAAC;MACnFb,WAAW,CAACJ,WAAW,CAACe,eAAe,CAAC;MAExC;MACA,MAAMG,aAAa,GAAG,IAAI,CAACF,aAAa,CAAC,aAAa,EAAE,IAAI1L,IAAI,CAAC4F,IAAI,CAACiG,UAAU,CAAC,CAACC,kBAAkB,EAAE,CAAC;MACvGhB,WAAW,CAACJ,WAAW,CAACkB,aAAa,CAAC;MAEtC;MACA,MAAMG,WAAW,GAAG,IAAI,CAACL,aAAa,CAAC,UAAU,EAAE,IAAI,CAACM,cAAc,CAACpG,IAAI,CAACR,QAAQ,CAAC,CAAC;MACtF0F,WAAW,CAACJ,WAAW,CAACqB,WAAW,CAAC;MAEpC;MACA,IAAInG,IAAI,CAACjF,WAAW,EAAE;QACpB,MAAMsL,QAAQ,GAAG,IAAI,CAACP,aAAa,CAAC,OAAO,EAAE,GAAG9F,IAAI,CAACjF,WAAW,CAAC4D,IAAI,KAAKqB,IAAI,CAACjF,WAAW,CAACuL,IAAI,GAAG,CAAC;QACnGpB,WAAW,CAACJ,WAAW,CAACuB,QAAQ,CAAC;;MAGnC;MACA,MAAME,QAAQ,GAAG,IAAI,CAACT,aAAa,CAAC,OAAO,EAAE9F,IAAI,CAAChC,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,GAAGgC,IAAI,CAAChC,SAAS,UAAU,CAAC;MAClHkH,WAAW,CAACJ,WAAW,CAACyB,QAAQ,CAAC;;IAGnC;IACA,MAAMC,YAAY,GAAG,IAAI,CAACrB,aAAa,CAAC,eAAe,EAAE,UAAU,CAAC;IAEpE,IAAIrH,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACZ,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAM6C,IAAI,GAAGlC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,MAAM0I,WAAW,GAAG5K,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;MACjDsE,WAAW,CAACrE,KAAK,CAACQ,OAAO,GAAG,MAAM;MAClC6D,WAAW,CAACrE,KAAK,CAACU,UAAU,GAAG,QAAQ;MACvC2D,WAAW,CAACrE,KAAK,CAACS,cAAc,GAAG,eAAe;MAClD4D,WAAW,CAACrE,KAAK,CAACwC,MAAM,GAAG,QAAQ;MACnC6B,WAAW,CAACrE,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEvC;MACA,MAAMpE,SAAS,GAAGpC,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;MAC/ClE,SAAS,CAACmE,KAAK,CAACsE,SAAS,GAAG,QAAQ;MACpCzI,SAAS,CAACmE,KAAK,CAACuE,IAAI,GAAG,GAAG;MAE1B,IAAI3G,IAAI,CAAC/B,SAAS,EAAE;QAClB,MAAM5E,aAAa,GAAGwC,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QACnD9I,aAAa,CAAC+I,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCxK,aAAa,CAAC+I,KAAK,CAACyC,UAAU,GAAG,MAAM;QACvCxL,aAAa,CAACsL,WAAW,GAAG,IAAIvK,IAAI,CAAC4F,IAAI,CAAC/B,SAAS,CAACC,IAAI,CAAC,CAAC0I,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAErH,MAAMC,gBAAgB,GAAGlL,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QACtD4E,gBAAgB,CAAC3E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACxCkD,gBAAgB,CAAC3E,KAAK,CAAC4E,SAAS,GAAG,KAAK;QACxCD,gBAAgB,CAACtD,SAAS,GAAG,WAAWzD,IAAI,CAAC/B,SAAS,CAACgJ,OAAO,EAAEX,IAAI,IAAI,KAAK,WAAW;QAExF,MAAMY,aAAa,GAAGrL,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QACnD+E,aAAa,CAAC9E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCqD,aAAa,CAAC9E,KAAK,CAACkB,KAAK,GAAG,MAAM;QAClC4D,aAAa,CAACvC,WAAW,GAAG3E,IAAI,CAAC/B,SAAS,CAACkJ,IAAI,EAAExI,IAAI,IAAI,KAAK;QAE9DV,SAAS,CAAC6G,WAAW,CAACzL,aAAa,CAAC;QACpC4E,SAAS,CAAC6G,WAAW,CAACiC,gBAAgB,CAAC;QACvC9I,SAAS,CAAC6G,WAAW,CAACoC,aAAa,CAAC;;MAGtC;MACA,MAAME,cAAc,GAAGvL,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;MACpDiF,cAAc,CAAChF,KAAK,CAACuE,IAAI,GAAG,GAAG;MAC/BS,cAAc,CAAChF,KAAK,CAACQ,OAAO,GAAG,MAAM;MACrCwE,cAAc,CAAChF,KAAK,CAACU,UAAU,GAAG,QAAQ;MAC1CsE,cAAc,CAAChF,KAAK,CAACS,cAAc,GAAG,QAAQ;MAC9CuE,cAAc,CAAChF,KAAK,CAACY,OAAO,GAAG,QAAQ;MAEvC,MAAMqE,IAAI,GAAGxL,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;MAC1CkF,IAAI,CAACjF,KAAK,CAACK,MAAM,GAAG,KAAK;MACzB4E,IAAI,CAACjF,KAAK,CAACM,eAAe,GAAG,MAAM;MACnC2E,IAAI,CAACjF,KAAK,CAACI,KAAK,GAAG,MAAM;MACzB6E,IAAI,CAACjF,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEhC,MAAMiF,KAAK,GAAGzL,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;MAC3CmF,KAAK,CAAC7D,SAAS,GAAG,iGAAiG;MACnH6D,KAAK,CAAClF,KAAK,CAACC,QAAQ,GAAG,UAAU;MACjCiF,KAAK,CAAClF,KAAK,CAACE,GAAG,GAAG,MAAM;MACxBgF,KAAK,CAAClF,KAAK,CAACG,IAAI,GAAG,KAAK;MACxB+E,KAAK,CAAClF,KAAK,CAACmF,UAAU,GAAG,MAAM;MAC/BD,KAAK,CAAClF,KAAK,CAACM,eAAe,GAAG,OAAO;MACrC4E,KAAK,CAAClF,KAAK,CAACY,OAAO,GAAG,OAAO;MAE7BqE,IAAI,CAACvC,WAAW,CAACwC,KAAK,CAAC;MACvBF,cAAc,CAACtC,WAAW,CAACuC,IAAI,CAAC;MAEhC;MACA,MAAMhJ,OAAO,GAAGxC,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;MAC7C9D,OAAO,CAAC+D,KAAK,CAACsE,SAAS,GAAG,QAAQ;MAClCrI,OAAO,CAAC+D,KAAK,CAACuE,IAAI,GAAG,GAAG;MAExB,IAAI3G,IAAI,CAAC3B,OAAO,EAAE;QAChB,MAAM3E,WAAW,GAAGmC,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QACjDzI,WAAW,CAAC0I,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCnK,WAAW,CAAC0I,KAAK,CAACyC,UAAU,GAAG,MAAM;QACrCnL,WAAW,CAACiL,WAAW,GAAG,IAAIvK,IAAI,CAAC4F,IAAI,CAAC3B,OAAO,CAACH,IAAI,CAAC,CAAC0I,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAEjH,MAAMU,cAAc,GAAG3L,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QACpDqF,cAAc,CAACpF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACtC2D,cAAc,CAACpF,KAAK,CAAC4E,SAAS,GAAG,KAAK;QACtCQ,cAAc,CAAC/D,SAAS,GAAG,WAAWzD,IAAI,CAAC3B,OAAO,CAAC4I,OAAO,EAAEX,IAAI,IAAI,KAAK,WAAW;QAEpF,MAAMmB,WAAW,GAAG5L,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QACjDsF,WAAW,CAACrF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnC4D,WAAW,CAACrF,KAAK,CAACkB,KAAK,GAAG,MAAM;QAChCmE,WAAW,CAAC9C,WAAW,GAAG3E,IAAI,CAAC3B,OAAO,CAAC8I,IAAI,EAAExI,IAAI,IAAI,KAAK;QAE1DN,OAAO,CAACyG,WAAW,CAACpL,WAAW,CAAC;QAChC2E,OAAO,CAACyG,WAAW,CAAC0C,cAAc,CAAC;QACnCnJ,OAAO,CAACyG,WAAW,CAAC2C,WAAW,CAAC;;MAGlChB,WAAW,CAAC3B,WAAW,CAAC7G,SAAS,CAAC;MAClCwI,WAAW,CAAC3B,WAAW,CAACsC,cAAc,CAAC;MACvCX,WAAW,CAAC3B,WAAW,CAACzG,OAAO,CAAC;MAEhCmI,YAAY,CAAC1B,WAAW,CAAC2B,WAAW,CAAC;MAErC;MACA,IAAIzG,IAAI,CAAC0H,QAAQ,IAAI1H,IAAI,CAAC0H,QAAQ,CAACvK,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMwK,aAAa,GAAG9L,QAAQ,CAACsG,aAAa,CAAC,IAAI,CAAC;QAClDwF,aAAa,CAAChD,WAAW,GAAG,iBAAiB;QAC7CgD,aAAa,CAACvF,KAAK,CAAC4E,SAAS,GAAG,MAAM;QACtCW,aAAa,CAACvF,KAAK,CAACkC,YAAY,GAAG,MAAM;QACzCqD,aAAa,CAACvF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC8D,aAAa,CAACvF,KAAK,CAACyC,UAAU,GAAG,KAAK;QACtC2B,YAAY,CAAC1B,WAAW,CAAC6C,aAAa,CAAC;QAEvC,MAAMC,YAAY,GAAG/L,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QAClDyF,YAAY,CAACxF,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnCgF,YAAY,CAACxF,KAAK,CAAC4C,aAAa,GAAG,QAAQ;QAC3C4C,YAAY,CAACxF,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/BjF,IAAI,CAAC0H,QAAQ,CAAC1K,OAAO,CAAC,CAAC6K,OAAO,EAAEC,KAAK,KAAI;UACvC,MAAMC,WAAW,GAAGlM,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;UACjD4F,WAAW,CAAC3F,KAAK,CAACY,OAAO,GAAG,MAAM;UAClC+E,WAAW,CAAC3F,KAAK,CAACM,eAAe,GAAG,SAAS;UAC7CqF,WAAW,CAAC3F,KAAK,CAACa,YAAY,GAAG,KAAK;UACtC8E,WAAW,CAAC3F,KAAK,CAACuB,MAAM,GAAG,gBAAgB;UAE3C,MAAMqE,aAAa,GAAGnM,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;UACnD6F,aAAa,CAAC5F,KAAK,CAACQ,OAAO,GAAG,MAAM;UACpCoF,aAAa,CAAC5F,KAAK,CAACS,cAAc,GAAG,eAAe;UACpDmF,aAAa,CAAC5F,KAAK,CAACkC,YAAY,GAAG,MAAM;UACzC0D,aAAa,CAAC5F,KAAK,CAACmC,aAAa,GAAG,MAAM;UAC1CyD,aAAa,CAAC5F,KAAK,CAACoC,YAAY,GAAG,gBAAgB;UAEnD,MAAMyD,YAAY,GAAGpM,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;UAClD8F,YAAY,CAACxE,SAAS,GAAG,mBAAmBqE,KAAK,GAAG,CAAC,cAAcD,OAAO,CAACnJ,OAAO,EAAEC,IAAI,IAAI,SAAS,IAAIkJ,OAAO,CAAC9B,QAAQ,EAAE;UAE3H,MAAMmC,eAAe,GAAGrM,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;UACrD+F,eAAe,CAACvD,WAAW,GAAG,IAAI,CAACyB,cAAc,CAACyB,OAAO,CAACrI,QAAQ,CAAC;UACnE0I,eAAe,CAAC9F,KAAK,CAACkB,KAAK,GAAG,MAAM;UAEpC0E,aAAa,CAAClD,WAAW,CAACmD,YAAY,CAAC;UACvCD,aAAa,CAAClD,WAAW,CAACoD,eAAe,CAAC;UAC1CH,WAAW,CAACjD,WAAW,CAACkD,aAAa,CAAC;UAEtC;UACA,MAAMG,YAAY,GAAGtM,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;UAClDgG,YAAY,CAAC/F,KAAK,CAACQ,OAAO,GAAG,MAAM;UACnCuF,YAAY,CAAC/F,KAAK,CAACU,UAAU,GAAG,QAAQ;UACxCqF,YAAY,CAAC/F,KAAK,CAACS,cAAc,GAAG,eAAe;UAEnD;UACA,MAAMuF,gBAAgB,GAAGvM,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;UACtDiG,gBAAgB,CAAChG,KAAK,CAACuE,IAAI,GAAG,GAAG;UAEjC,IAAIkB,OAAO,CAAC5J,SAAS,EAAE;YACrB,MAAMoK,OAAO,GAAGxM,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;YAC7CkG,OAAO,CAACjG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCwD,OAAO,CAAC1D,WAAW,GAAG,IAAIvK,IAAI,CAACyN,OAAO,CAAC5J,SAAS,CAACC,IAAI,CAAC,CAAC0I,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAElH,MAAMwB,UAAU,GAAGzM,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;YAChDmG,UAAU,CAAClG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClCyE,UAAU,CAAC3D,WAAW,GAAG,GAAGkD,OAAO,CAAC5J,SAAS,CAACgJ,OAAO,EAAEX,IAAI,IAAI,KAAK,KAAKuB,OAAO,CAAC5J,SAAS,CAACkJ,IAAI,EAAExI,IAAI,IAAI,KAAK,GAAG;YAEjHyJ,gBAAgB,CAACtD,WAAW,CAACuD,OAAO,CAAC;YACrCD,gBAAgB,CAACtD,WAAW,CAACwD,UAAU,CAAC;;UAG1C;UACA,MAAMC,KAAK,GAAG1M,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;UAC3CoG,KAAK,CAAC9E,SAAS,GAAG,qFAAqF;UAEvG;UACA,MAAM+E,cAAc,GAAG3M,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;UACpDqG,cAAc,CAACpG,KAAK,CAACuE,IAAI,GAAG,GAAG;UAC/B6B,cAAc,CAACpG,KAAK,CAACsE,SAAS,GAAG,OAAO;UAExC,IAAImB,OAAO,CAACxJ,OAAO,EAAE;YACnB,MAAMoK,OAAO,GAAG5M,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;YAC7CsG,OAAO,CAACrG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjC4D,OAAO,CAAC9D,WAAW,GAAG,IAAIvK,IAAI,CAACyN,OAAO,CAACxJ,OAAO,CAACH,IAAI,CAAC,CAAC0I,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAEhH,MAAM4B,UAAU,GAAG7M,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;YAChDuG,UAAU,CAACtG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClC6E,UAAU,CAAC/D,WAAW,GAAG,GAAGkD,OAAO,CAACxJ,OAAO,CAAC4I,OAAO,EAAEX,IAAI,IAAI,KAAK,KAAKuB,OAAO,CAACxJ,OAAO,CAAC8I,IAAI,EAAExI,IAAI,IAAI,KAAK,GAAG;YAE7G6J,cAAc,CAAC1D,WAAW,CAAC2D,OAAO,CAAC;YACnCD,cAAc,CAAC1D,WAAW,CAAC4D,UAAU,CAAC;;UAGxCP,YAAY,CAACrD,WAAW,CAACsD,gBAAgB,CAAC;UAC1CD,YAAY,CAACrD,WAAW,CAACyD,KAAK,CAAC;UAC/BJ,YAAY,CAACrD,WAAW,CAAC0D,cAAc,CAAC;UACxCT,WAAW,CAACjD,WAAW,CAACqD,YAAY,CAAC;UAErCP,YAAY,CAAC9C,WAAW,CAACiD,WAAW,CAAC;UAErC;UACA,IAAID,KAAK,GAAG9H,IAAI,CAAC0H,QAAQ,CAACvK,MAAM,GAAG,CAAC,EAAE;YACpC,MAAMwL,OAAO,GAAG9M,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;YAC7CwG,OAAO,CAACvG,KAAK,CAACsE,SAAS,GAAG,QAAQ;YAClCiC,OAAO,CAACvG,KAAK,CAACY,OAAO,GAAG,MAAM;YAC9B2F,OAAO,CAACvG,KAAK,CAACkB,KAAK,GAAG,SAAS;YAC/BqF,OAAO,CAACvG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAE/B;YACA,MAAM+E,cAAc,GAAG,IAAIxO,IAAI,CAACyN,OAAO,CAACxJ,OAAO,EAAEH,IAAI,IAAI,CAAC,CAAC,CAAC2K,OAAO,EAAE;YACrE,MAAMC,aAAa,GAAG,IAAI1O,IAAI,CAAC4F,IAAI,CAAC0H,QAAQ,CAACI,KAAK,GAAG,CAAC,CAAC,CAAC7J,SAAS,EAAEC,IAAI,IAAI,CAAC,CAAC,CAAC2K,OAAO,EAAE;YACvF,MAAME,WAAW,GAAGpI,IAAI,CAACqI,KAAK,CAAC,CAACF,aAAa,GAAGF,cAAc,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;YAE9ED,OAAO,CAAClF,SAAS,GAAG,yCAAyC,IAAI,CAAC2C,cAAc,CAAC2C,WAAW,CAAC,eAAelB,OAAO,CAACxJ,OAAO,EAAE8I,IAAI,EAAExI,IAAI,IAAI,iBAAiB,EAAE;YAE9JiJ,YAAY,CAAC9C,WAAW,CAAC6D,OAAO,CAAC;;QAErC,CAAC,CAAC;QAEFnC,YAAY,CAAC1B,WAAW,CAAC8C,YAAY,CAAC;;;IAI1C;IACA,MAAMqB,aAAa,GAAG,IAAI,CAAC9D,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAE5D,IAAIrH,MAAM,CAACiC,MAAM,IAAIjC,MAAM,CAACiC,MAAM,CAAC5C,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAM+L,UAAU,GAAGrN,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;MAChD+G,UAAU,CAAC9G,KAAK,CAACQ,OAAO,GAAG,MAAM;MACjCsG,UAAU,CAAC9G,KAAK,CAAC4C,aAAa,GAAG,QAAQ;MACzCkE,UAAU,CAAC9G,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE7BnH,MAAM,CAACiC,MAAM,CAAC/C,OAAO,CAAC,CAACiD,KAAK,EAAE6H,KAAK,KAAI;QACrC,MAAMqB,SAAS,GAAGtN,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QAC/CgH,SAAS,CAAC/G,KAAK,CAACY,OAAO,GAAG,MAAM;QAChCmG,SAAS,CAAC/G,KAAK,CAACM,eAAe,GAAG,SAAS;QAC3CyG,SAAS,CAAC/G,KAAK,CAACa,YAAY,GAAG,KAAK;QACpCkG,SAAS,CAAC/G,KAAK,CAACuB,MAAM,GAAG,gBAAgB;QAEzC;QACA,MAAMyF,WAAW,GAAGvN,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QACjDiH,WAAW,CAAChH,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCwG,WAAW,CAAChH,KAAK,CAACS,cAAc,GAAG,eAAe;QAClDuG,WAAW,CAAChH,KAAK,CAACkC,YAAY,GAAG,MAAM;QACvC8E,WAAW,CAAChH,KAAK,CAACmC,aAAa,GAAG,MAAM;QACxC6E,WAAW,CAAChH,KAAK,CAACoC,YAAY,GAAG,gBAAgB;QAEjD,MAAM6E,UAAU,GAAGxN,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QAChDkH,UAAU,CAAC5F,SAAS,GAAG,iBAAiBqE,KAAK,GAAG,CAAC,WAAW;QAC5DuB,UAAU,CAACjH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAElC,MAAMyF,UAAU,GAAGzN,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QAChDmH,UAAU,CAAClH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAClCyF,UAAU,CAAClH,KAAK,CAACyC,UAAU,GAAG,MAAM;QACpCyE,UAAU,CAAClH,KAAK,CAACkB,KAAK,GAAG,SAAS;QAClCgG,UAAU,CAAC3E,WAAW,GAAG,GAAG1E,KAAK,CAACC,KAAK,CAACY,MAAM,IAAIb,KAAK,CAACC,KAAK,CAAChF,QAAQ,EAAE;QAExEkO,WAAW,CAACtE,WAAW,CAACuE,UAAU,CAAC;QACnCD,WAAW,CAACtE,WAAW,CAACwE,UAAU,CAAC;QACnCH,SAAS,CAACrE,WAAW,CAACsE,WAAW,CAAC;QAElC;QACA,MAAMG,YAAY,GAAG1N,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;QAClDoH,YAAY,CAACnH,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnC2G,YAAY,CAACnH,KAAK,CAACoH,mBAAmB,GAAG,uCAAuC;QAChFD,YAAY,CAACnH,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B;QACA,MAAMwE,iBAAiB,GAAGxJ,KAAK,CAACE,YAAY,KAAKC,SAAS,GAAGH,KAAK,CAACE,YAAY,GACrDF,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACI,QAAQ,CAACC,kBAAkB,GAAG,CAAE;QACjF,MAAMH,YAAY,GAAG,IAAI,CAAC2F,aAAa,CAAC,cAAc,EAAE2D,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,eAAe,CAAC;QAC9GF,YAAY,CAACzE,WAAW,CAAC3E,YAAY,CAAC;QAEtC;QACA,IAAIF,KAAK,CAACyJ,SAAS,EAAE;UACnB,MAAMC,OAAO,GAAG,IAAI,CAAC7D,aAAa,CAAC,YAAY,EAAE,IAAI1L,IAAI,CAAC6F,KAAK,CAACyJ,SAAS,CAAC,CAACE,cAAc,EAAE,CAAC;UAC5FL,YAAY,CAACzE,WAAW,CAAC6E,OAAO,CAAC;;QAGnC;QACA,IAAI1J,KAAK,CAAC4J,WAAW,EAAE;UACrB,MAAMA,WAAW,GAAG,IAAI,CAAC/D,aAAa,CAAC,cAAc,EAAE7F,KAAK,CAAC4J,WAAW,CAAClL,IAAI,CAAC;UAC9E4K,YAAY,CAACzE,WAAW,CAAC+E,WAAW,CAAC;;QAGvC;QACA,IAAI5J,KAAK,CAAC6J,cAAc,EAAE;UACxB,MAAMC,UAAU,GAAG,IAAI,CAACjE,aAAa,CAAC,YAAY,EAAE7F,KAAK,CAAC6J,cAAc,CAACC,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC;UACnGR,YAAY,CAACzE,WAAW,CAACiF,UAAU,CAAC;;QAGtC;QACA,IAAI9J,KAAK,CAAC+J,mBAAmB,IAAI/J,KAAK,CAAC+J,mBAAmB,CAAC7M,MAAM,GAAG,CAAC,EAAE;UACrE,MAAM8M,YAAY,GAAGpO,QAAQ,CAACsG,aAAa,CAAC,IAAI,CAAC;UACjD8H,YAAY,CAACtF,WAAW,GAAG,qBAAqB;UAChDsF,YAAY,CAAC7H,KAAK,CAAC4E,SAAS,GAAG,MAAM;UACrCiD,YAAY,CAAC7H,KAAK,CAACkC,YAAY,GAAG,MAAM;UACxC2F,YAAY,CAAC7H,KAAK,CAACyB,QAAQ,GAAG,MAAM;UACpCoG,YAAY,CAAC7H,KAAK,CAACyC,UAAU,GAAG,KAAK;UACrCsE,SAAS,CAACrE,WAAW,CAACmF,YAAY,CAAC;UAEnC,MAAMC,gBAAgB,GAAGrO,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;UACtD+H,gBAAgB,CAAC9H,KAAK,CAACQ,OAAO,GAAG,MAAM;UACvCsH,gBAAgB,CAAC9H,KAAK,CAAC4C,aAAa,GAAG,QAAQ;UAC/CkF,gBAAgB,CAAC9H,KAAK,CAAC6C,GAAG,GAAG,MAAM;UACnCiF,gBAAgB,CAAC9H,KAAK,CAACkC,YAAY,GAAG,MAAM;UAE5C;UACA,MAAM6F,cAAc,GAAGlK,KAAK,CAAC+J,mBAAmB,CAACnM,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAACiL,WAAW,KAAK,CAAC,CAAC;UACjF,MAAMC,YAAY,GAAGpK,KAAK,CAAC+J,mBAAmB,CAACnM,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAACiL,WAAW,KAAK,CAAC,CAAC;UAC/E,MAAME,WAAW,GAAGrK,KAAK,CAAC+J,mBAAmB,CAACnM,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAACiL,WAAW,KAAK,CAAC,CAAC;UAE9E;UACA,IAAID,cAAc,CAAChN,MAAM,GAAG,CAAC,EAAE;YAC7BgN,cAAc,CAACnN,OAAO,CAACuN,OAAO,IAAG;cAC/B,MAAMC,WAAW,GAAG3O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC4H,WAAW,CAACpI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvC0H,WAAW,CAACpI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCwH,WAAW,CAACpI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C8H,WAAW,CAACpI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCuH,WAAW,CAACpI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM8G,WAAW,GAAG5O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDsI,WAAW,CAAChH,SAAS,GAAG,8FAA8F;cAEtH,MAAMiH,WAAW,GAAG7O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDuI,WAAW,CAACtI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC8H,WAAW,CAACtI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMoF,WAAW,GAAGvO,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDiI,WAAW,CAACzF,WAAW,GAAG,iBAAiB;cAC3CyF,WAAW,CAAChI,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCuF,WAAW,CAAChI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM8G,cAAc,GAAG9O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACpDwI,cAAc,CAACvI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC8G,cAAc,CAACvI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAIsH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACM,MAAM,GAAG,CAAC,EAAED,WAAW,IAAI,GAAGL,OAAO,CAACM,MAAM,KAAK;cAC7D,IAAIN,OAAO,CAACO,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIF,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACO,KAAK,WAAW;;cAE5CH,cAAc,CAAChG,WAAW,GAAGiG,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAAC5F,WAAW,CAACsF,WAAW,CAAC;cACpCM,WAAW,CAAC5F,WAAW,CAAC6F,cAAc,CAAC;cAEvCH,WAAW,CAAC1F,WAAW,CAAC2F,WAAW,CAAC;cACpCD,WAAW,CAAC1F,WAAW,CAAC4F,WAAW,CAAC;cACpCR,gBAAgB,CAACpF,WAAW,CAAC0F,WAAW,CAAC;YAC3C,CAAC,CAAC;;UAGJ;UACA,IAAIH,YAAY,CAAClN,MAAM,GAAG,CAAC,EAAE;YAC3BkN,YAAY,CAACrN,OAAO,CAACuN,OAAO,IAAG;cAC7B,MAAMC,WAAW,GAAG3O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC4H,WAAW,CAACpI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvC0H,WAAW,CAACpI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCwH,WAAW,CAACpI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C8H,WAAW,CAACpI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCuH,WAAW,CAACpI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM8G,WAAW,GAAG5O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDsI,WAAW,CAAChH,SAAS,GAAG,+FAA+F;cAEvH,MAAMiH,WAAW,GAAG7O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDuI,WAAW,CAACtI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC8H,WAAW,CAACtI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMoF,WAAW,GAAGvO,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDiI,WAAW,CAACzF,WAAW,GAAG,eAAe;cACzCyF,WAAW,CAAChI,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCuF,WAAW,CAAChI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM8G,cAAc,GAAG9O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACpDwI,cAAc,CAACvI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC8G,cAAc,CAACvI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAIsH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACM,MAAM,GAAG,CAAC,EAAED,WAAW,IAAI,GAAGL,OAAO,CAACM,MAAM,KAAK;cAC7D,IAAIN,OAAO,CAACO,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIF,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACO,KAAK,WAAW;;cAE5CH,cAAc,CAAChG,WAAW,GAAGiG,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAAC5F,WAAW,CAACsF,WAAW,CAAC;cACpCM,WAAW,CAAC5F,WAAW,CAAC6F,cAAc,CAAC;cAEvCH,WAAW,CAAC1F,WAAW,CAAC2F,WAAW,CAAC;cACpCD,WAAW,CAAC1F,WAAW,CAAC4F,WAAW,CAAC;cACpCR,gBAAgB,CAACpF,WAAW,CAAC0F,WAAW,CAAC;YAC3C,CAAC,CAAC;WACH,MAAM;YACL;YACA,MAAMA,WAAW,GAAG3O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;YACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;YAClC4H,WAAW,CAACpI,KAAK,CAACU,UAAU,GAAG,QAAQ;YACvC0H,WAAW,CAACpI,KAAK,CAACY,OAAO,GAAG,WAAW;YACvCwH,WAAW,CAACpI,KAAK,CAACM,eAAe,GAAG,SAAS;YAC7C8H,WAAW,CAACpI,KAAK,CAACa,YAAY,GAAG,KAAK;YACtCuH,WAAW,CAACpI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;YAE9C,MAAM8G,WAAW,GAAG5O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;YACjDsI,WAAW,CAAChH,SAAS,GAAG,+FAA+F;YAEvH,MAAMiH,WAAW,GAAG7O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;YACjDuI,WAAW,CAACtI,KAAK,CAACQ,OAAO,GAAG,MAAM;YAClC8H,WAAW,CAACtI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;YAE1C,MAAMoF,WAAW,GAAGvO,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;YACjDiI,WAAW,CAACzF,WAAW,GAAG,eAAe;YACzCyF,WAAW,CAAChI,KAAK,CAACyC,UAAU,GAAG,KAAK;YACpCuF,WAAW,CAAChI,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAEnC,MAAM8G,cAAc,GAAG9O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;YACpDwI,cAAc,CAACvI,KAAK,CAACyB,QAAQ,GAAG,MAAM;YACtC8G,cAAc,CAACvI,KAAK,CAACkB,KAAK,GAAG,MAAM;YACnCqH,cAAc,CAAChG,WAAW,GAAG,UAAU;YAEvC+F,WAAW,CAAC5F,WAAW,CAACsF,WAAW,CAAC;YACpCM,WAAW,CAAC5F,WAAW,CAAC6F,cAAc,CAAC;YAEvCH,WAAW,CAAC1F,WAAW,CAAC2F,WAAW,CAAC;YACpCD,WAAW,CAAC1F,WAAW,CAAC4F,WAAW,CAAC;YACpCR,gBAAgB,CAACpF,WAAW,CAAC0F,WAAW,CAAC;;UAG3C;UACA,IAAIF,WAAW,CAACnN,MAAM,GAAG,CAAC,EAAE;YAC1BmN,WAAW,CAACtN,OAAO,CAACuN,OAAO,IAAG;cAC5B,MAAMC,WAAW,GAAG3O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC4H,WAAW,CAACpI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvC0H,WAAW,CAACpI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCwH,WAAW,CAACpI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C8H,WAAW,CAACpI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCuH,WAAW,CAACpI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM8G,WAAW,GAAG5O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDsI,WAAW,CAAChH,SAAS,GAAG,kGAAkG;cAE1H,MAAMiH,WAAW,GAAG7O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDuI,WAAW,CAACtI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC8H,WAAW,CAACtI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMoF,WAAW,GAAGvO,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACjDiI,WAAW,CAACzF,WAAW,GAAG,cAAc;cACxCyF,WAAW,CAAChI,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCuF,WAAW,CAAChI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM8G,cAAc,GAAG9O,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;cACpDwI,cAAc,CAACvI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC8G,cAAc,CAACvI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAIsH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACM,MAAM,GAAG,CAAC,EAAED,WAAW,IAAI,GAAGL,OAAO,CAACM,MAAM,KAAK;cAC7D,IAAIN,OAAO,CAACO,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIF,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACO,KAAK,WAAW;;cAE5CH,cAAc,CAAChG,WAAW,GAAGiG,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAAC5F,WAAW,CAACsF,WAAW,CAAC;cACpCM,WAAW,CAAC5F,WAAW,CAAC6F,cAAc,CAAC;cAEvCH,WAAW,CAAC1F,WAAW,CAAC2F,WAAW,CAAC;cACpCD,WAAW,CAAC1F,WAAW,CAAC4F,WAAW,CAAC;cACpCR,gBAAgB,CAACpF,WAAW,CAAC0F,WAAW,CAAC;YAC3C,CAAC,CAAC;;UAGJrB,SAAS,CAACrE,WAAW,CAACoF,gBAAgB,CAAC;;QAGzCf,SAAS,CAACrE,WAAW,CAACyE,YAAY,CAAC;QAEnCL,UAAU,CAACpE,WAAW,CAACqE,SAAS,CAAC;MACnC,CAAC,CAAC;MAEFF,aAAa,CAACnE,WAAW,CAACoE,UAAU,CAAC;;IAGvC;IACA,IAAIpL,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,IAAID,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACgN,QAAQ,IAAIjN,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACgN,QAAQ,CAAC5N,MAAM,GAAG,CAAC,EAAE;MACtG,MAAM6N,eAAe,GAAG,IAAI,CAAC7F,aAAa,CAAC,UAAU,EAAE,mBAAmB,CAAC;MAE3E,MAAM8F,YAAY,GAAGpP,QAAQ,CAACsG,aAAa,CAAC,IAAI,CAAC;MACjD8I,YAAY,CAAC7I,KAAK,CAAC8I,SAAS,GAAG,MAAM;MACrCD,YAAY,CAAC7I,KAAK,CAACY,OAAO,GAAG,GAAG;MAChCiI,YAAY,CAAC7I,KAAK,CAACwC,MAAM,GAAG,GAAG;MAC/BqG,YAAY,CAAC7I,KAAK,CAACQ,OAAO,GAAG,MAAM;MACnCqI,YAAY,CAAC7I,KAAK,CAACoH,mBAAmB,GAAG,uCAAuC;MAChFyB,YAAY,CAAC7I,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE/BnH,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACgN,QAAQ,CAAC/N,OAAO,CAACmO,OAAO,IAAG;QACzC,MAAMC,WAAW,GAAGvP,QAAQ,CAACsG,aAAa,CAAC,IAAI,CAAC;QAChDiJ,WAAW,CAAChJ,KAAK,CAACY,OAAO,GAAG,MAAM;QAClCoI,WAAW,CAAChJ,KAAK,CAACM,eAAe,GAAG,SAAS;QAC7C0I,WAAW,CAAChJ,KAAK,CAACa,YAAY,GAAG,KAAK;QACtCmI,WAAW,CAAC3H,SAAS,GAAG,2EAA2E0H,OAAO,CAACxM,IAAI,IAAI,SAAS,EAAE;QAC9HsM,YAAY,CAACnG,WAAW,CAACsG,WAAW,CAAC;MACvC,CAAC,CAAC;MAEFJ,eAAe,CAAClG,WAAW,CAACmG,YAAY,CAAC;MACzClG,gBAAgB,CAACD,WAAW,CAACkG,eAAe,CAAC;;IAG/C;IACAjG,gBAAgB,CAACD,WAAW,CAACI,WAAW,CAAC;IACzCH,gBAAgB,CAACD,WAAW,CAAC0B,YAAY,CAAC;IAC1CzB,gBAAgB,CAACD,WAAW,CAACmE,aAAa,CAAC;IAE3C;IACAlG,YAAY,CAAC+B,WAAW,CAACtB,WAAW,CAAC;IACrCT,YAAY,CAAC+B,WAAW,CAACT,MAAM,CAAC;IAChCtB,YAAY,CAAC+B,WAAW,CAACC,gBAAgB,CAAC;IAC1C7C,QAAQ,CAAC4C,WAAW,CAAC/B,YAAY,CAAC;IAElC;IACAlH,QAAQ,CAACsI,IAAI,CAACW,WAAW,CAAC5C,QAAQ,CAAC;EACrC;EAEA;EACQiD,aAAaA,CAACT,KAAa,EAAE2G,SAAiB;IACpD,MAAM7J,OAAO,GAAG3F,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;IAC7CX,OAAO,CAACY,KAAK,CAACM,eAAe,GAAG,SAAS;IACzClB,OAAO,CAACY,KAAK,CAACa,YAAY,GAAG,KAAK;IAClCzB,OAAO,CAACY,KAAK,CAACY,OAAO,GAAG,MAAM;IAC9BxB,OAAO,CAACY,KAAK,CAACiB,SAAS,GAAG,+BAA+B;IAEzD,MAAMiI,aAAa,GAAGzP,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;IACnDmJ,aAAa,CAAClJ,KAAK,CAACQ,OAAO,GAAG,MAAM;IACpC0I,aAAa,CAAClJ,KAAK,CAACU,UAAU,GAAG,QAAQ;IACzCwI,aAAa,CAAClJ,KAAK,CAACkC,YAAY,GAAG,MAAM;IAEzC,MAAMvL,IAAI,GAAG8C,QAAQ,CAACsG,aAAa,CAAC,GAAG,CAAC;IACxCpJ,IAAI,CAACwS,SAAS,GAAG,OAAOF,SAAS,EAAE;IACnCtS,IAAI,CAACqJ,KAAK,CAACkB,KAAK,GAAG,SAAS;IAC5BvK,IAAI,CAACqJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC5B9K,IAAI,CAACqJ,KAAK,CAACqD,WAAW,GAAG,MAAM;IAE/B,MAAM+F,YAAY,GAAG3P,QAAQ,CAACsG,aAAa,CAAC,IAAI,CAAC;IACjDqJ,YAAY,CAAC7G,WAAW,GAAGD,KAAK;IAChC8G,YAAY,CAACpJ,KAAK,CAACwC,MAAM,GAAG,GAAG;IAC/B4G,YAAY,CAACpJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpC2H,YAAY,CAACpJ,KAAK,CAACyC,UAAU,GAAG,KAAK;IAErCyG,aAAa,CAACxG,WAAW,CAAC/L,IAAI,CAAC;IAC/BuS,aAAa,CAACxG,WAAW,CAAC0G,YAAY,CAAC;IACvChK,OAAO,CAACsD,WAAW,CAACwG,aAAa,CAAC;IAElC,OAAO9J,OAAO;EAChB;EAEA;EACQsE,aAAaA,CAAChN,KAAa,EAAED,KAAa;IAChD,MAAM4S,GAAG,GAAG5P,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;IACzCsJ,GAAG,CAACrJ,KAAK,CAACkC,YAAY,GAAG,MAAM;IAE/B,MAAMoH,YAAY,GAAG7P,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;IAClDuJ,YAAY,CAAC/G,WAAW,GAAG7L,KAAK;IAChC4S,YAAY,CAACtJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpC6H,YAAY,CAACtJ,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCoI,YAAY,CAACtJ,KAAK,CAACkC,YAAY,GAAG,KAAK;IAEvC,MAAMqH,YAAY,GAAG9P,QAAQ,CAACsG,aAAa,CAAC,KAAK,CAAC;IAClDwJ,YAAY,CAAChH,WAAW,GAAG9L,KAAK;IAChC8S,YAAY,CAACvJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAEpC4H,GAAG,CAAC3G,WAAW,CAAC4G,YAAY,CAAC;IAC7BD,GAAG,CAAC3G,WAAW,CAAC6G,YAAY,CAAC;IAE7B,OAAOF,GAAG;EACZ;EAEA;EACAG,gBAAgBA,CAAC9N,MAAc;IAC7B,IAAIA,MAAM,IAAIA,MAAM,CAACiC,MAAM,IAAIjC,MAAM,CAACiC,MAAM,CAAC5C,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAI0O,OAAO,GAAG/N,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,CAAC8L,OAAO,IAAI/N,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,CAAC+L,EAAE;MAE7D;MACA,IAAIC,QAAQ,GAAG,IAAI,CAAC1T,YAAY;MAEhC;MACA,IAAI,CAAC0T,QAAQ,EAAE;QACbA,QAAQ,GAAGjO,MAAM,CAACgO,EAAE;;MAGtBvO,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEuO,QAAQ,EAAE,cAAc,EAAEF,OAAO,CAAC;MAExF;MACA,IAAI,CAAChU,MAAM,CAACmU,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UACXF,QAAQ,EAAEA,QAAQ;UAClBF,OAAO,EAAEA;;OAEZ,CAAC;KACH,MAAM;MACLtO,OAAO,CAAC2O,KAAK,CAAC,sCAAsC,EAAEpO,MAAM,CAAC;;EAEjE;EAEAR,gBAAgBA,CAAA;IACd;IACA;IACA;EAAA;EAGFD,iBAAiBA,CAAA;IACf;IACA,MAAM8O,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACjC,MAAMC,mBAAmB,GAAG,CAAC,CAAC,CAAG;IAEjC,IAAI,CAACxU,cAAc,CAACyU,kBAAkB,CAACF,qBAAqB,CAAC,CAACG,SAAS,CAACC,SAAS,IAAG;MAClF,IAAI,CAACzU,kBAAkB,GAAGyU,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAAC3U,cAAc,CAACyU,kBAAkB,CAACD,mBAAmB,CAAC,CAACE,SAAS,CAACC,SAAS,IAAG;MAChF,IAAI,CAACxU,gBAAgB,GAAGwU,SAAS;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAAChS,UAAU,CAACiS,GAAG,CAAC,mBAAmB,CAAC,EAAEC,YAAY,CACnDC,IAAI,CACHzV,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC0B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B;QACA,IAAIA,KAAK,CAACsE,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACvF,cAAc,CAACyU,kBAAkB,CAACF,qBAAqB,CAAC,CAACO,IAAI,CACvEtV,GAAG,CAACmV,SAAS,IAAIA,SAAS,CAAC1O,MAAM,CAAC8O,QAAQ,IACxCA,QAAQ,CAAChO,IAAI,CAACiO,WAAW,EAAE,CAAChO,QAAQ,CAAC/F,KAAK,CAAC+T,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACrG,IAAI,IAAIqG,QAAQ,CAACrG,IAAI,CAACsG,WAAW,EAAE,CAAChO,QAAQ,CAAC/F,KAAK,CAAC+T,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAChV,cAAc,CAACyU,kBAAkB,CAACF,qBAAqB,CAAC;;;MAGxE,OAAO9U,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAiV,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACzU,kBAAkB,GAAGyU,SAAS;IACrC,CAAC,CAAC;IAEJ;IACA,IAAI,CAAChS,UAAU,CAACiS,GAAG,CAAC,iBAAiB,CAAC,EAAEC,YAAY,CACjDC,IAAI,CACHzV,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC0B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B;QACA,IAAIA,KAAK,CAACsE,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACvF,cAAc,CAACyU,kBAAkB,CAACD,mBAAmB,CAAC,CAACM,IAAI,CACrEtV,GAAG,CAACmV,SAAS,IAAIA,SAAS,CAAC1O,MAAM,CAAC8O,QAAQ,IACxCA,QAAQ,CAAChO,IAAI,CAACiO,WAAW,EAAE,CAAChO,QAAQ,CAAC/F,KAAK,CAAC+T,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACrG,IAAI,IAAIqG,QAAQ,CAACrG,IAAI,CAACsG,WAAW,EAAE,CAAChO,QAAQ,CAAC/F,KAAK,CAAC+T,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAChV,cAAc,CAACyU,kBAAkB,CAACD,mBAAmB,CAAC;;;MAGtE,OAAO/U,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAiV,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACxU,gBAAgB,GAAGwU,SAAS;IACnC,CAAC,CAAC;EACN;EAEAM,eAAeA,CAACF,QAA+B;IAC7C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAIG,WAAW,GAAGH,QAAQ,CAAChO,IAAI;IAC/B,IAAIgO,QAAQ,CAACrG,IAAI,EAAE;MACjBwG,WAAW,IAAI,KAAKH,QAAQ,CAACrG,IAAI,GAAG;;IAEtC,IAAIqG,QAAQ,CAACvQ,IAAI,KAAK7E,YAAY,CAACwV,OAAO,IAAIJ,QAAQ,CAACxF,IAAI,EAAE;MAC3D2F,WAAW,IAAI,MAAMH,QAAQ,CAACxF,IAAI,EAAE;;IAEtC,OAAO2F,WAAW;EACpB;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzS,UAAU,CAAC0S,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC3S,UAAU,CAAC;MAC1C;;IAGF,IAAI,CAACvC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACI,YAAY,GAAG,EAAE;IACtB,IAAI,CAACD,WAAW,GAAG,IAAI;IAEvB,MAAMgV,SAAS,GAAG,IAAI,CAAC5S,UAAU,CAAC1B,KAAK;IAEvC;IACA,MAAMuU,OAAO,GAAuB;MAClCC,WAAW,EAAEF,SAAS,CAAC1S,WAAW;MAClC6S,YAAY,EAAEH,SAAS,CAACxS,YAAY;MACpC4S,OAAO,EAAEJ,SAAS,CAACrS,aAAa;MAChC0S,kBAAkB,EAAE,CAClB;QACE1B,EAAE,EAAEqB,SAAS,CAACvS,iBAAiB,EAAEkR,EAAE,IAAI,EAAE;QACzC1P,IAAI,EAAE,CAAC,CAAC;OACT,CACF;;MACDqR,gBAAgB,EAAE,CAChB;QACE3B,EAAE,EAAEqB,SAAS,CAACtS,eAAe,EAAEiR,EAAE,IAAI,EAAE;QACvC1P,IAAI,EAAE,CAAC,CAAC;OACT,CACF;;MACDsR,UAAU,EAAE,IAAI,CAAC7Q,kBAAkB,EAAE;MACrC8Q,qBAAqB,EAAER,SAAS,CAACnS,OAAO;MACxC4S,oBAAoB,EAAE;QACpBC,oBAAoB,EAAE;UACpBtS,sBAAsB,EAAE4R,SAAS,CAAC5R;;OAErC;MACDJ,sBAAsB,EAAEgS,SAAS,CAAChS,sBAAsB;MACxDC,wBAAwB,EAAE+R,SAAS,CAAC/R,wBAAwB;MAC5DC,6BAA6B,EAAE8R,SAAS,CAAC9R,6BAA6B;MACtEC,mBAAmB,EAAE6R,SAAS,CAAC7R,mBAAmB;MAClDvB,aAAa,EAAE,CAACoT,SAAS,CAACpS,WAAW,CAAC;MACtC+S,OAAO,EAAEX,SAAS,CAAClS,OAAO;MAC1B8S,QAAQ,EAAEZ,SAAS,CAACjS;KACrB;IAED,IAAI,CAACtD,cAAc,CAACoW,WAAW,CAACZ,OAAO,CAAC,CACrCd,SAAS,CAAC;MACT2B,IAAI,EAAGC,QAA6B,IAAI;QACtC,IAAI,CAAClW,SAAS,GAAG,KAAK;QACtB,IAAIkW,QAAQ,CAAC7J,MAAM,CAAC8J,OAAO,EAAE;UAC3B,IAAI,CAAClW,aAAa,GAAGiW,QAAQ,CAAC/J,IAAI,CAACnF,OAAO;UAE1C;UACA,IAAI,CAAC+B,qBAAqB,EAAE;UAE5B;UACA,IAAI,CAACpD,eAAe,EAAE;UAEtB;UACAJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4Q,IAAI,CAACC,SAAS,CAACH,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEpE;UACA,IAAIA,QAAQ,CAAC/J,IAAI,IAAI+J,QAAQ,CAAC/J,IAAI,CAACnF,OAAO,IAAIkP,QAAQ,CAAC/J,IAAI,CAACnF,OAAO,CAAC7B,MAAM,GAAG,CAAC,EAAE;YAC9EI,OAAO,CAAC/C,KAAK,CAAC,uBAAuB,CAAC;YACtC+C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE0Q,QAAQ,CAAC/J,IAAI,CAACnF,OAAO,CAAC7B,MAAM,CAAC;YAE3D;YACA,MAAMmR,iBAAiB,GAAGJ,QAAQ,CAAC/J,IAAI,CAACnF,OAAO,CAACnB,MAAM,CAAC0Q,CAAC,IAAIA,CAAC,CAACxO,MAAM,IAAIwO,CAAC,CAACxO,MAAM,CAAC5C,MAAM,GAAG,CAAC,CAAC;YAC5FI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8Q,iBAAiB,CAACnR,MAAM,CAAC;YAE7D;YACA,MAAMqR,kBAAkB,GAAGF,iBAAiB,CAACG,OAAO,CAACF,CAAC,IAAIA,CAAC,CAACxO,MAAM,CAAC3I,GAAG,CAACsX,CAAC,IACtEA,CAAC,CAACvO,YAAY,KAAKC,SAAS,GAAGsO,CAAC,CAACvO,YAAY,GAAIuO,CAAC,CAACrO,QAAQ,GAAGqO,CAAC,CAACrO,QAAQ,CAACC,kBAAkB,GAAG,CAAE,CACjG,CAAC;YACF/C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgR,kBAAkB,CAAC;YAEvD;YACA,MAAMG,kBAAkB,GAAGH,kBAAkB,CAAC9R,MAAM,CAAC,CAACkS,GAAG,EAAEC,GAAG,KAAI;cAChE,IAAIA,GAAG,KAAKzO,SAAS,EAAE;gBACrBwO,GAAG,CAACC,GAAG,CAAC,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEhC,OAAOD,GAAG;YACZ,CAAC,EAAE,EAA4B,CAAC;YAChCrR,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmR,kBAAkB,CAAC;YAEvD;YACA,MAAMG,iBAAiB,GAAGR,iBAAiB,CAACzQ,MAAM,CAAC0Q,CAAC,IAClDA,CAAC,CAACxO,MAAM,CAACgP,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC5E,cAAc,IAAI4E,CAAC,CAAC5E,cAAc,CAACC,UAAU,KAAK,IAAI,CAAC,CAC7E;YACDxM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEsR,iBAAiB,CAAC3R,MAAM,CAAC;YAE5DI,OAAO,CAACyR,QAAQ,EAAE;;UAGpB;UACA,IAAId,QAAQ,CAAC/J,IAAI,IAAI+J,QAAQ,CAAC/J,IAAI,CAAC4H,QAAQ,EAAE;YAC3C,IAAI,CAAC1T,YAAY,GAAG6V,QAAQ,CAAC/J,IAAI,CAAC4H,QAAQ;YAC1CxO,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACnF,YAAY,CAAC;;UAErE;UAAA,KACK,IAAI6V,QAAQ,CAAC7J,MAAM,IAAI6J,QAAQ,CAAC7J,MAAM,CAAC4K,SAAS,EAAE;YACrD,IAAI,CAAC5W,YAAY,GAAG6V,QAAQ,CAAC7J,MAAM,CAAC4K,SAAS;YAC7C1R,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACnF,YAAY,CAAC;;UAExE;UAAA,KACK,IAAI6V,QAAQ,CAAC/J,IAAI,IAAI+J,QAAQ,CAAC/J,IAAI,CAACnF,OAAO,IAAIkP,QAAQ,CAAC/J,IAAI,CAACnF,OAAO,CAAC7B,MAAM,GAAG,CAAC,IAAI+Q,QAAQ,CAAC/J,IAAI,CAACnF,OAAO,CAAC,CAAC,CAAC,CAAC8M,EAAE,EAAE;YAClH,IAAI,CAACzT,YAAY,GAAG6V,QAAQ,CAAC/J,IAAI,CAACnF,OAAO,CAAC,CAAC,CAAC,CAAC8M,EAAE;YAC/CvO,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACnF,YAAY,CAAC;WAChE,MAAM;YACLkF,OAAO,CAAC2O,KAAK,CAAC,qCAAqC,CAAC;YACpD3O,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEhB,MAAM,CAAC8E,IAAI,CAAC4M,QAAQ,CAAC,CAAC;YACzD,IAAIA,QAAQ,CAAC/J,IAAI,EAAE5G,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEhB,MAAM,CAAC8E,IAAI,CAAC4M,QAAQ,CAAC/J,IAAI,CAAC,CAAC;YAC7E,IAAI+J,QAAQ,CAAC7J,MAAM,EAAE9G,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEhB,MAAM,CAAC8E,IAAI,CAAC4M,QAAQ,CAAC7J,MAAM,CAAC,CAAC;;SAEtF,MAAM;UACL,IAAI,CAACjM,YAAY,GAAG,sDAAsD;UAC1E,IAAI8V,QAAQ,CAAC7J,MAAM,CAAC6K,QAAQ,IAAIhB,QAAQ,CAAC7J,MAAM,CAAC6K,QAAQ,CAAC/R,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAAC/E,YAAY,GAAG8V,QAAQ,CAAC7J,MAAM,CAAC6K,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACDjD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAClU,SAAS,GAAG,KAAK;QACtB,IAAI,CAACI,YAAY,GAAG,wDAAwD;QAC5EmF,OAAO,CAAC2O,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEA;EACAgB,oBAAoBA,CAACkC,SAAoB;IACvC5S,MAAM,CAACC,MAAM,CAAC2S,SAAS,CAACC,QAAQ,CAAC,CAACrS,OAAO,CAACsS,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAYvY,SAAS,EAAE;QAChC,IAAI,CAACmW,oBAAoB,CAACoC,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA;EACAlJ,cAAcA,CAACoJ,OAAe;IAC5B,MAAMrR,KAAK,GAAGwC,IAAI,CAACqI,KAAK,CAACwG,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMC,IAAI,GAAGD,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGrR,KAAK,KAAKsR,IAAI,KAAK;EAC/B;EAEA;EACAC,UAAUA,CAACC,UAAkB;IAC3B,MAAMzR,IAAI,GAAG,IAAI9D,IAAI,CAACuV,UAAU,CAAC;IACjC,OAAOzR,IAAI,CAACgI,kBAAkB,CAAC,OAAO,EAAE;MACtC0J,OAAO,EAAE,OAAO;MAChBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACdjJ,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACAiJ,WAAWA,CAACjS,MAAc;IACxB,IAAI,CAACA,MAAM,CAACiC,MAAM,IAAIjC,MAAM,CAACiC,MAAM,CAAC5C,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd,MAAM6S,QAAQ,GAAGlS,MAAM,CAACiC,MAAM,CAACrD,MAAM,CAAC,CAACkE,GAAG,EAAEX,KAAK,KAC/CA,KAAK,CAACC,KAAK,CAACY,MAAM,GAAGF,GAAG,CAACV,KAAK,CAACY,MAAM,GAAGb,KAAK,GAAGW,GAAG,EAAE9C,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,CAAC;IAExE,OAAOiQ,QAAQ,CAAC9P,KAAK,CAAC+P,eAAe,IAAI,GAAGD,QAAQ,CAAC9P,KAAK,CAACY,MAAM,IAAIkP,QAAQ,CAAC9P,KAAK,CAAChF,QAAQ,EAAE;EAChG;EAEA;EACAgV,iBAAiBA,CAACpS,MAAc;IAC9B;IACA,IAAI,CAACA,MAAM,CAACiC,MAAM,IAAIjC,MAAM,CAACiC,MAAM,CAAC5C,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd;IACA,MAAM8C,KAAK,GAAGnC,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC;IAC9B,MAAM0J,iBAAiB,GAAGxJ,KAAK,CAACE,YAAY,KAAKC,SAAS,GAAGH,KAAK,CAACE,YAAY,GACrDF,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACI,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IACjF,OAAOmJ,iBAAiB,GAAG,CAAC;EAC9B;EAEA;EACA0G,yBAAyBA,CAAA;IACvB,MAAMC,YAAY,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,CAACxY,cAAc,CAACyU,kBAAkB,CAAC+D,YAAY,CAAC,CAAC9D,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACzU,kBAAkB,GAAGyU,SAAS;MACnC;MACA,MAAM8D,KAAK,GAAGxU,QAAQ,CAACyU,cAAc,CAAC,mBAAmB,CAAqB;MAC9E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAC,uBAAuBA,CAAA;IACrB,MAAMN,YAAY,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,CAACxY,cAAc,CAACyU,kBAAkB,CAAC+D,YAAY,CAAC,CAAC9D,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACxU,gBAAgB,GAAGwU,SAAS;MACjC;MACA,MAAM8D,KAAK,GAAGxU,QAAQ,CAACyU,cAAc,CAAC,iBAAiB,CAAqB;MAC5E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAE,aAAaA,CAAA;IACX,MAAM/V,iBAAiB,GAAG,IAAI,CAACL,UAAU,CAACiS,GAAG,CAAC,mBAAmB,CAAC,EAAE3T,KAAK;IACzE,MAAMgC,eAAe,GAAG,IAAI,CAACN,UAAU,CAACiS,GAAG,CAAC,iBAAiB,CAAC,EAAE3T,KAAK;IAErE,IAAI,CAAC0B,UAAU,CAACqW,UAAU,CAAC;MACzBhW,iBAAiB,EAAEC,eAAe;MAClCA,eAAe,EAAED;KAClB,CAAC;EACJ;EAEA;EACAiW,oBAAoBA,CAAClB,UAAkB;IACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B;IACA,MAAMzR,IAAI,GAAG,IAAI9D,IAAI,CAACuV,UAAU,CAAC;IACjC,OAAOzR,IAAI,CAAC0L,cAAc,EAAE;EAC9B;EAEA;EACAkH,kBAAkBA,CAAC1G,WAAmB;IACpC,QAAQA,WAAW;MACjB,KAAK,CAAC;QAAE,OAAO,eAAe;MAC9B,KAAK,CAAC;QAAE,OAAO,iBAAiB;MAChC,KAAK,CAAC;QAAE,OAAO,cAAc;MAC7B;QAAS,OAAO,SAAS;;EAE7B;EAEA;EACA2G,mBAAmBA,CAAC/G,mBAA0B,EAAE5N,IAAY;IAC1D,IAAI,CAAC4N,mBAAmB,IAAI,CAACgH,KAAK,CAACC,OAAO,CAACjH,mBAAmB,CAAC,EAAE;MAC/D,OAAO,EAAE;;IAEX,OAAOA,mBAAmB,CAACnM,MAAM,CAAC0M,OAAO,IAAIA,OAAO,CAACH,WAAW,KAAKhO,IAAI,CAAC;EAC5E;EAEA;EACA8U,oBAAoBA,CAACC,aAAqB;IACxC,QAAQA,aAAa;MACnB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,WAAW;;EAE/B;EAEA;EACAC,oBAAoBA,CAACC,cAAmB,EAAEC,WAAgB;IACxD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAAChT,OAAO,IAAI,CAACgT,cAAc,CAAChT,OAAO,CAACH,IAAI,IAC1E,CAACoT,WAAW,IAAI,CAACA,WAAW,CAACrT,SAAS,IAAI,CAACqT,WAAW,CAACrT,SAAS,CAACC,IAAI,EAAE;MACzE,OAAO,SAAS;;IAGlB,MAAMxE,WAAW,GAAG,IAAIU,IAAI,CAACiX,cAAc,CAAChT,OAAO,CAACH,IAAI,CAAC,CAAC2K,OAAO,EAAE;IACnE,MAAMxP,aAAa,GAAG,IAAIe,IAAI,CAACkX,WAAW,CAACrT,SAAS,CAACC,IAAI,CAAC,CAAC2K,OAAO,EAAE;IACpE,MAAM0I,MAAM,GAAGlY,aAAa,GAAGK,WAAW;IAC1C,MAAM8X,QAAQ,GAAG7Q,IAAI,CAACqI,KAAK,CAACuI,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIC,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,KAAK;KACxB,MAAM;MACL,MAAMrT,KAAK,GAAGwC,IAAI,CAACqI,KAAK,CAACwI,QAAQ,GAAG,EAAE,CAAC;MACvC,MAAM/B,IAAI,GAAG+B,QAAQ,GAAG,EAAE;MAC1B,OAAO,GAAGrT,KAAK,KAAKsR,IAAI,KAAK;;EAEjC;CACD;AAjiDCgC,UAAA,EADC3a,YAAY,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,0DAS1C;AA5IUW,oBAAoB,GAAAga,UAAA,EANhC7a,SAAS,CAAC;EACT8a,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,8BAA8B,EAAE,mBAAmB,CAAC;EAChEC,aAAa,EAAEhb,iBAAiB,CAACib;CAClC,CAAC,C,EACWra,oBAAoB,CAqqDhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}