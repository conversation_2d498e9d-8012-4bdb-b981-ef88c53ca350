import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BookingService } from '../../../services/booking.service';
import { AuthService } from '../../../services/auth.service';
import { SharedDataService } from '../../../services/shared-data.service';
import { CountryService, Country } from '../../../services/country.service';
import {
  BookingTransactionResponse,
  BeginTransactionResponse,
  SetReservationInfoRequest,
  SetReservationInfoResponse,
  CommitTransactionResponse,
  TravellerBeginResponse,
  TravellerRequest,
  PassportInfoRequest,
  AddressRequest,
  ContactPhoneRequest,
  CityRequest,
  CountryRequest,
  NationalityRequest
} from '../../../models/booking';
import { TravellerTitle, Gender, PassengerType } from '../../../models/enums.model';

@Component({
  selector: 'app-booking-transaction',
  templateUrl: './booking-transaction.component.html',
  styleUrls: ['./booking-transaction.component.css']
})
export class BookingTransactionComponent implements OnInit {
  // Étape actuelle du processus de réservation
  currentStep = 1;

  // Formulaires pour chaque étape
  beginTransactionForm: FormGroup;
  reservationInfoForm: FormGroup;
  commitTransactionForm: FormGroup;

  // Données de transaction
  transactionId: string = '';
  offerIds: string[] = [];
  searchId: string = '';
  passengersParam: string = '';

  // Informations de passagers
  passengerCounts: { [key: number]: number } = {};

  // Réponses des différentes étapes
  beginResponse: BeginTransactionResponse | null = null;
  infoResponse: SetReservationInfoResponse | null = null;
  commitResponse: CommitTransactionResponse | null = null;

  // États de chargement et d'erreur
  isLoading = false;
  errorMessage = '';

  // Options pour les formulaires
  travellerTitles = Object.keys(TravellerTitle)
    .filter(key => !isNaN(Number(TravellerTitle[key as keyof typeof TravellerTitle])))
    .map(key => ({
      value: TravellerTitle[key as keyof typeof TravellerTitle],
      label: key
    }));

  genderOptions = Object.keys(Gender)
    .filter(key => !isNaN(Number(Gender[key as keyof typeof Gender])))
    .map(key => ({
      value: Gender[key as keyof typeof Gender],
      label: key
    }));

  passengerTypes = Object.keys(PassengerType)
    .filter(key => !isNaN(Number(PassengerType[key as keyof typeof PassengerType])))
    .map(key => ({
      value: PassengerType[key as keyof typeof PassengerType],
      label: key
    }));

  // Liste des pays pour le sélecteur
  countries: Country[] = [];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private bookingService: BookingService,
    private authService: AuthService,
    private sharedDataService: SharedDataService,
    private countryService: CountryService,
    private snackBar: MatSnackBar
  ) {
    // Initialisation des formulaires sans valeurs par défaut
    this.beginTransactionForm = this.fb.group({
      currency: ['', Validators.required],
      culture: ['', Validators.required]
    });

    this.reservationInfoForm = this.fb.group({
      transactionId: ['', Validators.required],
      travellers: this.fb.array([]),
      reservationNote: [''],
      agencyReservationNumber: ['']
    });

    this.commitTransactionForm = this.fb.group({
      transactionId: ['', Validators.required],
      paymentOption: [null],
      paymentInformation: this.fb.group({
        accountName: [''],
        paymentTypeId: [null],
        paymentPrice: this.fb.group({
          amount: [null, Validators.required],
          currency: ['', Validators.required]
        }),
        installmentCount: [''],
        paymentDate: [''],
        receiptType: [''],
        reference: [''],
        paymentToken: ['']
      })
    });
  }

  ngOnInit(): void {
    // Charger la liste des pays
    this.countryService.getCountries().subscribe(countries => {
      this.countries = countries;
    });
    // Récupérer les offerIds, transactionId et informations de passagers depuis les paramètres de l'URL
    this.route.queryParams.subscribe(params => {
      // Récupérer les offerIds
      if (params['offerIds']) {
        try {
          this.offerIds = Array.isArray(params['offerIds'])
            ? params['offerIds']
            : [params['offerIds']];

          if (this.offerIds.length > 0) {
            console.log('OfferIds récupérés:', this.offerIds);
          } else {
            this.errorMessage = 'Aucun ID d\'offre n\'a été fourni.';
          }
        } catch (error) {
          console.error('Erreur lors de la récupération des offerIds:', error);
          this.errorMessage = 'Format d\'ID d\'offre invalide.';
        }
      } else {
        this.errorMessage = 'Aucun ID d\'offre n\'a été fourni.';
      }

      // Récupérer le searchId s'il est présent
      if (params['searchId']) {
        this.searchId = params['searchId'];
        console.log('SearchId récupéré:', this.searchId);
      }

      // Récupérer les informations de passagers
      if (params['passengers']) {
        this.passengersParam = params['passengers'];
        try {
          this.passengerCounts = JSON.parse(this.passengersParam);
          // Mettre à jour le service partagé avec les informations de passagers
          this.sharedDataService.setPassengerCounts(this.passengerCounts);
          console.log('Passenger counts parsed successfully:', this.passengerCounts);
        } catch (error) {
          console.error('Error parsing passenger information:', error);
          // Utiliser les valeurs par défaut du service
          this.passengerCounts = this.sharedDataService.getPassengerCounts();
        }
      } else {
        // Utiliser les valeurs par défaut du service
        this.passengerCounts = this.sharedDataService.getPassengerCounts();
      }

      // Vérifier si une transaction a déjà été démarrée automatiquement
      if (params['transactionId'] && params['autoStarted'] === 'true') {
        console.log('Transaction déjà démarrée avec ID:', params['transactionId']);
        this.transactionId = params['transactionId'];

        // Mettre à jour le formulaire d'informations de réservation avec l'ID de transaction
        this.reservationInfoForm.patchValue({
          transactionId: this.transactionId
        });

        // Créer un objet de réponse simulé pour l'étape 1
        this.beginResponse = {
          header: {
            requestId: '',
            success: true,
            responseTime: new Date().toISOString(),
            messages: []
          },
          body: {
            transactionId: this.transactionId,
            expiresOn: new Date(Date.now() + 3600000).toISOString(), // Expire dans 1 heure
            status: 1,
            transactionType: 1,
            reservationData: {
              travellers: [],
              reservationInfo: {
                bookingNumber: '',
                agency: {
                  code: '',
                  name: '',
                  country: { id: '', name: '' },
                  address: {},
                  ownAgency: false,
                  aceExport: false
                },
                agencyUser: {
                  id: 0,
                  name: '',
                  surname: '',
                  email: ''
                },
                beginDate: new Date().toISOString(),
                endDate: new Date().toISOString(),
                note: '',
                salePrice: { amount: 0, currency: 'EUR' },
                supplementDiscount: { amount: 0, currency: 'EUR' },
                passengerEB: { amount: 0, currency: 'EUR' },
                agencyEB: { amount: 0, currency: 'EUR' },
                passengerAmountToPay: { amount: 0, currency: 'EUR' },
                agencyAmountToPay: { amount: 0, currency: 'EUR' },
                discount: { amount: 0, currency: 'EUR' },
                agencyBalance: { amount: 0, currency: 'EUR' },
                passengerBalance: { amount: 0, currency: 'EUR' },
                agencyCommission: { amount: 0, currency: 'EUR', rate: 0 },
                brokerCommission: { amount: 0, currency: 'EUR', rate: 0 },
                agencySupplementCommission: { amount: 0, currency: 'EUR', rate: 0 },
                promotionAmount: { amount: 0, currency: 'EUR' },
                priceToPay: { amount: 0, currency: 'EUR' },
                agencyPriceToPay: { amount: 0, currency: 'EUR' },
                passengerPriceToPay: { amount: 0, currency: 'EUR' },
                totalPrice: { amount: 0, currency: 'EUR' }
              },
              services: [],
              paymentDetail: {},
              invoices: []
            }
          }
        };

        // Créer dynamiquement les voyageurs en fonction des informations de passagers
        this.createTravellersFromPassengerCounts();

        // Passer directement à l'étape 2
        this.currentStep = 2;

        // Afficher un message d'information
        this.snackBar.open('Transaction déjà démarrée. Veuillez compléter les informations des voyageurs.', 'Fermer', {
          duration: 5000,
          panelClass: ['info-snackbar']
        });
      }
    });
  }

  // Créer dynamiquement les voyageurs en fonction des informations de passagers
  createTravellersFromPassengerCounts(): void {
    // Vider le tableau de voyageurs existant
    while (this.travellers.length > 0) {
      this.travellers.removeAt(0);
    }

    // Parcourir les types de passagers et créer le nombre correspondant de voyageurs
    Object.entries(this.passengerCounts).forEach(([typeStr, count]) => {
      const type = parseInt(typeStr);
      for (let i = 0; i < count; i++) {
        // Créer un voyageur avec le type de passager correspondant
        const travellerForm = this.fb.group({
          type: [type, Validators.required], // Type de voyageur (adulte, enfant, etc.)
          title: [null, Validators.required], // Titre (M., Mme, etc.)
          passengerType: [type, Validators.required], // Type de passager (même que le type de voyageur)
          name: ['', [Validators.required, Validators.minLength(2)]],
          surname: ['', [Validators.required, Validators.minLength(2)]],
          isLeader: [this.travellers?.length === 0], // Premier voyageur est le leader par défaut
          birthDate: ['', Validators.required],
          nationality: this.fb.group({
            twoLetterCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]
          }),
          identityNumber: [''],
          passportInfo: this.fb.group({
            serial: [''],
            number: ['', [Validators.required, Validators.minLength(5)]],
            expireDate: ['', Validators.required],
            issueDate: ['', Validators.required],
            citizenshipCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],
            issueCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]
          }),
          address: this.fb.group({
            contactPhone: this.fb.group({
              countryCode: ['', Validators.required],
              areaCode: [''],
              phoneNumber: ['', [Validators.required, Validators.minLength(5)]]
            }),
            email: ['', [Validators.required, Validators.email]],
            address: ['', [Validators.required, Validators.minLength(5)]],
            zipCode: ['', [Validators.required, Validators.minLength(3)]],
            city: this.fb.group({
              id: ['', Validators.required],
              name: ['', [Validators.required, Validators.minLength(2)]]
            }),
            country: this.fb.group({
              id: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],
              name: ['', [Validators.required, Validators.minLength(2)]]
            })
          }),
          gender: [null, Validators.required]
        });

        // Ajouter des validateurs personnalisés
        this.addCustomValidators(travellerForm);

        // Ajouter le voyageur au formulaire
        this.travellers.push(travellerForm);
      }
    });

    // S'assurer qu'il y a au moins un voyageur
    if (this.travellers.length === 0) {
      this.addTraveller();
    }
  }

  // Getter pour accéder au FormArray des voyageurs
  get travellers(): FormArray {
    return this.reservationInfoForm.get('travellers') as FormArray;
  }

  // Ajouter un nouveau voyageur au formulaire
  addTraveller(): void {
    const travellerForm = this.fb.group({
      type: [null, Validators.required], // Type de voyageur
      title: [null, Validators.required], // Titre
      passengerType: [null, Validators.required], // Type de passager
      name: ['', [Validators.required, Validators.minLength(2)]],
      surname: ['', [Validators.required, Validators.minLength(2)]],
      isLeader: [this.travellers?.length === 0], // Premier voyageur est le leader par défaut
      birthDate: ['', Validators.required],
      nationality: this.fb.group({
        twoLetterCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]
      }),
      identityNumber: [''],
      passportInfo: this.fb.group({
        serial: [''],
        number: ['', [Validators.required, Validators.minLength(5)]],
        expireDate: ['', Validators.required],
        issueDate: ['', Validators.required],
        citizenshipCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],
        issueCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]
      }),
      address: this.fb.group({
        contactPhone: this.fb.group({
          countryCode: ['', Validators.required],
          areaCode: [''],
          phoneNumber: ['', [Validators.required, Validators.minLength(5)]]
        }),
        email: ['', [Validators.required, Validators.email]],
        address: ['', [Validators.required, Validators.minLength(5)]],
        zipCode: ['', [Validators.required, Validators.minLength(3)]],
        city: this.fb.group({
          id: ['', Validators.required],
          name: ['', [Validators.required, Validators.minLength(2)]]
        }),
        country: this.fb.group({
          id: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],
          name: ['', [Validators.required, Validators.minLength(2)]]
        })
      }),
      gender: [null, Validators.required] // Genre
    });

    // Ajouter des validateurs personnalisés
    this.addCustomValidators(travellerForm);

    this.travellers.push(travellerForm);
  }

  // Ajouter des validateurs personnalisés au formulaire de voyageur
  private addCustomValidators(form: FormGroup): void {
    // Ne pas synchroniser automatiquement issueCountryCode avec la nationalité
    // pour permettre une saisie entièrement dynamique
    const nationalityControl = form.get('nationality.twoLetterCode');
    const issueCountryCodeControl = form.get('passportInfo.issueCountryCode');

    // Nous ne synchronisons plus automatiquement les valeurs
    // L'utilisateur doit remplir tous les champs manuellement

    // Vérifier que la date d'expiration du passeport est dans le futur
    const expireDateControl = form.get('passportInfo.expireDate');
    if (expireDateControl) {
      expireDateControl.setValidators([
        Validators.required,
        (control) => {
          const value = control.value;
          if (!value) return null;

          const expireDate = new Date(value);
          const today = new Date();

          return expireDate > today ? null : { 'expireDateInvalid': true };
        }
      ]);
    }
  }

  // Supprimer un voyageur du formulaire
  removeTraveller(index: number): void {
    this.travellers.removeAt(index);
  }

  // Démarrer la transaction de réservation
  beginTransaction(): void {
    if (this.beginTransactionForm.invalid) {
      this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    const formValue = this.beginTransactionForm.value;

    this.bookingService.beginTransaction(
      this.offerIds,
      formValue.currency,
      formValue.culture
    ).subscribe({
      next: (response: BookingTransactionResponse) => {
        this.isLoading = false;
        console.log('Réponse reçue:', response);

        if (response && response.beginResponse && response.beginResponse.body) {
          this.beginResponse = response.beginResponse;
          this.transactionId = response.beginResponse.body.transactionId || '';

          // Mettre à jour le formulaire d'informations de réservation avec l'ID de transaction
          this.reservationInfoForm.patchValue({
            transactionId: this.transactionId
          });

          // Ajouter les voyageurs existants s'il y en a dans la réponse
          if (response.beginResponse.body.reservationData &&
              response.beginResponse.body.reservationData.travellers &&
              response.beginResponse.body.reservationData.travellers.length > 0) {

            // Vider le tableau de voyageurs existant
            while (this.travellers.length > 0) {
              this.travellers.removeAt(0);
            }

            // Ajouter les voyageurs de la réponse
            response.beginResponse.body.reservationData.travellers.forEach(traveller => {
              this.addTravellerFromResponse(traveller);
            });
          } else {
            // Créer dynamiquement les voyageurs en fonction des informations de passagers
            this.createTravellersFromPassengerCounts();
          }

          // Passer à l'étape suivante
          this.currentStep = 2;

          this.snackBar.open('Transaction démarrée avec succès!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        } else {
          this.errorMessage = 'Réponse de transaction invalide.';
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Erreur lors du démarrage de la transaction:', error);
        this.errorMessage = error.message || 'Une erreur est survenue lors du démarrage de la transaction.';

        this.snackBar.open(this.errorMessage, 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  // Ajouter un voyageur à partir de la réponse de l'API
  addTravellerFromResponse(traveller: TravellerBeginResponse): void {
    // Utiliser uniquement les données fournies par l'API, sans valeurs par défaut
    const travellerForm = this.fb.group({
      travellerId: [traveller.travellerId || ''],
      type: [traveller.type, Validators.required],
      title: [traveller.title, Validators.required],
      passengerType: [traveller.passengerType, Validators.required],
      name: [traveller.name || '', [Validators.required, Validators.minLength(2)]],
      surname: [traveller.surname || '', [Validators.required, Validators.minLength(2)]],
      isLeader: [traveller.isLeader !== undefined ? traveller.isLeader : this.travellers?.length === 0],
      birthDate: [traveller.birthDate || '', Validators.required],
      nationality: this.fb.group({
        twoLetterCode: [traveller.nationality?.twoLetterCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]
      }),
      identityNumber: [traveller.identityNumber || ''],
      passportInfo: this.fb.group({
        serial: [traveller.passportInfo?.serial || ''],
        number: [traveller.passportInfo?.number || '', [Validators.required, Validators.minLength(5)]],
        expireDate: [traveller.passportInfo?.expireDate || '', Validators.required],
        issueDate: [traveller.passportInfo?.issueDate || '', Validators.required],
        citizenshipCountryCode: [traveller.passportInfo?.citizenshipCountryCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],
        issueCountryCode: [traveller.passportInfo?.issueCountryCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]
      }),
      address: this.fb.group({
        contactPhone: this.fb.group({
          countryCode: [traveller.address?.contactPhone?.countryCode || '', Validators.required],
          areaCode: [''], // areaCode n'existe pas dans le modèle de réponse
          phoneNumber: [traveller.address?.contactPhone?.phoneNumber || '', [Validators.required, Validators.minLength(5)]]
        }),
        email: [traveller.address?.email || '', [Validators.required, Validators.email]],
        address: [traveller.address?.address || '', [Validators.required, Validators.minLength(5)]],
        zipCode: [traveller.address?.zipCode || '', [Validators.required, Validators.minLength(3)]],
        city: this.fb.group({
          id: [traveller.address?.city?.id || '', Validators.required],
          name: [traveller.address?.city?.name || '', [Validators.required, Validators.minLength(2)]]
        }),
        country: this.fb.group({
          id: [traveller.address?.country?.id || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],
          name: [traveller.address?.country?.name || '', [Validators.required, Validators.minLength(2)]]
        })
      }),
      gender: [traveller.gender, Validators.required]
    });

    // Ajouter des validateurs personnalisés
    this.addCustomValidators(travellerForm);

    this.travellers.push(travellerForm);
  }

  // Définir les informations de réservation
  setReservationInfo(): void {
    if (this.reservationInfoForm.invalid) {
      // Vérifier les erreurs spécifiques pour donner des messages plus précis
      let errorMessage = 'Veuillez remplir tous les champs obligatoires.';

      // Vérifier les erreurs de passeport
      const travellers = this.travellers.controls;
      for (let i = 0; i < travellers.length; i++) {
        const traveller = travellers[i] as FormGroup;

        // Vérifier les erreurs de date d'expiration du passeport
        const expireDateControl = traveller.get('passportInfo.expireDate');
        if (expireDateControl?.errors?.['expireDateInvalid']) {
          errorMessage = `Voyageur ${i+1}: La date d'expiration du passeport doit être dans le futur.`;
          break;
        }

        // Vérifier les erreurs de code pays
        const issueCountryCodeControl = traveller.get('passportInfo.issueCountryCode');
        if (issueCountryCodeControl?.invalid && issueCountryCodeControl?.touched) {
          errorMessage = `Voyageur ${i+1}: Le code pays d'émission du passeport est invalide.`;
          break;
        }

        // Vérifier les erreurs de numéro de passeport
        const passportNumberControl = traveller.get('passportInfo.number');
        if (passportNumberControl?.invalid && passportNumberControl?.touched) {
          errorMessage = `Voyageur ${i+1}: Le numéro de passeport est invalide.`;
          break;
        }
      }

      this.snackBar.open(errorMessage, 'Fermer', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    const formValue = this.reservationInfoForm.value;

    // Formater correctement les dates pour chaque voyageur
    const formattedTravellers = formValue.travellers.map((traveller: any) => {
      // Créer un nouvel objet pour éviter de modifier l'original
      const formattedTraveller = { ...traveller };

      // Formater la date de naissance
      if (formattedTraveller.birthDate) {
        // Convertir en Date si c'est un objet Date ou une chaîne
        const birthDate = new Date(formattedTraveller.birthDate);
        if (!isNaN(birthDate.getTime())) {
          formattedTraveller.birthDate = this.formatDateForApi(birthDate);
        }
      }

      // Formater les dates de passeport si présentes
      if (formattedTraveller.passportInfo) {
        const passportInfo = { ...formattedTraveller.passportInfo };

        if (passportInfo.expireDate) {
          const expireDate = new Date(passportInfo.expireDate);
          if (!isNaN(expireDate.getTime())) {
            passportInfo.expireDate = this.formatDateForApi(expireDate);
          }
        }

        if (passportInfo.issueDate) {
          const issueDate = new Date(passportInfo.issueDate);
          if (!isNaN(issueDate.getTime())) {
            passportInfo.issueDate = this.formatDateForApi(issueDate);
          }
        }

        formattedTraveller.passportInfo = passportInfo;
      }

      return formattedTraveller;
    });

    // Vérifier si tous les passagers sont des enfants ou des bébés
    const hasAdult = formattedTravellers.some((traveller: TravellerRequest) =>
      traveller.passengerType === PassengerType.Adult
    );

    // Créer la requête d'informations de réservation
    // Ne pas inclure customerInfo si tous les passagers sont des enfants ou des bébés
    const request: SetReservationInfoRequest = {
      transactionId: formValue.transactionId,
      travellers: formattedTravellers,
      reservationNote: formValue.reservationNote,
      agencyReservationNumber: formValue.agencyReservationNumber
    };

    // Afficher un message de log pour indiquer si customerInfo est inclus ou non
    console.log('Requête setReservationInfo - Présence d\'adultes:', hasAdult);
    console.log('Requête setReservationInfo - customerInfo non inclus car tous les passagers sont des enfants ou des bébés');

    this.bookingService.setReservationInfo(request).subscribe({
      next: (response: BookingTransactionResponse) => {
        this.isLoading = false;
        console.log('Réponse setReservationInfo reçue:', response);

        if (response && response.infoResponse && response.infoResponse.body) {
          this.infoResponse = response.infoResponse;

          // Mettre à jour le formulaire de finalisation de transaction avec l'ID de transaction
          this.commitTransactionForm.patchValue({
            transactionId: this.transactionId
          });

          // Mettre à jour le montant du paiement si disponible
          if (response.infoResponse.body.reservationData &&
              response.infoResponse.body.reservationData.reservationInfo &&
              response.infoResponse.body.reservationData.reservationInfo.priceToPay) {

            const priceToPay = response.infoResponse.body.reservationData.reservationInfo.priceToPay;

            this.commitTransactionForm.get('paymentInformation.paymentPrice')?.patchValue({
              amount: priceToPay.amount,
              currency: priceToPay.currency
            });
          }

          // Passer à l'étape suivante
          this.currentStep = 3;

          this.snackBar.open('Informations de réservation définies avec succès!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        } else {
          this.errorMessage = 'Réponse d\'informations de réservation invalide.';
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Erreur lors de la définition des informations de réservation:', error);

        // Analyser le message d'erreur pour des problèmes spécifiques
        let errorMessage = error.message || 'Une erreur est survenue lors de la définition des informations de réservation.';

        // Vérifier les erreurs spécifiques liées au passeport
        if (errorMessage.includes('passport') || errorMessage.includes('issueCountryCode')) {
          errorMessage = 'Erreur de validation du passeport: Veuillez vérifier que toutes les informations de passeport sont complètes, notamment le code pays d\'émission.';
        }
        // Vérifier les erreurs liées aux dates
        else if (errorMessage.includes('date') || errorMessage.includes('expireDate')) {
          errorMessage = 'Erreur de validation des dates: Veuillez vérifier que toutes les dates sont au format correct (YYYY-MM-DD).';
        }
        // Vérifier les erreurs liées aux informations personnelles
        else if (errorMessage.includes('name') || errorMessage.includes('surname')) {
          errorMessage = 'Erreur de validation des informations personnelles: Veuillez vérifier que tous les noms et prénoms sont correctement renseignés.';
        }

        this.errorMessage = errorMessage;

        this.snackBar.open(this.errorMessage, 'Fermer', {
          duration: 8000, // Durée plus longue pour les messages d'erreur détaillés
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  // Finaliser la transaction de réservation
  commitTransaction(): void {
    if (this.commitTransactionForm.invalid) {
      this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    const formValue = this.commitTransactionForm.value;

    this.bookingService.commitTransaction(formValue).subscribe({
      next: (response: BookingTransactionResponse) => {
        this.isLoading = false;
        console.log('Réponse commitTransaction reçue:', response);

        if (response && response.commitResponse && response.commitResponse.body) {
          this.commitResponse = response.commitResponse;

          this.snackBar.open('Réservation finalisée avec succès!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });

          // Afficher les détails de la réservation finalisée
          // Vous pourriez rediriger vers une page de confirmation ici
        } else {
          this.errorMessage = 'Réponse de finalisation de transaction invalide.';
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Erreur lors de la finalisation de la transaction:', error);
        this.errorMessage = error.message || 'Une erreur est survenue lors de la finalisation de la transaction.';

        this.snackBar.open(this.errorMessage, 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  // Revenir à l'étape précédente
  previousStep(): void {
    // Si la transaction a été démarrée automatiquement, ne pas permettre de revenir à l'étape 1
    if (this.currentStep > 2 || (this.currentStep === 2 && !this.isAutoStarted())) {
      this.currentStep--;
    } else {
      // Si on est à l'étape 2 et que la transaction a été démarrée automatiquement,
      // proposer de retourner à la page de sélection de vol
      this.snackBar.open('La transaction a déjà été démarrée. Voulez-vous annuler et retourner à la sélection de vol?', 'Retour', {
        duration: 5000,
        panelClass: ['warning-snackbar']
      }).onAction().subscribe(() => {
        this.router.navigate(['/get-offer'], {
          queryParams: {
            searchId: this.searchId,
            offerId: this.offerIds[0]
          }
        });
      });
    }
  }

  // Vérifier si la transaction a été démarrée automatiquement
  isAutoStarted(): boolean {
    return this.route.snapshot.queryParams['autoStarted'] === 'true';
  }

  // Formater une date pour l'affichage
  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    return date.toLocaleDateString();
  }

  // Formater une date pour l'API (format ISO 8601: YYYY-MM-DD)
  formatDateForApi(date: Date): string {
    if (!date) return '';

    // S'assurer que c'est une instance de Date valide
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      console.error('Date invalide:', date);
      return '';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  // Formater un prix pour l'affichage
  formatPrice(amount: number, currency: string): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency
    }).format(amount);
  }

  // Gérer la sélection d'un pays
  onCountrySelected(country: Country, travellerIndex: number, field: string): void {
    console.log(`Pays sélectionné pour le voyageur ${travellerIndex}, champ ${field}:`, country);

    const traveller = this.travellers.at(travellerIndex) as FormGroup;

    // Mettre à jour le champ correspondant
    switch (field) {
      case 'nationality':
        traveller.get('nationality.twoLetterCode')?.setValue(country.code);
        break;
      case 'citizenshipCountryCode':
        traveller.get('passportInfo.citizenshipCountryCode')?.setValue(country.code);
        break;
      case 'issueCountryCode':
        traveller.get('passportInfo.issueCountryCode')?.setValue(country.code);
        break;
      case 'country':
        traveller.get('address.country.id')?.setValue(country.code);
        traveller.get('address.country.name')?.setValue(country.name);
        break;
    }
  }

  // Formater un ID d'offre pour l'affichage
  formatOfferId(offerId: string): string {
    if (!offerId) return 'N/A';

    // Extraire les informations pertinentes de l'ID d'offre
    // Format typique: 13$3$1~^006^~AAABloLbX6oAAAAClhW0YYMc26FHjKULIrlKaQAAAZaC2zyuAAAAALH6UPiglvPwEjLokBT6TDI=~^006^~1~^006^~154.66~^006^~~^006^~154.66~^006^~ODBiZTZiMGQtYWYxYy00MzYzLThmNjctODcyNTA0NjVjZjgz

    // Extraire le début de l'ID (avant le premier ~)
    const parts = offerId.split('~');
    const firstPart = parts[0] || '';

    // Extraire les informations de base (type de vol, classe, etc.)
    const basicInfo = firstPart.split('$');

    // Créer un identifiant court
    const shortId = offerId.substring(0, 8) + '...' + offerId.substring(offerId.length - 8);

    return `Vol #${basicInfo[0] || 'N/A'} - Référence: ${shortId}`;
  }
}
