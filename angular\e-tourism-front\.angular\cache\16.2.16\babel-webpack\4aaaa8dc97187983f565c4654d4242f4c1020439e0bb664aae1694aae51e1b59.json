{"ast": null, "code": "import { trigger, transition, style, animate, query, stagger } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nexport class AccueilComponent {\n  constructor(authService, router, snackBar) {\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.userName = '';\n    // Destinations populaires\n    this.destinations = [{\n      name: 'Istanbul',\n      country: 'Turquie',\n      code: 'IST',\n      image: 'istanbul.jpg'\n    }, {\n      name: 'Tunis',\n      country: 'Tunisie',\n      code: 'TUN',\n      image: 'tunis.jpg'\n    }, {\n      name: 'Paris',\n      country: 'France',\n      code: 'CDG',\n      image: 'paris.jpg'\n    }, {\n      name: 'Dubai',\n      country: 'Émirats Arabes Unis',\n      code: 'DXB',\n      image: 'dubai.jpg'\n    }];\n    // Témoignages\n    this.testimonials = [{\n      name: '<PERSON>',\n      location: 'Paris, France',\n      rating: 5,\n      text: 'Service exceptionnel! J\\'ai trouvé un vol à un prix imbattable et la réservation a été simple et rapide.'\n    }, {\n      name: 'Thomas Dubois',\n      location: 'Lyon, France',\n      rating: 4.5,\n      text: 'Interface intuitive et options de vol nombreuses. J\\'ai pu comparer facilement et choisir la meilleure option pour mon budget.'\n    }, {\n      name: 'Marie Leroy',\n      location: 'Marseille, France',\n      rating: 5,\n      text: 'Le support client est remarquable. J\\'ai eu besoin d\\'aide pour modifier ma réservation et tout a été résolu rapidement.'\n    }];\n  }\n  ngOnInit() {\n    // Animation d'entrée pour la page\n    document.body.classList.add('accueil-page-active');\n    // Récupérer les informations de l'utilisateur\n    const userInfo = this.authService.getUserInfo();\n    if (userInfo) {\n      this.userName = userInfo.name || userInfo.username || 'Utilisateur';\n    }\n  }\n  ngOnDestroy() {\n    // Nettoyage de la classe lors de la destruction du composant\n    document.body.classList.remove('accueil-page-active');\n  }\n  navigateToSearchPrice() {\n    this.router.navigate(['/search-price']);\n  }\n  showMoreInfo() {\n    this.snackBar.open('Plus d\\'informations seront disponibles prochainement!', 'Fermer', {\n      duration: 3000,\n      panelClass: 'info-snackbar',\n      verticalPosition: 'bottom'\n    });\n  }\n  exchangeLocations() {\n    // Cette fonction sera implémentée pour échanger les emplacements de départ et d'arrivée\n    this.snackBar.open('Échange des emplacements de départ et d\\'arrivée', 'OK', {\n      duration: 2000\n    });\n  }\n  showAdvancedOptions() {\n    // Cette fonction affichera les options avancées\n    this.snackBar.open('Options avancées seront disponibles prochainement', 'OK', {\n      duration: 2000\n    });\n  }\n  // Méthode pour générer un tableau d'étoiles pour les avis\n  getStars(rating) {\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n    const stars = Array(fullStars).fill(1);\n    if (hasHalfStar) {\n      stars.push(0.5);\n    }\n    return stars;\n  }\n  static {\n    this.ɵfac = function AccueilComponent_Factory(t) {\n      return new (t || AccueilComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccueilComponent,\n      selectors: [[\"app-accueil\"]],\n      decls: 255,\n      vars: 0,\n      consts: [[1, \"accueil-container\"], [1, \"travelease-header\"], [1, \"travelease-logo\"], [1, \"fas\", \"fa-plane\"], [1, \"search-flights-section\"], [1, \"search-flights-container\"], [1, \"search-title\"], [1, \"search-subtitle\"], [1, \"search-form\"], [1, \"search-row\"], [1, \"search-group\"], [1, \"search-input-group\"], [1, \"search-type\"], [1, \"type-selector\"], [1, \"fas\", \"fa-map-marker-alt\"], [\"value\", \"city\"], [\"value\", \"airport\"], [1, \"location-input\"], [1, \"fas\", \"fa-plane-departure\"], [\"type\", \"text\", \"placeholder\", \"City...\"], [1, \"exchange-button\"], [3, \"click\"], [1, \"fas\", \"fa-exchange-alt\"], [1, \"fas\", \"fa-plane-arrival\"], [\"type\", \"text\", \"placeholder\", \"Airport...\"], [1, \"search-group\", \"date-group\"], [1, \"date-input\"], [1, \"far\", \"fa-calendar-alt\"], [\"type\", \"date\", \"placeholder\", \"01/05/2023\"], [1, \"search-group\", \"passengers-group\"], [1, \"passengers-input\"], [1, \"fas\", \"fa-users\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [\"value\", \"5\"], [1, \"search-group\", \"class-group\"], [1, \"class-input\"], [1, \"fas\", \"fa-chair\"], [\"value\", \"economy\"], [\"value\", \"business\"], [\"value\", \"first\"], [1, \"search-row\", \"search-actions\"], [1, \"nonstop-check\"], [1, \"checkbox-container\"], [\"type\", \"checkbox\"], [1, \"checkmark\"], [1, \"search-button\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"advanced-options\"], [1, \"advanced-button\"], [1, \"fas\", \"fa-cog\"], [1, \"features-section\"], [1, \"features-background\"], [\"src\", \"assets/images/features-background.svg\", \"alt\", \"Background pattern\"], [1, \"section-title\"], [1, \"section-subtitle\"], [1, \"features-grid\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-100\"], [1, \"feature-card-inner\"], [1, \"feature-icon\", \"blue-gradient\"], [1, \"feature-title\"], [1, \"feature-description\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-200\"], [1, \"feature-icon\", \"orange-gradient\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-300\"], [1, \"feature-icon\", \"teal-gradient\"], [1, \"fas\", \"fa-credit-card\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-400\"], [1, \"feature-icon\", \"purple-gradient\"], [1, \"fas\", \"fa-headset\"], [1, \"destinations-section\"], [1, \"destinations-grid\"], [1, \"destination-card\", \"istanbul\", \"animate-fade-in\"], [1, \"destination-image\"], [1, \"destination-overlay\"], [1, \"destination-content\"], [1, \"destination-code\"], [1, \"destination-card\", \"tunis\", \"animate-fade-in\", \"animate-delay-100\"], [1, \"destination-card\", \"paris\", \"animate-fade-in\", \"animate-delay-200\"], [1, \"destination-card\", \"dubai\", \"animate-fade-in\", \"animate-delay-300\"], [1, \"cta-section\"], [1, \"cta-background\"], [\"src\", \"assets/images/cta-background.svg\", \"alt\", \"CTA Background\"], [1, \"cta-content\"], [1, \"cta-title\"], [1, \"cta-description\"], [\"mat-flat-button\", \"\", 1, \"cta-button\", 3, \"click\"], [1, \"testimonials-section\"], [1, \"testimonials-container\"], [1, \"testimonial-card\", \"animate-fade-in\"], [1, \"testimonial-content\"], [1, \"testimonial-rating\"], [1, \"fas\", \"fa-star\"], [1, \"testimonial-text\"], [1, \"testimonial-author\"], [1, \"testimonial-avatar\"], [1, \"fas\", \"fa-user\"], [1, \"testimonial-info\"], [1, \"testimonial-card\", \"animate-fade-in\", \"animate-delay-100\"], [1, \"fas\", \"fa-star-half-alt\"], [1, \"testimonial-card\", \"animate-fade-in\", \"animate-delay-200\"]],\n      template: function AccueilComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵtext(4, \" TravelEase \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"h2\", 6);\n          i0.ɵɵtext(8, \"Search Flights\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 7);\n          i0.ɵɵtext(10, \"Enter your travel details below\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10)(14, \"label\");\n          i0.ɵɵtext(15, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 11)(17, \"div\", 12)(18, \"span\");\n          i0.ɵɵtext(19, \"From Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 13);\n          i0.ɵɵelement(21, \"i\", 14);\n          i0.ɵɵelementStart(22, \"select\")(23, \"option\", 15);\n          i0.ɵɵtext(24, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"option\", 16);\n          i0.ɵɵtext(26, \"Airport\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 17);\n          i0.ɵɵelement(28, \"i\", 18)(29, \"input\", 19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 20)(31, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function AccueilComponent_Template_button_click_31_listener() {\n            return ctx.exchangeLocations();\n          });\n          i0.ɵɵelement(32, \"i\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 10)(34, \"label\");\n          i0.ɵɵtext(35, \"To\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 11)(37, \"div\", 12)(38, \"span\");\n          i0.ɵɵtext(39, \"To Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 13);\n          i0.ɵɵelement(41, \"i\", 3);\n          i0.ɵɵelementStart(42, \"select\")(43, \"option\", 15);\n          i0.ɵɵtext(44, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"option\", 16);\n          i0.ɵɵtext(46, \"Airport\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"div\", 17);\n          i0.ɵɵelement(48, \"i\", 23)(49, \"input\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(50, \"div\", 9)(51, \"div\", 25)(52, \"label\");\n          i0.ɵɵtext(53, \"Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 26);\n          i0.ɵɵelement(55, \"i\", 27)(56, \"input\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 29)(58, \"label\");\n          i0.ɵɵtext(59, \"Passengers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 30);\n          i0.ɵɵelement(61, \"i\", 31);\n          i0.ɵɵelementStart(62, \"select\")(63, \"option\", 32);\n          i0.ɵɵtext(64, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"option\", 33);\n          i0.ɵɵtext(66, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"option\", 34);\n          i0.ɵɵtext(68, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"option\", 35);\n          i0.ɵɵtext(70, \"4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"option\", 36);\n          i0.ɵɵtext(72, \"5\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(73, \"div\", 37)(74, \"label\");\n          i0.ɵɵtext(75, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 38);\n          i0.ɵɵelement(77, \"i\", 39);\n          i0.ɵɵelementStart(78, \"select\")(79, \"option\", 40);\n          i0.ɵɵtext(80, \"Economy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"option\", 41);\n          i0.ɵɵtext(82, \"Business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"option\", 42);\n          i0.ɵɵtext(84, \"First Class\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(85, \"div\", 43)(86, \"div\", 44)(87, \"label\", 45);\n          i0.ɵɵelement(88, \"input\", 46)(89, \"span\", 47);\n          i0.ɵɵtext(90, \" Non-stop flights only \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"button\", 48);\n          i0.ɵɵlistener(\"click\", function AccueilComponent_Template_button_click_91_listener() {\n            return ctx.navigateToSearchPrice();\n          });\n          i0.ɵɵelement(92, \"i\", 49);\n          i0.ɵɵtext(93, \" Search \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"div\", 50)(95, \"button\", 51);\n          i0.ɵɵelement(96, \"i\", 52);\n          i0.ɵɵtext(97, \" Advanced Options \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(98, \"div\", 53)(99, \"div\", 54);\n          i0.ɵɵelement(100, \"img\", 55);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"h2\", 56);\n          i0.ɵɵtext(102, \"Pourquoi nous choisir\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"p\", 57);\n          i0.ɵɵtext(104, \"Nous offrons une exp\\u00E9rience de r\\u00E9servation de vol simple et efficace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"div\", 58)(106, \"div\", 59)(107, \"div\", 60)(108, \"div\", 61);\n          i0.ɵɵelement(109, \"i\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"h3\", 62);\n          i0.ɵɵtext(111, \"Recherche facile\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"p\", 63);\n          i0.ɵɵtext(113, \"Trouvez rapidement des vols avec notre moteur de recherche puissant\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(114, \"div\", 64)(115, \"div\", 60)(116, \"div\", 65);\n          i0.ɵɵelement(117, \"i\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"h3\", 62);\n          i0.ɵɵtext(119, \"Comparez les options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"p\", 63);\n          i0.ɵɵtext(121, \"Comparez diff\\u00E9rentes options de vol pour trouver la meilleure offre\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(122, \"div\", 66)(123, \"div\", 60)(124, \"div\", 67);\n          i0.ɵɵelement(125, \"i\", 68);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(126, \"h3\", 62);\n          i0.ɵɵtext(127, \"R\\u00E9servation s\\u00E9curis\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"p\", 63);\n          i0.ɵɵtext(129, \"R\\u00E9servez vos vols en toute s\\u00E9curit\\u00E9 avec notre plateforme de confiance\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(130, \"div\", 69)(131, \"div\", 60)(132, \"div\", 70);\n          i0.ɵɵelement(133, \"i\", 71);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"h3\", 62);\n          i0.ɵɵtext(135, \"Support 24/7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(136, \"p\", 63);\n          i0.ɵɵtext(137, \"Obtenez de l'aide \\u00E0 tout moment avec notre \\u00E9quipe de support d\\u00E9di\\u00E9e\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(138, \"div\", 72)(139, \"h2\", 56);\n          i0.ɵɵtext(140, \"Destinations populaires\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(141, \"p\", 57);\n          i0.ɵɵtext(142, \"Explorez nos destinations les plus recherch\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(143, \"div\", 73)(144, \"div\", 74)(145, \"div\", 75);\n          i0.ɵɵelement(146, \"div\", 76);\n          i0.ɵɵelementStart(147, \"div\", 77)(148, \"h3\");\n          i0.ɵɵtext(149, \"Istanbul\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(150, \"p\");\n          i0.ɵɵtext(151, \"Turquie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"span\", 78);\n          i0.ɵɵtext(153, \"IST\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(154, \"div\", 79)(155, \"div\", 75);\n          i0.ɵɵelement(156, \"div\", 76);\n          i0.ɵɵelementStart(157, \"div\", 77)(158, \"h3\");\n          i0.ɵɵtext(159, \"Tunis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(160, \"p\");\n          i0.ɵɵtext(161, \"Tunisie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(162, \"span\", 78);\n          i0.ɵɵtext(163, \"TUN\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(164, \"div\", 80)(165, \"div\", 75);\n          i0.ɵɵelement(166, \"div\", 76);\n          i0.ɵɵelementStart(167, \"div\", 77)(168, \"h3\");\n          i0.ɵɵtext(169, \"Paris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(170, \"p\");\n          i0.ɵɵtext(171, \"France\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(172, \"span\", 78);\n          i0.ɵɵtext(173, \"CDG\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(174, \"div\", 81)(175, \"div\", 75);\n          i0.ɵɵelement(176, \"div\", 76);\n          i0.ɵɵelementStart(177, \"div\", 77)(178, \"h3\");\n          i0.ɵɵtext(179, \"Dubai\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(180, \"p\");\n          i0.ɵɵtext(181, \"\\u00C9mirats Arabes Unis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(182, \"span\", 78);\n          i0.ɵɵtext(183, \"DXB\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(184, \"div\", 82)(185, \"div\", 83);\n          i0.ɵɵelement(186, \"img\", 84);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(187, \"div\", 85)(188, \"h2\", 86);\n          i0.ɵɵtext(189, \"Pr\\u00EAt \\u00E0 commencer votre voyage?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(190, \"p\", 87);\n          i0.ɵɵtext(191, \"R\\u00E9servez votre vol maintenant et embarquez pour votre prochaine aventure\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(192, \"button\", 88);\n          i0.ɵɵlistener(\"click\", function AccueilComponent_Template_button_click_192_listener() {\n            return ctx.navigateToSearchPrice();\n          });\n          i0.ɵɵelement(193, \"i\", 49);\n          i0.ɵɵtext(194, \" Rechercher des vols \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(195, \"div\", 89)(196, \"h2\", 56);\n          i0.ɵɵtext(197, \"Ce que disent nos clients\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(198, \"p\", 57);\n          i0.ɵɵtext(199, \"D\\u00E9couvrez les exp\\u00E9riences de nos voyageurs satisfaits\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(200, \"div\", 90)(201, \"div\", 91)(202, \"div\", 92)(203, \"div\", 93);\n          i0.ɵɵelement(204, \"i\", 94)(205, \"i\", 94)(206, \"i\", 94)(207, \"i\", 94)(208, \"i\", 94);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(209, \"p\", 95);\n          i0.ɵɵtext(210, \"\\\"Service exceptionnel! J'ai trouv\\u00E9 un vol \\u00E0 un prix imbattable et la r\\u00E9servation a \\u00E9t\\u00E9 simple et rapide.\\\"\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(211, \"div\", 96)(212, \"div\", 97);\n          i0.ɵɵelement(213, \"i\", 98);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(214, \"div\", 99)(215, \"h4\");\n          i0.ɵɵtext(216, \"Sophie Martin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(217, \"p\");\n          i0.ɵɵtext(218, \"Paris, France\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(219, \"div\", 100)(220, \"div\", 92)(221, \"div\", 93);\n          i0.ɵɵelement(222, \"i\", 94)(223, \"i\", 94)(224, \"i\", 94)(225, \"i\", 94)(226, \"i\", 101);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(227, \"p\", 95);\n          i0.ɵɵtext(228, \"\\\"Interface intuitive et options de vol nombreuses. J'ai pu comparer facilement et choisir la meilleure option pour mon budget.\\\"\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(229, \"div\", 96)(230, \"div\", 97);\n          i0.ɵɵelement(231, \"i\", 98);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(232, \"div\", 99)(233, \"h4\");\n          i0.ɵɵtext(234, \"Thomas Dubois\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(235, \"p\");\n          i0.ɵɵtext(236, \"Lyon, France\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(237, \"div\", 102)(238, \"div\", 92)(239, \"div\", 93);\n          i0.ɵɵelement(240, \"i\", 94)(241, \"i\", 94)(242, \"i\", 94)(243, \"i\", 94)(244, \"i\", 94);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(245, \"p\", 95);\n          i0.ɵɵtext(246, \"\\\"Le support client est remarquable. J'ai eu besoin d'aide pour modifier ma r\\u00E9servation et tout a \\u00E9t\\u00E9 r\\u00E9solu rapidement.\\\"\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(247, \"div\", 96)(248, \"div\", 97);\n          i0.ɵɵelement(249, \"i\", 98);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(250, \"div\", 99)(251, \"h4\");\n          i0.ɵɵtext(252, \"Marie Leroy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(253, \"p\");\n          i0.ɵɵtext(254, \"Marseille, France\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n      },\n      dependencies: [i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i5.MatButton],\n      styles: [\".accueil-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow-x: hidden;\\n  background-color: #f5f7fa;\\n}\\n\\n\\n\\n.travelease-header[_ngcontent-%COMP%] {\\n  background-color: #0066cc;\\n  padding: 15px 20px;\\n  color: white;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.travelease-logo[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.travelease-logo[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n}\\n\\n\\n\\n.search-flights-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 30px 0;\\n  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);\\n}\\n\\n.search-flights-container[_ngcontent-%COMP%] {\\n  max-width: 1000px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\n.search-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 5px;\\n}\\n\\n.search-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin-bottom: 25px;\\n}\\n\\n.search-form[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  padding: 25px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\\n}\\n\\n.search-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n  align-items: flex-end;\\n}\\n\\n.search-group[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.search-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #555;\\n  margin-bottom: 8px;\\n}\\n\\n.search-input-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.search-type[_ngcontent-%COMP%] {\\n  flex: 0 0 120px;\\n}\\n\\n.search-type[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #777;\\n  display: block;\\n  margin-bottom: 5px;\\n}\\n\\n.type-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background-color: #f5f7fa;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  padding: 8px 12px;\\n  height: 40px;\\n}\\n\\n.type-selector[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #0066cc;\\n}\\n\\n.type-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  border: none;\\n  background: transparent;\\n  width: 100%;\\n  font-size: 14px;\\n  color: #333;\\n  outline: none;\\n}\\n\\n.location-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  background-color: #f5f7fa;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  padding: 8px 12px;\\n  height: 40px;\\n}\\n\\n.location-input[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #0066cc;\\n}\\n\\n.location-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  border: none;\\n  background: transparent;\\n  width: 100%;\\n  font-size: 14px;\\n  color: #333;\\n  outline: none;\\n}\\n\\n.exchange-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-top: 25px;\\n}\\n\\n.exchange-button[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background-color: #0066cc;\\n  color: white;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 5px rgba(0, 102, 204, 0.3);\\n}\\n\\n.exchange-button[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background-color: #0055aa;\\n  transform: rotate(180deg);\\n}\\n\\n.date-input[_ngcontent-%COMP%], .passengers-input[_ngcontent-%COMP%], .class-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  background-color: #f5f7fa;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  padding: 8px 12px;\\n  height: 40px;\\n}\\n\\n.date-input[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .passengers-input[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .class-input[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #0066cc;\\n}\\n\\n.date-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .passengers-input[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], .class-input[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  border: none;\\n  background: transparent;\\n  width: 100%;\\n  font-size: 14px;\\n  color: #333;\\n  outline: none;\\n}\\n\\n.search-actions[_ngcontent-%COMP%] {\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.nonstop-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #555;\\n  position: relative;\\n  padding-left: 30px;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  position: absolute;\\n  opacity: 0;\\n  cursor: pointer;\\n  height: 0;\\n  width: 0;\\n}\\n\\n.checkmark[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  height: 20px;\\n  width: 20px;\\n  background-color: #f5f7fa;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%]    ~ .checkmark[_ngcontent-%COMP%] {\\n  background-color: #eee;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%] {\\n  background-color: #0066cc;\\n  border-color: #0066cc;\\n}\\n\\n.checkmark[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  display: none;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    ~ .checkmark[_ngcontent-%COMP%]:after {\\n  display: block;\\n}\\n\\n.checkbox-container[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after {\\n  left: 7px;\\n  top: 3px;\\n  width: 5px;\\n  height: 10px;\\n  border: solid white;\\n  border-width: 0 2px 2px 0;\\n  transform: rotate(45deg);\\n}\\n\\n.search-button[_ngcontent-%COMP%] {\\n  background-color: #0066cc;\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 10px 25px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 5px rgba(0, 102, 204, 0.3);\\n}\\n\\n.search-button[_ngcontent-%COMP%]:hover {\\n  background-color: #0055aa;\\n}\\n\\n.advanced-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 10px;\\n}\\n\\n.advanced-button[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #0066cc;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.advanced-button[_ngcontent-%COMP%]:hover {\\n  color: #0055aa;\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.features-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: var(--spacing-xxl) 0;\\n  margin-bottom: var(--spacing-xxl);\\n  overflow: hidden;\\n}\\n\\n.features-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: -1;\\n}\\n\\n.features-background[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: var(--spacing-lg);\\n  padding: 0 var(--spacing-md);\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  border-radius: var(--border-radius-large);\\n  overflow: hidden;\\n  height: 100%;\\n  perspective: 1000px;\\n}\\n\\n.feature-card-inner[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: var(--border-radius-large);\\n  box-shadow: var(--elevation-2);\\n  padding: var(--spacing-xl);\\n  height: 100%;\\n  transition: transform var(--transition-medium), box-shadow var(--transition-medium);\\n  transform-style: preserve-3d;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]:hover   .feature-card-inner[_ngcontent-%COMP%] {\\n  transform: translateY(-10px) rotateX(5deg);\\n  box-shadow: var(--elevation-4);\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 20px;\\n  margin-bottom: var(--spacing-lg);\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n.feature-icon[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);\\n  animation: shimmer 2s infinite;\\n}\\n\\n.feature-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: white;\\n  z-index: 1;\\n}\\n\\n.feature-title[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.feature-description[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 1rem;\\n  line-height: 1.6;\\n}\\n\\n\\n\\n.destinations-section[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xxl) 0;\\n  margin-bottom: var(--spacing-xxl);\\n}\\n\\n.destinations-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: var(--spacing-lg);\\n  padding: 0 var(--spacing-md);\\n}\\n\\n.destination-card[_ngcontent-%COMP%] {\\n  border-radius: var(--border-radius-large);\\n  overflow: hidden;\\n  height: 250px;\\n  position: relative;\\n  box-shadow: var(--elevation-2);\\n  transition: transform var(--transition-medium), box-shadow var(--transition-medium);\\n}\\n\\n.destination-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: var(--elevation-4);\\n}\\n\\n.destination-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.destination-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.2) 50%, rgba(0, 0, 0, 0) 100%);\\n  z-index: 1;\\n}\\n\\n.destination-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  padding: var(--spacing-lg);\\n  color: white;\\n  z-index: 2;\\n}\\n\\n.destination-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.destination-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  opacity: 0.9;\\n  margin: 0;\\n}\\n\\n.destination-code[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: var(--spacing-md);\\n  right: var(--spacing-md);\\n  background-color: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  padding: 4px 8px;\\n  border-radius: var(--border-radius-small);\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  letter-spacing: 1px;\\n}\\n\\n\\n\\n.istanbul[_ngcontent-%COMP%]   .destination-image[_ngcontent-%COMP%] {\\n  background: url('/assets/images/destinations/istanbul.jpg') center/cover no-repeat;\\n}\\n\\n.tunis[_ngcontent-%COMP%]   .destination-image[_ngcontent-%COMP%] {\\n  background: url('/assets/images/destinations/tunis.jpg') center/cover no-repeat;\\n}\\n\\n.paris[_ngcontent-%COMP%]   .destination-image[_ngcontent-%COMP%] {\\n  background: url('/assets/images/destinations/paris.jpg') center/cover no-repeat;\\n}\\n\\n.dubai[_ngcontent-%COMP%]   .destination-image[_ngcontent-%COMP%] {\\n  background: url('/assets/images/destinations/dubai.jpg') center/cover no-repeat;\\n}\\n\\n\\n\\n.cta-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: var(--border-radius-large);\\n  padding: var(--spacing-xxl) var(--spacing-xl);\\n  margin: 0 var(--spacing-md) var(--spacing-xxl);\\n  color: white;\\n  text-align: center;\\n  overflow: hidden;\\n}\\n\\n.cta-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: -1;\\n}\\n\\n.cta-background[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.cta-content[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.cta-title[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  font-weight: 700;\\n  margin-bottom: var(--spacing-md);\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n\\n.cta-description[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: var(--spacing-lg);\\n  opacity: 0.9;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n\\n.cta-button[_ngcontent-%COMP%] {\\n  padding: var(--spacing-md) var(--spacing-xl);\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  background-color: white;\\n  color: var(--primary-color);\\n  border-radius: var(--border-radius-medium);\\n  box-shadow: var(--elevation-3);\\n  transition: all var(--transition-fast);\\n  display: inline-flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n}\\n\\n.cta-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: var(--elevation-4);\\n}\\n\\n.cta-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(-1px);\\n  box-shadow: var(--elevation-2);\\n}\\n\\n.cta-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n\\n\\n\\n.testimonials-section[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xxl) 0;\\n  margin-bottom: var(--spacing-xxl);\\n}\\n\\n.testimonials-container[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: var(--spacing-lg);\\n  padding: 0 var(--spacing-md);\\n}\\n\\n.testimonial-card[_ngcontent-%COMP%] {\\n  border-radius: var(--border-radius-large);\\n  overflow: hidden;\\n  height: 100%;\\n}\\n\\n.testimonial-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: var(--border-radius-large);\\n  box-shadow: var(--elevation-2);\\n  padding: var(--spacing-xl);\\n  height: 100%;\\n  transition: transform var(--transition-medium), box-shadow var(--transition-medium);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.testimonial-card[_ngcontent-%COMP%]:hover   .testimonial-content[_ngcontent-%COMP%] {\\n  transform: translateY(-5px);\\n  box-shadow: var(--elevation-3);\\n}\\n\\n.testimonial-rating[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-md);\\n  color: var(--amber);\\n}\\n\\n.testimonial-rating[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 2px;\\n}\\n\\n.testimonial-text[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: var(--text-secondary);\\n  line-height: 1.6;\\n  margin-bottom: var(--spacing-lg);\\n  flex-grow: 1;\\n  font-style: italic;\\n}\\n\\n.testimonial-author[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n}\\n\\n.testimonial-avatar[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 1.5rem;\\n}\\n\\n.testimonial-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n  margin: 0 0 4px;\\n}\\n\\n.testimonial-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--text-secondary);\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 992px) {\\n  .hero-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: var(--spacing-xl) var(--spacing-md);\\n    min-height: auto;\\n  }\\n\\n  .hero-content[_ngcontent-%COMP%] {\\n    text-align: center;\\n    padding: var(--spacing-lg) var(--spacing-md);\\n    order: 2;\\n    max-width: 100%;\\n  }\\n\\n  .hero-image[_ngcontent-%COMP%] {\\n    order: 1;\\n    margin-bottom: var(--spacing-lg);\\n  }\\n\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n\\n  .hero-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n    margin-left: auto;\\n    margin-right: auto;\\n  }\\n\\n  .hero-cta-container[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    flex-wrap: wrap;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .cta-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .cta-description[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .testimonials-container[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .hero-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .hero-cta-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n\\n  .hero-cta[_ngcontent-%COMP%], .hero-cta-secondary[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n\\n  .features-grid[_ngcontent-%COMP%], .destinations-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .cta-section[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl) var(--spacing-md);\\n  }\\n\\n  .cta-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n\\n  .section-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n\\n  .cta-button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      data: {\n        animation: [trigger('fadeIn', [transition(':enter', [style({\n          opacity: 0\n        }), animate('600ms ease-in', style({\n          opacity: 1\n        }))])]), trigger('slideUp', [transition(':enter', [style({\n          transform: 'translateY(30px)',\n          opacity: 0\n        }), animate('600ms ease-out', style({\n          transform: 'translateY(0)',\n          opacity: 1\n        }))])]), trigger('staggerFadeIn', [transition('* => *', [query(':enter', [style({\n          opacity: 0,\n          transform: 'translateY(30px)'\n        }), stagger('100ms', [animate('600ms ease-out', style({\n          opacity: 1,\n          transform: 'translateY(0)'\n        }))])], {\n          optional: true\n        })])])]\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["trigger", "transition", "style", "animate", "query", "stagger", "AccueilComponent", "constructor", "authService", "router", "snackBar", "userName", "destinations", "name", "country", "code", "image", "testimonials", "location", "rating", "text", "ngOnInit", "document", "body", "classList", "add", "userInfo", "getUserInfo", "username", "ngOnDestroy", "remove", "navigateToSearchPrice", "navigate", "showMoreInfo", "open", "duration", "panelClass", "verticalPosition", "exchangeLocations", "showAdvancedOptions", "getStars", "fullStars", "Math", "floor", "hasHalfStar", "stars", "Array", "fill", "push", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "AccueilComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AccueilComponent_Template_button_click_31_listener", "AccueilComponent_Template_button_click_91_listener", "AccueilComponent_Template_button_click_192_listener", "opacity", "transform", "optional"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\accueil\\accueil.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\accueil\\accueil.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { trigger, transition, style, animate, query, stagger } from '@angular/animations';\nimport { MatSnackBar } from '@angular/material/snack-bar';\n\n@Component({\n  selector: 'app-accueil',\n  templateUrl: './accueil.component.html',\n  styleUrls: ['./accueil.component.css'],\n  animations: [\n    trigger('fadeIn', [\n      transition(':enter', [\n        style({ opacity: 0 }),\n        animate('600ms ease-in', style({ opacity: 1 }))\n      ])\n    ]),\n    trigger('slideUp', [\n      transition(':enter', [\n        style({ transform: 'translateY(30px)', opacity: 0 }),\n        animate('600ms ease-out', style({ transform: 'translateY(0)', opacity: 1 }))\n      ])\n    ]),\n    trigger('staggerFadeIn', [\n      transition('* => *', [\n        query(':enter', [\n          style({ opacity: 0, transform: 'translateY(30px)' }),\n          stagger('100ms', [\n            animate('600ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))\n          ])\n        ], { optional: true })\n      ])\n    ])\n  ]\n})\nexport class AccueilComponent implements OnInit, OnDestroy {\n  userName: string = '';\n\n  // Destinations populaires\n  destinations = [\n    { name: 'Istanbul', country: 'Turquie', code: 'IST', image: 'istanbul.jpg' },\n    { name: 'Tunis', country: 'Tunisie', code: 'TUN', image: 'tunis.jpg' },\n    { name: 'Paris', country: 'France', code: 'CDG', image: 'paris.jpg' },\n    { name: 'Dubai', country: 'Émirats Arabes Unis', code: 'DXB', image: 'dubai.jpg' }\n  ];\n\n  // Témoignages\n  testimonials = [\n    {\n      name: 'Sophie Martin',\n      location: 'Paris, France',\n      rating: 5,\n      text: 'Service exceptionnel! J\\'ai trouvé un vol à un prix imbattable et la réservation a été simple et rapide.'\n    },\n    {\n      name: 'Thomas Dubois',\n      location: 'Lyon, France',\n      rating: 4.5,\n      text: 'Interface intuitive et options de vol nombreuses. J\\'ai pu comparer facilement et choisir la meilleure option pour mon budget.'\n    },\n    {\n      name: 'Marie Leroy',\n      location: 'Marseille, France',\n      rating: 5,\n      text: 'Le support client est remarquable. J\\'ai eu besoin d\\'aide pour modifier ma réservation et tout a été résolu rapidement.'\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) { }\n\n  ngOnInit(): void {\n    // Animation d'entrée pour la page\n    document.body.classList.add('accueil-page-active');\n\n    // Récupérer les informations de l'utilisateur\n    const userInfo = this.authService.getUserInfo();\n    if (userInfo) {\n      this.userName = userInfo.name || userInfo.username || 'Utilisateur';\n    }\n  }\n\n  ngOnDestroy(): void {\n    // Nettoyage de la classe lors de la destruction du composant\n    document.body.classList.remove('accueil-page-active');\n  }\n\n  navigateToSearchPrice(): void {\n    this.router.navigate(['/search-price']);\n  }\n\n  showMoreInfo(): void {\n    this.snackBar.open('Plus d\\'informations seront disponibles prochainement!', 'Fermer', {\n      duration: 3000,\n      panelClass: 'info-snackbar',\n      verticalPosition: 'bottom'\n    });\n  }\n\n  exchangeLocations(): void {\n    // Cette fonction sera implémentée pour échanger les emplacements de départ et d'arrivée\n    this.snackBar.open('Échange des emplacements de départ et d\\'arrivée', 'OK', {\n      duration: 2000\n    });\n  }\n\n  showAdvancedOptions(): void {\n    // Cette fonction affichera les options avancées\n    this.snackBar.open('Options avancées seront disponibles prochainement', 'OK', {\n      duration: 2000\n    });\n  }\n\n  // Méthode pour générer un tableau d'étoiles pour les avis\n  getStars(rating: number): number[] {\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n    const stars = Array(fullStars).fill(1);\n\n    if (hasHalfStar) {\n      stars.push(0.5);\n    }\n\n    return stars;\n  }\n}\n", "<div class=\"accueil-container\">\n  <!-- Header Section with TravelEase Logo -->\n  <div class=\"travelease-header\">\n    <div class=\"travelease-logo\">\n      <i class=\"fas fa-plane\"></i> TravelEase\n    </div>\n  </div>\n\n  <!-- Search Flights Section -->\n  <div class=\"search-flights-section\">\n    <div class=\"search-flights-container\">\n      <h2 class=\"search-title\">Search Flights</h2>\n      <p class=\"search-subtitle\">Enter your travel details below</p>\n\n      <div class=\"search-form\">\n        <!-- From and To Fields Row -->\n        <div class=\"search-row\">\n          <div class=\"search-group\">\n            <label>From</label>\n            <div class=\"search-input-group\">\n              <div class=\"search-type\">\n                <span>From Type</span>\n                <div class=\"type-selector\">\n                  <i class=\"fas fa-map-marker-alt\"></i>\n                  <select>\n                    <option value=\"city\">City</option>\n                    <option value=\"airport\">Airport</option>\n                  </select>\n                </div>\n              </div>\n              <div class=\"location-input\">\n                <i class=\"fas fa-plane-departure\"></i>\n                <input type=\"text\" placeholder=\"City...\" />\n              </div>\n            </div>\n          </div>\n\n          <!-- Exchange Button -->\n          <div class=\"exchange-button\">\n            <button (click)=\"exchangeLocations()\">\n              <i class=\"fas fa-exchange-alt\"></i>\n            </button>\n          </div>\n\n          <div class=\"search-group\">\n            <label>To</label>\n            <div class=\"search-input-group\">\n              <div class=\"search-type\">\n                <span>To Type</span>\n                <div class=\"type-selector\">\n                  <i class=\"fas fa-plane\"></i>\n                  <select>\n                    <option value=\"city\">City</option>\n                    <option value=\"airport\">Airport</option>\n                  </select>\n                </div>\n              </div>\n              <div class=\"location-input\">\n                <i class=\"fas fa-plane-arrival\"></i>\n                <input type=\"text\" placeholder=\"Airport...\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Date, Passengers and Class Row -->\n        <div class=\"search-row\">\n          <div class=\"search-group date-group\">\n            <label>Date</label>\n            <div class=\"date-input\">\n              <i class=\"far fa-calendar-alt\"></i>\n              <input type=\"date\" placeholder=\"01/05/2023\" />\n            </div>\n          </div>\n\n          <div class=\"search-group passengers-group\">\n            <label>Passengers</label>\n            <div class=\"passengers-input\">\n              <i class=\"fas fa-users\"></i>\n              <select>\n                <option value=\"1\">1</option>\n                <option value=\"2\">2</option>\n                <option value=\"3\">3</option>\n                <option value=\"4\">4</option>\n                <option value=\"5\">5</option>\n              </select>\n            </div>\n          </div>\n\n          <div class=\"search-group class-group\">\n            <label>Class</label>\n            <div class=\"class-input\">\n              <i class=\"fas fa-chair\"></i>\n              <select>\n                <option value=\"economy\">Economy</option>\n                <option value=\"business\">Business</option>\n                <option value=\"first\">First Class</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        <!-- Non-stop Flights and Search Button Row -->\n        <div class=\"search-row search-actions\">\n          <div class=\"nonstop-check\">\n            <label class=\"checkbox-container\">\n              <input type=\"checkbox\" />\n              <span class=\"checkmark\"></span>\n              Non-stop flights only\n            </label>\n          </div>\n\n          <button class=\"search-button\" (click)=\"navigateToSearchPrice()\">\n            <i class=\"fas fa-search\"></i> Search\n          </button>\n        </div>\n\n        <!-- Advanced Options -->\n        <div class=\"advanced-options\">\n          <button class=\"advanced-button\">\n            <i class=\"fas fa-cog\"></i> Advanced Options\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Features Section -->\n  <div class=\"features-section\">\n    <div class=\"features-background\">\n      <img src=\"assets/images/features-background.svg\" alt=\"Background pattern\">\n    </div>\n\n    <h2 class=\"section-title\">Pourquoi nous choisir</h2>\n    <p class=\"section-subtitle\">Nous offrons une expérience de réservation de vol simple et efficace</p>\n\n    <div class=\"features-grid\">\n      <div class=\"feature-card animate-fade-in animate-delay-100\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon blue-gradient\">\n            <i class=\"fas fa-search\"></i>\n          </div>\n          <h3 class=\"feature-title\">Recherche facile</h3>\n          <p class=\"feature-description\">Trouvez rapidement des vols avec notre moteur de recherche puissant</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card animate-fade-in animate-delay-200\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon orange-gradient\">\n            <i class=\"fas fa-exchange-alt\"></i>\n          </div>\n          <h3 class=\"feature-title\">Comparez les options</h3>\n          <p class=\"feature-description\">Comparez différentes options de vol pour trouver la meilleure offre</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card animate-fade-in animate-delay-300\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon teal-gradient\">\n            <i class=\"fas fa-credit-card\"></i>\n          </div>\n          <h3 class=\"feature-title\">Réservation sécurisée</h3>\n          <p class=\"feature-description\">Réservez vos vols en toute sécurité avec notre plateforme de confiance</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card animate-fade-in animate-delay-400\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon purple-gradient\">\n            <i class=\"fas fa-headset\"></i>\n          </div>\n          <h3 class=\"feature-title\">Support 24/7</h3>\n          <p class=\"feature-description\">Obtenez de l'aide à tout moment avec notre équipe de support dédiée</p>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Destinations Section -->\n  <div class=\"destinations-section\">\n    <h2 class=\"section-title\">Destinations populaires</h2>\n    <p class=\"section-subtitle\">Explorez nos destinations les plus recherchées</p>\n\n    <div class=\"destinations-grid\">\n      <div class=\"destination-card istanbul animate-fade-in\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Istanbul</h3>\n            <p>Turquie</p>\n            <span class=\"destination-code\">IST</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"destination-card tunis animate-fade-in animate-delay-100\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Tunis</h3>\n            <p>Tunisie</p>\n            <span class=\"destination-code\">TUN</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"destination-card paris animate-fade-in animate-delay-200\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Paris</h3>\n            <p>France</p>\n            <span class=\"destination-code\">CDG</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"destination-card dubai animate-fade-in animate-delay-300\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Dubai</h3>\n            <p>Émirats Arabes Unis</p>\n            <span class=\"destination-code\">DXB</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- CTA Section -->\n  <div class=\"cta-section\">\n    <div class=\"cta-background\">\n      <img src=\"assets/images/cta-background.svg\" alt=\"CTA Background\">\n    </div>\n\n    <div class=\"cta-content\">\n      <h2 class=\"cta-title\">Prêt à commencer votre voyage?</h2>\n      <p class=\"cta-description\">Réservez votre vol maintenant et embarquez pour votre prochaine aventure</p>\n      <button mat-flat-button class=\"cta-button\" (click)=\"navigateToSearchPrice()\">\n        <i class=\"fas fa-search\"></i>\n        Rechercher des vols\n      </button>\n    </div>\n  </div>\n\n  <!-- Testimonials Section -->\n  <div class=\"testimonials-section\">\n    <h2 class=\"section-title\">Ce que disent nos clients</h2>\n    <p class=\"section-subtitle\">Découvrez les expériences de nos voyageurs satisfaits</p>\n\n    <div class=\"testimonials-container\">\n      <div class=\"testimonial-card animate-fade-in\">\n        <div class=\"testimonial-content\">\n          <div class=\"testimonial-rating\">\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n          </div>\n          <p class=\"testimonial-text\">\"Service exceptionnel! J'ai trouvé un vol à un prix imbattable et la réservation a été simple et rapide.\"</p>\n          <div class=\"testimonial-author\">\n            <div class=\"testimonial-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <div class=\"testimonial-info\">\n              <h4>Sophie Martin</h4>\n              <p>Paris, France</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"testimonial-card animate-fade-in animate-delay-100\">\n        <div class=\"testimonial-content\">\n          <div class=\"testimonial-rating\">\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star-half-alt\"></i>\n          </div>\n          <p class=\"testimonial-text\">\"Interface intuitive et options de vol nombreuses. J'ai pu comparer facilement et choisir la meilleure option pour mon budget.\"</p>\n          <div class=\"testimonial-author\">\n            <div class=\"testimonial-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <div class=\"testimonial-info\">\n              <h4>Thomas Dubois</h4>\n              <p>Lyon, France</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"testimonial-card animate-fade-in animate-delay-200\">\n        <div class=\"testimonial-content\">\n          <div class=\"testimonial-rating\">\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n          </div>\n          <p class=\"testimonial-text\">\"Le support client est remarquable. J'ai eu besoin d'aide pour modifier ma réservation et tout a été résolu rapidement.\"</p>\n          <div class=\"testimonial-author\">\n            <div class=\"testimonial-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <div class=\"testimonial-info\">\n              <h4>Marie Leroy</h4>\n              <p>Marseille, France</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAGA,SAASA,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;;;;;;;AAgCzF,OAAM,MAAOC,gBAAgB;EAiC3BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAnClB,KAAAC,QAAQ,GAAW,EAAE;IAErB;IACA,KAAAC,YAAY,GAAG,CACb;MAAEC,IAAI,EAAE,UAAU;MAAEC,OAAO,EAAE,SAAS;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC5E;MAAEH,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,SAAS;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAW,CAAE,EACtE;MAAEH,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,QAAQ;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAW,CAAE,EACrE;MAAEH,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,qBAAqB;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAW,CAAE,CACnF;IAED;IACA,KAAAC,YAAY,GAAG,CACb;MACEJ,IAAI,EAAE,eAAe;MACrBK,QAAQ,EAAE,eAAe;MACzBC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE;KACP,EACD;MACEP,IAAI,EAAE,eAAe;MACrBK,QAAQ,EAAE,cAAc;MACxBC,MAAM,EAAE,GAAG;MACXC,IAAI,EAAE;KACP,EACD;MACEP,IAAI,EAAE,aAAa;MACnBK,QAAQ,EAAE,mBAAmB;MAC7BC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE;KACP,CACF;EAMG;EAEJC,QAAQA,CAAA;IACN;IACAC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAElD;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAClB,WAAW,CAACmB,WAAW,EAAE;IAC/C,IAAID,QAAQ,EAAE;MACZ,IAAI,CAACf,QAAQ,GAAGe,QAAQ,CAACb,IAAI,IAAIa,QAAQ,CAACE,QAAQ,IAAI,aAAa;;EAEvE;EAEAC,WAAWA,CAAA;IACT;IACAP,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACM,MAAM,CAAC,qBAAqB,CAAC;EACvD;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACvB,QAAQ,CAACwB,IAAI,CAAC,wDAAwD,EAAE,QAAQ,EAAE;MACrFC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,eAAe;MAC3BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEAC,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAC5B,QAAQ,CAACwB,IAAI,CAAC,kDAAkD,EAAE,IAAI,EAAE;MAC3EC,QAAQ,EAAE;KACX,CAAC;EACJ;EAEAI,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC7B,QAAQ,CAACwB,IAAI,CAAC,mDAAmD,EAAE,IAAI,EAAE;MAC5EC,QAAQ,EAAE;KACX,CAAC;EACJ;EAEA;EACAK,QAAQA,CAACrB,MAAc;IACrB,MAAMsB,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACxB,MAAM,CAAC;IACpC,MAAMyB,WAAW,GAAGzB,MAAM,GAAG,CAAC,KAAK,CAAC;IACpC,MAAM0B,KAAK,GAAGC,KAAK,CAACL,SAAS,CAAC,CAACM,IAAI,CAAC,CAAC,CAAC;IAEtC,IAAIH,WAAW,EAAE;MACfC,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC;;IAGjB,OAAOH,KAAK;EACd;;;uBA5FWvC,gBAAgB,EAAA2C,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBlD,gBAAgB;MAAAmD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnC7Bd,EAAA,CAAAgB,cAAA,aAA+B;UAIzBhB,EAAA,CAAAiB,SAAA,WAA4B;UAACjB,EAAA,CAAAkB,MAAA,mBAC/B;UAAAlB,EAAA,CAAAmB,YAAA,EAAM;UAIRnB,EAAA,CAAAgB,cAAA,aAAoC;UAEPhB,EAAA,CAAAkB,MAAA,qBAAc;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UAC5CnB,EAAA,CAAAgB,cAAA,WAA2B;UAAAhB,EAAA,CAAAkB,MAAA,uCAA+B;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAE9DnB,EAAA,CAAAgB,cAAA,cAAyB;UAIZhB,EAAA,CAAAkB,MAAA,YAAI;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACnBnB,EAAA,CAAAgB,cAAA,eAAgC;UAEtBhB,EAAA,CAAAkB,MAAA,iBAAS;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UACtBnB,EAAA,CAAAgB,cAAA,eAA2B;UACzBhB,EAAA,CAAAiB,SAAA,aAAqC;UACrCjB,EAAA,CAAAgB,cAAA,cAAQ;UACehB,EAAA,CAAAkB,MAAA,YAAI;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAClCnB,EAAA,CAAAgB,cAAA,kBAAwB;UAAAhB,EAAA,CAAAkB,MAAA,eAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAI9CnB,EAAA,CAAAgB,cAAA,eAA4B;UAC1BhB,EAAA,CAAAiB,SAAA,aAAsC;UAExCjB,EAAA,CAAAmB,YAAA,EAAM;UAKVnB,EAAA,CAAAgB,cAAA,eAA6B;UACnBhB,EAAA,CAAAoB,UAAA,mBAAAC,mDAAA;YAAA,OAASN,GAAA,CAAA1B,iBAAA,EAAmB;UAAA,EAAC;UACnCW,EAAA,CAAAiB,SAAA,aAAmC;UACrCjB,EAAA,CAAAmB,YAAA,EAAS;UAGXnB,EAAA,CAAAgB,cAAA,eAA0B;UACjBhB,EAAA,CAAAkB,MAAA,UAAE;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACjBnB,EAAA,CAAAgB,cAAA,eAAgC;UAEtBhB,EAAA,CAAAkB,MAAA,eAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UACpBnB,EAAA,CAAAgB,cAAA,eAA2B;UACzBhB,EAAA,CAAAiB,SAAA,YAA4B;UAC5BjB,EAAA,CAAAgB,cAAA,cAAQ;UACehB,EAAA,CAAAkB,MAAA,YAAI;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAClCnB,EAAA,CAAAgB,cAAA,kBAAwB;UAAAhB,EAAA,CAAAkB,MAAA,eAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAI9CnB,EAAA,CAAAgB,cAAA,eAA4B;UAC1BhB,EAAA,CAAAiB,SAAA,aAAoC;UAEtCjB,EAAA,CAAAmB,YAAA,EAAM;UAMZnB,EAAA,CAAAgB,cAAA,cAAwB;UAEbhB,EAAA,CAAAkB,MAAA,YAAI;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACnBnB,EAAA,CAAAgB,cAAA,eAAwB;UACtBhB,EAAA,CAAAiB,SAAA,aAAmC;UAErCjB,EAAA,CAAAmB,YAAA,EAAM;UAGRnB,EAAA,CAAAgB,cAAA,eAA2C;UAClChB,EAAA,CAAAkB,MAAA,kBAAU;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACzBnB,EAAA,CAAAgB,cAAA,eAA8B;UAC5BhB,EAAA,CAAAiB,SAAA,aAA4B;UAC5BjB,EAAA,CAAAgB,cAAA,cAAQ;UACYhB,EAAA,CAAAkB,MAAA,SAAC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAC5BnB,EAAA,CAAAgB,cAAA,kBAAkB;UAAAhB,EAAA,CAAAkB,MAAA,SAAC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAC5BnB,EAAA,CAAAgB,cAAA,kBAAkB;UAAAhB,EAAA,CAAAkB,MAAA,SAAC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAC5BnB,EAAA,CAAAgB,cAAA,kBAAkB;UAAAhB,EAAA,CAAAkB,MAAA,SAAC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAC5BnB,EAAA,CAAAgB,cAAA,kBAAkB;UAAAhB,EAAA,CAAAkB,MAAA,SAAC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAKlCnB,EAAA,CAAAgB,cAAA,eAAsC;UAC7BhB,EAAA,CAAAkB,MAAA,aAAK;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACpBnB,EAAA,CAAAgB,cAAA,eAAyB;UACvBhB,EAAA,CAAAiB,SAAA,aAA4B;UAC5BjB,EAAA,CAAAgB,cAAA,cAAQ;UACkBhB,EAAA,CAAAkB,MAAA,eAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UACxCnB,EAAA,CAAAgB,cAAA,kBAAyB;UAAAhB,EAAA,CAAAkB,MAAA,gBAAQ;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAC1CnB,EAAA,CAAAgB,cAAA,kBAAsB;UAAAhB,EAAA,CAAAkB,MAAA,mBAAW;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAOlDnB,EAAA,CAAAgB,cAAA,eAAuC;UAGjChB,EAAA,CAAAiB,SAAA,iBAAyB;UAEzBjB,EAAA,CAAAkB,MAAA,+BACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UAGVnB,EAAA,CAAAgB,cAAA,kBAAgE;UAAlChB,EAAA,CAAAoB,UAAA,mBAAAE,mDAAA;YAAA,OAASP,GAAA,CAAAjC,qBAAA,EAAuB;UAAA,EAAC;UAC7DkB,EAAA,CAAAiB,SAAA,aAA6B;UAACjB,EAAA,CAAAkB,MAAA,gBAChC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAIXnB,EAAA,CAAAgB,cAAA,eAA8B;UAE1BhB,EAAA,CAAAiB,SAAA,aAA0B;UAACjB,EAAA,CAAAkB,MAAA,0BAC7B;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAOjBnB,EAAA,CAAAgB,cAAA,eAA8B;UAE1BhB,EAAA,CAAAiB,SAAA,gBAA0E;UAC5EjB,EAAA,CAAAmB,YAAA,EAAM;UAENnB,EAAA,CAAAgB,cAAA,eAA0B;UAAAhB,EAAA,CAAAkB,MAAA,8BAAqB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACpDnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,uFAAoE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAEpGnB,EAAA,CAAAgB,cAAA,gBAA2B;UAInBhB,EAAA,CAAAiB,SAAA,cAA6B;UAC/BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,eAA0B;UAAAhB,EAAA,CAAAkB,MAAA,yBAAgB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UAC/CnB,EAAA,CAAAgB,cAAA,cAA+B;UAAAhB,EAAA,CAAAkB,MAAA,4EAAmE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAI1GnB,EAAA,CAAAgB,cAAA,gBAA4D;UAGtDhB,EAAA,CAAAiB,SAAA,cAAmC;UACrCjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,eAA0B;UAAAhB,EAAA,CAAAkB,MAAA,6BAAoB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACnDnB,EAAA,CAAAgB,cAAA,cAA+B;UAAAhB,EAAA,CAAAkB,MAAA,iFAAmE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAI1GnB,EAAA,CAAAgB,cAAA,gBAA4D;UAGtDhB,EAAA,CAAAiB,SAAA,cAAkC;UACpCjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,eAA0B;UAAAhB,EAAA,CAAAkB,MAAA,6CAAqB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACpDnB,EAAA,CAAAgB,cAAA,cAA+B;UAAAhB,EAAA,CAAAkB,MAAA,8FAAsE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAI7GnB,EAAA,CAAAgB,cAAA,gBAA4D;UAGtDhB,EAAA,CAAAiB,SAAA,cAA8B;UAChCjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,eAA0B;UAAAhB,EAAA,CAAAkB,MAAA,qBAAY;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UAC3CnB,EAAA,CAAAgB,cAAA,cAA+B;UAAAhB,EAAA,CAAAkB,MAAA,gGAAmE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAO9GnB,EAAA,CAAAgB,cAAA,gBAAkC;UACNhB,EAAA,CAAAkB,MAAA,gCAAuB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACtDnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,4DAA8C;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAE9EnB,EAAA,CAAAgB,cAAA,gBAA+B;UAGzBhB,EAAA,CAAAiB,SAAA,gBAAuC;UACvCjB,EAAA,CAAAgB,cAAA,gBAAiC;UAC3BhB,EAAA,CAAAkB,MAAA,iBAAQ;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACjBnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,gBAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACdnB,EAAA,CAAAgB,cAAA,iBAA+B;UAAAhB,EAAA,CAAAkB,MAAA,YAAG;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAK/CnB,EAAA,CAAAgB,cAAA,gBAAsE;UAElEhB,EAAA,CAAAiB,SAAA,gBAAuC;UACvCjB,EAAA,CAAAgB,cAAA,gBAAiC;UAC3BhB,EAAA,CAAAkB,MAAA,cAAK;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACdnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,gBAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACdnB,EAAA,CAAAgB,cAAA,iBAA+B;UAAAhB,EAAA,CAAAkB,MAAA,YAAG;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAK/CnB,EAAA,CAAAgB,cAAA,gBAAsE;UAElEhB,EAAA,CAAAiB,SAAA,gBAAuC;UACvCjB,EAAA,CAAAgB,cAAA,gBAAiC;UAC3BhB,EAAA,CAAAkB,MAAA,cAAK;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACdnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,eAAM;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACbnB,EAAA,CAAAgB,cAAA,iBAA+B;UAAAhB,EAAA,CAAAkB,MAAA,YAAG;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAK/CnB,EAAA,CAAAgB,cAAA,gBAAsE;UAElEhB,EAAA,CAAAiB,SAAA,gBAAuC;UACvCjB,EAAA,CAAAgB,cAAA,gBAAiC;UAC3BhB,EAAA,CAAAkB,MAAA,cAAK;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACdnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,iCAAmB;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAC1BnB,EAAA,CAAAgB,cAAA,iBAA+B;UAAAhB,EAAA,CAAAkB,MAAA,YAAG;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAQnDnB,EAAA,CAAAgB,cAAA,gBAAyB;UAErBhB,EAAA,CAAAiB,SAAA,gBAAiE;UACnEjB,EAAA,CAAAmB,YAAA,EAAM;UAENnB,EAAA,CAAAgB,cAAA,gBAAyB;UACDhB,EAAA,CAAAkB,MAAA,iDAA8B;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACzDnB,EAAA,CAAAgB,cAAA,cAA2B;UAAAhB,EAAA,CAAAkB,MAAA,sFAAwE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACvGnB,EAAA,CAAAgB,cAAA,mBAA6E;UAAlChB,EAAA,CAAAoB,UAAA,mBAAAG,oDAAA;YAAA,OAASR,GAAA,CAAAjC,qBAAA,EAAuB;UAAA,EAAC;UAC1EkB,EAAA,CAAAiB,SAAA,cAA6B;UAC7BjB,EAAA,CAAAkB,MAAA,8BACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAKbnB,EAAA,CAAAgB,cAAA,gBAAkC;UACNhB,EAAA,CAAAkB,MAAA,kCAAyB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACxDnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,wEAAqD;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAErFnB,EAAA,CAAAgB,cAAA,gBAAoC;UAI5BhB,EAAA,CAAAiB,SAAA,cAA2B;UAK7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,6IAAyG;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACzInB,EAAA,CAAAgB,cAAA,gBAAgC;UAE5BhB,EAAA,CAAAiB,SAAA,cAA2B;UAC7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,gBAA8B;UACxBhB,EAAA,CAAAkB,MAAA,sBAAa;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACtBnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,sBAAa;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAM5BnB,EAAA,CAAAgB,cAAA,iBAAgE;UAG1DhB,EAAA,CAAAiB,SAAA,cAA2B;UAK7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,0IAA+H;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAC/JnB,EAAA,CAAAgB,cAAA,gBAAgC;UAE5BhB,EAAA,CAAAiB,SAAA,cAA2B;UAC7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,gBAA8B;UACxBhB,EAAA,CAAAkB,MAAA,sBAAa;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACtBnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,qBAAY;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAM3BnB,EAAA,CAAAgB,cAAA,iBAAgE;UAG1DhB,EAAA,CAAAiB,SAAA,cAA2B;UAK7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,uJAAwH;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACxJnB,EAAA,CAAAgB,cAAA,gBAAgC;UAE5BhB,EAAA,CAAAiB,SAAA,cAA2B;UAC7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,gBAA8B;UACxBhB,EAAA,CAAAkB,MAAA,oBAAW;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACpBnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,0BAAiB;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;;;;;;mBD/SxB,CACVpE,OAAO,CAAC,QAAQ,EAAE,CAChBC,UAAU,CAAC,QAAQ,EAAE,CACnBC,KAAK,CAAC;UAAEuE,OAAO,EAAE;QAAC,CAAE,CAAC,EACrBtE,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;UAAEuE,OAAO,EAAE;QAAC,CAAE,CAAC,CAAC,CAChD,CAAC,CACH,CAAC,EACFzE,OAAO,CAAC,SAAS,EAAE,CACjBC,UAAU,CAAC,QAAQ,EAAE,CACnBC,KAAK,CAAC;UAAEwE,SAAS,EAAE,kBAAkB;UAAED,OAAO,EAAE;QAAC,CAAE,CAAC,EACpDtE,OAAO,CAAC,gBAAgB,EAAED,KAAK,CAAC;UAAEwE,SAAS,EAAE,eAAe;UAAED,OAAO,EAAE;QAAC,CAAE,CAAC,CAAC,CAC7E,CAAC,CACH,CAAC,EACFzE,OAAO,CAAC,eAAe,EAAE,CACvBC,UAAU,CAAC,QAAQ,EAAE,CACnBG,KAAK,CAAC,QAAQ,EAAE,CACdF,KAAK,CAAC;UAAEuE,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAkB,CAAE,CAAC,EACpDrE,OAAO,CAAC,OAAO,EAAE,CACfF,OAAO,CAAC,gBAAgB,EAAED,KAAK,CAAC;UAAEuE,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAAC,CAC7E,CAAC,CACH,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC,CACvB,CAAC,CACH,CAAC;MACH;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}