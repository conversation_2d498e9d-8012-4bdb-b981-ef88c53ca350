{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class MainLayoutComponent {\n  constructor() {}\n  static {\n    this.ɵfac = function MainLayoutComponent_Factory(t) {\n      return new (t || MainLayoutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MainLayoutComponent,\n      selectors: [[\"app-main-layout\"]],\n      decls: 5,\n      vars: 0,\n      consts: [[1, \"main-layout\"], [1, \"main-content\"], [1, \"content-container\"]],\n      template: function MainLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-navbar\");\n          i0.ɵɵelementStart(2, \"main\", 1)(3, \"div\", 2);\n          i0.ɵɵelement(4, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      styles: [\".main-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 100vh;\\n  width: 100%;\\n  position: relative;\\n  overflow-x: hidden;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-top: 64px; \\n\\n  transition: padding-top var(--transition-medium);\\n}\\n\\n.content-container[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl) var(--spacing-lg);\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  width: 100%;\\n}\\n\\n\\n\\nbody.scrolled[_nghost-%COMP%]   .main-content[_ngcontent-%COMP%], body.scrolled   [_nghost-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n  padding-top: 56px; \\n\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .content-container[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg) var(--spacing-md);\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .content-container[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md) var(--spacing-sm);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbGF5b3V0L21haW4tbGF5b3V0L21haW4tbGF5b3V0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLGlCQUFpQjtFQUNqQixXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLE9BQU87RUFDUCxpQkFBaUIsRUFBRSx5QkFBeUI7RUFDNUMsZ0RBQWdEO0FBQ2xEOztBQUVBO0VBQ0UsNENBQTRDO0VBQzVDLGlCQUFpQjtFQUNqQixjQUFjO0VBQ2QsV0FBVztBQUNiOztBQUVBLDBDQUEwQztBQUMxQztFQUNFLGlCQUFpQixFQUFFLGlDQUFpQztBQUN0RDs7QUFFQSxzQkFBc0I7QUFDdEI7RUFDRTtJQUNFLDRDQUE0QztFQUM5QztBQUNGOztBQUVBO0VBQ0U7SUFDRSw0Q0FBNEM7RUFDOUM7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5tYWluLWxheW91dCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICBvdmVyZmxvdy14OiBoaWRkZW47XHJcbn1cclxuXHJcbi5tYWluLWNvbnRlbnQge1xyXG4gIGZsZXg6IDE7XHJcbiAgcGFkZGluZy10b3A6IDY0cHg7IC8qIEhhdXRldXIgZGUgbGEgbmF2YmFyICovXHJcbiAgdHJhbnNpdGlvbjogcGFkZGluZy10b3AgdmFyKC0tdHJhbnNpdGlvbi1tZWRpdW0pO1xyXG59XHJcblxyXG4uY29udGVudC1jb250YWluZXIge1xyXG4gIHBhZGRpbmc6IHZhcigtLXNwYWNpbmcteGwpIHZhcigtLXNwYWNpbmctbGcpO1xyXG4gIG1heC13aWR0aDogMTIwMHB4O1xyXG4gIG1hcmdpbjogMCBhdXRvO1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcblxyXG4vKiBFZmZldCBkZSBsYSBuYXZiYXIgbG9ycyBkdSBkw4PCqWZpbGVtZW50ICovXHJcbjpob3N0LWNvbnRleHQoYm9keS5zY3JvbGxlZCkgLm1haW4tY29udGVudCB7XHJcbiAgcGFkZGluZy10b3A6IDU2cHg7IC8qIEhhdXRldXIgcsODwqlkdWl0ZSBkZSBsYSBuYXZiYXIgKi9cclxufVxyXG5cclxuLyogUmVzcG9uc2l2ZSBzdHlsZXMgKi9cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLmNvbnRlbnQtY29udGFpbmVyIHtcclxuICAgIHBhZGRpbmc6IHZhcigtLXNwYWNpbmctbGcpIHZhcigtLXNwYWNpbmctbWQpO1xyXG4gIH1cclxufVxyXG5cclxuQG1lZGlhIChtYXgtd2lkdGg6IDU3NnB4KSB7XHJcbiAgLmNvbnRlbnQtY29udGFpbmVyIHtcclxuICAgIHBhZGRpbmc6IHZhcigtLXNwYWNpbmctbWQpIHZhcigtLXNwYWNpbmctc20pO1xyXG4gIH1cclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MainLayoutComponent", "constructor", "selectors", "decls", "vars", "consts", "template", "MainLayoutComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\main-layout\\main-layout.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\main-layout\\main-layout.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-main-layout',\n  templateUrl: './main-layout.component.html',\n  styleUrls: ['./main-layout.component.css']\n})\nexport class MainLayoutComponent {\n  constructor() { }\n}\n", "<div class=\"main-layout\">\n  <!-- Navbar sans événement pour la sidebar -->\n  <app-navbar></app-navbar>\n\n  <!-- Contenu principal -->\n  <main class=\"main-content\">\n    <div class=\"content-container\">\n      <router-outlet></router-outlet>\n    </div>\n  </main>\n</div>\n"], "mappings": ";AAOA,OAAM,MAAOA,mBAAmB;EAC9BC,YAAA,GAAgB;;;uBADLD,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPhCE,EAAA,CAAAC,cAAA,aAAyB;UAEvBD,EAAA,CAAAE,SAAA,iBAAyB;UAGzBF,EAAA,CAAAC,cAAA,cAA2B;UAEvBD,EAAA,CAAAE,SAAA,oBAA+B;UACjCF,EAAA,CAAAG,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}