{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/product.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/autocomplete\";\nimport * as i6 from \"@angular/material/core\";\nfunction SearchPriceComponent_mat_option_45_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r13.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_45_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r13.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_45_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 101);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r13.country);\n  }\n}\nconst _c0 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"fa-flag\": a0,\n    \"fa-city\": a1,\n    \"fa-building\": a2,\n    \"fa-home\": a3,\n    \"fa-plane\": a4\n  };\n};\nfunction SearchPriceComponent_mat_option_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88)(1, \"div\", 90)(2, \"div\", 91);\n    i0.ɵɵelement(3, \"i\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 93)(5, \"div\", 94);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 95);\n    i0.ɵɵtemplate(8, SearchPriceComponent_mat_option_45_span_8_Template, 2, 1, \"span\", 96);\n    i0.ɵɵtemplate(9, SearchPriceComponent_mat_option_45_span_9_Template, 2, 1, \"span\", 97);\n    i0.ɵɵtemplate(10, SearchPriceComponent_mat_option_45_span_10_Template, 2, 1, \"span\", 98);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r13);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r13.type === 1, location_r13.type === 2, location_r13.type === 3, location_r13.type === 4, location_r13.type === 5));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r13.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r13.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r13.city);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r13.country);\n  }\n}\nfunction SearchPriceComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelement(1, \"i\", 103);\n    i0.ɵɵtext(2, \" Please select a departure location \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_mat_option_58_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r20.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_58_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r20.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_58_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 101);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r20.country);\n  }\n}\nfunction SearchPriceComponent_mat_option_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 88)(1, \"div\", 90)(2, \"div\", 91);\n    i0.ɵɵelement(3, \"i\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 93)(5, \"div\", 94);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 95);\n    i0.ɵɵtemplate(8, SearchPriceComponent_mat_option_58_span_8_Template, 2, 1, \"span\", 96);\n    i0.ɵɵtemplate(9, SearchPriceComponent_mat_option_58_span_9_Template, 2, 1, \"span\", 97);\n    i0.ɵɵtemplate(10, SearchPriceComponent_mat_option_58_span_10_Template, 2, 1, \"span\", 98);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r20);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r20.type === 1, location_r20.type === 2, location_r20.type === 3, location_r20.type === 4, location_r20.type === 5));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r20.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r20.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r20.city);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r20.country);\n  }\n}\nfunction SearchPriceComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelement(1, \"i\", 103);\n    i0.ɵɵtext(2, \" Please select an arrival location \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵtext(1, \" Please select a departure date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_67_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵtext(1, \" Please select a return date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"label\", 104);\n    i0.ɵɵtext(2, \"Return Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 26);\n    i0.ɵɵelement(4, \"i\", 41)(5, \"input\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_67_div_6_Template, 2, 0, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"min\", ctx_r7.minReturnDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r7.searchForm.get(\"returnDate\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r7.searchForm.get(\"returnDate\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction SearchPriceComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_97_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const flightClass_r28 = restoredCtx.$implicit;\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.selectFlightClass(flightClass_r28.value));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r28 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵclassProp(\"selected\", ((tmp_0_0 = ctx_r8.searchForm.get(\"flightClass\")) == null ? null : tmp_0_0.value) === flightClass_r28.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flightClass_r28.label, \" \");\n  }\n}\nfunction SearchPriceComponent_i_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 107);\n  }\n}\nfunction SearchPriceComponent_span_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Search Flights\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108);\n    i0.ɵɵelement(1, \"div\", 109);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_156_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114)(1, \"div\", 115)(2, \"div\", 116);\n    i0.ɵɵelement(3, \"i\", 9)(4, \"div\", 117)(5, \"div\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Searching for the best flights...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 118)(9, \"span\");\n    i0.ɵɵtext(10, \"We're comparing prices from hundreds of airlines\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SearchPriceComponent_div_156_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"div\", 120)(2, \"div\", 121);\n    i0.ɵɵelement(3, \"i\", 122);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Oops! Something went wrong\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 102);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 123);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.onSearch());\n    });\n    i0.ɵɵelement(9, \"i\", 124);\n    i0.ɵɵtext(10, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r32.errorMessage);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r36.displayLocation((tmp_0_0 = ctx_r36.searchForm.get(\"departureLocation\")) == null ? null : tmp_0_0.value), \" to \", ctx_r36.displayLocation((tmp_0_0 = ctx_r36.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_0_0.value), \" \");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u00B7 \", ctx_r37.formatDateFull((tmp_0_0 = ctx_r37.searchForm.get(\"departureDate\")) == null ? null : tmp_0_0.value), \" \");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No flights found for your search. Please modify your criteria.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_p_7_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext(4);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u00B7 \", (tmp_0_0 = ctx_r44.searchForm.get(\"passengerCount\")) == null ? null : tmp_0_0.value, \" travelers \");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"span\", 152);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" flights found \");\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_156_div_3_p_7_span_4_Template, 2, 1, \"span\", 61);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r39.searchResults.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r39.searchForm.get(\"passengerCount\")) == null ? null : tmp_1_0.value) > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u00B7 \", ctx_r39.getFlightClassName((tmp_2_0 = ctx_r39.searchForm.get(\"flightClass\")) == null ? null : tmp_2_0.value), \" \");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_8_i_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 142);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_8_i_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 142);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 153)(1, \"div\", 154)(2, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_3_div_8_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r47.toggleFiltersPanel());\n    });\n    i0.ɵɵelement(3, \"i\", 156);\n    i0.ɵɵtext(4, \" Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 157)(6, \"div\", 158);\n    i0.ɵɵelement(7, \"i\", 159);\n    i0.ɵɵtext(8, \" Sort by: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"select\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_156_div_3_div_8_Template_select_change_9_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r49 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r49.sortResults($event));\n    });\n    i0.ɵɵelementStart(10, \"option\", 161);\n    i0.ɵɵtext(11, \"Price (lowest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 162);\n    i0.ɵɵtext(13, \"Duration (shortest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 163);\n    i0.ɵɵtext(15, \"Departure (earliest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 164);\n    i0.ɵɵtext(17, \"Arrival (earliest first)\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 165)(19, \"div\", 166);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_3_div_8_Template_div_click_19_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r50 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r50.toggleDirectFlightsOnly());\n    });\n    i0.ɵɵelementStart(20, \"div\", 141);\n    i0.ɵɵtemplate(21, SearchPriceComponent_div_156_div_3_div_8_i_21_Template, 1, 0, \"i\", 167);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Direct flights only\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 166);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_3_div_8_Template_div_click_24_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r51 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r51.toggleBaggageIncluded());\n    });\n    i0.ɵɵelementStart(25, \"div\", 141);\n    i0.ɵɵtemplate(26, SearchPriceComponent_div_156_div_3_div_8_i_26_Template, 1, 0, \"i\", 167);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Baggage included\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(20);\n    i0.ɵɵclassProp(\"checked\", ctx_r40.directFlightsOnly);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r40.directFlightsOnly);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"checked\", ctx_r40.baggageIncluded);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r40.baggageIncluded);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 168)(1, \"div\", 141);\n    i0.ɵɵelement(2, \"i\", 142);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const airline_r52 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(airline_r52);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 213);\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", flight_r53.items[0].airline.thumbnailFull, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 9);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 214);\n    i0.ɵɵelement(1, \"i\", 215);\n    i0.ɵɵtext(2, \" Direct \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 216);\n    i0.ɵɵelement(1, \"i\", 217);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r53.items[0].flightClass.name, \" \");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 218);\n    i0.ɵɵelement(1, \"i\", 219);\n    i0.ɵɵtext(2, \" Branded Fare \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 220);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", flight_r53.items[0].stopCount, \" stop\", flight_r53.items[0].stopCount > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 221);\n    i0.ɵɵtext(1, \" Direct \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 222);\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext().$implicit;\n    const ctx_r61 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r61.getBaggageSummary(flight_r53.items[0].baggageInformations));\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 222);\n    i0.ɵɵelement(1, \"i\", 223);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"In-flight services available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 222);\n    i0.ɵɵelement(1, \"i\", 217);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", flight_r53.offers[0].seatInfo.availableSeatCount, \" seats left\");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_span_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Show details\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_span_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Hide details\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_70_div_4_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 213);\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"src\", flight_r53.items[0].airline.thumbnailFull, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_70_div_4_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 241);\n    i0.ɵɵelement(1, \"i\", 9);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r53.items[0].aircraft, \" \");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_70_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 227)(1, \"div\", 228)(2, \"div\", 229);\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_156_div_3_div_57_div_70_div_4_img_3_Template, 1, 1, \"img\", 173);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 230);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, SearchPriceComponent_div_156_div_3_div_57_div_70_div_4_div_8_Template, 3, 1, \"div\", 231);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 232)(10, \"div\", 233)(11, \"div\", 234);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 235);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 236)(16, \"div\", 237);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 190);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 191);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 238)(23, \"div\", 239);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"div\", 194);\n    i0.ɵɵelementStart(26, \"div\", 195);\n    i0.ɵɵelement(27, \"i\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 240)(29, \"div\", 234);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 235);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 236)(34, \"div\", 237);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 190);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 191);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r74 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items[0].airline && flight_r53.items[0].airline.thumbnailFull);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r53.items[0].airline ? flight_r53.items[0].airline.name : \"Airline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r53.items[0].flightNo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items[0].aircraft);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r74.formatTime(flight_r53.items[0].departure.date));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r74.formatDate(flight_r53.items[0].departure.date));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r53.items[0].departure.airport.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r53.items[0].departure.airport.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", flight_r53.items[0].departure.city.name, \", \", flight_r53.items[0].departure.country.name, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r74.formatDuration(flight_r53.items[0].duration));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r74.formatTime(flight_r53.items[0].arrival.date));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r74.formatDate(flight_r53.items[0].arrival.date));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r53.items[0].arrival.airport.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r53.items[0].arrival.airport.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", flight_r53.items[0].arrival.city.name, \", \", flight_r53.items[0].arrival.country.name, \"\");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 213);\n  }\n  if (rf & 2) {\n    const segment_r82 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", segment_r82.airline.thumbnailFull, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 241);\n    i0.ɵɵelement(1, \"i\", 9);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r82 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", segment_r82.aircraft, \" \");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 245)(1, \"div\", 246);\n    i0.ɵɵelement(2, \"i\", 247);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 248)(4, \"span\", 249);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 250);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r89 = i0.ɵɵnextContext();\n    const segment_r82 = ctx_r89.$implicit;\n    const i_r83 = ctx_r89.index;\n    const flight_r53 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r86 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r86.calculateLayoverTime(segment_r82, flight_r53.items[0].segments[i_r83 + 1]));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Layover in \", segment_r82.arrival.city.name, \"\");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 227)(1, \"div\", 228)(2, \"div\", 243);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 229);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_img_5_Template, 1, 1, \"img\", 173);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 230);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_div_10_Template, 3, 1, \"div\", 231);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 232)(12, \"div\", 233)(13, \"div\", 234);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 235);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 236)(18, \"div\", 237);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 190);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 191);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 238)(25, \"div\", 239);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"div\", 194);\n    i0.ɵɵelementStart(28, \"div\", 195);\n    i0.ɵɵelement(29, \"i\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 240)(31, \"div\", 234);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 235);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 236)(36, \"div\", 237);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 190);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 191);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(42, SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_div_42_Template, 8, 2, \"div\", 244);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r82 = ctx.$implicit;\n    const i_r83 = ctx.index;\n    const flight_r53 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r81 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Segment \", i_r83 + 1, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", segment_r82.airline && segment_r82.airline.thumbnailFull);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r82.airline ? segment_r82.airline.name : \"Airline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r82.flightNo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", segment_r82.aircraft);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r81.formatTime(segment_r82.departure.date));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r81.formatDate(segment_r82.departure.date));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r82.departure.airport.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r82.departure.airport.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", segment_r82.departure.city.name, \", \", segment_r82.departure.country.name, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r81.formatDuration(segment_r82.duration));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r81.formatTime(segment_r82.arrival.date));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r81.formatDate(segment_r82.arrival.date));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r82.arrival.airport.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r82.arrival.airport.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", segment_r82.arrival.city.name, \", \", segment_r82.arrival.country.name, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r83 < flight_r53.items[0].segments.length - 1);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_Template, 43, 19, \"div\", 242);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", flight_r53.items[0].segments);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 224)(1, \"div\", 225)(2, \"h4\");\n    i0.ɵɵtext(3, \"Flight Itinerary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_156_div_3_div_57_div_70_div_4_Template, 40, 17, \"div\", 226);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_Template, 2, 1, \"div\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items && flight_r53.items[0] && flight_r53.items[0].stopCount === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items && flight_r53.items[0] && flight_r53.items[0].segments && flight_r53.items[0].segments.length > 1);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 268)(1, \"div\", 269);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 270);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r99 = ctx.$implicit;\n    const ctx_r96 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r96.getPassengerTypeName(item_r99.passengerType), \" (x\", item_r99.passengerCount, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 3, item_r99.price.amount, item_r99.price.currency));\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 271)(1, \"div\", 269);\n    i0.ɵɵtext(2, \"Service Fee\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 270);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, flight_r53.offers[0].serviceFee.amount, flight_r53.offers[0].serviceFee.currency));\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 272)(1, \"div\", 269);\n    i0.ɵɵtext(2, \"Taxes & Fees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 270);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, flight_r53.offers[0].priceBreakDown.items[0].airportTax.amount, flight_r53.offers[0].priceBreakDown.items[0].airportTax.currency));\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 261);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_div_1_Template, 6, 6, \"div\", 262);\n    i0.ɵɵtemplate(2, SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_div_2_Template, 6, 4, \"div\", 263);\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_div_3_Template, 6, 4, \"div\", 264);\n    i0.ɵɵelementStart(4, \"div\", 265)(5, \"div\", 266);\n    i0.ɵɵtext(6, \"Total Price\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 267);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", flight_r53.offers[0].priceBreakDown.items);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.offers[0].serviceFee);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.offers[0].priceBreakDown.items[0].airportTax);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 4, flight_r53.offers[0].price.amount, flight_r53.offers[0].price.currency));\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_71_div_5_div_4_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 281);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r104 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", feature_r104.explanations[0].text, \" \");\n  }\n}\nconst _c1 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"fa-suitcase\": a0,\n    \"fa-utensils\": a1,\n    \"fa-wifi\": a2,\n    \"fa-tv\": a3,\n    \"fa-exchange-alt\": a4,\n    \"fa-certificate\": true\n  };\n};\nfunction SearchPriceComponent_div_156_div_3_div_57_div_71_div_5_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 276)(1, \"div\", 277);\n    i0.ɵɵelement(2, \"i\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 278)(4, \"div\", 279);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_156_div_3_div_57_div_71_div_5_div_4_div_6_Template, 2, 1, \"div\", 280);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r104 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(3, _c1, feature_r104.serviceGroup === 1, feature_r104.serviceGroup === 2, feature_r104.serviceGroup === 3, feature_r104.serviceGroup === 4, feature_r104.serviceGroup === 5));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r104.commercialName || \"Feature\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", feature_r104.explanations && feature_r104.explanations.length > 0);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_71_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 273)(1, \"h5\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 274);\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_156_div_3_div_57_div_71_div_5_div_4_Template, 7, 9, \"div\", 275);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", flight_r53.offers[0].flightBrandInfo.name, \" Fare Features\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", flight_r53.offers[0].flightBrandInfo.features);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 224)(1, \"div\", 251)(2, \"h4\");\n    i0.ɵɵtext(3, \"Fare Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_Template, 10, 7, \"div\", 252);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_156_div_3_div_57_div_71_div_5_Template, 5, 2, \"div\", 253);\n    i0.ɵɵelementStart(6, \"div\", 254)(7, \"h5\");\n    i0.ɵɵtext(8, \"Fare Rules\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 255)(10, \"div\", 256);\n    i0.ɵɵelement(11, \"i\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 257)(13, \"div\", 258);\n    i0.ɵɵtext(14, \"Changes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 259);\n    i0.ɵɵtext(16, \"Changes allowed with fee\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 255)(18, \"div\", 256);\n    i0.ɵɵelement(19, \"i\", 260);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 257)(21, \"div\", 258);\n    i0.ɵɵtext(22, \"Cancellation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 259);\n    i0.ɵɵtext(24, \"Non-refundable\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.offers && flight_r53.offers[0] && flight_r53.offers[0].priceBreakDown);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.offers && flight_r53.offers[0] && flight_r53.offers[0].flightBrandInfo);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_div_1_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r112 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", baggage_r112.weight, \" \", baggage_r112.unitType === 1 ? \"kg\" : \"lbs\", \"\");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_div_1_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r112 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r112.piece, \" piece(s)\");\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"fa-suitcase\": a0,\n    \"fa-briefcase\": a1\n  };\n};\nfunction SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 288)(1, \"div\", 289);\n    i0.ɵɵelement(2, \"i\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 290)(4, \"div\", 291);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 282);\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_div_1_span_7_Template, 2, 2, \"span\", 61);\n    i0.ɵɵtemplate(8, SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_div_1_span_8_Template, 2, 1, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 292);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const baggage_r112 = ctx.$implicit;\n    const ctx_r111 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c2, baggage_r112.baggageType === 1, baggage_r112.baggageType === 2));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r111.getBaggageTypeName(baggage_r112.baggageType));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", baggage_r112.weight > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", baggage_r112.piece > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" For \", ctx_r111.getPassengerTypeName(baggage_r112.passengerType), \" \");\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 285);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_div_1_Template, 11, 8, \"div\", 286);\n    i0.ɵɵelementStart(2, \"div\", 287)(3, \"p\");\n    i0.ɵɵtext(4, \"Additional baggage can be purchased during check-in or by contacting the airline directly.\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", flight_r53.items[0].baggageInformations);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_72_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 293);\n    i0.ɵɵelement(1, \"i\", 294);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No baggage information available for this flight. Please contact the airline for details.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 224)(1, \"div\", 282)(2, \"h4\");\n    i0.ɵɵtext(3, \"Baggage Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_Template, 5, 1, \"div\", 283);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_156_div_3_div_57_div_72_div_5_Template, 4, 0, \"div\", 284);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r53 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items && flight_r53.items[0] && flight_r53.items[0].baggageInformations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !flight_r53.items || !flight_r53.items[0] || !flight_r53.items[0].baggageInformations || flight_r53.items[0].baggageInformations.length === 0);\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r120 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 169)(1, \"div\", 170)(2, \"div\", 171)(3, \"div\", 172);\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_156_div_3_div_57_img_4_Template, 1, 1, \"img\", 173);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_156_div_3_div_57_i_5_Template, 1, 0, \"i\", 174);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 175);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementStart(8, \"span\", 176);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 177);\n    i0.ɵɵtemplate(11, SearchPriceComponent_div_156_div_3_div_57_span_11_Template, 3, 0, \"span\", 178);\n    i0.ɵɵtemplate(12, SearchPriceComponent_div_156_div_3_div_57_span_12_Template, 3, 1, \"span\", 179);\n    i0.ɵɵtemplate(13, SearchPriceComponent_div_156_div_3_div_57_span_13_Template, 3, 0, \"span\", 180);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 181)(15, \"div\", 182);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 183);\n    i0.ɵɵtext(18, \"per person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 184);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 185)(22, \"div\", 186)(23, \"div\", 187);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 188);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 189)(28, \"div\", 190);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 191);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 192)(33, \"div\", 193);\n    i0.ɵɵelement(34, \"div\", 194);\n    i0.ɵɵelementStart(35, \"div\", 195);\n    i0.ɵɵelement(36, \"i\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 196);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 197);\n    i0.ɵɵtemplate(40, SearchPriceComponent_div_156_div_3_div_57_span_40_Template, 2, 2, \"span\", 198);\n    i0.ɵɵtemplate(41, SearchPriceComponent_div_156_div_3_div_57_span_41_Template, 2, 0, \"span\", 199);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 200)(43, \"div\", 187);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 188);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 189)(48, \"div\", 190);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 191);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(52, \"div\", 201);\n    i0.ɵɵtemplate(53, SearchPriceComponent_div_156_div_3_div_57_div_53_Template, 4, 1, \"div\", 202);\n    i0.ɵɵtemplate(54, SearchPriceComponent_div_156_div_3_div_57_div_54_Template, 4, 0, \"div\", 202);\n    i0.ɵɵtemplate(55, SearchPriceComponent_div_156_div_3_div_57_div_55_Template, 4, 1, \"div\", 202);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"div\", 203)(57, \"button\", 204);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_3_div_57_Template_button_click_57_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r120);\n      const flight_r53 = restoredCtx.$implicit;\n      const ctx_r119 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r119.toggleFlightDetails(flight_r53));\n    });\n    i0.ɵɵtemplate(58, SearchPriceComponent_div_156_div_3_div_57_span_58_Template, 2, 0, \"span\", 61);\n    i0.ɵɵtemplate(59, SearchPriceComponent_div_156_div_3_div_57_span_59_Template, 2, 0, \"span\", 61);\n    i0.ɵɵelement(60, \"i\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 205)(62, \"div\", 206)(63, \"div\", 207);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_3_div_57_Template_div_click_63_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r120);\n      const flight_r53 = restoredCtx.$implicit;\n      const ctx_r121 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r121.setActiveTab(flight_r53, \"flight\"));\n    });\n    i0.ɵɵtext(64, \" Flight Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"div\", 207);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_3_div_57_Template_div_click_65_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r120);\n      const flight_r53 = restoredCtx.$implicit;\n      const ctx_r122 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r122.setActiveTab(flight_r53, \"fare\"));\n    });\n    i0.ɵɵtext(66, \" Fare Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"div\", 207);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_3_div_57_Template_div_click_67_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r120);\n      const flight_r53 = restoredCtx.$implicit;\n      const ctx_r123 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r123.setActiveTab(flight_r53, \"baggage\"));\n    });\n    i0.ɵɵtext(68, \" Baggage \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 208);\n    i0.ɵɵtemplate(70, SearchPriceComponent_div_156_div_3_div_57_div_70_Template, 6, 2, \"div\", 209);\n    i0.ɵɵtemplate(71, SearchPriceComponent_div_156_div_3_div_57_div_71_Template, 25, 2, \"div\", 209);\n    i0.ɵɵtemplate(72, SearchPriceComponent_div_156_div_3_div_57_div_72_Template, 6, 2, \"div\", 209);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 210)(74, \"button\", 211);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_3_div_57_Template_button_click_74_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r120);\n      const flight_r53 = restoredCtx.$implicit;\n      const ctx_r124 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r124.selectThisFlight(flight_r53));\n    });\n    i0.ɵɵelement(75, \"i\", 212);\n    i0.ɵɵtext(76, \" Select \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r53 = ctx.$implicit;\n    const ctx_r42 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"unavailable\", !ctx_r42.isFlightAvailable(flight_r53));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items && flight_r53.items[0] && flight_r53.items[0].airline && flight_r53.items[0].airline.thumbnailFull);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(flight_r53.items && flight_r53.items[0] && flight_r53.items[0].airline && flight_r53.items[0].airline.thumbnailFull));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r53.items && flight_r53.items[0] && flight_r53.items[0].airline ? flight_r53.items[0].airline.name : \"Airline\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r53.items && flight_r53.items[0] ? flight_r53.items[0].flightNo : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items && flight_r53.items[0] && flight_r53.items[0].stopCount === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items && flight_r53.items[0] && flight_r53.items[0].flightClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.offers && flight_r53.offers[0] && flight_r53.offers[0].hasBrand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r42.getMinPrice(flight_r53));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"available\", ctx_r42.isFlightAvailable(flight_r53));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r42.isFlightAvailable(flight_r53) ? \"Available\" : \"Not available\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(flight_r53.items && flight_r53.items[0] && flight_r53.items[0].departure ? ctx_r42.formatTime(flight_r53.items[0].departure.date) : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r53.items && flight_r53.items[0] && flight_r53.items[0].departure ? ctx_r42.formatDate(flight_r53.items[0].departure.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r53.items && flight_r53.items[0] && flight_r53.items[0].departure && flight_r53.items[0].departure.airport ? flight_r53.items[0].departure.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r53.items && flight_r53.items[0] && flight_r53.items[0].departure && flight_r53.items[0].departure.city ? flight_r53.items[0].departure.city.name : \"N/A\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(flight_r53.items && flight_r53.items[0] ? ctx_r42.formatDuration(flight_r53.items[0].duration) : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items && flight_r53.items[0] && flight_r53.items[0].stopCount > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items && flight_r53.items[0] && flight_r53.items[0].stopCount === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r53.items && flight_r53.items[0] && flight_r53.items[0].arrival ? ctx_r42.formatTime(flight_r53.items[0].arrival.date) : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r53.items && flight_r53.items[0] && flight_r53.items[0].arrival ? ctx_r42.formatDate(flight_r53.items[0].arrival.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r53.items && flight_r53.items[0] && flight_r53.items[0].arrival && flight_r53.items[0].arrival.airport ? flight_r53.items[0].arrival.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r53.items && flight_r53.items[0] && flight_r53.items[0].arrival && flight_r53.items[0].arrival.city ? flight_r53.items[0].arrival.city.name : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items && flight_r53.items[0] && flight_r53.items[0].baggageInformations && flight_r53.items[0].baggageInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.items && flight_r53.items[0] && flight_r53.items[0].services && flight_r53.items[0].services.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r53.offers && flight_r53.offers[0] && flight_r53.offers[0].seatInfo);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r42.isFlightDetailsOpen(flight_r53));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r42.isFlightDetailsOpen(flight_r53));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r42.isFlightDetailsOpen(flight_r53) ? \"fa-chevron-up\" : \"fa-chevron-down\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"show\", ctx_r42.isFlightDetailsOpen(flight_r53));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r42.getActiveTab(flight_r53) === \"flight\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r42.getActiveTab(flight_r53) === \"fare\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r42.getActiveTab(flight_r53) === \"baggage\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r42.getActiveTab(flight_r53) === \"flight\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r42.getActiveTab(flight_r53) === \"fare\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r42.getActiveTab(flight_r53) === \"baggage\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r42.isFlightAvailable(flight_r53));\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r126 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 295)(1, \"div\", 296)(2, \"div\", 297);\n    i0.ɵɵelement(3, \"i\", 107);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"No flights found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"We couldn't find any flights matching your search criteria.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 298)(9, \"h4\");\n    i0.ɵɵtext(10, \"Try adjusting your search:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 299)(12, \"div\", 300);\n    i0.ɵɵelement(13, \"i\", 41);\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Try different dates\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 300);\n    i0.ɵɵelement(17, \"i\", 301);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Consider nearby airports\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 300);\n    i0.ɵɵelement(21, \"i\", 156);\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Remove filters\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"button\", 302);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_3_div_58_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r125 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r125.scrollToSearchForm());\n    });\n    i0.ɵɵelement(25, \"i\", 303);\n    i0.ɵɵtext(26, \" Modify Search \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SearchPriceComponent_div_156_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r128 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 125)(1, \"div\", 126)(2, \"div\", 127)(3, \"h3\");\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_156_div_3_span_4_Template, 2, 2, \"span\", 61);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_156_div_3_span_5_Template, 2, 1, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_156_div_3_p_6_Template, 2, 0, \"p\", 61);\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_156_div_3_p_7_Template, 7, 3, \"p\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, SearchPriceComponent_div_156_div_3_div_8_Template, 29, 6, \"div\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 129)(10, \"div\", 130)(11, \"h4\");\n    i0.ɵɵtext(12, \"Filter Results\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_3_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r128);\n      const ctx_r127 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r127.toggleFiltersPanel());\n    });\n    i0.ɵɵelement(14, \"i\", 132);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 133)(16, \"div\", 134)(17, \"h5\");\n    i0.ɵɵtext(18, \"Price Range\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 135)(20, \"div\", 136)(21, \"span\");\n    i0.ɵɵtext(22, \"\\u20AC0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24, \"\\u20AC2000\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 134)(26, \"h5\");\n    i0.ɵɵtext(27, \"Airlines\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 137);\n    i0.ɵɵtemplate(29, SearchPriceComponent_div_156_div_3_div_29_Template, 5, 1, \"div\", 138);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 134)(31, \"h5\");\n    i0.ɵɵtext(32, \"Departure Time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 139)(34, \"div\", 140)(35, \"div\", 141);\n    i0.ɵɵelement(36, \"i\", 142);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵelement(38, \"i\", 143);\n    i0.ɵɵtext(39, \" Morning (6AM - 12PM)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 140)(41, \"div\", 141);\n    i0.ɵɵelement(42, \"i\", 142);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\");\n    i0.ɵɵelement(44, \"i\", 144);\n    i0.ɵɵtext(45, \" Afternoon (12PM - 6PM)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 140)(47, \"div\", 141);\n    i0.ɵɵelement(48, \"i\", 145);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"span\");\n    i0.ɵɵtext(50, \"Evening/Night (After 6PM)\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(51, \"div\", 146)(52, \"button\", 147);\n    i0.ɵɵtext(53, \"Reset All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_156_div_3_Template_button_click_54_listener() {\n      i0.ɵɵrestoreView(_r128);\n      const ctx_r129 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r129.toggleFiltersPanel());\n    });\n    i0.ɵɵtext(55, \"Apply Filters\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(56, \"div\", 149);\n    i0.ɵɵtemplate(57, SearchPriceComponent_div_156_div_3_div_57_Template, 77, 42, \"div\", 150);\n    i0.ɵɵtemplate(58, SearchPriceComponent_div_156_div_3_div_58_Template, 27, 0, \"div\", 151);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r33.searchForm.get(\"departureLocation\")) == null ? null : tmp_0_0.value) && ((tmp_0_0 = ctx_r33.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_0_0.value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r33.searchForm.get(\"departureDate\")) == null ? null : tmp_1_0.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"show\", ctx_r33.showFiltersPanel);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r33.getUniqueAirlines());\n    i0.ɵɵadvance(28);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r33.searchResults);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r33.isLoading && !ctx_r33.errorMessage && ctx_r33.searchResults.length === 0);\n  }\n}\nfunction SearchPriceComponent_div_156_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_156_div_1_Template, 11, 0, \"div\", 111);\n    i0.ɵɵtemplate(2, SearchPriceComponent_div_156_div_2_Template, 11, 1, \"div\", 112);\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_156_div_3_Template, 59, 10, \"div\", 113);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.isLoading && ctx_r12.errorMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.isLoading && !ctx_r12.errorMessage);\n  }\n}\nexport class SearchPriceComponent {\n  constructor(fb, productService, router) {\n    this.fb = fb;\n    this.productService = productService;\n    this.router = router;\n    this.departureLocations = [];\n    this.arrivalLocations = [];\n    this.isLoading = false;\n    this.searchResults = [];\n    this.hasSearched = false;\n    this.errorMessage = '';\n    this.lastSearchId = '';\n    // Trip types\n    this.tripTypes = [{\n      value: 'roundTrip',\n      label: 'Round Trip'\n    }, {\n      value: 'oneWay',\n      label: 'One Way'\n    }, {\n      value: 'multiCity',\n      label: 'Multi-City'\n    }];\n    this.selectedTripType = 'roundTrip';\n    // Passenger type options\n    this.passengerTypes = [{\n      value: PassengerType.Adult,\n      label: 'Adult'\n    }, {\n      value: PassengerType.Child,\n      label: 'Child'\n    }, {\n      value: PassengerType.Infant,\n      label: 'Infant'\n    }];\n    // Flight class options\n    this.flightClasses = [{\n      value: FlightClassType.PROMO,\n      label: 'Promo'\n    }, {\n      value: FlightClassType.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClassType.BUSINESS,\n      label: 'Business'\n    }];\n    // Nouvelles propriétés pour les fonctionnalités avancées\n    this.showTravelersDropdown = false;\n    this.showFiltersPanel = false;\n    this.directFlightsOnly = false;\n    this.baggageIncluded = false;\n    this.flightDetailsMap = new Map();\n    // Price range filter\n    this.priceRange = {\n      min: 0,\n      max: 5000\n    };\n    this.filteredPriceRange = {\n      min: 0,\n      max: 5000\n    };\n    // Airlines filter\n    this.availableAirlines = [];\n    this.selectedAirlines = [];\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    this.minReturnDate = this.minDate;\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required],\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required],\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      returnDate: [this.addDays(this.minDate, 7)],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required],\n      // Options de vol\n      flightClass: [0, Validators.required],\n      nonStop: [false],\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n    // Listen for departure date changes to update minimum return date\n    this.searchForm.get('departureDate')?.valueChanges.subscribe(date => {\n      this.minReturnDate = date;\n      const currentReturnDate = this.searchForm.get('returnDate')?.value;\n      if (currentReturnDate && new Date(currentReturnDate) < new Date(date)) {\n        this.searchForm.get('returnDate')?.setValue(date);\n      }\n    });\n  }\n  ngOnInit() {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight) {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n    header.appendChild(logo);\n    header.appendChild(title);\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n        generalInfo.appendChild(airlineInfo);\n      }\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n      routeSection.appendChild(routeVisual);\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n          segmentsList.appendChild(segmentItem);\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n            segmentsList.appendChild(layover);\n          }\n        });\n        routeSection.appendChild(segmentsList);\n      }\n    }\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n        // ID de l'offre\n        const offerId = this.createInfoRow('Offer ID', offer.offerId || offer.id || 'N/A');\n        offerId.style.gridColumn = '1 / -1';\n        offerDetails.appendChild(offerId);\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n        offerItem.appendChild(offerDetails);\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n          const baggageList = document.createElement('ul');\n          baggageList.style.listStyle = 'none';\n          baggageList.style.padding = '0';\n          baggageList.style.margin = '0';\n          offer.baggageInformations.forEach(baggage => {\n            const baggageItem = document.createElement('li');\n            baggageItem.style.marginBottom = '5px';\n            baggageItem.innerHTML = `<i class=\"fas fa-suitcase\" style=\"color: #666; margin-right: 8px;\"></i> ${this.getBaggageTypeName(baggage.baggageType)}`;\n            baggageList.appendChild(baggageItem);\n          });\n          offerItem.appendChild(baggageList);\n        }\n        offersList.appendChild(offerItem);\n      });\n      offersSection.appendChild(offersList);\n    }\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n  // Méthode utilitaire pour créer une section\n  createSection(title, iconClass) {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n    return section;\n  }\n  // Méthode utilitaire pour créer une ligne d'information\n  createInfoRow(label, value) {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n    return row;\n  }\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight) {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n  preloadLocations() {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n  setupAutocomplete() {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.departureLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('departureLocation')?.setValue('');\n      });\n    });\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.arrivalLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('arrivalLocation')?.setValue('');\n      });\n    });\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  displayLocation(location) {\n    if (!location) return '';\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n  onSearch() {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n    const formValue = this.searchForm.value;\n    // Créer la requête de recherche entièrement dynamique\n    const request = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [{\n        id: formValue.departureLocation?.id || '',\n        type: formValue.departureLocationType\n      }],\n      ArrivalLocations: [{\n        id: formValue.arrivalLocation?.id || '',\n        type: formValue.arrivalLocationType\n      }],\n      Passengers: [{\n        type: formValue.passengerType,\n        count: formValue.passengerCount\n      }],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n    this.productService.searchPrice(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.header.success) {\n          this.searchResults = response.body.flights;\n          // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n          console.log('Full API Response:', JSON.stringify(response, null, 2));\n          // Analyser les données de disponibilité\n          if (response.body && response.body.flights && response.body.flights.length > 0) {\n            console.group('Availability Analysis');\n            console.log('Total flights:', response.body.flights.length);\n            // Compter les vols avec des offres\n            const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n            console.log('Flights with offers:', flightsWithOffers.length);\n            // Analyser les valeurs de disponibilité\n            const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability !== undefined ? o.availability : o.seatInfo ? o.seatInfo.availableSeatCount : 0));\n            console.log('Availability values:', availabilityValues);\n            // Compter les différentes valeurs de disponibilité\n            const availabilityCounts = availabilityValues.reduce((acc, val) => {\n              if (val !== undefined) {\n                acc[val] = (acc[val] || 0) + 1;\n              }\n              return acc;\n            }, {});\n            console.log('Availability counts:', availabilityCounts);\n            // Vérifier les vols réservables\n            const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true));\n            console.log('Reservable flights:', reservableFlights.length);\n            console.groupEnd();\n          }\n          // Vérifier si searchId existe dans le corps de la réponse\n          if (response.body && response.body.searchId) {\n            this.lastSearchId = response.body.searchId;\n            console.log('Search ID found in body.searchId:', this.lastSearchId);\n          }\n          // Vérifier si searchId existe dans l'en-tête de la réponse\n          else if (response.header && response.header.requestId) {\n            this.lastSearchId = response.header.requestId;\n            console.log('Search ID found in header.requestId:', this.lastSearchId);\n          }\n          // Vérifier d'autres emplacements possibles\n          else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n            this.lastSearchId = response.body.flights[0].id;\n            console.log('Using flight ID as search ID:', this.lastSearchId);\n          } else {\n            console.error('No search ID found in the response!');\n            console.log('Response structure:', Object.keys(response));\n            if (response.body) console.log('Body structure:', Object.keys(response.body));\n            if (response.header) console.log('Header structure:', Object.keys(response.header));\n          }\n        } else {\n          this.errorMessage = 'The search could not be completed. Please try again.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred during the search. Please try again.';\n        console.error('Error searching flights:', error);\n      }\n    });\n  }\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  // Formater la durée en heures et minutes\n  formatDuration(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}m`;\n  }\n  formatDate(dateString) {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      day: 'numeric',\n      month: 'short'\n    });\n  }\n  formatDateFull(dateString) {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric'\n    });\n  }\n  formatTime(dateString) {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit',\n      hour12: true\n    });\n  }\n  getMinPrice(flight) {\n    if (!flight.offers || flight.offers.length === 0) return 'N/A';\n    // Trouver l'offre avec le prix minimum\n    const minOffer = flight.offers.reduce((min, offer) => offer.price && offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n    return `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n  isFlightAvailable(flight) {\n    if (!flight.offers || flight.offers.length === 0) return false;\n    // Vérifier si au moins une offre est disponible\n    return flight.offers.some(offer => {\n      // Vérifier la disponibilité basée sur seatInfo si disponible\n      if (offer.seatInfo && offer.seatInfo.availableSeatCount !== undefined) {\n        return offer.seatInfo.availableSeatCount > 0;\n      }\n      // Sinon, vérifier la disponibilité basée sur le champ availability\n      if (offer.availability !== undefined) {\n        return offer.availability === 1; // 1 = Available dans l'API\n      }\n      // Par défaut, considérer comme disponible\n      return true;\n    });\n  }\n  showAllDepartureLocations() {\n    this.productService.getAllLocations().subscribe(locations => {\n      this.departureLocations = locations;\n    });\n  }\n  showAllArrivalLocations() {\n    this.productService.getAllLocations().subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  swapLocations() {\n    const departureValue = this.searchForm.get('departureLocation')?.value;\n    const departureTypeValue = this.searchForm.get('departureLocationType')?.value;\n    const arrivalValue = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalTypeValue = this.searchForm.get('arrivalLocationType')?.value;\n    this.searchForm.get('departureLocation')?.setValue(arrivalValue);\n    this.searchForm.get('departureLocationType')?.setValue(arrivalTypeValue);\n    this.searchForm.get('arrivalLocation')?.setValue(departureValue);\n    this.searchForm.get('arrivalLocationType')?.setValue(departureTypeValue);\n  }\n  formatExpirationDate(dateString) {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  getBaggageTypeName(baggageType) {\n    switch (baggageType) {\n      case 1:\n        return 'Checked Baggage';\n      case 2:\n        return 'Cabin Baggage';\n      default:\n        return 'Unknown';\n    }\n  }\n  getPassengerTypeName(passengerType) {\n    switch (passengerType) {\n      case 1:\n        return 'Adult';\n      case 2:\n        return 'Child';\n      case 3:\n        return 'Infant';\n      default:\n        return 'Unknown';\n    }\n  }\n  calculateLayoverTime(currentSegment, nextSegment) {\n    if (!currentSegment || !nextSegment || !currentSegment.arrival || !nextSegment.departure) {\n      return 'N/A';\n    }\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const layoverMinutes = Math.floor((departureTime - arrivalTime) / (1000 * 60));\n    return this.formatDuration(layoverMinutes);\n  }\n  toggleTravelersDropdown() {\n    this.showTravelersDropdown = !this.showTravelersDropdown;\n  }\n  incrementPassengers() {\n    const currentValue = this.searchForm.get('passengerCount')?.value;\n    if (currentValue < 9) {\n      this.searchForm.get('passengerCount')?.setValue(currentValue + 1);\n    }\n  }\n  decrementPassengers() {\n    const currentValue = this.searchForm.get('passengerCount')?.value;\n    if (currentValue > 1) {\n      this.searchForm.get('passengerCount')?.setValue(currentValue - 1);\n    }\n  }\n  selectFlightClass(value) {\n    this.searchForm.get('flightClass')?.setValue(value);\n  }\n  getFlightClassName(value) {\n    const flightClass = this.flightClasses.find(fc => fc.value === value);\n    return flightClass ? flightClass.label : 'Unknown';\n  }\n  toggleFiltersPanel() {\n    this.showFiltersPanel = !this.showFiltersPanel;\n  }\n  toggleDirectFlightsOnly() {\n    this.directFlightsOnly = !this.directFlightsOnly;\n    this.applyFilters();\n  }\n  toggleBaggageIncluded() {\n    this.baggageIncluded = !this.baggageIncluded;\n    this.applyFilters();\n  }\n  applyFilters() {\n    if (!this.hasSearched || this.searchResults.length === 0) return;\n    // Apply price range filter\n    if (this.filteredPriceRange.min > 0 || this.filteredPriceRange.max < 5000) {\n      // Filter by price logic would go here\n      // This would typically filter the searchResults array based on price\n      console.log(`Filtering by price range: ${this.filteredPriceRange.min} - ${this.filteredPriceRange.max}`);\n    }\n    // Apply direct flights filter\n    if (this.directFlightsOnly) {\n      // Filter for direct flights\n      console.log('Filtering for direct flights only');\n    }\n    // Apply baggage included filter\n    if (this.baggageIncluded) {\n      // Filter for flights with baggage included\n      console.log('Filtering for flights with baggage included');\n    }\n  }\n  toggleFlightDetails(flight) {\n    const flightId = flight.id || '';\n    if (!this.flightDetailsMap.has(flightId)) {\n      this.flightDetailsMap.set(flightId, {\n        isOpen: true,\n        activeTab: 'flight'\n      });\n    } else {\n      const details = this.flightDetailsMap.get(flightId);\n      if (details) {\n        details.isOpen = !details.isOpen;\n        this.flightDetailsMap.set(flightId, details);\n      }\n    }\n  }\n  isFlightDetailsOpen(flight) {\n    const flightId = flight.id || '';\n    return this.flightDetailsMap.has(flightId) && this.flightDetailsMap.get(flightId)?.isOpen === true;\n  }\n  setActiveTab(flight, tab) {\n    const flightId = flight.id || '';\n    if (this.flightDetailsMap.has(flightId)) {\n      const details = this.flightDetailsMap.get(flightId);\n      if (details) {\n        details.activeTab = tab;\n        this.flightDetailsMap.set(flightId, details);\n      }\n    } else {\n      this.flightDetailsMap.set(flightId, {\n        isOpen: true,\n        activeTab: tab\n      });\n    }\n  }\n  getActiveTab(flight) {\n    const flightId = flight.id || '';\n    return this.flightDetailsMap.has(flightId) ? this.flightDetailsMap.get(flightId)?.activeTab || 'flight' : 'flight';\n  }\n  scrollToSearchForm() {\n    const searchForm = document.querySelector('.search-form-container');\n    if (searchForm) {\n      searchForm.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }\n  setTripType(tripType) {\n    this.selectedTripType = tripType;\n    if (tripType === 'oneWay') {\n      this.searchForm.get('returnDate')?.clearValidators();\n      this.searchForm.get('returnDate')?.updateValueAndValidity();\n    } else if (tripType === 'roundTrip') {\n      this.searchForm.get('returnDate')?.setValidators([Validators.required]);\n      this.searchForm.get('returnDate')?.updateValueAndValidity();\n    }\n  }\n  addDays(date, days) {\n    const result = new Date(date);\n    result.setDate(result.getDate() + days);\n    return result.toISOString().split('T')[0];\n  }\n  filterByPrice() {\n    this.filteredPriceRange = {\n      ...this.priceRange\n    };\n    this.applyFilters();\n  }\n  sortResults(event) {\n    const sortBy = event.target.value;\n    console.log('Sorting results by:', sortBy);\n    // Implémenter la logique de tri\n    switch (sortBy) {\n      case 'price':\n        this.searchResults.sort((a, b) => {\n          const priceA = a.offers && a.offers[0] ? a.offers[0].price.amount : 0;\n          const priceB = b.offers && b.offers[0] ? b.offers[0].price.amount : 0;\n          return priceA - priceB;\n        });\n        break;\n      case 'duration':\n        this.searchResults.sort((a, b) => {\n          const durationA = a.items && a.items[0] ? a.items[0].duration : 0;\n          const durationB = b.items && b.items[0] ? b.items[0].duration : 0;\n          return durationA - durationB;\n        });\n        break;\n      case 'departure':\n        this.searchResults.sort((a, b) => {\n          const dateA = a.items && a.items[0] && a.items[0].departure ? new Date(a.items[0].departure.date).getTime() : 0;\n          const dateB = b.items && b.items[0] && b.items[0].departure ? new Date(b.items[0].departure.date).getTime() : 0;\n          return dateA - dateB;\n        });\n        break;\n      case 'arrival':\n        this.searchResults.sort((a, b) => {\n          const dateA = a.items && a.items[0] && a.items[0].arrival ? new Date(a.items[0].arrival.date).getTime() : 0;\n          const dateB = b.items && b.items[0] && b.items[0].arrival ? new Date(b.items[0].arrival.date).getTime() : 0;\n          return dateA - dateB;\n        });\n        break;\n    }\n  }\n  static {\n    this.ɵfac = function SearchPriceComponent_Factory(t) {\n      return new (t || SearchPriceComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchPriceComponent,\n      selectors: [[\"app-search-price\"]],\n      decls: 157,\n      vars: 34,\n      consts: [[1, \"search-price-container\"], [1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [1, \"search-content\"], [1, \"search-form-container\"], [1, \"search-tabs\"], [1, \"tab\", \"active\"], [1, \"fas\", \"fa-plane\"], [1, \"tab\", \"disabled\"], [1, \"fas\", \"fa-hotel\"], [1, \"fas\", \"fa-car\"], [1, \"fas\", \"fa-suitcase\"], [1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"trip-type-selector\"], [1, \"trip-type\", 3, \"click\"], [1, \"fas\", \"fa-exchange-alt\"], [1, \"fas\", \"fa-long-arrow-alt-right\"], [1, \"fas\", \"fa-route\"], [1, \"search-card\"], [\"type\", \"hidden\", \"formControlName\", \"productType\", \"value\", \"3\"], [\"type\", \"hidden\", \"formControlName\", \"serviceTypes\", \"value\", \"['1']\"], [1, \"search-grid\"], [1, \"form-group\", \"location-group\"], [\"for\", \"departureLocation\"], [1, \"input-with-icon\"], [1, \"fas\", \"fa-plane-departure\"], [\"type\", \"text\", \"id\", \"departureLocation\", \"formControlName\", \"departureLocation\", \"placeholder\", \"City or airport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [3, \"displayWith\"], [\"departureAuto\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"swap-button-container\"], [\"type\", \"button\", 1, \"swap-locations-btn\", 3, \"click\"], [\"for\", \"arrivalLocation\"], [1, \"fas\", \"fa-plane-arrival\"], [\"type\", \"text\", \"id\", \"arrivalLocation\", \"formControlName\", \"arrivalLocation\", \"placeholder\", \"City or airport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [\"arrivalAuto\", \"matAutocomplete\"], [1, \"form-group\", \"date-group\"], [\"for\", \"departureDate\"], [1, \"fas\", \"fa-calendar-alt\"], [\"type\", \"date\", \"id\", \"departureDate\", \"formControlName\", \"departureDate\", 1, \"form-control\", 3, \"min\"], [\"class\", \"form-group date-group\", 4, \"ngIf\"], [1, \"form-group\", \"travelers-group\"], [1, \"input-with-icon\", \"dropdown-toggle\", 3, \"click\"], [1, \"fas\", \"fa-users\"], [1, \"travelers-summary\"], [1, \"fas\", \"fa-chevron-down\"], [1, \"travelers-dropdown\"], [1, \"dropdown-section\"], [1, \"passenger-counter\"], [1, \"counter-controls\"], [\"type\", \"button\", 3, \"disabled\", \"click\"], [1, \"cabin-options\"], [\"class\", \"cabin-option\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"dropdown-actions\"], [\"type\", \"button\", 1, \"apply-btn\", 3, \"click\"], [1, \"search-button-container\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [\"class\", \"fas fa-search\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [1, \"advanced-options-container\"], [1, \"fas\", \"fa-sliders-h\"], [1, \"advanced-options\"], [1, \"options-grid\"], [1, \"form-group\", \"toggle-group\"], [1, \"toggle-switch\"], [\"type\", \"checkbox\", \"id\", \"nonStop\", \"formControlName\", \"nonStop\", 1, \"toggle-input\"], [\"for\", \"nonStop\", 1, \"toggle-label\"], [1, \"toggle-inner\"], [1, \"toggle-switch-label\"], [1, \"form-group\"], [\"for\", \"currency\"], [1, \"fas\", \"fa-money-bill-wave\"], [\"id\", \"currency\", \"formControlName\", \"currency\", 1, \"form-control\"], [\"value\", \"EUR\"], [\"value\", \"USD\"], [\"value\", \"GBP\"], [\"for\", \"culture\"], [1, \"fas\", \"fa-language\"], [\"id\", \"culture\", \"formControlName\", \"culture\", 1, \"form-control\"], [\"value\", \"en-US\"], [\"value\", \"fr-FR\"], [\"value\", \"es-ES\"], [\"for\", \"flightBaggageGetOption\"], [\"id\", \"flightBaggageGetOption\", \"formControlName\", \"flightBaggageGetOption\", 1, \"form-control\"], [3, \"value\"], [\"class\", \"search-results-container\", 4, \"ngIf\"], [1, \"location-option\"], [1, \"location-icon\"], [1, \"fas\", 3, \"ngClass\"], [1, \"location-details\"], [1, \"location-name\"], [1, \"location-info\"], [\"class\", \"location-code\", 4, \"ngIf\"], [\"class\", \"location-city\", 4, \"ngIf\"], [\"class\", \"location-country\", 4, \"ngIf\"], [1, \"location-code\"], [1, \"location-city\"], [1, \"location-country\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-circle\"], [\"for\", \"returnDate\"], [\"type\", \"date\", \"id\", \"returnDate\", \"formControlName\", \"returnDate\", 1, \"form-control\", 3, \"min\"], [1, \"cabin-option\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"spinner-container\"], [1, \"spinner\"], [1, \"search-results-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"search-results-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-animation\"], [1, \"plane-animation\"], [1, \"cloud\"], [1, \"loading-tips\"], [1, \"error-container\"], [1, \"error-content\"], [1, \"error-icon\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"retry-button\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"search-results-content\"], [1, \"results-header\"], [1, \"results-summary\"], [\"class\", \"results-actions\", 4, \"ngIf\"], [1, \"filters-panel\"], [1, \"filters-header\"], [1, \"close-filters\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"filters-content\"], [1, \"filter-section\"], [1, \"price-slider\"], [1, \"price-range\"], [1, \"airlines-list\"], [\"class\", \"airline-filter\", 4, \"ngFor\", \"ngForOf\"], [1, \"time-filters\"], [1, \"time-filter\"], [1, \"filter-checkbox\"], [1, \"fas\", \"fa-check\"], [1, \"fas\", \"fa-sun\"], [1, \"fas\", \"fa-cloud-sun\"], [1, \"fas\", \"fa-moon\"], [1, \"filters-actions\"], [1, \"reset-filters\"], [1, \"apply-filters\", 3, \"click\"], [1, \"flight-list\"], [\"class\", \"flight-card\", 3, \"unavailable\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"results-count\"], [1, \"results-actions\"], [1, \"filter-group\"], [1, \"filter-button\", 3, \"click\"], [1, \"fas\", \"fa-filter\"], [1, \"sort-dropdown\"], [1, \"sort-label\"], [1, \"fas\", \"fa-sort\"], [1, \"sort-select\", 3, \"change\"], [\"value\", \"price\"], [\"value\", \"duration\"], [\"value\", \"departure\"], [\"value\", \"arrival\"], [1, \"quick-filters\"], [1, \"quick-filter\", 3, \"click\"], [\"class\", \"fas fa-check\", 4, \"ngIf\"], [1, \"airline-filter\"], [1, \"flight-card\"], [1, \"flight-main-info\"], [1, \"airline-info\"], [1, \"airline-logo\"], [\"alt\", \"Airline logo\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"fas fa-plane\", 4, \"ngIf\"], [1, \"airline-name\"], [1, \"flight-number\"], [1, \"flight-badges\"], [\"class\", \"badge direct\", 4, \"ngIf\"], [\"class\", \"badge class\", 4, \"ngIf\"], [\"class\", \"badge branded\", 4, \"ngIf\"], [1, \"price-info\"], [1, \"price-amount\"], [1, \"price-details\"], [1, \"availability\"], [1, \"flight-route-info\"], [1, \"route-point\", \"departure\"], [1, \"time\"], [1, \"date\"], [1, \"location\"], [1, \"airport-code\"], [1, \"city\"], [1, \"flight-duration\"], [1, \"duration-line\"], [1, \"line\"], [1, \"plane-icon\"], [1, \"duration-time\"], [1, \"stops-info\"], [\"class\", \"has-stops\", 4, \"ngIf\"], [\"class\", \"non-stop\", 4, \"ngIf\"], [1, \"route-point\", \"arrival\"], [1, \"flight-features-summary\"], [\"class\", \"feature\", 4, \"ngIf\"], [1, \"flight-details-toggle\"], [1, \"toggle-details-btn\", 3, \"click\"], [1, \"flight-expanded-details\"], [1, \"details-tabs\"], [1, \"tab\", 3, \"click\"], [1, \"tab-content\"], [\"class\", \"tab-pane\", 4, \"ngIf\"], [1, \"flight-actions\"], [1, \"select-flight-btn\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-check-circle\"], [\"alt\", \"Airline logo\", 3, \"src\"], [1, \"badge\", \"direct\"], [1, \"fas\", \"fa-bolt\"], [1, \"badge\", \"class\"], [1, \"fas\", \"fa-chair\"], [1, \"badge\", \"branded\"], [1, \"fas\", \"fa-certificate\"], [1, \"has-stops\"], [1, \"non-stop\"], [1, \"feature\"], [1, \"fas\", \"fa-wifi\"], [1, \"tab-pane\"], [1, \"flight-segments\"], [\"class\", \"segment\", 4, \"ngIf\"], [1, \"segment\"], [1, \"segment-header\"], [1, \"segment-airline\"], [1, \"segment-flight-number\"], [\"class\", \"segment-aircraft\", 4, \"ngIf\"], [1, \"segment-timeline\"], [1, \"timeline-point\", \"departure\"], [1, \"point-time\"], [1, \"point-date\"], [1, \"point-location\"], [1, \"airport\"], [1, \"timeline-line\"], [1, \"duration\"], [1, \"timeline-point\", \"arrival\"], [1, \"segment-aircraft\"], [\"class\", \"segment\", 4, \"ngFor\", \"ngForOf\"], [1, \"segment-number\"], [\"class\", \"layover-info\", 4, \"ngIf\"], [1, \"layover-info\"], [1, \"layover-icon\"], [1, \"fas\", \"fa-hourglass-half\"], [1, \"layover-details\"], [1, \"layover-duration\"], [1, \"layover-location\"], [1, \"fare-details\"], [\"class\", \"fare-breakdown\", 4, \"ngIf\"], [\"class\", \"branded-fare-info\", 4, \"ngIf\"], [1, \"fare-rules\"], [1, \"rule-item\"], [1, \"rule-icon\"], [1, \"rule-details\"], [1, \"rule-name\"], [1, \"rule-description\"], [1, \"fas\", \"fa-ban\"], [1, \"fare-breakdown\"], [\"class\", \"breakdown-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"breakdown-item service-fee\", 4, \"ngIf\"], [\"class\", \"breakdown-item taxes\", 4, \"ngIf\"], [1, \"breakdown-total\"], [1, \"total-label\"], [1, \"total-value\"], [1, \"breakdown-item\"], [1, \"item-label\"], [1, \"item-value\"], [1, \"breakdown-item\", \"service-fee\"], [1, \"breakdown-item\", \"taxes\"], [1, \"branded-fare-info\"], [1, \"fare-features\"], [\"class\", \"feature-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature-item\"], [1, \"feature-icon\"], [1, \"feature-details\"], [1, \"feature-name\"], [\"class\", \"feature-description\", 4, \"ngIf\"], [1, \"feature-description\"], [1, \"baggage-details\"], [\"class\", \"baggage-allowance\", 4, \"ngIf\"], [\"class\", \"no-baggage-info\", 4, \"ngIf\"], [1, \"baggage-allowance\"], [\"class\", \"baggage-type\", 4, \"ngFor\", \"ngForOf\"], [1, \"baggage-policy\"], [1, \"baggage-type\"], [1, \"baggage-icon\"], [1, \"baggage-info\"], [1, \"baggage-name\"], [1, \"passenger-type\"], [1, \"no-baggage-info\"], [1, \"fas\", \"fa-info-circle\"], [1, \"no-results\"], [1, \"no-results-content\"], [1, \"no-results-icon\"], [1, \"suggestions\"], [1, \"suggestion-list\"], [1, \"suggestion-item\"], [1, \"fas\", \"fa-map-marker-alt\"], [1, \"modify-search-btn\", 3, \"click\"], [1, \"fas\", \"fa-edit\"]],\n      template: function SearchPriceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Discover Your Next Adventure\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Find the best flights to destinations worldwide\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7)(10, \"div\", 8);\n          i0.ɵɵelement(11, \"i\", 9);\n          i0.ɵɵtext(12, \" Flights \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 10);\n          i0.ɵɵelement(14, \"i\", 11);\n          i0.ɵɵtext(15, \" Hotels \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 10);\n          i0.ɵɵelement(17, \"i\", 12);\n          i0.ɵɵtext(18, \" Cars \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 10);\n          i0.ɵɵelement(20, \"i\", 13);\n          i0.ɵɵtext(21, \" Packages \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"form\", 14);\n          i0.ɵɵlistener(\"ngSubmit\", function SearchPriceComponent_Template_form_ngSubmit_22_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\", 16);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_div_click_24_listener() {\n            return ctx.setTripType(\"roundTrip\");\n          });\n          i0.ɵɵelement(25, \"i\", 17);\n          i0.ɵɵtext(26, \" Round Trip \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 16);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_div_click_27_listener() {\n            return ctx.setTripType(\"oneWay\");\n          });\n          i0.ɵɵelement(28, \"i\", 18);\n          i0.ɵɵtext(29, \" One Way \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 16);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_div_click_30_listener() {\n            return ctx.setTripType(\"multiCity\");\n          });\n          i0.ɵɵelement(31, \"i\", 19);\n          i0.ɵɵtext(32, \" Multi-City \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 20);\n          i0.ɵɵelement(34, \"input\", 21)(35, \"input\", 22);\n          i0.ɵɵelementStart(36, \"div\", 23)(37, \"div\", 24)(38, \"label\", 25);\n          i0.ɵɵtext(39, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 26);\n          i0.ɵɵelement(41, \"i\", 27);\n          i0.ɵɵelementStart(42, \"input\", 28);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_42_listener() {\n            return ctx.showAllDepartureLocations();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"mat-autocomplete\", 29, 30);\n          i0.ɵɵtemplate(45, SearchPriceComponent_mat_option_45_Template, 11, 12, \"mat-option\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, SearchPriceComponent_div_46_Template, 3, 0, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 33)(48, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_48_listener() {\n            return ctx.swapLocations();\n          });\n          i0.ɵɵelement(49, \"i\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 24)(51, \"label\", 35);\n          i0.ɵɵtext(52, \"To\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 26);\n          i0.ɵɵelement(54, \"i\", 36);\n          i0.ɵɵelementStart(55, \"input\", 37);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_55_listener() {\n            return ctx.showAllArrivalLocations();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"mat-autocomplete\", 29, 38);\n          i0.ɵɵtemplate(58, SearchPriceComponent_mat_option_58_Template, 11, 12, \"mat-option\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(59, SearchPriceComponent_div_59_Template, 3, 0, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 39)(61, \"label\", 40);\n          i0.ɵɵtext(62, \"Departure Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 26);\n          i0.ɵɵelement(64, \"i\", 41)(65, \"input\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(66, SearchPriceComponent_div_66_Template, 2, 0, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(67, SearchPriceComponent_div_67_Template, 7, 2, \"div\", 43);\n          i0.ɵɵelementStart(68, \"div\", 44)(69, \"label\");\n          i0.ɵɵtext(70, \"Travelers & Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 45);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_div_click_71_listener() {\n            return ctx.toggleTravelersDropdown();\n          });\n          i0.ɵɵelement(72, \"i\", 46);\n          i0.ɵɵelementStart(73, \"div\", 47)(74, \"span\");\n          i0.ɵɵtext(75);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"span\");\n          i0.ɵɵtext(77);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(78, \"i\", 48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 49)(80, \"div\", 50)(81, \"h4\");\n          i0.ɵɵtext(82, \"Passengers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"div\", 51)(84, \"span\");\n          i0.ɵɵtext(85, \"Adults\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"div\", 52)(87, \"button\", 53);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_87_listener() {\n            return ctx.decrementPassengers();\n          });\n          i0.ɵɵtext(88, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"span\");\n          i0.ɵɵtext(90);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"button\", 53);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_91_listener() {\n            return ctx.incrementPassengers();\n          });\n          i0.ɵɵtext(92, \"+\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(93, \"div\", 50)(94, \"h4\");\n          i0.ɵɵtext(95, \"Cabin Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"div\", 54);\n          i0.ɵɵtemplate(97, SearchPriceComponent_div_97_Template, 2, 3, \"div\", 55);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(98, \"div\", 56)(99, \"button\", 57);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_99_listener() {\n            return ctx.toggleTravelersDropdown();\n          });\n          i0.ɵɵtext(100, \"Apply\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(101, \"div\", 58)(102, \"button\", 59);\n          i0.ɵɵtemplate(103, SearchPriceComponent_i_103_Template, 1, 0, \"i\", 60);\n          i0.ɵɵtemplate(104, SearchPriceComponent_span_104_Template, 2, 0, \"span\", 61);\n          i0.ɵɵtemplate(105, SearchPriceComponent_div_105_Template, 2, 0, \"div\", 62);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(106, \"div\", 63)(107, \"details\")(108, \"summary\");\n          i0.ɵɵelement(109, \"i\", 64);\n          i0.ɵɵtext(110, \" More Options \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"div\", 65)(112, \"div\", 66)(113, \"div\", 67)(114, \"div\", 68);\n          i0.ɵɵelement(115, \"input\", 69);\n          i0.ɵɵelementStart(116, \"label\", 70);\n          i0.ɵɵelement(117, \"span\", 71);\n          i0.ɵɵelementStart(118, \"span\", 72);\n          i0.ɵɵtext(119, \"Direct flights only\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(120, \"div\", 73)(121, \"label\", 74);\n          i0.ɵɵtext(122, \"Currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(123, \"div\", 26);\n          i0.ɵɵelement(124, \"i\", 75);\n          i0.ɵɵelementStart(125, \"select\", 76)(126, \"option\", 77);\n          i0.ɵɵtext(127, \"Euro (\\u20AC)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"option\", 78);\n          i0.ɵɵtext(129, \"Dollar ($)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"option\", 79);\n          i0.ɵɵtext(131, \"Pound (\\u00A3)\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(132, \"div\", 73)(133, \"label\", 80);\n          i0.ɵɵtext(134, \"Language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(135, \"div\", 26);\n          i0.ɵɵelement(136, \"i\", 81);\n          i0.ɵɵelementStart(137, \"select\", 82)(138, \"option\", 83);\n          i0.ɵɵtext(139, \"English (US)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(140, \"option\", 84);\n          i0.ɵɵtext(141, \"Fran\\u00E7ais\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(142, \"option\", 85);\n          i0.ɵɵtext(143, \"Espa\\u00F1ol\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(144, \"div\", 73)(145, \"label\", 86);\n          i0.ɵɵtext(146, \"Baggage Options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"div\", 26);\n          i0.ɵɵelement(148, \"i\", 13);\n          i0.ɵɵelementStart(149, \"select\", 87)(150, \"option\", 88);\n          i0.ɵɵtext(151, \"All options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"option\", 88);\n          i0.ɵɵtext(153, \"Baggage included only\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(154, \"option\", 88);\n          i0.ɵɵtext(155, \"No baggage only\");\n          i0.ɵɵelementEnd()()()()()()()()()();\n          i0.ɵɵtemplate(156, SearchPriceComponent_div_156_Template, 4, 3, \"div\", 89);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(44);\n          const _r3 = i0.ɵɵreference(57);\n          let tmp_7_0;\n          let tmp_11_0;\n          let tmp_13_0;\n          let tmp_15_0;\n          let tmp_16_0;\n          let tmp_18_0;\n          let tmp_19_0;\n          let tmp_20_0;\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.selectedTripType === \"roundTrip\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"active\", ctx.selectedTripType === \"oneWay\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"active\", ctx.selectedTripType === \"multiCity\");\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.departureLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.searchForm.get(\"departureLocation\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.searchForm.get(\"departureLocation\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"matAutocomplete\", _r3);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrivalLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_11_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"min\", ctx.minDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.searchForm.get(\"departureDate\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.searchForm.get(\"departureDate\")) == null ? null : tmp_13_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTripType === \"roundTrip\");\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate1(\"\", (tmp_15_0 = ctx.searchForm.get(\"passengerCount\")) == null ? null : tmp_15_0.value, \" Traveler(s)\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFlightClassName((tmp_16_0 = ctx.searchForm.get(\"flightClass\")) == null ? null : tmp_16_0.value));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"show\", ctx.showTravelersDropdown);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ((tmp_18_0 = ctx.searchForm.get(\"passengerCount\")) == null ? null : tmp_18_0.value) <= 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate((tmp_19_0 = ctx.searchForm.get(\"passengerCount\")) == null ? null : tmp_19_0.value);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ((tmp_20_0 = ctx.searchForm.get(\"passengerCount\")) == null ? null : tmp_20_0.value) >= 9);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.searchForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(45);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasSearched);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.MatAutocomplete, i6.MatOption, i5.MatAutocompleteTrigger, i4.CurrencyPipe],\n      styles: [\"/* Styles globaux pour \\u00E9viter le d\\u00E9filement horizontal */\\n* {\\n  box-sizing: border-box;\\n}\\n\\n/* Conteneur principal - \\u00C9vite le d\\u00E9filement horizontal */\\n.search-price-container {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 10px;\\n  width: 100%;\\n  max-width: 100%; /* Utilisation de toute la largeur de l'\\u00E9cran */\\n  margin: 0;\\n  position: relative;\\n  z-index: 1;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-price-container {\\n    padding: 0;\\n    margin: 0;\\n  }\\n}\\n\\n.search-price-container::before {\\n  content: '';\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background:\\n    linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.03) 0%, rgba(var(--primary-color-rgb), 0) 50%),\\n    radial-gradient(circle at top right, rgba(var(--secondary-color-rgb), 0.03) 0%, rgba(var(--secondary-color-rgb), 0) 70%);\\n  z-index: -1;\\n  pointer-events: none;\\n}\\n\\n/* En-t\\u00EAte de page */\\n.page-header {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  margin-bottom: 30px;\\n  position: relative;\\n  overflow: hidden;\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.page-header::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.1));\\n  z-index: 1;\\n}\\n\\n.header-content {\\n  max-width: 800px;\\n  padding: 40px 20px;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 2;\\n  color: white;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n.page-title {\\n  font-size: 36px;\\n  font-weight: 700;\\n  color: white;\\n  margin-bottom: 15px;\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.page-title::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: white;\\n  border-radius: 3px;\\n}\\n\\n.page-subtitle {\\n  font-size: 18px;\\n  color: rgba(255, 255, 255, 0.9);\\n  line-height: 1.5;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  margin-top: 20px;\\n}\\n\\n.header-illustration {\\n  width: 100%;\\n  height: 300px;\\n  overflow: hidden;\\n}\\n\\n.header-illustration img {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n  transition: transform 10s ease;\\n}\\n\\n.page-header:hover .header-illustration img {\\n  transform: scale(1.1);\\n}\\n\\n/* Disposition pour les \\u00E9crans larges - Carte de recherche horizontale en haut */\\n@media (min-width: 992px) {\\n  .page-header {\\n    display: none;\\n  }\\n\\n  .search-price-container {\\n    flex-direction: column;\\n    align-items: stretch;\\n    padding: 0;\\n    margin: 0;\\n    width: 100%;\\n  }\\n\\n  .search-content {\\n    display: flex;\\n    flex-direction: column;\\n    width: 100%;\\n    gap: 0;\\n  }\\n\\n  .search-form-container {\\n    position: sticky;\\n    top: 0;\\n    width: 100%;\\n    max-height: none;\\n    overflow: visible;\\n    margin-bottom: 20px;\\n    padding: 0;\\n    border-radius: 0;\\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n    z-index: 100;\\n    background-color: white;\\n  }\\n\\n  .search-results-container {\\n    width: 100%;\\n    margin-left: 0;\\n    padding: 20px;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n  }\\n}\\n\\n/* Formulaire de recherche */\\n.search-form-container {\\n  background-color: white;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  padding: 25px;\\n  margin-bottom: 30px;\\n}\\n\\n/* Logo pour la version desktop */\\n.sidebar-logo {\\n  display: none;\\n}\\n\\n/* Style sp\\u00E9cifique pour les \\u00E9crans larges */\\n@media (min-width: 992px) {\\n  .search-form-container {\\n    padding: 0;\\n    margin-bottom: 20px;\\n    border-radius: 0;\\n  }\\n\\n  .sidebar-logo {\\n    display: block;\\n    background-color: var(--primary-color);\\n    color: white;\\n    padding: 15px 20px;\\n    text-align: left;\\n  }\\n\\n  .logo-container {\\n    display: flex;\\n    align-items: center;\\n    gap: 10px;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n    width: 100%;\\n  }\\n\\n  .logo-icon {\\n    font-size: 22px;\\n  }\\n\\n  .logo-text {\\n    font-size: 22px;\\n    font-weight: 700;\\n    letter-spacing: 0.5px;\\n  }\\n}\\n\\n.search-form-header {\\n  margin-bottom: 25px;\\n  text-align: center;\\n  padding: 20px 20px 0 20px;\\n}\\n\\n.search-form-header h2 {\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n  font-size: 26px;\\n  font-weight: 600;\\n}\\n\\n.search-form-header p {\\n  color: #666;\\n  font-size: 15px;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-form-header {\\n    max-width: 1200px;\\n    margin: 0 auto 15px auto;\\n    text-align: left;\\n    padding: 20px 20px 0 20px;\\n  }\\n}\\n\\n.search-form {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.form-group {\\n  margin-bottom: 15px;\\n}\\n\\n.form-row {\\n  display: flex;\\n  gap: 15px;\\n}\\n\\n.half-width {\\n  flex: 1;\\n}\\n\\nlabel {\\n  display: block;\\n  margin-bottom: 5px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 14px;\\n}\\n\\n.form-control {\\n  width: 100%;\\n  padding: 10px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 5px;\\n  font-size: 14px;\\n  transition: border-color 0.3s;\\n}\\n\\n.form-control:focus {\\n  border-color: #2989d8;\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(41, 137, 216, 0.2);\\n}\\n\\n.checkbox-group {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.checkbox-group input {\\n  margin-right: 8px;\\n}\\n\\n.search-button {\\n  padding: 12px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 5px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.3s;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-top: 10px;\\n}\\n\\n.search-button:hover {\\n  background-color: #1e5799;\\n}\\n\\n.search-button:disabled {\\n  background-color: #b3d4f0;\\n  cursor: not-allowed;\\n}\\n\\n.error-message {\\n  color: #e74c3c;\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n\\n/* Options avanc\\u00E9es */\\ndetails {\\n  margin-top: 10px;\\n  margin-bottom: 15px;\\n}\\n\\nsummary {\\n  cursor: pointer;\\n  color: #2989d8;\\n  font-weight: 500;\\n  padding: 5px 0;\\n}\\n\\nsummary:hover {\\n  text-decoration: underline;\\n}\\n\\n.advanced-options {\\n  margin-top: 10px;\\n  padding: 15px;\\n  background-color: #f8f9fa;\\n  border-radius: 5px;\\n  border: 1px solid #eee;\\n}\\n\\n/* Styles pour les listes d\\u00E9roulantes */\\n::ng-deep .mat-autocomplete-panel {\\n  max-height: 300px !important;\\n}\\n\\n::ng-deep .mat-option {\\n  height: auto !important;\\n  line-height: 1.2 !important;\\n  padding: 10px 16px !important;\\n}\\n\\n::ng-deep .mat-option small {\\n  color: #666;\\n  display: block;\\n  margin-top: 2px;\\n}\\n\\n/* R\\u00E9sultats de recherche - Design optimis\\u00E9 pour \\u00E9viter le d\\u00E9filement horizontal */\\n.search-results-container {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  box-shadow:\\n    0 5px 15px rgba(0, 0, 0, 0.08),\\n    0 0 0 1px rgba(var(--primary-color-rgb), 0.05);\\n  padding: 20px;\\n  position: relative;\\n  overflow-x: hidden;\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  box-sizing: border-box;\\n  width: 100%;\\n}\\n\\n.search-results-container::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 6px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\\n  z-index: 1;\\n}\\n\\n.loading-container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px;\\n  text-align: center;\\n}\\n\\n.loading-container p {\\n  margin-top: 20px;\\n  color: var(--primary-color);\\n  font-weight: 500;\\n  animation: pulse 1.5s infinite;\\n}\\n\\n.spinner {\\n  width: 30px;\\n  height: 30px;\\n  border: 3px solid rgba(var(--primary-color-rgb), 0.2);\\n  border-radius: 50%;\\n  border-top-color: var(--primary-color);\\n  animation: spin 1s cubic-bezier(0.6, 0.2, 0.4, 0.8) infinite;\\n  box-shadow: 0 0 10px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.spinner.large {\\n  width: 50px;\\n  height: 50px;\\n  border-width: 4px;\\n}\\n\\n@keyframes spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n.error-container {\\n  padding: 30px;\\n  background-color: rgba(231, 76, 60, 0.05);\\n  border-radius: 16px;\\n  text-align: center;\\n  border: 1px solid rgba(231, 76, 60, 0.1);\\n  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.05);\\n  animation: scaleIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\\n}\\n\\n.error-container h4 {\\n  color: #e74c3c;\\n  margin-bottom: 10px;\\n  font-size: 18px;\\n}\\n\\n.error-container p {\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.results-header {\\n  margin-bottom: 30px;\\n  position: relative;\\n  padding-bottom: 15px;\\n}\\n\\n.results-header::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0.1) 0%,\\n    rgba(var(--primary-color-rgb), 0.05) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n/* Provider information */\\n.provider-info {\\n  display: block;\\n  font-size: 12px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-top: 4px;\\n}\\n\\n/* Availability information */\\n.availability {\\n  font-size: 12px;\\n  display: block;\\n  margin-top: 4px;\\n  font-weight: 500;\\n}\\n\\n.availability i.fa-check-circle {\\n  color: #4CAF50;\\n}\\n\\n.availability i.fa-check-circle + span {\\n  color: #4CAF50;\\n}\\n\\n.availability i.fa-exclamation-triangle {\\n  color: #F44336;\\n}\\n\\n.availability i.fa-exclamation-triangle + span {\\n  color: #F44336;\\n}\\n\\n/* Expiration information */\\n.expiration {\\n  color: #FF9800;\\n  font-size: 12px;\\n  display: block;\\n  margin-top: 4px;\\n}\\n\\n/* Branded fare badge */\\n.flight-badge.branded {\\n  background-color: #9C27B0;\\n}\\n\\n/* Feature groups */\\n.feature-group {\\n  margin-bottom: 15px;\\n}\\n\\n.feature-group h4 {\\n  font-size: 14px;\\n  margin-bottom: 8px;\\n  color: rgba(0, 0, 0, 0.7);\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.feature-group .feature {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 4px;\\n  padding-left: 20px;\\n}\\n\\n/* Offer ID */\\n.offer-id {\\n  font-family: monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n}\\n\\n/* Price breakdown section */\\n.price-breakdown-section {\\n  margin: 15px 0;\\n}\\n\\n.price-breakdown-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.price-breakdown-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.price-breakdown-summary:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.price-breakdown-content {\\n  padding: 15px;\\n}\\n\\n.breakdown-group h4 {\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.breakdown-item {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 8px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.breakdown-item.service-fee {\\n  color: #FF5722;\\n}\\n\\n.breakdown-total {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-top: 10px;\\n  padding-top: 10px;\\n  border-top: 1px dashed rgba(0, 0, 0, 0.1);\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n}\\n\\n/* Segments section */\\n.segments-section {\\n  margin: 15px 0;\\n}\\n\\n.segments-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.segments-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.segments-summary:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.segments-content {\\n  padding: 15px;\\n}\\n\\n.segment-item {\\n  margin-bottom: 20px;\\n  padding-bottom: 15px;\\n  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);\\n}\\n\\n.segment-item:last-child {\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n  border-bottom: none;\\n}\\n\\n.segment-header {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 10px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.segment-number {\\n  font-weight: 600;\\n}\\n\\n.segment-route {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 10px;\\n}\\n\\n.layover-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 13px;\\n  color: #FF9800;\\n  margin-top: 10px;\\n  padding: 8px;\\n  background-color: rgba(255, 152, 0, 0.05);\\n  border-radius: 4px;\\n}\\n\\n/* Branded fare section */\\n.branded-fare-section {\\n  margin: 15px 0;\\n}\\n\\n.branded-fare-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.branded-fare-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(156, 39, 176, 0.05);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: #9C27B0;\\n}\\n\\n.branded-fare-summary:hover {\\n  background-color: rgba(156, 39, 176, 0.1);\\n}\\n\\n.branded-fare-content {\\n  padding: 15px;\\n}\\n\\n.branded-fare-description {\\n  margin-bottom: 15px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.branded-fare-features h4 {\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature-item {\\n  margin-bottom: 10px;\\n}\\n\\n.feature-name {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature-description {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.results-header h3 {\\n  color: var(--primary-dark);\\n  margin-bottom: 8px;\\n  font-size: 24px;\\n  font-weight: 600;\\n}\\n\\n.results-header p {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 14px;\\n}\\n\\n.flight-list {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 25px;\\n}\\n\\n.flight-card {\\n  background-color: white;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  box-shadow:\\n    0 2px 8px rgba(0, 0, 0, 0.05),\\n    0 0 0 1px rgba(0, 0, 0, 0.03);\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.flight-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.02) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  pointer-events: none;\\n}\\n\\n.flight-card:hover {\\n  transform: translateY(-8px) scale(1.01);\\n  box-shadow:\\n    0 15px 30px rgba(0, 0, 0, 0.08),\\n    0 0 0 1px rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n.flight-card.unavailable {\\n  opacity: 0.7;\\n  transform: none !important;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03) !important;\\n}\\n\\n.flight-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px;\\n  background-color: #f9f9f9;\\n  border-bottom: 1px solid #eaeaea;\\n  position: relative;\\n  flex-wrap: wrap;\\n}\\n\\n.flight-header::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -1px;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.airline-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.airline-logo {\\n  width: 40px;\\n  height: 40px;\\n  object-fit: contain;\\n  padding: 5px;\\n  background-color: white;\\n  border-radius: 50%;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .airline-logo {\\n  transform: scale(1.1);\\n}\\n\\n.airline-name {\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: var(--primary-dark);\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .airline-name {\\n  color: var(--primary-color);\\n}\\n\\n.flight-price {\\n  text-align: right;\\n  position: relative;\\n}\\n\\n.price {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: var(--primary-color);\\n  display: block;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n\\n.flight-card:hover .price {\\n  color: var(--secondary-color);\\n  transform: scale(1.05);\\n}\\n\\n.price::before {\\n  content: '';\\n  position: absolute;\\n  bottom: -3px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background-color: var(--secondary-color);\\n  transition: width 0.3s ease;\\n}\\n\\n.flight-card:hover .price::before {\\n  width: 100%;\\n}\\n\\n.availability {\\n  font-size: 13px;\\n  color: #e74c3c;\\n  font-weight: 500;\\n  margin-top: 5px;\\n}\\n\\n.flight-details {\\n  padding: 15px;\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.flight-route {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n  position: relative;\\n  width: 100%;\\n}\\n\\n@media (max-width: 768px) {\\n  .flight-route {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n}\\n\\n.departure, .arrival {\\n  flex: 1;\\n  position: relative;\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .departure {\\n  transform: translateX(-5px);\\n}\\n\\n.flight-card:hover .arrival {\\n  transform: translateX(5px);\\n}\\n\\n.time {\\n  font-size: 22px;\\n  font-weight: 700;\\n  margin-bottom: 8px;\\n  color: var(--primary-dark);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.time i {\\n  color: var(--primary-color);\\n  font-size: 18px;\\n  opacity: 0;\\n  transform: translateY(5px);\\n  transition: all 0.3s ease;\\n}\\n\\n.flight-card:hover .departure .time i {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.flight-card:hover .arrival .time i {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.location {\\n  font-size: 15px;\\n  color: rgba(0, 0, 0, 0.6);\\n  font-weight: 500;\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .location {\\n  color: var(--primary-color);\\n}\\n\\n.location small {\\n  display: block;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.4);\\n  margin-top: 3px;\\n}\\n\\n.flight-duration {\\n  flex: 1;\\n  text-align: center;\\n  padding: 0 20px;\\n  position: relative;\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .flight-duration {\\n  transform: translateY(-5px);\\n}\\n\\n.duration-line {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 10px;\\n  position: relative;\\n}\\n\\n.dot {\\n  width: 10px;\\n  height: 10px;\\n  background-color: var(--primary-color);\\n  border-radius: 50%;\\n  position: relative;\\n  z-index: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0 0 4px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.flight-card:hover .dot {\\n  background-color: var(--secondary-color);\\n  transform: scale(1.2);\\n  box-shadow: 0 0 0 6px rgba(var(--secondary-color-rgb), 0.15);\\n}\\n\\n.line {\\n  flex: 1;\\n  height: 2px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));\\n  margin: 0 8px;\\n  position: relative;\\n  transition: height 0.3s ease, background 0.3s ease;\\n}\\n\\n.flight-card:hover .line {\\n  height: 3px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\\n}\\n\\n.duration-text {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n  color: var(--primary-dark);\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .duration-text {\\n  color: var(--primary-color);\\n}\\n\\n.stops {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.5);\\n  font-weight: 500;\\n  padding: 4px 12px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  border-radius: 50px;\\n  display: inline-block;\\n  transition: all 0.3s ease;\\n}\\n\\n.flight-card:hover .stops {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n}\\n\\n.flight-info {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.5);\\n  margin-top: 20px;\\n  padding-top: 15px;\\n  border-top: 1px dashed rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.flight-actions {\\n  padding: 20px 25px;\\n  border-top: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  display: flex;\\n  gap: 15px;\\n  justify-content: flex-end;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.flight-actions::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.view-details-button {\\n  padding: 10px 18px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  color: var(--primary-dark);\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.1);\\n  border-radius: 50px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.view-details-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.1) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.view-details-button i {\\n  font-size: 16px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.view-details-button:hover {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.view-details-button:hover::before {\\n  opacity: 1;\\n}\\n\\n.view-details-button:hover i {\\n  transform: translateX(3px);\\n}\\n\\n.select-button {\\n  padding: 10px 24px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  color: white;\\n  border: none;\\n  border-radius: 50px;\\n  font-size: 15px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.select-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);\\n  z-index: 1;\\n}\\n\\n.select-button::after {\\n  content: '';\\n  position: absolute;\\n  top: -50%;\\n  left: -50%;\\n  width: 200%;\\n  height: 200%;\\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 60%);\\n  opacity: 0;\\n  transform: scale(0.5);\\n  transition: transform 0.8s ease, opacity 0.8s ease;\\n  z-index: 1;\\n}\\n\\n.select-button i {\\n  font-size: 16px;\\n  transition: transform 0.3s ease;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.select-button span {\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.select-button:hover {\\n  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));\\n  transform: translateY(-3px) scale(1.02);\\n  box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.select-button:hover::after {\\n  opacity: 1;\\n  transform: scale(1);\\n}\\n\\n.select-button:hover i {\\n  transform: translateX(3px);\\n  animation: pulse 1s infinite;\\n}\\n\\n.select-button:active {\\n  transform: translateY(-1px) scale(1);\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.select-button:disabled {\\n  background: linear-gradient(135deg, #b0b0b0, #d0d0d0);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  opacity: 0.7;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"/* Styles globaux pour \\u00E9viter le d\\u00E9filement horizontal */\\n.search-form * {\\n  box-sizing: border-box;\\n  max-width: 100%;\\n}\\n\\n.search-form input,\\n.search-form select,\\n.search-form button {\\n  max-width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n/* Styles pour la carte de recherche - Design professionnel inspir\\u00E9 des agences de voyage */\\n.search-card {\\n  background-color: var(--surface-color, white);\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n  padding: 20px;\\n  position: relative;\\n  margin-bottom: 20px;\\n  border: none;\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  box-sizing: border-box;\\n  overflow: hidden;\\n}\\n\\n/* Barre sup\\u00E9rieure bleue (style Booking.com) */\\n.search-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 4px;\\n  background-color: var(--primary-color);\\n  z-index: 1;\\n}\\n\\n/* Pas d'effet de survol exag\\u00E9r\\u00E9, juste une ombre l\\u00E9g\\u00E8rement plus prononc\\u00E9e */\\n.search-card:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n\\n/* Formulaire sur une seule ligne */\\n.single-line-form {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n  width: 100%;\\n}\\n\\n/* Disposition pour les \\u00E9crans larges - Carte de recherche horizontale */\\n@media (min-width: 992px) {\\n  .search-card {\\n    flex-direction: row;\\n    flex-wrap: nowrap;\\n    align-items: flex-end;\\n    padding: 15px;\\n    height: auto;\\n    border-radius: 0;\\n    box-shadow: none;\\n    margin-bottom: 0;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n  }\\n\\n  .single-line-form {\\n    flex-direction: row;\\n    align-items: flex-end;\\n    flex-wrap: nowrap;\\n    gap: 10px; /* Ajouter de l'espace entre les champs */\\n  }\\n\\n  /* Ajustement pour les champs From et To */\\n  .single-line-form .form-group:nth-child(1),\\n  .single-line-form .form-group:nth-child(5) {\\n    flex: 1.5;\\n    z-index: 4;\\n  }\\n\\n  /* Ajustement pour les autres champs */\\n  .single-line-form .form-group:nth-child(7),\\n  .single-line-form .form-group:nth-child(8),\\n  .single-line-form .form-group:nth-child(9) {\\n    flex: 0.8;\\n  }\\n\\n  /* Ajustement pour le conteneur du bouton d'\\u00E9change */\\n  .single-line-form .swap-button-container {\\n    flex: 0 0 auto;\\n    margin: 0;\\n    padding: 0 5px;\\n    z-index: 6;\\n  }\\n\\n  .form-group {\\n    min-width: 0; /* Permet aux \\u00E9l\\u00E9ments de r\\u00E9tr\\u00E9cir en dessous de leur largeur minimale */\\n    margin-bottom: 0;\\n    flex: 1;\\n    position: relative;\\n    z-index: 3;\\n    padding: 0 5px; /* Ajouter un peu d'espace de chaque c\\u00F4t\\u00E9 */\\n  }\\n\\n  .form-group.checkbox-group {\\n    flex: 0 0 auto;\\n  }\\n\\n  .search-button-container {\\n    flex: 0 0 auto;\\n    margin-top: 0;\\n    margin-left: 10px;\\n    text-align: center;\\n    padding-left: 5px;\\n  }\\n\\n  .search-button {\\n    width: auto;\\n    white-space: nowrap;\\n    padding: 10px 20px;\\n    height: 40px;\\n  }\\n}\\n\\n/* S\\u00E9lecteur de type de voyage - Style onglets (comme Booking.com) */\\n.trip-type-selector {\\n  display: flex;\\n  margin-bottom: 20px;\\n  position: relative;\\n  border-bottom: 1px solid #e7e7e7;\\n  padding-bottom: 0;\\n}\\n\\n.trip-type-option {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 20px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  font-weight: 500;\\n  color: #333;\\n  background-color: transparent;\\n  border: none;\\n  border-bottom: 3px solid transparent;\\n  position: relative;\\n  min-width: 100px;\\n  justify-content: center;\\n}\\n\\n.trip-type-option i {\\n  color: #666;\\n  font-size: 16px;\\n  transition: color 0.2s ease;\\n}\\n\\n.trip-type-option span {\\n  transition: color 0.2s ease;\\n}\\n\\n.trip-type-option.selected {\\n  color: var(--primary-color);\\n  font-weight: 600;\\n  border-bottom: 3px solid var(--primary-color);\\n  background-color: transparent;\\n}\\n\\n.trip-type-option.selected i {\\n  color: var(--primary-color);\\n}\\n\\n.trip-type-option:not(.selected):hover {\\n  color: var(--primary-color);\\n  border-bottom-color: rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.trip-type-option:not(.selected):hover i {\\n  color: var(--primary-color);\\n}\\n\\n.trip-type-option.disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n/* Rang\\u00E9es de formulaire - Style agences de voyage */\\n.form-row {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n\\n.locations-row {\\n  position: relative;\\n}\\n\\n/* Groupes de formulaire */\\n.form-group {\\n  flex: 1;\\n  position: relative;\\n}\\n\\n.location-type-selector {\\n  display: none; /* Cach\\u00E9 mais fonctionnel */\\n}\\n\\n/* \\u00C9tiquettes */\\nlabel {\\n  display: block;\\n  margin-bottom: 6px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 14px;\\n}\\n\\n/* Champs de saisie avec ic\\u00F4nes */\\n.input-with-icon {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.input-with-icon i {\\n  position: absolute;\\n  left: 12px;\\n  color: #666;\\n  font-size: 16px;\\n  z-index: 2;\\n}\\n\\n.form-control {\\n  width: 100%;\\n  padding: 12px 12px 12px 40px;\\n  border: 1px solid #e7e7e7;\\n  border-radius: 4px;\\n  font-size: 15px;\\n  transition: border-color 0.2s ease, box-shadow 0.2s ease;\\n  background-color: white;\\n  color: #333;\\n  height: 40px;\\n}\\n\\n.form-control:focus {\\n  outline: none;\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.form-control::placeholder {\\n  color: #999;\\n}\\n\\n/* Effet de focus sur le groupe entier */\\n.form-group:focus-within label {\\n  color: var(--primary-color);\\n}\\n\\n.form-group:focus-within .input-with-icon i {\\n  color: var(--primary-color);\\n}\\n\\n/* Ajustements pour le formulaire sur une seule ligne */\\n@media (min-width: 992px) {\\n  .single-line-form .form-group {\\n    margin-bottom: 0;\\n  }\\n\\n  .single-line-form label {\\n    font-size: 12px;\\n    margin-bottom: 4px;\\n  }\\n\\n  .single-line-form .form-control {\\n    font-size: 14px;\\n  }\\n\\n  .single-line-form .input-with-icon i {\\n    font-size: 14px;\\n  }\\n\\n  .single-line-form .error-message {\\n    position: absolute;\\n    font-size: 11px;\\n    bottom: -18px;\\n    left: 0;\\n    white-space: nowrap;\\n  }\\n}\\n\\n/* Conteneur du bouton d'\\u00E9change */\\n.swap-button-container {\\n  display: flex;\\n  align-items: flex-end;\\n  justify-content: center;\\n  padding-bottom: 10px; /* Aligner avec les champs de formulaire */\\n  position: relative;\\n  z-index: 5;\\n}\\n\\n/* Bouton d'\\u00E9change de lieux - Style agences de voyage */\\n.swap-locations-btn {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: var(--primary-color);\\n  color: white;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n\\n.swap-locations-btn:hover {\\n  background-color: var(--primary-dark);\\n  transform: rotate(180deg);\\n}\\n\\n.swap-locations-btn:active {\\n  transform: scale(0.95) rotate(180deg);\\n}\\n\\n.swap-locations-btn i {\\n  font-size: 16px;\\n}\\n\\n@media (min-width: 992px) {\\n  .swap-button-container {\\n    padding-bottom: 10px;\\n  }\\n}\\n\\n/* Options d'emplacement dans l'autocompl\\u00E9tion */\\n.location-option {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.location-name {\\n  font-weight: 500;\\n}\\n\\n.location-details {\\n  display: flex;\\n  gap: 12px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.location-code {\\n  font-weight: 500;\\n}\\n\\n.location-type {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.location-type i {\\n  font-size: 12px;\\n  color: #2989d8;\\n}\\n\\n/* Interrupteurs \\u00E0 bascule */\\n.toggle-switch {\\n  position: relative;\\n  display: inline-flex;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n\\n.toggle-input {\\n  opacity: 0;\\n  width: 0;\\n  height: 0;\\n  position: absolute;\\n}\\n\\n.toggle-label {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n}\\n\\n.toggle-inner {\\n  position: relative;\\n  display: inline-block;\\n  width: 50px;\\n  height: 24px;\\n  background-color: rgba(0, 0, 0, 0.12);\\n  border-radius: 12px;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.toggle-inner:before {\\n  content: '';\\n  position: absolute;\\n  left: 2px;\\n  top: 2px;\\n  width: 20px;\\n  height: 20px;\\n  background-color: white;\\n  border-radius: 50%;\\n  transition: transform 0.2s ease;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\\n}\\n\\n.toggle-input:checked + .toggle-label .toggle-inner {\\n  background-color: #2989d8;\\n}\\n\\n.toggle-input:checked + .toggle-label .toggle-inner:before {\\n  transform: translateX(26px);\\n}\\n\\n.toggle-switch-label {\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.8);\\n}\\n\\n.toggle-switch.small .toggle-inner {\\n  width: 40px;\\n  height: 20px;\\n}\\n\\n.toggle-switch.small .toggle-inner:before {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.toggle-switch.small .toggle-input:checked + .toggle-label .toggle-inner:before {\\n  transform: translateX(20px);\\n}\\n\\n/* Conteneur du bouton de recherche - Style agences de voyage */\\n.search-button-container {\\n  margin-top: 20px;\\n  display: flex;\\n  justify-content: center;\\n  position: relative;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-button-container {\\n    margin-top: 0;\\n  }\\n}\\n\\n/* Bouton de recherche - Style Booking.com */\\n.search-button {\\n  background-color: var(--primary-color);\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 12px 24px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 140px;\\n  justify-content: center;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (min-width: 992px) {\\n  .search-button {\\n    padding: 0 20px;\\n    font-size: 15px;\\n    min-width: 100px;\\n    height: 40px;\\n  }\\n\\n  .single-line-form .search-button-container {\\n    margin-top: 21px; /* Aligner avec les champs de formulaire (hauteur du label + marge) */\\n  }\\n}\\n\\n.search-button:hover:not(:disabled) {\\n  background-color: var(--primary-dark);\\n}\\n\\n.search-button:active:not(:disabled) {\\n  transform: translateY(1px);\\n}\\n\\n.search-button:disabled {\\n  background-color: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n.search-button i {\\n  font-size: 16px;\\n}\\n\\n/* Options avanc\\u00E9es - Design riche */\\n.advanced-options-container {\\n  margin-top: 25px;\\n  position: relative;\\n}\\n\\n.advanced-options-container::before {\\n  content: '';\\n  position: absolute;\\n  top: -10px;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.advanced-options-container summary {\\n  cursor: pointer;\\n  color: var(--primary-color);\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  padding: 12px 20px;\\n  transition: all 0.3s ease;\\n  outline: none;\\n  border-radius: 50px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.05);\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  margin: 0 auto;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.advanced-options-container summary::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.1) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.advanced-options-container summary:hover {\\n  color: var(--secondary-color);\\n  background-color: rgba(var(--primary-color-rgb), 0.08);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.advanced-options-container summary:hover::before {\\n  opacity: 1;\\n}\\n\\n.advanced-options-container summary i {\\n  font-size: 18px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.advanced-options-container[open] summary i {\\n  transform: rotate(180deg);\\n}\\n\\n.advanced-options {\\n  margin-top: 20px;\\n  padding: 25px;\\n  background-color: rgba(var(--primary-color-rgb), 0.03);\\n  border-radius: 16px;\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  box-shadow:\\n    inset 0 1px 8px rgba(var(--primary-color-rgb), 0.05),\\n    0 5px 15px rgba(0, 0, 0, 0.03);\\n  position: relative;\\n  animation: scaleIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\\n  overflow: hidden;\\n}\\n\\n.advanced-options::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background:\\n    radial-gradient(circle at top right, rgba(var(--primary-color-rgb), 0.05) 0%, rgba(var(--primary-color-rgb), 0) 70%),\\n    radial-gradient(circle at bottom left, rgba(var(--secondary-color-rgb), 0.05) 0%, rgba(var(--secondary-color-rgb), 0) 70%);\\n  pointer-events: none;\\n}\\n\\n.checkbox-options {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n}\\n\\n.checkbox-options .form-group {\\n  flex: 1 0 45%;\\n  transition: transform 0.3s ease;\\n}\\n\\n.checkbox-options .form-group:hover {\\n  transform: translateY(-2px);\\n}\\n\\n/* Messages d'erreur */\\n.error-message {\\n  color: #e74c3c;\\n  font-size: 13px;\\n  margin-top: 6px;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.error-message i {\\n  font-size: 14px;\\n}\\n\\n/* Animation de chargement */\\n.spinner-container {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spinner {\\n  width: 20px;\\n  height: 20px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  border-top-color: white;\\n  animation: spin 0.8s linear infinite;\\n}\\n\\n@keyframes spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n/* Styles pour les r\\u00E9sultats de recherche */\\n.search-results-container {\\n  background-color: white;\\n  border-radius: 16px;\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\\n  padding: 24px;\\n  margin-top: 24px;\\n}\\n\\n/* Animation de chargement personnalis\\u00E9e */\\n.loading-animation {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n}\\n\\n.plane-loader {\\n  position: relative;\\n  width: 200px;\\n  height: 100px;\\n  margin-bottom: 24px;\\n}\\n\\n.plane-loader i {\\n  position: absolute;\\n  font-size: 32px;\\n  color: #2989d8;\\n  animation: fly 3s infinite linear;\\n  top: 40%;\\n  left: 0;\\n}\\n\\n.cloud {\\n  position: absolute;\\n  width: 50px;\\n  height: 20px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 20px;\\n}\\n\\n.cloud:nth-child(2) {\\n  top: 20%;\\n  left: 20%;\\n  animation: cloud 8s infinite linear;\\n}\\n\\n.cloud:nth-child(3) {\\n  top: 60%;\\n  left: 40%;\\n  animation: cloud 6s infinite linear;\\n}\\n\\n.cloud:nth-child(4) {\\n  top: 40%;\\n  left: 60%;\\n  animation: cloud 10s infinite linear;\\n}\\n\\n@keyframes fly {\\n  0% {\\n    transform: translateX(0) rotate(0);\\n  }\\n  100% {\\n    transform: translateX(200px) rotate(0);\\n  }\\n}\\n\\n@keyframes cloud {\\n  0% {\\n    transform: translateX(0);\\n  }\\n  100% {\\n    transform: translateX(-200px);\\n  }\\n}\\n\\n.loading-animation p {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n/* Styles pour les r\\u00E9sultats */\\n.results-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.08);\\n}\\n\\n.results-title h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 4px;\\n}\\n\\n.results-count {\\n  font-weight: 600;\\n  color: #2989d8;\\n}\\n\\n.filter-option {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.filter-select {\\n  padding: 8px 12px;\\n  border: 1px solid rgba(0, 0, 0, 0.12);\\n  border-radius: 6px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.8);\\n  background-color: white;\\n}\\n\\n/* Carte de vol */\\n.flight-card {\\n  background-color: white;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n  margin-bottom: 20px;\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.flight-card:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\\n}\\n\\n.flight-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.airline-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.airline-logo-container {\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n.airline-logo {\\n  max-width: 32px;\\n  max-height: 32px;\\n  object-fit: contain;\\n}\\n\\n.airline-icon {\\n  font-size: 20px;\\n  color: #2989d8;\\n}\\n\\n.airline-details {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.airline-name {\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  font-size: 15px;\\n}\\n\\n.flight-number {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.flight-badges {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.flight-badge {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 4px 8px;\\n  background-color: rgba(41, 137, 216, 0.1);\\n  color: #2989d8;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.flight-price {\\n  text-align: right;\\n}\\n\\n.price-label {\\n  display: block;\\n  font-size: 12px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 4px;\\n}\\n\\n.price {\\n  font-size: 22px;\\n  font-weight: 700;\\n  color: #2989d8;\\n}\\n\\n.availability {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  color: #e74c3c;\\n  font-size: 12px;\\n  margin-top: 4px;\\n}\\n\\n.flight-details {\\n  padding: 20px;\\n}\\n\\n.flight-route {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.departure, .arrival {\\n  flex: 1;\\n}\\n\\n.time {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 6px;\\n}\\n\\n.location {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.airport-code {\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.7);\\n  font-size: 15px;\\n}\\n\\n.city-name {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 13px;\\n}\\n\\n.flight-duration {\\n  flex: 1;\\n  text-align: center;\\n  padding: 0 20px;\\n}\\n\\n.duration-line {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 10px;\\n}\\n\\n.dot {\\n  width: 10px;\\n  height: 10px;\\n  background-color: #2989d8;\\n  border-radius: 50%;\\n  z-index: 1;\\n}\\n\\n.departure-dot {\\n  background-color: #4CAF50;\\n}\\n\\n.arrival-dot {\\n  background-color: #F44336;\\n}\\n\\n.line-container {\\n  flex: 1;\\n  position: relative;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.line {\\n  width: 100%;\\n  height: 2px;\\n  background-color: #2989d8;\\n}\\n\\n.plane-icon {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  color: #2989d8;\\n  font-size: 14px;\\n}\\n\\n.duration-text {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.7);\\n  margin-bottom: 6px;\\n}\\n\\n.stops {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.stop-count {\\n  font-weight: 600;\\n  color: #F44336;\\n}\\n\\n.stops.direct {\\n  color: #4CAF50;\\n  font-weight: 500;\\n}\\n\\n.flight-features {\\n  display: flex;\\n  gap: 16px;\\n  margin-top: 16px;\\n  padding-top: 16px;\\n  border-top: 1px dashed rgba(0, 0, 0, 0.1);\\n}\\n\\n.feature {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature i {\\n  color: #2989d8;\\n  font-size: 14px;\\n}\\n\\n.flight-actions {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n  padding: 16px 20px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.view-details-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 10px 16px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: rgba(0, 0, 0, 0.7);\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.view-details-button:hover {\\n  background-color: rgba(0, 0, 0, 0.1);\\n}\\n\\n.select-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 10px 16px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(41, 137, 216, 0.3);\\n}\\n\\n.select-button:hover {\\n  background-color: #1e5799;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(41, 137, 216, 0.4);\\n}\\n\\n.select-button:disabled {\\n  background-color: rgba(0, 0, 0, 0.1);\\n  color: rgba(0, 0, 0, 0.4);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n/* Message pas de r\\u00E9sultats */\\n.no-results {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.no-results-icon {\\n  width: 80px;\\n  height: 80px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n}\\n\\n.no-results-icon i {\\n  font-size: 32px;\\n  color: rgba(0, 0, 0, 0.3);\\n}\\n\\n.no-results h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 8px;\\n}\\n\\n.no-results p {\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 24px;\\n  max-width: 500px;\\n}\\n\\n.no-results-suggestions {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  justify-content: center;\\n}\\n\\n.suggestion {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 10px 16px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 8px;\\n  color: rgba(0, 0, 0, 0.7);\\n  font-size: 14px;\\n}\\n\\n.suggestion i {\\n  color: #2989d8;\\n}\\n\\n/* Message d'erreur */\\n.error-container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.error-icon {\\n  width: 80px;\\n  height: 80px;\\n  background-color: rgba(231, 76, 60, 0.1);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n}\\n\\n.error-icon i {\\n  font-size: 32px;\\n  color: #e74c3c;\\n}\\n\\n.error-container h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 8px;\\n}\\n\\n.error-container .error-message {\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 24px;\\n  font-size: 15px;\\n  justify-content: center;\\n}\\n\\n.retry-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 20px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 15px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(41, 137, 216, 0.3);\\n}\\n\\n.retry-button:hover {\\n  background-color: #1e5799;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(41, 137, 216, 0.4);\\n}\\n\\n/* Styles responsifs */\\n@media (max-width: 768px) {\\n  .form-row {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .swap-locations-btn {\\n    display: none;\\n  }\\n\\n  .flight-route {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n\\n  .departure, .arrival {\\n    text-align: center;\\n  }\\n\\n  .flight-duration {\\n    margin: 16px 0;\\n  }\\n\\n  .flight-header {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .airline-info {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n\\n  .flight-badges {\\n    justify-content: center;\\n  }\\n\\n  .flight-price {\\n    text-align: center;\\n    width: 100%;\\n  }\\n\\n  .flight-features {\\n    flex-direction: column;\\n    gap: 12px;\\n    align-items: center;\\n  }\\n\\n  .flight-actions {\\n    flex-direction: column;\\n  }\\n\\n  .view-details-button, .select-button {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .page-header {\\n    flex-direction: column;\\n    gap: 24px;\\n  }\\n\\n  .header-content {\\n    text-align: center;\\n  }\\n\\n  .page-title {\\n    font-size: 1.75rem;\\n  }\\n\\n  .checkbox-options .form-group {\\n    flex: 1 0 100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "of", "FlightClassType", "LocationType", "PassengerType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "location_r13", "code", "city", "country", "ɵɵelement", "ɵɵtemplate", "SearchPriceComponent_mat_option_45_span_8_Template", "SearchPriceComponent_mat_option_45_span_9_Template", "SearchPriceComponent_mat_option_45_span_10_Template", "ɵɵproperty", "ɵɵpureFunction5", "_c0", "type", "name", "location_r20", "SearchPriceComponent_mat_option_58_span_8_Template", "SearchPriceComponent_mat_option_58_span_9_Template", "SearchPriceComponent_mat_option_58_span_10_Template", "SearchPriceComponent_div_67_div_6_Template", "ctx_r7", "minReturnDate", "tmp_1_0", "searchForm", "get", "invalid", "touched", "ɵɵlistener", "SearchPriceComponent_div_97_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r30", "flightClass_r28", "$implicit", "ctx_r29", "ɵɵnextContext", "ɵɵresetView", "selectFlightClass", "value", "ɵɵclassProp", "tmp_0_0", "ctx_r8", "ɵɵtextInterpolate1", "label", "SearchPriceComponent_div_156_div_2_Template_button_click_8_listener", "_r35", "ctx_r34", "onSearch", "ctx_r32", "errorMessage", "ɵɵtextInterpolate2", "ctx_r36", "displayLocation", "ctx_r37", "formatDateFull", "ctx_r44", "SearchPriceComponent_div_156_div_3_p_7_span_4_Template", "ctx_r39", "searchResults", "length", "getFlightClassName", "tmp_2_0", "SearchPriceComponent_div_156_div_3_div_8_Template_button_click_2_listener", "_r48", "ctx_r47", "toggleFiltersPanel", "SearchPriceComponent_div_156_div_3_div_8_Template_select_change_9_listener", "$event", "ctx_r49", "sortResults", "SearchPriceComponent_div_156_div_3_div_8_Template_div_click_19_listener", "ctx_r50", "toggleDirectFlightsOnly", "SearchPriceComponent_div_156_div_3_div_8_i_21_Template", "SearchPriceComponent_div_156_div_3_div_8_Template_div_click_24_listener", "ctx_r51", "toggleBaggageIncluded", "SearchPriceComponent_div_156_div_3_div_8_i_26_Template", "ctx_r40", "directFlightsOnly", "baggageIncluded", "airline_r52", "flight_r53", "items", "airline", "thumbnailFull", "ɵɵsanitizeUrl", "flightClass", "stopCount", "ctx_r61", "getBaggageSummary", "baggageInformations", "offers", "seatInfo", "availableSeatCount", "aircraft", "SearchPriceComponent_div_156_div_3_div_57_div_70_div_4_img_3_Template", "SearchPriceComponent_div_156_div_3_div_57_div_70_div_4_div_8_Template", "flightNo", "ctx_r74", "formatTime", "departure", "date", "formatDate", "airport", "formatDuration", "duration", "arrival", "segment_r82", "ctx_r86", "calculateLayoverTime", "segments", "i_r83", "SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_img_5_Template", "SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_div_10_Template", "SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_div_42_Template", "ctx_r81", "SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_div_1_Template", "SearchPriceComponent_div_156_div_3_div_57_div_70_div_4_Template", "SearchPriceComponent_div_156_div_3_div_57_div_70_div_5_Template", "ctx_r96", "getPassengerTypeName", "item_r99", "passengerType", "passengerCount", "ɵɵpipeBind2", "price", "amount", "currency", "serviceFee", "priceBreakDown", "airportTax", "SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_div_1_Template", "SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_div_2_Template", "SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_div_3_Template", "feature_r104", "explanations", "text", "SearchPriceComponent_div_156_div_3_div_57_div_71_div_5_div_4_div_6_Template", "_c1", "serviceGroup", "commercialName", "SearchPriceComponent_div_156_div_3_div_57_div_71_div_5_div_4_Template", "flightBrandInfo", "features", "SearchPriceComponent_div_156_div_3_div_57_div_71_div_4_Template", "SearchPriceComponent_div_156_div_3_div_57_div_71_div_5_Template", "baggage_r112", "weight", "unitType", "piece", "SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_div_1_span_7_Template", "SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_div_1_span_8_Template", "ɵɵpureFunction2", "_c2", "baggageType", "ctx_r111", "getBaggageTypeName", "SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_div_1_Template", "SearchPriceComponent_div_156_div_3_div_57_div_72_div_4_Template", "SearchPriceComponent_div_156_div_3_div_57_div_72_div_5_Template", "SearchPriceComponent_div_156_div_3_div_57_img_4_Template", "SearchPriceComponent_div_156_div_3_div_57_i_5_Template", "SearchPriceComponent_div_156_div_3_div_57_span_11_Template", "SearchPriceComponent_div_156_div_3_div_57_span_12_Template", "SearchPriceComponent_div_156_div_3_div_57_span_13_Template", "SearchPriceComponent_div_156_div_3_div_57_span_40_Template", "SearchPriceComponent_div_156_div_3_div_57_span_41_Template", "SearchPriceComponent_div_156_div_3_div_57_div_53_Template", "SearchPriceComponent_div_156_div_3_div_57_div_54_Template", "SearchPriceComponent_div_156_div_3_div_57_div_55_Template", "SearchPriceComponent_div_156_div_3_div_57_Template_button_click_57_listener", "_r120", "ctx_r119", "toggleFlightDetails", "SearchPriceComponent_div_156_div_3_div_57_span_58_Template", "SearchPriceComponent_div_156_div_3_div_57_span_59_Template", "SearchPriceComponent_div_156_div_3_div_57_Template_div_click_63_listener", "ctx_r121", "setActiveTab", "SearchPriceComponent_div_156_div_3_div_57_Template_div_click_65_listener", "ctx_r122", "SearchPriceComponent_div_156_div_3_div_57_Template_div_click_67_listener", "ctx_r123", "SearchPriceComponent_div_156_div_3_div_57_div_70_Template", "SearchPriceComponent_div_156_div_3_div_57_div_71_Template", "SearchPriceComponent_div_156_div_3_div_57_div_72_Template", "SearchPriceComponent_div_156_div_3_div_57_Template_button_click_74_listener", "ctx_r124", "selectThisFlight", "ctx_r42", "isFlightAvailable", "<PERSON><PERSON><PERSON>", "getMinPrice", "services", "isFlightDetailsOpen", "getActiveTab", "SearchPriceComponent_div_156_div_3_div_58_Template_button_click_24_listener", "_r126", "ctx_r125", "scrollToSearchForm", "SearchPriceComponent_div_156_div_3_span_4_Template", "SearchPriceComponent_div_156_div_3_span_5_Template", "SearchPriceComponent_div_156_div_3_p_6_Template", "SearchPriceComponent_div_156_div_3_p_7_Template", "SearchPriceComponent_div_156_div_3_div_8_Template", "SearchPriceComponent_div_156_div_3_Template_button_click_13_listener", "_r128", "ctx_r127", "SearchPriceComponent_div_156_div_3_div_29_Template", "SearchPriceComponent_div_156_div_3_Template_button_click_54_listener", "ctx_r129", "SearchPriceComponent_div_156_div_3_div_57_Template", "SearchPriceComponent_div_156_div_3_div_58_Template", "ctx_r33", "showFiltersPanel", "getUniqueAirlines", "isLoading", "SearchPriceComponent_div_156_div_1_Template", "SearchPriceComponent_div_156_div_2_Template", "SearchPriceComponent_div_156_div_3_Template", "ctx_r12", "SearchPriceComponent", "constructor", "fb", "productService", "router", "departureLocations", "arrivalLocations", "hasSearched", "lastSearchId", "tripTypes", "selectedTripType", "passengerTypes", "Adult", "Child", "Infant", "flightClasses", "PROMO", "ECONOMY", "BUSINESS", "showTravelersDropdown", "flightDetailsMap", "Map", "priceRange", "min", "max", "filteredPriceRange", "availableAirlines", "selectedAirlines", "minDate", "Date", "toISOString", "split", "group", "productType", "required", "serviceTypes", "departureLocation", "departureLocationType", "arrivalLocation", "arrivalLocationType", "departureDate", "returnDate", "addDays", "nonStop", "culture", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightBaggageGetOption", "valueChanges", "subscribe", "currentReturnDate", "setValue", "ngOnInit", "setupAutocomplete", "preloadLocations", "console", "log", "showAllDetails", "flight", "modalDiv", "document", "createElement", "style", "position", "top", "left", "width", "height", "backgroundColor", "zIndex", "display", "justifyContent", "alignItems", "modalContent", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "color", "fontFamily", "closeButton", "innerHTML", "right", "border", "background", "fontSize", "cursor", "transition", "on<PERSON><PERSON>ver", "onmouseout", "onclick", "body", "<PERSON><PERSON><PERSON><PERSON>", "header", "marginBottom", "paddingBottom", "borderBottom", "logo", "title", "textContent", "margin", "fontWeight", "append<PERSON><PERSON><PERSON>", "detailsContainer", "flexDirection", "gap", "generalInfo", "createSection", "item", "airlineInfo", "airlineLogo", "src", "alt", "marginRight", "airlineIcon", "airlineName", "internationalCode", "flightNumberRow", "createInfoRow", "flightDateRow", "flightDate", "toLocaleDateString", "durationRow", "classRow", "stopsRow", "routeSection", "routeVisual", "textAlign", "flex", "departureTime", "toLocaleTimeString", "hour", "minute", "departureAirport", "marginTop", "departureCity", "connectionLine", "line", "plane", "marginLeft", "arrivalTime", "arrivalAirport", "arrivalCity", "segmentsTitle", "segmentsList", "for<PERSON>ach", "segment", "index", "segmentItem", "segmentHeader", "segmentTitle", "segmentDuration", "segmentRoute", "segmentDeparture", "depTime", "depAirport", "arrow", "segmentArrival", "arrTime", "arrAirport", "layover", "currentArrival", "getTime", "nextDeparture", "layoverTime", "Math", "floor", "offersSection", "offersList", "offer", "offerItem", "offerHeader", "offerTitle", "offerPrice", "offerDetails", "gridTemplateColumns", "offerId", "id", "gridColumn", "availabilityValue", "availability", "undefined", "expiresOn", "expires", "toLocaleString", "brandedFare", "reservableInfo", "reservable", "baggageTitle", "baggageList", "listStyle", "baggage", "baggageItem", "servicesSection", "servicesList", "service", "serviceItem", "iconClass", "section", "section<PERSON><PERSON><PERSON>", "icon", "className", "sectionTitle", "row", "labelElement", "valueElement", "searchId", "navigate", "queryParams", "error", "getLocationsByType", "locations", "locationType", "pipe", "filter", "location", "toLowerCase", "includes", "displayText", "Airport", "markFormGroupTouched", "formValue", "request", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "ArrivalLocations", "Passengers", "count", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "searchPrice", "next", "response", "success", "flights", "JSON", "stringify", "flightsWithOffers", "f", "availabilityValues", "flatMap", "o", "availabilityCounts", "reduce", "acc", "val", "reservableFlights", "some", "groupEnd", "requestId", "Object", "keys", "messages", "message", "formGroup", "values", "controls", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "minutes", "hours", "mins", "dateString", "day", "month", "weekday", "year", "hour12", "min<PERSON>ffer", "showAllDepartureLocations", "getAllLocations", "showAllArrivalLocations", "swapLocations", "departureValue", "departureTypeValue", "arrivalValue", "arrivalTypeValue", "formatExpirationDate", "currentSegment", "nextSegment", "layoverMinutes", "toggleTravelersDropdown", "incrementPassengers", "currentValue", "decrement<PERSON><PERSON><PERSON><PERSON>", "find", "fc", "applyFilters", "flightId", "has", "set", "isOpen", "activeTab", "details", "tab", "querySelector", "scrollIntoView", "behavior", "setTripType", "tripType", "clearValidators", "updateValueAndValidity", "setValidators", "days", "result", "setDate", "getDate", "filterByPrice", "event", "sortBy", "target", "sort", "a", "b", "priceA", "priceB", "durationA", "durationB", "dateA", "dateB", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProductService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "SearchPriceComponent_Template", "rf", "ctx", "SearchPriceComponent_Template_form_ngSubmit_22_listener", "SearchPriceComponent_Template_div_click_24_listener", "SearchPriceComponent_Template_div_click_27_listener", "SearchPriceComponent_Template_div_click_30_listener", "SearchPriceComponent_Template_input_click_42_listener", "SearchPriceComponent_mat_option_45_Template", "SearchPriceComponent_div_46_Template", "SearchPriceComponent_Template_button_click_48_listener", "SearchPriceComponent_Template_input_click_55_listener", "SearchPriceComponent_mat_option_58_Template", "SearchPriceComponent_div_59_Template", "SearchPriceComponent_div_66_Template", "SearchPriceComponent_div_67_Template", "SearchPriceComponent_Template_div_click_71_listener", "SearchPriceComponent_Template_button_click_87_listener", "SearchPriceComponent_Template_button_click_91_listener", "SearchPriceComponent_div_97_Template", "SearchPriceComponent_Template_button_click_99_listener", "SearchPriceComponent_i_103_Template", "SearchPriceComponent_span_104_Template", "SearchPriceComponent_div_105_Template", "SearchPriceComponent_div_156_Template", "_r0", "bind", "tmp_7_0", "_r3", "tmp_11_0", "tmp_13_0", "tmp_15_0", "tmp_16_0", "tmp_18_0", "tmp_19_0", "tmp_20_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { Observable, of } from 'rxjs';\nimport { ProductService } from '../../../services/product.service';\nimport { LocationOption } from '../../../models/price-search-request.model';\nimport { PriceSearchRequest } from '../../../models/price-search-request.model';\nimport { PriceSearchResponse, Flight } from '../../../models/price-search-response.model';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css'],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchPriceComponent implements OnInit {\n  searchForm: FormGroup;\n  departureLocations: LocationOption[] = [];\n  arrivalLocations: LocationOption[] = [];\n  isLoading = false;\n  searchResults: Flight[] = [];\n  hasSearched = false;\n  errorMessage = '';\n  lastSearchId = '';\n\n  // Trip types\n  tripTypes = [\n    { value: 'roundTrip', label: 'Round Trip' },\n    { value: 'oneWay', label: 'One Way' },\n    { value: 'multiCity', label: 'Multi-City' }\n  ];\n  selectedTripType = 'roundTrip';\n\n  // Passenger type options\n  passengerTypes = [\n    { value: PassengerType.Adult, label: 'Adult' },\n    { value: PassengerType.Child, label: 'Child' },\n    { value: PassengerType.Infant, label: 'Infant' }\n  ];\n\n  // Flight class options\n  flightClasses = [\n    { value: FlightClassType.PROMO, label: 'Promo' },\n    { value: FlightClassType.ECONOMY, label: 'Economy' },\n    { value: FlightClassType.BUSINESS, label: 'Business' }\n  ];\n\n  // Date minimale (aujourd'hui)\n  minDate: string;\n  minReturnDate: string;\n\n  // Nouvelles propriétés pour les fonctionnalités avancées\n  showTravelersDropdown = false;\n  showFiltersPanel = false;\n  directFlightsOnly = false;\n  baggageIncluded = false;\n  flightDetailsMap = new Map<string, { isOpen: boolean, activeTab: string }>();\n  \n  // Price range filter\n  priceRange = { min: 0, max: 5000 };\n  filteredPriceRange = { min: 0, max: 5000 };\n  \n  // Airlines filter\n  availableAirlines: string[] = [];\n  selectedAirlines: string[] = [];\n\n  constructor(\n    private fb: FormBuilder,\n    private productService: ProductService,\n    private router: Router\n  ) {\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    this.minReturnDate = this.minDate;\n\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required], // Flight par défaut\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required], // Type 2 (City) par défaut\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required], // Type 5 (Airport) par défaut\n      departureDate: [this.minDate, Validators.required],\n      returnDate: [this.addDays(this.minDate, 7)], // Default return date is 7 days after departure\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required], // Adult par défaut\n\n      // Options de vol\n      flightClass: [0, Validators.required], // PROMO par défaut\n      nonStop: [false],\n\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n\n    // Listen for departure date changes to update minimum return date\n    this.searchForm.get('departureDate')?.valueChanges.subscribe(date => {\n      this.minReturnDate = date;\n      const currentReturnDate = this.searchForm.get('returnDate')?.value;\n      if (currentReturnDate && new Date(currentReturnDate) < new Date(date)) {\n        this.searchForm.get('returnDate')?.setValue(date);\n      }\n    });\n  }\n\n  ngOnInit(): void {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight: Flight): void {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n\n    header.appendChild(logo);\n    header.appendChild(title);\n\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n\n        generalInfo.appendChild(airlineInfo);\n      }\n\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n\n      routeSection.appendChild(routeVisual);\n\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n\n          segmentsList.appendChild(segmentItem);\n\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n\n            segmentsList.appendChild(layover);\n          }\n        });\n\n        routeSection.appendChild(segmentsList);\n      }\n    }\n\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n\n        // ID de l'offre\n        const offerId = this.createInfoRow('Offer ID', offer.offerId || offer.id || 'N/A');\n        offerId.style.gridColumn = '1 / -1';\n        offerDetails.appendChild(offerId);\n\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability :\n                                 (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n\n        offerItem.appendChild(offerDetails);\n\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n\n          const baggageList = document.createElement('ul');\n          baggageList.style.listStyle = 'none';\n          baggageList.style.padding = '0';\n          baggageList.style.margin = '0';\n\n          offer.baggageInformations.forEach(baggage => {\n            const baggageItem = document.createElement('li');\n            baggageItem.style.marginBottom = '5px';\n            baggageItem.innerHTML = `<i class=\"fas fa-suitcase\" style=\"color: #666; margin-right: 8px;\"></i> ${this.getBaggageTypeName(baggage.baggageType)}`;\n            baggageList.appendChild(baggageItem);\n          });\n\n          offerItem.appendChild(baggageList);\n        }\n\n        offersList.appendChild(offerItem);\n      });\n\n      offersSection.appendChild(offersList);\n    }\n\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n\n  // Méthode utilitaire pour créer une section\n  private createSection(title: string, iconClass: string): HTMLElement {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n\n    return section;\n  }\n\n  // Méthode utilitaire pour créer une ligne d'information\n  private createInfoRow(label: string, value: string): HTMLElement {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n\n    return row;\n  }\n\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight: Flight): void {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n\n  preloadLocations(): void {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n\n  setupAutocomplete(): void {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.departureLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('departureLocation')?.setValue('');\n        });\n      });\n\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.arrivalLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('arrivalLocation')?.setValue('');\n        });\n      });\n\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.departureLocations = locations;\n      });\n\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n  }\n\n  displayLocation(location: LocationOption | null): string {\n    if (!location) return '';\n\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n\n  onSearch(): void {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n\n    const formValue = this.searchForm.value;\n\n    // Créer la requête de recherche entièrement dynamique\n    const request: PriceSearchRequest = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [\n        {\n          id: formValue.departureLocation?.id || '',\n          type: formValue.departureLocationType\n        }\n      ],\n      ArrivalLocations: [\n        {\n          id: formValue.arrivalLocation?.id || '',\n          type: formValue.arrivalLocationType\n        }\n      ],\n      Passengers: [\n        {\n          type: formValue.passengerType,\n          count: formValue.passengerCount\n        }\n      ],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n\n    this.productService.searchPrice(request)\n      .subscribe({\n        next: (response: PriceSearchResponse) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            console.log('Full API Response:', JSON.stringify(response, null, 2));\n\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o =>\n                o.availability !== undefined ? o.availability : (o.seatInfo ? o.seatInfo.availableSeatCount : 0)\n              ));\n              console.log('Availability values:', availabilityValues);\n\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc, val) => {\n                if (val !== undefined) {\n                  acc[val] = (acc[val] || 0) + 1;\n                }\n                return acc;\n              }, {} as Record<number, number>);\n              console.log('Availability counts:', availabilityCounts);\n\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f =>\n                f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true)\n              );\n              console.log('Reservable flights:', reservableFlights.length);\n\n              console.groupEnd();\n            }\n\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n  }\n\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup: FormGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n\n  // Formater la durée en heures et minutes\n  formatDuration(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}m`;\n  }\n\n  formatDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', { day: 'numeric', month: 'short' });\n  }\n\n  formatDateFull(dateString: string): string {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' });\n  }\n\n  formatTime(dateString: string): string {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true });\n  }\n\n  getMinPrice(flight: Flight): string {\n    if (!flight.offers || flight.offers.length === 0) return 'N/A';\n    \n    // Trouver l'offre avec le prix minimum\n    const minOffer = flight.offers.reduce((min, offer) => \n      (offer.price && offer.price.amount < min.price.amount) ? offer : min, flight.offers[0]);\n    \n    return `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n\n  isFlightAvailable(flight: Flight): boolean {\n    if (!flight.offers || flight.offers.length === 0) return false;\n    \n    // Vérifier si au moins une offre est disponible\n    return flight.offers.some(offer => {\n      // Vérifier la disponibilité basée sur seatInfo si disponible\n      if (offer.seatInfo && offer.seatInfo.availableSeatCount !== undefined) {\n        return offer.seatInfo.availableSeatCount > 0;\n      }\n      \n      // Sinon, vérifier la disponibilité basée sur le champ availability\n      if (offer.availability !== undefined) {\n        return offer.availability === 1; // 1 = Available dans l'API\n      }\n      \n      // Par défaut, considérer comme disponible\n      return true;\n    });\n  }\n\n  showAllDepartureLocations(): void {\n    this.productService.getAllLocations().subscribe(locations => {\n      this.departureLocations = locations;\n    });\n  }\n\n  showAllArrivalLocations(): void {\n    this.productService.getAllLocations().subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n\n  swapLocations(): void {\n    const departureValue = this.searchForm.get('departureLocation')?.value;\n    const departureTypeValue = this.searchForm.get('departureLocationType')?.value;\n    const arrivalValue = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalTypeValue = this.searchForm.get('arrivalLocationType')?.value;\n    \n    this.searchForm.get('departureLocation')?.setValue(arrivalValue);\n    this.searchForm.get('departureLocationType')?.setValue(arrivalTypeValue);\n    this.searchForm.get('arrivalLocation')?.setValue(departureValue);\n    this.searchForm.get('arrivalLocationType')?.setValue(departureTypeValue);\n  }\n\n  formatExpirationDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n\n  getBaggageTypeName(baggageType: number): string {\n    switch (baggageType) {\n      case 1: return 'Checked Baggage';\n      case 2: return 'Cabin Baggage';\n      default: return 'Unknown';\n    }\n  }\n\n  getPassengerTypeName(passengerType: number): string {\n    switch (passengerType) {\n      case 1: return 'Adult';\n      case 2: return 'Child';\n      case 3: return 'Infant';\n      default: return 'Unknown';\n    }\n  }\n\n  calculateLayoverTime(currentSegment: any, nextSegment: any): string {\n    if (!currentSegment || !nextSegment || !currentSegment.arrival || !nextSegment.departure) {\n      return 'N/A';\n    }\n    \n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const layoverMinutes = Math.floor((departureTime - arrivalTime) / (1000 * 60));\n\n    return this.formatDuration(layoverMinutes);\n  }\n\n  toggleTravelersDropdown(): void {\n    this.showTravelersDropdown = !this.showTravelersDropdown;\n  }\n\n  incrementPassengers(): void {\n    const currentValue = this.searchForm.get('passengerCount')?.value;\n    if (currentValue < 9) {\n      this.searchForm.get('passengerCount')?.setValue(currentValue + 1);\n    }\n  }\n\n  decrementPassengers(): void {\n    const currentValue = this.searchForm.get('passengerCount')?.value;\n    if (currentValue > 1) {\n      this.searchForm.get('passengerCount')?.setValue(currentValue - 1);\n    }\n  }\n\n  selectFlightClass(value: number): void {\n    this.searchForm.get('flightClass')?.setValue(value);\n  }\n\n  getFlightClassName(value: number): string {\n    const flightClass = this.flightClasses.find(fc => fc.value === value);\n    return flightClass ? flightClass.label : 'Unknown';\n  }\n\n  toggleFiltersPanel(): void {\n    this.showFiltersPanel = !this.showFiltersPanel;\n  }\n\n  toggleDirectFlightsOnly(): void {\n    this.directFlightsOnly = !this.directFlightsOnly;\n    this.applyFilters();\n  }\n\n  toggleBaggageIncluded(): void {\n    this.baggageIncluded = !this.baggageIncluded;\n    this.applyFilters();\n  }\n\n  applyFilters(): void {\n    if (!this.hasSearched || this.searchResults.length === 0) return;\n\n    // Apply price range filter\n    if (this.filteredPriceRange.min > 0 || this.filteredPriceRange.max < 5000) {\n      // Filter by price logic would go here\n      // This would typically filter the searchResults array based on price\n      console.log(`Filtering by price range: ${this.filteredPriceRange.min} - ${this.filteredPriceRange.max}`);\n    }\n\n    // Apply direct flights filter\n    if (this.directFlightsOnly) {\n      // Filter for direct flights\n      console.log('Filtering for direct flights only');\n    }\n\n    // Apply baggage included filter\n    if (this.baggageIncluded) {\n      // Filter for flights with baggage included\n      console.log('Filtering for flights with baggage included');\n    }\n  }\n\n  toggleFlightDetails(flight: Flight): void {\n    const flightId = flight.id || '';\n    if (!this.flightDetailsMap.has(flightId)) {\n      this.flightDetailsMap.set(flightId, { isOpen: true, activeTab: 'flight' });\n    } else {\n      const details = this.flightDetailsMap.get(flightId);\n      if (details) {\n        details.isOpen = !details.isOpen;\n        this.flightDetailsMap.set(flightId, details);\n      }\n    }\n  }\n\n  isFlightDetailsOpen(flight: Flight): boolean {\n    const flightId = flight.id || '';\n    return this.flightDetailsMap.has(flightId) && this.flightDetailsMap.get(flightId)?.isOpen === true;\n  }\n\n  setActiveTab(flight: Flight, tab: string): void {\n    const flightId = flight.id || '';\n    if (this.flightDetailsMap.has(flightId)) {\n      const details = this.flightDetailsMap.get(flightId);\n      if (details) {\n        details.activeTab = tab;\n        this.flightDetailsMap.set(flightId, details);\n      }\n    } else {\n      this.flightDetailsMap.set(flightId, { isOpen: true, activeTab: tab });\n    }\n  }\n\n  getActiveTab(flight: Flight): string {\n    const flightId = flight.id || '';\n    return this.flightDetailsMap.has(flightId) ? this.flightDetailsMap.get(flightId)?.activeTab || 'flight' : 'flight';\n  }\n\n  scrollToSearchForm(): void {\n    const searchForm = document.querySelector('.search-form-container');\n    if (searchForm) {\n      searchForm.scrollIntoView({ behavior: 'smooth' });\n    }\n  }\n\n  setTripType(tripType: string): void {\n    this.selectedTripType = tripType;\n    \n    if (tripType === 'oneWay') {\n      this.searchForm.get('returnDate')?.clearValidators();\n      this.searchForm.get('returnDate')?.updateValueAndValidity();\n    } else if (tripType === 'roundTrip') {\n      this.searchForm.get('returnDate')?.setValidators([Validators.required]);\n      this.searchForm.get('returnDate')?.updateValueAndValidity();\n    }\n  }\n  \n  addDays(date: string, days: number): string {\n    const result = new Date(date);\n    result.setDate(result.getDate() + days);\n    return result.toISOString().split('T')[0];\n  }\n  \n  filterByPrice(): void {\n    this.filteredPriceRange = { ...this.priceRange };\n    this.applyFilters();\n  }\n  \n  sortResults(event: any): void {\n    const sortBy = event.target.value;\n    console.log('Sorting results by:', sortBy);\n    \n    // Implémenter la logique de tri\n    switch (sortBy) {\n      case 'price':\n        this.searchResults.sort((a, b) => {\n          const priceA = a.offers && a.offers[0] ? a.offers[0].price.amount : 0;\n          const priceB = b.offers && b.offers[0] ? b.offers[0].price.amount : 0;\n          return priceA - priceB;\n        });\n        break;\n      case 'duration':\n        this.searchResults.sort((a, b) => {\n          const durationA = a.items && a.items[0] ? a.items[0].duration : 0;\n          const durationB = b.items && b.items[0] ? b.items[0].duration : 0;\n          return durationA - durationB;\n        });\n        break;\n      case 'departure':\n        this.searchResults.sort((a, b) => {\n          const dateA = a.items && a.items[0] && a.items[0].departure ? new Date(a.items[0].departure.date).getTime() : 0;\n          const dateB = b.items && b.items[0] && b.items[0].departure ? new Date(b.items[0].departure.date).getTime() : 0;\n          return dateA - dateB;\n        });\n        break;\n      case 'arrival':\n        this.searchResults.sort((a, b) => {\n          const dateA = a.items && a.items[0] && a.items[0].arrival ? new Date(a.items[0].arrival.date).getTime() : 0;\n          const dateB = b.items && b.items[0] && b.items[0].arrival ? new Date(b.items[0].arrival.date).getTime() : 0;\n          return dateA - dateB;\n        });\n        break;\n    }\n  }\n}\n", "<div class=\"search-price-container\">\n  <!-- Hero Section with Background Image -->\n  <div class=\"hero-section\">\n    <div class=\"hero-content\">\n      <h1 class=\"hero-title\">Discover Your Next Adventure</h1>\n      <p class=\"hero-subtitle\">Find the best flights to destinations worldwide</p>\n    </div>\n  </div>\n\n  <div class=\"search-content\">\n    <!-- Search Form with Modern Design -->\n    <div class=\"search-form-container\">\n      <div class=\"search-tabs\">\n        <div class=\"tab active\">\n          <i class=\"fas fa-plane\"></i> Flights\n        </div>\n        <div class=\"tab disabled\">\n          <i class=\"fas fa-hotel\"></i> Hotels\n        </div>\n        <div class=\"tab disabled\">\n          <i class=\"fas fa-car\"></i> Cars\n        </div>\n        <div class=\"tab disabled\">\n          <i class=\"fas fa-suitcase\"></i> Packages\n        </div>\n      </div>\n\n      <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSearch()\" class=\"search-form\">\n        <!-- Trip Type Selection -->\n        <div class=\"trip-type-selector\">\n          <div class=\"trip-type\" [class.active]=\"selectedTripType === 'roundTrip'\" (click)=\"setTripType('roundTrip')\">\n            <i class=\"fas fa-exchange-alt\"></i> Round Trip\n          </div>\n          <div class=\"trip-type\" [class.active]=\"selectedTripType === 'oneWay'\" (click)=\"setTripType('oneWay')\">\n            <i class=\"fas fa-long-arrow-alt-right\"></i> One Way\n          </div>\n          <div class=\"trip-type\" [class.active]=\"selectedTripType === 'multiCity'\" (click)=\"setTripType('multiCity')\">\n            <i class=\"fas fa-route\"></i> Multi-City\n          </div>\n        </div>\n\n        <!-- Main Search Card -->\n        <div class=\"search-card\">\n          <!-- Hidden fields -->\n          <input type=\"hidden\" formControlName=\"productType\" value=\"3\">\n          <input type=\"hidden\" formControlName=\"serviceTypes\" value=\"['1']\">\n\n          <div class=\"search-grid\">\n            <!-- Departure Location -->\n            <div class=\"form-group location-group\">\n              <label for=\"departureLocation\">From</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-plane-departure\"></i>\n                <input\n                  type=\"text\"\n                  id=\"departureLocation\"\n                  formControlName=\"departureLocation\"\n                  placeholder=\"City or airport\"\n                  [matAutocomplete]=\"departureAuto\"\n                  class=\"form-control\"\n                  (click)=\"showAllDepartureLocations()\"\n                >\n              </div>\n              <mat-autocomplete #departureAuto=\"matAutocomplete\" [displayWith]=\"displayLocation.bind(this)\">\n                <mat-option *ngFor=\"let location of departureLocations\" [value]=\"location\">\n                  <div class=\"location-option\">\n                    <div class=\"location-icon\">\n                      <i class=\"fas\" [ngClass]=\"{\n                        'fa-flag': location.type === 1,\n                        'fa-city': location.type === 2,\n                        'fa-building': location.type === 3,\n                        'fa-home': location.type === 4,\n                        'fa-plane': location.type === 5\n                      }\"></i>\n                    </div>\n                    <div class=\"location-details\">\n                      <div class=\"location-name\">{{ location.name }}</div>\n                      <div class=\"location-info\">\n                        <span *ngIf=\"location.code\" class=\"location-code\">{{ location.code }}</span>\n                        <span *ngIf=\"location.city\" class=\"location-city\">{{ location.city }}</span>\n                        <span *ngIf=\"location.country\" class=\"location-country\">{{ location.country }}</span>\n                      </div>\n                    </div>\n                  </div>\n                </mat-option>\n              </mat-autocomplete>\n              <div *ngIf=\"searchForm.get('departureLocation')?.invalid && searchForm.get('departureLocation')?.touched\" class=\"error-message\">\n                <i class=\"fas fa-exclamation-circle\"></i> Please select a departure location\n              </div>\n            </div>\n\n            <!-- Swap Button -->\n            <div class=\"swap-button-container\">\n              <button type=\"button\" class=\"swap-locations-btn\" (click)=\"swapLocations()\">\n                <i class=\"fas fa-exchange-alt\"></i>\n              </button>\n            </div>\n\n            <!-- Arrival Location -->\n            <div class=\"form-group location-group\">\n              <label for=\"arrivalLocation\">To</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-plane-arrival\"></i>\n                <input\n                  type=\"text\"\n                  id=\"arrivalLocation\"\n                  formControlName=\"arrivalLocation\"\n                  placeholder=\"City or airport\"\n                  [matAutocomplete]=\"arrivalAuto\"\n                  class=\"form-control\"\n                  (click)=\"showAllArrivalLocations()\"\n                >\n              </div>\n              <mat-autocomplete #arrivalAuto=\"matAutocomplete\" [displayWith]=\"displayLocation.bind(this)\">\n                <mat-option *ngFor=\"let location of arrivalLocations\" [value]=\"location\">\n                  <div class=\"location-option\">\n                    <div class=\"location-icon\">\n                      <i class=\"fas\" [ngClass]=\"{\n                        'fa-flag': location.type === 1,\n                        'fa-city': location.type === 2,\n                        'fa-building': location.type === 3,\n                        'fa-home': location.type === 4,\n                        'fa-plane': location.type === 5\n                      }\"></i>\n                    </div>\n                    <div class=\"location-details\">\n                      <div class=\"location-name\">{{ location.name }}</div>\n                      <div class=\"location-info\">\n                        <span *ngIf=\"location.code\" class=\"location-code\">{{ location.code }}</span>\n                        <span *ngIf=\"location.city\" class=\"location-city\">{{ location.city }}</span>\n                        <span *ngIf=\"location.country\" class=\"location-country\">{{ location.country }}</span>\n                      </div>\n                    </div>\n                  </div>\n                </mat-option>\n              </mat-autocomplete>\n              <div *ngIf=\"searchForm.get('arrivalLocation')?.invalid && searchForm.get('arrivalLocation')?.touched\" class=\"error-message\">\n                <i class=\"fas fa-exclamation-circle\"></i> Please select an arrival location\n              </div>\n            </div>\n\n            <!-- Departure Date -->\n            <div class=\"form-group date-group\">\n              <label for=\"departureDate\">Departure Date</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-calendar-alt\"></i>\n                <input\n                  type=\"date\"\n                  id=\"departureDate\"\n                  formControlName=\"departureDate\"\n                  [min]=\"minDate\"\n                  class=\"form-control\"\n                >\n              </div>\n              <div class=\"error-message\" *ngIf=\"searchForm.get('departureDate')?.invalid && searchForm.get('departureDate')?.touched\">\n                Please select a departure date\n              </div>\n            </div>\n\n            <!-- Return Date (only for round trips) -->\n            <div class=\"form-group date-group\" *ngIf=\"selectedTripType === 'roundTrip'\">\n              <label for=\"returnDate\">Return Date</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-calendar-alt\"></i>\n                <input\n                  type=\"date\"\n                  id=\"returnDate\"\n                  formControlName=\"returnDate\"\n                  [min]=\"minReturnDate\"\n                  class=\"form-control\"\n                >\n              </div>\n              <div class=\"error-message\" *ngIf=\"searchForm.get('returnDate')?.invalid && searchForm.get('returnDate')?.touched\">\n                Please select a return date\n              </div>\n            </div>\n\n            <!-- Travelers and Class -->\n            <div class=\"form-group travelers-group\">\n              <label>Travelers & Class</label>\n              <div class=\"input-with-icon dropdown-toggle\" (click)=\"toggleTravelersDropdown()\">\n                <i class=\"fas fa-users\"></i>\n                <div class=\"travelers-summary\">\n                  <span>{{ searchForm.get('passengerCount')?.value }} Traveler(s)</span>\n                  <span>{{ getFlightClassName(searchForm.get('flightClass')?.value) }}</span>\n                </div>\n                <i class=\"fas fa-chevron-down\"></i>\n              </div>\n              \n              <!-- Dropdown for travelers and class -->\n              <div class=\"travelers-dropdown\" [class.show]=\"showTravelersDropdown\">\n                <div class=\"dropdown-section\">\n                  <h4>Passengers</h4>\n                  <div class=\"passenger-counter\">\n                    <span>Adults</span>\n                    <div class=\"counter-controls\">\n                      <button type=\"button\" (click)=\"decrementPassengers()\" [disabled]=\"searchForm.get('passengerCount')?.value <= 1\">-</button>\n                      <span>{{ searchForm.get('passengerCount')?.value }}</span>\n                      <button type=\"button\" (click)=\"incrementPassengers()\" [disabled]=\"searchForm.get('passengerCount')?.value >= 9\">+</button>\n                    </div>\n                  </div>\n                </div>\n                \n                <div class=\"dropdown-section\">\n                  <h4>Cabin Class</h4>\n                  <div class=\"cabin-options\">\n                    <div *ngFor=\"let flightClass of flightClasses\" \n                         class=\"cabin-option\" \n                         [class.selected]=\"searchForm.get('flightClass')?.value === flightClass.value\"\n                         (click)=\"selectFlightClass(flightClass.value)\">\n                      {{ flightClass.label }}\n                    </div>\n                  </div>\n                </div>\n                \n                <div class=\"dropdown-actions\">\n                  <button type=\"button\" class=\"apply-btn\" (click)=\"toggleTravelersDropdown()\">Apply</button>\n                </div>\n              </div>\n            </div>\n\n            <!-- Search Button -->\n            <div class=\"search-button-container\">\n              <button\n                type=\"submit\"\n                class=\"search-button\"\n                [disabled]=\"searchForm.invalid || isLoading\"\n              >\n                <i class=\"fas fa-search\" *ngIf=\"!isLoading\"></i>\n                <span *ngIf=\"!isLoading\">Search Flights</span>\n                <div *ngIf=\"isLoading\" class=\"spinner-container\">\n                  <div class=\"spinner\"></div>\n                </div>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Advanced Options (Collapsible) -->\n        <div class=\"advanced-options-container\">\n          <details>\n            <summary>\n              <i class=\"fas fa-sliders-h\"></i> More Options\n            </summary>\n            <div class=\"advanced-options\">\n              <div class=\"options-grid\">\n                <!-- Direct Flights Only -->\n                <div class=\"form-group toggle-group\">\n                  <div class=\"toggle-switch\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"nonStop\"\n                      formControlName=\"nonStop\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"nonStop\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Direct flights only</span>\n                    </label>\n                  </div>\n                </div>\n                \n                <!-- Currency -->\n                <div class=\"form-group\">\n                  <label for=\"currency\">Currency</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-money-bill-wave\"></i>\n                    <select id=\"currency\" formControlName=\"currency\" class=\"form-control\">\n                      <option value=\"EUR\">Euro (€)</option>\n                      <option value=\"USD\">Dollar ($)</option>\n                      <option value=\"GBP\">Pound (£)</option>\n                    </select>\n                  </div>\n                </div>\n                \n                <!-- Language -->\n                <div class=\"form-group\">\n                  <label for=\"culture\">Language</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-language\"></i>\n                    <select id=\"culture\" formControlName=\"culture\" class=\"form-control\">\n                      <option value=\"en-US\">English (US)</option>\n                      <option value=\"fr-FR\">Français</option>\n                      <option value=\"es-ES\">Español</option>\n                    </select>\n                  </div>\n                </div>\n                \n                <!-- Baggage Options -->\n                <div class=\"form-group\">\n                  <label for=\"flightBaggageGetOption\">Baggage Options</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-suitcase\"></i>\n                    <select id=\"flightBaggageGetOption\" formControlName=\"flightBaggageGetOption\" class=\"form-control\">\n                      <option [value]=\"0\">All options</option>\n                      <option [value]=\"1\">Baggage included only</option>\n                      <option [value]=\"2\">No baggage only</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </details>\n        </div>\n      </form>\n    </div>\n\n    <!-- Search Results -->\n    <div class=\"search-results-container\" *ngIf=\"hasSearched\">\n      <!-- Loading State -->\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-animation\">\n          <div class=\"plane-animation\">\n            <i class=\"fas fa-plane\"></i>\n            <div class=\"cloud\"></div>\n            <div class=\"cloud\"></div>\n          </div>\n          <p>Searching for the best flights...</p>\n          <div class=\"loading-tips\">\n            <span>We're comparing prices from hundreds of airlines</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Error State -->\n      <div *ngIf=\"!isLoading && errorMessage\" class=\"error-container\">\n        <div class=\"error-content\">\n          <div class=\"error-icon\">\n            <i class=\"fas fa-exclamation-triangle\"></i>\n          </div>\n          <h3>Oops! Something went wrong</h3>\n          <p class=\"error-message\">{{ errorMessage }}</p>\n          <button class=\"retry-button\" (click)=\"onSearch()\">\n            <i class=\"fas fa-redo\"></i> Try Again\n          </button>\n        </div>\n      </div>\n\n      <!-- Results Content -->\n      <div *ngIf=\"!isLoading && !errorMessage\" class=\"search-results-content\">\n        <!-- Results Header -->\n        <div class=\"results-header\">\n          <div class=\"results-summary\">\n            <h3>\n              <span *ngIf=\"searchForm.get('departureLocation')?.value && searchForm.get('arrivalLocation')?.value\">\n                {{ displayLocation(searchForm.get('departureLocation')?.value) }} to \n                {{ displayLocation(searchForm.get('arrivalLocation')?.value) }}\n              </span>\n              <span *ngIf=\"searchForm.get('departureDate')?.value\">\n                · {{ formatDateFull(searchForm.get('departureDate')?.value) }}\n              </span>\n            </h3>\n            <p *ngIf=\"searchResults.length === 0\">No flights found for your search. Please modify your criteria.</p>\n            <p *ngIf=\"searchResults.length > 0\">\n              <span class=\"results-count\">{{ searchResults.length }}</span> flights found\n              <span *ngIf=\"searchForm.get('passengerCount')?.value > 1\">\n                · {{ searchForm.get('passengerCount')?.value }} travelers\n              </span>\n              <span>\n                · {{ getFlightClassName(searchForm.get('flightClass')?.value) }}\n              </span>\n            </p>\n          </div>\n\n          <!-- Filters and Sorting -->\n          <div class=\"results-actions\" *ngIf=\"searchResults.length > 0\">\n            <div class=\"filter-group\">\n              <button class=\"filter-button\" (click)=\"toggleFiltersPanel()\">\n                <i class=\"fas fa-filter\"></i> Filters\n              </button>\n              \n              <div class=\"sort-dropdown\">\n                <div class=\"sort-label\">\n                  <i class=\"fas fa-sort\"></i> Sort by:\n                </div>\n                <select class=\"sort-select\" (change)=\"sortResults($event)\">\n                  <option value=\"price\">Price (lowest first)</option>\n                  <option value=\"duration\">Duration (shortest first)</option>\n                  <option value=\"departure\">Departure (earliest first)</option>\n                  <option value=\"arrival\">Arrival (earliest first)</option>\n                </select>\n              </div>\n            </div>\n            \n            <!-- Quick Filters -->\n            <div class=\"quick-filters\">\n              <div class=\"quick-filter\" (click)=\"toggleDirectFlightsOnly()\">\n                <div class=\"filter-checkbox\" [class.checked]=\"directFlightsOnly\">\n                  <i class=\"fas fa-check\" *ngIf=\"directFlightsOnly\"></i>\n                </div>\n                <span>Direct flights only</span>\n              </div>\n              \n              <div class=\"quick-filter\" (click)=\"toggleBaggageIncluded()\">\n                <div class=\"filter-checkbox\" [class.checked]=\"baggageIncluded\">\n                  <i class=\"fas fa-check\" *ngIf=\"baggageIncluded\"></i>\n                </div>\n                <span>Baggage included</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Filters Panel (hidden by default) -->\n        <div class=\"filters-panel\" [class.show]=\"showFiltersPanel\">\n          <div class=\"filters-header\">\n            <h4>Filter Results</h4>\n            <button class=\"close-filters\" (click)=\"toggleFiltersPanel()\">\n              <i class=\"fas fa-times\"></i>\n            </button>\n          </div>\n          \n          <div class=\"filters-content\">\n            <!-- Price Range Filter -->\n            <div class=\"filter-section\">\n              <h5>Price Range</h5>\n              <div class=\"price-slider\">\n                <!-- Implement price slider here -->\n                <div class=\"price-range\">\n                  <span>€0</span>\n                  <span>€2000</span>\n                </div>\n              </div>\n            </div>\n            \n            <!-- Airlines Filter -->\n            <div class=\"filter-section\">\n              <h5>Airlines</h5>\n              <div class=\"airlines-list\">\n                <div class=\"airline-filter\" *ngFor=\"let airline of getUniqueAirlines()\">\n                  <div class=\"filter-checkbox\">\n                    <i class=\"fas fa-check\"></i>\n                  </div>\n                  <span>{{ airline }}</span>\n                </div>\n              </div>\n            </div>\n            \n            <!-- Departure Time Filter -->\n            <div class=\"filter-section\">\n              <h5>Departure Time</h5>\n              <div class=\"time-filters\">\n                <div class=\"time-filter\">\n                  <div class=\"filter-checkbox\">\n                    <i class=\"fas fa-check\"></i>\n                  </div>\n                  <span><i class=\"fas fa-sun\"></i> Morning (6AM - 12PM)</span>\n                </div>\n                <div class=\"time-filter\">\n                  <div class=\"filter-checkbox\">\n                    <i class=\"fas fa-check\"></i>\n                  </div>\n                  <span><i class=\"fas fa-cloud-sun\"></i> Afternoon (12PM - 6PM)</span>\n                </div>\n                <div class=\"time-filter\">\n                  <div class=\"filter-checkbox\">\n                    <i class=\"fas fa-moon\"></i>\n                  </div>\n                  <span>Evening/Night (After 6PM)</span>\n                </div>\n              </div>\n            </div>\n            \n            <div class=\"filters-actions\">\n              <button class=\"reset-filters\">Reset All</button>\n              <button class=\"apply-filters\" (click)=\"toggleFiltersPanel()\">Apply Filters</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Flight Cards -->\n        <div class=\"flight-list\">\n          <!-- Flight Card -->\n          <div *ngFor=\"let flight of searchResults\" class=\"flight-card\" [class.unavailable]=\"!isFlightAvailable(flight)\">\n            <!-- Airline and Price Section -->\n            <div class=\"flight-main-info\">\n              <!-- Airline Information -->\n              <div class=\"airline-info\">\n                <div class=\"airline-logo\">\n                  <img *ngIf=\"flight.items && flight.items[0] && flight.items[0].airline && flight.items[0].airline.thumbnailFull\"\n                       [src]=\"flight.items[0].airline.thumbnailFull\"\n                       alt=\"Airline logo\">\n                  <i *ngIf=\"!(flight.items && flight.items[0] && flight.items[0].airline && flight.items[0].airline.thumbnailFull)\"\n                     class=\"fas fa-plane\"></i>\n                </div>\n                <div class=\"airline-name\">\n                  {{ flight.items && flight.items[0] && flight.items[0].airline ? flight.items[0].airline.name : 'Airline' }}\n                  <span class=\"flight-number\">{{ flight.items && flight.items[0] ? flight.items[0].flightNo : 'N/A' }}</span>\n                </div>\n              </div>\n              \n              <!-- Flight Class and Badges -->\n              <div class=\"flight-badges\">\n                <span class=\"badge direct\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount === 0\">\n                  <i class=\"fas fa-bolt\"></i> Direct\n                </span>\n                <span class=\"badge class\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].flightClass\">\n                  <i class=\"fas fa-chair\"></i> {{ flight.items[0].flightClass.name }}\n                </span>\n                <span class=\"badge branded\" *ngIf=\"flight.offers && flight.offers[0] && flight.offers[0].hasBrand\">\n                  <i class=\"fas fa-certificate\"></i> Branded Fare\n                </span>\n              </div>\n              \n              <!-- Price Information -->\n              <div class=\"price-info\">\n                <div class=\"price-amount\">{{ getMinPrice(flight) }}</div>\n                <div class=\"price-details\">per person</div>\n                <div class=\"availability\" [class.available]=\"isFlightAvailable(flight)\">\n                  {{ isFlightAvailable(flight) ? 'Available' : 'Not available' }}\n                </div>\n              </div>\n            </div>\n            \n            <!-- Flight Route Information -->\n            <div class=\"flight-route-info\">\n              <!-- Departure -->\n              <div class=\"route-point departure\">\n                <div class=\"time\">{{ flight.items && flight.items[0] && flight.items[0].departure ? formatTime(flight.items[0].departure.date) : 'N/A' }}</div>\n                <div class=\"date\">{{ flight.items && flight.items[0] && flight.items[0].departure ? formatDate(flight.items[0].departure.date) : 'N/A' }}</div>\n                <div class=\"location\">\n                  <div class=\"airport-code\">{{ flight.items && flight.items[0] && flight.items[0].departure && flight.items[0].departure.airport ? flight.items[0].departure.airport.code : 'N/A' }}</div>\n                  <div class=\"city\">{{ flight.items && flight.items[0] && flight.items[0].departure && flight.items[0].departure.city ? flight.items[0].departure.city.name : 'N/A' }}</div>\n                </div>\n              </div>\n              \n              <!-- Flight Duration -->\n              <div class=\"flight-duration\">\n                <div class=\"duration-line\">\n                  <div class=\"line\"></div>\n                  <div class=\"plane-icon\">\n                    <i class=\"fas fa-plane\"></i>\n                  </div>\n                </div>\n                <div class=\"duration-time\">{{ flight.items && flight.items[0] ? formatDuration(flight.items[0].duration) : 'N/A' }}</div>\n                <div class=\"stops-info\">\n                  <span *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount > 0\" class=\"has-stops\">\n                    {{ flight.items[0].stopCount }} stop{{ flight.items[0].stopCount > 1 ? 's' : '' }}\n                  </span>\n                  <span *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount === 0\" class=\"non-stop\">\n                    Direct\n                  </span>\n                </div>\n              </div>\n              \n              <!-- Arrival -->\n              <div class=\"route-point arrival\">\n                <div class=\"time\">{{ flight.items && flight.items[0] && flight.items[0].arrival ? formatTime(flight.items[0].arrival.date) : 'N/A' }}</div>\n                <div class=\"date\">{{ flight.items && flight.items[0] && flight.items[0].arrival ? formatDate(flight.items[0].arrival.date) : 'N/A' }}</div>\n                <div class=\"location\">\n                  <div class=\"airport-code\">{{ flight.items && flight.items[0] && flight.items[0].arrival && flight.items[0].arrival.airport ? flight.items[0].arrival.airport.code : 'N/A' }}</div>\n                  <div class=\"city\">{{ flight.items && flight.items[0] && flight.items[0].arrival && flight.items[0].arrival.city ? flight.items[0].arrival.city.name : 'N/A' }}</div>\n                </div>\n              </div>\n            </div>\n            \n            <!-- Flight Features Summary -->\n            <div class=\"flight-features-summary\">\n              <!-- Baggage -->\n              <div class=\"feature\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].baggageInformations && flight.items[0].baggageInformations.length > 0\">\n                <i class=\"fas fa-suitcase\"></i>\n                <span>{{ getBaggageSummary(flight.items[0].baggageInformations) }}</span>\n              </div>\n              \n              <!-- Amenities -->\n              <div class=\"feature\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0\">\n                <i class=\"fas fa-wifi\"></i>\n                <span>In-flight services available</span>\n              </div>\n              \n              <!-- Seat Availability -->\n              <div class=\"feature\" *ngIf=\"flight.offers && flight.offers[0] && flight.offers[0].seatInfo\">\n                <i class=\"fas fa-chair\"></i>\n                <span>{{ flight.offers[0].seatInfo.availableSeatCount }} seats left</span>\n              </div>\n            </div>\n            \n            <!-- Expandable Details Section -->\n            <div class=\"flight-details-toggle\">\n              <button class=\"toggle-details-btn\" (click)=\"toggleFlightDetails(flight)\">\n                <span *ngIf=\"!isFlightDetailsOpen(flight)\">Show details</span>\n                <span *ngIf=\"isFlightDetailsOpen(flight)\">Hide details</span>\n                <i class=\"fas\" [ngClass]=\"isFlightDetailsOpen(flight) ? 'fa-chevron-up' : 'fa-chevron-down'\"></i>\n              </button>\n            </div>\n            \n            <!-- Expanded Details (hidden by default) -->\n            <div class=\"flight-expanded-details\" [class.show]=\"isFlightDetailsOpen(flight)\">\n              <!-- Tabs for different details -->\n              <div class=\"details-tabs\">\n                <div class=\"tab\" [class.active]=\"getActiveTab(flight) === 'flight'\" (click)=\"setActiveTab(flight, 'flight')\">\n                  Flight Details\n                </div>\n                <div class=\"tab\" [class.active]=\"getActiveTab(flight) === 'fare'\" (click)=\"setActiveTab(flight, 'fare')\">\n                  Fare Information\n                </div>\n                <div class=\"tab\" [class.active]=\"getActiveTab(flight) === 'baggage'\" (click)=\"setActiveTab(flight, 'baggage')\">\n                  Baggage\n                </div>\n              </div>\n              \n              <!-- Tab Content -->\n              <div class=\"tab-content\">\n                <!-- Flight Details Tab -->\n                <div class=\"tab-pane\" *ngIf=\"getActiveTab(flight) === 'flight'\">\n                  <div class=\"flight-segments\">\n                    <h4>Flight Itinerary</h4>\n                    \n                    <!-- For direct flights -->\n                    <div class=\"segment\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount === 0\">\n                      <div class=\"segment-header\">\n                        <div class=\"segment-airline\">\n                          <img *ngIf=\"flight.items[0].airline && flight.items[0].airline.thumbnailFull\" \n                               [src]=\"flight.items[0].airline.thumbnailFull\" \n                               alt=\"Airline logo\">\n                          <span>{{ flight.items[0].airline ? flight.items[0].airline.name : 'Airline' }}</span>\n                          <span class=\"segment-flight-number\">{{ flight.items[0].flightNo }}</span>\n                        </div>\n                        <div class=\"segment-aircraft\" *ngIf=\"flight.items[0].aircraft\">\n                          <i class=\"fas fa-plane\"></i> {{ flight.items[0].aircraft }}\n                        </div>\n                      </div>\n                      \n                      <div class=\"segment-timeline\">\n                        <div class=\"timeline-point departure\">\n                          <div class=\"point-time\">{{ formatTime(flight.items[0].departure.date) }}</div>\n                          <div class=\"point-date\">{{ formatDate(flight.items[0].departure.date) }}</div>\n                          <div class=\"point-location\">\n                            <div class=\"airport\">{{ flight.items[0].departure.airport.name }}</div>\n                            <div class=\"airport-code\">{{ flight.items[0].departure.airport.code }}</div>\n                            <div class=\"city\">{{ flight.items[0].departure.city.name }}, {{ flight.items[0].departure.country.name }}</div>\n                          </div>\n                        </div>\n                        \n                        <div class=\"timeline-line\">\n                          <div class=\"duration\">{{ formatDuration(flight.items[0].duration) }}</div>\n                          <div class=\"line\"></div>\n                          <div class=\"plane-icon\">\n                            <i class=\"fas fa-plane\"></i>\n                          </div>\n                        </div>\n                        \n                        <div class=\"timeline-point arrival\">\n                          <div class=\"point-time\">{{ formatTime(flight.items[0].arrival.date) }}</div>\n                          <div class=\"point-date\">{{ formatDate(flight.items[0].arrival.date) }}</div>\n                          <div class=\"point-location\">\n                            <div class=\"airport\">{{ flight.items[0].arrival.airport.name }}</div>\n                            <div class=\"airport-code\">{{ flight.items[0].arrival.airport.code }}</div>\n                            <div class=\"city\">{{ flight.items[0].arrival.city.name }}, {{ flight.items[0].arrival.country.name }}</div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <!-- For connecting flights -->\n                    <div *ngIf=\"flight.items && flight.items[0] && flight.items[0].segments && flight.items[0].segments.length > 1\">\n                      <div class=\"segment\" *ngFor=\"let segment of flight.items[0].segments; let i = index\">\n                        <div class=\"segment-header\">\n                          <div class=\"segment-number\">Segment {{ i + 1 }}</div>\n                          <div class=\"segment-airline\">\n                            <img *ngIf=\"segment.airline && segment.airline.thumbnailFull\" \n                                 [src]=\"segment.airline.thumbnailFull\" \n                                 alt=\"Airline logo\">\n                            <span>{{ segment.airline ? segment.airline.name : 'Airline' }}</span>\n                            <span class=\"segment-flight-number\">{{ segment.flightNo }}</span>\n                          </div>\n                          <div class=\"segment-aircraft\" *ngIf=\"segment.aircraft\">\n                            <i class=\"fas fa-plane\"></i> {{ segment.aircraft }}\n                          </div>\n                        </div>\n                        \n                        <div class=\"segment-timeline\">\n                          <div class=\"timeline-point departure\">\n                            <div class=\"point-time\">{{ formatTime(segment.departure.date) }}</div>\n                            <div class=\"point-date\">{{ formatDate(segment.departure.date) }}</div>\n                            <div class=\"point-location\">\n                              <div class=\"airport\">{{ segment.departure.airport.name }}</div>\n                              <div class=\"airport-code\">{{ segment.departure.airport.code }}</div>\n                              <div class=\"city\">{{ segment.departure.city.name }}, {{ segment.departure.country.name }}</div>\n                            </div>\n                          </div>\n                          \n                          <div class=\"timeline-line\">\n                            <div class=\"duration\">{{ formatDuration(segment.duration) }}</div>\n                            <div class=\"line\"></div>\n                            <div class=\"plane-icon\">\n                              <i class=\"fas fa-plane\"></i>\n                            </div>\n                          </div>\n                          \n                          <div class=\"timeline-point arrival\">\n                            <div class=\"point-time\">{{ formatTime(segment.arrival.date) }}</div>\n                            <div class=\"point-date\">{{ formatDate(segment.arrival.date) }}</div>\n                            <div class=\"point-location\">\n                              <div class=\"airport\">{{ segment.arrival.airport.name }}</div>\n                              <div class=\"airport-code\">{{ segment.arrival.airport.code }}</div>\n                              <div class=\"city\">{{ segment.arrival.city.name }}, {{ segment.arrival.country.name }}</div>\n                            </div>\n                          </div>\n                        </div>\n                        \n                        <!-- Layover information if not the last segment -->\n                        <div class=\"layover-info\" *ngIf=\"i < flight.items[0].segments.length - 1\">\n                          <div class=\"layover-icon\">\n                            <i class=\"fas fa-hourglass-half\"></i>\n                          </div>\n                          <div class=\"layover-details\">\n                            <span class=\"layover-duration\">{{ calculateLayoverTime(segment, flight.items[0].segments[i+1]) }}</span>\n                            <span class=\"layover-location\">Layover in {{ segment.arrival.city.name }}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <!-- Fare Information Tab -->\n                <div class=\"tab-pane\" *ngIf=\"getActiveTab(flight) === 'fare'\">\n                  <div class=\"fare-details\">\n                    <h4>Fare Details</h4>\n                    \n                    <!-- Fare Breakdown -->\n                    <div class=\"fare-breakdown\" *ngIf=\"flight.offers && flight.offers[0] && flight.offers[0].priceBreakDown\">\n                      <div class=\"breakdown-item\" *ngFor=\"let item of flight.offers[0].priceBreakDown.items\">\n                        <div class=\"item-label\">{{ getPassengerTypeName(item.passengerType) }} (x{{ item.passengerCount }})</div>\n                        <div class=\"item-value\">{{ item.price.amount | currency:item.price.currency }}</div>\n                      </div>\n                      \n                      <div class=\"breakdown-item service-fee\" *ngIf=\"flight.offers[0].serviceFee\">\n                        <div class=\"item-label\">Service Fee</div>\n                        <div class=\"item-value\">{{ flight.offers[0].serviceFee.amount | currency:flight.offers[0].serviceFee.currency }}</div>\n                      </div>\n                      \n                      <div class=\"breakdown-item taxes\" *ngIf=\"flight.offers[0].priceBreakDown.items[0].airportTax\">\n                        <div class=\"item-label\">Taxes & Fees</div>\n                        <div class=\"item-value\">{{ flight.offers[0].priceBreakDown.items[0].airportTax.amount | currency:flight.offers[0].priceBreakDown.items[0].airportTax.currency }}</div>\n                      </div>\n                      \n                      <div class=\"breakdown-total\">\n                        <div class=\"total-label\">Total Price</div>\n                        <div class=\"total-value\">{{ flight.offers[0].price.amount | currency:flight.offers[0].price.currency }}</div>\n                      </div>\n                    </div>\n                    \n                    <!-- Branded Fare Information -->\n                    <div class=\"branded-fare-info\" *ngIf=\"flight.offers && flight.offers[0] && flight.offers[0].flightBrandInfo\">\n                      <h5>{{ flight.offers[0].flightBrandInfo.name }} Fare Features</h5>\n                      \n                      <div class=\"fare-features\">\n                        <div class=\"feature-item\" *ngFor=\"let feature of flight.offers[0].flightBrandInfo.features\">\n                          <div class=\"feature-icon\">\n                            <i class=\"fas\" [ngClass]=\"{\n                              'fa-suitcase': feature.serviceGroup === 1,\n                              'fa-utensils': feature.serviceGroup === 2,\n                              'fa-wifi': feature.serviceGroup === 3,\n                              'fa-tv': feature.serviceGroup === 4,\n                              'fa-exchange-alt': feature.serviceGroup === 5,\n                              'fa-certificate': true\n                            }\"></i>\n                          </div>\n                          <div class=\"feature-details\">\n                            <div class=\"feature-name\">{{ feature.commercialName || 'Feature' }}</div>\n                            <div class=\"feature-description\" *ngIf=\"feature.explanations && feature.explanations.length > 0\">\n                              {{ feature.explanations[0].text }}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <!-- Fare Rules -->\n                    <div class=\"fare-rules\">\n                      <h5>Fare Rules</h5>\n                      <div class=\"rule-item\">\n                        <div class=\"rule-icon\"><i class=\"fas fa-exchange-alt\"></i></div>\n                        <div class=\"rule-details\">\n                          <div class=\"rule-name\">Changes</div>\n                          <div class=\"rule-description\">Changes allowed with fee</div>\n                        </div>\n                      </div>\n                      <div class=\"rule-item\">\n                        <div class=\"rule-icon\"><i class=\"fas fa-ban\"></i></div>\n                        <div class=\"rule-details\">\n                          <div class=\"rule-name\">Cancellation</div>\n                          <div class=\"rule-description\">Non-refundable</div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <!-- Baggage Tab -->\n                <div class=\"tab-pane\" *ngIf=\"getActiveTab(flight) === 'baggage'\">\n                  <div class=\"baggage-details\">\n                    <h4>Baggage Information</h4>\n                    \n                    <div class=\"baggage-allowance\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].baggageInformations\">\n                      <div class=\"baggage-type\" *ngFor=\"let baggage of flight.items[0].baggageInformations\">\n                        <div class=\"baggage-icon\">\n                          <i class=\"fas\" [ngClass]=\"{'fa-suitcase': baggage.baggageType === 1, 'fa-briefcase': baggage.baggageType === 2}\"></i>\n                        </div>\n                        <div class=\"baggage-info\">\n                          <div class=\"baggage-name\">{{ getBaggageTypeName(baggage.baggageType) }}</div>\n                          <div class=\"baggage-details\">\n                            <span *ngIf=\"baggage.weight > 0\">{{ baggage.weight }} {{ baggage.unitType === 1 ? 'kg' : 'lbs' }}</span>\n                            <span *ngIf=\"baggage.piece > 0\">{{ baggage.piece }} piece(s)</span>\n                          </div>\n                          <div class=\"passenger-type\">\n                            For {{ getPassengerTypeName(baggage.passengerType) }}\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div class=\"baggage-policy\">\n                        <p>Additional baggage can be purchased during check-in or by contacting the airline directly.</p>\n                      </div>\n                    </div>\n                    \n                    <div class=\"no-baggage-info\" *ngIf=\"!flight.items || !flight.items[0] || !flight.items[0].baggageInformations || flight.items[0].baggageInformations.length === 0\">\n                      <i class=\"fas fa-info-circle\"></i>\n                      <p>No baggage information available for this flight. Please contact the airline for details.</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <!-- Flight Actions -->\n            <div class=\"flight-actions\">\n              <button class=\"select-flight-btn\" [disabled]=\"!isFlightAvailable(flight)\" (click)=\"selectThisFlight(flight)\">\n                <i class=\"fas fa-check-circle\"></i> Select\n              </button>\n            </div>\n          </div>\n          \n          <!-- No Results Message -->\n          <div class=\"no-results\" *ngIf=\"!isLoading && !errorMessage && searchResults.length === 0\">\n            <div class=\"no-results-content\">\n              <div class=\"no-results-icon\">\n                <i class=\"fas fa-search\"></i>\n              </div>\n              <h3>No flights found</h3>\n              <p>We couldn't find any flights matching your search criteria.</p>\n              \n              <div class=\"suggestions\">\n                <h4>Try adjusting your search:</h4>\n                <div class=\"suggestion-list\">\n                  <div class=\"suggestion-item\">\n                    <i class=\"fas fa-calendar-alt\"></i>\n                    <span>Try different dates</span>\n                  </div>\n                  <div class=\"suggestion-item\">\n                    <i class=\"fas fa-map-marker-alt\"></i>\n                    <span>Consider nearby airports</span>\n                  </div>\n                  <div class=\"suggestion-item\">\n                    <i class=\"fas fa-filter\"></i>\n                    <span>Remove filters</span>\n                  </div>\n                </div>\n              </div>\n              \n              <button class=\"modify-search-btn\" (click)=\"scrollToSearchForm()\">\n                <i class=\"fas fa-edit\"></i> Modify Search\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnF,SAAqBC,EAAE,QAAQ,MAAM;AAKrC,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;;;;;;;;;;ICqElEC,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,YAAA,CAAAC,IAAA,CAAmB;;;;;IACrEP,EAAA,CAAAC,cAAA,gBAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,YAAA,CAAAE,IAAA,CAAmB;;;;;IACrER,EAAA,CAAAC,cAAA,gBAAwD;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA7BH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAC,YAAA,CAAAG,OAAA,CAAsB;;;;;;;;;;;;;;IAhBtFT,EAAA,CAAAC,cAAA,qBAA2E;IAGrED,EAAA,CAAAU,SAAA,YAMO;IACTV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA8B;IACDD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAW,UAAA,IAAAC,kDAAA,mBAA4E;IAC5EZ,EAAA,CAAAW,UAAA,IAAAE,kDAAA,mBAA4E;IAC5Eb,EAAA,CAAAW,UAAA,KAAAG,mDAAA,mBAAqF;IACvFd,EAAA,CAAAG,YAAA,EAAM;;;;IAjB4CH,EAAA,CAAAe,UAAA,UAAAT,YAAA,CAAkB;IAGrDN,EAAA,CAAAI,SAAA,GAMb;IANaJ,EAAA,CAAAe,UAAA,YAAAf,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAX,YAAA,CAAAY,IAAA,QAAAZ,YAAA,CAAAY,IAAA,QAAAZ,YAAA,CAAAY,IAAA,QAAAZ,YAAA,CAAAY,IAAA,QAAAZ,YAAA,CAAAY,IAAA,QAMb;IAGyBlB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,YAAA,CAAAa,IAAA,CAAmB;IAErCnB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAe,UAAA,SAAAT,YAAA,CAAAC,IAAA,CAAmB;IACnBP,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAe,UAAA,SAAAT,YAAA,CAAAE,IAAA,CAAmB;IACnBR,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAe,UAAA,SAAAT,YAAA,CAAAG,OAAA,CAAsB;;;;;IAMvCT,EAAA,CAAAC,cAAA,eAAgI;IAC9HD,EAAA,CAAAU,SAAA,aAAyC;IAACV,EAAA,CAAAE,MAAA,2CAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAwCIH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAe,YAAA,CAAAb,IAAA,CAAmB;;;;;IACrEP,EAAA,CAAAC,cAAA,gBAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAe,YAAA,CAAAZ,IAAA,CAAmB;;;;;IACrER,EAAA,CAAAC,cAAA,gBAAwD;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA7BH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAe,YAAA,CAAAX,OAAA,CAAsB;;;;;IAhBtFT,EAAA,CAAAC,cAAA,qBAAyE;IAGnED,EAAA,CAAAU,SAAA,YAMO;IACTV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA8B;IACDD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAW,UAAA,IAAAU,kDAAA,mBAA4E;IAC5ErB,EAAA,CAAAW,UAAA,IAAAW,kDAAA,mBAA4E;IAC5EtB,EAAA,CAAAW,UAAA,KAAAY,mDAAA,mBAAqF;IACvFvB,EAAA,CAAAG,YAAA,EAAM;;;;IAjB0CH,EAAA,CAAAe,UAAA,UAAAK,YAAA,CAAkB;IAGnDpB,EAAA,CAAAI,SAAA,GAMb;IANaJ,EAAA,CAAAe,UAAA,YAAAf,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAG,YAAA,CAAAF,IAAA,QAAAE,YAAA,CAAAF,IAAA,QAAAE,YAAA,CAAAF,IAAA,QAAAE,YAAA,CAAAF,IAAA,QAAAE,YAAA,CAAAF,IAAA,QAMb;IAGyBlB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAe,YAAA,CAAAD,IAAA,CAAmB;IAErCnB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAe,UAAA,SAAAK,YAAA,CAAAb,IAAA,CAAmB;IACnBP,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAe,UAAA,SAAAK,YAAA,CAAAZ,IAAA,CAAmB;IACnBR,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAe,UAAA,SAAAK,YAAA,CAAAX,OAAA,CAAsB;;;;;IAMvCT,EAAA,CAAAC,cAAA,eAA4H;IAC1HD,EAAA,CAAAU,SAAA,aAAyC;IAACV,EAAA,CAAAE,MAAA,0CAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBNH,EAAA,CAAAC,cAAA,eAAwH;IACtHD,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBNH,EAAA,CAAAC,cAAA,eAAkH;IAChHD,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAdRH,EAAA,CAAAC,cAAA,cAA4E;IAClDD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3CH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAU,SAAA,YAAmC;IAQrCV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAW,UAAA,IAAAa,0CAAA,kBAEM;IACRxB,EAAA,CAAAG,YAAA,EAAM;;;;;IAPAH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAe,UAAA,QAAAU,MAAA,CAAAC,aAAA,CAAqB;IAIG1B,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAe,UAAA,WAAAY,OAAA,GAAAF,MAAA,CAAAG,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,OAAA,OAAAH,OAAA,GAAAF,MAAA,CAAAG,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAI,OAAA,EAAoF;;;;;;IAkC1G/B,EAAA,CAAAC,cAAA,eAGoD;IAA/CD,EAAA,CAAAgC,UAAA,mBAAAC,0DAAA;MAAA,MAAAC,WAAA,GAAAlC,EAAA,CAAAmC,aAAA,CAAAC,IAAA;MAAA,MAAAC,eAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAF,OAAA,CAAAG,iBAAA,CAAAL,eAAA,CAAAM,KAAA,CAAoC;IAAA,EAAC;IACjD3C,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAHDH,EAAA,CAAA4C,WAAA,eAAAC,OAAA,GAAAC,MAAA,CAAAlB,UAAA,CAAAC,GAAA,kCAAAgB,OAAA,CAAAF,KAAA,MAAAN,eAAA,CAAAM,KAAA,CAA6E;IAEhF3C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA+C,kBAAA,MAAAV,eAAA,CAAAW,KAAA,MACF;;;;;IAiBJhD,EAAA,CAAAU,SAAA,aAAgD;;;;;IAChDV,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC9CH,EAAA,CAAAC,cAAA,eAAiD;IAC/CD,EAAA,CAAAU,SAAA,eAA2B;IAC7BV,EAAA,CAAAG,YAAA,EAAM;;;;;IA8EhBH,EAAA,CAAAC,cAAA,eAAiD;IAG3CD,EAAA,CAAAU,SAAA,WAA4B;IAG9BV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,eAA0B;IAClBD,EAAA,CAAAE,MAAA,wDAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAMnEH,EAAA,CAAAC,cAAA,eAAgE;IAG1DD,EAAA,CAAAU,SAAA,aAA2C;IAC7CV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/CH,EAAA,CAAAC,cAAA,kBAAkD;IAArBD,EAAA,CAAAgC,UAAA,mBAAAiB,oEAAA;MAAAjD,EAAA,CAAAmC,aAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAU,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAC/CpD,EAAA,CAAAU,SAAA,aAA2B;IAACV,EAAA,CAAAE,MAAA,mBAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHgBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAgD,OAAA,CAAAC,YAAA,CAAkB;;;;;IAavCtD,EAAA,CAAAC,cAAA,WAAqG;IACnGD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFLH,EAAA,CAAAI,SAAA,GAEF;IAFEJ,EAAA,CAAAuD,kBAAA,MAAAC,OAAA,CAAAC,eAAA,EAAAZ,OAAA,GAAAW,OAAA,CAAA5B,UAAA,CAAAC,GAAA,wCAAAgB,OAAA,CAAAF,KAAA,WAAAa,OAAA,CAAAC,eAAA,EAAAZ,OAAA,GAAAW,OAAA,CAAA5B,UAAA,CAAAC,GAAA,sCAAAgB,OAAA,CAAAF,KAAA,OAEF;;;;;IACA3C,EAAA,CAAAC,cAAA,WAAqD;IACnDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA+C,kBAAA,aAAAW,OAAA,CAAAC,cAAA,EAAAd,OAAA,GAAAa,OAAA,CAAA9B,UAAA,CAAAC,GAAA,oCAAAgB,OAAA,CAAAF,KAAA,OACF;;;;;IAEF3C,EAAA,CAAAC,cAAA,QAAsC;IAAAD,EAAA,CAAAE,MAAA,qEAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAGtGH,EAAA,CAAAC,cAAA,WAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA+C,kBAAA,cAAAF,OAAA,GAAAe,OAAA,CAAAhC,UAAA,CAAAC,GAAA,qCAAAgB,OAAA,CAAAF,KAAA,gBACF;;;;;IAJF3C,EAAA,CAAAC,cAAA,QAAoC;IACND,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,sBAC9D;IAAAF,EAAA,CAAAW,UAAA,IAAAkD,sDAAA,mBAEO;IACP7D,EAAA,CAAAC,cAAA,WAAM;IACJD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IANqBH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAyD,OAAA,CAAAC,aAAA,CAAAC,MAAA,CAA0B;IAC/ChE,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAe,UAAA,WAAAY,OAAA,GAAAmC,OAAA,CAAAlC,UAAA,CAAAC,GAAA,qCAAAF,OAAA,CAAAgB,KAAA,MAAiD;IAItD3C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA+C,kBAAA,aAAAe,OAAA,CAAAG,kBAAA,EAAAC,OAAA,GAAAJ,OAAA,CAAAlC,UAAA,CAAAC,GAAA,kCAAAqC,OAAA,CAAAvB,KAAA,OACF;;;;;IA4BI3C,EAAA,CAAAU,SAAA,aAAsD;;;;;IAOtDV,EAAA,CAAAU,SAAA,aAAoD;;;;;;IA9B5DV,EAAA,CAAAC,cAAA,eAA8D;IAE5BD,EAAA,CAAAgC,UAAA,mBAAAmC,0EAAA;MAAAnE,EAAA,CAAAmC,aAAA,CAAAiC,IAAA;MAAA,MAAAC,OAAA,GAAArE,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAA4B,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAC1DtE,EAAA,CAAAU,SAAA,aAA6B;IAACV,EAAA,CAAAE,MAAA,gBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,eAA2B;IAEvBD,EAAA,CAAAU,SAAA,aAA2B;IAACV,EAAA,CAAAE,MAAA,iBAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAA2D;IAA/BD,EAAA,CAAAgC,UAAA,oBAAAuC,2EAAAC,MAAA;MAAAxE,EAAA,CAAAmC,aAAA,CAAAiC,IAAA;MAAA,MAAAK,OAAA,GAAAzE,EAAA,CAAAwC,aAAA;MAAA,OAAUxC,EAAA,CAAAyC,WAAA,CAAAgC,OAAA,CAAAC,WAAA,CAAAF,MAAA,CAAmB;IAAA,EAAC;IACxDxE,EAAA,CAAAC,cAAA,mBAAsB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnDH,EAAA,CAAAC,cAAA,mBAAyB;IAAAD,EAAA,CAAAE,MAAA,iCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC3DH,EAAA,CAAAC,cAAA,mBAA0B;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7DH,EAAA,CAAAC,cAAA,mBAAwB;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAM/DH,EAAA,CAAAC,cAAA,gBAA2B;IACCD,EAAA,CAAAgC,UAAA,mBAAA2C,wEAAA;MAAA3E,EAAA,CAAAmC,aAAA,CAAAiC,IAAA;MAAA,MAAAQ,OAAA,GAAA5E,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAmC,OAAA,CAAAC,uBAAA,EAAyB;IAAA,EAAC;IAC3D7E,EAAA,CAAAC,cAAA,gBAAiE;IAC/DD,EAAA,CAAAW,UAAA,KAAAmE,sDAAA,iBAAsD;IACxD9E,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGlCH,EAAA,CAAAC,cAAA,gBAA4D;IAAlCD,EAAA,CAAAgC,UAAA,mBAAA+C,wEAAA;MAAA/E,EAAA,CAAAmC,aAAA,CAAAiC,IAAA;MAAA,MAAAY,OAAA,GAAAhF,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAuC,OAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IACzDjF,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAW,UAAA,KAAAuE,sDAAA,iBAAoD;IACtDlF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAVAH,EAAA,CAAAI,SAAA,IAAmC;IAAnCJ,EAAA,CAAA4C,WAAA,YAAAuC,OAAA,CAAAC,iBAAA,CAAmC;IACrCpF,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,SAAAoE,OAAA,CAAAC,iBAAA,CAAuB;IAMrBpF,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAA4C,WAAA,YAAAuC,OAAA,CAAAE,eAAA,CAAiC;IACnCrF,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAe,UAAA,SAAAoE,OAAA,CAAAE,eAAA,CAAqB;;;;;IAkChDrF,EAAA,CAAAC,cAAA,eAAwE;IAEpED,EAAA,CAAAU,SAAA,aAA4B;IAC9BV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAiF,WAAA,CAAa;;;;;IA8CnBtF,EAAA,CAAAU,SAAA,eAEwB;;;;IADnBV,EAAA,CAAAe,UAAA,QAAAwE,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,EAAA1F,EAAA,CAAA2F,aAAA,CAA6C;;;;;IAElD3F,EAAA,CAAAU,SAAA,WAC4B;;;;;IAU9BV,EAAA,CAAAC,cAAA,gBAAsG;IACpGD,EAAA,CAAAU,SAAA,aAA2B;IAACV,EAAA,CAAAE,MAAA,eAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAAiG;IAC/FD,EAAA,CAAAU,SAAA,aAA4B;IAACV,EAAA,CAAAE,MAAA,GAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADwBH,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAA+C,kBAAA,MAAAwC,UAAA,CAAAC,KAAA,IAAAI,WAAA,CAAAzE,IAAA,MAC/B;;;;;IACAnB,EAAA,CAAAC,cAAA,gBAAmG;IACjGD,EAAA,CAAAU,SAAA,aAAkC;IAACV,EAAA,CAAAE,MAAA,qBACrC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAmCLH,EAAA,CAAAC,cAAA,gBAAiG;IAC/FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuD,kBAAA,MAAAgC,UAAA,CAAAC,KAAA,IAAAK,SAAA,WAAAN,UAAA,CAAAC,KAAA,IAAAK,SAAA,qBACF;;;;;IACA7F,EAAA,CAAAC,cAAA,gBAAkG;IAChGD,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAkBXH,EAAA,CAAAC,cAAA,eAAsJ;IACpJD,EAAA,CAAAU,SAAA,YAA+B;IAC/BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA4D;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAAnEH,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAAK,iBAAA,CAAAyF,OAAA,CAAAC,iBAAA,CAAAR,UAAA,CAAAC,KAAA,IAAAQ,mBAAA,EAA4D;;;;;IAIpEhG,EAAA,CAAAC,cAAA,eAAgI;IAC9HD,EAAA,CAAAU,SAAA,aAA2B;IAC3BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI3CH,EAAA,CAAAC,cAAA,eAA4F;IAC1FD,EAAA,CAAAU,SAAA,aAA4B;IAC5BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApEH,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAA+C,kBAAA,KAAAwC,UAAA,CAAAU,MAAA,IAAAC,QAAA,CAAAC,kBAAA,gBAA6D;;;;;IAOnEnG,EAAA,CAAAC,cAAA,WAA2C;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC9DH,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA+BnDH,EAAA,CAAAU,SAAA,eAEwB;;;;IADnBV,EAAA,CAAAe,UAAA,QAAAwE,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,EAAA1F,EAAA,CAAA2F,aAAA,CAA6C;;;;;IAKpD3F,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAU,SAAA,WAA4B;IAACV,EAAA,CAAAE,MAAA,GAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADyBH,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAA+C,kBAAA,MAAAwC,UAAA,CAAAC,KAAA,IAAAY,QAAA,MAC/B;;;;;IAXJpG,EAAA,CAAAC,cAAA,eAAgG;IAG1FD,EAAA,CAAAW,UAAA,IAAA0F,qEAAA,mBAEwB;IACxBrG,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAwE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrFH,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE3EH,EAAA,CAAAW,UAAA,IAAA2F,qEAAA,mBAEM;IACRtG,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA8B;IAEFD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9EH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9EH,EAAA,CAAAC,cAAA,gBAA4B;IACLD,EAAA,CAAAE,MAAA,IAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvEH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5EH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAuF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAInHH,EAAA,CAAAC,cAAA,gBAA2B;IACHD,EAAA,CAAAE,MAAA,IAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1EH,EAAA,CAAAU,SAAA,gBAAwB;IACxBV,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAAoC;IACVD,EAAA,CAAAE,MAAA,IAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5EH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5EH,EAAA,CAAAC,cAAA,gBAA4B;IACLD,EAAA,CAAAE,MAAA,IAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrEH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1EH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAmF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IApCvGH,EAAA,CAAAI,SAAA,GAAsE;IAAtEJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAC,OAAA,IAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,CAAsE;IAGtE1F,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAC,OAAA,GAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAtE,IAAA,aAAwE;IAC1CnB,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAe,QAAA,CAA8B;IAErCvG,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAY,QAAA,CAA8B;IAOnCpG,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAAmG,OAAA,CAAAC,UAAA,CAAAlB,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAC,IAAA,EAAgD;IAChD3G,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAAmG,OAAA,CAAAI,UAAA,CAAArB,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAC,IAAA,EAAgD;IAEjD3G,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAG,OAAA,CAAA1F,IAAA,CAA4C;IACvCnB,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAG,OAAA,CAAAtG,IAAA,CAA4C;IACpDP,EAAA,CAAAI,SAAA,GAAuF;IAAvFJ,EAAA,CAAAuD,kBAAA,KAAAgC,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAlG,IAAA,CAAAW,IAAA,QAAAoE,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAjG,OAAA,CAAAU,IAAA,KAAuF;IAKrFnB,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAK,iBAAA,CAAAmG,OAAA,CAAAM,cAAA,CAAAvB,UAAA,CAAAC,KAAA,IAAAuB,QAAA,EAA8C;IAQ5C/G,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAK,iBAAA,CAAAmG,OAAA,CAAAC,UAAA,CAAAlB,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAL,IAAA,EAA8C;IAC9C3G,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAK,iBAAA,CAAAmG,OAAA,CAAAI,UAAA,CAAArB,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAL,IAAA,EAA8C;IAE/C3G,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAH,OAAA,CAAA1F,IAAA,CAA0C;IACrCnB,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAH,OAAA,CAAAtG,IAAA,CAA0C;IAClDP,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAuD,kBAAA,KAAAgC,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAxG,IAAA,CAAAW,IAAA,QAAAoE,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAvG,OAAA,CAAAU,IAAA,KAAmF;;;;;IAYrGnB,EAAA,CAAAU,SAAA,eAEwB;;;;IADnBV,EAAA,CAAAe,UAAA,QAAAkG,WAAA,CAAAxB,OAAA,CAAAC,aAAA,EAAA1F,EAAA,CAAA2F,aAAA,CAAqC;;;;;IAK5C3F,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAU,SAAA,WAA4B;IAACV,EAAA,CAAAE,MAAA,GAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADyBH,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAA+C,kBAAA,MAAAkE,WAAA,CAAAb,QAAA,MAC/B;;;;;IAkCFpG,EAAA,CAAAC,cAAA,eAA0E;IAEtED,EAAA,CAAAU,SAAA,aAAqC;IACvCV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACID,EAAA,CAAAE,MAAA,GAAkE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxGH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;IADjDH,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAAK,iBAAA,CAAA6G,OAAA,CAAAC,oBAAA,CAAAF,WAAA,EAAA1B,UAAA,CAAAC,KAAA,IAAA4B,QAAA,CAAAC,KAAA,OAAkE;IAClErH,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAA+C,kBAAA,gBAAAkE,WAAA,CAAAD,OAAA,CAAAxG,IAAA,CAAAW,IAAA,KAA0C;;;;;IApD/EnB,EAAA,CAAAC,cAAA,eAAqF;IAErDD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrDH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAW,UAAA,IAAA2G,2EAAA,mBAEwB;IACxBtH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEnEH,EAAA,CAAAW,UAAA,KAAA4G,4EAAA,mBAEM;IACRvH,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAA8B;IAEFD,EAAA,CAAAE,MAAA,IAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtEH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtEH,EAAA,CAAAC,cAAA,gBAA4B;IACLD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/DH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAuE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAInGH,EAAA,CAAAC,cAAA,gBAA2B;IACHD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClEH,EAAA,CAAAU,SAAA,gBAAwB;IACxBV,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAAoC;IACVD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,gBAA4B;IACLD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7DH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClEH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAmE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAMjGH,EAAA,CAAAW,UAAA,KAAA6G,4EAAA,mBAQM;IACRxH,EAAA,CAAAG,YAAA,EAAM;;;;;;;IArD0BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAA+C,kBAAA,aAAAsE,KAAA,SAAmB;IAEvCrH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAe,UAAA,SAAAkG,WAAA,CAAAxB,OAAA,IAAAwB,WAAA,CAAAxB,OAAA,CAAAC,aAAA,CAAsD;IAGtD1F,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,iBAAA,CAAA4G,WAAA,CAAAxB,OAAA,GAAAwB,WAAA,CAAAxB,OAAA,CAAAtE,IAAA,aAAwD;IAC1BnB,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAA4G,WAAA,CAAAV,QAAA,CAAsB;IAE7BvG,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAe,UAAA,SAAAkG,WAAA,CAAAb,QAAA,CAAsB;IAO3BpG,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAK,iBAAA,CAAAoH,OAAA,CAAAhB,UAAA,CAAAQ,WAAA,CAAAP,SAAA,CAAAC,IAAA,EAAwC;IACxC3G,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAK,iBAAA,CAAAoH,OAAA,CAAAb,UAAA,CAAAK,WAAA,CAAAP,SAAA,CAAAC,IAAA,EAAwC;IAEzC3G,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,iBAAA,CAAA4G,WAAA,CAAAP,SAAA,CAAAG,OAAA,CAAA1F,IAAA,CAAoC;IAC/BnB,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,iBAAA,CAAA4G,WAAA,CAAAP,SAAA,CAAAG,OAAA,CAAAtG,IAAA,CAAoC;IAC5CP,EAAA,CAAAI,SAAA,GAAuE;IAAvEJ,EAAA,CAAAuD,kBAAA,KAAA0D,WAAA,CAAAP,SAAA,CAAAlG,IAAA,CAAAW,IAAA,QAAA8F,WAAA,CAAAP,SAAA,CAAAjG,OAAA,CAAAU,IAAA,KAAuE;IAKrEnB,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAAoH,OAAA,CAAAX,cAAA,CAAAG,WAAA,CAAAF,QAAA,EAAsC;IAQpC/G,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAAoH,OAAA,CAAAhB,UAAA,CAAAQ,WAAA,CAAAD,OAAA,CAAAL,IAAA,EAAsC;IACtC3G,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,CAAAoH,OAAA,CAAAb,UAAA,CAAAK,WAAA,CAAAD,OAAA,CAAAL,IAAA,EAAsC;IAEvC3G,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,iBAAA,CAAA4G,WAAA,CAAAD,OAAA,CAAAH,OAAA,CAAA1F,IAAA,CAAkC;IAC7BnB,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,iBAAA,CAAA4G,WAAA,CAAAD,OAAA,CAAAH,OAAA,CAAAtG,IAAA,CAAkC;IAC1CP,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAAuD,kBAAA,KAAA0D,WAAA,CAAAD,OAAA,CAAAxG,IAAA,CAAAW,IAAA,QAAA8F,WAAA,CAAAD,OAAA,CAAAvG,OAAA,CAAAU,IAAA,KAAmE;IAMhEnB,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAe,UAAA,SAAAsG,KAAA,GAAA9B,UAAA,CAAAC,KAAA,IAAA4B,QAAA,CAAApD,MAAA,KAA6C;;;;;IA/C5EhE,EAAA,CAAAC,cAAA,UAAgH;IAC9GD,EAAA,CAAAW,UAAA,IAAA+G,qEAAA,qBAuDM;IACR1H,EAAA,CAAAG,YAAA,EAAM;;;;IAxDqCH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAe,UAAA,YAAAwE,UAAA,CAAAC,KAAA,IAAA4B,QAAA,CAA6B;;;;;IApD5EpH,EAAA,CAAAC,cAAA,eAAgE;IAExDD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGzBH,EAAA,CAAAW,UAAA,IAAAgH,+DAAA,qBA2CM;IAGN3H,EAAA,CAAAW,UAAA,IAAAiH,+DAAA,kBAyDM;IACR5H,EAAA,CAAAG,YAAA,EAAM;;;;IAxGkBH,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAK,SAAA,OAAwE;IA8CxF7F,EAAA,CAAAI,SAAA,GAAwG;IAAxGJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAA4B,QAAA,IAAA7B,UAAA,CAAAC,KAAA,IAAA4B,QAAA,CAAApD,MAAA,KAAwG;;;;;IAoE5GhE,EAAA,CAAAC,cAAA,eAAuF;IAC7DD,EAAA,CAAAE,MAAA,GAA2E;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzGH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAsD;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAD5DH,EAAA,CAAAI,SAAA,GAA2E;IAA3EJ,EAAA,CAAAuD,kBAAA,KAAAsE,OAAA,CAAAC,oBAAA,CAAAC,QAAA,CAAAC,aAAA,UAAAD,QAAA,CAAAE,cAAA,MAA2E;IAC3EjI,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAkI,WAAA,OAAAH,QAAA,CAAAI,KAAA,CAAAC,MAAA,EAAAL,QAAA,CAAAI,KAAA,CAAAE,QAAA,EAAsD;;;;;IAGhFrI,EAAA,CAAAC,cAAA,eAA4E;IAClDD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzCH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAwF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA9FH,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAkI,WAAA,OAAA3C,UAAA,CAAAU,MAAA,IAAAqC,UAAA,CAAAF,MAAA,EAAA7C,UAAA,CAAAU,MAAA,IAAAqC,UAAA,CAAAD,QAAA,EAAwF;;;;;IAGlHrI,EAAA,CAAAC,cAAA,eAA8F;IACpED,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAwI;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA9IH,EAAA,CAAAI,SAAA,GAAwI;IAAxIJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAkI,WAAA,OAAA3C,UAAA,CAAAU,MAAA,IAAAsC,cAAA,CAAA/C,KAAA,IAAAgD,UAAA,CAAAJ,MAAA,EAAA7C,UAAA,CAAAU,MAAA,IAAAsC,cAAA,CAAA/C,KAAA,IAAAgD,UAAA,CAAAH,QAAA,EAAwI;;;;;IAbpKrI,EAAA,CAAAC,cAAA,eAAyG;IACvGD,EAAA,CAAAW,UAAA,IAAA8H,qEAAA,mBAGM;IAENzI,EAAA,CAAAW,UAAA,IAAA+H,qEAAA,mBAGM;IAEN1I,EAAA,CAAAW,UAAA,IAAAgI,qEAAA,mBAGM;IAEN3I,EAAA,CAAAC,cAAA,eAA6B;IACFD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA8E;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAjBlEH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAe,UAAA,YAAAwE,UAAA,CAAAU,MAAA,IAAAsC,cAAA,CAAA/C,KAAA,CAAwC;IAK5CxF,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAU,MAAA,IAAAqC,UAAA,CAAiC;IAKvCtI,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAU,MAAA,IAAAsC,cAAA,CAAA/C,KAAA,IAAAgD,UAAA,CAAyD;IAOjExI,EAAA,CAAAI,SAAA,GAA8E;IAA9EJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAkI,WAAA,OAAA3C,UAAA,CAAAU,MAAA,IAAAkC,KAAA,CAAAC,MAAA,EAAA7C,UAAA,CAAAU,MAAA,IAAAkC,KAAA,CAAAE,QAAA,EAA8E;;;;;IAsBnGrI,EAAA,CAAAC,cAAA,eAAiG;IAC/FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA+C,kBAAA,MAAA6F,YAAA,CAAAC,YAAA,IAAAC,IAAA,MACF;;;;;;;;;;;;;;;IAfJ9I,EAAA,CAAAC,cAAA,eAA4F;IAExFD,EAAA,CAAAU,SAAA,YAOO;IACTV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACDD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzEH,EAAA,CAAAW,UAAA,IAAAoI,2EAAA,mBAEM;IACR/I,EAAA,CAAAG,YAAA,EAAM;;;;IAdWH,EAAA,CAAAI,SAAA,GAOb;IAPaJ,EAAA,CAAAe,UAAA,YAAAf,EAAA,CAAAgB,eAAA,IAAAgI,GAAA,EAAAJ,YAAA,CAAAK,YAAA,QAAAL,YAAA,CAAAK,YAAA,QAAAL,YAAA,CAAAK,YAAA,QAAAL,YAAA,CAAAK,YAAA,QAAAL,YAAA,CAAAK,YAAA,QAOb;IAGwBjJ,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,iBAAA,CAAAuI,YAAA,CAAAM,cAAA,cAAyC;IACjClJ,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAe,UAAA,SAAA6H,YAAA,CAAAC,YAAA,IAAAD,YAAA,CAAAC,YAAA,CAAA7E,MAAA,KAA6D;;;;;IAjBvGhE,EAAA,CAAAC,cAAA,eAA6G;IACvGD,EAAA,CAAAE,MAAA,GAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAElEH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAW,UAAA,IAAAwI,qEAAA,mBAiBM;IACRnJ,EAAA,CAAAG,YAAA,EAAM;;;;IArBFH,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAA+C,kBAAA,KAAAwC,UAAA,CAAAU,MAAA,IAAAmD,eAAA,CAAAjI,IAAA,mBAAyD;IAGbnB,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAe,UAAA,YAAAwE,UAAA,CAAAU,MAAA,IAAAmD,eAAA,CAAAC,QAAA,CAA4C;;;;;IAhClGrJ,EAAA,CAAAC,cAAA,eAA8D;IAEtDD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGrBH,EAAA,CAAAW,UAAA,IAAA2I,+DAAA,oBAoBM;IAGNtJ,EAAA,CAAAW,UAAA,IAAA4I,+DAAA,mBAuBM;IAGNvJ,EAAA,CAAAC,cAAA,eAAwB;IAClBD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,eAAuB;IACED,EAAA,CAAAU,SAAA,aAAmC;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAChEH,EAAA,CAAAC,cAAA,gBAA0B;IACDD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpCH,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGhEH,EAAA,CAAAC,cAAA,gBAAuB;IACED,EAAA,CAAAU,SAAA,cAA0B;IAAAV,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,gBAA0B;IACDD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzCH,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA9D3BH,EAAA,CAAAI,SAAA,GAA0E;IAA1EJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAU,MAAA,IAAAV,UAAA,CAAAU,MAAA,OAAAV,UAAA,CAAAU,MAAA,IAAAsC,cAAA,CAA0E;IAuBvEvI,EAAA,CAAAI,SAAA,GAA2E;IAA3EJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAU,MAAA,IAAAV,UAAA,CAAAU,MAAA,OAAAV,UAAA,CAAAU,MAAA,IAAAmD,eAAA,CAA2E;;;;;IA2DnGpJ,EAAA,CAAAC,cAAA,WAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAgE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvEH,EAAA,CAAAI,SAAA,GAAgE;IAAhEJ,EAAA,CAAAuD,kBAAA,KAAAiG,YAAA,CAAAC,MAAA,OAAAD,YAAA,CAAAE,QAAA,0BAAgE;;;;;IACjG1J,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAnCH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAA+C,kBAAA,KAAAyG,YAAA,CAAAG,KAAA,cAA4B;;;;;;;;;;;IARlE3J,EAAA,CAAAC,cAAA,eAAsF;IAElFD,EAAA,CAAAU,SAAA,YAAqH;IACvHV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACED,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7EH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAW,UAAA,IAAAiJ,4EAAA,mBAAwG;IACxG5J,EAAA,CAAAW,UAAA,IAAAkJ,4EAAA,mBAAmE;IACrE7J,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAVSH,EAAA,CAAAI,SAAA,GAAiG;IAAjGJ,EAAA,CAAAe,UAAA,YAAAf,EAAA,CAAA8J,eAAA,IAAAC,GAAA,EAAAP,YAAA,CAAAQ,WAAA,QAAAR,YAAA,CAAAQ,WAAA,QAAiG;IAGtFhK,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,CAAA4J,QAAA,CAAAC,kBAAA,CAAAV,YAAA,CAAAQ,WAAA,EAA6C;IAE9DhK,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAe,UAAA,SAAAyI,YAAA,CAAAC,MAAA,KAAwB;IACxBzJ,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,SAAAyI,YAAA,CAAAG,KAAA,KAAuB;IAG9B3J,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA+C,kBAAA,UAAAkH,QAAA,CAAAnC,oBAAA,CAAA0B,YAAA,CAAAxB,aAAA,OACF;;;;;IAbNhI,EAAA,CAAAC,cAAA,eAA8G;IAC5GD,EAAA,CAAAW,UAAA,IAAAwJ,qEAAA,oBAcM;IAENnK,EAAA,CAAAC,cAAA,eAA4B;IACvBD,EAAA,CAAAE,MAAA,iGAA0F;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAjBrDH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAwE,UAAA,CAAAC,KAAA,IAAAQ,mBAAA,CAAsC;;;;;IAqBtFhG,EAAA,CAAAC,cAAA,eAAmK;IACjKD,EAAA,CAAAU,SAAA,aAAkC;IAClCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gGAAyF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IA5BtGH,EAAA,CAAAC,cAAA,eAAiE;IAEzDD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5BH,EAAA,CAAAW,UAAA,IAAAyJ,+DAAA,mBAoBM;IAENpK,EAAA,CAAAW,UAAA,IAAA0J,+DAAA,mBAGM;IACRrK,EAAA,CAAAG,YAAA,EAAM;;;;IA1B4BH,EAAA,CAAAI,SAAA,GAA4E;IAA5EJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAQ,mBAAA,CAA4E;IAsB9EhG,EAAA,CAAAI,SAAA,GAAmI;IAAnIJ,EAAA,CAAAe,UAAA,UAAAwE,UAAA,CAAAC,KAAA,KAAAD,UAAA,CAAAC,KAAA,QAAAD,UAAA,CAAAC,KAAA,IAAAQ,mBAAA,IAAAT,UAAA,CAAAC,KAAA,IAAAQ,mBAAA,CAAAhC,MAAA,OAAmI;;;;;;IAzV3KhE,EAAA,CAAAC,cAAA,eAA+G;IAMvGD,EAAA,CAAAW,UAAA,IAAA2J,wDAAA,mBAEwB;IACxBtK,EAAA,CAAAW,UAAA,IAAA4J,sDAAA,iBAC4B;IAC9BvK,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAwE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAK/GH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAW,UAAA,KAAA6J,0DAAA,oBAEO;IACPxK,EAAA,CAAAW,UAAA,KAAA8J,0DAAA,oBAEO;IACPzK,EAAA,CAAAW,UAAA,KAAA+J,0DAAA,oBAEO;IACT1K,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAAwB;IACID,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3CH,EAAA,CAAAC,cAAA,gBAAwE;IACtED,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,gBAA+B;IAGTD,EAAA,CAAAE,MAAA,IAAuH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/IH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAuH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/IH,EAAA,CAAAC,cAAA,gBAAsB;IACMD,EAAA,CAAAE,MAAA,IAAwJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxLH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAK9KH,EAAA,CAAAC,cAAA,gBAA6B;IAEzBD,EAAA,CAAAU,SAAA,gBAAwB;IACxBV,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAwF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzHH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAW,UAAA,KAAAgK,0DAAA,oBAEO;IACP3K,EAAA,CAAAW,UAAA,KAAAiK,0DAAA,oBAEO;IACT5K,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,gBAAiC;IACbD,EAAA,CAAAE,MAAA,IAAmH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3IH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAmH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3IH,EAAA,CAAAC,cAAA,gBAAsB;IACMD,EAAA,CAAAE,MAAA,IAAkJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClLH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA4I;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAM1KH,EAAA,CAAAC,cAAA,gBAAqC;IAEnCD,EAAA,CAAAW,UAAA,KAAAkK,yDAAA,mBAGM;IAGN7K,EAAA,CAAAW,UAAA,KAAAmK,yDAAA,mBAGM;IAGN9K,EAAA,CAAAW,UAAA,KAAAoK,yDAAA,mBAGM;IACR/K,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAAmC;IACED,EAAA,CAAAgC,UAAA,mBAAAgJ,4EAAA;MAAA,MAAA9I,WAAA,GAAAlC,EAAA,CAAAmC,aAAA,CAAA8I,KAAA;MAAA,MAAA1F,UAAA,GAAArD,WAAA,CAAAI,SAAA;MAAA,MAAA4I,QAAA,GAAAlL,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAyI,QAAA,CAAAC,mBAAA,CAAA5F,UAAA,CAA2B;IAAA,EAAC;IACtEvF,EAAA,CAAAW,UAAA,KAAAyK,0DAAA,mBAA8D;IAC9DpL,EAAA,CAAAW,UAAA,KAAA0K,0DAAA,mBAA6D;IAC7DrL,EAAA,CAAAU,SAAA,aAAiG;IACnGV,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,gBAAgF;IAGRD,EAAA,CAAAgC,UAAA,mBAAAsJ,yEAAA;MAAA,MAAApJ,WAAA,GAAAlC,EAAA,CAAAmC,aAAA,CAAA8I,KAAA;MAAA,MAAA1F,UAAA,GAAArD,WAAA,CAAAI,SAAA;MAAA,MAAAiJ,QAAA,GAAAvL,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAA8I,QAAA,CAAAC,YAAA,CAAAjG,UAAA,EAAqB,QAAQ,CAAC;IAAA,EAAC;IAC1GvF,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAyG;IAAvCD,EAAA,CAAAgC,UAAA,mBAAAyJ,yEAAA;MAAA,MAAAvJ,WAAA,GAAAlC,EAAA,CAAAmC,aAAA,CAAA8I,KAAA;MAAA,MAAA1F,UAAA,GAAArD,WAAA,CAAAI,SAAA;MAAA,MAAAoJ,QAAA,GAAA1L,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAiJ,QAAA,CAAAF,YAAA,CAAAjG,UAAA,EAAqB,MAAM,CAAC;IAAA,EAAC;IACtGvF,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA+G;IAA1CD,EAAA,CAAAgC,UAAA,mBAAA2J,yEAAA;MAAA,MAAAzJ,WAAA,GAAAlC,EAAA,CAAAmC,aAAA,CAAA8I,KAAA;MAAA,MAAA1F,UAAA,GAAArD,WAAA,CAAAI,SAAA;MAAA,MAAAsJ,QAAA,GAAA5L,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAmJ,QAAA,CAAAJ,YAAA,CAAAjG,UAAA,EAAqB,SAAS,CAAC;IAAA,EAAC;IAC5GvF,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,gBAAyB;IAEvBD,EAAA,CAAAW,UAAA,KAAAkL,yDAAA,mBA8GM;IAGN7L,EAAA,CAAAW,UAAA,KAAAmL,yDAAA,oBAwEM;IAGN9L,EAAA,CAAAW,UAAA,KAAAoL,yDAAA,mBA+BM;IACR/L,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,gBAA4B;IACgDD,EAAA,CAAAgC,UAAA,mBAAAgK,4EAAA;MAAA,MAAA9J,WAAA,GAAAlC,EAAA,CAAAmC,aAAA,CAAA8I,KAAA;MAAA,MAAA1F,UAAA,GAAArD,WAAA,CAAAI,SAAA;MAAA,MAAA2J,QAAA,GAAAjM,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAwJ,QAAA,CAAAC,gBAAA,CAAA3G,UAAA,CAAwB;IAAA,EAAC;IAC1GvF,EAAA,CAAAU,SAAA,cAAmC;IAACV,EAAA,CAAAE,MAAA,gBACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAtWiDH,EAAA,CAAA4C,WAAA,iBAAAuJ,OAAA,CAAAC,iBAAA,CAAA7G,UAAA,EAAgD;IAMhGvF,EAAA,CAAAI,SAAA,GAAyG;IAAzGJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,IAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,CAAyG;IAG3G1F,EAAA,CAAAI,SAAA,GAA4G;IAA5GJ,EAAA,CAAAe,UAAA,WAAAwE,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,IAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,EAA4G;IAIhH1F,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAA+C,kBAAA,MAAAwC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,GAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAtE,IAAA,kBACA;IAA4BnB,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,MAAAD,UAAA,CAAAC,KAAA,IAAAe,QAAA,SAAwE;IAM1EvG,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAK,SAAA,OAAwE;IAGzE7F,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAI,WAAA,CAAoE;IAGlE5F,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAU,MAAA,IAAAV,UAAA,CAAAU,MAAA,OAAAV,UAAA,CAAAU,MAAA,IAAAoG,QAAA,CAAoE;IAOvErM,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAA8L,OAAA,CAAAG,WAAA,CAAA/G,UAAA,EAAyB;IAEzBvF,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAA4C,WAAA,cAAAuJ,OAAA,CAAAC,iBAAA,CAAA7G,UAAA,EAA6C;IACrEvF,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA+C,kBAAA,MAAAoJ,OAAA,CAAAC,iBAAA,CAAA7G,UAAA,uCACF;IAQkBvF,EAAA,CAAAI,SAAA,GAAuH;IAAvHJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAkB,SAAA,GAAAyF,OAAA,CAAA1F,UAAA,CAAAlB,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAC,IAAA,UAAuH;IACvH3G,EAAA,CAAAI,SAAA,GAAuH;IAAvHJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAkB,SAAA,GAAAyF,OAAA,CAAAvF,UAAA,CAAArB,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAC,IAAA,UAAuH;IAE7G3G,EAAA,CAAAI,SAAA,GAAwJ;IAAxJJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAkB,SAAA,IAAAnB,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAG,OAAA,GAAAtB,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAG,OAAA,CAAAtG,IAAA,SAAwJ;IAChKP,EAAA,CAAAI,SAAA,GAAkJ;IAAlJJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAkB,SAAA,IAAAnB,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAlG,IAAA,GAAA+E,UAAA,CAAAC,KAAA,IAAAkB,SAAA,CAAAlG,IAAA,CAAAW,IAAA,SAAkJ;IAY3InB,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,MAAA2G,OAAA,CAAArF,cAAA,CAAAvB,UAAA,CAAAC,KAAA,IAAAuB,QAAA,UAAwF;IAE1G/G,EAAA,CAAAI,SAAA,GAAsE;IAAtEJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAK,SAAA,KAAsE;IAGtE7F,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAK,SAAA,OAAwE;IAQ/D7F,EAAA,CAAAI,SAAA,GAAmH;IAAnHJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAwB,OAAA,GAAAmF,OAAA,CAAA1F,UAAA,CAAAlB,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAL,IAAA,UAAmH;IACnH3G,EAAA,CAAAI,SAAA,GAAmH;IAAnHJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAwB,OAAA,GAAAmF,OAAA,CAAAvF,UAAA,CAAArB,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAL,IAAA,UAAmH;IAEzG3G,EAAA,CAAAI,SAAA,GAAkJ;IAAlJJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAwB,OAAA,IAAAzB,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAH,OAAA,GAAAtB,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAH,OAAA,CAAAtG,IAAA,SAAkJ;IAC1JP,EAAA,CAAAI,SAAA,GAA4I;IAA5IJ,EAAA,CAAAK,iBAAA,CAAAkF,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAwB,OAAA,IAAAzB,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAxG,IAAA,GAAA+E,UAAA,CAAAC,KAAA,IAAAwB,OAAA,CAAAxG,IAAA,CAAAW,IAAA,SAA4I;IAQ5InB,EAAA,CAAAI,SAAA,GAA8H;IAA9HJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAQ,mBAAA,IAAAT,UAAA,CAAAC,KAAA,IAAAQ,mBAAA,CAAAhC,MAAA,KAA8H;IAM9HhE,EAAA,CAAAI,SAAA,GAAwG;IAAxGJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAA+G,QAAA,IAAAhH,UAAA,CAAAC,KAAA,IAAA+G,QAAA,CAAAvI,MAAA,KAAwG;IAMxGhE,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAe,UAAA,SAAAwE,UAAA,CAAAU,MAAA,IAAAV,UAAA,CAAAU,MAAA,OAAAV,UAAA,CAAAU,MAAA,IAAAC,QAAA,CAAoE;IASjFlG,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAe,UAAA,UAAAoL,OAAA,CAAAK,mBAAA,CAAAjH,UAAA,EAAkC;IAClCvF,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAe,UAAA,SAAAoL,OAAA,CAAAK,mBAAA,CAAAjH,UAAA,EAAiC;IACzBvF,EAAA,CAAAI,SAAA,GAA6E;IAA7EJ,EAAA,CAAAe,UAAA,YAAAoL,OAAA,CAAAK,mBAAA,CAAAjH,UAAA,wCAA6E;IAK3DvF,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAA4C,WAAA,SAAAuJ,OAAA,CAAAK,mBAAA,CAAAjH,UAAA,EAA0C;IAG1DvF,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAA4C,WAAA,WAAAuJ,OAAA,CAAAM,YAAA,CAAAlH,UAAA,eAAkD;IAGlDvF,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAA4C,WAAA,WAAAuJ,OAAA,CAAAM,YAAA,CAAAlH,UAAA,aAAgD;IAGhDvF,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAA4C,WAAA,WAAAuJ,OAAA,CAAAM,YAAA,CAAAlH,UAAA,gBAAmD;IAQ7CvF,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAe,UAAA,SAAAoL,OAAA,CAAAM,YAAA,CAAAlH,UAAA,eAAuC;IAiHvCvF,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAe,UAAA,SAAAoL,OAAA,CAAAM,YAAA,CAAAlH,UAAA,aAAqC;IA2ErCvF,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAe,UAAA,SAAAoL,OAAA,CAAAM,YAAA,CAAAlH,UAAA,gBAAwC;IAqC/BvF,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAe,UAAA,cAAAoL,OAAA,CAAAC,iBAAA,CAAA7G,UAAA,EAAuC;;;;;;IAO7EvF,EAAA,CAAAC,cAAA,eAA0F;IAGpFD,EAAA,CAAAU,SAAA,aAA6B;IAC/BV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kEAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAElEH,EAAA,CAAAC,cAAA,eAAyB;IACnBD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,gBAA6B;IAEzBD,EAAA,CAAAU,SAAA,aAAmC;IACnCV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElCH,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,cAAqC;IACrCV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvCH,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,cAA6B;IAC7BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKjCH,EAAA,CAAAC,cAAA,mBAAiE;IAA/BD,EAAA,CAAAgC,UAAA,mBAAA0K,4EAAA;MAAA1M,EAAA,CAAAmC,aAAA,CAAAwK,KAAA;MAAA,MAAAC,QAAA,GAAA5M,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAmK,QAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAC9D7M,EAAA,CAAAU,SAAA,cAA2B;IAACV,EAAA,CAAAE,MAAA,uBAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA7gBjBH,EAAA,CAAAC,cAAA,eAAwE;IAKhED,EAAA,CAAAW,UAAA,IAAAmM,kDAAA,mBAGO;IACP9M,EAAA,CAAAW,UAAA,IAAAoM,kDAAA,mBAEO;IACT/M,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAW,UAAA,IAAAqM,+CAAA,gBAAwG;IACxGhN,EAAA,CAAAW,UAAA,IAAAsM,+CAAA,gBAQI;IACNjN,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAW,UAAA,IAAAuM,iDAAA,oBAmCM;IACRlN,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA2D;IAEnDD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,mBAA6D;IAA/BD,EAAA,CAAAgC,UAAA,mBAAAmL,qEAAA;MAAAnN,EAAA,CAAAmC,aAAA,CAAAiL,KAAA;MAAA,MAAAC,QAAA,GAAArN,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAA4K,QAAA,CAAA/I,kBAAA,EAAoB;IAAA,EAAC;IAC1DtE,EAAA,CAAAU,SAAA,cAA4B;IAC9BV,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,gBAA6B;IAGrBD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,gBAA0B;IAGhBD,EAAA,CAAAE,MAAA,eAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACfH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,kBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAMxBH,EAAA,CAAAC,cAAA,gBAA4B;IACtBD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAW,UAAA,KAAA2M,kDAAA,mBAKM;IACRtN,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,gBAA4B;IACtBD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,gBAA0B;IAGpBD,EAAA,CAAAU,SAAA,cAA4B;IAC9BV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,SAAA,cAA0B;IAACV,EAAA,CAAAE,MAAA,6BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE9DH,EAAA,CAAAC,cAAA,gBAAyB;IAErBD,EAAA,CAAAU,SAAA,cAA4B;IAC9BV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,SAAA,cAAgC;IAACV,EAAA,CAAAE,MAAA,+BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtEH,EAAA,CAAAC,cAAA,gBAAyB;IAErBD,EAAA,CAAAU,SAAA,cAA2B;IAC7BV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,iCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAK5CH,EAAA,CAAAC,cAAA,gBAA6B;IACGD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChDH,EAAA,CAAAC,cAAA,mBAA6D;IAA/BD,EAAA,CAAAgC,UAAA,mBAAAuL,qEAAA;MAAAvN,EAAA,CAAAmC,aAAA,CAAAiL,KAAA;MAAA,MAAAI,QAAA,GAAAxN,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAA+K,QAAA,CAAAlJ,kBAAA,EAAoB;IAAA,EAAC;IAACtE,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMzFH,EAAA,CAAAC,cAAA,gBAAyB;IAEvBD,EAAA,CAAAW,UAAA,KAAA8M,kDAAA,qBAwWM;IAGNzN,EAAA,CAAAW,UAAA,KAAA+M,kDAAA,oBA8BM;IACR1N,EAAA,CAAAG,YAAA,EAAM;;;;;;IA3gBOH,EAAA,CAAAI,SAAA,GAA4F;IAA5FJ,EAAA,CAAAe,UAAA,WAAA8B,OAAA,GAAA8K,OAAA,CAAA/L,UAAA,CAAAC,GAAA,wCAAAgB,OAAA,CAAAF,KAAA,OAAAE,OAAA,GAAA8K,OAAA,CAAA/L,UAAA,CAAAC,GAAA,sCAAAgB,OAAA,CAAAF,KAAA,EAA4F;IAI5F3C,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAe,UAAA,UAAAY,OAAA,GAAAgM,OAAA,CAAA/L,UAAA,CAAAC,GAAA,oCAAAF,OAAA,CAAAgB,KAAA,CAA4C;IAIjD3C,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAe,UAAA,SAAA4M,OAAA,CAAA5J,aAAA,CAAAC,MAAA,OAAgC;IAChChE,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAe,UAAA,SAAA4M,OAAA,CAAA5J,aAAA,CAAAC,MAAA,KAA8B;IAYNhE,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAe,UAAA,SAAA4M,OAAA,CAAA5J,aAAA,CAAAC,MAAA,KAA8B;IAuCnChE,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAA4C,WAAA,SAAA+K,OAAA,CAAAC,gBAAA,CAA+B;IAyBF5N,EAAA,CAAAI,SAAA,IAAsB;IAAtBJ,EAAA,CAAAe,UAAA,YAAA4M,OAAA,CAAAE,iBAAA,GAAsB;IA4CpD7N,EAAA,CAAAI,SAAA,IAAgB;IAAhBJ,EAAA,CAAAe,UAAA,YAAA4M,OAAA,CAAA5J,aAAA,CAAgB;IA2Wf/D,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAe,UAAA,UAAA4M,OAAA,CAAAG,SAAA,KAAAH,OAAA,CAAArK,YAAA,IAAAqK,OAAA,CAAA5J,aAAA,CAAAC,MAAA,OAA+D;;;;;IAhhB9FhE,EAAA,CAAAC,cAAA,eAA0D;IAExDD,EAAA,CAAAW,UAAA,IAAAoN,2CAAA,oBAYM;IAGN/N,EAAA,CAAAW,UAAA,IAAAqN,2CAAA,oBAWM;IAGNhO,EAAA,CAAAW,UAAA,IAAAsN,2CAAA,qBAihBM;IACRjO,EAAA,CAAAG,YAAA,EAAM;;;;IA/iBEH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAe,UAAA,SAAAmN,OAAA,CAAAJ,SAAA,CAAe;IAef9N,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAe,UAAA,UAAAmN,OAAA,CAAAJ,SAAA,IAAAI,OAAA,CAAA5K,YAAA,CAAgC;IAchCtD,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAe,UAAA,UAAAmN,OAAA,CAAAJ,SAAA,KAAAI,OAAA,CAAA5K,YAAA,CAAiC;;;ADlU7C,OAAM,MAAO6K,oBAAoB;EAmD/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IApDhB,KAAAC,kBAAkB,GAAqB,EAAE;IACzC,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAAX,SAAS,GAAG,KAAK;IACjB,KAAA/J,aAAa,GAAa,EAAE;IAC5B,KAAA2K,WAAW,GAAG,KAAK;IACnB,KAAApL,YAAY,GAAG,EAAE;IACjB,KAAAqL,YAAY,GAAG,EAAE;IAEjB;IACA,KAAAC,SAAS,GAAG,CACV;MAAEjM,KAAK,EAAE,WAAW;MAAEK,KAAK,EAAE;IAAY,CAAE,EAC3C;MAAEL,KAAK,EAAE,QAAQ;MAAEK,KAAK,EAAE;IAAS,CAAE,EACrC;MAAEL,KAAK,EAAE,WAAW;MAAEK,KAAK,EAAE;IAAY,CAAE,CAC5C;IACD,KAAA6L,gBAAgB,GAAG,WAAW;IAE9B;IACA,KAAAC,cAAc,GAAG,CACf;MAAEnM,KAAK,EAAE5C,aAAa,CAACgP,KAAK;MAAE/L,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAEL,KAAK,EAAE5C,aAAa,CAACiP,KAAK;MAAEhM,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAEL,KAAK,EAAE5C,aAAa,CAACkP,MAAM;MAAEjM,KAAK,EAAE;IAAQ,CAAE,CACjD;IAED;IACA,KAAAkM,aAAa,GAAG,CACd;MAAEvM,KAAK,EAAE9C,eAAe,CAACsP,KAAK;MAAEnM,KAAK,EAAE;IAAO,CAAE,EAChD;MAAEL,KAAK,EAAE9C,eAAe,CAACuP,OAAO;MAAEpM,KAAK,EAAE;IAAS,CAAE,EACpD;MAAEL,KAAK,EAAE9C,eAAe,CAACwP,QAAQ;MAAErM,KAAK,EAAE;IAAU,CAAE,CACvD;IAMD;IACA,KAAAsM,qBAAqB,GAAG,KAAK;IAC7B,KAAA1B,gBAAgB,GAAG,KAAK;IACxB,KAAAxI,iBAAiB,GAAG,KAAK;IACzB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAkK,gBAAgB,GAAG,IAAIC,GAAG,EAAkD;IAE5E;IACA,KAAAC,UAAU,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAI,CAAE;IAClC,KAAAC,kBAAkB,GAAG;MAAEF,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAI,CAAE;IAE1C;IACA,KAAAE,iBAAiB,GAAa,EAAE;IAChC,KAAAC,gBAAgB,GAAa,EAAE;IAO7B;IACA,IAAI,CAACC,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrD,IAAI,CAACxO,aAAa,GAAG,IAAI,CAACqO,OAAO;IAEjC;IACA,IAAI,CAACnO,UAAU,GAAG,IAAI,CAACyM,EAAE,CAAC8B,KAAK,CAAC;MAC9B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAE7Q,UAAU,CAAC8Q,QAAQ,CAAC;MACrCC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE/Q,UAAU,CAAC8Q,QAAQ,CAAC;MAC1CE,iBAAiB,EAAE,CAAC,EAAE,EAAEhR,UAAU,CAAC8Q,QAAQ,CAAC;MAC5CG,qBAAqB,EAAE,CAAC,CAAC,EAAEjR,UAAU,CAAC8Q,QAAQ,CAAC;MAC/CI,eAAe,EAAE,CAAC,EAAE,EAAElR,UAAU,CAAC8Q,QAAQ,CAAC;MAC1CK,mBAAmB,EAAE,CAAC,CAAC,EAAEnR,UAAU,CAAC8Q,QAAQ,CAAC;MAC7CM,aAAa,EAAE,CAAC,IAAI,CAACZ,OAAO,EAAExQ,UAAU,CAAC8Q,QAAQ,CAAC;MAClDO,UAAU,EAAE,CAAC,IAAI,CAACC,OAAO,CAAC,IAAI,CAACd,OAAO,EAAE,CAAC,CAAC,CAAC;MAC3C9H,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC1I,UAAU,CAAC8Q,QAAQ,EAAE9Q,UAAU,CAACmQ,GAAG,CAAC,CAAC,CAAC,EAAEnQ,UAAU,CAACoQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChF3H,aAAa,EAAE,CAAC,CAAC,EAAEzI,UAAU,CAAC8Q,QAAQ,CAAC;MAEvC;MACAzK,WAAW,EAAE,CAAC,CAAC,EAAErG,UAAU,CAAC8Q,QAAQ,CAAC;MACrCS,OAAO,EAAE,CAAC,KAAK,CAAC;MAEhB;MACAC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClB1I,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjB2I,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,wBAAwB,EAAE,CAAC,KAAK,CAAC;MACjCC,6BAA6B,EAAE,CAAC,IAAI,CAAC;MACrCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAE5B;MACAC,sBAAsB,EAAE,CAAC,CAAC;KAC3B,CAAC;IAEF;IACA,IAAI,CAACxP,UAAU,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEwP,YAAY,CAACC,SAAS,CAAC3K,IAAI,IAAG;MAClE,IAAI,CAACjF,aAAa,GAAGiF,IAAI;MACzB,MAAM4K,iBAAiB,GAAG,IAAI,CAAC3P,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEc,KAAK;MAClE,IAAI4O,iBAAiB,IAAI,IAAIvB,IAAI,CAACuB,iBAAiB,CAAC,GAAG,IAAIvB,IAAI,CAACrJ,IAAI,CAAC,EAAE;QACrE,IAAI,CAAC/E,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE2P,QAAQ,CAAC7K,IAAI,CAAC;;IAErD,CAAC,CAAC;EACJ;EAEA8K,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACAC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EACjF;EAEA;EACAC,cAAcA,CAACC,MAAc;IAC3B;IACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9CF,QAAQ,CAACG,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCJ,QAAQ,CAACG,KAAK,CAACE,GAAG,GAAG,GAAG;IACxBL,QAAQ,CAACG,KAAK,CAACG,IAAI,GAAG,GAAG;IACzBN,QAAQ,CAACG,KAAK,CAACI,KAAK,GAAG,MAAM;IAC7BP,QAAQ,CAACG,KAAK,CAACK,MAAM,GAAG,MAAM;IAC9BR,QAAQ,CAACG,KAAK,CAACM,eAAe,GAAG,oBAAoB;IACrDT,QAAQ,CAACG,KAAK,CAACO,MAAM,GAAG,MAAM;IAC9BV,QAAQ,CAACG,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC/BX,QAAQ,CAACG,KAAK,CAACS,cAAc,GAAG,QAAQ;IACxCZ,QAAQ,CAACG,KAAK,CAACU,UAAU,GAAG,QAAQ;IAEpC;IACA,MAAMC,YAAY,GAAGb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDY,YAAY,CAACX,KAAK,CAACM,eAAe,GAAG,OAAO;IAC5CK,YAAY,CAACX,KAAK,CAACY,OAAO,GAAG,MAAM;IACnCD,YAAY,CAACX,KAAK,CAACa,YAAY,GAAG,MAAM;IACxCF,YAAY,CAACX,KAAK,CAACc,QAAQ,GAAG,KAAK;IACnCH,YAAY,CAACX,KAAK,CAACe,SAAS,GAAG,KAAK;IACpCJ,YAAY,CAACX,KAAK,CAACgB,QAAQ,GAAG,MAAM;IACpCL,YAAY,CAACX,KAAK,CAACC,QAAQ,GAAG,UAAU;IACxCU,YAAY,CAACX,KAAK,CAACiB,SAAS,GAAG,gCAAgC;IAC/DN,YAAY,CAACX,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCP,YAAY,CAACX,KAAK,CAACmB,UAAU,GAAG,mBAAmB;IAEnD;IACA,MAAMC,WAAW,GAAGtB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACpDqB,WAAW,CAACC,SAAS,GAAG,SAAS;IACjCD,WAAW,CAACpB,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvCmB,WAAW,CAACpB,KAAK,CAACE,GAAG,GAAG,MAAM;IAC9BkB,WAAW,CAACpB,KAAK,CAACsB,KAAK,GAAG,MAAM;IAChCF,WAAW,CAACpB,KAAK,CAACuB,MAAM,GAAG,MAAM;IACjCH,WAAW,CAACpB,KAAK,CAACwB,UAAU,GAAG,MAAM;IACrCJ,WAAW,CAACpB,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACnCL,WAAW,CAACpB,KAAK,CAAC0B,MAAM,GAAG,SAAS;IACpCN,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnCE,WAAW,CAACpB,KAAK,CAAC2B,UAAU,GAAG,YAAY;IAC3CP,WAAW,CAACQ,WAAW,GAAG,MAAMR,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnEE,WAAW,CAACS,UAAU,GAAG,MAAMT,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IAClEE,WAAW,CAACU,OAAO,GAAG,MAAMhC,QAAQ,CAACiC,IAAI,CAACC,WAAW,CAACnC,QAAQ,CAAC;IAE/D;IACA,MAAMoC,MAAM,GAAGnC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5CkC,MAAM,CAACjC,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC7ByB,MAAM,CAACjC,KAAK,CAACU,UAAU,GAAG,QAAQ;IAClCuB,MAAM,CAACjC,KAAK,CAACkC,YAAY,GAAG,MAAM;IAClCD,MAAM,CAACjC,KAAK,CAACmC,aAAa,GAAG,MAAM;IACnCF,MAAM,CAACjC,KAAK,CAACoC,YAAY,GAAG,gBAAgB;IAE5C,MAAMC,IAAI,GAAGvC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CsC,IAAI,CAAChB,SAAS,GAAG,2FAA2F;IAE5G,MAAMiB,KAAK,GAAGxC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IAC1CuC,KAAK,CAACC,WAAW,GAAG,yBAAyB;IAC7CD,KAAK,CAACtC,KAAK,CAACwC,MAAM,GAAG,GAAG;IACxBF,KAAK,CAACtC,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC7Ba,KAAK,CAACtC,KAAK,CAACyC,UAAU,GAAG,KAAK;IAC9BH,KAAK,CAACtC,KAAK,CAACkB,KAAK,GAAG,SAAS;IAE7Be,MAAM,CAACS,WAAW,CAACL,IAAI,CAAC;IACxBJ,MAAM,CAACS,WAAW,CAACJ,KAAK,CAAC;IAEzB;IACA,MAAMK,gBAAgB,GAAG7C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACtD4C,gBAAgB,CAAC3C,KAAK,CAACQ,OAAO,GAAG,MAAM;IACvCmC,gBAAgB,CAAC3C,KAAK,CAAC4C,aAAa,GAAG,QAAQ;IAC/CD,gBAAgB,CAAC3C,KAAK,CAAC6C,GAAG,GAAG,MAAM;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9E;IACA,IAAInD,MAAM,CAACvM,KAAK,IAAIuM,MAAM,CAACvM,KAAK,CAACxB,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMmR,IAAI,GAAGpD,MAAM,CAACvM,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,IAAI2P,IAAI,CAAC1P,OAAO,EAAE;QAChB,MAAM2P,WAAW,GAAGnD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDkD,WAAW,CAACjD,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCyC,WAAW,CAACjD,KAAK,CAACU,UAAU,GAAG,QAAQ;QACvCuC,WAAW,CAACjD,KAAK,CAACkC,YAAY,GAAG,MAAM;QAEvC;QACA,IAAIc,IAAI,CAAC1P,OAAO,CAACC,aAAa,EAAE;UAC9B,MAAM2P,WAAW,GAAGpD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDmD,WAAW,CAACC,GAAG,GAAGH,IAAI,CAAC1P,OAAO,CAACC,aAAa;UAC5C2P,WAAW,CAACE,GAAG,GAAGJ,IAAI,CAAC1P,OAAO,CAACtE,IAAI;UACnCkU,WAAW,CAAClD,KAAK,CAACK,MAAM,GAAG,MAAM;UACjC6C,WAAW,CAAClD,KAAK,CAACqD,WAAW,GAAG,MAAM;UACtCJ,WAAW,CAACP,WAAW,CAACQ,WAAW,CAAC;SACrC,MAAM;UACL,MAAMI,WAAW,GAAGxD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDuD,WAAW,CAACjC,SAAS,GAAG,wFAAwF;UAChH4B,WAAW,CAACP,WAAW,CAACY,WAAW,CAAC;;QAGtC,MAAMC,WAAW,GAAGzD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDwD,WAAW,CAAClC,SAAS,GAAG,WAAW2B,IAAI,CAAC1P,OAAO,CAACtE,IAAI,cAAcgU,IAAI,CAAC1P,OAAO,CAACkQ,iBAAiB,GAAG;QACnGD,WAAW,CAACvD,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCwB,WAAW,CAACP,WAAW,CAACa,WAAW,CAAC;QAEpCT,WAAW,CAACJ,WAAW,CAACO,WAAW,CAAC;;MAGtC;MACA,MAAMQ,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC,eAAe,EAAEV,IAAI,CAAC5O,QAAQ,IAAI,KAAK,CAAC;MACnF0O,WAAW,CAACJ,WAAW,CAACe,eAAe,CAAC;MAExC;MACA,MAAME,aAAa,GAAG,IAAI,CAACD,aAAa,CAAC,aAAa,EAAE,IAAI7F,IAAI,CAACmF,IAAI,CAACY,UAAU,CAAC,CAACC,kBAAkB,EAAE,CAAC;MACvGf,WAAW,CAACJ,WAAW,CAACiB,aAAa,CAAC;MAEtC;MACA,MAAMG,WAAW,GAAG,IAAI,CAACJ,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC/O,cAAc,CAACqO,IAAI,CAACpO,QAAQ,CAAC,CAAC;MACtFkO,WAAW,CAACJ,WAAW,CAACoB,WAAW,CAAC;MAEpC;MACA,IAAId,IAAI,CAACvP,WAAW,EAAE;QACpB,MAAMsQ,QAAQ,GAAG,IAAI,CAACL,aAAa,CAAC,OAAO,EAAE,GAAGV,IAAI,CAACvP,WAAW,CAACzE,IAAI,KAAKgU,IAAI,CAACvP,WAAW,CAACrF,IAAI,GAAG,CAAC;QACnG0U,WAAW,CAACJ,WAAW,CAACqB,QAAQ,CAAC;;MAGnC;MACA,MAAMC,QAAQ,GAAG,IAAI,CAACN,aAAa,CAAC,OAAO,EAAEV,IAAI,CAACtP,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,GAAGsP,IAAI,CAACtP,SAAS,UAAU,CAAC;MAClHoP,WAAW,CAACJ,WAAW,CAACsB,QAAQ,CAAC;;IAGnC;IACA,MAAMC,YAAY,GAAG,IAAI,CAAClB,aAAa,CAAC,eAAe,EAAE,UAAU,CAAC;IAEpE,IAAInD,MAAM,CAACvM,KAAK,IAAIuM,MAAM,CAACvM,KAAK,CAACxB,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMmR,IAAI,GAAGpD,MAAM,CAACvM,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,MAAM6Q,WAAW,GAAGpE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACjDmE,WAAW,CAAClE,KAAK,CAACQ,OAAO,GAAG,MAAM;MAClC0D,WAAW,CAAClE,KAAK,CAACU,UAAU,GAAG,QAAQ;MACvCwD,WAAW,CAAClE,KAAK,CAACS,cAAc,GAAG,eAAe;MAClDyD,WAAW,CAAClE,KAAK,CAACwC,MAAM,GAAG,QAAQ;MACnC0B,WAAW,CAAClE,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEvC;MACA,MAAM1L,SAAS,GAAGuL,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/CxL,SAAS,CAACyL,KAAK,CAACmE,SAAS,GAAG,QAAQ;MACpC5P,SAAS,CAACyL,KAAK,CAACoE,IAAI,GAAG,GAAG;MAE1B,IAAIpB,IAAI,CAACzO,SAAS,EAAE;QAClB,MAAM8P,aAAa,GAAGvE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnDsE,aAAa,CAACrE,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC4C,aAAa,CAACrE,KAAK,CAACyC,UAAU,GAAG,MAAM;QACvC4B,aAAa,CAAC9B,WAAW,GAAG,IAAI1E,IAAI,CAACmF,IAAI,CAACzO,SAAS,CAACC,IAAI,CAAC,CAAC8P,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAErH,MAAMC,gBAAgB,GAAG3E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACtD0E,gBAAgB,CAACzE,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACxCgD,gBAAgB,CAACzE,KAAK,CAAC0E,SAAS,GAAG,KAAK;QACxCD,gBAAgB,CAACpD,SAAS,GAAG,WAAW2B,IAAI,CAACzO,SAAS,CAACG,OAAO,EAAEtG,IAAI,IAAI,KAAK,WAAW;QAExF,MAAMuW,aAAa,GAAG7E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnD4E,aAAa,CAAC3E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCkD,aAAa,CAAC3E,KAAK,CAACkB,KAAK,GAAG,MAAM;QAClCyD,aAAa,CAACpC,WAAW,GAAGS,IAAI,CAACzO,SAAS,CAAClG,IAAI,EAAEW,IAAI,IAAI,KAAK;QAE9DuF,SAAS,CAACmO,WAAW,CAAC2B,aAAa,CAAC;QACpC9P,SAAS,CAACmO,WAAW,CAAC+B,gBAAgB,CAAC;QACvClQ,SAAS,CAACmO,WAAW,CAACiC,aAAa,CAAC;;MAGtC;MACA,MAAMC,cAAc,GAAG9E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACpD6E,cAAc,CAAC5E,KAAK,CAACoE,IAAI,GAAG,GAAG;MAC/BQ,cAAc,CAAC5E,KAAK,CAACQ,OAAO,GAAG,MAAM;MACrCoE,cAAc,CAAC5E,KAAK,CAACU,UAAU,GAAG,QAAQ;MAC1CkE,cAAc,CAAC5E,KAAK,CAACS,cAAc,GAAG,QAAQ;MAC9CmE,cAAc,CAAC5E,KAAK,CAACY,OAAO,GAAG,QAAQ;MAEvC,MAAMiE,IAAI,GAAG/E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC1C8E,IAAI,CAAC7E,KAAK,CAACK,MAAM,GAAG,KAAK;MACzBwE,IAAI,CAAC7E,KAAK,CAACM,eAAe,GAAG,MAAM;MACnCuE,IAAI,CAAC7E,KAAK,CAACI,KAAK,GAAG,MAAM;MACzByE,IAAI,CAAC7E,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEhC,MAAM6E,KAAK,GAAGhF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC3C+E,KAAK,CAACzD,SAAS,GAAG,iGAAiG;MACnHyD,KAAK,CAAC9E,KAAK,CAACC,QAAQ,GAAG,UAAU;MACjC6E,KAAK,CAAC9E,KAAK,CAACE,GAAG,GAAG,MAAM;MACxB4E,KAAK,CAAC9E,KAAK,CAACG,IAAI,GAAG,KAAK;MACxB2E,KAAK,CAAC9E,KAAK,CAAC+E,UAAU,GAAG,MAAM;MAC/BD,KAAK,CAAC9E,KAAK,CAACM,eAAe,GAAG,OAAO;MACrCwE,KAAK,CAAC9E,KAAK,CAACY,OAAO,GAAG,OAAO;MAE7BiE,IAAI,CAACnC,WAAW,CAACoC,KAAK,CAAC;MACvBF,cAAc,CAAClC,WAAW,CAACmC,IAAI,CAAC;MAEhC;MACA,MAAMhQ,OAAO,GAAGiL,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7ClL,OAAO,CAACmL,KAAK,CAACmE,SAAS,GAAG,QAAQ;MAClCtP,OAAO,CAACmL,KAAK,CAACoE,IAAI,GAAG,GAAG;MAExB,IAAIpB,IAAI,CAACnO,OAAO,EAAE;QAChB,MAAMmQ,WAAW,GAAGlF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDiF,WAAW,CAAChF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCuD,WAAW,CAAChF,KAAK,CAACyC,UAAU,GAAG,MAAM;QACrCuC,WAAW,CAACzC,WAAW,GAAG,IAAI1E,IAAI,CAACmF,IAAI,CAACnO,OAAO,CAACL,IAAI,CAAC,CAAC8P,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAEjH,MAAMS,cAAc,GAAGnF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACpDkF,cAAc,CAACjF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACtCwD,cAAc,CAACjF,KAAK,CAAC0E,SAAS,GAAG,KAAK;QACtCO,cAAc,CAAC5D,SAAS,GAAG,WAAW2B,IAAI,CAACnO,OAAO,CAACH,OAAO,EAAEtG,IAAI,IAAI,KAAK,WAAW;QAEpF,MAAM8W,WAAW,GAAGpF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDmF,WAAW,CAAClF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCyD,WAAW,CAAClF,KAAK,CAACkB,KAAK,GAAG,MAAM;QAChCgE,WAAW,CAAC3C,WAAW,GAAGS,IAAI,CAACnO,OAAO,CAACxG,IAAI,EAAEW,IAAI,IAAI,KAAK;QAE1D6F,OAAO,CAAC6N,WAAW,CAACsC,WAAW,CAAC;QAChCnQ,OAAO,CAAC6N,WAAW,CAACuC,cAAc,CAAC;QACnCpQ,OAAO,CAAC6N,WAAW,CAACwC,WAAW,CAAC;;MAGlChB,WAAW,CAACxB,WAAW,CAACnO,SAAS,CAAC;MAClC2P,WAAW,CAACxB,WAAW,CAACkC,cAAc,CAAC;MACvCV,WAAW,CAACxB,WAAW,CAAC7N,OAAO,CAAC;MAEhCoP,YAAY,CAACvB,WAAW,CAACwB,WAAW,CAAC;MAErC;MACA,IAAIlB,IAAI,CAAC/N,QAAQ,IAAI+N,IAAI,CAAC/N,QAAQ,CAACpD,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMsT,aAAa,GAAGrF,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAClDoF,aAAa,CAAC5C,WAAW,GAAG,iBAAiB;QAC7C4C,aAAa,CAACnF,KAAK,CAAC0E,SAAS,GAAG,MAAM;QACtCS,aAAa,CAACnF,KAAK,CAACkC,YAAY,GAAG,MAAM;QACzCiD,aAAa,CAACnF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC0D,aAAa,CAACnF,KAAK,CAACyC,UAAU,GAAG,KAAK;QACtCwB,YAAY,CAACvB,WAAW,CAACyC,aAAa,CAAC;QAEvC,MAAMC,YAAY,GAAGtF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClDqF,YAAY,CAACpF,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnC4E,YAAY,CAACpF,KAAK,CAAC4C,aAAa,GAAG,QAAQ;QAC3CwC,YAAY,CAACpF,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/BG,IAAI,CAAC/N,QAAQ,CAACoQ,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAI;UACvC,MAAMC,WAAW,GAAG1F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDyF,WAAW,CAACxF,KAAK,CAACY,OAAO,GAAG,MAAM;UAClC4E,WAAW,CAACxF,KAAK,CAACM,eAAe,GAAG,SAAS;UAC7CkF,WAAW,CAACxF,KAAK,CAACa,YAAY,GAAG,KAAK;UACtC2E,WAAW,CAACxF,KAAK,CAACuB,MAAM,GAAG,gBAAgB;UAE3C,MAAMkE,aAAa,GAAG3F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACnD0F,aAAa,CAACzF,KAAK,CAACQ,OAAO,GAAG,MAAM;UACpCiF,aAAa,CAACzF,KAAK,CAACS,cAAc,GAAG,eAAe;UACpDgF,aAAa,CAACzF,KAAK,CAACkC,YAAY,GAAG,MAAM;UACzCuD,aAAa,CAACzF,KAAK,CAACmC,aAAa,GAAG,MAAM;UAC1CsD,aAAa,CAACzF,KAAK,CAACoC,YAAY,GAAG,gBAAgB;UAEnD,MAAMsD,YAAY,GAAG5F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClD2F,YAAY,CAACrE,SAAS,GAAG,mBAAmBkE,KAAK,GAAG,CAAC,cAAcD,OAAO,CAAChS,OAAO,EAAEtE,IAAI,IAAI,SAAS,IAAIsW,OAAO,CAAClR,QAAQ,EAAE;UAE3H,MAAMuR,eAAe,GAAG7F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACrD4F,eAAe,CAACpD,WAAW,GAAG,IAAI,CAAC5N,cAAc,CAAC2Q,OAAO,CAAC1Q,QAAQ,CAAC;UACnE+Q,eAAe,CAAC3F,KAAK,CAACkB,KAAK,GAAG,MAAM;UAEpCuE,aAAa,CAAC/C,WAAW,CAACgD,YAAY,CAAC;UACvCD,aAAa,CAAC/C,WAAW,CAACiD,eAAe,CAAC;UAC1CH,WAAW,CAAC9C,WAAW,CAAC+C,aAAa,CAAC;UAEtC;UACA,MAAMG,YAAY,GAAG9F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClD6F,YAAY,CAAC5F,KAAK,CAACQ,OAAO,GAAG,MAAM;UACnCoF,YAAY,CAAC5F,KAAK,CAACU,UAAU,GAAG,QAAQ;UACxCkF,YAAY,CAAC5F,KAAK,CAACS,cAAc,GAAG,eAAe;UAEnD;UACA,MAAMoF,gBAAgB,GAAG/F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACtD8F,gBAAgB,CAAC7F,KAAK,CAACoE,IAAI,GAAG,GAAG;UAEjC,IAAIkB,OAAO,CAAC/Q,SAAS,EAAE;YACrB,MAAMuR,OAAO,GAAGhG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7C+F,OAAO,CAAC9F,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCqD,OAAO,CAACvD,WAAW,GAAG,IAAI1E,IAAI,CAACyH,OAAO,CAAC/Q,SAAS,CAACC,IAAI,CAAC,CAAC8P,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAElH,MAAMuB,UAAU,GAAGjG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChDgG,UAAU,CAAC/F,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClCsE,UAAU,CAACxD,WAAW,GAAG,GAAG+C,OAAO,CAAC/Q,SAAS,CAACG,OAAO,EAAEtG,IAAI,IAAI,KAAK,KAAKkX,OAAO,CAAC/Q,SAAS,CAAClG,IAAI,EAAEW,IAAI,IAAI,KAAK,GAAG;YAEjH6W,gBAAgB,CAACnD,WAAW,CAACoD,OAAO,CAAC;YACrCD,gBAAgB,CAACnD,WAAW,CAACqD,UAAU,CAAC;;UAG1C;UACA,MAAMC,KAAK,GAAGlG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAC3CiG,KAAK,CAAC3E,SAAS,GAAG,qFAAqF;UAEvG;UACA,MAAM4E,cAAc,GAAGnG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACpDkG,cAAc,CAACjG,KAAK,CAACoE,IAAI,GAAG,GAAG;UAC/B6B,cAAc,CAACjG,KAAK,CAACmE,SAAS,GAAG,OAAO;UAExC,IAAImB,OAAO,CAACzQ,OAAO,EAAE;YACnB,MAAMqR,OAAO,GAAGpG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CmG,OAAO,CAAClG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCyD,OAAO,CAAC3D,WAAW,GAAG,IAAI1E,IAAI,CAACyH,OAAO,CAACzQ,OAAO,CAACL,IAAI,CAAC,CAAC8P,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAEhH,MAAM2B,UAAU,GAAGrG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChDoG,UAAU,CAACnG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClC0E,UAAU,CAAC5D,WAAW,GAAG,GAAG+C,OAAO,CAACzQ,OAAO,CAACH,OAAO,EAAEtG,IAAI,IAAI,KAAK,KAAKkX,OAAO,CAACzQ,OAAO,CAACxG,IAAI,EAAEW,IAAI,IAAI,KAAK,GAAG;YAE7GiX,cAAc,CAACvD,WAAW,CAACwD,OAAO,CAAC;YACnCD,cAAc,CAACvD,WAAW,CAACyD,UAAU,CAAC;;UAGxCP,YAAY,CAAClD,WAAW,CAACmD,gBAAgB,CAAC;UAC1CD,YAAY,CAAClD,WAAW,CAACsD,KAAK,CAAC;UAC/BJ,YAAY,CAAClD,WAAW,CAACuD,cAAc,CAAC;UACxCT,WAAW,CAAC9C,WAAW,CAACkD,YAAY,CAAC;UAErCR,YAAY,CAAC1C,WAAW,CAAC8C,WAAW,CAAC;UAErC;UACA,IAAID,KAAK,GAAGvC,IAAI,CAAC/N,QAAQ,CAACpD,MAAM,GAAG,CAAC,EAAE;YACpC,MAAMuU,OAAO,GAAGtG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CqG,OAAO,CAACpG,KAAK,CAACmE,SAAS,GAAG,QAAQ;YAClCiC,OAAO,CAACpG,KAAK,CAACY,OAAO,GAAG,MAAM;YAC9BwF,OAAO,CAACpG,KAAK,CAACkB,KAAK,GAAG,SAAS;YAC/BkF,OAAO,CAACpG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAE/B;YACA,MAAM4E,cAAc,GAAG,IAAIxI,IAAI,CAACyH,OAAO,CAACzQ,OAAO,EAAEL,IAAI,IAAI,CAAC,CAAC,CAAC8R,OAAO,EAAE;YACrE,MAAMC,aAAa,GAAG,IAAI1I,IAAI,CAACmF,IAAI,CAAC/N,QAAQ,CAACsQ,KAAK,GAAG,CAAC,CAAC,CAAChR,SAAS,EAAEC,IAAI,IAAI,CAAC,CAAC,CAAC8R,OAAO,EAAE;YACvF,MAAME,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,aAAa,GAAGF,cAAc,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;YAE9ED,OAAO,CAAC/E,SAAS,GAAG,yCAAyC,IAAI,CAAC1M,cAAc,CAAC6R,WAAW,CAAC,eAAelB,OAAO,CAACzQ,OAAO,EAAExG,IAAI,EAAEW,IAAI,IAAI,iBAAiB,EAAE;YAE9JoW,YAAY,CAAC1C,WAAW,CAAC0D,OAAO,CAAC;;QAErC,CAAC,CAAC;QAEFnC,YAAY,CAACvB,WAAW,CAAC0C,YAAY,CAAC;;;IAI1C;IACA,MAAMuB,aAAa,GAAG,IAAI,CAAC5D,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAE5D,IAAInD,MAAM,CAAC9L,MAAM,IAAI8L,MAAM,CAAC9L,MAAM,CAACjC,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAM+U,UAAU,GAAG9G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAChD6G,UAAU,CAAC5G,KAAK,CAACQ,OAAO,GAAG,MAAM;MACjCoG,UAAU,CAAC5G,KAAK,CAAC4C,aAAa,GAAG,QAAQ;MACzCgE,UAAU,CAAC5G,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE7BjD,MAAM,CAAC9L,MAAM,CAACuR,OAAO,CAAC,CAACwB,KAAK,EAAEtB,KAAK,KAAI;QACrC,MAAMuB,SAAS,GAAGhH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC/C+G,SAAS,CAAC9G,KAAK,CAACY,OAAO,GAAG,MAAM;QAChCkG,SAAS,CAAC9G,KAAK,CAACM,eAAe,GAAG,SAAS;QAC3CwG,SAAS,CAAC9G,KAAK,CAACa,YAAY,GAAG,KAAK;QACpCiG,SAAS,CAAC9G,KAAK,CAACuB,MAAM,GAAG,gBAAgB;QAEzC;QACA,MAAMwF,WAAW,GAAGjH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDgH,WAAW,CAAC/G,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCuG,WAAW,CAAC/G,KAAK,CAACS,cAAc,GAAG,eAAe;QAClDsG,WAAW,CAAC/G,KAAK,CAACkC,YAAY,GAAG,MAAM;QACvC6E,WAAW,CAAC/G,KAAK,CAACmC,aAAa,GAAG,MAAM;QACxC4E,WAAW,CAAC/G,KAAK,CAACoC,YAAY,GAAG,gBAAgB;QAEjD,MAAM4E,UAAU,GAAGlH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChDiH,UAAU,CAAC3F,SAAS,GAAG,iBAAiBkE,KAAK,GAAG,CAAC,WAAW;QAC5DyB,UAAU,CAAChH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAElC,MAAMwF,UAAU,GAAGnH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChDkH,UAAU,CAACjH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAClCwF,UAAU,CAACjH,KAAK,CAACyC,UAAU,GAAG,MAAM;QACpCwE,UAAU,CAACjH,KAAK,CAACkB,KAAK,GAAG,SAAS;QAClC+F,UAAU,CAAC1E,WAAW,GAAG,GAAGsE,KAAK,CAAC7Q,KAAK,CAACC,MAAM,IAAI4Q,KAAK,CAAC7Q,KAAK,CAACE,QAAQ,EAAE;QAExE6Q,WAAW,CAACrE,WAAW,CAACsE,UAAU,CAAC;QACnCD,WAAW,CAACrE,WAAW,CAACuE,UAAU,CAAC;QACnCH,SAAS,CAACpE,WAAW,CAACqE,WAAW,CAAC;QAElC;QACA,MAAMG,YAAY,GAAGpH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClDmH,YAAY,CAAClH,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnC0G,YAAY,CAAClH,KAAK,CAACmH,mBAAmB,GAAG,uCAAuC;QAChFD,YAAY,CAAClH,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B;QACA,MAAMuE,OAAO,GAAG,IAAI,CAAC1D,aAAa,CAAC,UAAU,EAAEmD,KAAK,CAACO,OAAO,IAAIP,KAAK,CAACQ,EAAE,IAAI,KAAK,CAAC;QAClFD,OAAO,CAACpH,KAAK,CAACsH,UAAU,GAAG,QAAQ;QACnCJ,YAAY,CAACxE,WAAW,CAAC0E,OAAO,CAAC;QAEjC;QACA,MAAMG,iBAAiB,GAAGV,KAAK,CAACW,YAAY,KAAKC,SAAS,GAAGZ,KAAK,CAACW,YAAY,GACrDX,KAAK,CAAC9S,QAAQ,GAAG8S,KAAK,CAAC9S,QAAQ,CAACC,kBAAkB,GAAG,CAAE;QACjF,MAAMwT,YAAY,GAAG,IAAI,CAAC9D,aAAa,CAAC,cAAc,EAAE6D,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,eAAe,CAAC;QAC9GL,YAAY,CAACxE,WAAW,CAAC8E,YAAY,CAAC;QAEtC;QACA,IAAIX,KAAK,CAACa,SAAS,EAAE;UACnB,MAAMC,OAAO,GAAG,IAAI,CAACjE,aAAa,CAAC,YAAY,EAAE,IAAI7F,IAAI,CAACgJ,KAAK,CAACa,SAAS,CAAC,CAACE,cAAc,EAAE,CAAC;UAC5FV,YAAY,CAACxE,WAAW,CAACiF,OAAO,CAAC;;QAGnC;QACA,IAAId,KAAK,CAACgB,WAAW,EAAE;UACrB,MAAMA,WAAW,GAAG,IAAI,CAACnE,aAAa,CAAC,cAAc,EAAEmD,KAAK,CAACgB,WAAW,CAAC7Y,IAAI,CAAC;UAC9EkY,YAAY,CAACxE,WAAW,CAACmF,WAAW,CAAC;;QAGvC;QACA,IAAIhB,KAAK,CAACiB,cAAc,EAAE;UACxB,MAAMC,UAAU,GAAG,IAAI,CAACrE,aAAa,CAAC,YAAY,EAAEmD,KAAK,CAACiB,cAAc,CAACC,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC;UACnGb,YAAY,CAACxE,WAAW,CAACqF,UAAU,CAAC;;QAGtCjB,SAAS,CAACpE,WAAW,CAACwE,YAAY,CAAC;QAEnC;QACA,IAAIL,KAAK,CAAChT,mBAAmB,IAAIgT,KAAK,CAAChT,mBAAmB,CAAChC,MAAM,GAAG,CAAC,EAAE;UACrE,MAAMmW,YAAY,GAAGlI,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;UACjDiI,YAAY,CAACzF,WAAW,GAAG,qBAAqB;UAChDyF,YAAY,CAAChI,KAAK,CAAC0E,SAAS,GAAG,MAAM;UACrCsD,YAAY,CAAChI,KAAK,CAACkC,YAAY,GAAG,MAAM;UACxC8F,YAAY,CAAChI,KAAK,CAACyB,QAAQ,GAAG,MAAM;UACpCuG,YAAY,CAAChI,KAAK,CAACyC,UAAU,GAAG,KAAK;UACrCqE,SAAS,CAACpE,WAAW,CAACsF,YAAY,CAAC;UAEnC,MAAMC,WAAW,GAAGnI,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;UAChDkI,WAAW,CAACjI,KAAK,CAACkI,SAAS,GAAG,MAAM;UACpCD,WAAW,CAACjI,KAAK,CAACY,OAAO,GAAG,GAAG;UAC/BqH,WAAW,CAACjI,KAAK,CAACwC,MAAM,GAAG,GAAG;UAE9BqE,KAAK,CAAChT,mBAAmB,CAACwR,OAAO,CAAC8C,OAAO,IAAG;YAC1C,MAAMC,WAAW,GAAGtI,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;YAChDqI,WAAW,CAACpI,KAAK,CAACkC,YAAY,GAAG,KAAK;YACtCkG,WAAW,CAAC/G,SAAS,GAAG,2EAA2E,IAAI,CAACtJ,kBAAkB,CAACoQ,OAAO,CAACtQ,WAAW,CAAC,EAAE;YACjJoQ,WAAW,CAACvF,WAAW,CAAC0F,WAAW,CAAC;UACtC,CAAC,CAAC;UAEFtB,SAAS,CAACpE,WAAW,CAACuF,WAAW,CAAC;;QAGpCrB,UAAU,CAAClE,WAAW,CAACoE,SAAS,CAAC;MACnC,CAAC,CAAC;MAEFH,aAAa,CAACjE,WAAW,CAACkE,UAAU,CAAC;;IAGvC;IACA,IAAIhH,MAAM,CAACvM,KAAK,IAAIuM,MAAM,CAACvM,KAAK,CAAC,CAAC,CAAC,IAAIuM,MAAM,CAACvM,KAAK,CAAC,CAAC,CAAC,CAAC+G,QAAQ,IAAIwF,MAAM,CAACvM,KAAK,CAAC,CAAC,CAAC,CAAC+G,QAAQ,CAACvI,MAAM,GAAG,CAAC,EAAE;MACtG,MAAMwW,eAAe,GAAG,IAAI,CAACtF,aAAa,CAAC,UAAU,EAAE,mBAAmB,CAAC;MAE3E,MAAMuF,YAAY,GAAGxI,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;MACjDuI,YAAY,CAACtI,KAAK,CAACkI,SAAS,GAAG,MAAM;MACrCI,YAAY,CAACtI,KAAK,CAACY,OAAO,GAAG,GAAG;MAChC0H,YAAY,CAACtI,KAAK,CAACwC,MAAM,GAAG,GAAG;MAC/B8F,YAAY,CAACtI,KAAK,CAACQ,OAAO,GAAG,MAAM;MACnC8H,YAAY,CAACtI,KAAK,CAACmH,mBAAmB,GAAG,uCAAuC;MAChFmB,YAAY,CAACtI,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE/BjD,MAAM,CAACvM,KAAK,CAAC,CAAC,CAAC,CAAC+G,QAAQ,CAACiL,OAAO,CAACkD,OAAO,IAAG;QACzC,MAAMC,WAAW,GAAG1I,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAChDyI,WAAW,CAACxI,KAAK,CAACY,OAAO,GAAG,MAAM;QAClC4H,WAAW,CAACxI,KAAK,CAACM,eAAe,GAAG,SAAS;QAC7CkI,WAAW,CAACxI,KAAK,CAACa,YAAY,GAAG,KAAK;QACtC2H,WAAW,CAACnH,SAAS,GAAG,2EAA2EkH,OAAO,CAACvZ,IAAI,IAAI,SAAS,EAAE;QAC9HsZ,YAAY,CAAC5F,WAAW,CAAC8F,WAAW,CAAC;MACvC,CAAC,CAAC;MAEFH,eAAe,CAAC3F,WAAW,CAAC4F,YAAY,CAAC;MACzC3F,gBAAgB,CAACD,WAAW,CAAC2F,eAAe,CAAC;;IAG/C;IACA1F,gBAAgB,CAACD,WAAW,CAACI,WAAW,CAAC;IACzCH,gBAAgB,CAACD,WAAW,CAACuB,YAAY,CAAC;IAC1CtB,gBAAgB,CAACD,WAAW,CAACiE,aAAa,CAAC;IAE3C;IACAhG,YAAY,CAAC+B,WAAW,CAACtB,WAAW,CAAC;IACrCT,YAAY,CAAC+B,WAAW,CAACT,MAAM,CAAC;IAChCtB,YAAY,CAAC+B,WAAW,CAACC,gBAAgB,CAAC;IAC1C9C,QAAQ,CAAC6C,WAAW,CAAC/B,YAAY,CAAC;IAElC;IACAb,QAAQ,CAACiC,IAAI,CAACW,WAAW,CAAC7C,QAAQ,CAAC;EACrC;EAEA;EACQkD,aAAaA,CAACT,KAAa,EAAEmG,SAAiB;IACpD,MAAMC,OAAO,GAAG5I,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7C2I,OAAO,CAAC1I,KAAK,CAACM,eAAe,GAAG,SAAS;IACzCoI,OAAO,CAAC1I,KAAK,CAACa,YAAY,GAAG,KAAK;IAClC6H,OAAO,CAAC1I,KAAK,CAACY,OAAO,GAAG,MAAM;IAC9B8H,OAAO,CAAC1I,KAAK,CAACiB,SAAS,GAAG,+BAA+B;IAEzD,MAAM0H,aAAa,GAAG7I,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACnD4I,aAAa,CAAC3I,KAAK,CAACQ,OAAO,GAAG,MAAM;IACpCmI,aAAa,CAAC3I,KAAK,CAACU,UAAU,GAAG,QAAQ;IACzCiI,aAAa,CAAC3I,KAAK,CAACkC,YAAY,GAAG,MAAM;IAEzC,MAAM0G,IAAI,GAAG9I,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC6I,IAAI,CAACC,SAAS,GAAG,OAAOJ,SAAS,EAAE;IACnCG,IAAI,CAAC5I,KAAK,CAACkB,KAAK,GAAG,SAAS;IAC5B0H,IAAI,CAAC5I,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC5BmH,IAAI,CAAC5I,KAAK,CAACqD,WAAW,GAAG,MAAM;IAE/B,MAAMyF,YAAY,GAAGhJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IACjD+I,YAAY,CAACvG,WAAW,GAAGD,KAAK;IAChCwG,YAAY,CAAC9I,KAAK,CAACwC,MAAM,GAAG,GAAG;IAC/BsG,YAAY,CAAC9I,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCqH,YAAY,CAAC9I,KAAK,CAACyC,UAAU,GAAG,KAAK;IAErCkG,aAAa,CAACjG,WAAW,CAACkG,IAAI,CAAC;IAC/BD,aAAa,CAACjG,WAAW,CAACoG,YAAY,CAAC;IACvCJ,OAAO,CAAChG,WAAW,CAACiG,aAAa,CAAC;IAElC,OAAOD,OAAO;EAChB;EAEA;EACQhF,aAAaA,CAAC7S,KAAa,EAAEL,KAAa;IAChD,MAAMuY,GAAG,GAAGjJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACzCgJ,GAAG,CAAC/I,KAAK,CAACkC,YAAY,GAAG,MAAM;IAE/B,MAAM8G,YAAY,GAAGlJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDiJ,YAAY,CAACzG,WAAW,GAAG1R,KAAK;IAChCmY,YAAY,CAAChJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCuH,YAAY,CAAChJ,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjC8H,YAAY,CAAChJ,KAAK,CAACkC,YAAY,GAAG,KAAK;IAEvC,MAAM+G,YAAY,GAAGnJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDkJ,YAAY,CAAC1G,WAAW,GAAG/R,KAAK;IAChCyY,YAAY,CAACjJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAEpCsH,GAAG,CAACrG,WAAW,CAACsG,YAAY,CAAC;IAC7BD,GAAG,CAACrG,WAAW,CAACuG,YAAY,CAAC;IAE7B,OAAOF,GAAG;EACZ;EAEA;EACAhP,gBAAgBA,CAAC6F,MAAc;IAC7B,IAAIA,MAAM,IAAIA,MAAM,CAAC9L,MAAM,IAAI8L,MAAM,CAAC9L,MAAM,CAACjC,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAIuV,OAAO,GAAGxH,MAAM,CAAC9L,MAAM,CAAC,CAAC,CAAC,CAACsT,OAAO,IAAIxH,MAAM,CAAC9L,MAAM,CAAC,CAAC,CAAC,CAACuT,EAAE;MAE7D;MACA,IAAI6B,QAAQ,GAAG,IAAI,CAAC1M,YAAY;MAEhC;MACA,IAAI,CAAC0M,QAAQ,EAAE;QACbA,QAAQ,GAAGtJ,MAAM,CAACyH,EAAE;;MAGtB5H,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEwJ,QAAQ,EAAE,cAAc,EAAE9B,OAAO,CAAC;MAExF;MACA,IAAI,CAAChL,MAAM,CAAC+M,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UACXF,QAAQ,EAAEA,QAAQ;UAClB9B,OAAO,EAAEA;;OAEZ,CAAC;KACH,MAAM;MACL3H,OAAO,CAAC4J,KAAK,CAAC,sCAAsC,EAAEzJ,MAAM,CAAC;;EAEjE;EAEAJ,gBAAgBA,CAAA;IACd;IACA;IACA;EAAA;EAGFD,iBAAiBA,CAAA;IACf;IACA,MAAMlB,qBAAqB,GAAG,IAAI,CAAC5O,UAAU,CAACC,GAAG,CAAC,uBAAuB,CAAC,EAAEc,KAAK,IAAI,CAAC;IACtF,MAAM+N,mBAAmB,GAAG,IAAI,CAAC9O,UAAU,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAEc,KAAK,IAAI,CAAC;IAElF,IAAI,CAAC2L,cAAc,CAACmN,kBAAkB,CAACjL,qBAAqB,CAAC,CAACc,SAAS,CAACoK,SAAS,IAAG;MAClF,IAAI,CAAClN,kBAAkB,GAAGkN,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAACpN,cAAc,CAACmN,kBAAkB,CAAC/K,mBAAmB,CAAC,CAACY,SAAS,CAACoK,SAAS,IAAG;MAChF,IAAI,CAACjN,gBAAgB,GAAGiN,SAAS;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC9Z,UAAU,CAACC,GAAG,CAAC,uBAAuB,CAAC,EAAEwP,YAAY,CACvDC,SAAS,CAACqK,YAAY,IAAG;MACxB,IAAI,CAACrN,cAAc,CAACmN,kBAAkB,CAACE,YAAY,CAAC,CAACrK,SAAS,CAACoK,SAAS,IAAG;QACzE,IAAI,CAAClN,kBAAkB,GAAGkN,SAAS;QACnC;QACA,IAAI,CAAC9Z,UAAU,CAACC,GAAG,CAAC,mBAAmB,CAAC,EAAE2P,QAAQ,CAAC,EAAE,CAAC;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC5P,UAAU,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAEwP,YAAY,CACrDC,SAAS,CAACqK,YAAY,IAAG;MACxB,IAAI,CAACrN,cAAc,CAACmN,kBAAkB,CAACE,YAAY,CAAC,CAACrK,SAAS,CAACoK,SAAS,IAAG;QACzE,IAAI,CAACjN,gBAAgB,GAAGiN,SAAS;QACjC;QACA,IAAI,CAAC9Z,UAAU,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAE2P,QAAQ,CAAC,EAAE,CAAC;MACtD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC5P,UAAU,CAACC,GAAG,CAAC,mBAAmB,CAAC,EAAEwP,YAAY,CACnDuK,IAAI,CACHpc,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACiD,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMgZ,YAAY,GAAG,IAAI,CAAC/Z,UAAU,CAACC,GAAG,CAAC,uBAAuB,CAAC,EAAEc,KAAK,IAAI,CAAC;QAC7E;QACA,IAAIA,KAAK,CAACqB,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACsK,cAAc,CAACmN,kBAAkB,CAACE,YAAY,CAAC,CAACC,IAAI,CAC9Djc,GAAG,CAAC+b,SAAS,IAAIA,SAAS,CAACG,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAAC3a,IAAI,CAAC4a,WAAW,EAAE,CAACC,QAAQ,CAACrZ,KAAK,CAACoZ,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACvb,IAAI,IAAIub,QAAQ,CAACvb,IAAI,CAACwb,WAAW,EAAE,CAACC,QAAQ,CAACrZ,KAAK,CAACoZ,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAACzN,cAAc,CAACmN,kBAAkB,CAACE,YAAY,CAAC;;;MAG/D,OAAO/b,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA0R,SAAS,CAACoK,SAAS,IAAG;MACrB,IAAI,CAAClN,kBAAkB,GAAGkN,SAAS;IACrC,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC9Z,UAAU,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEwP,YAAY,CACjDuK,IAAI,CACHpc,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACiD,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMgZ,YAAY,GAAG,IAAI,CAAC/Z,UAAU,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAEc,KAAK,IAAI,CAAC;QAC3E;QACA,IAAIA,KAAK,CAACqB,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACsK,cAAc,CAACmN,kBAAkB,CAACE,YAAY,CAAC,CAACC,IAAI,CAC9Djc,GAAG,CAAC+b,SAAS,IAAIA,SAAS,CAACG,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAAC3a,IAAI,CAAC4a,WAAW,EAAE,CAACC,QAAQ,CAACrZ,KAAK,CAACoZ,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACvb,IAAI,IAAIub,QAAQ,CAACvb,IAAI,CAACwb,WAAW,EAAE,CAACC,QAAQ,CAACrZ,KAAK,CAACoZ,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAACzN,cAAc,CAACmN,kBAAkB,CAACE,YAAY,CAAC;;;MAG/D,OAAO/b,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA0R,SAAS,CAACoK,SAAS,IAAG;MACrB,IAAI,CAACjN,gBAAgB,GAAGiN,SAAS;IACnC,CAAC,CAAC;EACN;EAEAjY,eAAeA,CAACqY,QAA+B;IAC7C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAIG,WAAW,GAAGH,QAAQ,CAAC3a,IAAI;IAC/B,IAAI2a,QAAQ,CAACvb,IAAI,EAAE;MACjB0b,WAAW,IAAI,KAAKH,QAAQ,CAACvb,IAAI,GAAG;;IAEtC,IAAIub,QAAQ,CAAC5a,IAAI,KAAKpB,YAAY,CAACoc,OAAO,IAAIJ,QAAQ,CAACtb,IAAI,EAAE;MAC3Dyb,WAAW,IAAI,MAAMH,QAAQ,CAACtb,IAAI,EAAE;;IAEtC,OAAOyb,WAAW;EACpB;EAEA7Y,QAAQA,CAAA;IACN,IAAI,IAAI,CAACxB,UAAU,CAACE,OAAO,EAAE;MAC3B,IAAI,CAACqa,oBAAoB,CAAC,IAAI,CAACva,UAAU,CAAC;MAC1C;;IAGF,IAAI,CAACkM,SAAS,GAAG,IAAI;IACrB,IAAI,CAACxK,YAAY,GAAG,EAAE;IACtB,IAAI,CAACoL,WAAW,GAAG,IAAI;IAEvB,MAAM0N,SAAS,GAAG,IAAI,CAACxa,UAAU,CAACe,KAAK;IAEvC;IACA,MAAM0Z,OAAO,GAAuB;MAClCC,WAAW,EAAEF,SAAS,CAAChM,WAAW;MAClCmM,YAAY,EAAEH,SAAS,CAAC9L,YAAY;MACpCkM,OAAO,EAAEJ,SAAS,CAACzL,aAAa;MAChC8L,kBAAkB,EAAE,CAClB;QACEjD,EAAE,EAAE4C,SAAS,CAAC7L,iBAAiB,EAAEiJ,EAAE,IAAI,EAAE;QACzCtY,IAAI,EAAEkb,SAAS,CAAC5L;OACjB,CACF;MACDkM,gBAAgB,EAAE,CAChB;QACElD,EAAE,EAAE4C,SAAS,CAAC3L,eAAe,EAAE+I,EAAE,IAAI,EAAE;QACvCtY,IAAI,EAAEkb,SAAS,CAAC1L;OACjB,CACF;MACDiM,UAAU,EAAE,CACV;QACEzb,IAAI,EAAEkb,SAAS,CAACpU,aAAa;QAC7B4U,KAAK,EAAER,SAAS,CAACnU;OAClB,CACF;MACD4U,qBAAqB,EAAET,SAAS,CAACtL,OAAO;MACxCgM,oBAAoB,EAAE;QACpBC,oBAAoB,EAAE;UACpB3L,sBAAsB,EAAEgL,SAAS,CAAChL;;OAErC;MACDJ,sBAAsB,EAAEoL,SAAS,CAACpL,sBAAsB;MACxDC,wBAAwB,EAAEmL,SAAS,CAACnL,wBAAwB;MAC5DC,6BAA6B,EAAEkL,SAAS,CAAClL,6BAA6B;MACtEC,mBAAmB,EAAEiL,SAAS,CAACjL,mBAAmB;MAClDjC,aAAa,EAAE,CAACkN,SAAS,CAACxW,WAAW,CAAC;MACtCoX,OAAO,EAAEZ,SAAS,CAACrL,OAAO;MAC1BkM,QAAQ,EAAEb,SAAS,CAAC/T;KACrB;IAED,IAAI,CAACiG,cAAc,CAAC4O,WAAW,CAACb,OAAO,CAAC,CACrC/K,SAAS,CAAC;MACT6L,IAAI,EAAGC,QAA6B,IAAI;QACtC,IAAI,CAACtP,SAAS,GAAG,KAAK;QACtB,IAAIsP,QAAQ,CAAChJ,MAAM,CAACiJ,OAAO,EAAE;UAC3B,IAAI,CAACtZ,aAAa,GAAGqZ,QAAQ,CAAClJ,IAAI,CAACoJ,OAAO;UAE1C;UACA1L,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0L,IAAI,CAACC,SAAS,CAACJ,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEpE;UACA,IAAIA,QAAQ,CAAClJ,IAAI,IAAIkJ,QAAQ,CAAClJ,IAAI,CAACoJ,OAAO,IAAIF,QAAQ,CAAClJ,IAAI,CAACoJ,OAAO,CAACtZ,MAAM,GAAG,CAAC,EAAE;YAC9E4N,OAAO,CAACzB,KAAK,CAAC,uBAAuB,CAAC;YACtCyB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEuL,QAAQ,CAAClJ,IAAI,CAACoJ,OAAO,CAACtZ,MAAM,CAAC;YAE3D;YACA,MAAMyZ,iBAAiB,GAAGL,QAAQ,CAAClJ,IAAI,CAACoJ,OAAO,CAACzB,MAAM,CAAC6B,CAAC,IAAIA,CAAC,CAACzX,MAAM,IAAIyX,CAAC,CAACzX,MAAM,CAACjC,MAAM,GAAG,CAAC,CAAC;YAC5F4N,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE4L,iBAAiB,CAACzZ,MAAM,CAAC;YAE7D;YACA,MAAM2Z,kBAAkB,GAAGF,iBAAiB,CAACG,OAAO,CAACF,CAAC,IAAIA,CAAC,CAACzX,MAAM,CAACtG,GAAG,CAACke,CAAC,IACtEA,CAAC,CAAClE,YAAY,KAAKC,SAAS,GAAGiE,CAAC,CAAClE,YAAY,GAAIkE,CAAC,CAAC3X,QAAQ,GAAG2X,CAAC,CAAC3X,QAAQ,CAACC,kBAAkB,GAAG,CAAE,CACjG,CAAC;YACFyL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8L,kBAAkB,CAAC;YAEvD;YACA,MAAMG,kBAAkB,GAAGH,kBAAkB,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAI;cAChE,IAAIA,GAAG,KAAKrE,SAAS,EAAE;gBACrBoE,GAAG,CAACC,GAAG,CAAC,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEhC,OAAOD,GAAG;YACZ,CAAC,EAAE,EAA4B,CAAC;YAChCpM,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEiM,kBAAkB,CAAC;YAEvD;YACA,MAAMI,iBAAiB,GAAGT,iBAAiB,CAAC5B,MAAM,CAAC6B,CAAC,IAClDA,CAAC,CAACzX,MAAM,CAACkY,IAAI,CAACN,CAAC,IAAIA,CAAC,CAAC5D,cAAc,IAAI4D,CAAC,CAAC5D,cAAc,CAACC,UAAU,KAAK,IAAI,CAAC,CAC7E;YACDtI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEqM,iBAAiB,CAACla,MAAM,CAAC;YAE5D4N,OAAO,CAACwM,QAAQ,EAAE;;UAGpB;UACA,IAAIhB,QAAQ,CAAClJ,IAAI,IAAIkJ,QAAQ,CAAClJ,IAAI,CAACmH,QAAQ,EAAE;YAC3C,IAAI,CAAC1M,YAAY,GAAGyO,QAAQ,CAAClJ,IAAI,CAACmH,QAAQ;YAC1CzJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAClD,YAAY,CAAC;;UAErE;UAAA,KACK,IAAIyO,QAAQ,CAAChJ,MAAM,IAAIgJ,QAAQ,CAAChJ,MAAM,CAACiK,SAAS,EAAE;YACrD,IAAI,CAAC1P,YAAY,GAAGyO,QAAQ,CAAChJ,MAAM,CAACiK,SAAS;YAC7CzM,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAClD,YAAY,CAAC;;UAExE;UAAA,KACK,IAAIyO,QAAQ,CAAClJ,IAAI,IAAIkJ,QAAQ,CAAClJ,IAAI,CAACoJ,OAAO,IAAIF,QAAQ,CAAClJ,IAAI,CAACoJ,OAAO,CAACtZ,MAAM,GAAG,CAAC,IAAIoZ,QAAQ,CAAClJ,IAAI,CAACoJ,OAAO,CAAC,CAAC,CAAC,CAAC9D,EAAE,EAAE;YAClH,IAAI,CAAC7K,YAAY,GAAGyO,QAAQ,CAAClJ,IAAI,CAACoJ,OAAO,CAAC,CAAC,CAAC,CAAC9D,EAAE;YAC/C5H,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAClD,YAAY,CAAC;WAChE,MAAM;YACLiD,OAAO,CAAC4J,KAAK,CAAC,qCAAqC,CAAC;YACpD5J,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEyM,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAC,CAAC;YACzD,IAAIA,QAAQ,CAAClJ,IAAI,EAAEtC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEyM,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAClJ,IAAI,CAAC,CAAC;YAC7E,IAAIkJ,QAAQ,CAAChJ,MAAM,EAAExC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEyM,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAChJ,MAAM,CAAC,CAAC;;SAEtF,MAAM;UACL,IAAI,CAAC9Q,YAAY,GAAG,sDAAsD;UAC1E,IAAI8Z,QAAQ,CAAChJ,MAAM,CAACoK,QAAQ,IAAIpB,QAAQ,CAAChJ,MAAM,CAACoK,QAAQ,CAACxa,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAACV,YAAY,GAAG8Z,QAAQ,CAAChJ,MAAM,CAACoK,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACDjD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC1N,SAAS,GAAG,KAAK;QACtB,IAAI,CAACxK,YAAY,GAAG,wDAAwD;QAC5EsO,OAAO,CAAC4J,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEA;EACAW,oBAAoBA,CAACuC,SAAoB;IACvCJ,MAAM,CAACK,MAAM,CAACD,SAAS,CAACE,QAAQ,CAAC,CAACpH,OAAO,CAACqH,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAYvf,SAAS,EAAE;QAChC,IAAI,CAAC6c,oBAAoB,CAAC0C,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA;EACA/X,cAAcA,CAACiY,OAAe;IAC5B,MAAMC,KAAK,GAAGpG,IAAI,CAACC,KAAK,CAACkG,OAAO,GAAG,EAAE,CAAC;IACtC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,KAAK,KAAKC,IAAI,GAAG;EAC7B;EAEArY,UAAUA,CAACsY,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,MAAMvY,IAAI,GAAG,IAAIqJ,IAAI,CAACkP,UAAU,CAAC;IACjC,OAAOvY,IAAI,CAACqP,kBAAkB,CAAC,OAAO,EAAE;MAAEmJ,GAAG,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAO,CAAE,CAAC;EAC7E;EAEAzb,cAAcA,CAACub,UAAkB;IAC/B,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,MAAMvY,IAAI,GAAG,IAAIqJ,IAAI,CAACkP,UAAU,CAAC;IACjC,OAAOvY,IAAI,CAACqP,kBAAkB,CAAC,OAAO,EAAE;MAAEqJ,OAAO,EAAE,MAAM;MAAEF,GAAG,EAAE,SAAS;MAAEC,KAAK,EAAE,MAAM;MAAEE,IAAI,EAAE;IAAS,CAAE,CAAC;EAC9G;EAEA7Y,UAAUA,CAACyY,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,MAAMvY,IAAI,GAAG,IAAIqJ,IAAI,CAACkP,UAAU,CAAC;IACjC,OAAOvY,IAAI,CAAC8P,kBAAkB,CAAC,OAAO,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAE4I,MAAM,EAAE;IAAI,CAAE,CAAC;EAC/F;EAEAjT,WAAWA,CAACyF,MAAc;IACxB,IAAI,CAACA,MAAM,CAAC9L,MAAM,IAAI8L,MAAM,CAAC9L,MAAM,CAACjC,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IAE9D;IACA,MAAMwb,QAAQ,GAAGzN,MAAM,CAAC9L,MAAM,CAAC8X,MAAM,CAAC,CAACrO,GAAG,EAAEsJ,KAAK,KAC9CA,KAAK,CAAC7Q,KAAK,IAAI6Q,KAAK,CAAC7Q,KAAK,CAACC,MAAM,GAAGsH,GAAG,CAACvH,KAAK,CAACC,MAAM,GAAI4Q,KAAK,GAAGtJ,GAAG,EAAEqC,MAAM,CAAC9L,MAAM,CAAC,CAAC,CAAC,CAAC;IAEzF,OAAO,GAAGuZ,QAAQ,CAACrX,KAAK,CAACC,MAAM,IAAIoX,QAAQ,CAACrX,KAAK,CAACE,QAAQ,EAAE;EAC9D;EAEA+D,iBAAiBA,CAAC2F,MAAc;IAC9B,IAAI,CAACA,MAAM,CAAC9L,MAAM,IAAI8L,MAAM,CAAC9L,MAAM,CAACjC,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IAE9D;IACA,OAAO+N,MAAM,CAAC9L,MAAM,CAACkY,IAAI,CAACnF,KAAK,IAAG;MAChC;MACA,IAAIA,KAAK,CAAC9S,QAAQ,IAAI8S,KAAK,CAAC9S,QAAQ,CAACC,kBAAkB,KAAKyT,SAAS,EAAE;QACrE,OAAOZ,KAAK,CAAC9S,QAAQ,CAACC,kBAAkB,GAAG,CAAC;;MAG9C;MACA,IAAI6S,KAAK,CAACW,YAAY,KAAKC,SAAS,EAAE;QACpC,OAAOZ,KAAK,CAACW,YAAY,KAAK,CAAC,CAAC,CAAC;;MAGnC;MACA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;EAEA8F,yBAAyBA,CAAA;IACvB,IAAI,CAACnR,cAAc,CAACoR,eAAe,EAAE,CAACpO,SAAS,CAACoK,SAAS,IAAG;MAC1D,IAAI,CAAClN,kBAAkB,GAAGkN,SAAS;IACrC,CAAC,CAAC;EACJ;EAEAiE,uBAAuBA,CAAA;IACrB,IAAI,CAACrR,cAAc,CAACoR,eAAe,EAAE,CAACpO,SAAS,CAACoK,SAAS,IAAG;MAC1D,IAAI,CAACjN,gBAAgB,GAAGiN,SAAS;IACnC,CAAC,CAAC;EACJ;EAEAkE,aAAaA,CAAA;IACX,MAAMC,cAAc,GAAG,IAAI,CAACje,UAAU,CAACC,GAAG,CAAC,mBAAmB,CAAC,EAAEc,KAAK;IACtE,MAAMmd,kBAAkB,GAAG,IAAI,CAACle,UAAU,CAACC,GAAG,CAAC,uBAAuB,CAAC,EAAEc,KAAK;IAC9E,MAAMod,YAAY,GAAG,IAAI,CAACne,UAAU,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEc,KAAK;IAClE,MAAMqd,gBAAgB,GAAG,IAAI,CAACpe,UAAU,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAEc,KAAK;IAE1E,IAAI,CAACf,UAAU,CAACC,GAAG,CAAC,mBAAmB,CAAC,EAAE2P,QAAQ,CAACuO,YAAY,CAAC;IAChE,IAAI,CAACne,UAAU,CAACC,GAAG,CAAC,uBAAuB,CAAC,EAAE2P,QAAQ,CAACwO,gBAAgB,CAAC;IACxE,IAAI,CAACpe,UAAU,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAE2P,QAAQ,CAACqO,cAAc,CAAC;IAChE,IAAI,CAACje,UAAU,CAACC,GAAG,CAAC,qBAAqB,CAAC,EAAE2P,QAAQ,CAACsO,kBAAkB,CAAC;EAC1E;EAEAG,oBAAoBA,CAACf,UAAkB;IACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,MAAMvY,IAAI,GAAG,IAAIqJ,IAAI,CAACkP,UAAU,CAAC;IACjC,OAAOvY,IAAI,CAACoT,cAAc,EAAE;EAC9B;EAEA7P,kBAAkBA,CAACF,WAAmB;IACpC,QAAQA,WAAW;MACjB,KAAK,CAAC;QAAE,OAAO,iBAAiB;MAChC,KAAK,CAAC;QAAE,OAAO,eAAe;MAC9B;QAAS,OAAO,SAAS;;EAE7B;EAEAlC,oBAAoBA,CAACE,aAAqB;IACxC,QAAQA,aAAa;MACnB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,SAAS;;EAE7B;EAEAb,oBAAoBA,CAAC+Y,cAAmB,EAAEC,WAAgB;IACxD,IAAI,CAACD,cAAc,IAAI,CAACC,WAAW,IAAI,CAACD,cAAc,CAAClZ,OAAO,IAAI,CAACmZ,WAAW,CAACzZ,SAAS,EAAE;MACxF,OAAO,KAAK;;IAGd,MAAMyQ,WAAW,GAAG,IAAInH,IAAI,CAACkQ,cAAc,CAAClZ,OAAO,CAACL,IAAI,CAAC,CAAC8R,OAAO,EAAE;IACnE,MAAMjC,aAAa,GAAG,IAAIxG,IAAI,CAACmQ,WAAW,CAACzZ,SAAS,CAACC,IAAI,CAAC,CAAC8R,OAAO,EAAE;IACpE,MAAM2H,cAAc,GAAGxH,IAAI,CAACC,KAAK,CAAC,CAACrC,aAAa,GAAGW,WAAW,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAE9E,OAAO,IAAI,CAACrQ,cAAc,CAACsZ,cAAc,CAAC;EAC5C;EAEAC,uBAAuBA,CAAA;IACrB,IAAI,CAAC/Q,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEAgR,mBAAmBA,CAAA;IACjB,MAAMC,YAAY,GAAG,IAAI,CAAC3e,UAAU,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAEc,KAAK;IACjE,IAAI4d,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAAC3e,UAAU,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAE2P,QAAQ,CAAC+O,YAAY,GAAG,CAAC,CAAC;;EAErE;EAEAC,mBAAmBA,CAAA;IACjB,MAAMD,YAAY,GAAG,IAAI,CAAC3e,UAAU,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAEc,KAAK;IACjE,IAAI4d,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAAC3e,UAAU,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAE2P,QAAQ,CAAC+O,YAAY,GAAG,CAAC,CAAC;;EAErE;EAEA7d,iBAAiBA,CAACC,KAAa;IAC7B,IAAI,CAACf,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAE2P,QAAQ,CAAC7O,KAAK,CAAC;EACrD;EAEAsB,kBAAkBA,CAACtB,KAAa;IAC9B,MAAMiD,WAAW,GAAG,IAAI,CAACsJ,aAAa,CAACuR,IAAI,CAACC,EAAE,IAAIA,EAAE,CAAC/d,KAAK,KAAKA,KAAK,CAAC;IACrE,OAAOiD,WAAW,GAAGA,WAAW,CAAC5C,KAAK,GAAG,SAAS;EACpD;EAEAsB,kBAAkBA,CAAA;IAChB,IAAI,CAACsJ,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEA/I,uBAAuBA,CAAA;IACrB,IAAI,CAACO,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAChD,IAAI,CAACub,YAAY,EAAE;EACrB;EAEA1b,qBAAqBA,CAAA;IACnB,IAAI,CAACI,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,CAACsb,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACjS,WAAW,IAAI,IAAI,CAAC3K,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;IAE1D;IACA,IAAI,IAAI,CAAC4L,kBAAkB,CAACF,GAAG,GAAG,CAAC,IAAI,IAAI,CAACE,kBAAkB,CAACD,GAAG,GAAG,IAAI,EAAE;MACzE;MACA;MACAiC,OAAO,CAACC,GAAG,CAAC,6BAA6B,IAAI,CAACjC,kBAAkB,CAACF,GAAG,MAAM,IAAI,CAACE,kBAAkB,CAACD,GAAG,EAAE,CAAC;;IAG1G;IACA,IAAI,IAAI,CAACvK,iBAAiB,EAAE;MAC1B;MACAwM,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;IAGlD;IACA,IAAI,IAAI,CAACxM,eAAe,EAAE;MACxB;MACAuM,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;;EAE9D;EAEA1G,mBAAmBA,CAAC4G,MAAc;IAChC,MAAM6O,QAAQ,GAAG7O,MAAM,CAACyH,EAAE,IAAI,EAAE;IAChC,IAAI,CAAC,IAAI,CAACjK,gBAAgB,CAACsR,GAAG,CAACD,QAAQ,CAAC,EAAE;MACxC,IAAI,CAACrR,gBAAgB,CAACuR,GAAG,CAACF,QAAQ,EAAE;QAAEG,MAAM,EAAE,IAAI;QAAEC,SAAS,EAAE;MAAQ,CAAE,CAAC;KAC3E,MAAM;MACL,MAAMC,OAAO,GAAG,IAAI,CAAC1R,gBAAgB,CAAC1N,GAAG,CAAC+e,QAAQ,CAAC;MACnD,IAAIK,OAAO,EAAE;QACXA,OAAO,CAACF,MAAM,GAAG,CAACE,OAAO,CAACF,MAAM;QAChC,IAAI,CAACxR,gBAAgB,CAACuR,GAAG,CAACF,QAAQ,EAAEK,OAAO,CAAC;;;EAGlD;EAEAzU,mBAAmBA,CAACuF,MAAc;IAChC,MAAM6O,QAAQ,GAAG7O,MAAM,CAACyH,EAAE,IAAI,EAAE;IAChC,OAAO,IAAI,CAACjK,gBAAgB,CAACsR,GAAG,CAACD,QAAQ,CAAC,IAAI,IAAI,CAACrR,gBAAgB,CAAC1N,GAAG,CAAC+e,QAAQ,CAAC,EAAEG,MAAM,KAAK,IAAI;EACpG;EAEAvV,YAAYA,CAACuG,MAAc,EAAEmP,GAAW;IACtC,MAAMN,QAAQ,GAAG7O,MAAM,CAACyH,EAAE,IAAI,EAAE;IAChC,IAAI,IAAI,CAACjK,gBAAgB,CAACsR,GAAG,CAACD,QAAQ,CAAC,EAAE;MACvC,MAAMK,OAAO,GAAG,IAAI,CAAC1R,gBAAgB,CAAC1N,GAAG,CAAC+e,QAAQ,CAAC;MACnD,IAAIK,OAAO,EAAE;QACXA,OAAO,CAACD,SAAS,GAAGE,GAAG;QACvB,IAAI,CAAC3R,gBAAgB,CAACuR,GAAG,CAACF,QAAQ,EAAEK,OAAO,CAAC;;KAE/C,MAAM;MACL,IAAI,CAAC1R,gBAAgB,CAACuR,GAAG,CAACF,QAAQ,EAAE;QAAEG,MAAM,EAAE,IAAI;QAAEC,SAAS,EAAEE;MAAG,CAAE,CAAC;;EAEzE;EAEAzU,YAAYA,CAACsF,MAAc;IACzB,MAAM6O,QAAQ,GAAG7O,MAAM,CAACyH,EAAE,IAAI,EAAE;IAChC,OAAO,IAAI,CAACjK,gBAAgB,CAACsR,GAAG,CAACD,QAAQ,CAAC,GAAG,IAAI,CAACrR,gBAAgB,CAAC1N,GAAG,CAAC+e,QAAQ,CAAC,EAAEI,SAAS,IAAI,QAAQ,GAAG,QAAQ;EACpH;EAEAnU,kBAAkBA,CAAA;IAChB,MAAMjL,UAAU,GAAGqQ,QAAQ,CAACkP,aAAa,CAAC,wBAAwB,CAAC;IACnE,IAAIvf,UAAU,EAAE;MACdA,UAAU,CAACwf,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;;EAErD;EAEAC,WAAWA,CAACC,QAAgB;IAC1B,IAAI,CAAC1S,gBAAgB,GAAG0S,QAAQ;IAEhC,IAAIA,QAAQ,KAAK,QAAQ,EAAE;MACzB,IAAI,CAAC3f,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE2f,eAAe,EAAE;MACpD,IAAI,CAAC5f,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE4f,sBAAsB,EAAE;KAC5D,MAAM,IAAIF,QAAQ,KAAK,WAAW,EAAE;MACnC,IAAI,CAAC3f,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE6f,aAAa,CAAC,CAACniB,UAAU,CAAC8Q,QAAQ,CAAC,CAAC;MACvE,IAAI,CAACzO,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE4f,sBAAsB,EAAE;;EAE/D;EAEA5Q,OAAOA,CAAClK,IAAY,EAAEgb,IAAY;IAChC,MAAMC,MAAM,GAAG,IAAI5R,IAAI,CAACrJ,IAAI,CAAC;IAC7Bib,MAAM,CAACC,OAAO,CAACD,MAAM,CAACE,OAAO,EAAE,GAAGH,IAAI,CAAC;IACvC,OAAOC,MAAM,CAAC3R,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3C;EAEA6R,aAAaA,CAAA;IACX,IAAI,CAACnS,kBAAkB,GAAG;MAAE,GAAG,IAAI,CAACH;IAAU,CAAE;IAChD,IAAI,CAACkR,YAAY,EAAE;EACrB;EAEAjc,WAAWA,CAACsd,KAAU;IACpB,MAAMC,MAAM,GAAGD,KAAK,CAACE,MAAM,CAACvf,KAAK;IACjCiP,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoQ,MAAM,CAAC;IAE1C;IACA,QAAQA,MAAM;MACZ,KAAK,OAAO;QACV,IAAI,CAACle,aAAa,CAACoe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAC/B,MAAMC,MAAM,GAAGF,CAAC,CAACnc,MAAM,IAAImc,CAAC,CAACnc,MAAM,CAAC,CAAC,CAAC,GAAGmc,CAAC,CAACnc,MAAM,CAAC,CAAC,CAAC,CAACkC,KAAK,CAACC,MAAM,GAAG,CAAC;UACrE,MAAMma,MAAM,GAAGF,CAAC,CAACpc,MAAM,IAAIoc,CAAC,CAACpc,MAAM,CAAC,CAAC,CAAC,GAAGoc,CAAC,CAACpc,MAAM,CAAC,CAAC,CAAC,CAACkC,KAAK,CAACC,MAAM,GAAG,CAAC;UACrE,OAAOka,MAAM,GAAGC,MAAM;QACxB,CAAC,CAAC;QACF;MACF,KAAK,UAAU;QACb,IAAI,CAACxe,aAAa,CAACoe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAC/B,MAAMG,SAAS,GAAGJ,CAAC,CAAC5c,KAAK,IAAI4c,CAAC,CAAC5c,KAAK,CAAC,CAAC,CAAC,GAAG4c,CAAC,CAAC5c,KAAK,CAAC,CAAC,CAAC,CAACuB,QAAQ,GAAG,CAAC;UACjE,MAAM0b,SAAS,GAAGJ,CAAC,CAAC7c,KAAK,IAAI6c,CAAC,CAAC7c,KAAK,CAAC,CAAC,CAAC,GAAG6c,CAAC,CAAC7c,KAAK,CAAC,CAAC,CAAC,CAACuB,QAAQ,GAAG,CAAC;UACjE,OAAOyb,SAAS,GAAGC,SAAS;QAC9B,CAAC,CAAC;QACF;MACF,KAAK,WAAW;QACd,IAAI,CAAC1e,aAAa,CAACoe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAC/B,MAAMK,KAAK,GAAGN,CAAC,CAAC5c,KAAK,IAAI4c,CAAC,CAAC5c,KAAK,CAAC,CAAC,CAAC,IAAI4c,CAAC,CAAC5c,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,GAAG,IAAIsJ,IAAI,CAACoS,CAAC,CAAC5c,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,CAACC,IAAI,CAAC,CAAC8R,OAAO,EAAE,GAAG,CAAC;UAC/G,MAAMkK,KAAK,GAAGN,CAAC,CAAC7c,KAAK,IAAI6c,CAAC,CAAC7c,KAAK,CAAC,CAAC,CAAC,IAAI6c,CAAC,CAAC7c,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,GAAG,IAAIsJ,IAAI,CAACqS,CAAC,CAAC7c,KAAK,CAAC,CAAC,CAAC,CAACkB,SAAS,CAACC,IAAI,CAAC,CAAC8R,OAAO,EAAE,GAAG,CAAC;UAC/G,OAAOiK,KAAK,GAAGC,KAAK;QACtB,CAAC,CAAC;QACF;MACF,KAAK,SAAS;QACZ,IAAI,CAAC5e,aAAa,CAACoe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAC/B,MAAMK,KAAK,GAAGN,CAAC,CAAC5c,KAAK,IAAI4c,CAAC,CAAC5c,KAAK,CAAC,CAAC,CAAC,IAAI4c,CAAC,CAAC5c,KAAK,CAAC,CAAC,CAAC,CAACwB,OAAO,GAAG,IAAIgJ,IAAI,CAACoS,CAAC,CAAC5c,KAAK,CAAC,CAAC,CAAC,CAACwB,OAAO,CAACL,IAAI,CAAC,CAAC8R,OAAO,EAAE,GAAG,CAAC;UAC3G,MAAMkK,KAAK,GAAGN,CAAC,CAAC7c,KAAK,IAAI6c,CAAC,CAAC7c,KAAK,CAAC,CAAC,CAAC,IAAI6c,CAAC,CAAC7c,KAAK,CAAC,CAAC,CAAC,CAACwB,OAAO,GAAG,IAAIgJ,IAAI,CAACqS,CAAC,CAAC7c,KAAK,CAAC,CAAC,CAAC,CAACwB,OAAO,CAACL,IAAI,CAAC,CAAC8R,OAAO,EAAE,GAAG,CAAC;UAC3G,OAAOiK,KAAK,GAAGC,KAAK;QACtB,CAAC,CAAC;QACF;;EAEN;;;uBAzrCWxU,oBAAoB,EAAAnO,EAAA,CAAA4iB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9iB,EAAA,CAAA4iB,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhjB,EAAA,CAAA4iB,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApB/U,oBAAoB;MAAAgV,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjCzjB,EAAA,CAAAC,cAAA,aAAoC;UAIPD,EAAA,CAAAE,MAAA,mCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,WAAyB;UAAAD,EAAA,CAAAE,MAAA,sDAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIhFH,EAAA,CAAAC,cAAA,aAA4B;UAKpBD,EAAA,CAAAU,SAAA,YAA4B;UAACV,EAAA,CAAAE,MAAA,iBAC/B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0B;UACxBD,EAAA,CAAAU,SAAA,aAA4B;UAACV,EAAA,CAAAE,MAAA,gBAC/B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0B;UACxBD,EAAA,CAAAU,SAAA,aAA0B;UAACV,EAAA,CAAAE,MAAA,cAC7B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0B;UACxBD,EAAA,CAAAU,SAAA,aAA+B;UAACV,EAAA,CAAAE,MAAA,kBAClC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,gBAA2E;UAA5CD,EAAA,CAAAgC,UAAA,sBAAA2hB,wDAAA;YAAA,OAAYD,GAAA,CAAAtgB,QAAA,EAAU;UAAA,EAAC;UAEpDpD,EAAA,CAAAC,cAAA,eAAgC;UAC2CD,EAAA,CAAAgC,UAAA,mBAAA4hB,oDAAA;YAAA,OAASF,GAAA,CAAApC,WAAA,CAAY,WAAW,CAAC;UAAA,EAAC;UACzGthB,EAAA,CAAAU,SAAA,aAAmC;UAACV,EAAA,CAAAE,MAAA,oBACtC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAsG;UAAhCD,EAAA,CAAAgC,UAAA,mBAAA6hB,oDAAA;YAAA,OAASH,GAAA,CAAApC,WAAA,CAAY,QAAQ,CAAC;UAAA,EAAC;UACnGthB,EAAA,CAAAU,SAAA,aAA2C;UAACV,EAAA,CAAAE,MAAA,iBAC9C;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA4G;UAAnCD,EAAA,CAAAgC,UAAA,mBAAA8hB,oDAAA;YAAA,OAASJ,GAAA,CAAApC,WAAA,CAAY,WAAW,CAAC;UAAA,EAAC;UACzGthB,EAAA,CAAAU,SAAA,aAA4B;UAACV,EAAA,CAAAE,MAAA,oBAC/B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAyB;UAEvBD,EAAA,CAAAU,SAAA,iBAA6D;UAG7DV,EAAA,CAAAC,cAAA,eAAyB;UAGUD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAU,SAAA,aAAsC;UACtCV,EAAA,CAAAC,cAAA,iBAQC;UADCD,EAAA,CAAAgC,UAAA,mBAAA+hB,sDAAA;YAAA,OAASL,GAAA,CAAAjE,yBAAA,EAA2B;UAAA,EAAC;UAPvCzf,EAAA,CAAAG,YAAA,EAQC;UAEHH,EAAA,CAAAC,cAAA,gCAA8F;UAC5FD,EAAA,CAAAW,UAAA,KAAAqjB,2CAAA,2BAoBa;UACfhkB,EAAA,CAAAG,YAAA,EAAmB;UACnBH,EAAA,CAAAW,UAAA,KAAAsjB,oCAAA,kBAEM;UACRjkB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAmC;UACgBD,EAAA,CAAAgC,UAAA,mBAAAkiB,uDAAA;YAAA,OAASR,GAAA,CAAA9D,aAAA,EAAe;UAAA,EAAC;UACxE5f,EAAA,CAAAU,SAAA,aAAmC;UACrCV,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAAuC;UACRD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvCH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAU,SAAA,aAAoC;UACpCV,EAAA,CAAAC,cAAA,iBAQC;UADCD,EAAA,CAAAgC,UAAA,mBAAAmiB,sDAAA;YAAA,OAAST,GAAA,CAAA/D,uBAAA,EAAyB;UAAA,EAAC;UAPrC3f,EAAA,CAAAG,YAAA,EAQC;UAEHH,EAAA,CAAAC,cAAA,gCAA4F;UAC1FD,EAAA,CAAAW,UAAA,KAAAyjB,2CAAA,2BAoBa;UACfpkB,EAAA,CAAAG,YAAA,EAAmB;UACnBH,EAAA,CAAAW,UAAA,KAAA0jB,oCAAA,kBAEM;UACRrkB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAmC;UACND,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAU,SAAA,aAAmC;UAQrCV,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAW,UAAA,KAAA2jB,oCAAA,kBAEM;UACRtkB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAW,UAAA,KAAA4jB,oCAAA,kBAeM;UAGNvkB,EAAA,CAAAC,cAAA,eAAwC;UAC/BD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,eAAiF;UAApCD,EAAA,CAAAgC,UAAA,mBAAAwiB,oDAAA;YAAA,OAASd,GAAA,CAAArD,uBAAA,EAAyB;UAAA,EAAC;UAC9ErgB,EAAA,CAAAU,SAAA,aAA4B;UAC5BV,EAAA,CAAAC,cAAA,eAA+B;UACvBD,EAAA,CAAAE,MAAA,IAAyD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtEH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA8D;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE7EH,EAAA,CAAAU,SAAA,aAAmC;UACrCV,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAqE;UAE7DD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,eAA+B;UACvBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnBH,EAAA,CAAAC,cAAA,eAA8B;UACND,EAAA,CAAAgC,UAAA,mBAAAyiB,uDAAA;YAAA,OAASf,GAAA,CAAAlD,mBAAA,EAAqB;UAAA,EAAC;UAA2DxgB,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1HH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC1DH,EAAA,CAAAC,cAAA,kBAAgH;UAA1FD,EAAA,CAAAgC,UAAA,mBAAA0iB,uDAAA;YAAA,OAAShB,GAAA,CAAApD,mBAAA,EAAqB;UAAA,EAAC;UAA2DtgB,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKhIH,EAAA,CAAAC,cAAA,eAA8B;UACxBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAW,UAAA,KAAAgkB,oCAAA,kBAKM;UACR3kB,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA8B;UACYD,EAAA,CAAAgC,UAAA,mBAAA4iB,uDAAA;YAAA,OAASlB,GAAA,CAAArD,uBAAA,EAAyB;UAAA,EAAC;UAACrgB,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMhGH,EAAA,CAAAC,cAAA,gBAAqC;UAMjCD,EAAA,CAAAW,UAAA,MAAAkkB,mCAAA,gBAAgD;UAChD7kB,EAAA,CAAAW,UAAA,MAAAmkB,sCAAA,mBAA8C;UAC9C9kB,EAAA,CAAAW,UAAA,MAAAokB,qCAAA,kBAEM;UACR/kB,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,gBAAwC;UAGlCD,EAAA,CAAAU,SAAA,cAAgC;UAACV,EAAA,CAAAE,MAAA,uBACnC;UAAAF,EAAA,CAAAG,YAAA,EAAU;UACVH,EAAA,CAAAC,cAAA,gBAA8B;UAKtBD,EAAA,CAAAU,SAAA,kBAKC;UACDV,EAAA,CAAAC,cAAA,kBAA0C;UACxCD,EAAA,CAAAU,SAAA,iBAAkC;UAClCV,EAAA,CAAAC,cAAA,iBAAkC;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAMlEH,EAAA,CAAAC,cAAA,gBAAwB;UACAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAU,SAAA,cAAsC;UACtCV,EAAA,CAAAC,cAAA,mBAAsE;UAChDD,EAAA,CAAAE,MAAA,sBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvCH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,uBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM5CH,EAAA,CAAAC,cAAA,gBAAwB;UACDD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrCH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAU,SAAA,cAA+B;UAC/BV,EAAA,CAAAC,cAAA,mBAAoE;UAC5CD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,mBAAsB;UAAAD,EAAA,CAAAE,MAAA,sBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvCH,EAAA,CAAAC,cAAA,mBAAsB;UAAAD,EAAA,CAAAE,MAAA,qBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM5CH,EAAA,CAAAC,cAAA,gBAAwB;UACcD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3DH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAU,SAAA,cAA+B;UAC/BV,EAAA,CAAAC,cAAA,mBAAkG;UAC5ED,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClDH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAY9DH,EAAA,CAAAW,UAAA,MAAAqkB,qCAAA,kBAijBM;UACRhlB,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;;;UA30BIH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAe,UAAA,cAAA2iB,GAAA,CAAA9hB,UAAA,CAAwB;UAGH5B,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAA4C,WAAA,WAAA8gB,GAAA,CAAA7U,gBAAA,iBAAiD;UAGjD7O,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA4C,WAAA,WAAA8gB,GAAA,CAAA7U,gBAAA,cAA8C;UAG9C7O,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAA4C,WAAA,WAAA8gB,GAAA,CAAA7U,gBAAA,iBAAiD;UAsBhE7O,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAAe,UAAA,oBAAAkkB,GAAA,CAAiC;UAKcjlB,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAe,UAAA,gBAAA2iB,GAAA,CAAAjgB,eAAA,CAAAyhB,IAAA,CAAAxB,GAAA,EAA0C;UAC1D1jB,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAe,UAAA,YAAA2iB,GAAA,CAAAlV,kBAAA,CAAqB;UAsBlDxO,EAAA,CAAAI,SAAA,GAAkG;UAAlGJ,EAAA,CAAAe,UAAA,WAAAokB,OAAA,GAAAzB,GAAA,CAAA9hB,UAAA,CAAAC,GAAA,wCAAAsjB,OAAA,CAAArjB,OAAA,OAAAqjB,OAAA,GAAAzB,GAAA,CAAA9hB,UAAA,CAAAC,GAAA,wCAAAsjB,OAAA,CAAApjB,OAAA,EAAkG;UAsBpG/B,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAe,UAAA,oBAAAqkB,GAAA,CAA+B;UAKcplB,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAe,UAAA,gBAAA2iB,GAAA,CAAAjgB,eAAA,CAAAyhB,IAAA,CAAAxB,GAAA,EAA0C;UACxD1jB,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAe,UAAA,YAAA2iB,GAAA,CAAAjV,gBAAA,CAAmB;UAsBhDzO,EAAA,CAAAI,SAAA,GAA8F;UAA9FJ,EAAA,CAAAe,UAAA,WAAAskB,QAAA,GAAA3B,GAAA,CAAA9hB,UAAA,CAAAC,GAAA,sCAAAwjB,QAAA,CAAAvjB,OAAA,OAAAujB,QAAA,GAAA3B,GAAA,CAAA9hB,UAAA,CAAAC,GAAA,sCAAAwjB,QAAA,CAAAtjB,OAAA,EAA8F;UAchG/B,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAe,UAAA,QAAA2iB,GAAA,CAAA3T,OAAA,CAAe;UAIS/P,EAAA,CAAAI,SAAA,GAA0F;UAA1FJ,EAAA,CAAAe,UAAA,WAAAukB,QAAA,GAAA5B,GAAA,CAAA9hB,UAAA,CAAAC,GAAA,oCAAAyjB,QAAA,CAAAxjB,OAAA,OAAAwjB,QAAA,GAAA5B,GAAA,CAAA9hB,UAAA,CAAAC,GAAA,oCAAAyjB,QAAA,CAAAvjB,OAAA,EAA0F;UAMpF/B,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAe,UAAA,SAAA2iB,GAAA,CAAA7U,gBAAA,iBAAsC;UAuB9D7O,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAA+C,kBAAA,MAAAwiB,QAAA,GAAA7B,GAAA,CAAA9hB,UAAA,CAAAC,GAAA,qCAAA0jB,QAAA,CAAA5iB,KAAA,iBAAyD;UACzD3C,EAAA,CAAAI,SAAA,GAA8D;UAA9DJ,EAAA,CAAAK,iBAAA,CAAAqjB,GAAA,CAAAzf,kBAAA,EAAAuhB,QAAA,GAAA9B,GAAA,CAAA9hB,UAAA,CAAAC,GAAA,kCAAA2jB,QAAA,CAAA7iB,KAAA,EAA8D;UAMxC3C,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAA4C,WAAA,SAAA8gB,GAAA,CAAApU,qBAAA,CAAoC;UAMNtP,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAAe,UAAA,eAAA0kB,QAAA,GAAA/B,GAAA,CAAA9hB,UAAA,CAAAC,GAAA,qCAAA4jB,QAAA,CAAA9iB,KAAA,OAAyD;UACzG3C,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAK,iBAAA,EAAAqlB,QAAA,GAAAhC,GAAA,CAAA9hB,UAAA,CAAAC,GAAA,qCAAA6jB,QAAA,CAAA/iB,KAAA,CAA6C;UACG3C,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAAe,UAAA,eAAA4kB,QAAA,GAAAjC,GAAA,CAAA9hB,UAAA,CAAAC,GAAA,qCAAA8jB,QAAA,CAAAhjB,KAAA,OAAyD;UAQpF3C,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAe,UAAA,YAAA2iB,GAAA,CAAAxU,aAAA,CAAgB;UAoBjDlP,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAe,UAAA,aAAA2iB,GAAA,CAAA9hB,UAAA,CAAAE,OAAA,IAAA4hB,GAAA,CAAA5V,SAAA,CAA4C;UAElB9N,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAe,UAAA,UAAA2iB,GAAA,CAAA5V,SAAA,CAAgB;UACnC9N,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAe,UAAA,UAAA2iB,GAAA,CAAA5V,SAAA,CAAgB;UACjB9N,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAe,UAAA,SAAA2iB,GAAA,CAAA5V,SAAA,CAAe;UAgEP9N,EAAA,CAAAI,SAAA,IAAW;UAAXJ,EAAA,CAAAe,UAAA,YAAW;UACXf,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAe,UAAA,YAAW;UACXf,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAe,UAAA,YAAW;UAYEf,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAe,UAAA,SAAA2iB,GAAA,CAAAhV,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}