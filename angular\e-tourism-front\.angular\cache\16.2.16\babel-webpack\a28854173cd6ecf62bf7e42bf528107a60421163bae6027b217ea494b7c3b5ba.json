{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction LoginComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction LoginComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(authService, router, snackBar) {\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.credentials = {\n        Agency: '',\n        User: '',\n        Password: ''\n      };\n      this.loading = false;\n      this.error = '';\n      this.hidePassword = true;\n    }\n    ngOnInit() {\n      // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n      this.authService.logout();\n      // Animation d'entrée\n      document.body.classList.add('login-page-active');\n    }\n    onSubmit() {\n      if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n        this.error = 'Please fill in all required fields.';\n        return;\n      }\n      this.loading = true;\n      this.error = '';\n      this.authService.login(this.credentials).subscribe({\n        next: response => {\n          this.loading = false;\n          if (response.header.success) {\n            this.snackBar.open('Login successful! Welcome back.', 'Close', {\n              duration: 3000,\n              panelClass: 'success-snackbar',\n              verticalPosition: 'top'\n            });\n            this.router.navigate(['/accueil']);\n          } else {\n            this.error = 'Authentication failed. Please check your credentials.';\n          }\n        },\n        error: err => {\n          this.loading = false;\n          this.error = 'An error occurred during login. Please try again.';\n          console.error('Login error:', err);\n        }\n      });\n    }\n    ngOnDestroy() {\n      document.body.classList.remove('login-page-active');\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        decls: 46,\n        vars: 9,\n        consts: [[1, \"login-page\"], [1, \"login-background\"], [1, \"login-shapes\"], [1, \"shape\", \"shape-1\"], [1, \"shape\", \"shape-2\"], [1, \"shape\", \"shape-3\"], [1, \"login-container\"], [1, \"login-card\", \"animate-slide-up\"], [1, \"login-logo\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"login-header\"], [1, \"login-form\", 3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"agency\"], [1, \"input-with-icon\"], [1, \"fas\", \"fa-building\"], [\"type\", \"text\", \"id\", \"agency\", \"name\", \"agency\", \"required\", \"\", \"placeholder\", \"Enter your agency name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"username\"], [1, \"fas\", \"fa-user\"], [\"type\", \"text\", \"id\", \"username\", \"name\", \"username\", \"required\", \"\", \"placeholder\", \"Enter your username\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [1, \"fas\", \"fa-lock\"], [\"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"type\", \"ngModel\", \"ngModelChange\"], [\"type\", \"button\", 1, \"password-toggle\", 3, \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"login-footer\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"spinner-container\"], [1, \"spinner\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵelement(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8);\n            i0.ɵɵelement(9, \"i\", 9);\n            i0.ɵɵelementStart(10, \"h1\");\n            i0.ɵɵtext(11, \"E-Tourism\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"div\", 10)(13, \"h2\");\n            i0.ɵɵtext(14, \"Welcome Back\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"p\");\n            i0.ɵɵtext(16, \"Sign in to continue to your account\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"form\", 11, 12);\n            i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_17_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(19, \"div\", 13)(20, \"label\", 14);\n            i0.ɵɵtext(21, \"Agency\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 15);\n            i0.ɵɵelement(23, \"i\", 16);\n            i0.ɵɵelementStart(24, \"input\", 17);\n            i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_24_listener($event) {\n              return ctx.credentials.Agency = $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(25, \"div\", 13)(26, \"label\", 18);\n            i0.ɵɵtext(27, \"Username\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"div\", 15);\n            i0.ɵɵelement(29, \"i\", 19);\n            i0.ɵɵelementStart(30, \"input\", 20);\n            i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_30_listener($event) {\n              return ctx.credentials.User = $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"div\", 13)(32, \"label\", 21);\n            i0.ɵɵtext(33, \"Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"div\", 15);\n            i0.ɵɵelement(35, \"i\", 22);\n            i0.ɵɵelementStart(36, \"input\", 23);\n            i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_36_listener($event) {\n              return ctx.credentials.Password = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_37_listener() {\n              return ctx.hidePassword = !ctx.hidePassword;\n            });\n            i0.ɵɵelement(38, \"i\", 25);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(39, LoginComponent_div_39_Template, 4, 1, \"div\", 26);\n            i0.ɵɵelementStart(40, \"button\", 27);\n            i0.ɵɵtemplate(41, LoginComponent_div_41_Template, 2, 0, \"div\", 28);\n            i0.ɵɵtemplate(42, LoginComponent_span_42_Template, 2, 0, \"span\", 29);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(43, \"div\", 30)(44, \"p\");\n            i0.ɵɵtext(45, \"\\u00A9 2023 E-Tourism. All rights reserved.\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            const _r0 = i0.ɵɵreference(18);\n            i0.ɵɵadvance(24);\n            i0.ɵɵproperty(\"ngModel\", ctx.credentials.Agency);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngModel\", ctx.credentials.User);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\")(\"ngModel\", ctx.credentials.Password);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngClass\", ctx.hidePassword ? \"fa-eye\" : \"fa-eye-slash\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", !_r0.form.valid || ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm],\n        styles: [\".login-page[_ngcontent-%COMP%]{position:relative;min-height:100vh;width:100%;overflow:hidden;display:flex;justify-content:center;align-items:center}.login-background[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,var(--primary-dark) 0%,var(--primary-color) 50%,var(--primary-light) 100%);z-index:-1}.login-shapes[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;overflow:hidden}.shape[_ngcontent-%COMP%]{position:absolute;background-color:#ffffff1a;border-radius:50%}.shape-1[_ngcontent-%COMP%]{width:500px;height:500px;top:-250px;right:-100px;animation:_ngcontent-%COMP%_float 15s infinite alternate ease-in-out}.shape-2[_ngcontent-%COMP%]{width:300px;height:300px;bottom:-150px;left:-50px;animation:_ngcontent-%COMP%_float 20s infinite alternate-reverse ease-in-out}.shape-3[_ngcontent-%COMP%]{width:200px;height:200px;top:50%;left:50%;transform:translate(-50%,-50%);animation:_ngcontent-%COMP%_pulse 10s infinite alternate ease-in-out}@keyframes _ngcontent-%COMP%_float{0%{transform:translateY(0) rotate(0)}to{transform:translateY(30px) rotate(10deg)}}@keyframes _ngcontent-%COMP%_pulse{0%{transform:translate(-50%,-50%) scale(1);opacity:.5}to{transform:translate(-50%,-50%) scale(1.5);opacity:.2}}.login-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;width:100%;padding:var(--spacing-md);z-index:1}.login-card[_ngcontent-%COMP%]{width:100%;max-width:450px;background-color:var(--surface-color);border-radius:var(--border-radius-medium);box-shadow:var(--elevation-4);padding:var(--spacing-xl);overflow:hidden}.login-logo[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;margin-bottom:var(--spacing-lg)}.login-logo[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;color:var(--primary-color);margin-bottom:var(--spacing-sm)}.login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:600;color:var(--primary-color);margin:0}.login-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:var(--spacing-lg)}.login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--text-primary);margin-bottom:var(--spacing-xs);font-size:1.5rem;font-weight:500}.login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:1rem;margin:0}.login-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spacing-md)}.form-group[_ngcontent-%COMP%]{margin-bottom:var(--spacing-md)}label[_ngcontent-%COMP%]{display:block;margin-bottom:var(--spacing-xs);font-weight:500;color:var(--text-primary);font-size:.9rem}.input-with-icon[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center}.input-with-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:12px;color:var(--primary-color);font-size:1rem}.form-control[_ngcontent-%COMP%]{width:100%;padding:12px 12px 12px 40px;border:1px solid var(--divider-color);border-radius:var(--border-radius-small);font-size:1rem;transition:border-color var(--transition-fast),box-shadow var(--transition-fast);background-color:var(--surface-color);color:var(--text-primary)}.form-control[_ngcontent-%COMP%]:focus{outline:none;border-color:var(--primary-color);box-shadow:0 0 0 3px rgba(var(--primary-color-rgb),.2)}.form-control[_ngcontent-%COMP%]::placeholder{color:var(--text-hint)}.password-toggle[_ngcontent-%COMP%]{position:absolute;right:12px;background:none;border:none;color:var(--text-secondary);cursor:pointer;padding:0;font-size:1rem;transition:color var(--transition-fast)}.password-toggle[_ngcontent-%COMP%]:hover{color:var(--primary-color)}.error-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-sm);color:var(--error-color);background-color:#f443361a;padding:var(--spacing-sm) var(--spacing-md);border-radius:var(--border-radius-small);margin-bottom:var(--spacing-sm)}.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem}.login-button[_ngcontent-%COMP%]{width:100%;height:48px;font-size:1rem;font-weight:500;margin-top:var(--spacing-sm);position:relative;background-color:var(--primary-color);color:#fff;border:none;border-radius:var(--border-radius-small);cursor:pointer;transition:background-color var(--transition-fast)}.login-button[_ngcontent-%COMP%]:hover:not(:disabled){background-color:var(--primary-dark)}.login-button[_ngcontent-%COMP%]:disabled{background-color:var(--divider-color);cursor:not-allowed}.spinner-container[_ngcontent-%COMP%]{position:absolute;inset:0;display:flex;align-items:center;justify-content:center}.spinner[_ngcontent-%COMP%]{width:20px;height:20px;border:2px solid rgba(255,255,255,.3);border-radius:50%;border-top-color:#fff;animation:_ngcontent-%COMP%_spin .8s linear infinite}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}.login-footer[_ngcontent-%COMP%]{margin-top:var(--spacing-xl);text-align:center}.login-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--text-hint);font-size:.75rem;margin:0}@media (max-width: 500px){.login-card[_ngcontent-%COMP%]{padding:var(--spacing-lg)}.login-logo[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2.5rem}.login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.25rem}}body.login-page-active[_nghost-%COMP%], body.login-page-active   [_nghost-%COMP%]{animation:fadeIn .5s ease-in-out}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}