{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/product.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/autocomplete\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/datepicker\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nfunction SearchPriceComponent_mat_option_33_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", location_r11.city, \" \");\n  }\n}\nfunction SearchPriceComponent_mat_option_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, SearchPriceComponent_mat_option_33_span_2_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r11);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", location_r11.name, \" \", location_r11.code ? \"(\" + location_r11.code + \")\" : \"\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r11.type === 5 && location_r11.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_56_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", location_r14.city, \" \");\n  }\n}\nfunction SearchPriceComponent_mat_option_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, SearchPriceComponent_mat_option_56_span_2_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r14);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", location_r14.name, \" \", location_r14.code ? \"(\" + location_r14.code + \")\" : \"\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r14.type === 5 && location_r14.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r17.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r17.label, \" \");\n  }\n}\nfunction SearchPriceComponent_mat_option_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", flightClass_r18.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flightClass_r18.label, \" \");\n  }\n}\nfunction SearchPriceComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"mat-spinner\", 57);\n    i0.ɵɵelementStart(2, \"p\", 58);\n    i0.ɵɵtext(3, \"Recherche des meilleurs vols...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵelement(2, \"i\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.errorMessage);\n  }\n}\nfunction SearchPriceComponent_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"i\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Aucun vol trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Essayez de modifier vos crit\\u00E8res de recherche\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1, \" escale(s) \");\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"i\", 67);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Vol direct\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r20.offers[0] == null ? null : flight_r20.offers[0].flightBrandInfo == null ? null : flight_r20.offers[0].flightBrandInfo.name);\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"div\", 72)(3, \"div\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 74)(6, \"span\", 75);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 76);\n    i0.ɵɵelementStart(9, \"span\", 77);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 78)(12, \"div\", 79);\n    i0.ɵɵtext(13, \"\\u00E0 partir de\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 80);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 81)(17, \"div\", 82)(18, \"div\", 83)(19, \"div\", 84)(20, \"div\", 85);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 86);\n    i0.ɵɵelement(24, \"i\", 87);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 88);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 89);\n    i0.ɵɵelement(30, \"div\", 90);\n    i0.ɵɵelementStart(31, \"div\", 91);\n    i0.ɵɵtemplate(32, SearchPriceComponent_div_102_div_5_div_32_Template, 2, 1, \"div\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"div\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 93)(35, \"div\", 94);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 95);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 96)(40, \"div\", 97);\n    i0.ɵɵelement(41, \"i\", 98);\n    i0.ɵɵelementStart(42, \"span\");\n    i0.ɵɵtext(43, \"Bagages inclus\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(44, SearchPriceComponent_div_102_div_5_div_44_Template, 4, 0, \"div\", 99);\n    i0.ɵɵtemplate(45, SearchPriceComponent_div_102_div_5_div_45_Template, 4, 1, \"div\", 99);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 100)(47, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_102_div_5_Template_button_click_47_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const flight_r20 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.showFlightDetails(flight_r20));\n    });\n    i0.ɵɵelement(48, \"i\", 102);\n    i0.ɵɵtext(49, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_102_div_5_Template_button_click_50_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const flight_r20 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.selectThisFlight(flight_r20));\n    });\n    i0.ɵɵelement(51, \"i\", 104);\n    i0.ɵɵtext(52, \" S\\u00E9lectionner \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const flight_r20 = ctx.$implicit;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(flight_r20.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((flight_r20.items[0] == null ? null : flight_r20.items[0].segments[0] == null ? null : flight_r20.items[0].segments[0].departure == null ? null : flight_r20.items[0].segments[0].departure.city == null ? null : flight_r20.items[0].segments[0].departure.city.name) || \"D\\u00E9part\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((flight_r20.items[0] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival.city == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival.city.name) || \"Arriv\\u00E9e\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r19.getMinPrice(flight_r20));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(22, 14, flight_r20.items[0] == null ? null : flight_r20.items[0].segments[0] == null ? null : flight_r20.items[0].segments[0].departure == null ? null : flight_r20.items[0].segments[0].departure.date, \"HH:mm\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.formatDuration((flight_r20.items[0] == null ? null : flight_r20.items[0].duration) || 0), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(28, 17, flight_r20.items[0] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival.date, \"HH:mm\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"has-stops\", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) > 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments[0] == null ? null : flight_r20.items[0].segments[0].departure == null ? null : flight_r20.items[0].segments[0].departure.airport == null ? null : flight_r20.items[0].segments[0].departure.airport.code) || \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1] == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival.airport == null ? null : flight_r20.items[0].segments[(flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) - 1].arrival.airport.code) || \"\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r20.offers[0] == null ? null : flight_r20.offers[0].flightBrandInfo == null ? null : flight_r20.offers[0].flightBrandInfo.name);\n  }\n}\nfunction SearchPriceComponent_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"h3\", 66);\n    i0.ɵɵelement(2, \"i\", 67);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 68);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_102_div_5_Template, 53, 20, \"div\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Vols disponibles (\", ctx_r10.searchResults.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.searchResults);\n  }\n}\nconst _c0 = \"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\";\nexport class SearchPriceComponent {\n  constructor(fb, productService, router) {\n    this.fb = fb;\n    this.productService = productService;\n    this.router = router;\n    this.departureLocations = [];\n    this.arrivalLocations = [];\n    this.isLoading = false;\n    this.searchResults = [];\n    this.hasSearched = false;\n    this.errorMessage = '';\n    this.lastSearchId = '';\n    // Passenger type options\n    this.passengerTypes = [{\n      value: PassengerType.Adult,\n      label: 'Adult'\n    }, {\n      value: PassengerType.Child,\n      label: 'Child'\n    }, {\n      value: PassengerType.Infant,\n      label: 'Infant'\n    }];\n    // Flight class options\n    this.flightClasses = [{\n      value: FlightClassType.PROMO,\n      label: 'Promo'\n    }, {\n      value: FlightClassType.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClassType.BUSINESS,\n      label: 'Business'\n    }];\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required],\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required],\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required],\n      // Options de vol\n      flightClass: [0, Validators.required],\n      nonStop: [false],\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n  ngOnInit() {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight) {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n    header.appendChild(logo);\n    header.appendChild(title);\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n        generalInfo.appendChild(airlineInfo);\n      }\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n      routeSection.appendChild(routeVisual);\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n          segmentsList.appendChild(segmentItem);\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n            segmentsList.appendChild(layover);\n          }\n        });\n        routeSection.appendChild(segmentsList);\n      }\n    }\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n        // ID de l'offre\n        const offerId = this.createInfoRow('Offer ID', offer.offerId || offer.id || 'N/A');\n        offerId.style.gridColumn = '1 / -1';\n        offerDetails.appendChild(offerId);\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n        offerItem.appendChild(offerDetails);\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n          const baggageList = document.createElement('ul');\n          baggageList.style.listStyle = 'none';\n          baggageList.style.padding = '0';\n          baggageList.style.margin = '0';\n          offer.baggageInformations.forEach(baggage => {\n            const baggageItem = document.createElement('li');\n            baggageItem.style.marginBottom = '5px';\n            baggageItem.innerHTML = `<i class=\"fas fa-suitcase\" style=\"color: #666; margin-right: 8px;\"></i> ${this.getBaggageTypeName(baggage.baggageType)}`;\n            baggageList.appendChild(baggageItem);\n          });\n          offerItem.appendChild(baggageList);\n        }\n        offersList.appendChild(offerItem);\n      });\n      offersSection.appendChild(offersList);\n    }\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n  // Méthode utilitaire pour créer une section\n  createSection(title, iconClass) {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n    return section;\n  }\n  // Méthode utilitaire pour créer une ligne d'information\n  createInfoRow(label, value) {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n    return row;\n  }\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight) {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n  preloadLocations() {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n  setupAutocomplete() {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.departureLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('departureLocation')?.setValue('');\n      });\n    });\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.arrivalLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('arrivalLocation')?.setValue('');\n      });\n    });\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  displayLocation(location) {\n    if (!location) return '';\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n  onSearch() {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n    const formValue = this.searchForm.value;\n    // Créer la requête de recherche entièrement dynamique\n    const request = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [{\n        id: formValue.departureLocation?.id || '',\n        type: formValue.departureLocationType\n      }],\n      ArrivalLocations: [{\n        id: formValue.arrivalLocation?.id || '',\n        type: formValue.arrivalLocationType\n      }],\n      Passengers: [{\n        type: formValue.passengerType,\n        count: formValue.passengerCount\n      }],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n    this.productService.searchPrice(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.header.success) {\n          this.searchResults = response.body.flights;\n          // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n          console.log('Full API Response:', JSON.stringify(response, null, 2));\n          // Analyser les données de disponibilité\n          if (response.body && response.body.flights && response.body.flights.length > 0) {\n            console.group('Availability Analysis');\n            console.log('Total flights:', response.body.flights.length);\n            // Compter les vols avec des offres\n            const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n            console.log('Flights with offers:', flightsWithOffers.length);\n            // Analyser les valeurs de disponibilité\n            const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability !== undefined ? o.availability : o.seatInfo ? o.seatInfo.availableSeatCount : 0));\n            console.log('Availability values:', availabilityValues);\n            // Compter les différentes valeurs de disponibilité\n            const availabilityCounts = availabilityValues.reduce((acc, val) => {\n              if (val !== undefined) {\n                acc[val] = (acc[val] || 0) + 1;\n              }\n              return acc;\n            }, {});\n            console.log('Availability counts:', availabilityCounts);\n            // Vérifier les vols réservables\n            const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true));\n            console.log('Reservable flights:', reservableFlights.length);\n            console.groupEnd();\n          }\n          // Vérifier si searchId existe dans le corps de la réponse\n          if (response.body && response.body.searchId) {\n            this.lastSearchId = response.body.searchId;\n            console.log('Search ID found in body.searchId:', this.lastSearchId);\n          }\n          // Vérifier si searchId existe dans l'en-tête de la réponse\n          else if (response.header && response.header.requestId) {\n            this.lastSearchId = response.header.requestId;\n            console.log('Search ID found in header.requestId:', this.lastSearchId);\n          }\n          // Vérifier d'autres emplacements possibles\n          else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n            this.lastSearchId = response.body.flights[0].id;\n            console.log('Using flight ID as search ID:', this.lastSearchId);\n          } else {\n            console.error('No search ID found in the response!');\n            console.log('Response structure:', Object.keys(response));\n            if (response.body) console.log('Body structure:', Object.keys(response.body));\n            if (response.header) console.log('Header structure:', Object.keys(response.header));\n          }\n        } else {\n          this.errorMessage = 'The search could not be completed. Please try again.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred during the search. Please try again.';\n        console.error('Error searching flights:', error);\n      }\n    });\n  }\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  // Formater la durée en heures et minutes\n  formatDuration(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n  // Formater la date pour l'affichage\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n    const minOffer = flight.offers.reduce((min, offer) => offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight) {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    return availabilityValue > 0;\n  }\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations() {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations() {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations() {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString) {\n    if (!dateString) return 'N/A';\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType) {\n    switch (baggageType) {\n      case 1:\n        return 'Cabin Baggage';\n      case 2:\n        return 'Checked Baggage';\n      case 3:\n        return 'Hand Baggage';\n      default:\n        return 'Baggage';\n    }\n  }\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType) {\n    switch (passengerType) {\n      case 1:\n        return 'Adult';\n      case 2:\n        return 'Child';\n      case 3:\n        return 'Infant';\n      default:\n        return 'Passenger';\n    }\n  }\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment, nextSegment) {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date || !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n  static {\n    this.ɵfac = function SearchPriceComponent_Factory(t) {\n      return new (t || SearchPriceComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchPriceComponent,\n      selectors: [[\"app-search-price\"]],\n      decls: 103,\n      vars: 23,\n      consts: [[1, \"search-price-container\"], [1, \"search-layout\"], [1, \"search-card-container\"], [1, \"search-card\"], [1, \"search-card-header\"], [1, \"search-card-title\"], [1, \"fas\", \"fa-search\"], [1, \"search-card-subtitle\"], [1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"location-container\"], [1, \"form-group\", \"location-group\"], [\"for\", \"departureLocation\"], [1, \"location-input-container\"], [1, \"location-type-selector\"], [\"appearance\", \"outline\"], [\"formControlName\", \"departureLocationType\"], [3, \"value\"], [1, \"location-autocomplete\"], [\"type\", \"text\", \"id\", \"departureLocation\", \"matInput\", \"\", \"formControlName\", \"departureLocation\", 3, \"matAutocomplete\", \"click\"], [\"matPrefix\", \"\"], [3, \"displayWith\"], [\"departureAuto\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"swap-button\", 3, \"click\"], [1, \"fas\", \"fa-exchange-alt\"], [\"for\", \"arrivalLocation\"], [\"formControlName\", \"arrivalLocationType\"], [\"type\", \"text\", \"id\", \"arrivalLocation\", \"matInput\", \"\", \"formControlName\", \"arrivalLocation\", 3, \"matAutocomplete\", \"click\"], [\"arrivalAuto\", \"matAutocomplete\"], [1, \"date-passengers-container\"], [1, \"form-group\", \"date-group\"], [\"for\", \"departureDate\"], [\"matInput\", \"\", \"formControlName\", \"departureDate\", \"id\", \"departureDate\", 3, \"min\", \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"picker\", \"\"], [1, \"form-group\", \"passenger-group\"], [\"for\", \"passengerCount\"], [1, \"passenger-inputs\"], [\"formControlName\", \"passengerType\", \"id\", \"passengerType\"], [\"matInput\", \"\", \"type\", \"number\", \"min\", \"1\", \"max\", \"9\", \"formControlName\", \"passengerCount\", \"id\", \"passengerCount\"], [1, \"flight-options-container\"], [1, \"form-group\", \"class-group\"], [\"for\", \"flightClass\"], [\"formControlName\", \"flightClass\", \"id\", \"flightClass\"], [1, \"form-group\", \"nonstop-group\"], [1, \"checkbox-container\"], [\"formControlName\", \"nonStop\", \"id\", \"nonStop\", \"color\", \"primary\"], [1, \"search-button-container\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [1, \"search-results-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"no-results-container\", 4, \"ngIf\"], [\"class\", \"results-list\", 4, \"ngIf\"], [\"class\", \"location-city\", 4, \"ngIf\"], [1, \"location-city\"], [1, \"loading-container\"], [\"diameter\", \"50\", \"color\", \"accent\"], [1, \"loading-text\"], [1, \"error-container\"], [1, \"error-icon\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"error-message\"], [1, \"no-results-container\"], [1, \"no-results-icon\"], [1, \"results-list\"], [1, \"results-title\"], [1, \"fas\", \"fa-plane\"], [1, \"flight-cards\"], [\"class\", \"flight-card animate-fade-in\", 4, \"ngFor\", \"ngForOf\"], [1, \"flight-card\", \"animate-fade-in\"], [1, \"flight-card-header\"], [1, \"flight-info\"], [1, \"flight-id\"], [1, \"flight-route\"], [1, \"departure\"], [1, \"fas\", \"fa-long-arrow-alt-right\"], [1, \"arrival\"], [1, \"flight-price\"], [1, \"price-label\"], [1, \"price-amount\"], [1, \"flight-card-content\"], [1, \"flight-details\"], [1, \"segment-info\"], [1, \"time-info\"], [1, \"departure-time\"], [1, \"flight-duration\"], [1, \"fas\", \"fa-clock\"], [1, \"arrival-time\"], [1, \"route-line\"], [1, \"route-point\"], [1, \"route-path\"], [\"class\", \"stops-indicator\", 4, \"ngIf\"], [1, \"location-info\"], [1, \"departure-location\"], [1, \"arrival-location\"], [1, \"flight-features\"], [1, \"feature\"], [1, \"fas\", \"fa-suitcase\"], [\"class\", \"feature\", 4, \"ngIf\"], [1, \"flight-actions\"], [1, \"view-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [1, \"select-flight-button\", 3, \"click\"], [1, \"fas\", \"fa-check-circle\"], [1, \"stops-indicator\"], [1, \"fas\", \"fa-tag\"]],\n      template: function SearchPriceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementStart(7, \"h2\");\n          i0.ɵɵtext(8, \"Rechercher un vol\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7);\n          i0.ɵɵtext(10, \" Trouvez les meilleurs tarifs pour votre prochain voyage \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function SearchPriceComponent_Template_form_ngSubmit_11_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"label\", 11);\n          i0.ɵɵtext(15, \"D\\u00E9part\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 12)(17, \"div\", 13)(18, \"mat-form-field\", 14)(19, \"mat-select\", 15)(20, \"mat-option\", 16);\n          i0.ɵɵtext(21, \"Ville\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-option\", 16);\n          i0.ɵɵtext(23, \"A\\u00E9roport\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(24, \"div\", 17)(25, \"mat-form-field\", 14)(26, \"mat-label\");\n          i0.ɵɵtext(27, \"Ville ou a\\u00E9roport de d\\u00E9part\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"input\", 18);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_28_listener() {\n            return ctx.showAllDepartureLocations();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"mat-icon\", 19);\n          i0.ɵɵtext(30, \"flight_takeoff\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"mat-autocomplete\", 20, 21);\n          i0.ɵɵtemplate(33, SearchPriceComponent_mat_option_33_Template, 3, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(34, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_34_listener() {\n            return ctx.swapLocations();\n          });\n          i0.ɵɵelement(35, \"i\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 10)(37, \"label\", 25);\n          i0.ɵɵtext(38, \"Arriv\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 12)(40, \"div\", 13)(41, \"mat-form-field\", 14)(42, \"mat-select\", 26)(43, \"mat-option\", 16);\n          i0.ɵɵtext(44, \"Ville\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"mat-option\", 16);\n          i0.ɵɵtext(46, \"A\\u00E9roport\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"div\", 17)(48, \"mat-form-field\", 14)(49, \"mat-label\");\n          i0.ɵɵtext(50, \"Ville ou a\\u00E9roport d'arriv\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"input\", 27);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_51_listener() {\n            return ctx.showAllArrivalLocations();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"mat-icon\", 19);\n          i0.ɵɵtext(53, \"flight_land\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-autocomplete\", 20, 28);\n          i0.ɵɵtemplate(56, SearchPriceComponent_mat_option_56_Template, 3, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(57, \"div\", 29)(58, \"div\", 30)(59, \"label\", 31);\n          i0.ɵɵtext(60, \"Date de d\\u00E9part\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 14)(62, \"mat-label\");\n          i0.ɵɵtext(63, \"Choisir une date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 32)(65, \"mat-datepicker-toggle\", 33)(66, \"mat-datepicker\", null, 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 35)(69, \"label\", 36);\n          i0.ɵɵtext(70, \"Passagers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 37)(72, \"mat-form-field\", 14)(73, \"mat-label\");\n          i0.ɵɵtext(74, \"Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"mat-select\", 38);\n          i0.ɵɵtemplate(76, SearchPriceComponent_mat_option_76_Template, 2, 2, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"mat-form-field\", 14)(78, \"mat-label\");\n          i0.ɵɵtext(79, \"Nombre\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(80, \"input\", 39);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(81, \"div\", 40)(82, \"div\", 41)(83, \"label\", 42);\n          i0.ɵɵtext(84, \"Classe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"mat-form-field\", 14)(86, \"mat-label\");\n          i0.ɵɵtext(87, \"Classe de vol\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"mat-select\", 43);\n          i0.ɵɵtemplate(89, SearchPriceComponent_mat_option_89_Template, 2, 2, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"div\", 44)(91, \"div\", 45)(92, \"mat-checkbox\", 46);\n          i0.ɵɵtext(93, \" Vol direct uniquement \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(94, \"div\", 47)(95, \"button\", 48);\n          i0.ɵɵelement(96, \"i\", 6);\n          i0.ɵɵtext(97, \" Rechercher \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(98, \"div\", 49);\n          i0.ɵɵtemplate(99, SearchPriceComponent_div_99_Template, 4, 0, \"div\", 50);\n          i0.ɵɵtemplate(100, SearchPriceComponent_div_100_Template, 5, 1, \"div\", 51);\n          i0.ɵɵtemplate(101, SearchPriceComponent_div_101_Template, 7, 0, \"div\", 52);\n          i0.ɵɵtemplate(102, SearchPriceComponent_div_102_Template, 6, 2, \"div\", 53);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(32);\n          const _r2 = i0.ɵɵreference(55);\n          const _r4 = i0.ɵɵreference(67);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.departureLocations);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matAutocomplete\", _r2);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrivalLocations);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"min\", ctx.minDate)(\"matDatepicker\", _r4);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.passengerTypes);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"has-results\", ctx.hasSearched);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.searchResults.length === 0 && ctx.hasSearched && !ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.searchResults.length > 0);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i5.MatAutocomplete, i6.MatOption, i5.MatAutocompleteTrigger, i7.MatInput, i8.MatFormField, i8.MatLabel, i8.MatPrefix, i8.MatSuffix, i9.MatSelect, i10.MatDatepicker, i10.MatDatepickerInput, i10.MatDatepickerToggle, i11.MatIcon, i12.MatProgressSpinner, i4.DatePipe],\n      styles: [_c0, _c0],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "of", "FlightClassType", "LocationType", "PassengerType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "location_r11", "city", "ɵɵtemplate", "SearchPriceComponent_mat_option_33_span_2_Template", "ɵɵproperty", "ɵɵtextInterpolate2", "name", "code", "type", "location_r14", "SearchPriceComponent_mat_option_56_span_2_Template", "type_r17", "value", "label", "flightClass_r18", "ɵɵelement", "ɵɵtextInterpolate", "ctx_r8", "errorMessage", "flight_r20", "items", "segments", "length", "offers", "flightBrandInfo", "SearchPriceComponent_div_102_div_5_div_32_Template", "SearchPriceComponent_div_102_div_5_div_44_Template", "SearchPriceComponent_div_102_div_5_div_45_Template", "ɵɵlistener", "SearchPriceComponent_div_102_div_5_Template_button_click_47_listener", "restoredCtx", "ɵɵrestoreView", "_r27", "$implicit", "ctx_r26", "ɵɵnextContext", "ɵɵresetView", "showFlightDetails", "SearchPriceComponent_div_102_div_5_Template_button_click_50_listener", "ctx_r28", "selectThisFlight", "id", "departure", "arrival", "ctx_r19", "getMinPrice", "ɵɵpipeBind2", "date", "formatDuration", "duration", "ɵɵclassProp", "airport", "SearchPriceComponent_div_102_div_5_Template", "ctx_r10", "searchResults", "SearchPriceComponent", "constructor", "fb", "productService", "router", "departureLocations", "arrivalLocations", "isLoading", "hasSearched", "lastSearchId", "passengerTypes", "Adult", "Child", "Infant", "flightClasses", "PROMO", "ECONOMY", "BUSINESS", "minDate", "Date", "toISOString", "split", "searchForm", "group", "productType", "required", "serviceTypes", "departureLocation", "departureLocationType", "arrivalLocation", "arrivalLocationType", "departureDate", "passengerCount", "min", "max", "passengerType", "flightClass", "nonStop", "culture", "currency", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightBaggageGetOption", "ngOnInit", "setupAutocomplete", "preloadLocations", "console", "log", "showAllDetails", "flight", "modalDiv", "document", "createElement", "style", "position", "top", "left", "width", "height", "backgroundColor", "zIndex", "display", "justifyContent", "alignItems", "modalContent", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "color", "fontFamily", "closeButton", "innerHTML", "right", "border", "background", "fontSize", "cursor", "transition", "on<PERSON><PERSON>ver", "onmouseout", "onclick", "body", "<PERSON><PERSON><PERSON><PERSON>", "header", "marginBottom", "paddingBottom", "borderBottom", "logo", "title", "textContent", "margin", "fontWeight", "append<PERSON><PERSON><PERSON>", "detailsContainer", "flexDirection", "gap", "generalInfo", "createSection", "item", "airline", "airlineInfo", "thumbnailFull", "airlineLogo", "src", "alt", "marginRight", "airlineIcon", "airlineName", "internationalCode", "flightNumberRow", "createInfoRow", "flightNo", "flightDateRow", "flightDate", "toLocaleDateString", "durationRow", "classRow", "stopsRow", "stopCount", "routeSection", "routeVisual", "textAlign", "flex", "departureTime", "toLocaleTimeString", "hour", "minute", "departureAirport", "marginTop", "departureCity", "connectionLine", "line", "plane", "marginLeft", "arrivalTime", "arrivalAirport", "arrivalCity", "segmentsTitle", "segmentsList", "for<PERSON>ach", "segment", "index", "segmentItem", "segmentHeader", "segmentTitle", "segmentDuration", "segmentRoute", "segmentDeparture", "depTime", "depAirport", "arrow", "segmentArrival", "arrTime", "arrAirport", "layover", "currentArrival", "getTime", "nextDeparture", "layoverTime", "Math", "floor", "offersSection", "offersList", "offer", "offerItem", "offerHeader", "offerTitle", "offerPrice", "price", "amount", "offerDetails", "gridTemplateColumns", "offerId", "gridColumn", "availabilityValue", "availability", "undefined", "seatInfo", "availableSeatCount", "expiresOn", "expires", "toLocaleString", "brandedFare", "reservableInfo", "reservable", "baggageInformations", "baggageTitle", "baggageList", "listStyle", "baggage", "baggageItem", "getBaggageTypeName", "baggageType", "services", "servicesSection", "servicesList", "service", "serviceItem", "iconClass", "section", "section<PERSON><PERSON><PERSON>", "icon", "className", "sectionTitle", "row", "labelElement", "valueElement", "searchId", "navigate", "queryParams", "error", "get", "getLocationsByType", "subscribe", "locations", "valueChanges", "locationType", "setValue", "pipe", "filter", "location", "toLowerCase", "includes", "displayLocation", "displayText", "Airport", "onSearch", "invalid", "markFormGroupTouched", "formValue", "request", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "ArrivalLocations", "Passengers", "count", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "searchPrice", "next", "response", "success", "flights", "JSON", "stringify", "flightsWithOffers", "f", "availabilityValues", "flatMap", "o", "availabilityCounts", "reduce", "acc", "val", "reservableFlights", "some", "groupEnd", "requestId", "Object", "keys", "messages", "message", "formGroup", "values", "controls", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "minutes", "hours", "mins", "formatDate", "dateString", "weekday", "day", "month", "min<PERSON>ffer", "formattedAmount", "isFlightAvailable", "showAllDepartureLocations", "input", "getElementById", "focus", "dispatchEvent", "Event", "showAllArrivalLocations", "swapLocations", "patchValue", "formatExpirationDate", "getPassengerTypeName", "calculateLayoverTime", "currentSegment", "nextSegment", "diffMs", "diffMins", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProductService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "SearchPriceComponent_Template", "rf", "ctx", "SearchPriceComponent_Template_form_ngSubmit_11_listener", "SearchPriceComponent_Template_input_click_28_listener", "SearchPriceComponent_mat_option_33_Template", "SearchPriceComponent_Template_button_click_34_listener", "SearchPriceComponent_Template_input_click_51_listener", "SearchPriceComponent_mat_option_56_Template", "SearchPriceComponent_mat_option_76_Template", "SearchPriceComponent_mat_option_89_Template", "SearchPriceComponent_div_99_Template", "SearchPriceComponent_div_100_Template", "SearchPriceComponent_div_101_Template", "SearchPriceComponent_div_102_Template", "_r0", "_r2", "_r4"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { Observable, of } from 'rxjs';\nimport { ProductService } from '../../../services/product.service';\nimport { LocationOption } from '../../../models/price-search-request.model';\nimport { PriceSearchRequest } from '../../../models/price-search-request.model';\nimport { PriceSearchResponse, Flight } from '../../../models/price-search-response.model';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css'],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchPriceComponent implements OnInit {\n  searchForm: FormGroup;\n  departureLocations: LocationOption[] = [];\n  arrivalLocations: LocationOption[] = [];\n  isLoading = false;\n  searchResults: Flight[] = [];\n  hasSearched = false;\n  errorMessage = '';\n  lastSearchId = '';\n\n  // Passenger type options\n  passengerTypes = [\n    { value: PassengerType.Adult, label: 'Adult' },\n    { value: PassengerType.Child, label: 'Child' },\n    { value: PassengerType.Infant, label: 'Infant' }\n  ];\n\n  // Flight class options\n  flightClasses = [\n    { value: FlightClassType.PROMO, label: 'Promo' },\n    { value: FlightClassType.ECONOMY, label: 'Economy' },\n    { value: FlightClassType.BUSINESS, label: 'Business' }\n  ];\n\n  // Date minimale (aujourd'hui)\n  minDate: string;\n\n  constructor(\n    private fb: FormBuilder,\n    private productService: ProductService,\n    private router: Router\n  ) {\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required], // Flight par défaut\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required], // Type 2 (City) par défaut\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required], // Type 5 (Airport) par défaut\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required], // Adult par défaut\n\n      // Options de vol\n      flightClass: [0, Validators.required], // PROMO par défaut\n      nonStop: [false],\n\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n\n  ngOnInit(): void {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight: Flight): void {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n\n    header.appendChild(logo);\n    header.appendChild(title);\n\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n\n        generalInfo.appendChild(airlineInfo);\n      }\n\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n\n      routeSection.appendChild(routeVisual);\n\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n\n          segmentsList.appendChild(segmentItem);\n\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n\n            segmentsList.appendChild(layover);\n          }\n        });\n\n        routeSection.appendChild(segmentsList);\n      }\n    }\n\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n\n        // ID de l'offre\n        const offerId = this.createInfoRow('Offer ID', offer.offerId || offer.id || 'N/A');\n        offerId.style.gridColumn = '1 / -1';\n        offerDetails.appendChild(offerId);\n\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability :\n                                 (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n\n        offerItem.appendChild(offerDetails);\n\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n\n          const baggageList = document.createElement('ul');\n          baggageList.style.listStyle = 'none';\n          baggageList.style.padding = '0';\n          baggageList.style.margin = '0';\n\n          offer.baggageInformations.forEach(baggage => {\n            const baggageItem = document.createElement('li');\n            baggageItem.style.marginBottom = '5px';\n            baggageItem.innerHTML = `<i class=\"fas fa-suitcase\" style=\"color: #666; margin-right: 8px;\"></i> ${this.getBaggageTypeName(baggage.baggageType)}`;\n            baggageList.appendChild(baggageItem);\n          });\n\n          offerItem.appendChild(baggageList);\n        }\n\n        offersList.appendChild(offerItem);\n      });\n\n      offersSection.appendChild(offersList);\n    }\n\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n\n  // Méthode utilitaire pour créer une section\n  private createSection(title: string, iconClass: string): HTMLElement {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n\n    return section;\n  }\n\n  // Méthode utilitaire pour créer une ligne d'information\n  private createInfoRow(label: string, value: string): HTMLElement {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n\n    return row;\n  }\n\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight: Flight): void {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n\n  preloadLocations(): void {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n\n  setupAutocomplete(): void {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.departureLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('departureLocation')?.setValue('');\n        });\n      });\n\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.arrivalLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('arrivalLocation')?.setValue('');\n        });\n      });\n\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.departureLocations = locations;\n      });\n\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n  }\n\n  displayLocation(location: LocationOption | null): string {\n    if (!location) return '';\n\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n\n  onSearch(): void {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n\n    const formValue = this.searchForm.value;\n\n    // Créer la requête de recherche entièrement dynamique\n    const request: PriceSearchRequest = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [\n        {\n          id: formValue.departureLocation?.id || '',\n          type: formValue.departureLocationType\n        }\n      ],\n      ArrivalLocations: [\n        {\n          id: formValue.arrivalLocation?.id || '',\n          type: formValue.arrivalLocationType\n        }\n      ],\n      Passengers: [\n        {\n          type: formValue.passengerType,\n          count: formValue.passengerCount\n        }\n      ],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n\n    this.productService.searchPrice(request)\n      .subscribe({\n        next: (response: PriceSearchResponse) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            console.log('Full API Response:', JSON.stringify(response, null, 2));\n\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o =>\n                o.availability !== undefined ? o.availability : (o.seatInfo ? o.seatInfo.availableSeatCount : 0)\n              ));\n              console.log('Availability values:', availabilityValues);\n\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc, val) => {\n                if (val !== undefined) {\n                  acc[val] = (acc[val] || 0) + 1;\n                }\n                return acc;\n              }, {} as Record<number, number>);\n              console.log('Availability counts:', availabilityCounts);\n\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f =>\n                f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true)\n              );\n              console.log('Reservable flights:', reservableFlights.length);\n\n              console.groupEnd();\n            }\n\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n  }\n\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup: FormGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n\n  // Formater la durée en heures et minutes\n  formatDuration(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n\n  // Formater la date pour l'affichage\n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight: Flight): string {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n\n    const minOffer = flight.offers.reduce((min, offer) =>\n      offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight: Flight): boolean {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability :\n                             (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n    return availabilityValue > 0;\n  }\n\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations(): void {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations(): void {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations(): void {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType: number): string {\n    switch (baggageType) {\n      case 1: return 'Cabin Baggage';\n      case 2: return 'Checked Baggage';\n      case 3: return 'Hand Baggage';\n      default: return 'Baggage';\n    }\n  }\n\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType: number): string {\n    switch (passengerType) {\n      case 1: return 'Adult';\n      case 2: return 'Child';\n      case 3: return 'Infant';\n      default: return 'Passenger';\n    }\n  }\n\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment: any, nextSegment: any): string {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date ||\n        !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n}\n", "<div class=\"search-price-container\">\n  <div class=\"search-layout\">\n    <!-- Carte de recherche -->\n    <div class=\"search-card-container\">\n      <div class=\"search-card\">\n        <div class=\"search-card-header\">\n          <div class=\"search-card-title\">\n            <i class=\"fas fa-search\"></i>\n            <h2>Rechercher un vol</h2>\n          </div>\n          <div class=\"search-card-subtitle\">\n            Trouvez les meilleurs tarifs pour votre prochain voyage\n          </div>\n        </div>\n\n        <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSearch()\" class=\"search-form\">\n          <!-- Emplacements de départ et d'arrivée -->\n          <div class=\"location-container\">\n            <div class=\"form-group location-group\">\n              <label for=\"departureLocation\">Départ</label>\n              <div class=\"location-input-container\">\n                <div class=\"location-type-selector\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-select formControlName=\"departureLocationType\">\n                      <mat-option [value]=\"2\">Ville</mat-option>\n                      <mat-option [value]=\"5\">Aéroport</mat-option>\n                    </mat-select>\n                  </mat-form-field>\n                </div>\n                <div class=\"location-autocomplete\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Ville ou aéroport de départ</mat-label>\n                    <input type=\"text\"\n                           id=\"departureLocation\"\n                           matInput\n                           [matAutocomplete]=\"departureAuto\"\n                           formControlName=\"departureLocation\"\n                           (click)=\"showAllDepartureLocations()\">\n                    <mat-icon matPrefix>flight_takeoff</mat-icon>\n                    <mat-autocomplete #departureAuto=\"matAutocomplete\" [displayWith]=\"displayLocation\">\n                      <mat-option *ngFor=\"let location of departureLocations\" [value]=\"location\">\n                        {{ location.name }} {{ location.code ? '(' + location.code + ')' : '' }}\n                        <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">\n                          - {{ location.city }}\n                        </span>\n                      </mat-option>\n                    </mat-autocomplete>\n                  </mat-form-field>\n                </div>\n              </div>\n            </div>\n\n            <button type=\"button\" class=\"swap-button\" (click)=\"swapLocations()\">\n              <i class=\"fas fa-exchange-alt\"></i>\n            </button>\n\n            <div class=\"form-group location-group\">\n              <label for=\"arrivalLocation\">Arrivée</label>\n              <div class=\"location-input-container\">\n                <div class=\"location-type-selector\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-select formControlName=\"arrivalLocationType\">\n                      <mat-option [value]=\"2\">Ville</mat-option>\n                      <mat-option [value]=\"5\">Aéroport</mat-option>\n                    </mat-select>\n                  </mat-form-field>\n                </div>\n                <div class=\"location-autocomplete\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Ville ou aéroport d'arrivée</mat-label>\n                    <input type=\"text\"\n                           id=\"arrivalLocation\"\n                           matInput\n                           [matAutocomplete]=\"arrivalAuto\"\n                           formControlName=\"arrivalLocation\"\n                           (click)=\"showAllArrivalLocations()\">\n                    <mat-icon matPrefix>flight_land</mat-icon>\n                    <mat-autocomplete #arrivalAuto=\"matAutocomplete\" [displayWith]=\"displayLocation\">\n                      <mat-option *ngFor=\"let location of arrivalLocations\" [value]=\"location\">\n                        {{ location.name }} {{ location.code ? '(' + location.code + ')' : '' }}\n                        <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">\n                          - {{ location.city }}\n                        </span>\n                      </mat-option>\n                    </mat-autocomplete>\n                  </mat-form-field>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Date et passagers -->\n          <div class=\"date-passengers-container\">\n            <div class=\"form-group date-group\">\n              <label for=\"departureDate\">Date de départ</label>\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Choisir une date</mat-label>\n                <input matInput [min]=\"minDate\" [matDatepicker]=\"picker\" formControlName=\"departureDate\" id=\"departureDate\">\n                <mat-datepicker-toggle matSuffix [for]=\"picker\"></mat-datepicker-toggle>\n                <mat-datepicker #picker></mat-datepicker>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-group passenger-group\">\n              <label for=\"passengerCount\">Passagers</label>\n              <div class=\"passenger-inputs\">\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Type</mat-label>\n                  <mat-select formControlName=\"passengerType\" id=\"passengerType\">\n                    <mat-option *ngFor=\"let type of passengerTypes\" [value]=\"type.value\">\n                      {{ type.label }}\n                    </mat-option>\n                  </mat-select>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Nombre</mat-label>\n                  <input matInput type=\"number\" min=\"1\" max=\"9\" formControlName=\"passengerCount\" id=\"passengerCount\">\n                </mat-form-field>\n              </div>\n            </div>\n          </div>\n\n          <!-- Options de vol -->\n          <div class=\"flight-options-container\">\n            <div class=\"form-group class-group\">\n              <label for=\"flightClass\">Classe</label>\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Classe de vol</mat-label>\n                <mat-select formControlName=\"flightClass\" id=\"flightClass\">\n                  <mat-option *ngFor=\"let flightClass of flightClasses\" [value]=\"flightClass.value\">\n                    {{ flightClass.label }}\n                  </mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-group nonstop-group\">\n              <div class=\"checkbox-container\">\n                <mat-checkbox formControlName=\"nonStop\" id=\"nonStop\" color=\"primary\">\n                  Vol direct uniquement\n                </mat-checkbox>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton de recherche -->\n          <div class=\"search-button-container\">\n            <button type=\"submit\" class=\"search-button\" [disabled]=\"isLoading\">\n              <i class=\"fas fa-search\"></i>\n              Rechercher\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n\n    <!-- Résultats de recherche -->\n    <div class=\"search-results-container\" [class.has-results]=\"hasSearched\">\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <mat-spinner diameter=\"50\" color=\"accent\"></mat-spinner>\n        <p class=\"loading-text\">Recherche des meilleurs vols...</p>\n      </div>\n\n      <div *ngIf=\"errorMessage && !isLoading\" class=\"error-container\">\n        <div class=\"error-icon\">\n          <i class=\"fas fa-exclamation-circle\"></i>\n        </div>\n        <p class=\"error-message\">{{ errorMessage }}</p>\n      </div>\n\n      <div *ngIf=\"!isLoading && searchResults.length === 0 && hasSearched && !errorMessage\" class=\"no-results-container\">\n        <div class=\"no-results-icon\">\n          <i class=\"fas fa-search\"></i>\n        </div>\n        <h3>Aucun vol trouvé</h3>\n        <p>Essayez de modifier vos critères de recherche</p>\n      </div>\n\n      <div *ngIf=\"!isLoading && searchResults.length > 0\" class=\"results-list\">\n        <h3 class=\"results-title\">\n          <i class=\"fas fa-plane\"></i>\n          Vols disponibles ({{ searchResults.length }})\n        </h3>\n\n        <div class=\"flight-cards\">\n          <div *ngFor=\"let flight of searchResults\" class=\"flight-card animate-fade-in\">\n            <div class=\"flight-card-header\">\n              <div class=\"flight-info\">\n                <div class=\"flight-id\">{{ flight.id }}</div>\n                <div class=\"flight-route\">\n                  <span class=\"departure\">{{ flight.items[0]?.segments[0]?.departure?.city?.name || 'Départ' }}</span>\n                  <i class=\"fas fa-long-arrow-alt-right\"></i>\n                  <span class=\"arrival\">{{ flight.items[0]?.segments[flight.items[0]?.segments.length - 1]?.arrival?.city?.name || 'Arrivée' }}</span>\n                </div>\n              </div>\n              <div class=\"flight-price\">\n                <div class=\"price-label\">à partir de</div>\n                <div class=\"price-amount\">{{ getMinPrice(flight) }}</div>\n              </div>\n            </div>\n\n            <div class=\"flight-card-content\">\n              <div class=\"flight-details\">\n                <div class=\"segment-info\">\n                  <div class=\"time-info\">\n                    <div class=\"departure-time\">\n                      {{ flight.items[0]?.segments[0]?.departure?.date | date:'HH:mm' }}\n                    </div>\n                    <div class=\"flight-duration\">\n                      <i class=\"fas fa-clock\"></i>\n                      {{ formatDuration(flight.items[0]?.duration || 0) }}\n                    </div>\n                    <div class=\"arrival-time\">\n                      {{ flight.items[0]?.segments[flight.items[0]?.segments.length - 1]?.arrival?.date | date:'HH:mm' }}\n                    </div>\n                  </div>\n                  <div class=\"route-line\">\n                    <div class=\"route-point\"></div>\n                    <div class=\"route-path\" [class.has-stops]=\"flight.items[0]?.segments.length > 1\">\n                      <div *ngIf=\"flight.items[0]?.segments.length > 1\" class=\"stops-indicator\">\n                        {{ flight.items[0]?.segments.length - 1 }} escale(s)\n                      </div>\n                    </div>\n                    <div class=\"route-point\"></div>\n                  </div>\n                  <div class=\"location-info\">\n                    <div class=\"departure-location\">\n                      {{ flight.items[0]?.segments[0]?.departure?.airport?.code || '' }}\n                    </div>\n                    <div class=\"arrival-location\">\n                      {{ flight.items[0]?.segments[flight.items[0]?.segments.length - 1]?.arrival?.airport?.code || '' }}\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"flight-features\">\n                  <div class=\"feature\">\n                    <i class=\"fas fa-suitcase\"></i>\n                    <span>Bagages inclus</span>\n                  </div>\n                  <div class=\"feature\" *ngIf=\"flight.items[0]?.segments.length === 1\">\n                    <i class=\"fas fa-plane\"></i>\n                    <span>Vol direct</span>\n                  </div>\n                  <div class=\"feature\" *ngIf=\"flight.offers[0]?.flightBrandInfo?.name\">\n                    <i class=\"fas fa-tag\"></i>\n                    <span>{{ flight.offers[0]?.flightBrandInfo?.name }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"flight-actions\">\n                <button class=\"view-details-button\" (click)=\"showFlightDetails(flight)\">\n                  <i class=\"fas fa-info-circle\"></i>\n                  Détails\n                </button>\n                <button class=\"select-flight-button\" (click)=\"selectThisFlight(flight)\">\n                  <i class=\"fas fa-check-circle\"></i>\n                  Sélectionner\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnF,SAAqBC,EAAE,QAAQ,MAAM;AAKrC,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;;;;;;;;;;;;;;;;ICiClEC,EAAA,CAAAC,cAAA,eAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,QAAAC,YAAA,CAAAC,IAAA,MACF;;;;;IAJFP,EAAA,CAAAC,cAAA,qBAA2E;IACzED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAQ,UAAA,IAAAC,kDAAA,mBAEO;IACTT,EAAA,CAAAG,YAAA,EAAa;;;;IAL2CH,EAAA,CAAAU,UAAA,UAAAJ,YAAA,CAAkB;IACxEN,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAW,kBAAA,MAAAL,YAAA,CAAAM,IAAA,OAAAN,YAAA,CAAAO,IAAA,SAAAP,YAAA,CAAAO,IAAA,iBACA;IAAOb,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAU,UAAA,SAAAJ,YAAA,CAAAQ,IAAA,UAAAR,YAAA,CAAAC,IAAA,CAA0C;;;;;IAsCjDP,EAAA,CAAAC,cAAA,eAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,QAAAU,YAAA,CAAAR,IAAA,MACF;;;;;IAJFP,EAAA,CAAAC,cAAA,qBAAyE;IACvED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAQ,UAAA,IAAAQ,kDAAA,mBAEO;IACThB,EAAA,CAAAG,YAAA,EAAa;;;;IALyCH,EAAA,CAAAU,UAAA,UAAAK,YAAA,CAAkB;IACtEf,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAW,kBAAA,MAAAI,YAAA,CAAAH,IAAA,OAAAG,YAAA,CAAAF,IAAA,SAAAE,YAAA,CAAAF,IAAA,iBACA;IAAOb,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAU,UAAA,SAAAK,YAAA,CAAAD,IAAA,UAAAC,YAAA,CAAAR,IAAA,CAA0C;;;;;IA6BrDP,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAU,UAAA,UAAAO,QAAA,CAAAC,KAAA,CAAoB;IAClElB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAY,QAAA,CAAAE,KAAA,MACF;;;;;IAmBFnB,EAAA,CAAAC,cAAA,qBAAkF;IAChFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFyCH,EAAA,CAAAU,UAAA,UAAAU,eAAA,CAAAF,KAAA,CAA2B;IAC/ElB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAe,eAAA,CAAAD,KAAA,MACF;;;;;IA2BZnB,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAqB,SAAA,sBAAwD;IACxDrB,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAG7DH,EAAA,CAAAC,cAAA,cAAgE;IAE5DD,EAAA,CAAAqB,SAAA,YAAyC;IAC3CrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAtBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAsB,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;IAG7CxB,EAAA,CAAAC,cAAA,cAAmH;IAE/GD,EAAA,CAAAqB,SAAA,WAA6B;IAC/BrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,4BAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yDAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IA4CtCH,EAAA,CAAAC,cAAA,eAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,OAAAoB,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,qBACF;;;;;IAmBJ5B,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAqB,SAAA,YAA4B;IAC5BrB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEzBH,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAqB,SAAA,aAA0B;IAC1BrB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApDH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAsB,iBAAA,CAAAG,UAAA,CAAAI,MAAA,qBAAAJ,UAAA,CAAAI,MAAA,IAAAC,eAAA,kBAAAL,UAAA,CAAAI,MAAA,IAAAC,eAAA,CAAAlB,IAAA,CAA6C;;;;;;IA7D7DZ,EAAA,CAAAC,cAAA,cAA8E;IAGjDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAA0B;IACAD,EAAA,CAAAE,MAAA,GAAqE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpGH,EAAA,CAAAqB,SAAA,YAA2C;IAC3CrB,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAuG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGxIH,EAAA,CAAAC,cAAA,eAA0B;IACCD,EAAA,CAAAE,MAAA,wBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAI7DH,EAAA,CAAAC,cAAA,eAAiC;IAKvBD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAqB,SAAA,aAA4B;IAC5BrB,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAqB,SAAA,eAA+B;IAC/BrB,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAQ,UAAA,KAAAuB,kDAAA,kBAEM;IACR/B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAqB,SAAA,eAA+B;IACjCrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAEvBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIVH,EAAA,CAAAC,cAAA,eAA6B;IAEzBD,EAAA,CAAAqB,SAAA,aAA+B;IAC/BrB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7BH,EAAA,CAAAQ,UAAA,KAAAwB,kDAAA,kBAGM;IACNhC,EAAA,CAAAQ,UAAA,KAAAyB,kDAAA,kBAGM;IACRjC,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAA4B;IACUD,EAAA,CAAAkC,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,WAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA;MAAA,MAAAb,UAAA,GAAAW,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAyC,aAAA;MAAA,OAASzC,EAAA,CAAA0C,WAAA,CAAAF,OAAA,CAAAG,iBAAA,CAAAlB,UAAA,CAAyB;IAAA,EAAC;IACrEzB,EAAA,CAAAqB,SAAA,cAAkC;IAClCrB,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAwE;IAAnCD,EAAA,CAAAkC,UAAA,mBAAAU,qEAAA;MAAA,MAAAR,WAAA,GAAApC,EAAA,CAAAqC,aAAA,CAAAC,IAAA;MAAA,MAAAb,UAAA,GAAAW,WAAA,CAAAG,SAAA;MAAA,MAAAM,OAAA,GAAA7C,EAAA,CAAAyC,aAAA;MAAA,OAASzC,EAAA,CAAA0C,WAAA,CAAAG,OAAA,CAAAC,gBAAA,CAAArB,UAAA,CAAwB;IAAA,EAAC;IACrEzB,EAAA,CAAAqB,SAAA,cAAmC;IACnCrB,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAvEcH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAsB,iBAAA,CAAAG,UAAA,CAAAsB,EAAA,CAAe;IAEZ/C,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAsB,iBAAA,EAAAG,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,qBAAAF,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,kBAAAvB,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,CAAAzC,IAAA,kBAAAkB,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,CAAAzC,IAAA,CAAAK,IAAA,mBAAqE;IAEvEZ,EAAA,CAAAI,SAAA,GAAuG;IAAvGJ,EAAA,CAAAsB,iBAAA,EAAAG,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,wBAAAH,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,kBAAAxB,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,CAAA1C,IAAA,kBAAAkB,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,CAAA1C,IAAA,CAAAK,IAAA,oBAAuG;IAKrGZ,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAsB,iBAAA,CAAA4B,OAAA,CAAAC,WAAA,CAAA1B,UAAA,EAAyB;IAS7CzB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAoD,WAAA,SAAA3B,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,qBAAAF,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,kBAAAvB,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,CAAAK,IAAA,gBACF;IAGErD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA6C,OAAA,CAAAI,cAAA,EAAA7B,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAA6B,QAAA,aACF;IAEEvD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAoD,WAAA,SAAA3B,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,wBAAAH,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,kBAAAxB,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,CAAAI,IAAA,gBACF;IAIwBrD,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAwD,WAAA,eAAA/B,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,MAAwD;IACxE5B,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAU,UAAA,UAAAe,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,MAA0C;IAQhD5B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,OAAAoB,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,qBAAAF,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,kBAAAvB,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,CAAAS,OAAA,kBAAAhC,UAAA,CAAAC,KAAA,IAAAC,QAAA,IAAAqB,SAAA,CAAAS,OAAA,CAAA5C,IAAA,aACF;IAEEb,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,OAAAoB,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,wBAAAH,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,kBAAAxB,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,CAAAQ,OAAA,kBAAAhC,UAAA,CAAAC,KAAA,IAAAC,QAAA,EAAAF,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,OAAAqB,OAAA,CAAAQ,OAAA,CAAA5C,IAAA,aACF;IASoBb,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAU,UAAA,UAAAe,UAAA,CAAAC,KAAA,qBAAAD,UAAA,CAAAC,KAAA,IAAAC,QAAA,CAAAC,MAAA,QAA4C;IAI5C5B,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAU,UAAA,SAAAe,UAAA,CAAAI,MAAA,qBAAAJ,UAAA,CAAAI,MAAA,IAAAC,eAAA,kBAAAL,UAAA,CAAAI,MAAA,IAAAC,eAAA,CAAAlB,IAAA,CAA6C;;;;;IAlE/EZ,EAAA,CAAAC,cAAA,cAAyE;IAErED,EAAA,CAAAqB,SAAA,YAA4B;IAC5BrB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAQ,UAAA,IAAAkD,2CAAA,oBA6EM;IACR1D,EAAA,CAAAG,YAAA,EAAM;;;;IAlFJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,wBAAAsD,OAAA,CAAAC,aAAA,CAAAhC,MAAA,OACF;IAG0B5B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAU,UAAA,YAAAiD,OAAA,CAAAC,aAAA,CAAgB;;;;ADzKlD,OAAM,MAAOC,oBAAoB;EA2B/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IA5BhB,KAAAC,kBAAkB,GAAqB,EAAE;IACzC,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAR,aAAa,GAAa,EAAE;IAC5B,KAAAS,WAAW,GAAG,KAAK;IACnB,KAAA7C,YAAY,GAAG,EAAE;IACjB,KAAA8C,YAAY,GAAG,EAAE;IAEjB;IACA,KAAAC,cAAc,GAAG,CACf;MAAErD,KAAK,EAAEnB,aAAa,CAACyE,KAAK;MAAErD,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAEnB,aAAa,CAAC0E,KAAK;MAAEtD,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAEnB,aAAa,CAAC2E,MAAM;MAAEvD,KAAK,EAAE;IAAQ,CAAE,CACjD;IAED;IACA,KAAAwD,aAAa,GAAG,CACd;MAAEzD,KAAK,EAAErB,eAAe,CAAC+E,KAAK;MAAEzD,KAAK,EAAE;IAAO,CAAE,EAChD;MAAED,KAAK,EAAErB,eAAe,CAACgF,OAAO;MAAE1D,KAAK,EAAE;IAAS,CAAE,EACpD;MAAED,KAAK,EAAErB,eAAe,CAACiF,QAAQ;MAAE3D,KAAK,EAAE;IAAU,CAAE,CACvD;IAUC;IACA,IAAI,CAAC4D,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAErD;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACpB,EAAE,CAACqB,KAAK,CAAC;MAC9B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAE9F,UAAU,CAAC+F,QAAQ,CAAC;MACrCC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,EAAEhG,UAAU,CAAC+F,QAAQ,CAAC;MAC1CE,iBAAiB,EAAE,CAAC,EAAE,EAAEjG,UAAU,CAAC+F,QAAQ,CAAC;MAC5CG,qBAAqB,EAAE,CAAC,CAAC,EAAElG,UAAU,CAAC+F,QAAQ,CAAC;MAC/CI,eAAe,EAAE,CAAC,EAAE,EAAEnG,UAAU,CAAC+F,QAAQ,CAAC;MAC1CK,mBAAmB,EAAE,CAAC,CAAC,EAAEpG,UAAU,CAAC+F,QAAQ,CAAC;MAC7CM,aAAa,EAAE,CAAC,IAAI,CAACb,OAAO,EAAExF,UAAU,CAAC+F,QAAQ,CAAC;MAClDO,cAAc,EAAE,CAAC,CAAC,EAAE,CAACtG,UAAU,CAAC+F,QAAQ,EAAE/F,UAAU,CAACuG,GAAG,CAAC,CAAC,CAAC,EAAEvG,UAAU,CAACwG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChFC,aAAa,EAAE,CAAC,CAAC,EAAEzG,UAAU,CAAC+F,QAAQ,CAAC;MAEvC;MACAW,WAAW,EAAE,CAAC,CAAC,EAAE1G,UAAU,CAAC+F,QAAQ,CAAC;MACrCY,OAAO,EAAE,CAAC,KAAK,CAAC;MAEhB;MACAC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBC,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,wBAAwB,EAAE,CAAC,KAAK,CAAC;MACjCC,6BAA6B,EAAE,CAAC,IAAI,CAAC;MACrCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAE5B;MACAC,sBAAsB,EAAE,CAAC,CAAC;KAC3B,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACAC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EACjF;EAEA;EACAC,cAAcA,CAACC,MAAc;IAC3B;IACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9CF,QAAQ,CAACG,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCJ,QAAQ,CAACG,KAAK,CAACE,GAAG,GAAG,GAAG;IACxBL,QAAQ,CAACG,KAAK,CAACG,IAAI,GAAG,GAAG;IACzBN,QAAQ,CAACG,KAAK,CAACI,KAAK,GAAG,MAAM;IAC7BP,QAAQ,CAACG,KAAK,CAACK,MAAM,GAAG,MAAM;IAC9BR,QAAQ,CAACG,KAAK,CAACM,eAAe,GAAG,oBAAoB;IACrDT,QAAQ,CAACG,KAAK,CAACO,MAAM,GAAG,MAAM;IAC9BV,QAAQ,CAACG,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC/BX,QAAQ,CAACG,KAAK,CAACS,cAAc,GAAG,QAAQ;IACxCZ,QAAQ,CAACG,KAAK,CAACU,UAAU,GAAG,QAAQ;IAEpC;IACA,MAAMC,YAAY,GAAGb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDY,YAAY,CAACX,KAAK,CAACM,eAAe,GAAG,OAAO;IAC5CK,YAAY,CAACX,KAAK,CAACY,OAAO,GAAG,MAAM;IACnCD,YAAY,CAACX,KAAK,CAACa,YAAY,GAAG,MAAM;IACxCF,YAAY,CAACX,KAAK,CAACc,QAAQ,GAAG,KAAK;IACnCH,YAAY,CAACX,KAAK,CAACe,SAAS,GAAG,KAAK;IACpCJ,YAAY,CAACX,KAAK,CAACgB,QAAQ,GAAG,MAAM;IACpCL,YAAY,CAACX,KAAK,CAACC,QAAQ,GAAG,UAAU;IACxCU,YAAY,CAACX,KAAK,CAACiB,SAAS,GAAG,gCAAgC;IAC/DN,YAAY,CAACX,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCP,YAAY,CAACX,KAAK,CAACmB,UAAU,GAAG,mBAAmB;IAEnD;IACA,MAAMC,WAAW,GAAGtB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACpDqB,WAAW,CAACC,SAAS,GAAG,SAAS;IACjCD,WAAW,CAACpB,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvCmB,WAAW,CAACpB,KAAK,CAACE,GAAG,GAAG,MAAM;IAC9BkB,WAAW,CAACpB,KAAK,CAACsB,KAAK,GAAG,MAAM;IAChCF,WAAW,CAACpB,KAAK,CAACuB,MAAM,GAAG,MAAM;IACjCH,WAAW,CAACpB,KAAK,CAACwB,UAAU,GAAG,MAAM;IACrCJ,WAAW,CAACpB,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACnCL,WAAW,CAACpB,KAAK,CAAC0B,MAAM,GAAG,SAAS;IACpCN,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnCE,WAAW,CAACpB,KAAK,CAAC2B,UAAU,GAAG,YAAY;IAC3CP,WAAW,CAACQ,WAAW,GAAG,MAAMR,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnEE,WAAW,CAACS,UAAU,GAAG,MAAMT,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IAClEE,WAAW,CAACU,OAAO,GAAG,MAAMhC,QAAQ,CAACiC,IAAI,CAACC,WAAW,CAACnC,QAAQ,CAAC;IAE/D;IACA,MAAMoC,MAAM,GAAGnC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5CkC,MAAM,CAACjC,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC7ByB,MAAM,CAACjC,KAAK,CAACU,UAAU,GAAG,QAAQ;IAClCuB,MAAM,CAACjC,KAAK,CAACkC,YAAY,GAAG,MAAM;IAClCD,MAAM,CAACjC,KAAK,CAACmC,aAAa,GAAG,MAAM;IACnCF,MAAM,CAACjC,KAAK,CAACoC,YAAY,GAAG,gBAAgB;IAE5C,MAAMC,IAAI,GAAGvC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CsC,IAAI,CAAChB,SAAS,GAAG,2FAA2F;IAE5G,MAAMiB,KAAK,GAAGxC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IAC1CuC,KAAK,CAACC,WAAW,GAAG,yBAAyB;IAC7CD,KAAK,CAACtC,KAAK,CAACwC,MAAM,GAAG,GAAG;IACxBF,KAAK,CAACtC,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC7Ba,KAAK,CAACtC,KAAK,CAACyC,UAAU,GAAG,KAAK;IAC9BH,KAAK,CAACtC,KAAK,CAACkB,KAAK,GAAG,SAAS;IAE7Be,MAAM,CAACS,WAAW,CAACL,IAAI,CAAC;IACxBJ,MAAM,CAACS,WAAW,CAACJ,KAAK,CAAC;IAEzB;IACA,MAAMK,gBAAgB,GAAG7C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACtD4C,gBAAgB,CAAC3C,KAAK,CAACQ,OAAO,GAAG,MAAM;IACvCmC,gBAAgB,CAAC3C,KAAK,CAAC4C,aAAa,GAAG,QAAQ;IAC/CD,gBAAgB,CAAC3C,KAAK,CAAC6C,GAAG,GAAG,MAAM;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9E;IACA,IAAInD,MAAM,CAACtF,KAAK,IAAIsF,MAAM,CAACtF,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMwI,IAAI,GAAGpD,MAAM,CAACtF,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,IAAI0I,IAAI,CAACC,OAAO,EAAE;QAChB,MAAMC,WAAW,GAAGpD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDmD,WAAW,CAAClD,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClC0C,WAAW,CAAClD,KAAK,CAACU,UAAU,GAAG,QAAQ;QACvCwC,WAAW,CAAClD,KAAK,CAACkC,YAAY,GAAG,MAAM;QAEvC;QACA,IAAIc,IAAI,CAACC,OAAO,CAACE,aAAa,EAAE;UAC9B,MAAMC,WAAW,GAAGtD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDqD,WAAW,CAACC,GAAG,GAAGL,IAAI,CAACC,OAAO,CAACE,aAAa;UAC5CC,WAAW,CAACE,GAAG,GAAGN,IAAI,CAACC,OAAO,CAACzJ,IAAI;UACnC4J,WAAW,CAACpD,KAAK,CAACK,MAAM,GAAG,MAAM;UACjC+C,WAAW,CAACpD,KAAK,CAACuD,WAAW,GAAG,MAAM;UACtCL,WAAW,CAACR,WAAW,CAACU,WAAW,CAAC;SACrC,MAAM;UACL,MAAMI,WAAW,GAAG1D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDyD,WAAW,CAACnC,SAAS,GAAG,wFAAwF;UAChH6B,WAAW,CAACR,WAAW,CAACc,WAAW,CAAC;;QAGtC,MAAMC,WAAW,GAAG3D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjD0D,WAAW,CAACpC,SAAS,GAAG,WAAW2B,IAAI,CAACC,OAAO,CAACzJ,IAAI,cAAcwJ,IAAI,CAACC,OAAO,CAACS,iBAAiB,GAAG;QACnGD,WAAW,CAACzD,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCyB,WAAW,CAACR,WAAW,CAACe,WAAW,CAAC;QAEpCX,WAAW,CAACJ,WAAW,CAACQ,WAAW,CAAC;;MAGtC;MACA,MAAMS,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC,eAAe,EAAEZ,IAAI,CAACa,QAAQ,IAAI,KAAK,CAAC;MACnFf,WAAW,CAACJ,WAAW,CAACiB,eAAe,CAAC;MAExC;MACA,MAAMG,aAAa,GAAG,IAAI,CAACF,aAAa,CAAC,aAAa,EAAE,IAAIhG,IAAI,CAACoF,IAAI,CAACe,UAAU,CAAC,CAACC,kBAAkB,EAAE,CAAC;MACvGlB,WAAW,CAACJ,WAAW,CAACoB,aAAa,CAAC;MAEtC;MACA,MAAMG,WAAW,GAAG,IAAI,CAACL,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC1H,cAAc,CAAC8G,IAAI,CAAC7G,QAAQ,CAAC,CAAC;MACtF2G,WAAW,CAACJ,WAAW,CAACuB,WAAW,CAAC;MAEpC;MACA,IAAIjB,IAAI,CAACnE,WAAW,EAAE;QACpB,MAAMqF,QAAQ,GAAG,IAAI,CAACN,aAAa,CAAC,OAAO,EAAE,GAAGZ,IAAI,CAACnE,WAAW,CAACrF,IAAI,KAAKwJ,IAAI,CAACnE,WAAW,CAACpF,IAAI,GAAG,CAAC;QACnGqJ,WAAW,CAACJ,WAAW,CAACwB,QAAQ,CAAC;;MAGnC;MACA,MAAMC,QAAQ,GAAG,IAAI,CAACP,aAAa,CAAC,OAAO,EAAEZ,IAAI,CAACoB,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,GAAGpB,IAAI,CAACoB,SAAS,UAAU,CAAC;MAClHtB,WAAW,CAACJ,WAAW,CAACyB,QAAQ,CAAC;;IAGnC;IACA,MAAME,YAAY,GAAG,IAAI,CAACtB,aAAa,CAAC,eAAe,EAAE,UAAU,CAAC;IAEpE,IAAInD,MAAM,CAACtF,KAAK,IAAIsF,MAAM,CAACtF,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMwI,IAAI,GAAGpD,MAAM,CAACtF,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,MAAMgK,WAAW,GAAGxE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACjDuE,WAAW,CAACtE,KAAK,CAACQ,OAAO,GAAG,MAAM;MAClC8D,WAAW,CAACtE,KAAK,CAACU,UAAU,GAAG,QAAQ;MACvC4D,WAAW,CAACtE,KAAK,CAACS,cAAc,GAAG,eAAe;MAClD6D,WAAW,CAACtE,KAAK,CAACwC,MAAM,GAAG,QAAQ;MACnC8B,WAAW,CAACtE,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEvC;MACA,MAAMrE,SAAS,GAAGkE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/CnE,SAAS,CAACoE,KAAK,CAACuE,SAAS,GAAG,QAAQ;MACpC3I,SAAS,CAACoE,KAAK,CAACwE,IAAI,GAAG,GAAG;MAE1B,IAAIxB,IAAI,CAACpH,SAAS,EAAE;QAClB,MAAM6I,aAAa,GAAG3E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnD0E,aAAa,CAACzE,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCgD,aAAa,CAACzE,KAAK,CAACyC,UAAU,GAAG,MAAM;QACvCgC,aAAa,CAAClC,WAAW,GAAG,IAAI3E,IAAI,CAACoF,IAAI,CAACpH,SAAS,CAACK,IAAI,CAAC,CAACyI,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAErH,MAAMC,gBAAgB,GAAG/E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACtD8E,gBAAgB,CAAC7E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACxCoD,gBAAgB,CAAC7E,KAAK,CAAC8E,SAAS,GAAG,KAAK;QACxCD,gBAAgB,CAACxD,SAAS,GAAG,WAAW2B,IAAI,CAACpH,SAAS,CAACS,OAAO,EAAE5C,IAAI,IAAI,KAAK,WAAW;QAExF,MAAMsL,aAAa,GAAGjF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnDgF,aAAa,CAAC/E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCsD,aAAa,CAAC/E,KAAK,CAACkB,KAAK,GAAG,MAAM;QAClC6D,aAAa,CAACxC,WAAW,GAAGS,IAAI,CAACpH,SAAS,CAACzC,IAAI,EAAEK,IAAI,IAAI,KAAK;QAE9DoC,SAAS,CAAC8G,WAAW,CAAC+B,aAAa,CAAC;QACpC7I,SAAS,CAAC8G,WAAW,CAACmC,gBAAgB,CAAC;QACvCjJ,SAAS,CAAC8G,WAAW,CAACqC,aAAa,CAAC;;MAGtC;MACA,MAAMC,cAAc,GAAGlF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACpDiF,cAAc,CAAChF,KAAK,CAACwE,IAAI,GAAG,GAAG;MAC/BQ,cAAc,CAAChF,KAAK,CAACQ,OAAO,GAAG,MAAM;MACrCwE,cAAc,CAAChF,KAAK,CAACU,UAAU,GAAG,QAAQ;MAC1CsE,cAAc,CAAChF,KAAK,CAACS,cAAc,GAAG,QAAQ;MAC9CuE,cAAc,CAAChF,KAAK,CAACY,OAAO,GAAG,QAAQ;MAEvC,MAAMqE,IAAI,GAAGnF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC1CkF,IAAI,CAACjF,KAAK,CAACK,MAAM,GAAG,KAAK;MACzB4E,IAAI,CAACjF,KAAK,CAACM,eAAe,GAAG,MAAM;MACnC2E,IAAI,CAACjF,KAAK,CAACI,KAAK,GAAG,MAAM;MACzB6E,IAAI,CAACjF,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEhC,MAAMiF,KAAK,GAAGpF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC3CmF,KAAK,CAAC7D,SAAS,GAAG,iGAAiG;MACnH6D,KAAK,CAAClF,KAAK,CAACC,QAAQ,GAAG,UAAU;MACjCiF,KAAK,CAAClF,KAAK,CAACE,GAAG,GAAG,MAAM;MACxBgF,KAAK,CAAClF,KAAK,CAACG,IAAI,GAAG,KAAK;MACxB+E,KAAK,CAAClF,KAAK,CAACmF,UAAU,GAAG,MAAM;MAC/BD,KAAK,CAAClF,KAAK,CAACM,eAAe,GAAG,OAAO;MACrC4E,KAAK,CAAClF,KAAK,CAACY,OAAO,GAAG,OAAO;MAE7BqE,IAAI,CAACvC,WAAW,CAACwC,KAAK,CAAC;MACvBF,cAAc,CAACtC,WAAW,CAACuC,IAAI,CAAC;MAEhC;MACA,MAAMpJ,OAAO,GAAGiE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7ClE,OAAO,CAACmE,KAAK,CAACuE,SAAS,GAAG,QAAQ;MAClC1I,OAAO,CAACmE,KAAK,CAACwE,IAAI,GAAG,GAAG;MAExB,IAAIxB,IAAI,CAACnH,OAAO,EAAE;QAChB,MAAMuJ,WAAW,GAAGtF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDqF,WAAW,CAACpF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnC2D,WAAW,CAACpF,KAAK,CAACyC,UAAU,GAAG,MAAM;QACrC2C,WAAW,CAAC7C,WAAW,GAAG,IAAI3E,IAAI,CAACoF,IAAI,CAACnH,OAAO,CAACI,IAAI,CAAC,CAACyI,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAEjH,MAAMS,cAAc,GAAGvF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACpDsF,cAAc,CAACrF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACtC4D,cAAc,CAACrF,KAAK,CAAC8E,SAAS,GAAG,KAAK;QACtCO,cAAc,CAAChE,SAAS,GAAG,WAAW2B,IAAI,CAACnH,OAAO,CAACQ,OAAO,EAAE5C,IAAI,IAAI,KAAK,WAAW;QAEpF,MAAM6L,WAAW,GAAGxF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDuF,WAAW,CAACtF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnC6D,WAAW,CAACtF,KAAK,CAACkB,KAAK,GAAG,MAAM;QAChCoE,WAAW,CAAC/C,WAAW,GAAGS,IAAI,CAACnH,OAAO,CAAC1C,IAAI,EAAEK,IAAI,IAAI,KAAK;QAE1DqC,OAAO,CAAC6G,WAAW,CAAC0C,WAAW,CAAC;QAChCvJ,OAAO,CAAC6G,WAAW,CAAC2C,cAAc,CAAC;QACnCxJ,OAAO,CAAC6G,WAAW,CAAC4C,WAAW,CAAC;;MAGlChB,WAAW,CAAC5B,WAAW,CAAC9G,SAAS,CAAC;MAClC0I,WAAW,CAAC5B,WAAW,CAACsC,cAAc,CAAC;MACvCV,WAAW,CAAC5B,WAAW,CAAC7G,OAAO,CAAC;MAEhCwI,YAAY,CAAC3B,WAAW,CAAC4B,WAAW,CAAC;MAErC;MACA,IAAItB,IAAI,CAACzI,QAAQ,IAAIyI,IAAI,CAACzI,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAM+K,aAAa,GAAGzF,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAClDwF,aAAa,CAAChD,WAAW,GAAG,iBAAiB;QAC7CgD,aAAa,CAACvF,KAAK,CAAC8E,SAAS,GAAG,MAAM;QACtCS,aAAa,CAACvF,KAAK,CAACkC,YAAY,GAAG,MAAM;QACzCqD,aAAa,CAACvF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC8D,aAAa,CAACvF,KAAK,CAACyC,UAAU,GAAG,KAAK;QACtC4B,YAAY,CAAC3B,WAAW,CAAC6C,aAAa,CAAC;QAEvC,MAAMC,YAAY,GAAG1F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClDyF,YAAY,CAACxF,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnCgF,YAAY,CAACxF,KAAK,CAAC4C,aAAa,GAAG,QAAQ;QAC3C4C,YAAY,CAACxF,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/BG,IAAI,CAACzI,QAAQ,CAACkL,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAI;UACvC,MAAMC,WAAW,GAAG9F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjD6F,WAAW,CAAC5F,KAAK,CAACY,OAAO,GAAG,MAAM;UAClCgF,WAAW,CAAC5F,KAAK,CAACM,eAAe,GAAG,SAAS;UAC7CsF,WAAW,CAAC5F,KAAK,CAACa,YAAY,GAAG,KAAK;UACtC+E,WAAW,CAAC5F,KAAK,CAACuB,MAAM,GAAG,gBAAgB;UAE3C,MAAMsE,aAAa,GAAG/F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACnD8F,aAAa,CAAC7F,KAAK,CAACQ,OAAO,GAAG,MAAM;UACpCqF,aAAa,CAAC7F,KAAK,CAACS,cAAc,GAAG,eAAe;UACpDoF,aAAa,CAAC7F,KAAK,CAACkC,YAAY,GAAG,MAAM;UACzC2D,aAAa,CAAC7F,KAAK,CAACmC,aAAa,GAAG,MAAM;UAC1C0D,aAAa,CAAC7F,KAAK,CAACoC,YAAY,GAAG,gBAAgB;UAEnD,MAAM0D,YAAY,GAAGhG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClD+F,YAAY,CAACzE,SAAS,GAAG,mBAAmBsE,KAAK,GAAG,CAAC,cAAcD,OAAO,CAACzC,OAAO,EAAEzJ,IAAI,IAAI,SAAS,IAAIkM,OAAO,CAAC7B,QAAQ,EAAE;UAE3H,MAAMkC,eAAe,GAAGjG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACrDgG,eAAe,CAACxD,WAAW,GAAG,IAAI,CAACrG,cAAc,CAACwJ,OAAO,CAACvJ,QAAQ,CAAC;UACnE4J,eAAe,CAAC/F,KAAK,CAACkB,KAAK,GAAG,MAAM;UAEpC2E,aAAa,CAACnD,WAAW,CAACoD,YAAY,CAAC;UACvCD,aAAa,CAACnD,WAAW,CAACqD,eAAe,CAAC;UAC1CH,WAAW,CAAClD,WAAW,CAACmD,aAAa,CAAC;UAEtC;UACA,MAAMG,YAAY,GAAGlG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClDiG,YAAY,CAAChG,KAAK,CAACQ,OAAO,GAAG,MAAM;UACnCwF,YAAY,CAAChG,KAAK,CAACU,UAAU,GAAG,QAAQ;UACxCsF,YAAY,CAAChG,KAAK,CAACS,cAAc,GAAG,eAAe;UAEnD;UACA,MAAMwF,gBAAgB,GAAGnG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACtDkG,gBAAgB,CAACjG,KAAK,CAACwE,IAAI,GAAG,GAAG;UAEjC,IAAIkB,OAAO,CAAC9J,SAAS,EAAE;YACrB,MAAMsK,OAAO,GAAGpG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CmG,OAAO,CAAClG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCyD,OAAO,CAAC3D,WAAW,GAAG,IAAI3E,IAAI,CAAC8H,OAAO,CAAC9J,SAAS,CAACK,IAAI,CAAC,CAACyI,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAElH,MAAMuB,UAAU,GAAGrG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChDoG,UAAU,CAACnG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClC0E,UAAU,CAAC5D,WAAW,GAAG,GAAGmD,OAAO,CAAC9J,SAAS,CAACS,OAAO,EAAE5C,IAAI,IAAI,KAAK,KAAKiM,OAAO,CAAC9J,SAAS,CAACzC,IAAI,EAAEK,IAAI,IAAI,KAAK,GAAG;YAEjHyM,gBAAgB,CAACvD,WAAW,CAACwD,OAAO,CAAC;YACrCD,gBAAgB,CAACvD,WAAW,CAACyD,UAAU,CAAC;;UAG1C;UACA,MAAMC,KAAK,GAAGtG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAC3CqG,KAAK,CAAC/E,SAAS,GAAG,qFAAqF;UAEvG;UACA,MAAMgF,cAAc,GAAGvG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACpDsG,cAAc,CAACrG,KAAK,CAACwE,IAAI,GAAG,GAAG;UAC/B6B,cAAc,CAACrG,KAAK,CAACuE,SAAS,GAAG,OAAO;UAExC,IAAImB,OAAO,CAAC7J,OAAO,EAAE;YACnB,MAAMyK,OAAO,GAAGxG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CuG,OAAO,CAACtG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjC6D,OAAO,CAAC/D,WAAW,GAAG,IAAI3E,IAAI,CAAC8H,OAAO,CAAC7J,OAAO,CAACI,IAAI,CAAC,CAACyI,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAEhH,MAAM2B,UAAU,GAAGzG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChDwG,UAAU,CAACvG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClC8E,UAAU,CAAChE,WAAW,GAAG,GAAGmD,OAAO,CAAC7J,OAAO,CAACQ,OAAO,EAAE5C,IAAI,IAAI,KAAK,KAAKiM,OAAO,CAAC7J,OAAO,CAAC1C,IAAI,EAAEK,IAAI,IAAI,KAAK,GAAG;YAE7G6M,cAAc,CAAC3D,WAAW,CAAC4D,OAAO,CAAC;YACnCD,cAAc,CAAC3D,WAAW,CAAC6D,UAAU,CAAC;;UAGxCP,YAAY,CAACtD,WAAW,CAACuD,gBAAgB,CAAC;UAC1CD,YAAY,CAACtD,WAAW,CAAC0D,KAAK,CAAC;UAC/BJ,YAAY,CAACtD,WAAW,CAAC2D,cAAc,CAAC;UACxCT,WAAW,CAAClD,WAAW,CAACsD,YAAY,CAAC;UAErCR,YAAY,CAAC9C,WAAW,CAACkD,WAAW,CAAC;UAErC;UACA,IAAID,KAAK,GAAG3C,IAAI,CAACzI,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACpC,MAAMgM,OAAO,GAAG1G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CyG,OAAO,CAACxG,KAAK,CAACuE,SAAS,GAAG,QAAQ;YAClCiC,OAAO,CAACxG,KAAK,CAACY,OAAO,GAAG,MAAM;YAC9B4F,OAAO,CAACxG,KAAK,CAACkB,KAAK,GAAG,SAAS;YAC/BsF,OAAO,CAACxG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAE/B;YACA,MAAMgF,cAAc,GAAG,IAAI7I,IAAI,CAAC8H,OAAO,CAAC7J,OAAO,EAAEI,IAAI,IAAI,CAAC,CAAC,CAACyK,OAAO,EAAE;YACrE,MAAMC,aAAa,GAAG,IAAI/I,IAAI,CAACoF,IAAI,CAACzI,QAAQ,CAACoL,KAAK,GAAG,CAAC,CAAC,CAAC/J,SAAS,EAAEK,IAAI,IAAI,CAAC,CAAC,CAACyK,OAAO,EAAE;YACvF,MAAME,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,aAAa,GAAGF,cAAc,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;YAE9ED,OAAO,CAACnF,SAAS,GAAG,yCAAyC,IAAI,CAACnF,cAAc,CAAC0K,WAAW,CAAC,eAAelB,OAAO,CAAC7J,OAAO,EAAE1C,IAAI,EAAEK,IAAI,IAAI,iBAAiB,EAAE;YAE9JgM,YAAY,CAAC9C,WAAW,CAAC8D,OAAO,CAAC;;QAErC,CAAC,CAAC;QAEFnC,YAAY,CAAC3B,WAAW,CAAC8C,YAAY,CAAC;;;IAI1C;IACA,MAAMuB,aAAa,GAAG,IAAI,CAAChE,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAE5D,IAAInD,MAAM,CAACnF,MAAM,IAAImF,MAAM,CAACnF,MAAM,CAACD,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAMwM,UAAU,GAAGlH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAChDiH,UAAU,CAAChH,KAAK,CAACQ,OAAO,GAAG,MAAM;MACjCwG,UAAU,CAAChH,KAAK,CAAC4C,aAAa,GAAG,QAAQ;MACzCoE,UAAU,CAAChH,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE7BjD,MAAM,CAACnF,MAAM,CAACgL,OAAO,CAAC,CAACwB,KAAK,EAAEtB,KAAK,KAAI;QACrC,MAAMuB,SAAS,GAAGpH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC/CmH,SAAS,CAAClH,KAAK,CAACY,OAAO,GAAG,MAAM;QAChCsG,SAAS,CAAClH,KAAK,CAACM,eAAe,GAAG,SAAS;QAC3C4G,SAAS,CAAClH,KAAK,CAACa,YAAY,GAAG,KAAK;QACpCqG,SAAS,CAAClH,KAAK,CAACuB,MAAM,GAAG,gBAAgB;QAEzC;QACA,MAAM4F,WAAW,GAAGrH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDoH,WAAW,CAACnH,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClC2G,WAAW,CAACnH,KAAK,CAACS,cAAc,GAAG,eAAe;QAClD0G,WAAW,CAACnH,KAAK,CAACkC,YAAY,GAAG,MAAM;QACvCiF,WAAW,CAACnH,KAAK,CAACmC,aAAa,GAAG,MAAM;QACxCgF,WAAW,CAACnH,KAAK,CAACoC,YAAY,GAAG,gBAAgB;QAEjD,MAAMgF,UAAU,GAAGtH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChDqH,UAAU,CAAC/F,SAAS,GAAG,iBAAiBsE,KAAK,GAAG,CAAC,WAAW;QAC5DyB,UAAU,CAACpH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAElC,MAAM4F,UAAU,GAAGvH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChDsH,UAAU,CAACrH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAClC4F,UAAU,CAACrH,KAAK,CAACyC,UAAU,GAAG,MAAM;QACpC4E,UAAU,CAACrH,KAAK,CAACkB,KAAK,GAAG,SAAS;QAClCmG,UAAU,CAAC9E,WAAW,GAAG,GAAG0E,KAAK,CAACK,KAAK,CAACC,MAAM,IAAIN,KAAK,CAACK,KAAK,CAACtI,QAAQ,EAAE;QAExEmI,WAAW,CAACzE,WAAW,CAAC0E,UAAU,CAAC;QACnCD,WAAW,CAACzE,WAAW,CAAC2E,UAAU,CAAC;QACnCH,SAAS,CAACxE,WAAW,CAACyE,WAAW,CAAC;QAElC;QACA,MAAMK,YAAY,GAAG1H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClDyH,YAAY,CAACxH,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnCgH,YAAY,CAACxH,KAAK,CAACyH,mBAAmB,GAAG,uCAAuC;QAChFD,YAAY,CAACxH,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B;QACA,MAAM6E,OAAO,GAAG,IAAI,CAAC9D,aAAa,CAAC,UAAU,EAAEqD,KAAK,CAACS,OAAO,IAAIT,KAAK,CAACtL,EAAE,IAAI,KAAK,CAAC;QAClF+L,OAAO,CAAC1H,KAAK,CAAC2H,UAAU,GAAG,QAAQ;QACnCH,YAAY,CAAC9E,WAAW,CAACgF,OAAO,CAAC;QAEjC;QACA,MAAME,iBAAiB,GAAGX,KAAK,CAACY,YAAY,KAAKC,SAAS,GAAGb,KAAK,CAACY,YAAY,GACrDZ,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACC,kBAAkB,GAAG,CAAE;QACjF,MAAMH,YAAY,GAAG,IAAI,CAACjE,aAAa,CAAC,cAAc,EAAEgE,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,eAAe,CAAC;QAC9GJ,YAAY,CAAC9E,WAAW,CAACmF,YAAY,CAAC;QAEtC;QACA,IAAIZ,KAAK,CAACgB,SAAS,EAAE;UACnB,MAAMC,OAAO,GAAG,IAAI,CAACtE,aAAa,CAAC,YAAY,EAAE,IAAIhG,IAAI,CAACqJ,KAAK,CAACgB,SAAS,CAAC,CAACE,cAAc,EAAE,CAAC;UAC5FX,YAAY,CAAC9E,WAAW,CAACwF,OAAO,CAAC;;QAGnC;QACA,IAAIjB,KAAK,CAACmB,WAAW,EAAE;UACrB,MAAMA,WAAW,GAAG,IAAI,CAACxE,aAAa,CAAC,cAAc,EAAEqD,KAAK,CAACmB,WAAW,CAAC5O,IAAI,CAAC;UAC9EgO,YAAY,CAAC9E,WAAW,CAAC0F,WAAW,CAAC;;QAGvC;QACA,IAAInB,KAAK,CAACoB,cAAc,EAAE;UACxB,MAAMC,UAAU,GAAG,IAAI,CAAC1E,aAAa,CAAC,YAAY,EAAEqD,KAAK,CAACoB,cAAc,CAACC,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC;UACnGd,YAAY,CAAC9E,WAAW,CAAC4F,UAAU,CAAC;;QAGtCpB,SAAS,CAACxE,WAAW,CAAC8E,YAAY,CAAC;QAEnC;QACA,IAAIP,KAAK,CAACsB,mBAAmB,IAAItB,KAAK,CAACsB,mBAAmB,CAAC/N,MAAM,GAAG,CAAC,EAAE;UACrE,MAAMgO,YAAY,GAAG1I,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;UACjDyI,YAAY,CAACjG,WAAW,GAAG,qBAAqB;UAChDiG,YAAY,CAACxI,KAAK,CAAC8E,SAAS,GAAG,MAAM;UACrC0D,YAAY,CAACxI,KAAK,CAACkC,YAAY,GAAG,MAAM;UACxCsG,YAAY,CAACxI,KAAK,CAACyB,QAAQ,GAAG,MAAM;UACpC+G,YAAY,CAACxI,KAAK,CAACyC,UAAU,GAAG,KAAK;UACrCyE,SAAS,CAACxE,WAAW,CAAC8F,YAAY,CAAC;UAEnC,MAAMC,WAAW,GAAG3I,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;UAChD0I,WAAW,CAACzI,KAAK,CAAC0I,SAAS,GAAG,MAAM;UACpCD,WAAW,CAACzI,KAAK,CAACY,OAAO,GAAG,GAAG;UAC/B6H,WAAW,CAACzI,KAAK,CAACwC,MAAM,GAAG,GAAG;UAE9ByE,KAAK,CAACsB,mBAAmB,CAAC9C,OAAO,CAACkD,OAAO,IAAG;YAC1C,MAAMC,WAAW,GAAG9I,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;YAChD6I,WAAW,CAAC5I,KAAK,CAACkC,YAAY,GAAG,KAAK;YACtC0G,WAAW,CAACvH,SAAS,GAAG,2EAA2E,IAAI,CAACwH,kBAAkB,CAACF,OAAO,CAACG,WAAW,CAAC,EAAE;YACjJL,WAAW,CAAC/F,WAAW,CAACkG,WAAW,CAAC;UACtC,CAAC,CAAC;UAEF1B,SAAS,CAACxE,WAAW,CAAC+F,WAAW,CAAC;;QAGpCzB,UAAU,CAACtE,WAAW,CAACwE,SAAS,CAAC;MACnC,CAAC,CAAC;MAEFH,aAAa,CAACrE,WAAW,CAACsE,UAAU,CAAC;;IAGvC;IACA,IAAIpH,MAAM,CAACtF,KAAK,IAAIsF,MAAM,CAACtF,KAAK,CAAC,CAAC,CAAC,IAAIsF,MAAM,CAACtF,KAAK,CAAC,CAAC,CAAC,CAACyO,QAAQ,IAAInJ,MAAM,CAACtF,KAAK,CAAC,CAAC,CAAC,CAACyO,QAAQ,CAACvO,MAAM,GAAG,CAAC,EAAE;MACtG,MAAMwO,eAAe,GAAG,IAAI,CAACjG,aAAa,CAAC,UAAU,EAAE,mBAAmB,CAAC;MAE3E,MAAMkG,YAAY,GAAGnJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;MACjDkJ,YAAY,CAACjJ,KAAK,CAAC0I,SAAS,GAAG,MAAM;MACrCO,YAAY,CAACjJ,KAAK,CAACY,OAAO,GAAG,GAAG;MAChCqI,YAAY,CAACjJ,KAAK,CAACwC,MAAM,GAAG,GAAG;MAC/ByG,YAAY,CAACjJ,KAAK,CAACQ,OAAO,GAAG,MAAM;MACnCyI,YAAY,CAACjJ,KAAK,CAACyH,mBAAmB,GAAG,uCAAuC;MAChFwB,YAAY,CAACjJ,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE/BjD,MAAM,CAACtF,KAAK,CAAC,CAAC,CAAC,CAACyO,QAAQ,CAACtD,OAAO,CAACyD,OAAO,IAAG;QACzC,MAAMC,WAAW,GAAGrJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAChDoJ,WAAW,CAACnJ,KAAK,CAACY,OAAO,GAAG,MAAM;QAClCuI,WAAW,CAACnJ,KAAK,CAACM,eAAe,GAAG,SAAS;QAC7C6I,WAAW,CAACnJ,KAAK,CAACa,YAAY,GAAG,KAAK;QACtCsI,WAAW,CAAC9H,SAAS,GAAG,2EAA2E6H,OAAO,CAAC1P,IAAI,IAAI,SAAS,EAAE;QAC9HyP,YAAY,CAACvG,WAAW,CAACyG,WAAW,CAAC;MACvC,CAAC,CAAC;MAEFH,eAAe,CAACtG,WAAW,CAACuG,YAAY,CAAC;MACzCtG,gBAAgB,CAACD,WAAW,CAACsG,eAAe,CAAC;;IAG/C;IACArG,gBAAgB,CAACD,WAAW,CAACI,WAAW,CAAC;IACzCH,gBAAgB,CAACD,WAAW,CAAC2B,YAAY,CAAC;IAC1C1B,gBAAgB,CAACD,WAAW,CAACqE,aAAa,CAAC;IAE3C;IACApG,YAAY,CAAC+B,WAAW,CAACtB,WAAW,CAAC;IACrCT,YAAY,CAAC+B,WAAW,CAACT,MAAM,CAAC;IAChCtB,YAAY,CAAC+B,WAAW,CAACC,gBAAgB,CAAC;IAC1C9C,QAAQ,CAAC6C,WAAW,CAAC/B,YAAY,CAAC;IAElC;IACAb,QAAQ,CAACiC,IAAI,CAACW,WAAW,CAAC7C,QAAQ,CAAC;EACrC;EAEA;EACQkD,aAAaA,CAACT,KAAa,EAAE8G,SAAiB;IACpD,MAAMC,OAAO,GAAGvJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7CsJ,OAAO,CAACrJ,KAAK,CAACM,eAAe,GAAG,SAAS;IACzC+I,OAAO,CAACrJ,KAAK,CAACa,YAAY,GAAG,KAAK;IAClCwI,OAAO,CAACrJ,KAAK,CAACY,OAAO,GAAG,MAAM;IAC9ByI,OAAO,CAACrJ,KAAK,CAACiB,SAAS,GAAG,+BAA+B;IAEzD,MAAMqI,aAAa,GAAGxJ,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACnDuJ,aAAa,CAACtJ,KAAK,CAACQ,OAAO,GAAG,MAAM;IACpC8I,aAAa,CAACtJ,KAAK,CAACU,UAAU,GAAG,QAAQ;IACzC4I,aAAa,CAACtJ,KAAK,CAACkC,YAAY,GAAG,MAAM;IAEzC,MAAMqH,IAAI,GAAGzJ,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCwJ,IAAI,CAACC,SAAS,GAAG,OAAOJ,SAAS,EAAE;IACnCG,IAAI,CAACvJ,KAAK,CAACkB,KAAK,GAAG,SAAS;IAC5BqI,IAAI,CAACvJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC5B8H,IAAI,CAACvJ,KAAK,CAACuD,WAAW,GAAG,MAAM;IAE/B,MAAMkG,YAAY,GAAG3J,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IACjD0J,YAAY,CAAClH,WAAW,GAAGD,KAAK;IAChCmH,YAAY,CAACzJ,KAAK,CAACwC,MAAM,GAAG,GAAG;IAC/BiH,YAAY,CAACzJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCgI,YAAY,CAACzJ,KAAK,CAACyC,UAAU,GAAG,KAAK;IAErC6G,aAAa,CAAC5G,WAAW,CAAC6G,IAAI,CAAC;IAC/BD,aAAa,CAAC5G,WAAW,CAAC+G,YAAY,CAAC;IACvCJ,OAAO,CAAC3G,WAAW,CAAC4G,aAAa,CAAC;IAElC,OAAOD,OAAO;EAChB;EAEA;EACQzF,aAAaA,CAAC7J,KAAa,EAAED,KAAa;IAChD,MAAM4P,GAAG,GAAG5J,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACzC2J,GAAG,CAAC1J,KAAK,CAACkC,YAAY,GAAG,MAAM;IAE/B,MAAMyH,YAAY,GAAG7J,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClD4J,YAAY,CAACpH,WAAW,GAAGxI,KAAK;IAChC4P,YAAY,CAAC3J,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCkI,YAAY,CAAC3J,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCyI,YAAY,CAAC3J,KAAK,CAACkC,YAAY,GAAG,KAAK;IAEvC,MAAM0H,YAAY,GAAG9J,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClD6J,YAAY,CAACrH,WAAW,GAAGzI,KAAK;IAChC8P,YAAY,CAAC5J,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAEpCiI,GAAG,CAAChH,WAAW,CAACiH,YAAY,CAAC;IAC7BD,GAAG,CAAChH,WAAW,CAACkH,YAAY,CAAC;IAE7B,OAAOF,GAAG;EACZ;EAEA;EACAhO,gBAAgBA,CAACkE,MAAc;IAC7B,IAAIA,MAAM,IAAIA,MAAM,CAACnF,MAAM,IAAImF,MAAM,CAACnF,MAAM,CAACD,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAIkN,OAAO,GAAG9H,MAAM,CAACnF,MAAM,CAAC,CAAC,CAAC,CAACiN,OAAO,IAAI9H,MAAM,CAACnF,MAAM,CAAC,CAAC,CAAC,CAACkB,EAAE;MAE7D;MACA,IAAIkO,QAAQ,GAAG,IAAI,CAAC3M,YAAY;MAEhC;MACA,IAAI,CAAC2M,QAAQ,EAAE;QACbA,QAAQ,GAAGjK,MAAM,CAACjE,EAAE;;MAGtB8D,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEmK,QAAQ,EAAE,cAAc,EAAEnC,OAAO,CAAC;MAExF;MACA,IAAI,CAAC7K,MAAM,CAACiN,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UACXF,QAAQ,EAAEA,QAAQ;UAClBnC,OAAO,EAAEA;;OAEZ,CAAC;KACH,MAAM;MACLjI,OAAO,CAACuK,KAAK,CAAC,sCAAsC,EAAEpK,MAAM,CAAC;;EAEjE;EAEAJ,gBAAgBA,CAAA;IACd;IACA;IACA;EAAA;EAGFD,iBAAiBA,CAAA;IACf;IACA,MAAMlB,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAACkM,GAAG,CAAC,uBAAuB,CAAC,EAAEnQ,KAAK,IAAI,CAAC;IACtF,MAAMyE,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAACkM,GAAG,CAAC,qBAAqB,CAAC,EAAEnQ,KAAK,IAAI,CAAC;IAElF,IAAI,CAAC8C,cAAc,CAACsN,kBAAkB,CAAC7L,qBAAqB,CAAC,CAAC8L,SAAS,CAACC,SAAS,IAAG;MAClF,IAAI,CAACtN,kBAAkB,GAAGsN,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAACxN,cAAc,CAACsN,kBAAkB,CAAC3L,mBAAmB,CAAC,CAAC4L,SAAS,CAACC,SAAS,IAAG;MAChF,IAAI,CAACrN,gBAAgB,GAAGqN,SAAS;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAACrM,UAAU,CAACkM,GAAG,CAAC,uBAAuB,CAAC,EAAEI,YAAY,CACvDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAAC1N,cAAc,CAACsN,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAACtN,kBAAkB,GAAGsN,SAAS;QACnC;QACA,IAAI,CAACrM,UAAU,CAACkM,GAAG,CAAC,mBAAmB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAACxM,UAAU,CAACkM,GAAG,CAAC,qBAAqB,CAAC,EAAEI,YAAY,CACrDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAAC1N,cAAc,CAACsN,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAACrN,gBAAgB,GAAGqN,SAAS;QACjC;QACA,IAAI,CAACrM,UAAU,CAACkM,GAAG,CAAC,iBAAiB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACtD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAACxM,UAAU,CAACkM,GAAG,CAAC,mBAAmB,CAAC,EAAEI,YAAY,CACnDG,IAAI,CACHpS,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACwB,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMwQ,YAAY,GAAG,IAAI,CAACvM,UAAU,CAACkM,GAAG,CAAC,uBAAuB,CAAC,EAAEnQ,KAAK,IAAI,CAAC;QAC7E;QACA,IAAIA,KAAK,CAACU,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACoC,cAAc,CAACsN,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9DjS,GAAG,CAAC6R,SAAS,IAAIA,SAAS,CAACK,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAAClR,IAAI,CAACmR,WAAW,EAAE,CAACC,QAAQ,CAAC9Q,KAAK,CAAC6Q,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACjR,IAAI,IAAIiR,QAAQ,CAACjR,IAAI,CAACkR,WAAW,EAAE,CAACC,QAAQ,CAAC9Q,KAAK,CAAC6Q,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAC/N,cAAc,CAACsN,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAO9R,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA2R,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACtN,kBAAkB,GAAGsN,SAAS;IACrC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACrM,UAAU,CAACkM,GAAG,CAAC,iBAAiB,CAAC,EAAEI,YAAY,CACjDG,IAAI,CACHpS,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACwB,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMwQ,YAAY,GAAG,IAAI,CAACvM,UAAU,CAACkM,GAAG,CAAC,qBAAqB,CAAC,EAAEnQ,KAAK,IAAI,CAAC;QAC3E;QACA,IAAIA,KAAK,CAACU,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACoC,cAAc,CAACsN,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9DjS,GAAG,CAAC6R,SAAS,IAAIA,SAAS,CAACK,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAAClR,IAAI,CAACmR,WAAW,EAAE,CAACC,QAAQ,CAAC9Q,KAAK,CAAC6Q,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACjR,IAAI,IAAIiR,QAAQ,CAACjR,IAAI,CAACkR,WAAW,EAAE,CAACC,QAAQ,CAAC9Q,KAAK,CAAC6Q,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAC/N,cAAc,CAACsN,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAO9R,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA2R,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACrN,gBAAgB,GAAGqN,SAAS;IACnC,CAAC,CAAC;EACN;EAEAS,eAAeA,CAACH,QAA+B;IAC7C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAII,WAAW,GAAGJ,QAAQ,CAAClR,IAAI;IAC/B,IAAIkR,QAAQ,CAACjR,IAAI,EAAE;MACjBqR,WAAW,IAAI,KAAKJ,QAAQ,CAACjR,IAAI,GAAG;;IAEtC,IAAIiR,QAAQ,CAAChR,IAAI,KAAKhB,YAAY,CAACqS,OAAO,IAAIL,QAAQ,CAACvR,IAAI,EAAE;MAC3D2R,WAAW,IAAI,MAAMJ,QAAQ,CAACvR,IAAI,EAAE;;IAEtC,OAAO2R,WAAW;EACpB;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjN,UAAU,CAACkN,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACnN,UAAU,CAAC;MAC1C;;IAGF,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC5C,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC6C,WAAW,GAAG,IAAI;IAEvB,MAAMkO,SAAS,GAAG,IAAI,CAACpN,UAAU,CAACjE,KAAK;IAEvC;IACA,MAAMsR,OAAO,GAAuB;MAClCC,WAAW,EAAEF,SAAS,CAAClN,WAAW;MAClCqN,YAAY,EAAEH,SAAS,CAAChN,YAAY;MACpCoN,OAAO,EAAEJ,SAAS,CAAC3M,aAAa;MAChCgN,kBAAkB,EAAE,CAClB;QACE7P,EAAE,EAAEwP,SAAS,CAAC/M,iBAAiB,EAAEzC,EAAE,IAAI,EAAE;QACzCjC,IAAI,EAAEyR,SAAS,CAAC9M;OACjB,CACF;MACDoN,gBAAgB,EAAE,CAChB;QACE9P,EAAE,EAAEwP,SAAS,CAAC7M,eAAe,EAAE3C,EAAE,IAAI,EAAE;QACvCjC,IAAI,EAAEyR,SAAS,CAAC5M;OACjB,CACF;MACDmN,UAAU,EAAE,CACV;QACEhS,IAAI,EAAEyR,SAAS,CAACvM,aAAa;QAC7B+M,KAAK,EAAER,SAAS,CAAC1M;OAClB,CACF;MACDmN,qBAAqB,EAAET,SAAS,CAACrM,OAAO;MACxC+M,oBAAoB,EAAE;QACpBC,oBAAoB,EAAE;UACpBzM,sBAAsB,EAAE8L,SAAS,CAAC9L;;OAErC;MACDJ,sBAAsB,EAAEkM,SAAS,CAAClM,sBAAsB;MACxDC,wBAAwB,EAAEiM,SAAS,CAACjM,wBAAwB;MAC5DC,6BAA6B,EAAEgM,SAAS,CAAChM,6BAA6B;MACtEC,mBAAmB,EAAE+L,SAAS,CAAC/L,mBAAmB;MAClD7B,aAAa,EAAE,CAAC4N,SAAS,CAACtM,WAAW,CAAC;MACtCkN,OAAO,EAAEZ,SAAS,CAACpM,OAAO;MAC1BiN,QAAQ,EAAEb,SAAS,CAACnM;KACrB;IAED,IAAI,CAACpC,cAAc,CAACqP,WAAW,CAACb,OAAO,CAAC,CACrCjB,SAAS,CAAC;MACT+B,IAAI,EAAGC,QAA6B,IAAI;QACtC,IAAI,CAACnP,SAAS,GAAG,KAAK;QACtB,IAAImP,QAAQ,CAAClK,MAAM,CAACmK,OAAO,EAAE;UAC3B,IAAI,CAAC5P,aAAa,GAAG2P,QAAQ,CAACpK,IAAI,CAACsK,OAAO;UAE1C;UACA5M,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4M,IAAI,CAACC,SAAS,CAACJ,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEpE;UACA,IAAIA,QAAQ,CAACpK,IAAI,IAAIoK,QAAQ,CAACpK,IAAI,CAACsK,OAAO,IAAIF,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC7R,MAAM,GAAG,CAAC,EAAE;YAC9EiF,OAAO,CAACzB,KAAK,CAAC,uBAAuB,CAAC;YACtCyB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEyM,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC7R,MAAM,CAAC;YAE3D;YACA,MAAMgS,iBAAiB,GAAGL,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC5B,MAAM,CAACgC,CAAC,IAAIA,CAAC,CAAChS,MAAM,IAAIgS,CAAC,CAAChS,MAAM,CAACD,MAAM,GAAG,CAAC,CAAC;YAC5FiF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8M,iBAAiB,CAAChS,MAAM,CAAC;YAE7D;YACA,MAAMkS,kBAAkB,GAAGF,iBAAiB,CAACG,OAAO,CAACF,CAAC,IAAIA,CAAC,CAAChS,MAAM,CAAClC,GAAG,CAACqU,CAAC,IACtEA,CAAC,CAAC/E,YAAY,KAAKC,SAAS,GAAG8E,CAAC,CAAC/E,YAAY,GAAI+E,CAAC,CAAC7E,QAAQ,GAAG6E,CAAC,CAAC7E,QAAQ,CAACC,kBAAkB,GAAG,CAAE,CACjG,CAAC;YACFvI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgN,kBAAkB,CAAC;YAEvD;YACA,MAAMG,kBAAkB,GAAGH,kBAAkB,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAI;cAChE,IAAIA,GAAG,KAAKlF,SAAS,EAAE;gBACrBiF,GAAG,CAACC,GAAG,CAAC,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEhC,OAAOD,GAAG;YACZ,CAAC,EAAE,EAA4B,CAAC;YAChCtN,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmN,kBAAkB,CAAC;YAEvD;YACA,MAAMI,iBAAiB,GAAGT,iBAAiB,CAAC/B,MAAM,CAACgC,CAAC,IAClDA,CAAC,CAAChS,MAAM,CAACyS,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACvE,cAAc,IAAIuE,CAAC,CAACvE,cAAc,CAACC,UAAU,KAAK,IAAI,CAAC,CAC7E;YACD7I,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuN,iBAAiB,CAACzS,MAAM,CAAC;YAE5DiF,OAAO,CAAC0N,QAAQ,EAAE;;UAGpB;UACA,IAAIhB,QAAQ,CAACpK,IAAI,IAAIoK,QAAQ,CAACpK,IAAI,CAAC8H,QAAQ,EAAE;YAC3C,IAAI,CAAC3M,YAAY,GAAGiP,QAAQ,CAACpK,IAAI,CAAC8H,QAAQ;YAC1CpK,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACxC,YAAY,CAAC;;UAErE;UAAA,KACK,IAAIiP,QAAQ,CAAClK,MAAM,IAAIkK,QAAQ,CAAClK,MAAM,CAACmL,SAAS,EAAE;YACrD,IAAI,CAAClQ,YAAY,GAAGiP,QAAQ,CAAClK,MAAM,CAACmL,SAAS;YAC7C3N,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACxC,YAAY,CAAC;;UAExE;UAAA,KACK,IAAIiP,QAAQ,CAACpK,IAAI,IAAIoK,QAAQ,CAACpK,IAAI,CAACsK,OAAO,IAAIF,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC7R,MAAM,GAAG,CAAC,IAAI2R,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC,CAAC,CAAC,CAAC1Q,EAAE,EAAE;YAClH,IAAI,CAACuB,YAAY,GAAGiP,QAAQ,CAACpK,IAAI,CAACsK,OAAO,CAAC,CAAC,CAAC,CAAC1Q,EAAE;YAC/C8D,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACxC,YAAY,CAAC;WAChE,MAAM;YACLuC,OAAO,CAACuK,KAAK,CAAC,qCAAqC,CAAC;YACpDvK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE2N,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAC,CAAC;YACzD,IAAIA,QAAQ,CAACpK,IAAI,EAAEtC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE2N,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAACpK,IAAI,CAAC,CAAC;YAC7E,IAAIoK,QAAQ,CAAClK,MAAM,EAAExC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2N,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAClK,MAAM,CAAC,CAAC;;SAEtF,MAAM;UACL,IAAI,CAAC7H,YAAY,GAAG,sDAAsD;UAC1E,IAAI+R,QAAQ,CAAClK,MAAM,CAACsL,QAAQ,IAAIpB,QAAQ,CAAClK,MAAM,CAACsL,QAAQ,CAAC/S,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAACJ,YAAY,GAAG+R,QAAQ,CAAClK,MAAM,CAACsL,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACDxD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChN,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC5C,YAAY,GAAG,wDAAwD;QAC5EqF,OAAO,CAACuK,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEA;EACAkB,oBAAoBA,CAACuC,SAAoB;IACvCJ,MAAM,CAACK,MAAM,CAACD,SAAS,CAACE,QAAQ,CAAC,CAAClI,OAAO,CAACmI,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAY1V,SAAS,EAAE;QAChC,IAAI,CAACgT,oBAAoB,CAAC0C,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA;EACA1R,cAAcA,CAAC4R,OAAe;IAC5B,MAAMC,KAAK,GAAGlH,IAAI,CAACC,KAAK,CAACgH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,KAAK,KAAKC,IAAI,KAAK;EAC/B;EAEA;EACAC,UAAUA,CAACC,UAAkB;IAC3B,MAAMjS,IAAI,GAAG,IAAI2B,IAAI,CAACsQ,UAAU,CAAC;IACjC,OAAOjS,IAAI,CAAC+H,kBAAkB,CAAC,OAAO,EAAE;MACtCmK,OAAO,EAAE,OAAO;MAChBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACd1J,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACA7I,WAAWA,CAAC6D,MAAc;IACxB,IAAI,CAACA,MAAM,CAACnF,MAAM,IAAImF,MAAM,CAACnF,MAAM,CAACD,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd,MAAM8T,QAAQ,GAAG1O,MAAM,CAACnF,MAAM,CAACqS,MAAM,CAAC,CAACpO,GAAG,EAAEuI,KAAK,KAC/CA,KAAK,CAACK,KAAK,CAACC,MAAM,GAAG7I,GAAG,CAAC4I,KAAK,CAACC,MAAM,GAAGN,KAAK,GAAGvI,GAAG,EAAEkB,MAAM,CAACnF,MAAM,CAAC,CAAC,CAAC,CAAC;IAExE,OAAO6T,QAAQ,CAAChH,KAAK,CAACiH,eAAe,IAAI,GAAGD,QAAQ,CAAChH,KAAK,CAACC,MAAM,IAAI+G,QAAQ,CAAChH,KAAK,CAACtI,QAAQ,EAAE;EAChG;EAEA;EACAwP,iBAAiBA,CAAC5O,MAAc;IAC9B;IACA,IAAI,CAACA,MAAM,CAACnF,MAAM,IAAImF,MAAM,CAACnF,MAAM,CAACD,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd;IACA,MAAMyM,KAAK,GAAGrH,MAAM,CAACnF,MAAM,CAAC,CAAC,CAAC;IAC9B,MAAMmN,iBAAiB,GAAGX,KAAK,CAACY,YAAY,KAAKC,SAAS,GAAGb,KAAK,CAACY,YAAY,GACrDZ,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IACjF,OAAOJ,iBAAiB,GAAG,CAAC;EAC9B;EAEA;EACA6G,yBAAyBA,CAAA;IACvB,MAAMnE,YAAY,GAAG,IAAI,CAACvM,UAAU,CAACkM,GAAG,CAAC,uBAAuB,CAAC,EAAEnQ,KAAK,IAAI,CAAC;IAC7E,IAAI,CAAC8C,cAAc,CAACsN,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACtN,kBAAkB,GAAGsN,SAAS;MACnC;MACA,MAAMsE,KAAK,GAAG5O,QAAQ,CAAC6O,cAAc,CAAC,mBAAmB,CAAqB;MAC9E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAC,uBAAuBA,CAAA;IACrB,MAAMzE,YAAY,GAAG,IAAI,CAACvM,UAAU,CAACkM,GAAG,CAAC,qBAAqB,CAAC,EAAEnQ,KAAK,IAAI,CAAC;IAC3E,IAAI,CAAC8C,cAAc,CAACsN,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACrN,gBAAgB,GAAGqN,SAAS;MACjC;MACA,MAAMsE,KAAK,GAAG5O,QAAQ,CAAC6O,cAAc,CAAC,iBAAiB,CAAqB;MAC5E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAE,aAAaA,CAAA;IACX,MAAM5Q,iBAAiB,GAAG,IAAI,CAACL,UAAU,CAACkM,GAAG,CAAC,mBAAmB,CAAC,EAAEnQ,KAAK;IACzE,MAAMuE,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAACkM,GAAG,CAAC,uBAAuB,CAAC,EAAEnQ,KAAK;IACjF,MAAMwE,eAAe,GAAG,IAAI,CAACP,UAAU,CAACkM,GAAG,CAAC,iBAAiB,CAAC,EAAEnQ,KAAK;IACrE,MAAMyE,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAACkM,GAAG,CAAC,qBAAqB,CAAC,EAAEnQ,KAAK;IAE7E,IAAI,CAACiE,UAAU,CAACkR,UAAU,CAAC;MACzB7Q,iBAAiB,EAAEE,eAAe;MAClCD,qBAAqB,EAAEE,mBAAmB;MAC1CD,eAAe,EAAEF,iBAAiB;MAClCG,mBAAmB,EAAEF;KACtB,CAAC;EACJ;EAEA;EACA6Q,oBAAoBA,CAAChB,UAAkB;IACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B;IACA,MAAMjS,IAAI,GAAG,IAAI2B,IAAI,CAACsQ,UAAU,CAAC;IACjC,OAAOjS,IAAI,CAACkM,cAAc,EAAE;EAC9B;EAEA;EACAU,kBAAkBA,CAACC,WAAmB;IACpC,QAAQA,WAAW;MACjB,KAAK,CAAC;QAAE,OAAO,eAAe;MAC9B,KAAK,CAAC;QAAE,OAAO,iBAAiB;MAChC,KAAK,CAAC;QAAE,OAAO,cAAc;MAC7B;QAAS,OAAO,SAAS;;EAE7B;EAEA;EACAqG,oBAAoBA,CAACvQ,aAAqB;IACxC,QAAQA,aAAa;MACnB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,WAAW;;EAE/B;EAEA;EACAwQ,oBAAoBA,CAACC,cAAmB,EAAEC,WAAgB;IACxD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAACxT,OAAO,IAAI,CAACwT,cAAc,CAACxT,OAAO,CAACI,IAAI,IAC1E,CAACqT,WAAW,IAAI,CAACA,WAAW,CAAC1T,SAAS,IAAI,CAAC0T,WAAW,CAAC1T,SAAS,CAACK,IAAI,EAAE;MACzE,OAAO,SAAS;;IAGlB,MAAMmJ,WAAW,GAAG,IAAIxH,IAAI,CAACyR,cAAc,CAACxT,OAAO,CAACI,IAAI,CAAC,CAACyK,OAAO,EAAE;IACnE,MAAMjC,aAAa,GAAG,IAAI7G,IAAI,CAAC0R,WAAW,CAAC1T,SAAS,CAACK,IAAI,CAAC,CAACyK,OAAO,EAAE;IACpE,MAAM6I,MAAM,GAAG9K,aAAa,GAAGW,WAAW;IAC1C,MAAMoK,QAAQ,GAAG3I,IAAI,CAACC,KAAK,CAACyI,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIC,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,KAAK;KACxB,MAAM;MACL,MAAMzB,KAAK,GAAGlH,IAAI,CAACC,KAAK,CAAC0I,QAAQ,GAAG,EAAE,CAAC;MACvC,MAAMxB,IAAI,GAAGwB,QAAQ,GAAG,EAAE;MAC1B,OAAO,GAAGzB,KAAK,KAAKC,IAAI,KAAK;;EAEjC;;;uBAvgCWvR,oBAAoB,EAAA7D,EAAA,CAAA6W,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/W,EAAA,CAAA6W,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAjX,EAAA,CAAA6W,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApBtT,oBAAoB;MAAAuT,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjC1X,EAAA,CAAAC,cAAA,aAAoC;UAOxBD,EAAA,CAAAqB,SAAA,WAA6B;UAC7BrB,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5BH,EAAA,CAAAC,cAAA,aAAkC;UAChCD,EAAA,CAAAE,MAAA,iEACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA2E;UAA5CD,EAAA,CAAAkC,UAAA,sBAAA0V,wDAAA;YAAA,OAAYD,GAAA,CAAAvF,QAAA,EAAU;UAAA,EAAC;UAEpDpS,EAAA,CAAAC,cAAA,cAAgC;UAEGD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAAC,cAAA,eAAsC;UAIND,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC1CH,EAAA,CAAAC,cAAA,sBAAwB;UAAAD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAInDH,EAAA,CAAAC,cAAA,eAAmC;UAEpBD,EAAA,CAAAE,MAAA,6CAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClDH,EAAA,CAAAC,cAAA,iBAK6C;UAAtCD,EAAA,CAAAkC,UAAA,mBAAA2V,sDAAA;YAAA,OAASF,GAAA,CAAA9B,yBAAA,EAA2B;UAAA,EAAC;UAL5C7V,EAAA,CAAAG,YAAA,EAK6C;UAC7CH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gCAAmF;UACjFD,EAAA,CAAAQ,UAAA,KAAAsX,2CAAA,yBAKa;UACf9X,EAAA,CAAAG,YAAA,EAAmB;UAM3BH,EAAA,CAAAC,cAAA,kBAAoE;UAA1BD,EAAA,CAAAkC,UAAA,mBAAA6V,uDAAA;YAAA,OAASJ,GAAA,CAAAvB,aAAA,EAAe;UAAA,EAAC;UACjEpW,EAAA,CAAAqB,SAAA,aAAmC;UACrCrB,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAuC;UACRD,EAAA,CAAAE,MAAA,oBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,eAAsC;UAIND,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC1CH,EAAA,CAAAC,cAAA,sBAAwB;UAAAD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAInDH,EAAA,CAAAC,cAAA,eAAmC;UAEpBD,EAAA,CAAAE,MAAA,6CAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClDH,EAAA,CAAAC,cAAA,iBAK2C;UAApCD,EAAA,CAAAkC,UAAA,mBAAA8V,sDAAA;YAAA,OAASL,GAAA,CAAAxB,uBAAA,EAAyB;UAAA,EAAC;UAL1CnW,EAAA,CAAAG,YAAA,EAK2C;UAC3CH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAC,cAAA,gCAAiF;UAC/ED,EAAA,CAAAQ,UAAA,KAAAyX,2CAAA,yBAKa;UACfjY,EAAA,CAAAG,YAAA,EAAmB;UAQ7BH,EAAA,CAAAC,cAAA,eAAuC;UAERD,EAAA,CAAAE,MAAA,2BAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,0BAAqC;UACxBD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAqB,SAAA,iBAA4G;UAG9GrB,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAC,cAAA,eAAwC;UACVD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAAC,cAAA,eAA8B;UAEfD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC3BH,EAAA,CAAAC,cAAA,sBAA+D;UAC7DD,EAAA,CAAAQ,UAAA,KAAA0X,2CAAA,yBAEa;UACflY,EAAA,CAAAG,YAAA,EAAa;UAGfH,EAAA,CAAAC,cAAA,0BAAqC;UACxBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAqB,SAAA,iBAAmG;UACrGrB,EAAA,CAAAG,YAAA,EAAiB;UAMvBH,EAAA,CAAAC,cAAA,eAAsC;UAETD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvCH,EAAA,CAAAC,cAAA,0BAAqC;UACxBD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAC,cAAA,sBAA2D;UACzDD,EAAA,CAAAQ,UAAA,KAAA2X,2CAAA,yBAEa;UACfnY,EAAA,CAAAG,YAAA,EAAa;UAIjBH,EAAA,CAAAC,cAAA,eAAsC;UAGhCD,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAe;UAMrBH,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAqB,SAAA,YAA6B;UAC7BrB,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOjBH,EAAA,CAAAC,cAAA,eAAwE;UACtED,EAAA,CAAAQ,UAAA,KAAA4X,oCAAA,kBAGM;UAENpY,EAAA,CAAAQ,UAAA,MAAA6X,qCAAA,kBAKM;UAENrY,EAAA,CAAAQ,UAAA,MAAA8X,qCAAA,kBAMM;UAENtY,EAAA,CAAAQ,UAAA,MAAA+X,qCAAA,kBAsFM;UACRvY,EAAA,CAAAG,YAAA,EAAM;;;;;;UA3PIH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAU,UAAA,cAAAiX,GAAA,CAAAxS,UAAA,CAAwB;UASJnF,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UACXV,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UAUlBV,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAU,UAAA,oBAAA8X,GAAA,CAAiC;UAIWxY,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAU,UAAA,gBAAAiX,GAAA,CAAA1F,eAAA,CAA+B;UAC/CjS,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAU,UAAA,YAAAiX,GAAA,CAAAzT,kBAAA,CAAqB;UAsB1ClE,EAAA,CAAAI,SAAA,IAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UACXV,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UAUlBV,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAU,UAAA,oBAAA+X,GAAA,CAA+B;UAIWzY,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAU,UAAA,gBAAAiX,GAAA,CAAA1F,eAAA,CAA+B;UAC7CjS,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAU,UAAA,YAAAiX,GAAA,CAAAxT,gBAAA,CAAmB;UAmB1CnE,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAU,UAAA,QAAAiX,GAAA,CAAA5S,OAAA,CAAe,kBAAA2T,GAAA;UACE1Y,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAU,UAAA,QAAAgY,GAAA,CAAc;UAWd1Y,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAU,UAAA,YAAAiX,GAAA,CAAApT,cAAA,CAAiB;UAqBZvE,EAAA,CAAAI,SAAA,IAAgB;UAAhBJ,EAAA,CAAAU,UAAA,YAAAiX,GAAA,CAAAhT,aAAA,CAAgB;UAkBd3E,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAU,UAAA,aAAAiX,GAAA,CAAAvT,SAAA,CAAsB;UAUpCpE,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAwD,WAAA,gBAAAmU,GAAA,CAAAtT,WAAA,CAAiC;UAC/DrE,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAU,UAAA,SAAAiX,GAAA,CAAAvT,SAAA,CAAe;UAKfpE,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAU,UAAA,SAAAiX,GAAA,CAAAnW,YAAA,KAAAmW,GAAA,CAAAvT,SAAA,CAAgC;UAOhCpE,EAAA,CAAAI,SAAA,GAA8E;UAA9EJ,EAAA,CAAAU,UAAA,UAAAiX,GAAA,CAAAvT,SAAA,IAAAuT,GAAA,CAAA/T,aAAA,CAAAhC,MAAA,UAAA+V,GAAA,CAAAtT,WAAA,KAAAsT,GAAA,CAAAnW,YAAA,CAA8E;UAQ9ExB,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAU,UAAA,UAAAiX,GAAA,CAAAvT,SAAA,IAAAuT,GAAA,CAAA/T,aAAA,CAAAhC,MAAA,KAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}