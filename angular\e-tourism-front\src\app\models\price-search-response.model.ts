// Modèles pour la réponse de recherche de prix
export interface PriceSearchResponse {
  header: Header;
  body: Body;
}

export interface Header {
  requestId: string;
  success: boolean;
  responseTime: string;
  messages: Message[];
}

export interface Message {
  id: number;
  code: string;
  messageType: number;
  message: string;
}

export interface Body {
  searchId: string;
  expiresOn: string;
  flights: Flight[];
}

export interface Flight {
  provider: number;
  id: string;
  items: FlightItem[];
  offers: Offer[];
  groupKeys: string[];
}

export interface FlightItem {
  id?: string;
  segmentNumber: number;
  flightNo: string;
  pnlName: string;
  flightDate: string;
  airline: Airline;
  marketingAirline: Airline;
  duration: number;
  dayChange: number;
  departure: FlightLocation;
  arrival: FlightLocation;
  flightClass: FlightClass;
  route: number;
  segments: Segment[];
  stopCount: number;
  flightProvider: FlightProvider;
  baggageInformations: BaggageInformation[];
  passengers: PassengerInfo[];
  flightType: number;
  services?: Service[];
  aircraft?: string;
}

export interface Airline {
  internationalCode: string;
  thumbnail: string;
  thumbnailFull: string;
  logo: string;
  logoFull: string;
  id: string;
  name: string;
}

export interface FlightLocation {
  country: Country;
  city: City;
  airport: Airport;
  date: string;
  geoLocation: GeoLocation;
}

export interface Country {
  name: string;
  provider: number;
  isTopRegion: boolean;
  id: string;
}

export interface City {
  name: string;
  provider: number;
  isTopRegion: boolean;
  id: string;
}

export interface Airport {
  name: string;
  code: string;
  provider: number;
  id: string;
  geolocation?: GeoLocation;
}

export interface GeoLocation {
  latitude: string | number;
  longitude: string | number;
}

export interface FlightClass {
  type: number;
  name: string;
  id: string;
  code: string;
}

export interface Segment {
  id: string;
  flightNo: string;
  pnlName: string;
  flightClass: FlightClass;
  airline: Airline;
  marketingAirline: Airline;
  departure: FlightLocation;
  arrival: FlightLocation;
  duration: number;
  baggageInformations: BaggageInformation[];
  services?: Service[];
  aircraft?: string;
}

export interface Service {
  segments?: string[];
  thumbnail?: string;
  thumbnailFull?: string;
  id?: string;
  name?: string;
}

export interface FlightProvider {
  displayName: string;
  id: string;
  name: string;
}

export interface BaggageInformation {
  segmentId: string;
  weight: number;
  piece: number;
  baggageType: number;
  unitType: number;
  passengerType: number;
}

export interface PassengerInfo {
  type: number;
  count: number;
  ages?: number[];
}

export interface Offer {
  // Champs principaux de l'API
  segmentNumber: number;
  singleAdultPrice: Price;
  priceBreakDown: PriceBreakDown;
  serviceFee: Price;
  seatInfo: SeatInfo;
  flightClassInformations: FlightClass[];
  baggageInformations: BaggageInformation[];
  services: Service[];
  reservableInfo: ReservableInfo;
  groupKeys: string[];
  fees?: Fees;
  isPackageOffer: boolean;
  hasBrand: boolean;
  route: number;
  flightProvider: FlightProvider;
  flightBrandInfo: FlightBrandInfo;
  expiresOn: string;
  offerId: string;
  price: Price;
  provider: number;

  // Champs supplémentaires ou alternatifs du frontend
  id?: string;  // Utilisé dans certains contextes au lieu de offerId
  offerIds?: {
    groupKey: string;
    offerId: string;
  }[];
  availability?: number;  // Dérivé de seatInfo.availableSeatCount
  brandedFare?: BrandedFare;  // Information supplémentaire sur le tarif de marque
}

export interface Price {
  amount: number;
  currency: string;
  currencyId?: string;
  formattedAmount?: string;
}

export interface PriceBreakDown {
  items: PriceBreakDownItem[];
}

export interface PriceBreakDownItem {
  passengerType: number;
  passengerCount: number;
  price: Price;
  airportTax?: Price;
}

export interface SeatInfo {
  availableSeatCount: number;
  availableSeatCountType: number;
}

export interface ReservableInfo {
  reservable: boolean;
}

export interface Fees {
  key?: string;
  oneWay?: OneWayFees;
}

export interface OneWayFees {
  items?: FeeItem[];
  price?: Price;
}

export interface FeeItem {
  passengerType: number;
  quantity: number;
  price: number;
  totalPrice: number;
}

export interface FlightBrandInfo {
  id: string;
  name: string;
  features?: Feature[];
}

export interface Feature {
  commercialName?: string;
  serviceGroup: number;
  pricingType: number;
  explanations?: Explanation[];
}

export interface Explanation {
  text: string;
}

export interface BrandedFare {
  id: string;
  name: string;
  description?: string;
  brandFeatures?: BrandFeature[];
  features?: Feature[];
}

export interface BrandFeature {
  serviceGroup: number;
  pricingType: number;
  description?: string;
  commercialName?: string;
  explanations?: Explanation[];
}
