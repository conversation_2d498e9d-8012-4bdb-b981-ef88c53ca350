{"ast": null, "code": "import { PassengerType } from '../models/enums.model';\nimport * as i0 from \"@angular/core\";\nexport let SharedDataService = /*#__PURE__*/(() => {\n  class SharedDataService {\n    constructor() {\n      // Stockage des informations de passagers\n      this.passengerCounts = {\n        [PassengerType.Adult]: 1,\n        [PassengerType.Child]: 0,\n        [PassengerType.Infant]: 0\n      };\n    }\n    /**\n     * Définir le nombre de passagers par type\n     * @param passengerCounts Objet contenant le nombre de passagers par type\n     */\n    setPassengerCounts(passengerCounts) {\n      this.passengerCounts = {\n        ...passengerCounts\n      };\n    }\n    /**\n     * Obtenir le nombre de passagers par type\n     * @returns Objet contenant le nombre de passagers par type\n     */\n    getPassengerCounts() {\n      return {\n        ...this.passengerCounts\n      };\n    }\n    /**\n     * Obtenir le nombre total de passagers\n     * @returns Nombre total de passagers\n     */\n    getTotalPassengers() {\n      return Object.values(this.passengerCounts).reduce((sum, count) => sum + count, 0);\n    }\n    /**\n     * Obtenir un tableau de passagers pour l'API\n     * @returns Tableau de passagers pour l'API\n     */\n    getPassengersArray() {\n      const passengers = [];\n      // Ajouter chaque type de passager avec un nombre > 0\n      Object.entries(this.passengerCounts).forEach(([type, count]) => {\n        if (count > 0) {\n          passengers.push({\n            type: parseInt(type),\n            count: count\n          });\n        }\n      });\n      // S'assurer qu'au moins un passager est inclus\n      if (passengers.length === 0) {\n        passengers.push({\n          type: PassengerType.Adult,\n          count: 1\n        });\n      }\n      return passengers;\n    }\n    static {\n      this.ɵfac = function SharedDataService_Factory(t) {\n        return new (t || SharedDataService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SharedDataService,\n        factory: SharedDataService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SharedDataService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}