{"ast": null, "code": "import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction LoginComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction LoginComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"div\", 43);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(authService, router, snackBar) {\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.credentials = {\n        Agency: '',\n        User: '',\n        Password: ''\n      };\n      this.loading = false;\n      this.error = '';\n      this.hidePassword = true;\n    }\n    ngOnInit() {\n      // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n      this.authService.logout();\n      // Animation d'entrée\n      document.body.classList.add('login-page-active');\n    }\n    ngOnDestroy() {\n      // Nettoyage de la classe lors de la destruction du composant\n      document.body.classList.remove('login-page-active');\n    }\n    onSubmit() {\n      if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n        this.error = 'Veuillez remplir tous les champs requis.';\n        return;\n      }\n      this.loading = true;\n      this.error = '';\n      this.authService.login(this.credentials).subscribe({\n        next: response => {\n          this.loading = false;\n          if (response.header.success) {\n            this.snackBar.open('Connexion réussie! Bienvenue.', 'Fermer', {\n              duration: 3000,\n              panelClass: 'success-snackbar',\n              verticalPosition: 'top'\n            });\n            this.router.navigate(['/search-price']);\n          } else {\n            this.error = 'Échec de l\\'authentification. Veuillez vérifier vos identifiants.';\n          }\n        },\n        error: err => {\n          this.loading = false;\n          this.error = 'Une erreur s\\'est produite lors de la connexion. Veuillez réessayer.';\n          console.error('Login error:', err);\n        }\n      });\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        decls: 58,\n        vars: 10,\n        consts: [[1, \"login-page\"], [1, \"login-background\"], [1, \"login-shapes\"], [1, \"shape\", \"shape-1\"], [1, \"shape\", \"shape-2\"], [1, \"shape\", \"shape-3\"], [1, \"shape\", \"shape-4\"], [1, \"shape\", \"shape-5\"], [1, \"login-illustration\"], [\"src\", \"assets/images/login-illustration.svg\", \"alt\", \"Travel Illustration\"], [1, \"login-container\"], [1, \"login-card\", \"animate-slide-up\"], [1, \"login-logo\"], [1, \"logo-icon\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"login-header\"], [1, \"login-form\", 3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"agency\"], [1, \"input-with-icon\"], [1, \"fas\", \"fa-building\"], [\"type\", \"text\", \"id\", \"agency\", \"name\", \"agency\", \"required\", \"\", \"placeholder\", \"Entrez le nom de votre agence\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"input-focus-border\"], [\"for\", \"username\"], [1, \"fas\", \"fa-user\"], [\"type\", \"text\", \"id\", \"username\", \"name\", \"username\", \"required\", \"\", \"placeholder\", \"Entrez votre nom d'utilisateur\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [1, \"fas\", \"fa-lock\"], [\"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"placeholder\", \"Entrez votre mot de passe\", 1, \"form-control\", 3, \"type\", \"ngModel\", \"ngModelChange\"], [\"type\", \"button\", \"aria-label\", \"Afficher/masquer le mot de passe\", 1, \"password-toggle\", 3, \"title\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"login-footer\"], [1, \"footer-decoration\"], [1, \"decoration-line\"], [1, \"fas\", \"fa-globe-americas\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"spinner-container\"], [1, \"spinner\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵelement(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"div\", 8);\n            i0.ɵɵelement(9, \"img\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13);\n            i0.ɵɵelement(14, \"i\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"h1\");\n            i0.ɵɵtext(16, \"E-Tourism\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 15)(18, \"h2\");\n            i0.ɵɵtext(19, \"Bienvenue\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"p\");\n            i0.ɵɵtext(21, \"Connectez-vous pour acc\\u00E9der \\u00E0 votre compte\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"form\", 16, 17);\n            i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_22_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(24, \"div\", 18)(25, \"label\", 19);\n            i0.ɵɵtext(26, \"Agence\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"div\", 20);\n            i0.ɵɵelement(28, \"i\", 21);\n            i0.ɵɵelementStart(29, \"input\", 22);\n            i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_29_listener($event) {\n              return ctx.credentials.Agency = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(30, \"div\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 18)(32, \"label\", 24);\n            i0.ɵɵtext(33, \"Nom d'utilisateur\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"div\", 20);\n            i0.ɵɵelement(35, \"i\", 25);\n            i0.ɵɵelementStart(36, \"input\", 26);\n            i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_36_listener($event) {\n              return ctx.credentials.User = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(37, \"div\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"div\", 18)(39, \"label\", 27);\n            i0.ɵɵtext(40, \"Mot de passe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"div\", 20);\n            i0.ɵɵelement(42, \"i\", 28);\n            i0.ɵɵelementStart(43, \"input\", 29);\n            i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_43_listener($event) {\n              return ctx.credentials.Password = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"button\", 30);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_44_listener() {\n              return ctx.hidePassword = !ctx.hidePassword;\n            });\n            i0.ɵɵelement(45, \"i\", 31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(46, \"div\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(47, LoginComponent_div_47_Template, 4, 1, \"div\", 32);\n            i0.ɵɵelementStart(48, \"button\", 33);\n            i0.ɵɵtemplate(49, LoginComponent_div_49_Template, 2, 0, \"div\", 34);\n            i0.ɵɵtemplate(50, LoginComponent_span_50_Template, 2, 0, \"span\", 35);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(51, \"div\", 36)(52, \"div\", 37);\n            i0.ɵɵelement(53, \"div\", 38)(54, \"i\", 39)(55, \"div\", 38);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"p\");\n            i0.ɵɵtext(57, \"\\u00A9 2023 E-Tourism. Tous droits r\\u00E9serv\\u00E9s.\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            const _r0 = i0.ɵɵreference(23);\n            i0.ɵɵadvance(29);\n            i0.ɵɵproperty(\"ngModel\", ctx.credentials.Agency);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngModel\", ctx.credentials.User);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\")(\"ngModel\", ctx.credentials.Password);\n            i0.ɵɵadvance(1);\n            i0.ɵɵpropertyInterpolate(\"title\", ctx.hidePassword ? \"Afficher le mot de passe\" : \"Masquer le mot de passe\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngClass\", ctx.hidePassword ? \"fa-eye\" : \"fa-eye-slash\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", !_r0.form.valid || ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm],\n        styles: [\".login-page[_ngcontent-%COMP%]{position:relative;min-height:100vh;width:100%;overflow:hidden;display:flex;justify-content:center;align-items:center}.login-background[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,var(--ocean-blue) 0%,var(--primary-color) 60%,var(--sky-blue) 100%);z-index:-1}.login-shapes[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;overflow:hidden}.shape[_ngcontent-%COMP%]{position:absolute;background-color:#ffffff1a;border-radius:50%}.shape-1[_ngcontent-%COMP%]{width:600px;height:600px;top:-300px;right:-200px;background:radial-gradient(circle at center,rgba(255,255,255,.1) 0%,rgba(255,255,255,.05) 70%,transparent 100%);animation:_ngcontent-%COMP%_float 20s infinite alternate ease-in-out}.shape-2[_ngcontent-%COMP%]{width:400px;height:400px;bottom:-200px;left:-100px;background:radial-gradient(circle at center,rgba(255,255,255,.1) 0%,rgba(255,255,255,.05) 70%,transparent 100%);animation:_ngcontent-%COMP%_float 25s infinite alternate-reverse ease-in-out}.shape-3[_ngcontent-%COMP%]{width:300px;height:300px;top:20%;right:10%;background:radial-gradient(circle at center,rgba(255,255,255,.08) 0%,rgba(255,255,255,.03) 70%,transparent 100%);animation:_ngcontent-%COMP%_pulse 15s infinite alternate ease-in-out}.shape-4[_ngcontent-%COMP%]{width:200px;height:200px;bottom:15%;right:20%;background:radial-gradient(circle at center,rgba(255,255,255,.08) 0%,rgba(255,255,255,.03) 70%,transparent 100%);animation:_ngcontent-%COMP%_float 18s infinite alternate-reverse ease-in-out}.shape-5[_ngcontent-%COMP%]{width:150px;height:150px;top:30%;left:15%;background:radial-gradient(circle at center,rgba(255,255,255,.08) 0%,rgba(255,255,255,.03) 70%,transparent 100%);animation:_ngcontent-%COMP%_pulse 12s infinite alternate ease-in-out}.login-illustration[_ngcontent-%COMP%]{position:absolute;bottom:5%;right:5%;width:40%;max-width:500px;opacity:.9;animation:_ngcontent-%COMP%_float 6s infinite alternate ease-in-out;display:none}.login-illustration[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:auto}@keyframes _ngcontent-%COMP%_float{0%{transform:translateY(0) rotate(0)}to{transform:translateY(20px) rotate(3deg)}}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1);opacity:.5}to{transform:scale(1.2);opacity:.2}}.login-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;width:100%;padding:var(--spacing-md);z-index:1}.login-card[_ngcontent-%COMP%]{width:100%;max-width:450px;background-color:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:var(--border-radius-large);box-shadow:var(--elevation-4),0 10px 30px #0000001a;padding:var(--spacing-xl);overflow:hidden;animation:_ngcontent-%COMP%_cardAppear .8s var(--transition-bounce) forwards}@keyframes _ngcontent-%COMP%_cardAppear{0%{opacity:0;transform:translateY(30px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}.login-logo[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;margin-bottom:var(--spacing-lg)}.logo-icon[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:80px;height:80px;background:linear-gradient(135deg,var(--primary-color),var(--accent-color));border-radius:50%;margin-bottom:var(--spacing-md);box-shadow:0 4px 15px rgba(var(--primary-color-rgb),.3);position:relative;overflow:hidden}.logo-icon[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(45deg,transparent 0%,rgba(255,255,255,.2) 50%,transparent 100%);animation:_ngcontent-%COMP%_shimmer 2s infinite}.logo-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2.5rem;color:#fff}.login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:600;background:linear-gradient(135deg,var(--primary-dark),var(--accent-color));-webkit-background-clip:text;-webkit-text-fill-color:transparent;margin:0}.login-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:var(--spacing-lg)}.login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--text-primary);margin-bottom:var(--spacing-xs);font-size:1.75rem;font-weight:600}.login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--text-secondary);font-size:1rem;margin:0}.login-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:var(--spacing-md)}.form-group[_ngcontent-%COMP%]{margin-bottom:var(--spacing-md)}label[_ngcontent-%COMP%]{display:block;margin-bottom:var(--spacing-xs);font-weight:500;color:var(--text-primary);font-size:.9rem;transition:color var(--transition-fast)}.input-with-icon[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center}.input-with-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:16px;color:var(--primary-color);font-size:1rem;transition:all var(--transition-fast)}.form-control[_ngcontent-%COMP%]{width:100%;padding:14px 14px 14px 45px;border:1px solid var(--divider-color);border-radius:var(--border-radius-medium);font-size:1rem;transition:all var(--transition-fast);background-color:var(--surface-color);color:var(--text-primary);box-shadow:0 2px 5px #00000005}input[type=password].form-control[_ngcontent-%COMP%], input[type=text].form-control[name=password][_ngcontent-%COMP%]{padding-right:45px}.form-control[_ngcontent-%COMP%]:focus{outline:none;border-color:var(--primary-color);box-shadow:0 0 0 3px rgba(var(--primary-color-rgb),.15)}.form-control[_ngcontent-%COMP%]:focus + .input-focus-border[_ngcontent-%COMP%]{transform:scaleX(1)}.input-focus-border[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;width:100%;height:2px;background:linear-gradient(to right,var(--primary-color),var(--accent-color));transform:scaleX(0);transform-origin:left;transition:transform var(--transition-medium)}.form-control[_ngcontent-%COMP%]::placeholder{color:var(--text-hint)}.input-with-icon[_ngcontent-%COMP%]:focus-within   i[_ngcontent-%COMP%]{color:var(--accent-color)}.password-toggle[_ngcontent-%COMP%]{position:absolute;right:12px;top:50%;transform:translateY(-50%);background:none;border:none;color:var(--text-secondary);cursor:pointer;padding:8px;font-size:1rem;transition:all var(--transition-fast);border-radius:50%;z-index:2;display:flex;align-items:center;justify-content:center;height:36px;width:36px}.password-toggle[_ngcontent-%COMP%]:hover{color:var(--primary-color);background-color:rgba(var(--primary-color-rgb),.05)}.password-toggle[_ngcontent-%COMP%]:active{transform:translateY(-50%) scale(.95)}.password-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:all .2s ease}.password-toggle[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.1)}.error-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-sm);color:var(--error-color);background-color:#f443361a;padding:var(--spacing-sm) var(--spacing-md);border-radius:var(--border-radius-medium);margin-bottom:var(--spacing-sm);animation:_ngcontent-%COMP%_shake .5s cubic-bezier(.36,.07,.19,.97) both}@keyframes _ngcontent-%COMP%_shake{10%,90%{transform:translate3d(-1px,0,0)}20%,80%{transform:translate3d(2px,0,0)}30%,50%,70%{transform:translate3d(-3px,0,0)}40%,60%{transform:translate3d(3px,0,0)}}.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem}.login-button[_ngcontent-%COMP%]{width:100%;height:50px;font-size:1rem;font-weight:600;margin-top:var(--spacing-md);position:relative;background:linear-gradient(135deg,var(--primary-color),var(--accent-color));color:#fff;border:none;border-radius:var(--border-radius-medium);cursor:pointer;transition:all var(--transition-fast);overflow:hidden;box-shadow:0 4px 15px rgba(var(--primary-color-rgb),.3)}.login-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:all .6s}.login-button[_ngcontent-%COMP%]:hover:not(:disabled):before{left:100%}.login-button[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 6px 20px rgba(var(--primary-color-rgb),.4)}.login-button[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(1px);box-shadow:0 2px 10px rgba(var(--primary-color-rgb),.3)}.login-button[_ngcontent-%COMP%]:disabled{background:linear-gradient(135deg,#ccc,#ddd);cursor:not-allowed;box-shadow:none}.spinner-container[_ngcontent-%COMP%]{position:absolute;inset:0;display:flex;align-items:center;justify-content:center}.spinner[_ngcontent-%COMP%]{width:24px;height:24px;border:3px solid rgba(255,255,255,.3);border-radius:50%;border-top-color:#fff;animation:_ngcontent-%COMP%_spin .8s linear infinite}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}.login-footer[_ngcontent-%COMP%]{margin-top:var(--spacing-xl);text-align:center}.footer-decoration[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;margin-bottom:var(--spacing-md)}.decoration-line[_ngcontent-%COMP%]{height:1px;width:60px;background:linear-gradient(to right,transparent,var(--divider-color))}.decoration-line[_ngcontent-%COMP%]:last-child{background:linear-gradient(to left,transparent,var(--divider-color))}.footer-decoration[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:var(--primary-color);font-size:1.2rem;margin:0 var(--spacing-sm)}.login-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--text-hint);font-size:.75rem;margin:0}@media (min-width: 768px){.login-illustration[_ngcontent-%COMP%]{display:block}.login-container[_ngcontent-%COMP%]{margin-left:-10%}}@media (max-width: 767px){.login-card[_ngcontent-%COMP%]{max-width:90%}}@media (max-width: 500px){.login-card[_ngcontent-%COMP%]{padding:var(--spacing-lg)}.logo-icon[_ngcontent-%COMP%]{width:70px;height:70px}.logo-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem}.login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.5rem}.form-control[_ngcontent-%COMP%]{padding:12px 12px 12px 40px}}body.login-page-active[_nghost-%COMP%], body.login-page-active   [_nghost-%COMP%]{animation:fadeIn .5s ease-in-out}@keyframes _ngcontent-%COMP%_shimmer{0%{transform:translate(-100%)}to{transform:translate(100%)}}\"],\n        data: {\n          animation: [trigger('fadeInOut', [transition(':enter', [style({\n            opacity: 0\n          }), animate('0.5s ease-in-out', style({\n            opacity: 1\n          }))]), transition(':leave', [animate('0.5s ease-in-out', style({\n            opacity: 0\n          }))])])]\n        }\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}