{"ast": null, "code": "import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/button\";\nexport class AccueilComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.userName = '';\n    // Pour l'animation séquentielle des cartes\n    this.featureCardDelay = [0, 100, 200, 300];\n  }\n  ngOnInit() {\n    const userInfo = this.authService.getUserInfo();\n    if (userInfo) {\n      this.userName = userInfo.name || 'User';\n    }\n  }\n  navigateToSearchPrice() {\n    this.router.navigate(['/search-price']);\n  }\n  static {\n    this.ɵfac = function AccueilComponent_Factory(t) {\n      return new (t || AccueilComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccueilComponent,\n      selectors: [[\"app-accueil\"]],\n      decls: 178,\n      vars: 0,\n      consts: [[1, \"accueil-container\"], [1, \"hero-section\"], [1, \"hero-background\"], [1, \"hero-shapes\"], [1, \"shape\", \"shape-1\"], [1, \"shape\", \"shape-2\"], [1, \"shape\", \"shape-3\"], [1, \"hero-content\", \"animate-fade-in\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [1, \"hero-cta-container\"], [\"mat-flat-button\", \"\", 1, \"hero-cta\", \"primary-gradient\", 3, \"click\"], [1, \"fas\", \"fa-plane-departure\"], [\"mat-stroked-button\", \"\", 1, \"hero-cta-secondary\"], [1, \"fas\", \"fa-info-circle\"], [1, \"hero-image\"], [\"src\", \"assets/images/home-illustration.svg\", \"alt\", \"Travel illustration\", 1, \"animate-float\"], [1, \"features-section\"], [1, \"features-background\"], [\"src\", \"assets/images/features-background.svg\", \"alt\", \"Background pattern\"], [1, \"section-title\"], [1, \"section-subtitle\"], [1, \"features-grid\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-100\"], [1, \"feature-card-inner\"], [1, \"feature-icon\", \"blue-gradient\"], [1, \"fas\", \"fa-search\"], [1, \"feature-title\"], [1, \"feature-description\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-200\"], [1, \"feature-icon\", \"orange-gradient\"], [1, \"fas\", \"fa-exchange-alt\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-300\"], [1, \"feature-icon\", \"teal-gradient\"], [1, \"fas\", \"fa-credit-card\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-400\"], [1, \"feature-icon\", \"purple-gradient\"], [1, \"fas\", \"fa-headset\"], [1, \"destinations-section\"], [1, \"destinations-grid\"], [1, \"destination-card\", \"istanbul\", \"animate-fade-in\"], [1, \"destination-image\"], [1, \"destination-overlay\"], [1, \"destination-content\"], [1, \"destination-code\"], [1, \"destination-card\", \"tunis\", \"animate-fade-in\", \"animate-delay-100\"], [1, \"destination-card\", \"paris\", \"animate-fade-in\", \"animate-delay-200\"], [1, \"destination-card\", \"dubai\", \"animate-fade-in\", \"animate-delay-300\"], [1, \"cta-section\"], [1, \"cta-background\"], [\"src\", \"assets/images/cta-background.svg\", \"alt\", \"CTA Background\"], [1, \"cta-content\"], [1, \"cta-title\"], [1, \"cta-description\"], [\"mat-flat-button\", \"\", 1, \"cta-button\", 3, \"click\"], [1, \"testimonials-section\"], [1, \"testimonials-container\"], [1, \"testimonial-card\", \"animate-fade-in\"], [1, \"testimonial-content\"], [1, \"testimonial-rating\"], [1, \"fas\", \"fa-star\"], [1, \"testimonial-text\"], [1, \"testimonial-author\"], [1, \"testimonial-avatar\"], [1, \"fas\", \"fa-user\"], [1, \"testimonial-info\"], [1, \"testimonial-card\", \"animate-fade-in\", \"animate-delay-100\"], [1, \"fas\", \"fa-star-half-alt\"], [1, \"testimonial-card\", \"animate-fade-in\", \"animate-delay-200\"]],\n      template: function AccueilComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"h1\", 8);\n          i0.ɵɵtext(9, \"D\\u00E9couvrez le monde avec E-Tourism\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 9);\n          i0.ɵɵtext(11, \"Trouvez et r\\u00E9servez vos vols parfaits pour votre prochaine aventure\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function AccueilComponent_Template_button_click_13_listener() {\n            return ctx.navigateToSearchPrice();\n          });\n          i0.ɵɵelement(14, \"i\", 12);\n          i0.ɵɵtext(15, \" R\\u00E9server un vol \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 13);\n          i0.ɵɵelement(17, \"i\", 14);\n          i0.ɵɵtext(18, \" En savoir plus \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 15);\n          i0.ɵɵelement(20, \"img\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 17)(22, \"div\", 18);\n          i0.ɵɵelement(23, \"img\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"h2\", 20);\n          i0.ɵɵtext(25, \"Pourquoi nous choisir\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"p\", 21);\n          i0.ɵɵtext(27, \"Nous offrons une exp\\u00E9rience de r\\u00E9servation de vol simple et efficace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 22)(29, \"div\", 23)(30, \"div\", 24)(31, \"div\", 25);\n          i0.ɵɵelement(32, \"i\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"h3\", 27);\n          i0.ɵɵtext(34, \"Recherche facile\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"p\", 28);\n          i0.ɵɵtext(36, \"Trouvez rapidement des vols avec notre moteur de recherche puissant\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 29)(38, \"div\", 24)(39, \"div\", 30);\n          i0.ɵɵelement(40, \"i\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"h3\", 27);\n          i0.ɵɵtext(42, \"Comparez les options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\", 28);\n          i0.ɵɵtext(44, \"Comparez diff\\u00E9rentes options de vol pour trouver la meilleure offre\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 32)(46, \"div\", 24)(47, \"div\", 33);\n          i0.ɵɵelement(48, \"i\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"h3\", 27);\n          i0.ɵɵtext(50, \"R\\u00E9servation s\\u00E9curis\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"p\", 28);\n          i0.ɵɵtext(52, \"R\\u00E9servez vos vols en toute s\\u00E9curit\\u00E9 avec notre plateforme de confiance\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"div\", 35)(54, \"div\", 24)(55, \"div\", 36);\n          i0.ɵɵelement(56, \"i\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"h3\", 27);\n          i0.ɵɵtext(58, \"Support 24/7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"p\", 28);\n          i0.ɵɵtext(60, \"Obtenez de l'aide \\u00E0 tout moment avec notre \\u00E9quipe de support d\\u00E9di\\u00E9e\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(61, \"div\", 38)(62, \"h2\", 20);\n          i0.ɵɵtext(63, \"Destinations populaires\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"p\", 21);\n          i0.ɵɵtext(65, \"Explorez nos destinations les plus recherch\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 39)(67, \"div\", 40)(68, \"div\", 41);\n          i0.ɵɵelement(69, \"div\", 42);\n          i0.ɵɵelementStart(70, \"div\", 43)(71, \"h3\");\n          i0.ɵɵtext(72, \"Istanbul\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"p\");\n          i0.ɵɵtext(74, \"Turquie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"span\", 44);\n          i0.ɵɵtext(76, \"IST\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(77, \"div\", 45)(78, \"div\", 41);\n          i0.ɵɵelement(79, \"div\", 42);\n          i0.ɵɵelementStart(80, \"div\", 43)(81, \"h3\");\n          i0.ɵɵtext(82, \"Tunis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"p\");\n          i0.ɵɵtext(84, \"Tunisie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"span\", 44);\n          i0.ɵɵtext(86, \"TUN\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(87, \"div\", 46)(88, \"div\", 41);\n          i0.ɵɵelement(89, \"div\", 42);\n          i0.ɵɵelementStart(90, \"div\", 43)(91, \"h3\");\n          i0.ɵɵtext(92, \"Paris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"p\");\n          i0.ɵɵtext(94, \"France\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"span\", 44);\n          i0.ɵɵtext(96, \"CDG\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(97, \"div\", 47)(98, \"div\", 41);\n          i0.ɵɵelement(99, \"div\", 42);\n          i0.ɵɵelementStart(100, \"div\", 43)(101, \"h3\");\n          i0.ɵɵtext(102, \"Dubai\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"p\");\n          i0.ɵɵtext(104, \"\\u00C9mirats Arabes Unis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"span\", 44);\n          i0.ɵɵtext(106, \"DXB\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(107, \"div\", 48)(108, \"div\", 49);\n          i0.ɵɵelement(109, \"img\", 50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"div\", 51)(111, \"h2\", 52);\n          i0.ɵɵtext(112, \"Pr\\u00EAt \\u00E0 commencer votre voyage?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"p\", 53);\n          i0.ɵɵtext(114, \"R\\u00E9servez votre vol maintenant et embarquez pour votre prochaine aventure\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"button\", 54);\n          i0.ɵɵlistener(\"click\", function AccueilComponent_Template_button_click_115_listener() {\n            return ctx.navigateToSearchPrice();\n          });\n          i0.ɵɵelement(116, \"i\", 26);\n          i0.ɵɵtext(117, \" Rechercher des vols \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(118, \"div\", 55)(119, \"h2\", 20);\n          i0.ɵɵtext(120, \"Ce que disent nos clients\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(121, \"p\", 21);\n          i0.ɵɵtext(122, \"D\\u00E9couvrez les exp\\u00E9riences de nos voyageurs satisfaits\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(123, \"div\", 56)(124, \"div\", 57)(125, \"div\", 58)(126, \"div\", 59);\n          i0.ɵɵelement(127, \"i\", 60)(128, \"i\", 60)(129, \"i\", 60)(130, \"i\", 60)(131, \"i\", 60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(132, \"p\", 61);\n          i0.ɵɵtext(133, \"\\\"Service exceptionnel! J'ai trouv\\u00E9 un vol \\u00E0 un prix imbattable et la r\\u00E9servation a \\u00E9t\\u00E9 simple et rapide.\\\"\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"div\", 62)(135, \"div\", 63);\n          i0.ɵɵelement(136, \"i\", 64);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"div\", 65)(138, \"h4\");\n          i0.ɵɵtext(139, \"Sophie Martin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(140, \"p\");\n          i0.ɵɵtext(141, \"Paris, France\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(142, \"div\", 66)(143, \"div\", 58)(144, \"div\", 59);\n          i0.ɵɵelement(145, \"i\", 60)(146, \"i\", 60)(147, \"i\", 60)(148, \"i\", 60)(149, \"i\", 67);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(150, \"p\", 61);\n          i0.ɵɵtext(151, \"\\\"Interface intuitive et options de vol nombreuses. J'ai pu comparer facilement et choisir la meilleure option pour mon budget.\\\"\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"div\", 62)(153, \"div\", 63);\n          i0.ɵɵelement(154, \"i\", 64);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"div\", 65)(156, \"h4\");\n          i0.ɵɵtext(157, \"Thomas Dubois\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(158, \"p\");\n          i0.ɵɵtext(159, \"Lyon, France\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(160, \"div\", 68)(161, \"div\", 58)(162, \"div\", 59);\n          i0.ɵɵelement(163, \"i\", 60)(164, \"i\", 60)(165, \"i\", 60)(166, \"i\", 60)(167, \"i\", 60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(168, \"p\", 61);\n          i0.ɵɵtext(169, \"\\\"Le support client est remarquable. J'ai eu besoin d'aide pour modifier ma r\\u00E9servation et tout a \\u00E9t\\u00E9 r\\u00E9solu rapidement.\\\"\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(170, \"div\", 62)(171, \"div\", 63);\n          i0.ɵɵelement(172, \"i\", 64);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(173, \"div\", 65)(174, \"h4\");\n          i0.ɵɵtext(175, \"Marie Leroy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(176, \"p\");\n          i0.ɵɵtext(177, \"Marseille, France\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n      },\n      dependencies: [i3.MatButton],\n      styles: [\".accueil-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n\\n\\n.hero-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: var(--spacing-xl) 0;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.05) 0%, rgba(var(--primary-color-rgb), 0.1) 100%);\\n  border-radius: var(--border-radius-large);\\n  overflow: hidden;\\n  margin-bottom: var(--spacing-xxl);\\n}\\n\\n.hero-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0 var(--spacing-xl);\\n}\\n\\n.hero-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: var(--primary-dark);\\n  margin-bottom: var(--spacing-md);\\n  line-height: 1.2;\\n}\\n\\n.hero-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  color: var(--text-secondary);\\n  margin-bottom: var(--spacing-lg);\\n  max-width: 600px;\\n}\\n\\n.hero-cta[_ngcontent-%COMP%] {\\n  padding: var(--spacing-sm) var(--spacing-lg);\\n  font-size: 1rem;\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  border-radius: var(--border-radius-medium);\\n  transition: transform var(--transition-fast);\\n}\\n\\n.hero-cta[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n\\n.hero-image[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: var(--spacing-lg);\\n}\\n\\n.hero-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  max-height: 400px;\\n}\\n\\n\\n\\n.features-section[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl) 0;\\n  margin-bottom: var(--spacing-xxl);\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 2rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n  margin-bottom: var(--spacing-xl);\\n}\\n\\n.features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: var(--spacing-lg);\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  border-radius: var(--border-radius-medium);\\n  box-shadow: var(--elevation-2);\\n  transition: transform var(--transition-medium), box-shadow var(--transition-medium);\\n  overflow: hidden;\\n  height: 100%;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: var(--elevation-4);\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  width: 60px;\\n  height: 60px;\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  border-radius: 50%;\\n  margin: 0 auto var(--spacing-md);\\n}\\n\\n.feature-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 30px;\\n  height: 30px;\\n  width: 30px;\\n  color: var(--primary-color);\\n}\\n\\n.feature-title[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 1.25rem;\\n  font-weight: 500;\\n  color: var(--text-primary);\\n  margin-bottom: var(--spacing-sm);\\n}\\n\\n.feature-description[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.cta-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\\n  border-radius: var(--border-radius-large);\\n  padding: var(--spacing-xl);\\n  margin-bottom: var(--spacing-xxl);\\n  color: white;\\n  text-align: center;\\n}\\n\\n.cta-content[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n\\n.cta-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 600;\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.cta-description[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: var(--spacing-lg);\\n  opacity: 0.9;\\n}\\n\\n.cta-button[_ngcontent-%COMP%] {\\n  padding: var(--spacing-sm) var(--spacing-xl);\\n  font-size: 1rem;\\n  font-weight: 500;\\n  background-color: white;\\n  color: var(--primary-color);\\n}\\n\\n\\n\\n@media (max-width: 992px) {\\n  .hero-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: var(--spacing-lg) 0;\\n  }\\n\\n  .hero-content[_ngcontent-%COMP%] {\\n    text-align: center;\\n    padding: var(--spacing-lg);\\n    order: 2;\\n  }\\n\\n  .hero-image[_ngcontent-%COMP%] {\\n    order: 1;\\n    margin-bottom: var(--spacing-lg);\\n  }\\n\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .hero-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n    margin-left: auto;\\n    margin-right: auto;\\n  }\\n\\n  .hero-cta[_ngcontent-%COMP%] {\\n    margin: 0 auto;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n\\n  .cta-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n\\n  .cta-description[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n\\n  .hero-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .features-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .cta-section[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg);\\n  }\\n\\n  .cta-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      data: {\n        animation: [trigger('fadeIn', [transition(':enter', [style({\n          opacity: 0\n        }), animate('500ms ease-in', style({\n          opacity: 1\n        }))])]), trigger('slideUp', [transition(':enter', [style({\n          transform: 'translateY(20px)',\n          opacity: 0\n        }), animate('500ms ease-out', style({\n          transform: 'translateY(0)',\n          opacity: 1\n        }))])])]\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["trigger", "transition", "style", "animate", "AccueilComponent", "constructor", "authService", "router", "userName", "featureCardDelay", "ngOnInit", "userInfo", "getUserInfo", "name", "navigateToSearchPrice", "navigate", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AccueilComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "AccueilComponent_Template_button_click_13_listener", "AccueilComponent_Template_button_click_115_listener", "opacity", "transform"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\accueil\\accueil.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\accueil\\accueil.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { trigger, transition, style, animate } from '@angular/animations';\n\n@Component({\n  selector: 'app-accueil',\n  templateUrl: './accueil.component.html',\n  styleUrls: ['./accueil.component.css'],\n  animations: [\n    trigger('fadeIn', [\n      transition(':enter', [\n        style({ opacity: 0 }),\n        animate('500ms ease-in', style({ opacity: 1 }))\n      ])\n    ]),\n    trigger('slideUp', [\n      transition(':enter', [\n        style({ transform: 'translateY(20px)', opacity: 0 }),\n        animate('500ms ease-out', style({ transform: 'translateY(0)', opacity: 1 }))\n      ])\n    ])\n  ]\n})\nexport class AccueilComponent implements OnInit {\n  userName: string = '';\n\n  // Pour l'animation séquentielle des cartes\n  featureCardDelay = [0, 100, 200, 300];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) { }\n\n  ngOnInit(): void {\n    const userInfo = this.authService.getUserInfo();\n    if (userInfo) {\n      this.userName = userInfo.name || 'User';\n    }\n  }\n\n  navigateToSearchPrice(): void {\n    this.router.navigate(['/search-price']);\n  }\n}\n", "<div class=\"accueil-container\">\n  <!-- Hero Section -->\n  <div class=\"hero-section\">\n    <div class=\"hero-background\">\n      <div class=\"hero-shapes\">\n        <div class=\"shape shape-1\"></div>\n        <div class=\"shape shape-2\"></div>\n        <div class=\"shape shape-3\"></div>\n      </div>\n    </div>\n\n    <div class=\"hero-content animate-fade-in\">\n      <h1 class=\"hero-title\">Découvrez le monde avec E-Tourism</h1>\n      <p class=\"hero-subtitle\">Trouvez et réservez vos vols parfaits pour votre prochaine aventure</p>\n      <div class=\"hero-cta-container\">\n        <button mat-flat-button class=\"hero-cta primary-gradient\" (click)=\"navigateToSearchPrice()\">\n          <i class=\"fas fa-plane-departure\"></i>\n          Réserver un vol\n        </button>\n        <button mat-stroked-button class=\"hero-cta-secondary\">\n          <i class=\"fas fa-info-circle\"></i>\n          En savoir plus\n        </button>\n      </div>\n    </div>\n\n    <div class=\"hero-image\">\n      <img src=\"assets/images/home-illustration.svg\" alt=\"Travel illustration\" class=\"animate-float\">\n    </div>\n  </div>\n\n  <!-- Features Section -->\n  <div class=\"features-section\">\n    <div class=\"features-background\">\n      <img src=\"assets/images/features-background.svg\" alt=\"Background pattern\">\n    </div>\n\n    <h2 class=\"section-title\">Pourquoi nous choisir</h2>\n    <p class=\"section-subtitle\">Nous offrons une expérience de réservation de vol simple et efficace</p>\n\n    <div class=\"features-grid\">\n      <div class=\"feature-card animate-fade-in animate-delay-100\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon blue-gradient\">\n            <i class=\"fas fa-search\"></i>\n          </div>\n          <h3 class=\"feature-title\">Recherche facile</h3>\n          <p class=\"feature-description\">Trouvez rapidement des vols avec notre moteur de recherche puissant</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card animate-fade-in animate-delay-200\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon orange-gradient\">\n            <i class=\"fas fa-exchange-alt\"></i>\n          </div>\n          <h3 class=\"feature-title\">Comparez les options</h3>\n          <p class=\"feature-description\">Comparez différentes options de vol pour trouver la meilleure offre</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card animate-fade-in animate-delay-300\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon teal-gradient\">\n            <i class=\"fas fa-credit-card\"></i>\n          </div>\n          <h3 class=\"feature-title\">Réservation sécurisée</h3>\n          <p class=\"feature-description\">Réservez vos vols en toute sécurité avec notre plateforme de confiance</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card animate-fade-in animate-delay-400\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon purple-gradient\">\n            <i class=\"fas fa-headset\"></i>\n          </div>\n          <h3 class=\"feature-title\">Support 24/7</h3>\n          <p class=\"feature-description\">Obtenez de l'aide à tout moment avec notre équipe de support dédiée</p>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Destinations Section -->\n  <div class=\"destinations-section\">\n    <h2 class=\"section-title\">Destinations populaires</h2>\n    <p class=\"section-subtitle\">Explorez nos destinations les plus recherchées</p>\n\n    <div class=\"destinations-grid\">\n      <div class=\"destination-card istanbul animate-fade-in\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Istanbul</h3>\n            <p>Turquie</p>\n            <span class=\"destination-code\">IST</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"destination-card tunis animate-fade-in animate-delay-100\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Tunis</h3>\n            <p>Tunisie</p>\n            <span class=\"destination-code\">TUN</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"destination-card paris animate-fade-in animate-delay-200\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Paris</h3>\n            <p>France</p>\n            <span class=\"destination-code\">CDG</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"destination-card dubai animate-fade-in animate-delay-300\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Dubai</h3>\n            <p>Émirats Arabes Unis</p>\n            <span class=\"destination-code\">DXB</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- CTA Section -->\n  <div class=\"cta-section\">\n    <div class=\"cta-background\">\n      <img src=\"assets/images/cta-background.svg\" alt=\"CTA Background\">\n    </div>\n\n    <div class=\"cta-content\">\n      <h2 class=\"cta-title\">Prêt à commencer votre voyage?</h2>\n      <p class=\"cta-description\">Réservez votre vol maintenant et embarquez pour votre prochaine aventure</p>\n      <button mat-flat-button class=\"cta-button\" (click)=\"navigateToSearchPrice()\">\n        <i class=\"fas fa-search\"></i>\n        Rechercher des vols\n      </button>\n    </div>\n  </div>\n\n  <!-- Testimonials Section -->\n  <div class=\"testimonials-section\">\n    <h2 class=\"section-title\">Ce que disent nos clients</h2>\n    <p class=\"section-subtitle\">Découvrez les expériences de nos voyageurs satisfaits</p>\n\n    <div class=\"testimonials-container\">\n      <div class=\"testimonial-card animate-fade-in\">\n        <div class=\"testimonial-content\">\n          <div class=\"testimonial-rating\">\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n          </div>\n          <p class=\"testimonial-text\">\"Service exceptionnel! J'ai trouvé un vol à un prix imbattable et la réservation a été simple et rapide.\"</p>\n          <div class=\"testimonial-author\">\n            <div class=\"testimonial-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <div class=\"testimonial-info\">\n              <h4>Sophie Martin</h4>\n              <p>Paris, France</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"testimonial-card animate-fade-in animate-delay-100\">\n        <div class=\"testimonial-content\">\n          <div class=\"testimonial-rating\">\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star-half-alt\"></i>\n          </div>\n          <p class=\"testimonial-text\">\"Interface intuitive et options de vol nombreuses. J'ai pu comparer facilement et choisir la meilleure option pour mon budget.\"</p>\n          <div class=\"testimonial-author\">\n            <div class=\"testimonial-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <div class=\"testimonial-info\">\n              <h4>Thomas Dubois</h4>\n              <p>Lyon, France</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"testimonial-card animate-fade-in animate-delay-200\">\n        <div class=\"testimonial-content\">\n          <div class=\"testimonial-rating\">\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n          </div>\n          <p class=\"testimonial-text\">\"Le support client est remarquable. J'ai eu besoin d'aide pour modifier ma réservation et tout a été résolu rapidement.\"</p>\n          <div class=\"testimonial-author\">\n            <div class=\"testimonial-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <div class=\"testimonial-info\">\n              <h4>Marie Leroy</h4>\n              <p>Marseille, France</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAGA,SAASA,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;;;;;AAqBzE,OAAM,MAAOC,gBAAgB;EAM3BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,QAAQ,GAAW,EAAE;IAErB;IACA,KAAAC,gBAAgB,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAKjC;EAEJC,QAAQA,CAAA;IACN,MAAMC,QAAQ,GAAG,IAAI,CAACL,WAAW,CAACM,WAAW,EAAE;IAC/C,IAAID,QAAQ,EAAE;MACZ,IAAI,CAACH,QAAQ,GAAGG,QAAQ,CAACE,IAAI,IAAI,MAAM;;EAE3C;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;;;uBApBWX,gBAAgB,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBjB,gBAAgB;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxB7BZ,EAAA,CAAAc,cAAA,aAA+B;UAKvBd,EAAA,CAAAe,SAAA,aAAiC;UAGnCf,EAAA,CAAAgB,YAAA,EAAM;UAGRhB,EAAA,CAAAc,cAAA,aAA0C;UACjBd,EAAA,CAAAiB,MAAA,6CAAiC;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UAC7DhB,EAAA,CAAAc,cAAA,YAAyB;UAAAd,EAAA,CAAAiB,MAAA,gFAAmE;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAChGhB,EAAA,CAAAc,cAAA,eAAgC;UAC4Bd,EAAA,CAAAkB,UAAA,mBAAAC,mDAAA;YAAA,OAASN,GAAA,CAAAf,qBAAA,EAAuB;UAAA,EAAC;UACzFE,EAAA,CAAAe,SAAA,aAAsC;UACtCf,EAAA,CAAAiB,MAAA,8BACF;UAAAjB,EAAA,CAAAgB,YAAA,EAAS;UACThB,EAAA,CAAAc,cAAA,kBAAsD;UACpDd,EAAA,CAAAe,SAAA,aAAkC;UAClCf,EAAA,CAAAiB,MAAA,wBACF;UAAAjB,EAAA,CAAAgB,YAAA,EAAS;UAIbhB,EAAA,CAAAc,cAAA,eAAwB;UACtBd,EAAA,CAAAe,SAAA,eAA+F;UACjGf,EAAA,CAAAgB,YAAA,EAAM;UAIRhB,EAAA,CAAAc,cAAA,eAA8B;UAE1Bd,EAAA,CAAAe,SAAA,eAA0E;UAC5Ef,EAAA,CAAAgB,YAAA,EAAM;UAENhB,EAAA,CAAAc,cAAA,cAA0B;UAAAd,EAAA,CAAAiB,MAAA,6BAAqB;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACpDhB,EAAA,CAAAc,cAAA,aAA4B;UAAAd,EAAA,CAAAiB,MAAA,sFAAoE;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAEpGhB,EAAA,CAAAc,cAAA,eAA2B;UAInBd,EAAA,CAAAe,SAAA,aAA6B;UAC/Bf,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAc,cAAA,cAA0B;UAAAd,EAAA,CAAAiB,MAAA,wBAAgB;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UAC/ChB,EAAA,CAAAc,cAAA,aAA+B;UAAAd,EAAA,CAAAiB,MAAA,2EAAmE;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAI1GhB,EAAA,CAAAc,cAAA,eAA4D;UAGtDd,EAAA,CAAAe,SAAA,aAAmC;UACrCf,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAc,cAAA,cAA0B;UAAAd,EAAA,CAAAiB,MAAA,4BAAoB;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACnDhB,EAAA,CAAAc,cAAA,aAA+B;UAAAd,EAAA,CAAAiB,MAAA,gFAAmE;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAI1GhB,EAAA,CAAAc,cAAA,eAA4D;UAGtDd,EAAA,CAAAe,SAAA,aAAkC;UACpCf,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAc,cAAA,cAA0B;UAAAd,EAAA,CAAAiB,MAAA,4CAAqB;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACpDhB,EAAA,CAAAc,cAAA,aAA+B;UAAAd,EAAA,CAAAiB,MAAA,6FAAsE;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAI7GhB,EAAA,CAAAc,cAAA,eAA4D;UAGtDd,EAAA,CAAAe,SAAA,aAA8B;UAChCf,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAc,cAAA,cAA0B;UAAAd,EAAA,CAAAiB,MAAA,oBAAY;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UAC3ChB,EAAA,CAAAc,cAAA,aAA+B;UAAAd,EAAA,CAAAiB,MAAA,+FAAmE;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAO9GhB,EAAA,CAAAc,cAAA,eAAkC;UACNd,EAAA,CAAAiB,MAAA,+BAAuB;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACtDhB,EAAA,CAAAc,cAAA,aAA4B;UAAAd,EAAA,CAAAiB,MAAA,2DAA8C;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAE9EhB,EAAA,CAAAc,cAAA,eAA+B;UAGzBd,EAAA,CAAAe,SAAA,eAAuC;UACvCf,EAAA,CAAAc,cAAA,eAAiC;UAC3Bd,EAAA,CAAAiB,MAAA,gBAAQ;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACjBhB,EAAA,CAAAc,cAAA,SAAG;UAAAd,EAAA,CAAAiB,MAAA,eAAO;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UACdhB,EAAA,CAAAc,cAAA,gBAA+B;UAAAd,EAAA,CAAAiB,MAAA,WAAG;UAAAjB,EAAA,CAAAgB,YAAA,EAAO;UAK/ChB,EAAA,CAAAc,cAAA,eAAsE;UAElEd,EAAA,CAAAe,SAAA,eAAuC;UACvCf,EAAA,CAAAc,cAAA,eAAiC;UAC3Bd,EAAA,CAAAiB,MAAA,aAAK;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACdhB,EAAA,CAAAc,cAAA,SAAG;UAAAd,EAAA,CAAAiB,MAAA,eAAO;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UACdhB,EAAA,CAAAc,cAAA,gBAA+B;UAAAd,EAAA,CAAAiB,MAAA,WAAG;UAAAjB,EAAA,CAAAgB,YAAA,EAAO;UAK/ChB,EAAA,CAAAc,cAAA,eAAsE;UAElEd,EAAA,CAAAe,SAAA,eAAuC;UACvCf,EAAA,CAAAc,cAAA,eAAiC;UAC3Bd,EAAA,CAAAiB,MAAA,aAAK;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACdhB,EAAA,CAAAc,cAAA,SAAG;UAAAd,EAAA,CAAAiB,MAAA,cAAM;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UACbhB,EAAA,CAAAc,cAAA,gBAA+B;UAAAd,EAAA,CAAAiB,MAAA,WAAG;UAAAjB,EAAA,CAAAgB,YAAA,EAAO;UAK/ChB,EAAA,CAAAc,cAAA,eAAsE;UAElEd,EAAA,CAAAe,SAAA,eAAuC;UACvCf,EAAA,CAAAc,cAAA,gBAAiC;UAC3Bd,EAAA,CAAAiB,MAAA,cAAK;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACdhB,EAAA,CAAAc,cAAA,UAAG;UAAAd,EAAA,CAAAiB,MAAA,iCAAmB;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAC1BhB,EAAA,CAAAc,cAAA,iBAA+B;UAAAd,EAAA,CAAAiB,MAAA,YAAG;UAAAjB,EAAA,CAAAgB,YAAA,EAAO;UAQnDhB,EAAA,CAAAc,cAAA,gBAAyB;UAErBd,EAAA,CAAAe,SAAA,gBAAiE;UACnEf,EAAA,CAAAgB,YAAA,EAAM;UAENhB,EAAA,CAAAc,cAAA,gBAAyB;UACDd,EAAA,CAAAiB,MAAA,iDAA8B;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACzDhB,EAAA,CAAAc,cAAA,cAA2B;UAAAd,EAAA,CAAAiB,MAAA,sFAAwE;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UACvGhB,EAAA,CAAAc,cAAA,mBAA6E;UAAlCd,EAAA,CAAAkB,UAAA,mBAAAE,oDAAA;YAAA,OAASP,GAAA,CAAAf,qBAAA,EAAuB;UAAA,EAAC;UAC1EE,EAAA,CAAAe,SAAA,cAA6B;UAC7Bf,EAAA,CAAAiB,MAAA,8BACF;UAAAjB,EAAA,CAAAgB,YAAA,EAAS;UAKbhB,EAAA,CAAAc,cAAA,gBAAkC;UACNd,EAAA,CAAAiB,MAAA,kCAAyB;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACxDhB,EAAA,CAAAc,cAAA,cAA4B;UAAAd,EAAA,CAAAiB,MAAA,wEAAqD;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAErFhB,EAAA,CAAAc,cAAA,gBAAoC;UAI5Bd,EAAA,CAAAe,SAAA,cAA2B;UAK7Bf,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAc,cAAA,cAA4B;UAAAd,EAAA,CAAAiB,MAAA,6IAAyG;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UACzIhB,EAAA,CAAAc,cAAA,gBAAgC;UAE5Bd,EAAA,CAAAe,SAAA,cAA2B;UAC7Bf,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAc,cAAA,gBAA8B;UACxBd,EAAA,CAAAiB,MAAA,sBAAa;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACtBhB,EAAA,CAAAc,cAAA,UAAG;UAAAd,EAAA,CAAAiB,MAAA,sBAAa;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAM5BhB,EAAA,CAAAc,cAAA,gBAAgE;UAG1Dd,EAAA,CAAAe,SAAA,cAA2B;UAK7Bf,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAc,cAAA,cAA4B;UAAAd,EAAA,CAAAiB,MAAA,0IAA+H;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAC/JhB,EAAA,CAAAc,cAAA,gBAAgC;UAE5Bd,EAAA,CAAAe,SAAA,cAA2B;UAC7Bf,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAc,cAAA,gBAA8B;UACxBd,EAAA,CAAAiB,MAAA,sBAAa;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACtBhB,EAAA,CAAAc,cAAA,UAAG;UAAAd,EAAA,CAAAiB,MAAA,qBAAY;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UAM3BhB,EAAA,CAAAc,cAAA,gBAAgE;UAG1Dd,EAAA,CAAAe,SAAA,cAA2B;UAK7Bf,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAc,cAAA,cAA4B;UAAAd,EAAA,CAAAiB,MAAA,uJAAwH;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;UACxJhB,EAAA,CAAAc,cAAA,gBAAgC;UAE5Bd,EAAA,CAAAe,SAAA,cAA2B;UAC7Bf,EAAA,CAAAgB,YAAA,EAAM;UACNhB,EAAA,CAAAc,cAAA,gBAA8B;UACxBd,EAAA,CAAAiB,MAAA,oBAAW;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACpBhB,EAAA,CAAAc,cAAA,UAAG;UAAAd,EAAA,CAAAiB,MAAA,0BAAiB;UAAAjB,EAAA,CAAAgB,YAAA,EAAI;;;;;;mBDhNxB,CACVhC,OAAO,CAAC,QAAQ,EAAE,CAChBC,UAAU,CAAC,QAAQ,EAAE,CACnBC,KAAK,CAAC;UAAEmC,OAAO,EAAE;QAAC,CAAE,CAAC,EACrBlC,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;UAAEmC,OAAO,EAAE;QAAC,CAAE,CAAC,CAAC,CAChD,CAAC,CACH,CAAC,EACFrC,OAAO,CAAC,SAAS,EAAE,CACjBC,UAAU,CAAC,QAAQ,EAAE,CACnBC,KAAK,CAAC;UAAEoC,SAAS,EAAE,kBAAkB;UAAED,OAAO,EAAE;QAAC,CAAE,CAAC,EACpDlC,OAAO,CAAC,gBAAgB,EAAED,KAAK,CAAC;UAAEoC,SAAS,EAAE,eAAe;UAAED,OAAO,EAAE;QAAC,CAAE,CAAC,CAAC,CAC7E,CAAC,CACH,CAAC;MACH;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}