{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CountrySelectorComponent } from './country-selector/country-selector.component';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, MatAutocompleteModule, MatFormFieldModule, MatInputModule, MatIconModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [CountrySelectorComponent],\n    imports: [CommonModule, ReactiveFormsModule, MatAutocompleteModule, MatFormFieldModule, MatInputModule, MatIconModule],\n    exports: [CountrySelectorComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "MatAutocompleteModule", "MatFormFieldModule", "MatInputModule", "MatIconModule", "CountrySelectorComponent", "SharedModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CountrySelectorComponent } from './country-selector/country-selector.component';\n\n@NgModule({\n  declarations: [\n    CountrySelectorComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatAutocompleteModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatIconModule\n  ],\n  exports: [\n    CountrySelectorComponent\n  ]\n})\nexport class SharedModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,+CAA+C;;AAkBxF,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAXrBP,YAAY,EACZC,mBAAmB,EACnBC,qBAAqB,EACrBC,kBAAkB,EAClBC,cAAc,EACdC,aAAa;IAAA;EAAA;;;2EAMJE,YAAY;IAAAC,YAAA,GAdrBF,wBAAwB;IAAAG,OAAA,GAGxBT,YAAY,EACZC,mBAAmB,EACnBC,qBAAqB,EACrBC,kBAAkB,EAClBC,cAAc,EACdC,aAAa;IAAAK,OAAA,GAGbJ,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}