/* Styles spécifiques pour les vols aller-retour */

/* Section aller-retour */
.round-trip-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.round-trip-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  background-color: #f9f9f9;
}

.round-trip-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #0056b3;
  font-weight: 600;
}

.round-trip-header i {
  margin-right: 8px;
  font-size: 16px;
}

.round-trip-label {
  font-size: 14px;
}

/* Section aller */
.outbound {
  border-left: 4px solid #4caf50;
}

/* Section retour */
.inbound {
  border-left: 4px solid #2196f3;
}

/* Message d'information sur les dates */
.date-info-container {
  margin-bottom: 10px;
}

.date-info-message {
  background-color: #fff8e6;
  border-left: 4px solid #f59e0b;
  padding: 10px;
  border-radius: 0 4px 4px 0;
  font-size: 13px;
  color: #92400e;
  margin-bottom: 10px;
}

.date-info-message i {
  margin-right: 5px;
  color: #f59e0b;
}

.date-info-success {
  background-color: #f0f9f0;
  border-left: 4px solid #4caf50;
  color: #2e7d32;
}

.date-info-success i {
  color: #4caf50;
}

/* Durée du séjour */
.stay-duration {
  display: flex;
  align-items: center;
  background-color: #e3f2fd;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  color: #0d47a1;
  margin-top: 5px;
}

.stay-duration i {
  margin-right: 8px;
  color: #1976d2;
}

/* Indicateur de différence de date */
.date-diff {
  position: relative;
}

.date-diff-indicator {
  font-size: 11px;
  color: #e65100;
  font-weight: bold;
  margin-left: 4px;
}
