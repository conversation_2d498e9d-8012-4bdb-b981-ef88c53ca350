{"ast": null, "code": "import { NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/progress-bar\";\nimport * as i6 from \"@angular/material/toolbar\";\nimport * as i7 from \"@angular/material/divider\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"@angular/material/badge\";\nfunction NavbarComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"a\", 12);\n    i0.ɵɵelement(2, \"i\", 13);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Accueil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"a\", 14);\n    i0.ɵɵelement(6, \"i\", 15);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Rechercher\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction NavbarComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"button\", 17);\n    i0.ɵɵelement(2, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19)(4, \"button\", 20)(5, \"div\", 21);\n    i0.ɵɵelement(6, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 23);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"i\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-menu\", 25, 26)(12, \"div\", 27)(13, \"div\", 28);\n    i0.ɵɵelement(14, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 29)(16, \"h4\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(20, \"mat-divider\");\n    i0.ɵɵelementStart(21, \"button\", 30);\n    i0.ɵɵelement(22, \"i\", 31);\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24, \"Mon profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"button\", 30);\n    i0.ɵɵelement(26, \"i\", 32);\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"button\", 30);\n    i0.ɵɵelement(30, \"i\", 33);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \"Historique\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(33, \"mat-divider\");\n    i0.ɵɵelementStart(34, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_div_10_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.logout());\n    });\n    i0.ɵɵelement(35, \"i\", 35);\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r3 = i0.ɵɵreference(11);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matBadge\", 0)(\"matBadgeHidden\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.userName);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.userName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.userAgency);\n  }\n}\nfunction NavbarComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"mat-progress-bar\", 37);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class NavbarComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.isLoggedIn = false;\n    this.userName = '';\n    this.userAgency = '';\n    this.loading = false;\n    this.subscriptions = [];\n    this.scrolled = false;\n  }\n  ngOnInit() {\n    // Surveiller l'état d'authentification\n    const authSub = this.authService.isAuthenticated$.subscribe(isAuthenticated => {\n      this.isLoggedIn = isAuthenticated;\n      if (isAuthenticated) {\n        const userInfo = this.authService.getUserInfo();\n        this.userName = userInfo?.username || 'Utilisateur';\n        this.userAgency = userInfo?.agency || 'Agence';\n      }\n    });\n    this.subscriptions.push(authSub);\n    // Surveiller les événements de navigation pour afficher la barre de progression\n    const routerSub = this.router.events.pipe(filter(event => event instanceof NavigationStart || event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError)).subscribe(event => {\n      if (event instanceof NavigationStart) {\n        this.loading = true;\n      } else {\n        this.loading = false;\n      }\n    });\n    this.subscriptions.push(routerSub);\n    // Vérifier le défilement initial\n    this.checkScroll();\n  }\n  ngOnDestroy() {\n    // Nettoyer les abonnements\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  // Méthode de déconnexion\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n  // Écouter l'événement de défilement pour changer l'apparence de la navbar\n  checkScroll() {\n    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n    if (scrollPosition > 20 && !this.scrolled) {\n      document.body.classList.add('scrolled');\n      this.scrolled = true;\n    } else if (scrollPosition <= 20 && this.scrolled) {\n      document.body.classList.remove('scrolled');\n      this.scrolled = false;\n    }\n  }\n  static {\n    this.ɵfac = function NavbarComponent_Factory(t) {\n      return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavbarComponent,\n      selectors: [[\"app-navbar\"]],\n      hostBindings: function NavbarComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"scroll\", function NavbarComponent_scroll_HostBindingHandler() {\n            return ctx.checkScroll();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      decls: 12,\n      vars: 3,\n      consts: [[1, \"navbar-wrapper\"], [1, \"navbar\"], [1, \"navbar-container\"], [1, \"navbar-brand\"], [\"routerLink\", \"/accueil\", 1, \"brand-link\"], [1, \"brand-icon\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"brand-name\"], [\"class\", \"navbar-links\", 4, \"ngIf\"], [\"class\", \"navbar-actions\", 4, \"ngIf\"], [\"class\", \"progress-bar-container\", 4, \"ngIf\"], [1, \"navbar-links\"], [\"routerLink\", \"/accueil\", \"routerLinkActive\", \"active-link\", 1, \"nav-link\"], [1, \"fas\", \"fa-home\"], [\"routerLink\", \"/search-price\", \"routerLinkActive\", \"active-link\", 1, \"nav-link\"], [1, \"fas\", \"fa-search\"], [1, \"navbar-actions\"], [\"mat-icon-button\", \"\", \"matBadgeColor\", \"accent\", \"aria-label\", \"Notifications\", 1, \"notification-button\", 3, \"matBadge\", \"matBadgeHidden\"], [1, \"fas\", \"fa-bell\"], [1, \"user-menu-container\"], [\"mat-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"user-avatar\"], [1, \"fas\", \"fa-user\"], [1, \"user-name\"], [1, \"fas\", \"fa-chevron-down\"], [\"xPosition\", \"before\", 1, \"user-menu\"], [\"userMenu\", \"matMenu\"], [1, \"user-menu-header\"], [1, \"user-avatar\", \"large\"], [1, \"user-info\"], [\"mat-menu-item\", \"\", \"disabled\", \"\"], [1, \"fas\", \"fa-user-circle\"], [1, \"fas\", \"fa-cog\"], [1, \"fas\", \"fa-history\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [1, \"progress-bar-container\"], [\"mode\", \"indeterminate\", \"color\", \"accent\"]],\n      template: function NavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-toolbar\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 7);\n          i0.ɵɵtext(8, \"E-Tourism\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(9, NavbarComponent_div_9_Template, 9, 0, \"div\", 8);\n          i0.ɵɵtemplate(10, NavbarComponent_div_10_Template, 38, 6, \"div\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, NavbarComponent_div_11_Template, 2, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [i3.NgIf, i2.RouterLink, i2.RouterLinkActive, i4.MatButton, i4.MatIconButton, i5.MatProgressBar, i6.MatToolbar, i7.MatDivider, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, i9.MatBadge],\n      styles: [\".navbar-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: var(--z-index-fixed);\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  height: 64px;\\n  padding: 0;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: var(--text-primary);\\n  box-shadow: var(--elevation-2);\\n  transition: all var(--transition-medium);\\n}\\n\\n.navbar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  height: 100%;\\n  padding: 0 var(--spacing-md);\\n  max-width: var(--container-xl);\\n  margin: 0 auto;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.brand-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  text-decoration: none;\\n  font-weight: 600;\\n  font-size: 1.25rem;\\n  color: var(--primary-dark);\\n  transition: all var(--transition-fast);\\n}\\n\\n.brand-link[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n\\n.brand-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  border-radius: 12px;\\n  color: white;\\n  font-size: 1.2rem;\\n  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.3);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);\\n  animation: shimmer 2s infinite;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.brand-name[_ngcontent-%COMP%] {\\n  letter-spacing: 0.5px;\\n  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  font-weight: 700;\\n}\\n\\n\\n\\n.navbar-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  margin-left: var(--spacing-xl);\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  position: relative;\\n  color: var(--text-secondary);\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n  padding: var(--spacing-sm) var(--spacing-md);\\n  border-radius: var(--border-radius-medium);\\n  transition: all var(--transition-fast);\\n  text-decoration: none;\\n}\\n\\n.nav-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  transition: all var(--transition-fast);\\n}\\n\\n.nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 50%;\\n  width: 0;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--primary-color), var(--accent-color));\\n  border-radius: 3px;\\n  transform: translateX(-50%);\\n  transition: width var(--transition-medium);\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateY(-2px);\\n}\\n\\n.nav-link.active-link[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-weight: 600;\\n}\\n\\n.nav-link.active-link[_ngcontent-%COMP%]::after {\\n  width: 30px;\\n}\\n\\n\\n\\n.navbar-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n}\\n\\n\\n\\n.notification-button[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  transition: all var(--transition-fast);\\n}\\n\\n.notification-button[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n  transform: translateY(-2px);\\n}\\n\\n.notification-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n\\n\\n\\n.user-menu-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  color: var(--text-primary);\\n  font-weight: 500;\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--border-radius-medium);\\n  transition: all var(--transition-fast);\\n  border: 1px solid transparent;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  border-color: rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));\\n  border-radius: 50%;\\n  color: white;\\n  font-size: 0.9rem;\\n  box-shadow: 0 2px 4px rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.user-avatar.large[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  font-size: 1.2rem;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  max-width: 150px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: var(--text-secondary);\\n  transition: transform var(--transition-fast);\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%]:hover   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  transform: translateY(2px);\\n}\\n\\n\\n\\n.user-menu-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  padding: var(--spacing-md) var(--spacing-md) var(--spacing-sm);\\n}\\n\\n.user-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n}\\n\\n.user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.8rem;\\n  color: var(--text-secondary);\\n}\\n\\n\\n\\n  .mat-menu-panel {\\n  border-radius: var(--border-radius-medium) !important;\\n  overflow: hidden;\\n  min-width: 220px !important;\\n  box-shadow: var(--elevation-3) !important;\\n}\\n\\n  .mat-menu-content {\\n  padding: 0 !important;\\n}\\n\\n  .mat-menu-item {\\n  font-family: var(--font-family);\\n  height: 48px;\\n  line-height: 48px;\\n}\\n\\n  .mat-menu-item i {\\n  margin-right: var(--spacing-sm);\\n  font-size: 1.1rem;\\n  width: 24px;\\n  height: 24px;\\n  line-height: 24px;\\n  text-align: center;\\n  color: var(--primary-color);\\n}\\n\\n  .mat-menu-item:hover:not([disabled]) {\\n  background-color: rgba(var(--primary-color-rgb), 0.05) !important;\\n}\\n\\n  .mat-menu-item[disabled] {\\n  color: var(--text-disabled) !important;\\n}\\n\\n  .mat-menu-item[disabled] i {\\n  color: var(--text-disabled) !important;\\n}\\n\\n\\n\\n.progress-bar-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 64px;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.scrolled[_nghost-%COMP%]   .navbar[_ngcontent-%COMP%], .scrolled   [_nghost-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  height: 56px;\\n  box-shadow: var(--elevation-3);\\n  background: rgba(255, 255, 255, 0.98);\\n}\\n\\n.scrolled[_nghost-%COMP%]   .brand-icon[_ngcontent-%COMP%], .scrolled   [_nghost-%COMP%]   .brand-icon[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  font-size: 1.1rem;\\n}\\n\\n\\n\\n@media (max-width: 992px) {\\n  .navbar-links[_ngcontent-%COMP%] {\\n    margin-left: var(--spacing-md);\\n    gap: var(--spacing-sm);\\n  }\\n\\n  .nav-link[_ngcontent-%COMP%] {\\n    padding: var(--spacing-sm) var(--spacing-sm);\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .menu-toggle[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n\\n  .navbar-links[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n\\n  .brand-link[_ngcontent-%COMP%] {\\n    margin-right: auto;\\n  }\\n}\\n\\n@media (max-width: 600px) {\\n  .brand-name[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n\\n  .user-name[_ngcontent-%COMP%] {\\n    max-width: 80px;\\n  }\\n\\n  .navbar-actions[_ngcontent-%COMP%] {\\n    gap: var(--spacing-sm);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NavigationStart", "NavigationEnd", "NavigationCancel", "NavigationError", "filter", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "NavbarComponent_div_10_Template_button_click_34_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵadvance", "ɵɵproperty", "_r3", "ɵɵtextInterpolate", "ctx_r1", "userName", "userAgency", "NavbarComponent", "constructor", "authService", "router", "isLoggedIn", "loading", "subscriptions", "scrolled", "ngOnInit", "authSub", "isAuthenticated$", "subscribe", "isAuthenticated", "userInfo", "getUserInfo", "username", "agency", "push", "routerSub", "events", "pipe", "event", "checkScroll", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "navigate", "scrollPosition", "window", "pageYOffset", "document", "documentElement", "scrollTop", "body", "classList", "add", "remove", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "hostBindings", "NavbarComponent_HostBindings", "rf", "ctx", "ɵɵresolveWindow", "ɵɵtemplate", "NavbarComponent_div_9_Template", "NavbarComponent_div_10_Template", "NavbarComponent_div_11_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\navbar\\navbar.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\navbar\\navbar.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { Subscription } from 'rxjs';\nimport { filter } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-navbar',\n  templateUrl: './navbar.component.html',\n  styleUrls: ['./navbar.component.css']\n})\nexport class NavbarComponent implements OnInit, OnDestroy {\n  isLoggedIn = false;\n  userName = '';\n  userAgency = '';\n  loading = false;\n\n  private subscriptions: Subscription[] = [];\n  private scrolled = false;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) { }\n\n  ngOnInit(): void {\n    // Surveiller l'état d'authentification\n    const authSub = this.authService.isAuthenticated$.subscribe(isAuthenticated => {\n      this.isLoggedIn = isAuthenticated;\n\n      if (isAuthenticated) {\n        const userInfo = this.authService.getUserInfo();\n        this.userName = userInfo?.username || 'Utilisateur';\n        this.userAgency = userInfo?.agency || 'Agence';\n      }\n    });\n    this.subscriptions.push(authSub);\n\n    // Surveiller les événements de navigation pour afficher la barre de progression\n    const routerSub = this.router.events\n      .pipe(\n        filter(event =>\n          event instanceof NavigationStart ||\n          event instanceof NavigationEnd ||\n          event instanceof NavigationCancel ||\n          event instanceof NavigationError\n        )\n      )\n      .subscribe(event => {\n        if (event instanceof NavigationStart) {\n          this.loading = true;\n        } else {\n          this.loading = false;\n        }\n      });\n    this.subscriptions.push(routerSub);\n\n    // Vérifier le défilement initial\n    this.checkScroll();\n  }\n\n  ngOnDestroy(): void {\n    // Nettoyer les abonnements\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  // Méthode de déconnexion\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n\n  // Écouter l'événement de défilement pour changer l'apparence de la navbar\n  @HostListener('window:scroll')\n  checkScroll(): void {\n    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n\n    if (scrollPosition > 20 && !this.scrolled) {\n      document.body.classList.add('scrolled');\n      this.scrolled = true;\n    } else if (scrollPosition <= 20 && this.scrolled) {\n      document.body.classList.remove('scrolled');\n      this.scrolled = false;\n    }\n  }\n}\n", "<div class=\"navbar-wrapper\">\n  <mat-toolbar class=\"navbar\">\n    <div class=\"navbar-container\">\n      <!-- Brand Logo -->\n      <div class=\"navbar-brand\">\n        <a routerLink=\"/accueil\" class=\"brand-link\">\n          <div class=\"brand-icon\">\n            <i class=\"fas fa-plane-departure\"></i>\n          </div>\n          <span class=\"brand-name\">E-Tourism</span>\n        </a>\n      </div>\n\n      <!-- Navigation Links -->\n      <div class=\"navbar-links\" *ngIf=\"isLoggedIn\">\n        <a routerLink=\"/accueil\" routerLinkActive=\"active-link\" class=\"nav-link\">\n          <i class=\"fas fa-home\"></i>\n          <span>Accueil</span>\n        </a>\n        <a routerLink=\"/search-price\" routerLinkActive=\"active-link\" class=\"nav-link\">\n          <i class=\"fas fa-search\"></i>\n          <span>Rechercher</span>\n        </a>\n      </div>\n\n      <!-- Right Side Actions -->\n      <div class=\"navbar-actions\" *ngIf=\"isLoggedIn\">\n        <!-- Notification Icon (for future use) -->\n        <button mat-icon-button class=\"notification-button\" [matBadge]=\"0\" [matBadgeHidden]=\"true\" matBadgeColor=\"accent\" aria-label=\"Notifications\">\n          <i class=\"fas fa-bell\"></i>\n        </button>\n\n        <!-- User Menu -->\n        <div class=\"user-menu-container\">\n          <button mat-button [matMenuTriggerFor]=\"userMenu\" class=\"user-menu-button\">\n            <div class=\"user-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <span class=\"user-name\">{{ userName }}</span>\n            <i class=\"fas fa-chevron-down\"></i>\n          </button>\n\n          <mat-menu #userMenu=\"matMenu\" xPosition=\"before\" class=\"user-menu\">\n            <div class=\"user-menu-header\">\n              <div class=\"user-avatar large\">\n                <i class=\"fas fa-user\"></i>\n              </div>\n              <div class=\"user-info\">\n                <h4>{{ userName }}</h4>\n                <p>{{ userAgency }}</p>\n              </div>\n            </div>\n\n            <mat-divider></mat-divider>\n\n            <button mat-menu-item disabled>\n              <i class=\"fas fa-user-circle\"></i>\n              <span>Mon profil</span>\n            </button>\n            <button mat-menu-item disabled>\n              <i class=\"fas fa-cog\"></i>\n              <span>Paramètres</span>\n            </button>\n            <button mat-menu-item disabled>\n              <i class=\"fas fa-history\"></i>\n              <span>Historique</span>\n            </button>\n\n            <mat-divider></mat-divider>\n\n            <button mat-menu-item (click)=\"logout()\">\n              <i class=\"fas fa-sign-out-alt\"></i>\n              <span>Déconnexion</span>\n            </button>\n          </mat-menu>\n        </div>\n      </div>\n    </div>\n  </mat-toolbar>\n\n  <!-- Progress Bar for page loading -->\n  <div class=\"progress-bar-container\" *ngIf=\"loading\">\n    <mat-progress-bar mode=\"indeterminate\" color=\"accent\"></mat-progress-bar>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiBA,eAAe,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,iBAAiB;AAG3G,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;ICUjCC,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAE,SAAA,YAA2B;IAC3BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEtBJ,EAAA,CAAAC,cAAA,YAA8E;IAC5ED,EAAA,CAAAE,SAAA,YAA6B;IAC7BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAK3BJ,EAAA,CAAAC,cAAA,cAA+C;IAG3CD,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAC,cAAA,cAAiC;IAG3BD,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAG,MAAA,GAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC7CJ,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAC,cAAA,wBAAmE;IAG7DD,EAAA,CAAAE,SAAA,aAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAG,MAAA,IAAc;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvBJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAI3BJ,EAAA,CAAAE,SAAA,mBAA2B;IAE3BF,EAAA,CAAAC,cAAA,kBAA+B;IAC7BD,EAAA,CAAAE,SAAA,aAAkC;IAClCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEzBJ,EAAA,CAAAC,cAAA,kBAA+B;IAC7BD,EAAA,CAAAE,SAAA,aAA0B;IAC1BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,uBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEzBJ,EAAA,CAAAC,cAAA,kBAA+B;IAC7BD,EAAA,CAAAE,SAAA,aAA8B;IAC9BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGzBJ,EAAA,CAAAE,SAAA,mBAA2B;IAE3BF,EAAA,CAAAC,cAAA,kBAAyC;IAAnBD,EAAA,CAAAK,UAAA,mBAAAC,yDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IACtCZ,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,wBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IA5CsBJ,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAc,UAAA,eAAc;IAM7Cd,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAAc,UAAA,sBAAAC,GAAA,CAA8B;IAIvBf,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAc;IAU9BlB,EAAA,CAAAa,SAAA,GAAc;IAAdb,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAc;IACflB,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAE,UAAA,CAAgB;;;;;IAgCjCnB,EAAA,CAAAC,cAAA,cAAoD;IAClDD,EAAA,CAAAE,SAAA,2BAAyE;IAC3EF,EAAA,CAAAI,YAAA,EAAM;;;ADxER,OAAM,MAAOgB,eAAe;EAS1BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAVhB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAN,QAAQ,GAAG,EAAE;IACb,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAM,OAAO,GAAG,KAAK;IAEP,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,QAAQ,GAAG,KAAK;EAKpB;EAEJC,QAAQA,CAAA;IACN;IACA,MAAMC,OAAO,GAAG,IAAI,CAACP,WAAW,CAACQ,gBAAgB,CAACC,SAAS,CAACC,eAAe,IAAG;MAC5E,IAAI,CAACR,UAAU,GAAGQ,eAAe;MAEjC,IAAIA,eAAe,EAAE;QACnB,MAAMC,QAAQ,GAAG,IAAI,CAACX,WAAW,CAACY,WAAW,EAAE;QAC/C,IAAI,CAAChB,QAAQ,GAAGe,QAAQ,EAAEE,QAAQ,IAAI,aAAa;QACnD,IAAI,CAAChB,UAAU,GAAGc,QAAQ,EAAEG,MAAM,IAAI,QAAQ;;IAElD,CAAC,CAAC;IACF,IAAI,CAACV,aAAa,CAACW,IAAI,CAACR,OAAO,CAAC;IAEhC;IACA,MAAMS,SAAS,GAAG,IAAI,CAACf,MAAM,CAACgB,MAAM,CACjCC,IAAI,CACHzC,MAAM,CAAC0C,KAAK,IACVA,KAAK,YAAY9C,eAAe,IAChC8C,KAAK,YAAY7C,aAAa,IAC9B6C,KAAK,YAAY5C,gBAAgB,IACjC4C,KAAK,YAAY3C,eAAe,CACjC,CACF,CACAiC,SAAS,CAACU,KAAK,IAAG;MACjB,IAAIA,KAAK,YAAY9C,eAAe,EAAE;QACpC,IAAI,CAAC8B,OAAO,GAAG,IAAI;OACpB,MAAM;QACL,IAAI,CAACA,OAAO,GAAG,KAAK;;IAExB,CAAC,CAAC;IACJ,IAAI,CAACC,aAAa,CAACW,IAAI,CAACC,SAAS,CAAC;IAElC;IACA,IAAI,CAACI,WAAW,EAAE;EACpB;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACjB,aAAa,CAACkB,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEA;EACAlC,MAAMA,CAAA;IACJ,IAAI,CAACU,WAAW,CAACV,MAAM,EAAE;IACzB,IAAI,CAACW,MAAM,CAACwB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEA;EAEAL,WAAWA,CAAA;IACT,MAAMM,cAAc,GAAGC,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACC,SAAS,IAAIF,QAAQ,CAACG,IAAI,CAACD,SAAS,IAAI,CAAC;IAE/G,IAAIL,cAAc,GAAG,EAAE,IAAI,CAAC,IAAI,CAACrB,QAAQ,EAAE;MACzCwB,QAAQ,CAACG,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACvC,IAAI,CAAC7B,QAAQ,GAAG,IAAI;KACrB,MAAM,IAAIqB,cAAc,IAAI,EAAE,IAAI,IAAI,CAACrB,QAAQ,EAAE;MAChDwB,QAAQ,CAACG,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,UAAU,CAAC;MAC1C,IAAI,CAAC9B,QAAQ,GAAG,KAAK;;EAEzB;;;uBAzEWP,eAAe,EAAApB,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5D,EAAA,CAAA0D,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAf1C,eAAe;MAAA2C,SAAA;MAAAC,YAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAAfC,GAAA,CAAAzB,WAAA,EAAa;UAAA,UAAA1C,EAAA,CAAAoE,eAAA;;;;;;;;UCX1BpE,EAAA,CAAAC,cAAA,aAA4B;UAOhBD,EAAA,CAAAE,SAAA,WAAsC;UACxCF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAG,MAAA,gBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAK7CJ,EAAA,CAAAqE,UAAA,IAAAC,8BAAA,iBASM;UAGNtE,EAAA,CAAAqE,UAAA,KAAAE,+BAAA,kBAkDM;UACRvE,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAqE,UAAA,KAAAG,+BAAA,kBAEM;UACRxE,EAAA,CAAAI,YAAA,EAAM;;;UAtE2BJ,EAAA,CAAAa,SAAA,GAAgB;UAAhBb,EAAA,CAAAc,UAAA,SAAAqD,GAAA,CAAA3C,UAAA,CAAgB;UAYdxB,EAAA,CAAAa,SAAA,GAAgB;UAAhBb,EAAA,CAAAc,UAAA,SAAAqD,GAAA,CAAA3C,UAAA,CAAgB;UAuDZxB,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAAc,UAAA,SAAAqD,GAAA,CAAA1C,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}