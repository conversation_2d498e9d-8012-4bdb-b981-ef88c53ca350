.multi-city-container {
  margin-top: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.multi-city-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #2989d8;
  font-size: 18px;
}

.additional-segment {
  margin-bottom: 20px;
  position: relative;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

.additional-segment:not(:first-child) {
  border-top: 1px solid #e0e0e0;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.segment-header h4 {
  margin: 0;
  color: #2989d8;
  font-size: 16px;
  font-weight: 600;
}

.add-flight-button-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 15px;
}

.add-flight-btn {
  background-color: #2989d8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.add-flight-btn:hover {
  background-color: #1e5799;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.delete-segment-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.delete-segment-btn:hover {
  background-color: #c82333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.multi-city-placeholder {
  display: none;
}

/* Styles pour les champs de formulaire dans les segments multi-city */
.additional-segment .single-line-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: flex-start;
}

.additional-segment .form-group {
  flex: 1;
  min-width: 150px;
}

.additional-segment .swap-button-container {
  display: flex;
  align-items: flex-end;
  margin-bottom: 20px;
}

.additional-segment .delete-button-container {
  display: flex;
  align-items: flex-end;
  margin-bottom: 20px;
  flex: 0 0 auto;
}
