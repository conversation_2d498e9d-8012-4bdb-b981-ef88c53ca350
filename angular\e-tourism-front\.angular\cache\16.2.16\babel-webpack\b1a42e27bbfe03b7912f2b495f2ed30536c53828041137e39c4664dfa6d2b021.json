{"ast": null, "code": "import { of } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { FlightClassType, LocationType, ProductType } from '../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ProductService = /*#__PURE__*/(() => {\n  class ProductService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = 'http://localhost:8080/product';\n      // Données statiques pour les emplacements par type (à remplacer par un appel API réel)\n      this.countryLocations = [{\n        id: 'FR',\n        name: 'France',\n        type: 1,\n        code: 'FR',\n        country: 'France'\n      }, {\n        id: 'UK',\n        name: 'United Kingdom',\n        type: 1,\n        code: 'UK',\n        country: 'United Kingdom'\n      }, {\n        id: 'US',\n        name: 'United States',\n        type: 1,\n        code: 'US',\n        country: 'United States'\n      }, {\n        id: 'ES',\n        name: 'Spain',\n        type: 1,\n        code: 'ES',\n        country: 'Spain'\n      }, {\n        id: 'IT',\n        name: 'Italy',\n        type: 1,\n        code: 'IT',\n        country: 'Italy'\n      }, {\n        id: 'DE',\n        name: 'Germany',\n        type: 1,\n        code: 'DE',\n        country: 'Germany'\n      }, {\n        id: 'TR',\n        name: 'Turkey',\n        type: 1,\n        code: 'TR',\n        country: 'Turkey'\n      }, {\n        id: 'AE',\n        name: 'United Arab Emirates',\n        type: 1,\n        code: 'AE',\n        country: 'United Arab Emirates'\n      }, {\n        id: 'AU',\n        name: 'Australia',\n        type: 1,\n        code: 'AU',\n        country: 'Australia'\n      }, {\n        id: 'JP',\n        name: 'Japan',\n        type: 1,\n        code: 'JP',\n        country: 'Japan'\n      }, {\n        id: 'TN',\n        name: 'Tunisia',\n        type: 1,\n        code: 'TN',\n        country: 'Tunisia'\n      }, {\n        id: 'MA',\n        name: 'Morocco',\n        type: 1,\n        code: 'MA',\n        country: 'Morocco'\n      }, {\n        id: 'EG',\n        name: 'Egypt',\n        type: 1,\n        code: 'EG',\n        country: 'Egypt'\n      }, {\n        id: 'CA',\n        name: 'Canada',\n        type: 1,\n        code: 'CA',\n        country: 'Canada'\n      }, {\n        id: 'MX',\n        name: 'Mexico',\n        type: 1,\n        code: 'MX',\n        country: 'Mexico'\n      }];\n      this.cityLocations = [{\n        id: 'PAR',\n        name: 'Paris',\n        type: 2,\n        code: 'PAR',\n        country: 'France'\n      }, {\n        id: 'LON',\n        name: 'London',\n        type: 2,\n        code: 'LON',\n        country: 'United Kingdom'\n      }, {\n        id: 'NYC',\n        name: 'New York',\n        type: 2,\n        code: 'NYC',\n        country: 'United States'\n      }, {\n        id: 'MAD',\n        name: 'Madrid',\n        type: 2,\n        code: 'MAD',\n        country: 'Spain'\n      }, {\n        id: 'BCN',\n        name: 'Barcelona',\n        type: 2,\n        code: 'BCN',\n        country: 'Spain'\n      }, {\n        id: 'ROM',\n        name: 'Rome',\n        type: 2,\n        code: 'ROM',\n        country: 'Italy'\n      }, {\n        id: 'BER',\n        name: 'Berlin',\n        type: 2,\n        code: 'BER',\n        country: 'Germany'\n      }, {\n        id: 'IST',\n        name: 'Istanbul',\n        type: 2,\n        code: 'IST',\n        country: 'Turkey'\n      }, {\n        id: 'DXB',\n        name: 'Dubai',\n        type: 2,\n        code: 'DXB',\n        country: 'United Arab Emirates'\n      }, {\n        id: 'SYD',\n        name: 'Sydney',\n        type: 2,\n        code: 'SYD',\n        country: 'Australia'\n      }, {\n        id: 'TYO',\n        name: 'Tokyo',\n        type: 2,\n        code: 'TYO',\n        country: 'Japan'\n      }, {\n        id: 'TUN',\n        name: 'Tunis',\n        type: 2,\n        code: 'TUN',\n        country: 'Tunisia'\n      }, {\n        id: 'CAS',\n        name: 'Casablanca',\n        type: 2,\n        code: 'CAS',\n        country: 'Morocco'\n      }, {\n        id: 'CAI',\n        name: 'Cairo',\n        type: 2,\n        code: 'CAI',\n        country: 'Egypt'\n      }, {\n        id: 'YTO',\n        name: 'Toronto',\n        type: 2,\n        code: 'YTO',\n        country: 'Canada'\n      }, {\n        id: 'MEX',\n        name: 'Mexico City',\n        type: 2,\n        code: 'MEX',\n        country: 'Mexico'\n      }];\n      this.townLocations = [{\n        id: 'NIC',\n        name: 'Nice',\n        type: 3,\n        code: 'NIC',\n        country: 'France'\n      }, {\n        id: 'MAN',\n        name: 'Manchester',\n        type: 3,\n        code: 'MAN',\n        country: 'United Kingdom'\n      }, {\n        id: 'BOS',\n        name: 'Boston',\n        type: 3,\n        code: 'BOS',\n        country: 'United States'\n      }, {\n        id: 'VAL',\n        name: 'Valencia',\n        type: 3,\n        code: 'VAL',\n        country: 'Spain'\n      }, {\n        id: 'NAP',\n        name: 'Naples',\n        type: 3,\n        code: 'NAP',\n        country: 'Italy'\n      }, {\n        id: 'MUC',\n        name: 'Munich',\n        type: 3,\n        code: 'MUC',\n        country: 'Germany'\n      }, {\n        id: 'ANK',\n        name: 'Ankara',\n        type: 3,\n        code: 'ANK',\n        country: 'Turkey'\n      }, {\n        id: 'SHJ',\n        name: 'Sharjah',\n        type: 3,\n        code: 'SHJ',\n        country: 'United Arab Emirates'\n      }, {\n        id: 'MEL',\n        name: 'Melbourne',\n        type: 3,\n        code: 'MEL',\n        country: 'Australia'\n      }, {\n        id: 'OSA',\n        name: 'Osaka',\n        type: 3,\n        code: 'OSA',\n        country: 'Japan'\n      }];\n      this.villageLocations = [{\n        id: 'CDV',\n        name: 'Courchevel',\n        type: 4,\n        code: 'CDV',\n        country: 'France'\n      }, {\n        id: 'OXF',\n        name: 'Oxford',\n        type: 4,\n        code: 'OXF',\n        country: 'United Kingdom'\n      }, {\n        id: 'ASP',\n        name: 'Aspen',\n        type: 4,\n        code: 'ASP',\n        country: 'United States'\n      }, {\n        id: 'IBZ',\n        name: 'Ibiza',\n        type: 4,\n        code: 'IBZ',\n        country: 'Spain'\n      }, {\n        id: 'PSA',\n        name: 'Pisa',\n        type: 4,\n        code: 'PSA',\n        country: 'Italy'\n      }, {\n        id: 'BAD',\n        name: 'Baden-Baden',\n        type: 4,\n        code: 'BAD',\n        country: 'Germany'\n      }, {\n        id: 'BOD',\n        name: 'Bodrum',\n        type: 4,\n        code: 'BOD',\n        country: 'Turkey'\n      }, {\n        id: 'FUJ',\n        name: 'Fujairah',\n        type: 4,\n        code: 'FUJ',\n        country: 'United Arab Emirates'\n      }, {\n        id: 'BNK',\n        name: 'Ballina',\n        type: 4,\n        code: 'BNK',\n        country: 'Australia'\n      }, {\n        id: 'MYJ',\n        name: 'Matsuyama',\n        type: 4,\n        code: 'MYJ',\n        country: 'Japan'\n      }];\n      this.airportLocations = [{\n        id: 'CDG',\n        name: 'Charles de Gaulle Airport',\n        type: 5,\n        code: 'CDG',\n        country: 'France',\n        city: 'Paris'\n      }, {\n        id: 'ORY',\n        name: 'Orly Airport',\n        type: 5,\n        code: 'ORY',\n        country: 'France',\n        city: 'Paris'\n      }, {\n        id: 'LHR',\n        name: 'Heathrow Airport',\n        type: 5,\n        code: 'LHR',\n        country: 'United Kingdom',\n        city: 'London'\n      }, {\n        id: 'LGW',\n        name: 'Gatwick Airport',\n        type: 5,\n        code: 'LGW',\n        country: 'United Kingdom',\n        city: 'London'\n      }, {\n        id: 'JFK',\n        name: 'John F. Kennedy Airport',\n        type: 5,\n        code: 'JFK',\n        country: 'United States',\n        city: 'New York'\n      }, {\n        id: 'LGA',\n        name: 'LaGuardia Airport',\n        type: 5,\n        code: 'LGA',\n        country: 'United States',\n        city: 'New York'\n      }, {\n        id: 'FCO',\n        name: 'Leonardo da Vinci Airport',\n        type: 5,\n        code: 'FCO',\n        country: 'Italy',\n        city: 'Rome'\n      }, {\n        id: 'TXL',\n        name: 'Tegel Airport',\n        type: 5,\n        code: 'TXL',\n        country: 'Germany',\n        city: 'Berlin'\n      }, {\n        id: 'SAW',\n        name: 'Sabiha Gökçen Airport',\n        type: 5,\n        code: 'SAW',\n        country: 'Turkey',\n        city: 'Istanbul'\n      }, {\n        id: 'DXB',\n        name: 'Dubai International Airport',\n        type: 5,\n        code: 'DXB',\n        country: 'United Arab Emirates',\n        city: 'Dubai'\n      }, {\n        id: 'SYD',\n        name: 'Sydney Airport',\n        type: 5,\n        code: 'SYD',\n        country: 'Australia',\n        city: 'Sydney'\n      }, {\n        id: 'HND',\n        name: 'Haneda Airport',\n        type: 5,\n        code: 'HND',\n        country: 'Japan',\n        city: 'Tokyo'\n      }, {\n        id: 'NRT',\n        name: 'Narita Airport',\n        type: 5,\n        code: 'NRT',\n        country: 'Japan',\n        city: 'Tokyo'\n      }, {\n        id: 'TUN',\n        name: 'Tunis Carthage Airport',\n        type: 5,\n        code: 'TUN',\n        country: 'Tunisia',\n        city: 'Tunis'\n      }, {\n        id: 'CMN',\n        name: 'Mohammed V Airport',\n        type: 5,\n        code: 'CMN',\n        country: 'Morocco',\n        city: 'Casablanca'\n      }, {\n        id: 'CAI',\n        name: 'Cairo International Airport',\n        type: 5,\n        code: 'CAI',\n        country: 'Egypt',\n        city: 'Cairo'\n      }, {\n        id: 'YYZ',\n        name: 'Toronto Pearson Airport',\n        type: 5,\n        code: 'YYZ',\n        country: 'Canada',\n        city: 'Toronto'\n      }, {\n        id: 'MEX',\n        name: 'Mexico City International Airport',\n        type: 5,\n        code: 'MEX',\n        country: 'Mexico',\n        city: 'Mexico City'\n      }];\n      // Toutes les locations combinées\n      this.locations = [];\n      // Initialiser la liste combinée\n      this.locations = [...this.countryLocations, ...this.cityLocations, ...this.townLocations, ...this.villageLocations, ...this.airportLocations];\n    }\n    searchPrice(request) {\n      return this.http.post(`${this.apiUrl}/pricesearch`, request).pipe(catchError(error => {\n        console.error('Error searching prices:', error);\n        throw error;\n      }));\n    }\n    getOffers(request) {\n      return this.http.post(`${this.apiUrl}/getoffers`, request).pipe(catchError(error => {\n        console.error('Error getting offers:', error);\n        throw error;\n      }));\n    }\n    getLocations(query) {\n      // Dans une application réelle, cela devrait être un appel API\n      // Pour l'instant, nous filtrons les données statiques\n      return of(this.locations.filter(location => location.name.toLowerCase().includes(query.toLowerCase()) || location.code && location.code.toLowerCase().includes(query.toLowerCase())));\n    }\n    // Méthode pour obtenir toutes les locations disponibles\n    getAllLocations() {\n      // Dans une application réelle, cela devrait être un appel API\n      // Pour l'instant, nous retournons toutes les données statiques\n      return of(this.locations);\n    }\n    // Méthode pour obtenir les locations par type\n    getLocationsByType(locationType) {\n      switch (locationType) {\n        case 1:\n          // Country\n          return of(this.countryLocations);\n        case 2:\n          // City\n          return of(this.cityLocations);\n        case 3:\n          // Town\n          return of(this.townLocations);\n        case 4:\n          // Village\n          return of(this.villageLocations);\n        case 5:\n          // Airport\n          return of(this.airportLocations);\n        default:\n          return of(this.locations);\n      }\n    }\n    // Méthode utilitaire pour créer une requête de recherche de prix par défaut\n    createDefaultPriceSearchRequest(departureLocationId, arrivalLocationId, departureDate, passengerCount = 1) {\n      return {\n        ProductType: ProductType.Flight,\n        ServiceTypes: [\"Flight\"],\n        CheckIn: departureDate,\n        DepartureLocations: [{\n          id: departureLocationId,\n          type: LocationType.Airport\n        }],\n        ArrivalLocations: [{\n          id: arrivalLocationId,\n          type: LocationType.Airport\n        }],\n        Passengers: [{\n          type: 1,\n          count: passengerCount\n        }],\n        showOnlyNonStopFlight: false,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: 0 // All\n          }\n        },\n\n        acceptPendingProviders: true,\n        forceFlightBundlePackage: false,\n        disablePackageOfferTotalPrice: false,\n        calculateFlightFees: true,\n        flightClasses: [FlightClassType.ECONOMY],\n        Culture: \"fr-FR\",\n        Currency: \"EUR\"\n      };\n    }\n    // Méthode utilitaire pour créer une requête GetOffers par défaut\n    createDefaultGetOffersRequest(searchId, offerIds, productType = 3,\n    // Flight par défaut\n    currency = 'EUR', culture = 'en-US') {\n      return {\n        productType: productType,\n        searchId: searchId,\n        offerIds: offerIds,\n        currency: currency,\n        culture: culture\n      };\n    }\n    static {\n      this.ɵfac = function ProductService_Factory(t) {\n        return new (t || ProductService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ProductService,\n        factory: ProductService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ProductService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}