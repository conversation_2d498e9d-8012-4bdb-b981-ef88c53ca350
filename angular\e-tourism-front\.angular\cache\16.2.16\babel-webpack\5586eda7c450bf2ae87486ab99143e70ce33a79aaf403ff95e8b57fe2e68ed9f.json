{"ast": null, "code": "import * as i1 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { coerceElement, coerceNumberProperty, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { EMPTY, Subject, fromEvent } from 'rxjs';\nimport { auditTime, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nlet AutofillMonitor = /*#__PURE__*/(() => {\n  class AutofillMonitor {\n    constructor(_platform, _ngZone) {\n      this._platform = _platform;\n      this._ngZone = _ngZone;\n      this._monitoredElements = new Map();\n    }\n    monitor(elementOrRef) {\n      if (!this._platform.isBrowser) {\n        return EMPTY;\n      }\n      const element = coerceElement(elementOrRef);\n      const info = this._monitoredElements.get(element);\n      if (info) {\n        return info.subject;\n      }\n      const result = new Subject();\n      const cssClass = 'cdk-text-field-autofilled';\n      const listener = event => {\n        // Animation events fire on initial element render, we check for the presence of the autofill\n        // CSS class to make sure this is a real change in state, not just the initial render before\n        // we fire off events.\n        if (event.animationName === 'cdk-text-field-autofill-start' && !element.classList.contains(cssClass)) {\n          element.classList.add(cssClass);\n          this._ngZone.run(() => result.next({\n            target: event.target,\n            isAutofilled: true\n          }));\n        } else if (event.animationName === 'cdk-text-field-autofill-end' && element.classList.contains(cssClass)) {\n          element.classList.remove(cssClass);\n          this._ngZone.run(() => result.next({\n            target: event.target,\n            isAutofilled: false\n          }));\n        }\n      };\n      this._ngZone.runOutsideAngular(() => {\n        element.addEventListener('animationstart', listener, listenerOptions);\n        element.classList.add('cdk-text-field-autofill-monitored');\n      });\n      this._monitoredElements.set(element, {\n        subject: result,\n        unlisten: () => {\n          element.removeEventListener('animationstart', listener, listenerOptions);\n        }\n      });\n      return result;\n    }\n    stopMonitoring(elementOrRef) {\n      const element = coerceElement(elementOrRef);\n      const info = this._monitoredElements.get(element);\n      if (info) {\n        info.unlisten();\n        info.subject.complete();\n        element.classList.remove('cdk-text-field-autofill-monitored');\n        element.classList.remove('cdk-text-field-autofilled');\n        this._monitoredElements.delete(element);\n      }\n    }\n    ngOnDestroy() {\n      this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    static {\n      this.ɵfac = function AutofillMonitor_Factory(t) {\n        return new (t || AutofillMonitor)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: AutofillMonitor,\n        factory: AutofillMonitor.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AutofillMonitor;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** A directive that can be used to monitor the autofill state of an input. */\nlet CdkAutofill = /*#__PURE__*/(() => {\n  class CdkAutofill {\n    constructor(_elementRef, _autofillMonitor) {\n      this._elementRef = _elementRef;\n      this._autofillMonitor = _autofillMonitor;\n      /** Emits when the autofill state of the element changes. */\n      this.cdkAutofill = new EventEmitter();\n    }\n    ngOnInit() {\n      this._autofillMonitor.monitor(this._elementRef).subscribe(event => this.cdkAutofill.emit(event));\n    }\n    ngOnDestroy() {\n      this._autofillMonitor.stopMonitoring(this._elementRef);\n    }\n    static {\n      this.ɵfac = function CdkAutofill_Factory(t) {\n        return new (t || CdkAutofill)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(AutofillMonitor));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkAutofill,\n        selectors: [[\"\", \"cdkAutofill\", \"\"]],\n        outputs: {\n          cdkAutofill: \"cdkAutofill\"\n        }\n      });\n    }\n  }\n  return CdkAutofill;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Directive to automatically resize a textarea to fit its content. */\nlet CdkTextareaAutosize = /*#__PURE__*/(() => {\n  class CdkTextareaAutosize {\n    /** Minimum amount of rows in the textarea. */\n    get minRows() {\n      return this._minRows;\n    }\n    set minRows(value) {\n      this._minRows = coerceNumberProperty(value);\n      this._setMinHeight();\n    }\n    /** Maximum amount of rows in the textarea. */\n    get maxRows() {\n      return this._maxRows;\n    }\n    set maxRows(value) {\n      this._maxRows = coerceNumberProperty(value);\n      this._setMaxHeight();\n    }\n    /** Whether autosizing is enabled or not */\n    get enabled() {\n      return this._enabled;\n    }\n    set enabled(value) {\n      value = coerceBooleanProperty(value);\n      // Only act if the actual value changed. This specifically helps to not run\n      // resizeToFitContent too early (i.e. before ngAfterViewInit)\n      if (this._enabled !== value) {\n        (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n      }\n    }\n    get placeholder() {\n      return this._textareaElement.placeholder;\n    }\n    set placeholder(value) {\n      this._cachedPlaceholderHeight = undefined;\n      if (value) {\n        this._textareaElement.setAttribute('placeholder', value);\n      } else {\n        this._textareaElement.removeAttribute('placeholder');\n      }\n      this._cacheTextareaPlaceholderHeight();\n    }\n    constructor(_elementRef, _platform, _ngZone, /** @breaking-change 11.0.0 make document required */\n    document) {\n      this._elementRef = _elementRef;\n      this._platform = _platform;\n      this._ngZone = _ngZone;\n      this._destroyed = new Subject();\n      this._enabled = true;\n      /**\n       * Value of minRows as of last resize. If the minRows has decreased, the\n       * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n       * does not have the same problem because it does not affect the textarea's scrollHeight.\n       */\n      this._previousMinRows = -1;\n      this._isViewInited = false;\n      /** Handles `focus` and `blur` events. */\n      this._handleFocusEvent = event => {\n        this._hasFocus = event.type === 'focus';\n      };\n      this._document = document;\n      this._textareaElement = this._elementRef.nativeElement;\n    }\n    /** Sets the minimum height of the textarea as determined by minRows. */\n    _setMinHeight() {\n      const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n      if (minHeight) {\n        this._textareaElement.style.minHeight = minHeight;\n      }\n    }\n    /** Sets the maximum height of the textarea as determined by maxRows. */\n    _setMaxHeight() {\n      const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n      if (maxHeight) {\n        this._textareaElement.style.maxHeight = maxHeight;\n      }\n    }\n    ngAfterViewInit() {\n      if (this._platform.isBrowser) {\n        // Remember the height which we started with in case autosizing is disabled\n        this._initialHeight = this._textareaElement.style.height;\n        this.resizeToFitContent();\n        this._ngZone.runOutsideAngular(() => {\n          const window = this._getWindow();\n          fromEvent(window, 'resize').pipe(auditTime(16), takeUntil(this._destroyed)).subscribe(() => this.resizeToFitContent(true));\n          this._textareaElement.addEventListener('focus', this._handleFocusEvent);\n          this._textareaElement.addEventListener('blur', this._handleFocusEvent);\n        });\n        this._isViewInited = true;\n        this.resizeToFitContent(true);\n      }\n    }\n    ngOnDestroy() {\n      this._textareaElement.removeEventListener('focus', this._handleFocusEvent);\n      this._textareaElement.removeEventListener('blur', this._handleFocusEvent);\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /**\n     * Cache the height of a single-row textarea if it has not already been cached.\n     *\n     * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n     * maxRows. For the initial version, we will assume that the height of a single line in the\n     * textarea does not ever change.\n     */\n    _cacheTextareaLineHeight() {\n      if (this._cachedLineHeight) {\n        return;\n      }\n      // Use a clone element because we have to override some styles.\n      let textareaClone = this._textareaElement.cloneNode(false);\n      textareaClone.rows = 1;\n      // Use `position: absolute` so that this doesn't cause a browser layout and use\n      // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n      // would affect the height.\n      textareaClone.style.position = 'absolute';\n      textareaClone.style.visibility = 'hidden';\n      textareaClone.style.border = 'none';\n      textareaClone.style.padding = '0';\n      textareaClone.style.height = '';\n      textareaClone.style.minHeight = '';\n      textareaClone.style.maxHeight = '';\n      // In Firefox it happens that textarea elements are always bigger than the specified amount\n      // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n      // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n      // to hidden. This ensures that there is no invalid calculation of the line height.\n      // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n      textareaClone.style.overflow = 'hidden';\n      this._textareaElement.parentNode.appendChild(textareaClone);\n      this._cachedLineHeight = textareaClone.clientHeight;\n      textareaClone.remove();\n      // Min and max heights have to be re-calculated if the cached line height changes\n      this._setMinHeight();\n      this._setMaxHeight();\n    }\n    _measureScrollHeight() {\n      const element = this._textareaElement;\n      const previousMargin = element.style.marginBottom || '';\n      const isFirefox = this._platform.FIREFOX;\n      const needsMarginFiller = isFirefox && this._hasFocus;\n      const measuringClass = isFirefox ? 'cdk-textarea-autosize-measuring-firefox' : 'cdk-textarea-autosize-measuring';\n      // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n      // work around it by assigning a temporary margin with the same height as the `textarea` so that\n      // it occupies the same amount of space. See #23233.\n      if (needsMarginFiller) {\n        element.style.marginBottom = `${element.clientHeight}px`;\n      }\n      // Reset the textarea height to auto in order to shrink back to its default size.\n      // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n      element.classList.add(measuringClass);\n      // The measuring class includes a 2px padding to workaround an issue with Chrome,\n      // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n      const scrollHeight = element.scrollHeight - 4;\n      element.classList.remove(measuringClass);\n      if (needsMarginFiller) {\n        element.style.marginBottom = previousMargin;\n      }\n      return scrollHeight;\n    }\n    _cacheTextareaPlaceholderHeight() {\n      if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n        return;\n      }\n      if (!this.placeholder) {\n        this._cachedPlaceholderHeight = 0;\n        return;\n      }\n      const value = this._textareaElement.value;\n      this._textareaElement.value = this._textareaElement.placeholder;\n      this._cachedPlaceholderHeight = this._measureScrollHeight();\n      this._textareaElement.value = value;\n    }\n    ngDoCheck() {\n      if (this._platform.isBrowser) {\n        this.resizeToFitContent();\n      }\n    }\n    /**\n     * Resize the textarea to fit its content.\n     * @param force Whether to force a height recalculation. By default the height will be\n     *    recalculated only if the value changed since the last call.\n     */\n    resizeToFitContent(force = false) {\n      // If autosizing is disabled, just skip everything else\n      if (!this._enabled) {\n        return;\n      }\n      this._cacheTextareaLineHeight();\n      this._cacheTextareaPlaceholderHeight();\n      // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n      // in checking the height of the textarea.\n      if (!this._cachedLineHeight) {\n        return;\n      }\n      const textarea = this._elementRef.nativeElement;\n      const value = textarea.value;\n      // Only resize if the value or minRows have changed since these calculations can be expensive.\n      if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n        return;\n      }\n      const scrollHeight = this._measureScrollHeight();\n      const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n      // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n      textarea.style.height = `${height}px`;\n      this._ngZone.runOutsideAngular(() => {\n        if (typeof requestAnimationFrame !== 'undefined') {\n          requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n        } else {\n          setTimeout(() => this._scrollToCaretPosition(textarea));\n        }\n      });\n      this._previousValue = value;\n      this._previousMinRows = this._minRows;\n    }\n    /**\n     * Resets the textarea to its original size\n     */\n    reset() {\n      // Do not try to change the textarea, if the initialHeight has not been determined yet\n      // This might potentially remove styles when reset() is called before ngAfterViewInit\n      if (this._initialHeight !== undefined) {\n        this._textareaElement.style.height = this._initialHeight;\n      }\n    }\n    _noopInputHandler() {\n      // no-op handler that ensures we're running change detection on input events.\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n      return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n      const doc = this._getDocument();\n      return doc.defaultView || window;\n    }\n    /**\n     * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n     * prevent it from scrolling to the caret position. We need to re-set the selection\n     * in order for it to scroll to the proper position.\n     */\n    _scrollToCaretPosition(textarea) {\n      const {\n        selectionStart,\n        selectionEnd\n      } = textarea;\n      // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n      // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n      // between the time we requested the animation frame and when it was executed.\n      // Also note that we have to assert that the textarea is focused before we set the\n      // selection range. Setting the selection range on a non-focused textarea will cause\n      // it to receive focus on IE and Edge.\n      if (!this._destroyed.isStopped && this._hasFocus) {\n        textarea.setSelectionRange(selectionStart, selectionEnd);\n      }\n    }\n    static {\n      this.ɵfac = function CdkTextareaAutosize_Factory(t) {\n        return new (t || CdkTextareaAutosize)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(DOCUMENT, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkTextareaAutosize,\n        selectors: [[\"textarea\", \"cdkTextareaAutosize\", \"\"]],\n        hostAttrs: [\"rows\", \"1\", 1, \"cdk-textarea-autosize\"],\n        hostBindings: function CdkTextareaAutosize_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"input\", function CdkTextareaAutosize_input_HostBindingHandler() {\n              return ctx._noopInputHandler();\n            });\n          }\n        },\n        inputs: {\n          minRows: [\"cdkAutosizeMinRows\", \"minRows\"],\n          maxRows: [\"cdkAutosizeMaxRows\", \"maxRows\"],\n          enabled: [\"cdkTextareaAutosize\", \"enabled\"],\n          placeholder: \"placeholder\"\n        },\n        exportAs: [\"cdkTextareaAutosize\"]\n      });\n    }\n  }\n  return CdkTextareaAutosize;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TextFieldModule = /*#__PURE__*/(() => {\n  class TextFieldModule {\n    static {\n      this.ɵfac = function TextFieldModule_Factory(t) {\n        return new (t || TextFieldModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: TextFieldModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n    }\n  }\n  return TextFieldModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };\n//# sourceMappingURL=text-field.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}