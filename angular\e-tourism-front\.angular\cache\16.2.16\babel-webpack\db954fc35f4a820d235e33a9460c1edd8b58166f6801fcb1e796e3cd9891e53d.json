{"ast": null, "code": "import { createOperatorSubscriber } from './OperatorSubscriber';\nexport function scanInternals(accumulator, seed, hasSeed, emitOnNext, emitBeforeComplete) {\n  return (source, subscriber) => {\n    let hasState = hasSeed;\n    let state = seed;\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const i = index++;\n      state = hasState ? accumulator(state, value, i) : (hasState = true, value);\n      emitOnNext && subscriber.next(state);\n    }, emitBeforeComplete && (() => {\n      hasState && subscriber.next(state);\n      subscriber.complete();\n    })));\n  };\n}\n//# sourceMappingURL=scanInternals.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}