// Import les modèles principaux
import { BookingTransactionRequest } from './booking-transaction-request.model';
import { BookingTransactionResponse } from './booking-transaction-response.model';

// Import les modèles de requête
import { BeginTransactionRequest } from './begin-transaction-request.model';
import { SetReservationInfoRequest } from './set-reservation-info-request.model';
import { CommitTransactionRequest } from './commit-transaction-request.model';

// Import les modèles de réponse
import { BeginTransactionResponse } from './begin-transaction-response.model';
import { SetReservationInfoResponse } from './set-reservation-info-response.model';
import { CommitTransactionResponse } from './commit-transaction-response.model';

// Import les modèles imbriqués de begin-transaction-request
import {
  // Pas de modèles imbriqués spécifiques à exporter
} from './begin-transaction-request.model';

// Import les modèles imbriqués de set-reservation-info-request
import {
  Traveller as TravellerRequest,
  AcademicTitle as AcademicTitleRequest,
  Nationality as NationalityRequest,
  PassportInfo as PassportInfoRequest,
  Address as AddressRequest,
  ContactPhone as ContactPhoneRequest,
  City as CityRequest,
  Country as CountryRequest,
  DestinationAddress,
  Document,
  InsertField,
  CustomerInfo,
  TaxInfo
} from './set-reservation-info-request.model';

// Import les modèles imbriqués de commit-transaction-request
import {
  PaymentInformation,
  VCCInformation,
  Price as PriceRequest
} from './commit-transaction-request.model';

// Import les modèles imbriqués de begin-transaction-response
import {
  Header as HeaderBeginResponse,
  Message as MessageBeginResponse,
  Body as BodyBeginResponse,
  ReservationData as ReservationDataBeginResponse,
  Traveller as TravellerBeginResponse,
  Nationality as NationalityBeginResponse,
  PassportInfo as PassportInfoBeginResponse,
  Address as AddressBeginResponse,
  ContactPhone as ContactPhoneBeginResponse,
  City as CityBeginResponse,
  Country as CountryBeginResponse,
  ReservationInfo as ReservationInfoBeginResponse,
  Agency as AgencyBeginResponse,
  AgencyUser as AgencyUserBeginResponse,
  Price as PriceBeginResponse,
  Commission as CommissionBeginResponse,
  Service as ServiceBeginResponse,
  ServiceDetails as ServiceDetailsBeginResponse,
  PaymentDetail as PaymentDetailBeginResponse,
  Invoice as InvoiceBeginResponse
} from './begin-transaction-response.model';

// Import les modèles imbriqués de set-reservation-info-response
import {
  Header as HeaderInfoResponse,
  Message as MessageInfoResponse,
  Body as BodyInfoResponse,
  ReservationData as ReservationDataInfoResponse,
  Traveller as TravellerInfoResponse,
  Title,
  AcademicTitle as AcademicTitleInfoResponse,
  Nationality as NationalityInfoResponse,
  PassportInfo as PassportInfoInfoResponse,
  Address as AddressInfoResponse,
  ContactPhone as ContactPhoneInfoResponse,
  City as CityInfoResponse,
  Country as CountryInfoResponse,
  Document as DocumentInfoResponse,
  ReservationInfo as ReservationInfoInfoResponse,
  Agency as AgencyInfoResponse,
  AgencyUser as AgencyUserInfoResponse,
  Price as PriceInfoResponse,
  Commission as CommissionInfoResponse,
  Service as ServiceInfoResponse,
  ServiceDetails as ServiceDetailsInfoResponse,
  PaymentDetail as PaymentDetailInfoResponse,
  Invoice as InvoiceInfoResponse
} from './set-reservation-info-response.model';

// Import les modèles imbriqués de commit-transaction-response
import {
  Header as HeaderCommitResponse,
  Message as MessageCommitResponse,
  Body as BodyCommitResponse
} from './commit-transaction-response.model';

// Export les modèles principaux
export {
  // Modèles principaux
  BookingTransactionRequest,
  BookingTransactionResponse,

  // Modèles de requête
  BeginTransactionRequest,
  SetReservationInfoRequest,
  CommitTransactionRequest,

  // Modèles de réponse
  BeginTransactionResponse,
  SetReservationInfoResponse,
  CommitTransactionResponse,

  // Modèles imbriqués de set-reservation-info-request
  TravellerRequest,
  AcademicTitleRequest,
  NationalityRequest,
  PassportInfoRequest,
  AddressRequest,
  ContactPhoneRequest,
  CityRequest,
  CountryRequest,
  DestinationAddress,
  Document,
  InsertField,
  CustomerInfo,
  TaxInfo,

  // Modèles imbriqués de commit-transaction-request
  PaymentInformation,
  VCCInformation,
  PriceRequest,

  // Modèles imbriqués de begin-transaction-response
  HeaderBeginResponse,
  MessageBeginResponse,
  BodyBeginResponse,
  ReservationDataBeginResponse,
  TravellerBeginResponse,
  NationalityBeginResponse,
  PassportInfoBeginResponse,
  AddressBeginResponse,
  ContactPhoneBeginResponse,
  CityBeginResponse,
  CountryBeginResponse,
  ReservationInfoBeginResponse,
  AgencyBeginResponse,
  AgencyUserBeginResponse,
  PriceBeginResponse,
  CommissionBeginResponse,
  ServiceBeginResponse,
  ServiceDetailsBeginResponse,
  PaymentDetailBeginResponse,
  InvoiceBeginResponse,

  // Modèles imbriqués de set-reservation-info-response
  HeaderInfoResponse,
  MessageInfoResponse,
  BodyInfoResponse,
  ReservationDataInfoResponse,
  TravellerInfoResponse,
  Title,
  AcademicTitleInfoResponse,
  NationalityInfoResponse,
  PassportInfoInfoResponse,
  AddressInfoResponse,
  ContactPhoneInfoResponse,
  CityInfoResponse,
  CountryInfoResponse,
  DocumentInfoResponse,
  ReservationInfoInfoResponse,
  AgencyInfoResponse,
  AgencyUserInfoResponse,
  PriceInfoResponse,
  CommissionInfoResponse,
  ServiceInfoResponse,
  ServiceDetailsInfoResponse,
  PaymentDetailInfoResponse,
  InvoiceInfoResponse,

  // Modèles imbriqués de commit-transaction-response
  HeaderCommitResponse,
  MessageCommitResponse,
  BodyCommitResponse
};
