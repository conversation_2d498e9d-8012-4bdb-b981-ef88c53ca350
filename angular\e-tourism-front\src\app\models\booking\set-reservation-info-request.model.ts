/**
 * Model for Set Reservation Info Request
 * Used to set traveler and customer information for a reservation
 */
export interface SetReservationInfoRequest {
    transactionId: string;
    travellers: Traveller[];
    customerInfo?: CustomerInfo;
    reservationNote?: string;
    agencyReservationNumber?: string;
}

export interface Traveller {
    travellerId?: string;
    type: number;
    title: number;
    academicTitle?: AcademicTitle;
    passengerType: number;
    name: string;
    surname: string;
    isLeader: boolean;
    birthDate: string;
    nationality: Nationality;
    identityNumber?: string;
    passportInfo?: PassportInfo;
    address?: Address;
    destinationAddress?: DestinationAddress;
    orderNumber?: number;
    documents?: Document[];
    insertFields?: InsertField[];
    status?: number;
    gender: number;
}

export interface AcademicTitle {
    id: number;
}

export interface Nationality {
    twoLetterCode: string;
}

export interface PassportInfo {
    serial?: string;
    number: string;
    expireDate: string;
    issueDate: string;
    citizenshipCountryCode: string;
    issueCountryCode: string;
}

export interface Address {
    contactPhone?: ContactPhone;
    email?: string;
    address?: string;
    zipCode?: string;
    city?: City;
    country?: Country;
}

export interface ContactPhone {
    countryCode: string;
    areaCode?: string;
    phoneNumber: string;
}

export interface City {
    id: string;
    name: string;
}

export interface Country {
    id: string;
    name: string;
}

export interface DestinationAddress {
    contactPhone?: ContactPhone;
    email?: string;
    address?: string;
    zipCode?: string;
    city?: City;
    country?: Country;
}

export interface Document {
    // Can be extended as needed
}

export interface InsertField {
    // Can be extended as needed
}

export interface CustomerInfo {
    isCompany: boolean;
    passportInfo?: PassportInfo;
    address?: Address;
    taxInfo?: TaxInfo;
    title: number;
    name: string;
    surname: string;
    birthDate?: string;
    identityNumber?: string;
}

export interface TaxInfo {
    taxOffice: string;
    taxNumber: string;
}
