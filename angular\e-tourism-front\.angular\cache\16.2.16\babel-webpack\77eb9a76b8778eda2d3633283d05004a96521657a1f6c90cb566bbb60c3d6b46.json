{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function buffer(closingNotifier) {\n  return operate((source, subscriber) => {\n    let currentBuffer = [];\n    source.subscribe(createOperatorSubscriber(subscriber, value => currentBuffer.push(value), () => {\n      subscriber.next(currentBuffer);\n      subscriber.complete();\n    }));\n    innerFrom(closingNotifier).subscribe(createOperatorSubscriber(subscriber, () => {\n      const b = currentBuffer;\n      currentBuffer = [];\n      subscriber.next(b);\n    }, noop));\n    return () => {\n      currentBuffer = null;\n    };\n  });\n}\n//# sourceMappingURL=buffer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}