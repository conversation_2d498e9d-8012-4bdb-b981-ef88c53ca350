package com.paximum.demo.models;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RoundTripRequest {
    @JsonProperty("ProductType")
    private int productType;

    @JsonProperty("ServiceTypes")
    private List<String> serviceTypes;

    @JsonProperty("DepartureLocations")
    private List<Location> departureLocations;

    @JsonProperty("ArrivalLocations")
    private List<Location> arrivalLocations;

    @JsonProperty("CheckIn")
    private String checkIn;

    @JsonProperty("Night")
    private int night;

    @JsonProperty("ReturnDate")
    private String returnDate; // Ajout du champ returnDate pour les vols aller-retour

    @JsonProperty("Passengers")
    private List<Passenger> passengers;

    @JsonProperty("acceptPendingProviders")
    private boolean acceptPendingProviders;

    @JsonProperty("forceFlightBundlePackage")
    private boolean forceFlightBundlePackage;

    @JsonProperty("disablePackageOfferTotalPrice")
    private boolean disablePackageOfferTotalPrice;

    @JsonProperty("supportedFlightResponseListTypes")
    private List<Integer> supportedFlightResponseListTypes;

    @JsonProperty("showOnlyNonStopFlight")
    private boolean showOnlyNonStopFlight;

    @JsonProperty("additionalParameters")
    private AdditionalParameters additionalParameters;

    @JsonProperty("calculateFlightFees")
    private boolean calculateFlightFees;

    @JsonProperty("Culture")
    private String culture;

    @JsonProperty("Currency")
    private String currency;

    // Nested classes
    public static class Location {
        @JsonProperty("id")
        private String id;

        @JsonProperty("type")
        private int type;

        @JsonProperty("provider")
        private int provider;

        // Getters and setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public int getType() { return type; }
        public void setType(int type) { this.type = type; }
        public int getProvider() { return provider; }
        public void setProvider(int provider) { this.provider = provider; }
    }

    public static class Passenger {
        @JsonProperty("type")
        private int type;

        @JsonProperty("count")
        private int count;

        // Getters and setters
        public int getType() { return type; }
        public void setType(int type) { this.type = type; }
        public int getCount() { return count; }
        public void setCount(int count) { this.count = count; }
    }

    public static class AdditionalParameters {
        @JsonProperty("getOptionsParameters")
        private GetOptionsParameters getOptionsParameters;

        @JsonProperty("corporateCodes")
        private List<CorporateCode> corporateCodes;

        // Getters and setters
        public GetOptionsParameters getGetOptionsParameters() { return getOptionsParameters; }
        public void setGetOptionsParameters(GetOptionsParameters getOptionsParameters) { this.getOptionsParameters = getOptionsParameters; }
        public List<CorporateCode> getCorporateCodes() { return corporateCodes; }
        public void setCorporateCodes(List<CorporateCode> corporateCodes) { this.corporateCodes = corporateCodes; }
    }

    public static class GetOptionsParameters {
        @JsonProperty("flightBaggageGetOption")
        private int flightBaggageGetOption;

        // Getters and setters
        public int getFlightBaggageGetOption() { return flightBaggageGetOption; }
        public void setFlightBaggageGetOption(int flightBaggageGetOption) { this.flightBaggageGetOption = flightBaggageGetOption; }
    }

    public static class CorporateCode {
        @JsonProperty("code")
        private String code;

        @JsonProperty("rule")
        private Rule rule;

        // Getters and setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public Rule getRule() { return rule; }
        public void setRule(Rule rule) { this.rule = rule; }
    }

    public static class Rule {
        @JsonProperty("airline")
        private String airline;

        @JsonProperty("supplier")
        private String supplier;

        // Getters and setters
        public String getAirline() { return airline; }
        public void setAirline(String airline) { this.airline = airline; }
        public String getSupplier() { return supplier; }
        public void setSupplier(String supplier) { this.supplier = supplier; }
    }

    // Getters and setters
    public int getProductType() { return productType; }
    public void setProductType(int productType) { this.productType = productType; }
    public List<String> getServiceTypes() { return serviceTypes; }
    public void setServiceTypes(List<String> serviceTypes) { this.serviceTypes = serviceTypes; }
    public List<Location> getDepartureLocations() { return departureLocations; }
    public void setDepartureLocations(List<Location> departureLocations) { this.departureLocations = departureLocations; }
    public List<Location> getArrivalLocations() { return arrivalLocations; }
    public void setArrivalLocations(List<Location> arrivalLocations) { this.arrivalLocations = arrivalLocations; }
    public String getCheckIn() { return checkIn; }
    public void setCheckIn(String checkIn) { this.checkIn = checkIn; }
    public int getNight() { return night; }
    public void setNight(int night) { this.night = night; }
    public String getReturnDate() { return returnDate; }
    public void setReturnDate(String returnDate) { this.returnDate = returnDate; }
    public List<Passenger> getPassengers() { return passengers; }
    public void setPassengers(List<Passenger> passengers) { this.passengers = passengers; }
    public boolean isAcceptPendingProviders() { return acceptPendingProviders; }
    public void setAcceptPendingProviders(boolean acceptPendingProviders) { this.acceptPendingProviders = acceptPendingProviders; }
    public boolean isForceFlightBundlePackage() { return forceFlightBundlePackage; }
    public void setForceFlightBundlePackage(boolean forceFlightBundlePackage) { this.forceFlightBundlePackage = forceFlightBundlePackage; }
    public boolean isDisablePackageOfferTotalPrice() { return disablePackageOfferTotalPrice; }
    public void setDisablePackageOfferTotalPrice(boolean disablePackageOfferTotalPrice) { this.disablePackageOfferTotalPrice = disablePackageOfferTotalPrice; }
    public List<Integer> getSupportedFlightResponseListTypes() { return supportedFlightResponseListTypes; }
    public void setSupportedFlightResponseListTypes(List<Integer> supportedFlightResponseListTypes) { this.supportedFlightResponseListTypes = supportedFlightResponseListTypes; }
    public boolean isShowOnlyNonStopFlight() { return showOnlyNonStopFlight; }
    public void setShowOnlyNonStopFlight(boolean showOnlyNonStopFlight) { this.showOnlyNonStopFlight = showOnlyNonStopFlight; }
    public AdditionalParameters getAdditionalParameters() { return additionalParameters; }
    public void setAdditionalParameters(AdditionalParameters additionalParameters) { this.additionalParameters = additionalParameters; }
    public boolean isCalculateFlightFees() { return calculateFlightFees; }
    public void setCalculateFlightFees(boolean calculateFlightFees) { this.calculateFlightFees = calculateFlightFees; }
    public String getCulture() { return culture; }
    public void setCulture(String culture) { this.culture = culture; }
    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }
}
