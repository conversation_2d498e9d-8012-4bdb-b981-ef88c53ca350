{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/product.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../services/shared-data.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/autocomplete\";\nimport * as i7 from \"@angular/material/core\";\nfunction SearchPriceComponent_div_41_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"12+ years\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_41_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"2-11 years\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_41_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"0-23 months\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"div\", 99);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 100);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_41_span_5_Template, 2, 0, \"span\", 66);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_41_span_6_Template, 2, 0, \"span\", 66);\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_41_span_7_Template, 2, 0, \"span\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 101)(9, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_41_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const type_r14 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.decreasePassengerCount(type_r14.value));\n    });\n    i0.ɵɵelement(10, \"i\", 103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 104);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_41_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const type_r14 = restoredCtx.$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.increasePassengerCount(type_r14.value));\n    });\n    i0.ɵɵelement(14, \"i\", 106);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const type_r14 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(type_r14.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", type_r14.value === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", type_r14.value === 2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", type_r14.value === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", type_r14.value === 1 && ctx_r0.getPassengerCount(type_r14.value) <= 1 || ctx_r0.getPassengerCount(type_r14.value) <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getPassengerCount(type_r14.value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.getTotalPassengers() >= 9);\n  }\n}\nfunction SearchPriceComponent_option_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", flightClass_r21.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(flightClass_r21.label);\n  }\n}\nfunction SearchPriceComponent_mat_option_71_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 114);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r22.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_71_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 115);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r22.city);\n  }\n}\nconst _c0 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"fa-flag\": a0,\n    \"fa-city\": a1,\n    \"fa-building\": a2,\n    \"fa-home\": a3,\n    \"fa-plane\": a4\n  };\n};\nfunction SearchPriceComponent_mat_option_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 85)(1, \"div\", 107)(2, \"div\", 108);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 109);\n    i0.ɵɵtemplate(5, SearchPriceComponent_mat_option_71_span_5_Template, 2, 1, \"span\", 110);\n    i0.ɵɵtemplate(6, SearchPriceComponent_mat_option_71_span_6_Template, 2, 1, \"span\", 111);\n    i0.ɵɵelementStart(7, \"span\", 112);\n    i0.ɵɵelement(8, \"i\", 113);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r22);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r22.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r22.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r22.type === 5 && location_r22.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r22.type === 1, location_r22.type === 2, location_r22.type === 3, location_r22.type === 4, location_r22.type === 5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r22.type === 1 ? \"Country\" : location_r22.type === 2 ? \"City\" : location_r22.type === 3 ? \"Town\" : location_r22.type === 4 ? \"Village\" : location_r22.type === 5 ? \"Airport\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵelement(1, \"i\", 117);\n    i0.ɵɵtext(2, \" Please select a departure location \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_mat_option_84_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 114);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r27.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_84_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 115);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r27.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 85)(1, \"div\", 107)(2, \"div\", 108);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 109);\n    i0.ɵɵtemplate(5, SearchPriceComponent_mat_option_84_span_5_Template, 2, 1, \"span\", 110);\n    i0.ɵɵtemplate(6, SearchPriceComponent_mat_option_84_span_6_Template, 2, 1, \"span\", 111);\n    i0.ɵɵelementStart(7, \"span\", 112);\n    i0.ɵɵelement(8, \"i\", 113);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r27 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r27);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r27.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r27.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r27.type === 5 && location_r27.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r27.type === 1, location_r27.type === 2, location_r27.type === 3, location_r27.type === 4, location_r27.type === 5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r27.type === 1 ? \"Country\" : location_r27.type === 2 ? \"City\" : location_r27.type === 3 ? \"Town\" : location_r27.type === 4 ? \"Village\" : location_r27.type === 5 ? \"Airport\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵelement(1, \"i\", 117);\n    i0.ɵɵtext(2, \" Please select an arrival location \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵelement(1, \"i\", 117);\n    i0.ɵɵtext(2, \" Please select a departure date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_94_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵelement(1, \"i\", 117);\n    i0.ɵɵtext(2, \" Please select a return date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\", 118);\n    i0.ɵɵtext(2, \"Return\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33);\n    i0.ɵɵelement(4, \"i\", 60)(5, \"input\", 119);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_94_div_6_Template, 3, 0, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"min\", ((tmp_0_0 = ctx_r9.searchForm.get(\"departureDate\")) == null ? null : tmp_0_0.value) || ctx_r9.minDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r9.searchForm.get(\"returnDate\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r9.searchForm.get(\"returnDate\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction SearchPriceComponent_i_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 120);\n  }\n}\nfunction SearchPriceComponent_span_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Search\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121);\n    i0.ɵɵelement(1, \"div\", 122);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_168_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127)(1, \"div\", 128)(2, \"div\", 129);\n    i0.ɵɵelement(3, \"i\", 130)(4, \"div\", 131)(5, \"div\", 131)(6, \"div\", 131);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Searching for the best flights...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SearchPriceComponent_div_168_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"div\", 133);\n    i0.ɵɵelement(2, \"i\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Oops! Something went wrong\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 116);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_168_div_2_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.onSearch());\n    });\n    i0.ɵɵelement(8, \"i\", 135);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r34.errorMessage);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No flights found for your search. Please modify your criteria.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_p_6_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 147);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r44.currentFilter, \")\");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"span\", 145);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" flights found \");\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_168_div_3_p_6_span_4_Template, 2, 1, \"span\", 146);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r39.filteredResults.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.currentFilter !== \"recommended\");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_7_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 151);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_168_div_3_div_7_button_2_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r48);\n      const filter_r46 = restoredCtx.$implicit;\n      const ctx_r47 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r47.applyFilter(filter_r46.value));\n    });\n    i0.ɵɵelement(1, \"i\", 113);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r46 = ctx.$implicit;\n    const ctx_r45 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"active\", ctx_r45.currentFilter === filter_r46.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", filter_r46.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", filter_r46.label, \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148)(1, \"div\", 149);\n    i0.ɵɵtemplate(2, SearchPriceComponent_div_168_div_3_div_7_button_2_Template, 3, 4, \"button\", 150);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r40.filterOptions);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r49 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r49.formatPrice(ctx_r49.filterPrices.stops.direct));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r50 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r50.formatPrice(ctx_r50.filterPrices.stops.oneStop));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r51.formatPrice(ctx_r51.filterPrices.stops.multiStop));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_span_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r52.formatPrice(ctx_r52.filterPrices.departureTime.earlyMorning));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_span_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r53.formatPrice(ctx_r53.filterPrices.departureTime.morning));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_span_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r54.formatPrice(ctx_r54.filterPrices.departureTime.afternoon));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_span_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r55.formatPrice(ctx_r55.filterPrices.departureTime.evening));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_span_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r56.formatPrice(ctx_r56.filterPrices.arrivalTime.earlyMorning));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_span_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r57 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r57.formatPrice(ctx_r57.filterPrices.arrivalTime.morning));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_span_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r58 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r58.formatPrice(ctx_r58.filterPrices.arrivalTime.afternoon));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_span_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r59.formatPrice(ctx_r59.filterPrices.arrivalTime.evening));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_div_105_div_6_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const airline_r62 = i0.ɵɵnextContext().$implicit;\n    const ctx_r63 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r63.formatPrice(ctx_r63.filterPrices.airlines[airline_r62]));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_div_105_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 158)(1, \"label\", 159)(2, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_div_105_div_6_Template_input_change_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r66);\n      const airline_r62 = restoredCtx.$implicit;\n      const ctx_r65 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r65.toggleAirlineFilter(airline_r62));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 161);\n    i0.ɵɵelementStart(4, \"span\", 162);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_168_div_3_div_9_div_105_div_6_span_6_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const airline_r62 = ctx.$implicit;\n    const ctx_r61 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r61.sidebarFilters.airlines[airline_r62]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(airline_r62);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r61.filterPrices.airlines[airline_r62] > 0);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r68 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 155)(1, \"div\", 156);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_168_div_3_div_9_div_105_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r67 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r67.toggleSection(\"airlines\"));\n    });\n    i0.ɵɵelementStart(2, \"h4\");\n    i0.ɵɵtext(3, \"Compagnies a\\u00E9riennes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 113);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 157);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_168_div_3_div_9_div_105_div_6_Template, 7, 3, \"div\", 167);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r60 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r60.expandedSections[\"airlines\"] ? \"fa-chevron-up\" : \"fa-chevron-down\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"expanded\", ctx_r60.expandedSections[\"airlines\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r60.getAirlineKeys());\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r70 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 152)(1, \"div\", 153)(2, \"h3\");\n    i0.ɵɵtext(3, \"Filtres\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 154);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_168_div_3_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r69 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r69.clearAllFilters());\n    });\n    i0.ɵɵtext(5, \"Tout effacer\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 155)(7, \"div\", 156);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_168_div_3_div_9_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r71 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r71.toggleSection(\"stops\"));\n    });\n    i0.ɵɵelementStart(8, \"h4\");\n    i0.ɵɵtext(9, \"Escales\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"i\", 113);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 157)(12, \"div\", 158)(13, \"label\", 159)(14, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_Template_input_change_14_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r72 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r72.toggleStopFilter(\"direct\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"span\", 161);\n    i0.ɵɵelementStart(16, \"span\", 162);\n    i0.ɵɵtext(17, \"Direct\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, SearchPriceComponent_div_168_div_3_div_9_span_18_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 158)(20, \"label\", 159)(21, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_Template_input_change_21_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r73 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r73.toggleStopFilter(\"oneStop\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"span\", 161);\n    i0.ɵɵelementStart(23, \"span\", 162);\n    i0.ɵɵtext(24, \"1 escale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, SearchPriceComponent_div_168_div_3_div_9_span_25_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 158)(27, \"label\", 159)(28, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_Template_input_change_28_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r74 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r74.toggleStopFilter(\"multiStop\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"span\", 161);\n    i0.ɵɵelementStart(30, \"span\", 162);\n    i0.ɵɵtext(31, \"2 escales ou plus\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, SearchPriceComponent_div_168_div_3_div_9_span_32_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(33, \"div\", 155)(34, \"div\", 156);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_168_div_3_div_9_Template_div_click_34_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r75 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r75.toggleSection(\"departureTime\"));\n    });\n    i0.ɵɵelementStart(35, \"h4\");\n    i0.ɵɵtext(36, \"Horaires de d\\u00E9part\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"i\", 113);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 157)(39, \"div\", 164);\n    i0.ɵɵtext(40, \"D\\u00E9part\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 158)(42, \"label\", 159)(43, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_Template_input_change_43_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r76 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r76.toggleDepartureTimeFilter(\"earlyMorning\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"span\", 161);\n    i0.ɵɵelementStart(45, \"span\", 162);\n    i0.ɵɵtext(46, \"00:00 - 08:00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, SearchPriceComponent_div_168_div_3_div_9_span_47_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 158)(49, \"label\", 159)(50, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_Template_input_change_50_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r77 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r77.toggleDepartureTimeFilter(\"morning\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"span\", 161);\n    i0.ɵɵelementStart(52, \"span\", 162);\n    i0.ɵɵtext(53, \"08:00 - 12:00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, SearchPriceComponent_div_168_div_3_div_9_span_54_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 158)(56, \"label\", 159)(57, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_Template_input_change_57_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r78 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r78.toggleDepartureTimeFilter(\"afternoon\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(58, \"span\", 161);\n    i0.ɵɵelementStart(59, \"span\", 162);\n    i0.ɵɵtext(60, \"12:00 - 18:00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(61, SearchPriceComponent_div_168_div_3_div_9_span_61_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 158)(63, \"label\", 159)(64, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_Template_input_change_64_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r79 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r79.toggleDepartureTimeFilter(\"evening\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(65, \"span\", 161);\n    i0.ɵɵelementStart(66, \"span\", 162);\n    i0.ɵɵtext(67, \"18:00 - 00:00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(68, SearchPriceComponent_div_168_div_3_div_9_span_68_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(69, \"div\", 155)(70, \"div\", 156);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_168_div_3_div_9_Template_div_click_70_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r80 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r80.toggleSection(\"arrivalTime\"));\n    });\n    i0.ɵɵelementStart(71, \"h4\");\n    i0.ɵɵtext(72, \"Horaires d'arriv\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(73, \"i\", 113);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"div\", 157)(75, \"div\", 164);\n    i0.ɵɵtext(76, \"D\\u00E9part\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 158)(78, \"label\", 159)(79, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_Template_input_change_79_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r81 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r81.toggleArrivalTimeFilter(\"earlyMorning\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(80, \"span\", 161);\n    i0.ɵɵelementStart(81, \"span\", 162);\n    i0.ɵɵtext(82, \"00:00 - 08:00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(83, SearchPriceComponent_div_168_div_3_div_9_span_83_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(84, \"div\", 158)(85, \"label\", 159)(86, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_Template_input_change_86_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r82 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r82.toggleArrivalTimeFilter(\"morning\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(87, \"span\", 161);\n    i0.ɵɵelementStart(88, \"span\", 162);\n    i0.ɵɵtext(89, \"08:00 - 12:00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(90, SearchPriceComponent_div_168_div_3_div_9_span_90_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(91, \"div\", 158)(92, \"label\", 159)(93, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_Template_input_change_93_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r83 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r83.toggleArrivalTimeFilter(\"afternoon\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(94, \"span\", 161);\n    i0.ɵɵelementStart(95, \"span\", 162);\n    i0.ɵɵtext(96, \"12:00 - 18:00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(97, SearchPriceComponent_div_168_div_3_div_9_span_97_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(98, \"div\", 158)(99, \"label\", 159)(100, \"input\", 160);\n    i0.ɵɵlistener(\"change\", function SearchPriceComponent_div_168_div_3_div_9_Template_input_change_100_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r84 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r84.toggleArrivalTimeFilter(\"evening\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(101, \"span\", 161);\n    i0.ɵɵelementStart(102, \"span\", 162);\n    i0.ɵɵtext(103, \"18:00 - 00:00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(104, SearchPriceComponent_div_168_div_3_div_9_span_104_Template, 2, 1, \"span\", 163);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(105, SearchPriceComponent_div_168_div_3_div_9_div_105_Template, 7, 4, \"div\", 165);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", ctx_r41.expandedSections[\"stops\"] ? \"fa-chevron-up\" : \"fa-chevron-down\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"expanded\", ctx_r41.expandedSections[\"stops\"]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r41.sidebarFilters.stops.direct);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.filterPrices.stops.direct > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r41.sidebarFilters.stops.oneStop);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.filterPrices.stops.oneStop > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r41.sidebarFilters.stops.multiStop);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.filterPrices.stops.multiStop > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r41.expandedSections[\"departureTime\"] ? \"fa-chevron-up\" : \"fa-chevron-down\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"expanded\", ctx_r41.expandedSections[\"departureTime\"]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"checked\", ctx_r41.sidebarFilters.departureTime.earlyMorning);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.filterPrices.departureTime.earlyMorning > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r41.sidebarFilters.departureTime.morning);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.filterPrices.departureTime.morning > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r41.sidebarFilters.departureTime.afternoon);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.filterPrices.departureTime.afternoon > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r41.sidebarFilters.departureTime.evening);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.filterPrices.departureTime.evening > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r41.expandedSections[\"arrivalTime\"] ? \"fa-chevron-up\" : \"fa-chevron-down\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"expanded\", ctx_r41.expandedSections[\"arrivalTime\"]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"checked\", ctx_r41.sidebarFilters.arrivalTime.earlyMorning);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.filterPrices.arrivalTime.earlyMorning > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r41.sidebarFilters.arrivalTime.morning);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.filterPrices.arrivalTime.morning > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r41.sidebarFilters.arrivalTime.afternoon);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.filterPrices.arrivalTime.afternoon > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r41.sidebarFilters.arrivalTime.evening);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.filterPrices.arrivalTime.evening > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.getAirlineKeys().length > 0);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 202);\n    i0.ɵɵelement(1, \"i\", 18);\n    i0.ɵɵtext(2, \" Round Trip \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 203);\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", flight_r85.items[0].airline.thumbnailFull, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 204);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 205);\n    i0.ɵɵelement(1, \"i\", 206);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r85.items && flight_r85.items[0] && flight_r85.items[0].flightProvider && flight_r85.items[0].flightProvider.displayName || flight_r85.items && flight_r85.items[0] && flight_r85.items[0].flightProvider && flight_r85.items[0].flightProvider.name || \"Provider \" + flight_r85.provider, \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 207);\n    i0.ɵɵelement(1, \"i\", 208);\n    i0.ɵɵtext(2, \" Direct \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 207);\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r85.items[0].flightClass.name, \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 209);\n    i0.ɵɵelement(1, \"i\", 210);\n    i0.ɵɵtext(2, \" Branded Fare \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"fa-check-circle\": a0,\n    \"fa-exclamation-triangle\": a1\n  };\n};\nfunction SearchPriceComponent_div_168_div_3_div_11_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 211);\n    i0.ɵɵelement(1, \"i\", 113);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    const ctx_r93 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c1, ctx_r93.isFlightAvailable(flight_r85), !ctx_r93.isFlightAvailable(flight_r85)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r93.isFlightAvailable(flight_r85) ? \"Available\" : \"Not available\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 212);\n    i0.ɵɵelement(1, \"i\", 213);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    const ctx_r94 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Expires: \", ctx_r94.formatExpirationDate(flight_r85.offers[0].expiresOn), \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_24_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 232)(1, \"span\", 233);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r85.items[0].stopCount);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" stop\", flight_r85.items[0].stopCount > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_24_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 234);\n    i0.ɵɵtext(1, \" Direct flight \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 214)(1, \"div\", 215)(2, \"div\", 216)(3, \"div\", 217);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 218)(6, \"span\", 219);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 220);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 221)(11, \"div\", 222);\n    i0.ɵɵelement(12, \"span\", 223);\n    i0.ɵɵelementStart(13, \"div\", 224);\n    i0.ɵɵelement(14, \"span\", 225);\n    i0.ɵɵelementStart(15, \"span\", 226);\n    i0.ɵɵelement(16, \"i\", 130);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"span\", 227);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 228);\n    i0.ɵɵelement(19, \"i\", 213);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, SearchPriceComponent_div_168_div_3_div_11_div_24_div_21_Template, 4, 2, \"div\", 229);\n    i0.ɵɵtemplate(22, SearchPriceComponent_div_168_div_3_div_11_div_24_div_22_Template, 2, 0, \"div\", 230);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 231)(24, \"div\", 217);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 218)(27, \"span\", 219);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 220);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    const ctx_r95 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(flight_r85.items && flight_r85.items[0] && flight_r85.items[0].departure ? ctx_r95.formatDate(flight_r85.items[0].departure.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r85.items && flight_r85.items[0] && flight_r85.items[0].departure && flight_r85.items[0].departure.airport ? flight_r85.items[0].departure.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r85.items && flight_r85.items[0] && flight_r85.items[0].departure && flight_r85.items[0].departure.city ? flight_r85.items[0].departure.city.name : \"N/A\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", flight_r85.items && flight_r85.items[0] ? ctx_r95.formatDuration(flight_r85.items[0].duration) : \"N/A\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.items && flight_r85.items[0] && flight_r85.items[0].stopCount > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.items && flight_r85.items[0] && flight_r85.items[0].stopCount === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r85.items && flight_r85.items[0] && flight_r85.items[0].arrival ? ctx_r95.formatDate(flight_r85.items[0].arrival.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r85.items && flight_r85.items[0] && flight_r85.items[0].arrival && flight_r85.items[0].arrival.airport ? flight_r85.items[0].arrival.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r85.items && flight_r85.items[0] && flight_r85.items[0].arrival && flight_r85.items[0].arrival.city ? flight_r85.items[0].arrival.city.name : \"N/A\");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_25_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 232)(1, \"span\", 233);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r112 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r112.getOutboundSegments(flight_r85).length - 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" stop\", ctx_r112.getOutboundSegments(flight_r85).length > 2 ? \"s\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_25_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 234);\n    i0.ɵɵtext(1, \" Direct flight \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_25_div_36_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 232)(1, \"span\", 233);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r117 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r117.getInboundSegments(flight_r85).length - 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" stop\", ctx_r117.getInboundSegments(flight_r85).length > 2 ? \"s\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_25_div_36_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 234);\n    i0.ɵɵtext(1, \" Direct flight \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_25_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 243)(1, \"div\", 238);\n    i0.ɵɵelement(2, \"i\", 56);\n    i0.ɵɵelementStart(3, \"span\", 239);\n    i0.ɵɵtext(4, \"Inbound Flight\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 215)(6, \"div\", 216);\n    i0.ɵɵelement(7, \"div\", 240);\n    i0.ɵɵelementStart(8, \"div\", 218)(9, \"span\", 219);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 220);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 221)(14, \"div\", 222);\n    i0.ɵɵelement(15, \"span\", 223);\n    i0.ɵɵelementStart(16, \"div\", 224);\n    i0.ɵɵelement(17, \"span\", 225);\n    i0.ɵɵelementStart(18, \"span\", 226);\n    i0.ɵɵelement(19, \"i\", 130);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(20, \"span\", 227);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 228);\n    i0.ɵɵelement(22, \"i\", 213);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SearchPriceComponent_div_168_div_3_div_11_div_25_div_36_div_24_Template, 4, 2, \"div\", 229);\n    i0.ɵɵtemplate(25, SearchPriceComponent_div_168_div_3_div_11_div_25_div_36_div_25_Template, 2, 0, \"div\", 230);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 231)(27, \"div\", 217);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 218)(30, \"span\", 219);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 220);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r114 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"innerHTML\", (ctx_r114.getInboundSegments(flight_r85)[0] == null ? null : ctx_r114.getInboundSegments(flight_r85)[0].departure) ? ctx_r114.formatDateWithRequestComparison(ctx_r114.getInboundSegments(flight_r85)[0].departure.date, (tmp_0_0 = ctx_r114.searchForm.get(\"returnDate\")) == null ? null : tmp_0_0.value) : \"N/A\", i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r114.getInboundSegments(flight_r85)[0] == null ? null : ctx_r114.getInboundSegments(flight_r85)[0].departure == null ? null : ctx_r114.getInboundSegments(flight_r85)[0].departure.airport == null ? null : ctx_r114.getInboundSegments(flight_r85)[0].departure.airport.code) || \"N/A\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r114.getInboundSegments(flight_r85)[0] == null ? null : ctx_r114.getInboundSegments(flight_r85)[0].departure == null ? null : ctx_r114.getInboundSegments(flight_r85)[0].departure.city == null ? null : ctx_r114.getInboundSegments(flight_r85)[0].departure.city.name) || \"N/A\", \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r114.getInboundSegments(flight_r85)[0] == null ? null : ctx_r114.getInboundSegments(flight_r85)[0].duration) ? ctx_r114.formatDuration(ctx_r114.getInboundSegments(flight_r85)[0].duration) : flight_r85.items && flight_r85.items.length > 1 ? ctx_r114.formatDuration(flight_r85.items[1].duration) : \"N/A\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r114.getInboundSegments(flight_r85).length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r114.getInboundSegments(flight_r85).length === 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r114.getInboundSegments(flight_r85)[ctx_r114.getInboundSegments(flight_r85).length - 1] == null ? null : ctx_r114.getInboundSegments(flight_r85)[ctx_r114.getInboundSegments(flight_r85).length - 1].arrival) ? ctx_r114.formatDate(ctx_r114.getInboundSegments(flight_r85)[ctx_r114.getInboundSegments(flight_r85).length - 1].arrival.date) : \"N/A\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r114.getInboundSegments(flight_r85)[ctx_r114.getInboundSegments(flight_r85).length - 1] == null ? null : ctx_r114.getInboundSegments(flight_r85)[ctx_r114.getInboundSegments(flight_r85).length - 1].arrival == null ? null : ctx_r114.getInboundSegments(flight_r85)[ctx_r114.getInboundSegments(flight_r85).length - 1].arrival.airport == null ? null : ctx_r114.getInboundSegments(flight_r85)[ctx_r114.getInboundSegments(flight_r85).length - 1].arrival.airport.code) || \"N/A\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r114.getInboundSegments(flight_r85)[ctx_r114.getInboundSegments(flight_r85).length - 1] == null ? null : ctx_r114.getInboundSegments(flight_r85)[ctx_r114.getInboundSegments(flight_r85).length - 1].arrival == null ? null : ctx_r114.getInboundSegments(flight_r85)[ctx_r114.getInboundSegments(flight_r85).length - 1].arrival.city == null ? null : ctx_r114.getInboundSegments(flight_r85)[ctx_r114.getInboundSegments(flight_r85).length - 1].arrival.city.name) || \"N/A\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_25_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 244);\n    i0.ɵɵelement(1, \"i\", 245);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r115 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Stay duration: \", ctx_r115.calculateStayDuration(ctx_r115.getOutboundSegments(flight_r85)[ctx_r115.getOutboundSegments(flight_r85).length - 1] == null ? null : ctx_r115.getOutboundSegments(flight_r85)[ctx_r115.getOutboundSegments(flight_r85).length - 1].arrival == null ? null : ctx_r115.getOutboundSegments(flight_r85)[ctx_r115.getOutboundSegments(flight_r85).length - 1].arrival.date, ctx_r115.getInboundSegments(flight_r85)[0] == null ? null : ctx_r115.getInboundSegments(flight_r85)[0].departure == null ? null : ctx_r115.getInboundSegments(flight_r85)[0].departure.date), \"\");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 235);\n    i0.ɵɵelement(1, \"div\", 236);\n    i0.ɵɵelementStart(2, \"div\", 237)(3, \"div\", 238);\n    i0.ɵɵelement(4, \"i\", 48);\n    i0.ɵɵelementStart(5, \"span\", 239);\n    i0.ɵɵtext(6, \"Outbound Flight\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 215)(8, \"div\", 216);\n    i0.ɵɵelement(9, \"div\", 240);\n    i0.ɵɵelementStart(10, \"div\", 218)(11, \"span\", 219);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 220);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 221)(16, \"div\", 222);\n    i0.ɵɵelement(17, \"span\", 223);\n    i0.ɵɵelementStart(18, \"div\", 224);\n    i0.ɵɵelement(19, \"span\", 225);\n    i0.ɵɵelementStart(20, \"span\", 226);\n    i0.ɵɵelement(21, \"i\", 130);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(22, \"span\", 227);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 228);\n    i0.ɵɵelement(24, \"i\", 213);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, SearchPriceComponent_div_168_div_3_div_11_div_25_div_26_Template, 4, 2, \"div\", 229);\n    i0.ɵɵtemplate(27, SearchPriceComponent_div_168_div_3_div_11_div_25_div_27_Template, 2, 0, \"div\", 230);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 231)(29, \"div\", 217);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 218)(32, \"span\", 219);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\", 220);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(36, SearchPriceComponent_div_168_div_3_div_11_div_25_div_36_Template, 34, 9, \"div\", 241);\n    i0.ɵɵtemplate(37, SearchPriceComponent_div_168_div_3_div_11_div_25_div_37_Template, 4, 1, \"div\", 242);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    const ctx_r96 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r96.getFlightDateInfoMessage(flight_r85, (tmp_0_0 = ctx_r96.searchForm.get(\"departureDate\")) == null ? null : tmp_0_0.value, (tmp_0_0 = ctx_r96.searchForm.get(\"returnDate\")) == null ? null : tmp_0_0.value), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"innerHTML\", (ctx_r96.getOutboundSegments(flight_r85)[0] == null ? null : ctx_r96.getOutboundSegments(flight_r85)[0].departure) ? ctx_r96.formatDateWithRequestComparison(ctx_r96.getOutboundSegments(flight_r85)[0].departure.date, (tmp_1_0 = ctx_r96.searchForm.get(\"departureDate\")) == null ? null : tmp_1_0.value) : flight_r85.items && flight_r85.items[0] && flight_r85.items[0].departure ? ctx_r96.formatDateWithRequestComparison(flight_r85.items[0].departure.date, (tmp_1_0 = ctx_r96.searchForm.get(\"departureDate\")) == null ? null : tmp_1_0.value) : \"N/A\", i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r96.getOutboundSegments(flight_r85)[0] == null ? null : ctx_r96.getOutboundSegments(flight_r85)[0].departure == null ? null : ctx_r96.getOutboundSegments(flight_r85)[0].departure.airport == null ? null : ctx_r96.getOutboundSegments(flight_r85)[0].departure.airport.code) || (flight_r85.items && flight_r85.items[0] && flight_r85.items[0].departure && flight_r85.items[0].departure.airport ? flight_r85.items[0].departure.airport.code : \"N/A\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r96.getOutboundSegments(flight_r85)[0] == null ? null : ctx_r96.getOutboundSegments(flight_r85)[0].departure == null ? null : ctx_r96.getOutboundSegments(flight_r85)[0].departure.city == null ? null : ctx_r96.getOutboundSegments(flight_r85)[0].departure.city.name) || (flight_r85.items && flight_r85.items[0] && flight_r85.items[0].departure && flight_r85.items[0].departure.city ? flight_r85.items[0].departure.city.name : \"N/A\"), \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r96.getOutboundSegments(flight_r85)[0] == null ? null : ctx_r96.getOutboundSegments(flight_r85)[0].duration) ? ctx_r96.formatDuration(ctx_r96.getOutboundSegments(flight_r85)[0].duration) : flight_r85.items && flight_r85.items[0] ? ctx_r96.formatDuration(flight_r85.items[0].duration) : \"N/A\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r96.getOutboundSegments(flight_r85).length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r96.getOutboundSegments(flight_r85).length === 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r96.getOutboundSegments(flight_r85)[ctx_r96.getOutboundSegments(flight_r85).length - 1] == null ? null : ctx_r96.getOutboundSegments(flight_r85)[ctx_r96.getOutboundSegments(flight_r85).length - 1].arrival) ? ctx_r96.formatDate(ctx_r96.getOutboundSegments(flight_r85)[ctx_r96.getOutboundSegments(flight_r85).length - 1].arrival.date) : flight_r85.items && flight_r85.items[0] && flight_r85.items[0].arrival ? ctx_r96.formatDate(flight_r85.items[0].arrival.date) : \"N/A\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r96.getOutboundSegments(flight_r85)[ctx_r96.getOutboundSegments(flight_r85).length - 1] == null ? null : ctx_r96.getOutboundSegments(flight_r85)[ctx_r96.getOutboundSegments(flight_r85).length - 1].arrival == null ? null : ctx_r96.getOutboundSegments(flight_r85)[ctx_r96.getOutboundSegments(flight_r85).length - 1].arrival.airport == null ? null : ctx_r96.getOutboundSegments(flight_r85)[ctx_r96.getOutboundSegments(flight_r85).length - 1].arrival.airport.code) || (flight_r85.items && flight_r85.items[0] && flight_r85.items[0].arrival && flight_r85.items[0].arrival.airport ? flight_r85.items[0].arrival.airport.code : \"N/A\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r96.getOutboundSegments(flight_r85)[ctx_r96.getOutboundSegments(flight_r85).length - 1] == null ? null : ctx_r96.getOutboundSegments(flight_r85)[ctx_r96.getOutboundSegments(flight_r85).length - 1].arrival == null ? null : ctx_r96.getOutboundSegments(flight_r85)[ctx_r96.getOutboundSegments(flight_r85).length - 1].arrival.city == null ? null : ctx_r96.getOutboundSegments(flight_r85)[ctx_r96.getOutboundSegments(flight_r85).length - 1].arrival.city.name) || (flight_r85.items && flight_r85.items[0] && flight_r85.items[0].arrival && flight_r85.items[0].arrival.city ? flight_r85.items[0].arrival.city.name : \"N/A\"), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r96.getInboundSegments(flight_r85).length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r96.getInboundSegments(flight_r85).length > 0 && ctx_r96.getOutboundSegments(flight_r85).length > 0);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_5_div_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 255);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r128 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r128.weight, \" kg\");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_5_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 255);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r128 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r128.piece, \" piece(s)\");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 251);\n    i0.ɵɵelement(1, \"i\", 83);\n    i0.ɵɵelementStart(2, \"div\", 252)(3, \"span\", 253);\n    i0.ɵɵtext(4, \"Checked Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_5_div_1_span_5_Template, 2, 1, \"span\", 254);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_5_div_1_span_6_Template, 2, 1, \"span\", 254);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const baggage_r128 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", baggage_r128.weight > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", baggage_r128.piece > 0);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_5_div_1_Template, 7, 2, \"div\", 250);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r123 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, ctx_r123.filterBaggageByType(flight_r85.items[0].baggageInformations, 2), 0, 2));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_6_div_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 255);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r135 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r135.piece, \" piece(s)\");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_6_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 255);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r135 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r135.weight, \" kg\");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 257);\n    i0.ɵɵelement(1, \"i\", 258);\n    i0.ɵɵelementStart(2, \"div\", 252)(3, \"span\", 253);\n    i0.ɵɵtext(4, \"Cabin Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_6_div_1_span_5_Template, 2, 1, \"span\", 254);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_6_div_1_span_6_Template, 2, 1, \"span\", 254);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const baggage_r135 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", baggage_r135.piece > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", baggage_r135.weight > 0);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_6_div_1_Template, 7, 2, \"div\", 256);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r124 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, ctx_r124.filterBaggageByType(flight_r85.items[0].baggageInformations, 1), 0, 1));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_27_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 257);\n    i0.ɵɵelement(1, \"i\", 258);\n    i0.ɵɵelementStart(2, \"div\", 252)(3, \"span\", 253);\n    i0.ɵɵtext(4, \"Cabin Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 255);\n    i0.ɵɵtext(6, \"Included\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_27_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 259)(1, \"span\");\n    i0.ɵɵtext(2, \"No detailed baggage information available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 246)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 83);\n    i0.ɵɵtext(3, \" Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 247);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_5_Template, 3, 5, \"ng-container\", 66);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_168_div_3_div_11_div_27_ng_container_6_Template, 3, 5, \"ng-container\", 66);\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_168_div_3_div_11_div_27_div_7_Template, 7, 0, \"div\", 248);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, SearchPriceComponent_div_168_div_3_div_11_div_27_div_8_Template, 3, 0, \"div\", 249);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    const ctx_r97 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.items[0].baggageInformations && flight_r85.items[0].baggageInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.items[0].baggageInformations && flight_r85.items[0].baggageInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !flight_r85.items[0].baggageInformations || flight_r85.items[0].baggageInformations.length === 0 || ctx_r97.filterBaggageByType(flight_r85.items[0].baggageInformations, 1).length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !flight_r85.items[0].baggageInformations || flight_r85.items[0].baggageInformations.length === 0);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_28_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 259)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const service_r143 = ctx.$implicit;\n    const ctx_r142 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r142.getServiceName(service_r143));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 246)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 260);\n    i0.ɵɵtext(3, \" Services\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_168_div_3_div_11_div_28_div_4_Template, 3, 1, \"div\", 261);\n    i0.ɵɵpipe(5, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(5, 1, flight_r85.items[0].services, 0, 3));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_29_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 259)(1, \"span\");\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"small\", 262);\n    i0.ɵɵtext(5, \"(from API response)\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(flight_r85.offers[0].reservableInfo.reservable ? \"fas fa-check-circle text-success\" : \"fas fa-times-circle text-danger\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r85.offers[0].reservableInfo.reservable ? \"Reservable\" : \"Not reservable\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 246)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 206);\n    i0.ɵɵtext(3, \" Offer Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_168_div_3_div_11_div_29_div_4_Template, 6, 3, \"div\", 249);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.offers[0].reservableInfo);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_35_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 271)(1, \"span\", 272);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 273);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r151 = ctx.$implicit;\n    const ctx_r149 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r149.getPassengerTypeName(item_r151.passengerType), \" (x\", item_r151.passengerCount, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 3, item_r151.price.amount, item_r151.price.currency));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_35_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 274)(1, \"span\", 275);\n    i0.ɵɵtext(2, \"Service Fee\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 276);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, flight_r85.offers[0].serviceFee.amount, flight_r85.offers[0].serviceFee.currency));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 265)(1, \"h4\");\n    i0.ɵɵtext(2, \"Price Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_168_div_3_div_11_div_35_div_1_div_3_Template, 6, 6, \"div\", 266);\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_168_div_3_div_11_div_35_div_1_div_4_Template, 6, 4, \"div\", 267);\n    i0.ɵɵelementStart(5, \"div\", 268)(6, \"span\", 269);\n    i0.ɵɵtext(7, \"Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 270);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", flight_r85.offers[0].priceBreakDown.items);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.offers[0].serviceFee);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 3, flight_r85.offers[0].price.amount, flight_r85.offers[0].price.currency));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 263);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_168_div_3_div_11_div_35_div_1_Template, 11, 6, \"div\", 264);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.offers[0].priceBreakDown && flight_r85.offers[0].priceBreakDown.items);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_36_div_6_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 292);\n    i0.ɵɵelement(1, \"i\", 293);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r159 = i0.ɵɵnextContext();\n    const segment_r156 = ctx_r159.$implicit;\n    const i_r157 = ctx_r159.index;\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r158 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r158.calculateLayoverTime(segment_r156, flight_r85.items[0].segments[i_r157 + 1]), \" layover in \", segment_r156.arrival && segment_r156.arrival.city && segment_r156.arrival.city.name || \"connecting city\", \"\");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_36_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 282)(1, \"div\", 283)(2, \"span\", 284);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 285);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 286);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 287)(9, \"div\", 288)(10, \"div\", 217);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 218)(13, \"span\", 219);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 220);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 289)(18, \"div\", 222);\n    i0.ɵɵelement(19, \"span\", 223);\n    i0.ɵɵelementStart(20, \"div\", 224);\n    i0.ɵɵelement(21, \"span\", 225);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"span\", 227);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 228);\n    i0.ɵɵelement(24, \"i\", 213);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 290)(27, \"div\", 217);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 218)(30, \"span\", 219);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 220);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(34, SearchPriceComponent_div_168_div_3_div_11_div_36_div_6_div_34_Template, 4, 2, \"div\", 291);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r156 = ctx.$implicit;\n    const i_r157 = ctx.index;\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r155 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Segment \", i_r157 + 1, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r156.airline && segment_r156.airline.name || \"Airline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r156.flightNo);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(segment_r156.departure ? ctx_r155.formatDate(segment_r156.departure.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r156.departure && segment_r156.departure.airport && segment_r156.departure.airport.code || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r156.departure && segment_r156.departure.city && segment_r156.departure.city.name || \"N/A\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r155.formatDuration(segment_r156.duration), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r156.arrival ? ctx_r155.formatDate(segment_r156.arrival.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r156.arrival && segment_r156.arrival.airport && segment_r156.arrival.airport.code || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r156.arrival && segment_r156.arrival.city && segment_r156.arrival.city.name || \"N/A\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r157 < flight_r85.items[0].segments.length - 1);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 277)(1, \"details\", 278)(2, \"summary\", 279);\n    i0.ɵɵelement(3, \"i\", 48);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 280);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_168_div_3_div_11_div_36_div_6_Template, 35, 11, \"div\", 281);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Flight Segments (\", flight_r85.items[0].segments.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", flight_r85.items[0].segments);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_37_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 300);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r85.offers[0].brandedFare.description, \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_37_div_7_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 306);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r167 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", feature_r167.explanations[0].text, \" \");\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_37_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 303)(1, \"div\", 304);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_168_div_3_div_11_div_37_div_7_div_3_div_3_Template, 2, 1, \"div\", 305);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r167 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r167.commercialName || \"Feature\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", feature_r167.explanations && feature_r167.explanations.length > 0);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_37_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 301)(1, \"h4\");\n    i0.ɵɵtext(2, \"Features\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_168_div_3_div_11_div_37_div_7_div_3_Template, 4, 2, \"div\", 302);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", flight_r85.offers[0].brandedFare.features);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 294)(1, \"details\", 295)(2, \"summary\", 296);\n    i0.ɵɵelement(3, \"i\", 210);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 297);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_168_div_3_div_11_div_37_div_6_Template, 2, 1, \"div\", 298);\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_168_div_3_div_11_div_37_div_7_Template, 4, 1, \"div\", 299);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r85 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", flight_r85.offers[0].brandedFare.name || \"Branded Fare\", \" Details \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.offers[0].brandedFare.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.offers[0].brandedFare.features && flight_r85.offers[0].brandedFare.features.length > 0);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r173 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 168);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_168_div_3_div_11_div_1_Template, 3, 0, \"div\", 169);\n    i0.ɵɵelementStart(2, \"div\", 170)(3, \"div\", 171)(4, \"div\", 172);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_168_div_3_div_11_img_5_Template, 1, 1, \"img\", 173);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_168_div_3_div_11_i_6_Template, 1, 0, \"i\", 174);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 175)(8, \"span\", 176);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 177);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, SearchPriceComponent_div_168_div_3_div_11_span_12_Template, 3, 1, \"span\", 178);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 179);\n    i0.ɵɵtemplate(14, SearchPriceComponent_div_168_div_3_div_11_span_14_Template, 3, 0, \"span\", 180);\n    i0.ɵɵtemplate(15, SearchPriceComponent_div_168_div_3_div_11_span_15_Template, 3, 1, \"span\", 180);\n    i0.ɵɵtemplate(16, SearchPriceComponent_div_168_div_3_div_11_span_16_Template, 3, 0, \"span\", 181);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 182)(18, \"span\", 183);\n    i0.ɵɵtext(19, \"Price per person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 184);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, SearchPriceComponent_div_168_div_3_div_11_span_22_Template, 3, 5, \"span\", 185);\n    i0.ɵɵtemplate(23, SearchPriceComponent_div_168_div_3_div_11_span_23_Template, 3, 1, \"span\", 186);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, SearchPriceComponent_div_168_div_3_div_11_div_24_Template, 31, 9, \"div\", 187);\n    i0.ɵɵtemplate(25, SearchPriceComponent_div_168_div_3_div_11_div_25_Template, 38, 12, \"div\", 188);\n    i0.ɵɵelementStart(26, \"div\", 189);\n    i0.ɵɵtemplate(27, SearchPriceComponent_div_168_div_3_div_11_div_27_Template, 9, 4, \"div\", 190);\n    i0.ɵɵtemplate(28, SearchPriceComponent_div_168_div_3_div_11_div_28_Template, 6, 5, \"div\", 190);\n    i0.ɵɵtemplate(29, SearchPriceComponent_div_168_div_3_div_11_div_29_Template, 5, 1, \"div\", 190);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 191)(31, \"details\", 192)(32, \"summary\", 193);\n    i0.ɵɵelement(33, \"i\", 78);\n    i0.ɵɵtext(34, \" Price Breakdown \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(35, SearchPriceComponent_div_168_div_3_div_11_div_35_Template, 2, 1, \"div\", 194);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, SearchPriceComponent_div_168_div_3_div_11_div_36_Template, 7, 2, \"div\", 195);\n    i0.ɵɵtemplate(37, SearchPriceComponent_div_168_div_3_div_11_div_37_Template, 8, 3, \"div\", 196);\n    i0.ɵɵelementStart(38, \"div\", 197)(39, \"button\", 198);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_168_div_3_div_11_Template_button_click_39_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r173);\n      const flight_r85 = restoredCtx.$implicit;\n      const ctx_r172 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r172.showAllDetails(flight_r85));\n    });\n    i0.ɵɵelement(40, \"i\", 199);\n    i0.ɵɵtext(41, \" View All Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 200);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_168_div_3_div_11_Template_button_click_42_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r173);\n      const flight_r85 = restoredCtx.$implicit;\n      const ctx_r174 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r174.selectThisFlight(flight_r85));\n    });\n    i0.ɵɵelement(43, \"i\", 201);\n    i0.ɵɵtext(44, \" Select This Flight \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r85 = ctx.$implicit;\n    const ctx_r42 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"unavailable\", !ctx_r42.isFlightAvailable(flight_r85))(\"round-trip\", ctx_r42.isRoundTripFlight(flight_r85));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r42.isRoundTripFlight(flight_r85));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.items && flight_r85.items[0] && flight_r85.items[0].airline && flight_r85.items[0].airline.thumbnailFull);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(flight_r85.items && flight_r85.items[0] && flight_r85.items[0].airline && flight_r85.items[0].airline.thumbnailFull));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r85.items && flight_r85.items[0] && flight_r85.items[0].airline ? flight_r85.items[0].airline.name : \"Airline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r85.items && flight_r85.items[0] ? flight_r85.items[0].flightNo : \"N/A\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.provider || flight_r85.items && flight_r85.items[0] && flight_r85.items[0].flightProvider);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.items && flight_r85.items[0] && flight_r85.items[0].stopCount === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.items && flight_r85.items[0] && flight_r85.items[0].flightClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.offers && flight_r85.offers[0] && flight_r85.offers[0].hasBrand);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r42.getMinPrice(flight_r85));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.offers && flight_r85.offers.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.offers && flight_r85.offers.length > 0 && flight_r85.offers[0].expiresOn);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r42.isRoundTripFlight(flight_r85));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r42.isRoundTripFlight(flight_r85));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.items && flight_r85.items[0]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.items && flight_r85.items[0] && flight_r85.items[0].services && flight_r85.items[0].services.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.offers && flight_r85.offers.length > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.offers && flight_r85.offers[0]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.items && flight_r85.items[0] && flight_r85.items[0].segments && flight_r85.items[0].segments.length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r85.offers && flight_r85.offers[0] && flight_r85.offers[0].brandedFare);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !flight_r85.offers || flight_r85.offers.length === 0 || !ctx_r42.isFlightAvailable(flight_r85));\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 314);\n    i0.ɵɵelement(1, \"i\", 315);\n    i0.ɵɵelementStart(2, \"span\", 316);\n    i0.ɵɵtext(3, \"Business class flights may not be available for this route. Try selecting Economy class instead.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_12_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 311);\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Try Economy class\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 307)(1, \"div\", 308);\n    i0.ɵɵelement(2, \"i\", 120);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No flights found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"We couldn't find any flights matching your search criteria. Try adjusting your search parameters.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_168_div_3_div_12_div_7_Template, 4, 0, \"div\", 309);\n    i0.ɵɵelementStart(8, \"div\", 310)(9, \"div\", 311);\n    i0.ɵɵelement(10, \"i\", 60);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Try different dates\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 311);\n    i0.ɵɵelement(14, \"i\", 312);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"Try nearby airports\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 311);\n    i0.ɵɵelement(18, \"i\", 130);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Include flights with stops\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, SearchPriceComponent_div_168_div_3_div_12_div_21_Template, 4, 0, \"div\", 313);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r43.searchForm.get(\"flightClass\")) == null ? null : tmp_0_0.value) === 2);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r43.searchForm.get(\"flightClass\")) == null ? null : tmp_1_0.value) === 2);\n  }\n}\nfunction SearchPriceComponent_div_168_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 136)(1, \"div\", 137)(2, \"div\", 138)(3, \"h3\");\n    i0.ɵɵtext(4, \"Flight Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_168_div_3_p_5_Template, 2, 0, \"p\", 66);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_168_div_3_p_6_Template, 5, 2, \"p\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_168_div_3_div_7_Template, 3, 1, \"div\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 140);\n    i0.ɵɵtemplate(9, SearchPriceComponent_div_168_div_3_div_9_Template, 106, 32, \"div\", 141);\n    i0.ɵɵelementStart(10, \"div\", 142);\n    i0.ɵɵtemplate(11, SearchPriceComponent_div_168_div_3_div_11_Template, 45, 25, \"div\", 143);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, SearchPriceComponent_div_168_div_3_div_12_Template, 22, 2, \"div\", 144);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r35.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r35.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r35.searchResults.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r35.searchResults.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r35.filteredResults);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r35.isLoading && !ctx_r35.errorMessage && ctx_r35.filteredResults.length === 0);\n  }\n}\nfunction SearchPriceComponent_div_168_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_168_div_1_Template, 9, 0, \"div\", 124);\n    i0.ɵɵtemplate(2, SearchPriceComponent_div_168_div_2_Template, 10, 1, \"div\", 125);\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_168_div_3_Template, 13, 6, \"div\", 126);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r13.isLoading && ctx_r13.errorMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r13.isLoading && !ctx_r13.errorMessage);\n  }\n}\nexport let SearchPriceComponent = /*#__PURE__*/(() => {\n  class SearchPriceComponent {\n    constructor(fb, productService, router, sharedDataService) {\n      this.fb = fb;\n      this.productService = productService;\n      this.router = router;\n      this.sharedDataService = sharedDataService;\n      this.departureLocations = [];\n      this.arrivalLocations = [];\n      this.isLoading = false;\n      this.searchResults = []; // Utiliser any[] pour accepter les deux types de vols\n      this.filteredResults = []; // Utiliser any[] pour accepter les deux types de vols\n      this.hasSearched = false;\n      this.errorMessage = '';\n      this.lastSearchId = '';\n      this.isRoundTripResponse = false; // Indique si la réponse actuelle est un aller-retour\n      // Types de recherche de vol\n      this.SEARCH_TYPE_ONE_WAY = 1;\n      this.SEARCH_TYPE_ROUND_TRIP = 2;\n      this.SEARCH_TYPE_MULTI_CITY = 3;\n      // Type de recherche actuel (par défaut: aller simple)\n      this.currentSearchType = this.SEARCH_TYPE_ONE_WAY;\n      // Passenger selector properties\n      this.showPassengerDropdown = false;\n      this.passengerCounts = {\n        [PassengerType.Adult]: 1,\n        [PassengerType.Child]: 0,\n        [PassengerType.Infant]: 0\n      };\n      // Filter options\n      this.currentFilter = 'recommended';\n      this.filterOptions = [{\n        value: 'recommended',\n        label: 'Recommended',\n        icon: 'fa-star'\n      }, {\n        value: 'cheapest',\n        label: 'Cheapest',\n        icon: 'fa-dollar-sign'\n      }, {\n        value: 'shortest',\n        label: 'Shortest',\n        icon: 'fa-clock'\n      }];\n      // Sidebar filter options\n      this.sidebarFilters = {\n        stops: {\n          direct: false,\n          oneStop: false,\n          multiStop: false\n        },\n        departureTime: {\n          earlyMorning: false,\n          morning: false,\n          afternoon: false,\n          evening: false // 18:00 - 00:00\n        },\n\n        arrivalTime: {\n          earlyMorning: false,\n          morning: false,\n          afternoon: false,\n          evening: false // 18:00 - 00:00\n        },\n\n        airlines: {} // Will be populated dynamically based on search results\n      };\n      // Expanded sections in sidebar\n      this.expandedSections = {\n        stops: true,\n        departureTime: true,\n        arrivalTime: true,\n        airlines: true\n      };\n      // Price ranges for each filter option (will be calculated from results)\n      this.filterPrices = {\n        stops: {\n          direct: 0,\n          oneStop: 0,\n          multiStop: 0\n        },\n        departureTime: {\n          earlyMorning: 0,\n          morning: 0,\n          afternoon: 0,\n          evening: 0\n        },\n        arrivalTime: {\n          earlyMorning: 0,\n          morning: 0,\n          afternoon: 0,\n          evening: 0\n        },\n        airlines: {}\n      };\n      // Passenger type options\n      this.passengerTypes = [{\n        value: PassengerType.Adult,\n        label: 'Adult'\n      }, {\n        value: PassengerType.Child,\n        label: 'Child'\n      }, {\n        value: PassengerType.Infant,\n        label: 'Infant'\n      }];\n      // Flight class options\n      this.flightClasses = [{\n        value: FlightClassType.PROMO,\n        label: 'Promo'\n      }, {\n        value: FlightClassType.ECONOMY,\n        label: 'Economy'\n      }, {\n        value: FlightClassType.BUSINESS,\n        label: 'Business'\n      }];\n      // Initialiser la date minimale à aujourd'hui\n      this.minDate = new Date().toISOString().split('T')[0];\n      // Initialiser le formulaire avec tous les champs dynamiques\n      this.searchForm = this.fb.group({\n        // Champs principaux\n        productType: [3, Validators.required],\n        serviceTypes: [['1'], Validators.required],\n        departureLocation: ['', Validators.required],\n        arrivalLocation: ['', Validators.required],\n        departureDate: [this.minDate, Validators.required],\n        returnDate: [''],\n        // Options de vol\n        flightClass: [1, Validators.required],\n        nonStop: [false],\n        // Options avancées\n        culture: ['en-US'],\n        currency: ['EUR'],\n        acceptPendingProviders: [false],\n        forceFlightBundlePackage: [false],\n        disablePackageOfferTotalPrice: [true],\n        calculateFlightFees: [false],\n        // Options de bagages\n        flightBaggageGetOption: [0]\n      });\n      // Initialiser les compteurs de passagers dans le service partagé\n      this.sharedDataService.setPassengerCounts(this.passengerCounts);\n    }\n    // Close dropdown when clicking outside\n    onDocumentClick(event) {\n      // Check if click is outside the passenger dropdown\n      const clickedElement = event.target;\n      const passengerSelector = document.querySelector('.passengers-selector');\n      if (passengerSelector && !passengerSelector.contains(clickedElement)) {\n        this.showPassengerDropdown = false;\n      }\n    }\n    // Toggle passenger dropdown\n    togglePassengerDropdown(event) {\n      event.stopPropagation();\n      this.showPassengerDropdown = !this.showPassengerDropdown;\n    }\n    // Close passenger dropdown\n    closePassengerDropdown() {\n      this.showPassengerDropdown = false;\n    }\n    // Get passenger count for a specific type\n    getPassengerCount(type) {\n      return this.passengerCounts[type] || 0;\n    }\n    // Increase passenger count\n    increasePassengerCount(type) {\n      if (this.getTotalPassengers() < 9) {\n        this.passengerCounts[type] = (this.passengerCounts[type] || 0) + 1;\n        // Mettre à jour le service partagé\n        this.sharedDataService.setPassengerCounts(this.passengerCounts);\n      }\n    }\n    // Decrease passenger count\n    decreasePassengerCount(type) {\n      // Pour les adultes, ne pas permettre de descendre en dessous de 1\n      if (type === PassengerType.Adult) {\n        if (this.passengerCounts[type] > 1) {\n          this.passengerCounts[type] -= 1;\n        }\n      } else if (this.passengerCounts[type] > 0) {\n        this.passengerCounts[type] -= 1;\n      }\n      // Mettre à jour le service partagé\n      this.sharedDataService.setPassengerCounts(this.passengerCounts);\n    }\n    // Get total number of passengers\n    getTotalPassengers() {\n      return Object.values(this.passengerCounts).reduce((sum, count) => sum + count, 0);\n    }\n    // Get passengers array for API request\n    getPassengersArray() {\n      // Utiliser le service partagé pour obtenir les passagers\n      return this.sharedDataService.getPassengersArray();\n    }\n    ngOnInit() {\n      // Configurer les observables pour l'autocomplétion\n      this.setupAutocomplete();\n      // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n      this.preloadLocations();\n      // Ajouter un écouteur pour les messages de la console\n      console.log('SearchPriceComponent initialized - Debugging availability issues');\n    }\n    // Méthode pour appliquer le filtre sélectionné\n    applyFilter(filterValue) {\n      this.currentFilter = filterValue;\n      this.applyAllFilters();\n    }\n    // Méthode pour vérifier si un vol correspond à la classe sélectionnée\n    isFlightMatchingClass(flight, selectedClass) {\n      // Vérifier si le vol a des items\n      if (!flight.items || flight.items.length === 0) {\n        console.log(`Flight ${flight.id} has no items`);\n        return false;\n      }\n      console.log(`Checking flight ${flight.id} for class ${selectedClass}`);\n      // Vérifier la classe de vol dans les items\n      for (const item of flight.items) {\n        if (item.flightClass) {\n          console.log(`Item class: ${item.flightClass.name} (type: ${item.flightClass.type})`);\n          if (item.flightClass.type === selectedClass) {\n            console.log(`Match found in item flightClass`);\n            return true;\n          }\n        }\n        // Vérifier également dans les segments si disponibles\n        if (item.segments) {\n          console.log(`Checking ${item.segments.length} segments`);\n          for (const segment of item.segments) {\n            if (segment.flightClass) {\n              console.log(`Segment class: ${segment.flightClass.name} (type: ${segment.flightClass.type})`);\n              if (segment.flightClass.type === selectedClass) {\n                console.log(`Match found in segment flightClass`);\n                return true;\n              }\n            }\n          }\n        }\n      }\n      // Vérifier également dans les offres si disponibles\n      if (flight.offers && flight.offers.length > 0) {\n        console.log(`Checking ${flight.offers.length} offers`);\n        for (const offer of flight.offers) {\n          if (offer.flightClassInformations && offer.flightClassInformations.length > 0) {\n            console.log(`Offer has ${offer.flightClassInformations.length} class infos`);\n            for (const classInfo of offer.flightClassInformations) {\n              console.log(`Offer class info: ${classInfo.name} (type: ${classInfo.type})`);\n              if (classInfo.type === selectedClass) {\n                console.log(`Match found in offer flightClassInformations`);\n                return true;\n              }\n            }\n          } else {\n            console.log(`Offer has no flightClassInformations`);\n          }\n        }\n      }\n      console.log(`No match found for flight ${flight.id}`);\n      return false;\n    }\n    // Méthode pour appliquer tous les filtres (top et sidebar)\n    applyAllFilters() {\n      if (!this.searchResults || this.searchResults.length === 0) {\n        this.filteredResults = [];\n        return;\n      }\n      // Étape 1: Appliquer les filtres de la sidebar\n      let results = [...this.searchResults];\n      // Récupérer la classe de vol sélectionnée\n      const selectedClass = this.searchForm.get('flightClass')?.value;\n      // Filtrer par classe de vol\n      if (selectedClass !== undefined) {\n        console.log('Filtering by flight class:', selectedClass);\n        // Afficher le nombre total de vols avant filtrage\n        console.log('Total flights before class filtering:', results.length);\n        // Vérifier combien de vols correspondent à chaque classe\n        const classCounts = {\n          [FlightClassType.PROMO]: 0,\n          [FlightClassType.ECONOMY]: 0,\n          [FlightClassType.BUSINESS]: 0\n        };\n        results.forEach(flight => {\n          if (this.isFlightMatchingClass(flight, FlightClassType.PROMO)) classCounts[FlightClassType.PROMO]++;\n          if (this.isFlightMatchingClass(flight, FlightClassType.ECONOMY)) classCounts[FlightClassType.ECONOMY]++;\n          if (this.isFlightMatchingClass(flight, FlightClassType.BUSINESS)) classCounts[FlightClassType.BUSINESS]++;\n        });\n        console.log('Flights by class (before filtering):', classCounts);\n        // Option pour désactiver temporairement le filtrage par classe (pour débogage)\n        // Mettre à true pour voir tous les vols disponibles sans filtrage par classe\n        const disableClassFiltering = true;\n        if (!disableClassFiltering) {\n          results = results.filter(flight => this.isFlightMatchingClass(flight, selectedClass));\n          console.log('Flights after class filtering:', results.length);\n        } else {\n          console.log('Class filtering disabled for debugging');\n        }\n      }\n      // Filtrer par nombre d'escales\n      if (this.sidebarFilters.stops.direct || this.sidebarFilters.stops.oneStop || this.sidebarFilters.stops.multiStop) {\n        results = results.filter(flight => {\n          if (!flight.items || flight.items.length === 0) return false;\n          const stopCount = flight.items[0].stopCount || 0;\n          return this.sidebarFilters.stops.direct && stopCount === 0 || this.sidebarFilters.stops.oneStop && stopCount === 1 || this.sidebarFilters.stops.multiStop && stopCount >= 2;\n        });\n      }\n      // Filtrer par horaire de départ\n      if (this.sidebarFilters.departureTime.earlyMorning || this.sidebarFilters.departureTime.morning || this.sidebarFilters.departureTime.afternoon || this.sidebarFilters.departureTime.evening) {\n        results = results.filter(flight => {\n          if (!flight.items || flight.items.length === 0 || !flight.items[0].departure || !flight.items[0].departure.date) {\n            return false;\n          }\n          const departureDate = new Date(flight.items[0].departure.date);\n          const hours = departureDate.getHours();\n          return this.sidebarFilters.departureTime.earlyMorning && hours >= 0 && hours < 8 || this.sidebarFilters.departureTime.morning && hours >= 8 && hours < 12 || this.sidebarFilters.departureTime.afternoon && hours >= 12 && hours < 18 || this.sidebarFilters.departureTime.evening && hours >= 18 && hours < 24;\n        });\n      }\n      // Filtrer par horaire d'arrivée\n      if (this.sidebarFilters.arrivalTime.earlyMorning || this.sidebarFilters.arrivalTime.morning || this.sidebarFilters.arrivalTime.afternoon || this.sidebarFilters.arrivalTime.evening) {\n        results = results.filter(flight => {\n          if (!flight.items || flight.items.length === 0 || !flight.items[0].arrival || !flight.items[0].arrival.date) {\n            return false;\n          }\n          const arrivalDate = new Date(flight.items[0].arrival.date);\n          const hours = arrivalDate.getHours();\n          return this.sidebarFilters.arrivalTime.earlyMorning && hours >= 0 && hours < 8 || this.sidebarFilters.arrivalTime.morning && hours >= 8 && hours < 12 || this.sidebarFilters.arrivalTime.afternoon && hours >= 12 && hours < 18 || this.sidebarFilters.arrivalTime.evening && hours >= 18 && hours < 24;\n        });\n      }\n      // Filtrer par compagnie aérienne\n      const selectedAirlines = Object.entries(this.sidebarFilters.airlines).filter(([_, selected]) => selected).map(([airline, _]) => airline);\n      if (selectedAirlines.length > 0) {\n        results = results.filter(flight => {\n          if (!flight.items || flight.items.length === 0 || !flight.items[0].airline || !flight.items[0].airline.name) {\n            return false;\n          }\n          return selectedAirlines.includes(flight.items[0].airline.name);\n        });\n      }\n      // Étape 2: Appliquer le tri selon le filtre sélectionné en haut\n      switch (this.currentFilter) {\n        case 'cheapest':\n          this.filteredResults = this.sortByPrice(results);\n          break;\n        case 'shortest':\n          this.filteredResults = this.sortByDuration(results);\n          break;\n        case 'recommended':\n        default:\n          this.filteredResults = this.sortByRecommendation(results);\n          break;\n      }\n    }\n    // Trier les vols par prix (du moins cher au plus cher)\n    sortByPrice(flights) {\n      return flights.sort((a, b) => {\n        const priceA = this.getMinPriceAmount(a);\n        const priceB = this.getMinPriceAmount(b);\n        return priceA - priceB;\n      });\n    }\n    // Trier les vols par durée (du plus court au plus long)\n    sortByDuration(flights) {\n      return flights.sort((a, b) => {\n        const durationA = a.items && a.items[0] ? a.items[0].duration : Number.MAX_VALUE;\n        const durationB = b.items && b.items[0] ? b.items[0].duration : Number.MAX_VALUE;\n        return durationA - durationB;\n      });\n    }\n    // Trier les vols par recommandation (combinaison de prix, durée et autres facteurs)\n    sortByRecommendation(flights) {\n      return flights.sort((a, b) => {\n        // Calculer un score pour chaque vol basé sur plusieurs facteurs\n        const scoreA = this.calculateRecommendationScore(a);\n        const scoreB = this.calculateRecommendationScore(b);\n        return scoreB - scoreA; // Ordre décroissant (score plus élevé = meilleur)\n      });\n    }\n    // Calculer un score de recommandation pour un vol\n    calculateRecommendationScore(flight) {\n      if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n        return 0;\n      }\n      const item = flight.items[0];\n      const offer = flight.offers[0];\n      // Facteurs à considérer pour le score\n      const price = this.getMinPriceAmount(flight);\n      const duration = item.duration;\n      const stopCount = item.stopCount || 0;\n      const availability = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n      // Normaliser les valeurs (plus le score est élevé, meilleur est le vol)\n      const priceScore = 1000 / (price + 100); // Inverse du prix (moins cher = meilleur score)\n      const durationScore = 1000 / (duration + 100); // Inverse de la durée (plus court = meilleur score)\n      const stopScore = 1 / (stopCount + 1); // Moins d'escales = meilleur score\n      const availabilityScore = Math.min(availability, 10) / 10; // Plus de disponibilité = meilleur score\n      // Pondération des facteurs (ajustable selon les préférences)\n      const weights = {\n        price: 0.4,\n        duration: 0.3,\n        stops: 0.2,\n        availability: 0.1 // 10% importance pour la disponibilité\n      };\n      // Calculer le score final pondéré\n      return priceScore * weights.price + durationScore * weights.duration + stopScore * weights.stops + availabilityScore * weights.availability;\n    }\n    // Obtenir le montant du prix minimum pour un vol\n    getMinPriceAmount(flight) {\n      if (!flight.offers || flight.offers.length === 0) {\n        return Number.MAX_VALUE;\n      }\n      return flight.offers.reduce((min, offer) => offer.price && offer.price.amount < min ? offer.price.amount : min, flight.offers[0].price ? flight.offers[0].price.amount : Number.MAX_VALUE);\n    }\n    // Calculer les prix minimums pour chaque option de filtre\n    calculateFilterPrices() {\n      if (!this.searchResults || this.searchResults.length === 0) {\n        return;\n      }\n      // Réinitialiser les prix\n      this.resetFilterPrices();\n      // Collecter toutes les compagnies aériennes\n      const airlines = new Set();\n      // Parcourir tous les vols pour calculer les prix minimums\n      this.searchResults.forEach(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n          return;\n        }\n        const item = flight.items[0];\n        const price = this.getMinPriceAmount(flight);\n        // Ajouter la compagnie aérienne à la liste\n        if (item.airline && item.airline.name) {\n          airlines.add(item.airline.name);\n          // Initialiser le prix pour cette compagnie si nécessaire\n          if (!(item.airline.name in this.filterPrices.airlines)) {\n            this.filterPrices.airlines[item.airline.name] = Number.MAX_VALUE;\n          }\n          // Mettre à jour le prix minimum pour cette compagnie\n          this.filterPrices.airlines[item.airline.name] = Math.min(this.filterPrices.airlines[item.airline.name], price);\n        }\n        // Mettre à jour les prix par nombre d'escales\n        const stopCount = item.stopCount || 0;\n        if (stopCount === 0) {\n          this.filterPrices.stops.direct = Math.min(this.filterPrices.stops.direct, price);\n        } else if (stopCount === 1) {\n          this.filterPrices.stops.oneStop = Math.min(this.filterPrices.stops.oneStop, price);\n        } else {\n          this.filterPrices.stops.multiStop = Math.min(this.filterPrices.stops.multiStop, price);\n        }\n        // Mettre à jour les prix par horaire de départ\n        if (item.departure && item.departure.date) {\n          const departureDate = new Date(item.departure.date);\n          const departureHours = departureDate.getHours();\n          if (departureHours >= 0 && departureHours < 8) {\n            this.filterPrices.departureTime.earlyMorning = Math.min(this.filterPrices.departureTime.earlyMorning, price);\n          } else if (departureHours >= 8 && departureHours < 12) {\n            this.filterPrices.departureTime.morning = Math.min(this.filterPrices.departureTime.morning, price);\n          } else if (departureHours >= 12 && departureHours < 18) {\n            this.filterPrices.departureTime.afternoon = Math.min(this.filterPrices.departureTime.afternoon, price);\n          } else {\n            this.filterPrices.departureTime.evening = Math.min(this.filterPrices.departureTime.evening, price);\n          }\n        }\n        // Mettre à jour les prix par horaire d'arrivée\n        if (item.arrival && item.arrival.date) {\n          const arrivalDate = new Date(item.arrival.date);\n          const arrivalHours = arrivalDate.getHours();\n          if (arrivalHours >= 0 && arrivalHours < 8) {\n            this.filterPrices.arrivalTime.earlyMorning = Math.min(this.filterPrices.arrivalTime.earlyMorning, price);\n          } else if (arrivalHours >= 8 && arrivalHours < 12) {\n            this.filterPrices.arrivalTime.morning = Math.min(this.filterPrices.arrivalTime.morning, price);\n          } else if (arrivalHours >= 12 && arrivalHours < 18) {\n            this.filterPrices.arrivalTime.afternoon = Math.min(this.filterPrices.arrivalTime.afternoon, price);\n          } else {\n            this.filterPrices.arrivalTime.evening = Math.min(this.filterPrices.arrivalTime.evening, price);\n          }\n        }\n      });\n      // Initialiser les filtres de compagnies aériennes\n      airlines.forEach(airline => {\n        if (!(airline in this.sidebarFilters.airlines)) {\n          this.sidebarFilters.airlines[airline] = false;\n        }\n      });\n      // Remplacer les valeurs MAX_VALUE par 0 pour les options sans vols\n      this.cleanupFilterPrices();\n    }\n    // Réinitialiser les prix des filtres\n    resetFilterPrices() {\n      this.filterPrices = {\n        stops: {\n          direct: Number.MAX_VALUE,\n          oneStop: Number.MAX_VALUE,\n          multiStop: Number.MAX_VALUE\n        },\n        departureTime: {\n          earlyMorning: Number.MAX_VALUE,\n          morning: Number.MAX_VALUE,\n          afternoon: Number.MAX_VALUE,\n          evening: Number.MAX_VALUE\n        },\n        arrivalTime: {\n          earlyMorning: Number.MAX_VALUE,\n          morning: Number.MAX_VALUE,\n          afternoon: Number.MAX_VALUE,\n          evening: Number.MAX_VALUE\n        },\n        airlines: {}\n      };\n    }\n    // Nettoyer les prix des filtres (remplacer MAX_VALUE par 0)\n    cleanupFilterPrices() {\n      // Escales\n      if (this.filterPrices.stops.direct === Number.MAX_VALUE) this.filterPrices.stops.direct = 0;\n      if (this.filterPrices.stops.oneStop === Number.MAX_VALUE) this.filterPrices.stops.oneStop = 0;\n      if (this.filterPrices.stops.multiStop === Number.MAX_VALUE) this.filterPrices.stops.multiStop = 0;\n      // Horaires de départ\n      if (this.filterPrices.departureTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.departureTime.earlyMorning = 0;\n      if (this.filterPrices.departureTime.morning === Number.MAX_VALUE) this.filterPrices.departureTime.morning = 0;\n      if (this.filterPrices.departureTime.afternoon === Number.MAX_VALUE) this.filterPrices.departureTime.afternoon = 0;\n      if (this.filterPrices.departureTime.evening === Number.MAX_VALUE) this.filterPrices.departureTime.evening = 0;\n      // Horaires d'arrivée\n      if (this.filterPrices.arrivalTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.arrivalTime.earlyMorning = 0;\n      if (this.filterPrices.arrivalTime.morning === Number.MAX_VALUE) this.filterPrices.arrivalTime.morning = 0;\n      if (this.filterPrices.arrivalTime.afternoon === Number.MAX_VALUE) this.filterPrices.arrivalTime.afternoon = 0;\n      if (this.filterPrices.arrivalTime.evening === Number.MAX_VALUE) this.filterPrices.arrivalTime.evening = 0;\n      // Compagnies aériennes\n      Object.keys(this.filterPrices.airlines).forEach(airline => {\n        if (this.filterPrices.airlines[airline] === Number.MAX_VALUE) {\n          this.filterPrices.airlines[airline] = 0;\n        }\n      });\n    }\n    // Basculer l'état d'expansion d'une section\n    toggleSection(section) {\n      this.expandedSections[section] = !this.expandedSections[section];\n    }\n    // Basculer un filtre d'escale\n    toggleStopFilter(filter) {\n      this.sidebarFilters.stops[filter] = !this.sidebarFilters.stops[filter];\n      this.applyAllFilters();\n    }\n    // Basculer un filtre d'horaire de départ\n    toggleDepartureTimeFilter(filter) {\n      this.sidebarFilters.departureTime[filter] = !this.sidebarFilters.departureTime[filter];\n      this.applyAllFilters();\n    }\n    // Basculer un filtre d'horaire d'arrivée\n    toggleArrivalTimeFilter(filter) {\n      this.sidebarFilters.arrivalTime[filter] = !this.sidebarFilters.arrivalTime[filter];\n      this.applyAllFilters();\n    }\n    // Basculer un filtre de compagnie aérienne\n    toggleAirlineFilter(airline) {\n      this.sidebarFilters.airlines[airline] = !this.sidebarFilters.airlines[airline];\n      this.applyAllFilters();\n    }\n    // Effacer tous les filtres\n    clearAllFilters() {\n      // Réinitialiser les filtres d'escales\n      this.sidebarFilters.stops.direct = false;\n      this.sidebarFilters.stops.oneStop = false;\n      this.sidebarFilters.stops.multiStop = false;\n      // Réinitialiser les filtres d'horaires de départ\n      this.sidebarFilters.departureTime.earlyMorning = false;\n      this.sidebarFilters.departureTime.morning = false;\n      this.sidebarFilters.departureTime.afternoon = false;\n      this.sidebarFilters.departureTime.evening = false;\n      // Réinitialiser les filtres d'horaires d'arrivée\n      this.sidebarFilters.arrivalTime.earlyMorning = false;\n      this.sidebarFilters.arrivalTime.morning = false;\n      this.sidebarFilters.arrivalTime.afternoon = false;\n      this.sidebarFilters.arrivalTime.evening = false;\n      // Réinitialiser les filtres de compagnies aériennes\n      Object.keys(this.sidebarFilters.airlines).forEach(airline => {\n        this.sidebarFilters.airlines[airline] = false;\n      });\n      // Appliquer les filtres (qui seront tous désactivés)\n      this.applyAllFilters();\n    }\n    // Formater le prix pour l'affichage\n    formatPrice(price) {\n      if (price === 0) return '-';\n      return price.toFixed(0) + ' €';\n    }\n    // Obtenir les clés des compagnies aériennes\n    getAirlineKeys() {\n      return Object.keys(this.sidebarFilters.airlines);\n    }\n    // Méthode pour afficher tous les détails du vol avec un design professionnel\n    showAllDetails(flight) {\n      // Créer une fenêtre modale pour afficher les détails\n      const modalDiv = document.createElement('div');\n      modalDiv.style.position = 'fixed';\n      modalDiv.style.top = '0';\n      modalDiv.style.left = '0';\n      modalDiv.style.width = '100%';\n      modalDiv.style.height = '100%';\n      modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n      modalDiv.style.zIndex = '1000';\n      modalDiv.style.display = 'flex';\n      modalDiv.style.justifyContent = 'center';\n      modalDiv.style.alignItems = 'center';\n      // Créer le contenu de la fenêtre modale\n      const modalContent = document.createElement('div');\n      modalContent.style.backgroundColor = 'white';\n      modalContent.style.padding = '30px';\n      modalContent.style.borderRadius = '12px';\n      modalContent.style.maxWidth = '90%';\n      modalContent.style.maxHeight = '90%';\n      modalContent.style.overflow = 'auto';\n      modalContent.style.position = 'relative';\n      modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n      modalContent.style.color = '#333';\n      modalContent.style.fontFamily = 'Arial, sans-serif';\n      // Ajouter un bouton de fermeture\n      const closeButton = document.createElement('button');\n      closeButton.innerHTML = '&times;';\n      closeButton.style.position = 'absolute';\n      closeButton.style.top = '15px';\n      closeButton.style.right = '20px';\n      closeButton.style.border = 'none';\n      closeButton.style.background = 'none';\n      closeButton.style.fontSize = '28px';\n      closeButton.style.cursor = 'pointer';\n      closeButton.style.color = '#2989d8';\n      closeButton.style.transition = 'color 0.3s';\n      closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n      closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n      closeButton.onclick = () => document.body.removeChild(modalDiv);\n      // Ajouter un en-tête avec logo et titre\n      const header = document.createElement('div');\n      header.style.display = 'flex';\n      header.style.alignItems = 'center';\n      header.style.marginBottom = '25px';\n      header.style.paddingBottom = '15px';\n      header.style.borderBottom = '1px solid #eee';\n      const logo = document.createElement('div');\n      logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n      const title = document.createElement('h2');\n      title.textContent = 'Complete Flight Details';\n      title.style.margin = '0';\n      title.style.fontSize = '24px';\n      title.style.fontWeight = '600';\n      title.style.color = '#2989d8';\n      header.appendChild(logo);\n      header.appendChild(title);\n      // Créer le conteneur principal\n      const detailsContainer = document.createElement('div');\n      detailsContainer.style.display = 'flex';\n      detailsContainer.style.flexDirection = 'column';\n      detailsContainer.style.gap = '25px';\n      // 1. Section d'informations générales sur le vol\n      const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n      // Ajouter les informations de base du vol\n      if (flight.items && flight.items.length > 0) {\n        const item = flight.items[0];\n        // Informations de la compagnie aérienne\n        if (item.airline) {\n          const airlineInfo = document.createElement('div');\n          airlineInfo.style.display = 'flex';\n          airlineInfo.style.alignItems = 'center';\n          airlineInfo.style.marginBottom = '15px';\n          // Logo de la compagnie\n          if (item.airline.thumbnailFull) {\n            const airlineLogo = document.createElement('img');\n            airlineLogo.src = item.airline.thumbnailFull;\n            airlineLogo.alt = item.airline.name;\n            airlineLogo.style.height = '40px';\n            airlineLogo.style.marginRight = '15px';\n            airlineInfo.appendChild(airlineLogo);\n          } else {\n            const airlineIcon = document.createElement('div');\n            airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n            airlineInfo.appendChild(airlineIcon);\n          }\n          const airlineName = document.createElement('div');\n          airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n          airlineName.style.fontSize = '18px';\n          airlineInfo.appendChild(airlineName);\n          generalInfo.appendChild(airlineInfo);\n        }\n        // Numéro de vol et type\n        const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n        generalInfo.appendChild(flightNumberRow);\n        // Date du vol\n        const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n        generalInfo.appendChild(flightDateRow);\n        // Durée du vol\n        const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n        generalInfo.appendChild(durationRow);\n        // Classe de vol\n        if (item.flightClass) {\n          const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n          generalInfo.appendChild(classRow);\n        }\n        // Nombre d'escales\n        const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n        generalInfo.appendChild(stopsRow);\n      }\n      // 2. Section d'itinéraire\n      const routeSection = this.createSection('Route Details', 'fa-route');\n      if (flight.items && flight.items.length > 0) {\n        const item = flight.items[0];\n        // Créer la visualisation de l'itinéraire\n        const routeVisual = document.createElement('div');\n        routeVisual.style.display = 'flex';\n        routeVisual.style.alignItems = 'center';\n        routeVisual.style.justifyContent = 'space-between';\n        routeVisual.style.margin = '20px 0';\n        routeVisual.style.position = 'relative';\n        // Départ\n        const departure = document.createElement('div');\n        departure.style.textAlign = 'center';\n        departure.style.flex = '1';\n        if (item.departure) {\n          const departureTime = document.createElement('div');\n          departureTime.style.fontSize = '22px';\n          departureTime.style.fontWeight = 'bold';\n          departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n          const departureAirport = document.createElement('div');\n          departureAirport.style.fontSize = '16px';\n          departureAirport.style.marginTop = '5px';\n          departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n          const departureCity = document.createElement('div');\n          departureCity.style.fontSize = '14px';\n          departureCity.style.color = '#666';\n          departureCity.textContent = item.departure.city?.name || 'N/A';\n          departure.appendChild(departureTime);\n          departure.appendChild(departureAirport);\n          departure.appendChild(departureCity);\n        }\n        // Ligne de connexion\n        const connectionLine = document.createElement('div');\n        connectionLine.style.flex = '2';\n        connectionLine.style.display = 'flex';\n        connectionLine.style.alignItems = 'center';\n        connectionLine.style.justifyContent = 'center';\n        connectionLine.style.padding = '0 20px';\n        const line = document.createElement('div');\n        line.style.height = '2px';\n        line.style.backgroundColor = '#ddd';\n        line.style.width = '100%';\n        line.style.position = 'relative';\n        const plane = document.createElement('div');\n        plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n        plane.style.position = 'absolute';\n        plane.style.top = '-9px';\n        plane.style.left = '50%';\n        plane.style.marginLeft = '-9px';\n        plane.style.backgroundColor = 'white';\n        plane.style.padding = '0 5px';\n        line.appendChild(plane);\n        connectionLine.appendChild(line);\n        // Arrivée\n        const arrival = document.createElement('div');\n        arrival.style.textAlign = 'center';\n        arrival.style.flex = '1';\n        if (item.arrival) {\n          const arrivalTime = document.createElement('div');\n          arrivalTime.style.fontSize = '22px';\n          arrivalTime.style.fontWeight = 'bold';\n          arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n          const arrivalAirport = document.createElement('div');\n          arrivalAirport.style.fontSize = '16px';\n          arrivalAirport.style.marginTop = '5px';\n          arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n          const arrivalCity = document.createElement('div');\n          arrivalCity.style.fontSize = '14px';\n          arrivalCity.style.color = '#666';\n          arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n          arrival.appendChild(arrivalTime);\n          arrival.appendChild(arrivalAirport);\n          arrival.appendChild(arrivalCity);\n        }\n        routeVisual.appendChild(departure);\n        routeVisual.appendChild(connectionLine);\n        routeVisual.appendChild(arrival);\n        routeSection.appendChild(routeVisual);\n        // Afficher les segments si c'est un vol avec escales\n        if (item.segments && item.segments.length > 1) {\n          const segmentsTitle = document.createElement('h4');\n          segmentsTitle.textContent = 'Flight Segments';\n          segmentsTitle.style.marginTop = '20px';\n          segmentsTitle.style.marginBottom = '15px';\n          segmentsTitle.style.fontSize = '16px';\n          segmentsTitle.style.fontWeight = '600';\n          routeSection.appendChild(segmentsTitle);\n          const segmentsList = document.createElement('div');\n          segmentsList.style.display = 'flex';\n          segmentsList.style.flexDirection = 'column';\n          segmentsList.style.gap = '15px';\n          item.segments.forEach((segment, index) => {\n            const segmentItem = document.createElement('div');\n            segmentItem.style.padding = '15px';\n            segmentItem.style.backgroundColor = '#f9f9f9';\n            segmentItem.style.borderRadius = '8px';\n            segmentItem.style.border = '1px solid #eee';\n            const segmentHeader = document.createElement('div');\n            segmentHeader.style.display = 'flex';\n            segmentHeader.style.justifyContent = 'space-between';\n            segmentHeader.style.marginBottom = '10px';\n            segmentHeader.style.paddingBottom = '10px';\n            segmentHeader.style.borderBottom = '1px solid #eee';\n            const segmentTitle = document.createElement('div');\n            segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n            const segmentDuration = document.createElement('div');\n            segmentDuration.textContent = this.formatDuration(segment.duration);\n            segmentDuration.style.color = '#666';\n            segmentHeader.appendChild(segmentTitle);\n            segmentHeader.appendChild(segmentDuration);\n            segmentItem.appendChild(segmentHeader);\n            // Ajouter les détails du segment (similaire à la visualisation principale)\n            const segmentRoute = document.createElement('div');\n            segmentRoute.style.display = 'flex';\n            segmentRoute.style.alignItems = 'center';\n            segmentRoute.style.justifyContent = 'space-between';\n            // Départ du segment\n            const segmentDeparture = document.createElement('div');\n            segmentDeparture.style.flex = '1';\n            if (segment.departure) {\n              const depTime = document.createElement('div');\n              depTime.style.fontWeight = 'bold';\n              depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n              });\n              const depAirport = document.createElement('div');\n              depAirport.style.fontSize = '14px';\n              depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n              segmentDeparture.appendChild(depTime);\n              segmentDeparture.appendChild(depAirport);\n            }\n            // Flèche\n            const arrow = document.createElement('div');\n            arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n            // Arrivée du segment\n            const segmentArrival = document.createElement('div');\n            segmentArrival.style.flex = '1';\n            segmentArrival.style.textAlign = 'right';\n            if (segment.arrival) {\n              const arrTime = document.createElement('div');\n              arrTime.style.fontWeight = 'bold';\n              arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n              });\n              const arrAirport = document.createElement('div');\n              arrAirport.style.fontSize = '14px';\n              arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n              segmentArrival.appendChild(arrTime);\n              segmentArrival.appendChild(arrAirport);\n            }\n            segmentRoute.appendChild(segmentDeparture);\n            segmentRoute.appendChild(arrow);\n            segmentRoute.appendChild(segmentArrival);\n            segmentItem.appendChild(segmentRoute);\n            segmentsList.appendChild(segmentItem);\n            // Ajouter information d'escale si ce n'est pas le dernier segment\n            if (index < item.segments.length - 1) {\n              const layover = document.createElement('div');\n              layover.style.textAlign = 'center';\n              layover.style.padding = '10px';\n              layover.style.color = '#FF9800';\n              layover.style.fontSize = '14px';\n              // Calculer le temps d'escale\n              const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n              const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n              const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n              layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n              segmentsList.appendChild(layover);\n            }\n          });\n          routeSection.appendChild(segmentsList);\n        }\n      }\n      // 3. Section des offres\n      const offersSection = this.createSection('Offers', 'fa-tag');\n      if (flight.offers && flight.offers.length > 0) {\n        const offersList = document.createElement('div');\n        offersList.style.display = 'flex';\n        offersList.style.flexDirection = 'column';\n        offersList.style.gap = '15px';\n        flight.offers.forEach((offer, index) => {\n          const offerItem = document.createElement('div');\n          offerItem.style.padding = '15px';\n          offerItem.style.backgroundColor = '#f9f9f9';\n          offerItem.style.borderRadius = '8px';\n          offerItem.style.border = '1px solid #eee';\n          // En-tête de l'offre\n          const offerHeader = document.createElement('div');\n          offerHeader.style.display = 'flex';\n          offerHeader.style.justifyContent = 'space-between';\n          offerHeader.style.marginBottom = '15px';\n          offerHeader.style.paddingBottom = '10px';\n          offerHeader.style.borderBottom = '1px solid #eee';\n          const offerTitle = document.createElement('div');\n          offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n          offerTitle.style.fontSize = '16px';\n          const offerPrice = document.createElement('div');\n          offerPrice.style.fontSize = '18px';\n          offerPrice.style.fontWeight = 'bold';\n          offerPrice.style.color = '#2989d8';\n          offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n          offerHeader.appendChild(offerTitle);\n          offerHeader.appendChild(offerPrice);\n          offerItem.appendChild(offerHeader);\n          // Détails de l'offre\n          const offerDetails = document.createElement('div');\n          offerDetails.style.display = 'grid';\n          offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n          offerDetails.style.gap = '10px';\n          // Disponibilité\n          const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n          const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n          offerDetails.appendChild(availability);\n          // Date d'expiration\n          if (offer.expiresOn) {\n            const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n            offerDetails.appendChild(expires);\n          }\n          // Tarif de marque\n          if (offer.brandedFare) {\n            const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n            offerDetails.appendChild(brandedFare);\n          }\n          // Réservable\n          if (offer.reservableInfo) {\n            const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n            offerDetails.appendChild(reservable);\n          }\n          // Bagages\n          if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n            const baggageTitle = document.createElement('h4');\n            baggageTitle.textContent = 'Baggage Information';\n            baggageTitle.style.marginTop = '15px';\n            baggageTitle.style.marginBottom = '10px';\n            baggageTitle.style.fontSize = '14px';\n            baggageTitle.style.fontWeight = '600';\n            offerItem.appendChild(baggageTitle);\n            const baggageContainer = document.createElement('div');\n            baggageContainer.style.display = 'flex';\n            baggageContainer.style.flexDirection = 'column';\n            baggageContainer.style.gap = '10px';\n            baggageContainer.style.marginBottom = '15px';\n            // Filtrer et regrouper les bagages par type\n            const checkedBaggage = offer.baggageInformations.filter(b => b.baggageType === 2);\n            const cabinBaggage = offer.baggageInformations.filter(b => b.baggageType === 1);\n            const handBaggage = offer.baggageInformations.filter(b => b.baggageType === 3);\n            // Bagages en soute\n            if (checkedBaggage.length > 0) {\n              checkedBaggage.forEach(baggage => {\n                const baggageItem = document.createElement('div');\n                baggageItem.style.display = 'flex';\n                baggageItem.style.alignItems = 'center';\n                baggageItem.style.padding = '10px 15px';\n                baggageItem.style.backgroundColor = '#e7f5ff';\n                baggageItem.style.borderRadius = '6px';\n                baggageItem.style.border = '1px solid #c5e1f9';\n                const baggageIcon = document.createElement('div');\n                baggageIcon.innerHTML = '<i class=\"fas fa-suitcase\" style=\"color: #4a6fa5; font-size: 16px; margin-right: 10px;\"></i>';\n                const baggageInfo = document.createElement('div');\n                baggageInfo.style.display = 'flex';\n                baggageInfo.style.flexDirection = 'column';\n                const baggageType = document.createElement('div');\n                baggageType.textContent = 'Checked Baggage';\n                baggageType.style.fontWeight = '600';\n                baggageType.style.fontSize = '14px';\n                const baggageDetails = document.createElement('div');\n                baggageDetails.style.fontSize = '12px';\n                baggageDetails.style.color = '#666';\n                let detailsText = '';\n                if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n                if (baggage.piece > 0) {\n                  if (detailsText) detailsText += ' - ';\n                  detailsText += `${baggage.piece} piece(s)`;\n                }\n                baggageDetails.textContent = detailsText || 'Included';\n                baggageInfo.appendChild(baggageType);\n                baggageInfo.appendChild(baggageDetails);\n                baggageItem.appendChild(baggageIcon);\n                baggageItem.appendChild(baggageInfo);\n                baggageContainer.appendChild(baggageItem);\n              });\n            }\n            // Bagages cabine\n            if (cabinBaggage.length > 0) {\n              cabinBaggage.forEach(baggage => {\n                const baggageItem = document.createElement('div');\n                baggageItem.style.display = 'flex';\n                baggageItem.style.alignItems = 'center';\n                baggageItem.style.padding = '10px 15px';\n                baggageItem.style.backgroundColor = '#f3f0ff';\n                baggageItem.style.borderRadius = '6px';\n                baggageItem.style.border = '1px solid #e5dbff';\n                const baggageIcon = document.createElement('div');\n                baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n                const baggageInfo = document.createElement('div');\n                baggageInfo.style.display = 'flex';\n                baggageInfo.style.flexDirection = 'column';\n                const baggageType = document.createElement('div');\n                baggageType.textContent = 'Cabin Baggage';\n                baggageType.style.fontWeight = '600';\n                baggageType.style.fontSize = '14px';\n                const baggageDetails = document.createElement('div');\n                baggageDetails.style.fontSize = '12px';\n                baggageDetails.style.color = '#666';\n                let detailsText = '';\n                if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n                if (baggage.piece > 0) {\n                  if (detailsText) detailsText += ' - ';\n                  detailsText += `${baggage.piece} piece(s)`;\n                }\n                baggageDetails.textContent = detailsText || 'Included';\n                baggageInfo.appendChild(baggageType);\n                baggageInfo.appendChild(baggageDetails);\n                baggageItem.appendChild(baggageIcon);\n                baggageItem.appendChild(baggageInfo);\n                baggageContainer.appendChild(baggageItem);\n              });\n            } else {\n              // Ajouter un bagage cabine par défaut si aucun n'est spécifié\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#f3f0ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #e5dbff';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Cabin Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              baggageDetails.textContent = 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            }\n            // Bagages à main\n            if (handBaggage.length > 0) {\n              handBaggage.forEach(baggage => {\n                const baggageItem = document.createElement('div');\n                baggageItem.style.display = 'flex';\n                baggageItem.style.alignItems = 'center';\n                baggageItem.style.padding = '10px 15px';\n                baggageItem.style.backgroundColor = '#fff4e6';\n                baggageItem.style.borderRadius = '6px';\n                baggageItem.style.border = '1px solid #ffe8cc';\n                const baggageIcon = document.createElement('div');\n                baggageIcon.innerHTML = '<i class=\"fas fa-shopping-bag\" style=\"color: #e8590c; font-size: 16px; margin-right: 10px;\"></i>';\n                const baggageInfo = document.createElement('div');\n                baggageInfo.style.display = 'flex';\n                baggageInfo.style.flexDirection = 'column';\n                const baggageType = document.createElement('div');\n                baggageType.textContent = 'Hand Baggage';\n                baggageType.style.fontWeight = '600';\n                baggageType.style.fontSize = '14px';\n                const baggageDetails = document.createElement('div');\n                baggageDetails.style.fontSize = '12px';\n                baggageDetails.style.color = '#666';\n                let detailsText = '';\n                if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n                if (baggage.piece > 0) {\n                  if (detailsText) detailsText += ' - ';\n                  detailsText += `${baggage.piece} piece(s)`;\n                }\n                baggageDetails.textContent = detailsText || 'Included';\n                baggageInfo.appendChild(baggageType);\n                baggageInfo.appendChild(baggageDetails);\n                baggageItem.appendChild(baggageIcon);\n                baggageItem.appendChild(baggageInfo);\n                baggageContainer.appendChild(baggageItem);\n              });\n            }\n            offerItem.appendChild(baggageContainer);\n          }\n          offerItem.appendChild(offerDetails);\n          offersList.appendChild(offerItem);\n        });\n        offersSection.appendChild(offersList);\n      }\n      // 4. Section des services\n      if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n        const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n        const servicesList = document.createElement('ul');\n        servicesList.style.listStyle = 'none';\n        servicesList.style.padding = '0';\n        servicesList.style.margin = '0';\n        servicesList.style.display = 'grid';\n        servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        servicesList.style.gap = '10px';\n        flight.items[0].services.forEach(service => {\n          const serviceItem = document.createElement('li');\n          serviceItem.style.padding = '10px';\n          serviceItem.style.backgroundColor = '#f9f9f9';\n          serviceItem.style.borderRadius = '5px';\n          serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n          servicesList.appendChild(serviceItem);\n        });\n        servicesSection.appendChild(servicesList);\n        detailsContainer.appendChild(servicesSection);\n      }\n      // Ajouter les sections au conteneur principal\n      detailsContainer.appendChild(generalInfo);\n      detailsContainer.appendChild(routeSection);\n      detailsContainer.appendChild(offersSection);\n      // Assembler la fenêtre modale\n      modalContent.appendChild(closeButton);\n      modalContent.appendChild(header);\n      modalContent.appendChild(detailsContainer);\n      modalDiv.appendChild(modalContent);\n      // Ajouter la fenêtre modale au document\n      document.body.appendChild(modalDiv);\n    }\n    // Méthode utilitaire pour créer une section\n    createSection(title, iconClass) {\n      const section = document.createElement('div');\n      section.style.backgroundColor = '#f9f9f9';\n      section.style.borderRadius = '8px';\n      section.style.padding = '20px';\n      section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n      const sectionHeader = document.createElement('div');\n      sectionHeader.style.display = 'flex';\n      sectionHeader.style.alignItems = 'center';\n      sectionHeader.style.marginBottom = '15px';\n      const icon = document.createElement('i');\n      icon.className = `fas ${iconClass}`;\n      icon.style.color = '#2989d8';\n      icon.style.fontSize = '18px';\n      icon.style.marginRight = '10px';\n      const sectionTitle = document.createElement('h3');\n      sectionTitle.textContent = title;\n      sectionTitle.style.margin = '0';\n      sectionTitle.style.fontSize = '18px';\n      sectionTitle.style.fontWeight = '600';\n      sectionHeader.appendChild(icon);\n      sectionHeader.appendChild(sectionTitle);\n      section.appendChild(sectionHeader);\n      return section;\n    }\n    // Méthode utilitaire pour créer une ligne d'information\n    createInfoRow(label, value) {\n      const row = document.createElement('div');\n      row.style.marginBottom = '10px';\n      const labelElement = document.createElement('div');\n      labelElement.textContent = label;\n      labelElement.style.fontSize = '12px';\n      labelElement.style.color = '#666';\n      labelElement.style.marginBottom = '3px';\n      const valueElement = document.createElement('div');\n      valueElement.textContent = value;\n      valueElement.style.fontSize = '14px';\n      row.appendChild(labelElement);\n      row.appendChild(valueElement);\n      return row;\n    }\n    // Méthode pour sélectionner un vol et rediriger vers getoffers\n    selectThisFlight(flight) {\n      if (flight && flight.offers && flight.offers.length > 0) {\n        // Obtenir l'ID de l'offre\n        let offerId = flight.offers[0].offerId || flight.offers[0].id;\n        // Obtenir l'ID de recherche\n        let searchId = this.lastSearchId;\n        // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n        if (!searchId) {\n          searchId = flight.id;\n        }\n        // Obtenir les informations de passagers\n        const passengerCounts = this.sharedDataService.getPassengerCounts();\n        // Sérialiser les informations de passagers pour les passer dans l'URL\n        const passengerInfo = JSON.stringify(passengerCounts);\n        console.log('Navigating to get-offer with searchId:', searchId, 'offerId:', offerId, 'and passengers:', passengerInfo);\n        // Rediriger vers la page get-offer avec les informations de passagers\n        this.router.navigate(['/get-offer'], {\n          queryParams: {\n            searchId: searchId,\n            offerId: offerId,\n            passengers: passengerInfo\n          }\n        });\n      } else {\n        console.error('No offers available for this flight:', flight);\n      }\n    }\n    preloadLocations() {\n      // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n      // toutes les valeurs sans valeurs par défaut\n      // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n    }\n    setupAutocomplete() {\n      // Charger les locations par type par défaut\n      const departureLocationType = 2; // Type 2 (City) par défaut\n      const arrivalLocationType = 5; // Type 5 (Airport) par défaut\n      this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n        this.departureLocations = locations;\n      });\n      this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n      // Autocomplétion pour le lieu de départ\n      this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n        if (typeof value === 'string') {\n          // Si l'utilisateur tape quelque chose, filtrer les résultats\n          if (value.length > 0) {\n            // Filtrer les résultats par type et par texte\n            return this.productService.getLocationsByType(departureLocationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n          } else {\n            // Si le champ est vide, afficher toutes les options du type sélectionné\n            return this.productService.getLocationsByType(departureLocationType);\n          }\n        }\n        return of([]);\n      })).subscribe(locations => {\n        this.departureLocations = locations;\n      });\n      // Autocomplétion pour le lieu d'arrivée\n      this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n        if (typeof value === 'string') {\n          // Si l'utilisateur tape quelque chose, filtrer les résultats\n          if (value.length > 0) {\n            // Filtrer les résultats par type et par texte\n            return this.productService.getLocationsByType(arrivalLocationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n          } else {\n            // Si le champ est vide, afficher toutes les options du type sélectionné\n            return this.productService.getLocationsByType(arrivalLocationType);\n          }\n        }\n        return of([]);\n      })).subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n    }\n    displayLocation(location) {\n      if (!location) return '';\n      let displayText = location.name;\n      if (location.code) {\n        displayText += ` (${location.code})`;\n      }\n      if (location.type === LocationType.Airport && location.city) {\n        displayText += ` - ${location.city}`;\n      }\n      return displayText;\n    }\n    // Méthode pour changer le type de recherche (aller simple, aller-retour)\n    setSearchType(type) {\n      this.currentSearchType = type;\n      // Mettre à jour le type de service dans le formulaire\n      if (type === this.SEARCH_TYPE_ONE_WAY) {\n        this.searchForm.get('serviceTypes')?.setValue(['1']);\n        // Rendre le champ de date de retour optionnel\n        this.searchForm.get('returnDate')?.clearValidators();\n      } else if (type === this.SEARCH_TYPE_ROUND_TRIP) {\n        this.searchForm.get('serviceTypes')?.setValue(['2']);\n        // Rendre le champ de date de retour obligatoire\n        this.searchForm.get('returnDate')?.setValidators([Validators.required]);\n      }\n      // Mettre à jour les validateurs\n      this.searchForm.get('returnDate')?.updateValueAndValidity();\n    }\n    onSearch() {\n      if (this.searchForm.invalid) {\n        this.markFormGroupTouched(this.searchForm);\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      this.hasSearched = true;\n      const formValue = this.searchForm.value;\n      // Vérifier la valeur de la classe de vol et le type de recherche\n      console.log('Form values:', formValue);\n      console.log('Selected flight class:', formValue.flightClass);\n      console.log('Current search type:', this.currentSearchType);\n      // Créer la requête en fonction du type de recherche (aller simple ou aller-retour)\n      let request;\n      if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n        // Requête pour vol aller simple\n        request = {\n          ProductType: formValue.productType,\n          ServiceTypes: ['1'],\n          CheckIn: formValue.departureDate,\n          DepartureLocations: [{\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }],\n\n          ArrivalLocations: [{\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }],\n\n          Passengers: this.getPassengersArray(),\n          showOnlyNonStopFlight: formValue.nonStop,\n          additionalParameters: {\n            getOptionsParameters: {\n              flightBaggageGetOption: formValue.flightBaggageGetOption\n            }\n          },\n          acceptPendingProviders: formValue.acceptPendingProviders,\n          forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n          disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n          calculateFlightFees: formValue.calculateFlightFees,\n          flightClasses: [formValue.flightClass],\n          Culture: formValue.culture,\n          Currency: formValue.currency\n        };\n        console.log('Sending one-way search request with ServiceTypes:', request.ServiceTypes);\n      } else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n        // Requête pour vol aller-retour\n        request = {\n          ProductType: formValue.productType,\n          ServiceTypes: ['2'],\n          CheckIn: formValue.departureDate,\n          ReturnDate: formValue.returnDate,\n          DepartureLocations: [{\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }],\n\n          ArrivalLocations: [{\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }],\n\n          Passengers: this.getPassengersArray(),\n          showOnlyNonStopFlight: formValue.nonStop,\n          additionalParameters: {\n            getOptionsParameters: {\n              flightBaggageGetOption: formValue.flightBaggageGetOption\n            }\n          },\n          acceptPendingProviders: formValue.acceptPendingProviders,\n          forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n          disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n          calculateFlightFees: formValue.calculateFlightFees,\n          flightClasses: [formValue.flightClass],\n          Culture: formValue.culture,\n          Currency: formValue.currency\n        };\n        console.log('Sending round-trip search request with ServiceTypes:', request.ServiceTypes);\n      }\n      // Créer la requête appropriée selon le type de recherche\n      if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n        // Requête pour vol aller simple\n        request = {\n          ProductType: formValue.productType,\n          ServiceTypes: ['1'],\n          CheckIn: formValue.departureDate,\n          DepartureLocations: [{\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }],\n\n          ArrivalLocations: [{\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }],\n\n          Passengers: this.getPassengersArray(),\n          showOnlyNonStopFlight: formValue.nonStop,\n          additionalParameters: {\n            getOptionsParameters: {\n              flightBaggageGetOption: formValue.flightBaggageGetOption\n            }\n          },\n          acceptPendingProviders: formValue.acceptPendingProviders,\n          forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n          disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n          calculateFlightFees: formValue.calculateFlightFees,\n          flightClasses: [formValue.flightClass],\n          Culture: formValue.culture,\n          Currency: formValue.currency\n        };\n        console.log('Sending one-way search request with ServiceTypes:', request.ServiceTypes);\n      } else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n        // Requête pour vol aller-retour\n        request = {\n          ProductType: formValue.productType,\n          ServiceTypes: ['2'],\n          CheckIn: formValue.departureDate,\n          ReturnDate: formValue.returnDate,\n          DepartureLocations: [{\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }],\n\n          ArrivalLocations: [{\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }],\n\n          Passengers: this.getPassengersArray(),\n          showOnlyNonStopFlight: formValue.nonStop,\n          additionalParameters: {\n            getOptionsParameters: {\n              flightBaggageGetOption: formValue.flightBaggageGetOption\n            }\n          },\n          acceptPendingProviders: formValue.acceptPendingProviders,\n          forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n          disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n          calculateFlightFees: formValue.calculateFlightFees,\n          flightClasses: [formValue.flightClass],\n          Culture: formValue.culture,\n          Currency: formValue.currency\n        };\n        console.log('Sending round-trip search request with ServiceTypes:', request.ServiceTypes);\n      }\n      // Utiliser la méthode searchPrice pour envoyer la requête\n      this.productService.searchPrice(request).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            // Vérifier si c'est une recherche aller-retour en examinant les vols\n            this.isRoundTripResponse = response.body.flights.some(flight => flight.items && flight.items.some(item => item.route === 2));\n            console.log('Is round trip response:', this.isRoundTripResponse);\n            this.searchResults = response.body.flights;\n            // Analyser les classes de vol dans les résultats\n            console.group('Flight Class Analysis');\n            console.log('Selected flight class:', formValue.flightClass);\n            console.log('Total flights received:', response.body.flights.length);\n            // Compter les vols par classe\n            const flightsByClass = {\n              [FlightClassType.PROMO.toString()]: 0,\n              [FlightClassType.ECONOMY.toString()]: 0,\n              [FlightClassType.BUSINESS.toString()]: 0,\n              'unknown': 0\n            };\n            response.body.flights.forEach((flight, index) => {\n              if (flight.items && flight.items.length > 0 && flight.items[0].flightClass) {\n                const classType = flight.items[0].flightClass.type !== undefined ? flight.items[0].flightClass.type.toString() : 'unknown';\n                if (flightsByClass[classType] !== undefined) {\n                  flightsByClass[classType]++;\n                } else {\n                  flightsByClass['unknown']++;\n                }\n                // Afficher les détails de classe pour chaque vol\n                console.log(`Flight ${flight.id} class:`, flight.items[0].flightClass ? `${flight.items[0].flightClass.name} (type: ${flight.items[0].flightClass.type})` : 'No class info');\n                // Afficher la structure complète du premier vol pour analyse\n                if (index === 0) {\n                  console.group('First Flight Structure');\n                  console.log('Flight ID:', flight.id);\n                  console.log('Flight Items:', flight.items);\n                  if (flight.items && flight.items.length > 0) {\n                    console.log('First Item FlightClass:', flight.items[0].flightClass);\n                    if (flight.items[0].segments) {\n                      console.log('Segments:', flight.items[0].segments);\n                      flight.items[0].segments.forEach((segment, segIndex) => {\n                        console.log(`Segment ${segIndex} FlightClass:`, segment.flightClass);\n                      });\n                    }\n                  }\n                  if (flight.offers && flight.offers.length > 0) {\n                    console.log('First Offer:', flight.offers[0]);\n                    console.log('FlightClassInformations:', flight.offers[0].flightClassInformations);\n                  }\n                  console.groupEnd();\n                }\n              } else {\n                flightsByClass['unknown']++;\n              }\n            });\n            console.log('Flights by class:', flightsByClass);\n            console.groupEnd();\n            // Calculer les prix minimums pour chaque option de filtre\n            this.calculateFilterPrices();\n            // Appliquer le filtre actuel aux résultats\n            this.applyAllFilters();\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            // console.log('Full API Response:', JSON.stringify(response, null, 2));\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability !== undefined ? o.availability : o.seatInfo ? o.seatInfo.availableSeatCount : 0));\n              console.log('Availability values:', availabilityValues);\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc, val) => {\n                if (val !== undefined) {\n                  acc[val] = (acc[val] || 0) + 1;\n                }\n                return acc;\n              }, {});\n              console.log('Availability counts:', availabilityCounts);\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true));\n              console.log('Reservable flights:', reservableFlights.length);\n              console.groupEnd();\n            }\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n    }\n    // Utilitaire pour marquer tous les champs comme touchés\n    markFormGroupTouched(formGroup) {\n      Object.values(formGroup.controls).forEach(control => {\n        control.markAsTouched();\n        if (control instanceof FormGroup) {\n          this.markFormGroupTouched(control);\n        }\n      });\n    }\n    // Formater la durée en heures et minutes\n    formatDuration(minutes) {\n      const hours = Math.floor(minutes / 60);\n      const mins = minutes % 60;\n      return `${hours}h ${mins}min`;\n    }\n    // Formater la date pour l'affichage\n    formatDate(dateString) {\n      if (!dateString) return 'N/A';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('fr-FR', {\n        weekday: 'short',\n        day: '2-digit',\n        month: 'short',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    // Formater la date pour l'affichage avec indication si elle diffère de la date demandée\n    formatDateWithRequestComparison(dateString, requestedDate) {\n      if (!dateString) return 'N/A';\n      const date = new Date(dateString);\n      const formatted = date.toLocaleDateString('fr-FR', {\n        weekday: 'short',\n        day: '2-digit',\n        month: 'short',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n      // Si une date demandée est fournie, vérifier si la date réelle est différente\n      if (requestedDate) {\n        const requested = new Date(requestedDate);\n        // Comparer seulement les dates (sans l'heure)\n        const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n        const requestedOnly = new Date(requested.getFullYear(), requested.getMonth(), requested.getDate());\n        if (dateOnly.getTime() !== requestedOnly.getTime()) {\n          // Ajouter une indication visuelle que la date diffère de celle demandée\n          return `${formatted} <span class=\"date-differs\" title=\"Differs from requested date: ${requestedOnly.toLocaleDateString('fr-FR')}\">*</span>`;\n        }\n      }\n      return formatted;\n    }\n    // Obtenir les dates de départ et de retour d'un vol aller-retour\n    getFlightDates(flight) {\n      const result = {\n        outbound: 'N/A',\n        inbound: 'N/A'\n      };\n      if (!flight || !flight.items || flight.items.length === 0) {\n        return result;\n      }\n      // Chercher les segments aller (outbound)\n      const outboundItems = flight.items.filter(item => item.segmentNumber === 1 || item.route === 1);\n      if (outboundItems.length > 0 && outboundItems[0].departure && outboundItems[0].departure.date) {\n        result.outbound = outboundItems[0].departure.date;\n      } else if (flight.items[0] && flight.items[0].departure && flight.items[0].departure.date) {\n        result.outbound = flight.items[0].departure.date;\n      }\n      // Chercher les segments retour (inbound)\n      const inboundItems = flight.items.filter(item => item.segmentNumber === 2 || item.route === 2);\n      if (inboundItems.length > 0 && inboundItems[0].departure && inboundItems[0].departure.date) {\n        result.inbound = inboundItems[0].departure.date;\n      } else if (flight.items.length > 1 && flight.items[1] && flight.items[1].departure && flight.items[1].departure.date) {\n        result.inbound = flight.items[1].departure.date;\n      }\n      return result;\n    }\n    // Obtenir le prix minimum pour un vol\n    getMinPrice(flight) {\n      if (!flight.offers || flight.offers.length === 0) {\n        return 'N/A';\n      }\n      const minOffer = flight.offers.reduce((min, offer) => offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n      return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n    }\n    // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n    isFlightAvailable(flight) {\n      // Vérifier si le vol a des offres\n      if (!flight.offers || flight.offers.length === 0) {\n        return false;\n      }\n      // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n      const offer = flight.offers[0];\n      const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n      return availabilityValue > 0;\n    }\n    // Vérifier si les dates réelles du vol correspondent aux dates demandées\n    checkFlightDatesMatch(flight, requestedDepartureDate, requestedReturnDate) {\n      const result = {\n        match: true,\n        message: ''\n      };\n      if (!flight || !flight.items || flight.items.length === 0) {\n        return result;\n      }\n      // Obtenir les dates réelles du vol\n      const flightDates = this.getFlightDates(flight);\n      // Comparer la date de départ\n      if (requestedDepartureDate && flightDates.outbound !== 'N/A') {\n        const requestedDepDate = new Date(requestedDepartureDate);\n        const actualDepDate = new Date(flightDates.outbound);\n        // Comparer seulement les dates (sans l'heure)\n        const requestedDepDay = new Date(requestedDepDate.getFullYear(), requestedDepDate.getMonth(), requestedDepDate.getDate());\n        const actualDepDay = new Date(actualDepDate.getFullYear(), actualDepDate.getMonth(), actualDepDate.getDate());\n        if (requestedDepDay.getTime() !== actualDepDay.getTime()) {\n          result.match = false;\n          result.message += `Departure date differs from requested: ${actualDepDay.toLocaleDateString()} instead of ${requestedDepDay.toLocaleDateString()}. `;\n        }\n      }\n      // Comparer la date de retour (si c'est un vol aller-retour)\n      if (requestedReturnDate && flightDates.inbound !== 'N/A') {\n        const requestedRetDate = new Date(requestedReturnDate);\n        const actualRetDate = new Date(flightDates.inbound);\n        // Comparer seulement les dates (sans l'heure)\n        const requestedRetDay = new Date(requestedRetDate.getFullYear(), requestedRetDate.getMonth(), requestedRetDate.getDate());\n        const actualRetDay = new Date(actualRetDate.getFullYear(), actualRetDate.getMonth(), actualRetDate.getDate());\n        if (requestedRetDay.getTime() !== actualRetDay.getTime()) {\n          result.match = false;\n          result.message += `Return date differs from requested: ${actualRetDay.toLocaleDateString()} instead of ${requestedRetDay.toLocaleDateString()}.`;\n        }\n      }\n      return result;\n    }\n    // Afficher un message d'information sur les dates des vols\n    getFlightDateInfoMessage(flight, requestedDepartureDate, requestedReturnDate) {\n      // Vérifier si c'est un vol aller-retour\n      const isRoundTrip = this.isRoundTripFlight(flight);\n      // Obtenir les dates réelles du vol\n      const flightDates = this.getFlightDates(flight);\n      // Vérifier si les dates correspondent\n      const dateCheck = this.checkFlightDatesMatch(flight, requestedDepartureDate, requestedReturnDate);\n      if (!dateCheck.match) {\n        // Si les dates ne correspondent pas, afficher un message d'information\n        return `<div class=\"date-info-message\">\n                <i class=\"fas fa-info-circle\"></i>\n                <span>The actual flight dates differ from your search criteria. ${dateCheck.message}</span>\n              </div>`;\n      } else if (isRoundTrip) {\n        // Si c'est un vol aller-retour avec des dates correspondantes\n        return `<div class=\"date-info-message date-info-success\">\n                <i class=\"fas fa-check-circle\"></i>\n                <span>Round-trip flight: Outbound on ${new Date(flightDates.outbound).toLocaleDateString()}, Return on ${new Date(flightDates.inbound).toLocaleDateString()}</span>\n              </div>`;\n      }\n      return '';\n    }\n    // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n    showAllDepartureLocations() {\n      const locationType = 2; // Type 2 (City) par défaut\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.departureLocations = locations;\n        // Forcer l'ouverture du menu d'autocomplétion\n        const input = document.getElementById('departureLocation');\n        if (input) {\n          input.focus();\n          input.dispatchEvent(new Event('input'));\n        }\n      });\n    }\n    // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n    showAllArrivalLocations() {\n      const locationType = 5; // Type 5 (Airport) par défaut\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.arrivalLocations = locations;\n        // Forcer l'ouverture du menu d'autocomplétion\n        const input = document.getElementById('arrivalLocation');\n        if (input) {\n          input.focus();\n          input.dispatchEvent(new Event('input'));\n        }\n      });\n    }\n    // Échanger les emplacements de départ et d'arrivée\n    swapLocations() {\n      const departureLocation = this.searchForm.get('departureLocation')?.value;\n      const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n      this.searchForm.patchValue({\n        departureLocation: arrivalLocation,\n        arrivalLocation: departureLocation\n      });\n    }\n    // Afficher la date d'expiration telle qu'elle est fournie par l'API\n    formatExpirationDate(dateString) {\n      if (!dateString) return 'N/A';\n      // Afficher la date au format local\n      const date = new Date(dateString);\n      return date.toLocaleString();\n    }\n    // Obtenir le nom du type de bagage\n    getBaggageTypeName(baggageType) {\n      switch (baggageType) {\n        case 1:\n          return 'Cabin Baggage';\n        case 2:\n          return 'Checked Baggage';\n        case 3:\n          return 'Hand Baggage';\n        default:\n          return 'Baggage';\n      }\n    }\n    // Obtenir le nom du service\n    getServiceName(service) {\n      if (!service) return 'Service';\n      return service.name || 'Service';\n    }\n    // Filtrer les bagages par type\n    filterBaggageByType(baggageInformations, type) {\n      if (!baggageInformations || !Array.isArray(baggageInformations)) {\n        return [];\n      }\n      return baggageInformations.filter(baggage => baggage.baggageType === type);\n    }\n    // Obtenir le nom du type de passager\n    getPassengerTypeName(passengerType) {\n      switch (passengerType) {\n        case 1:\n          return 'Adult';\n        case 2:\n          return 'Child';\n        case 3:\n          return 'Infant';\n        default:\n          return 'Passenger';\n      }\n    }\n    // Calculer le temps d'escale entre deux segments\n    calculateLayoverTime(currentSegment, nextSegment) {\n      if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date || !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n        return 'Unknown';\n      }\n      const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n      const departureTime = new Date(nextSegment.departure.date).getTime();\n      const diffMs = departureTime - arrivalTime;\n      const diffMins = Math.floor(diffMs / (1000 * 60));\n      if (diffMins < 60) {\n        return `${diffMins}min`;\n      } else {\n        const hours = Math.floor(diffMins / 60);\n        const mins = diffMins % 60;\n        return `${hours}h ${mins}min`;\n      }\n    }\n    // Calculer la durée du séjour entre deux dates\n    calculateStayDuration(outboundArrivalDate, inboundDepartureDate) {\n      if (!outboundArrivalDate || !inboundDepartureDate) {\n        return 'N/A';\n      }\n      const arrival = new Date(outboundArrivalDate);\n      const departure = new Date(inboundDepartureDate);\n      // Calculer la différence en jours\n      const diffTime = Math.abs(departure.getTime() - arrival.getTime());\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      if (diffDays === 0) {\n        return 'Same day';\n      } else if (diffDays === 1) {\n        return '1 day';\n      } else {\n        return `${diffDays} days`;\n      }\n    }\n    // Détecter si un vol est un aller-retour\n    isRoundTripFlight(flight) {\n      if (!flight || !flight.items || flight.items.length === 0) {\n        return false;\n      }\n      // Vérifier si la réponse est de type aller-retour\n      if (this.isRoundTripResponse) {\n        return true;\n      }\n      // Méthode 1: Vérifier le segmentNumber (1 = aller, 2 = retour)\n      // Dans la réponse API, les vols aller-retour ont souvent un item avec segmentNumber = 2\n      if (flight.items.some(item => item.segmentNumber === 2)) {\n        console.log(`Flight ${flight.id} identified as round-trip by segmentNumber`);\n        return true;\n      }\n      // Méthode 2: Vérifier la route (1 = aller, 2 = retour)\n      if (flight.items.some(item => item.route === 2)) {\n        console.log(`Flight ${flight.id} identified as round-trip by route`);\n        return true;\n      }\n      // Méthode 3: Vérifier si le vol a plusieurs items (aller et retour)\n      if (flight.items.length > 1) {\n        console.log(`Flight ${flight.id} identified as round-trip by multiple items`);\n        return true;\n      }\n      // Méthode 3: Vérifier si le premier item a des segments qui forment un aller-retour\n      if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n        const firstSegment = flight.items[0].segments[0];\n        const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n        // Vérifier si le premier segment part de A vers B et le dernier segment part de B vers A\n        if (firstSegment && firstSegment.departure && firstSegment.arrival && lastSegment && lastSegment.departure && lastSegment.arrival) {\n          const firstDeparture = firstSegment.departure.airport?.code || firstSegment.departure.city?.name;\n          const firstArrival = firstSegment.arrival.airport?.code || firstSegment.arrival.city?.name;\n          const lastDeparture = lastSegment.departure.airport?.code || lastSegment.departure.city?.name;\n          const lastArrival = lastSegment.arrival.airport?.code || lastSegment.arrival.city?.name;\n          // Si le dernier segment revient au point de départ du premier segment (boucle)\n          if (firstDeparture && lastArrival && firstDeparture === lastArrival) {\n            console.log(`Flight ${flight.id} identified as round-trip by segment loop`);\n            return true;\n          }\n          // Vérifier si c'est un aller-retour classique (A→B, B→A)\n          if (firstDeparture && firstArrival && lastDeparture && lastArrival && firstDeparture === lastArrival && firstArrival === lastDeparture) {\n            console.log(`Flight ${flight.id} identified as round-trip by segment pattern`);\n            return true;\n          }\n        }\n      }\n      // Méthode 4: Vérifier si le vol a été recherché avec ServiceTypes = 2 (aller-retour)\n      if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n        console.log(`Flight ${flight.id} identified as round-trip by search type`);\n        return true;\n      }\n      return false;\n    }\n    // Obtenir les segments aller d'un vol aller-retour\n    getOutboundSegments(flight) {\n      if (!flight.items) return [];\n      // Méthode 1: Utiliser la route pour identifier les segments aller (route = 1)\n      const outboundItemsByRoute = flight.items.filter(item => item.route === 1);\n      if (outboundItemsByRoute.length > 0) {\n        console.log(`Flight ${flight.id} outbound segments identified by route`);\n        return outboundItemsByRoute[0].segments || [outboundItemsByRoute[0]];\n      }\n      // Méthode 2: Utiliser le segmentNumber pour identifier les segments aller (segmentNumber = 1)\n      const outboundItems = flight.items.filter(item => item.segmentNumber === 1);\n      if (outboundItems.length > 0) {\n        console.log(`Flight ${flight.id} outbound segments identified by segmentNumber`);\n        return outboundItems[0].segments || [outboundItems[0]];\n      }\n      // Méthode 2: Si le vol a plusieurs items, le premier est généralement l'aller\n      if (flight.items.length > 1) {\n        console.log(`Flight ${flight.id} outbound segments identified by multiple items`);\n        return flight.items[0].segments || [flight.items[0]];\n      }\n      // Méthode 3: Si le vol a un seul item avec plusieurs segments\n      if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n        // Pour un aller-retour, on considère la première moitié des segments comme l'aller\n        const halfLength = Math.ceil(flight.items[0].segments.length / 2);\n        console.log(`Flight ${flight.id} outbound segments identified by splitting segments`);\n        return flight.items[0].segments.slice(0, halfLength);\n      }\n      // Par défaut, retourner tous les segments du premier item\n      return flight.items[0].segments || [flight.items[0]];\n    }\n    // Obtenir les segments retour d'un vol aller-retour\n    getInboundSegments(flight) {\n      if (!flight.items) return [];\n      // Méthode 1: Utiliser la route pour identifier les segments retour (route = 2)\n      const inboundItemsByRoute = flight.items.filter(item => item.route === 2);\n      if (inboundItemsByRoute.length > 0) {\n        console.log(`Flight ${flight.id} inbound segments identified by route`);\n        return inboundItemsByRoute[0].segments || [inboundItemsByRoute[0]];\n      }\n      // Méthode 2: Utiliser le segmentNumber pour identifier les segments retour (segmentNumber = 2)\n      const inboundItems = flight.items.filter(item => item.segmentNumber === 2);\n      if (inboundItems.length > 0) {\n        console.log(`Flight ${flight.id} inbound segments identified by segmentNumber`);\n        return inboundItems[0].segments || [inboundItems[0]];\n      }\n      // Méthode 2: Si le vol a plusieurs items, le second est généralement le retour\n      if (flight.items.length > 1) {\n        console.log(`Flight ${flight.id} inbound segments identified by multiple items`);\n        return flight.items[1].segments || [flight.items[1]];\n      }\n      // Méthode 3: Si le vol a un seul item avec plusieurs segments\n      if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n        // Pour un aller-retour, on considère la seconde moitié des segments comme le retour\n        const halfLength = Math.ceil(flight.items[0].segments.length / 2);\n        console.log(`Flight ${flight.id} inbound segments identified by splitting segments`);\n        return flight.items[0].segments.slice(halfLength);\n      }\n      // Si aucun segment retour n'est trouvé\n      return [];\n    }\n    static {\n      this.ɵfac = function SearchPriceComponent_Factory(t) {\n        return new (t || SearchPriceComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.SharedDataService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SearchPriceComponent,\n        selectors: [[\"app-search-price\"]],\n        hostBindings: function SearchPriceComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function SearchPriceComponent_click_HostBindingHandler($event) {\n              return ctx.onDocumentClick($event);\n            }, false, i0.ɵɵresolveDocument);\n          }\n        },\n        decls: 169,\n        vars: 30,\n        consts: [[1, \"search-price-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-illustration\"], [\"src\", \"assets/images/airplane-banner.jpg\", \"alt\", \"Airplane in the sky\"], [1, \"search-content\"], [1, \"search-form-container\"], [1, \"sidebar-logo\"], [1, \"logo-container\"], [1, \"fas\", \"fa-plane-departure\", \"logo-icon\"], [1, \"logo-text\"], [1, \"search-form-header\"], [1, \"header-text\"], [1, \"flight-type-tabs\"], [\"type\", \"button\", 1, \"flight-type-tab\", 3, \"click\"], [1, \"fas\", \"fa-long-arrow-alt-right\"], [1, \"fas\", \"fa-exchange-alt\"], [1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"passenger-class-options\"], [1, \"form-group\", \"passengers-selector\"], [\"for\", \"passengers\"], [1, \"input-with-icon\", 3, \"click\"], [1, \"fas\", \"fa-user-friends\"], [1, \"passengers-display\", \"form-control\"], [1, \"fas\", \"fa-chevron-down\"], [1, \"passengers-dropdown\"], [\"class\", \"passenger-type-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"passengers-dropdown-footer\"], [\"type\", \"button\", 1, \"apply-btn\", 3, \"click\"], [1, \"form-group\"], [\"for\", \"flightClass\"], [1, \"input-with-icon\"], [1, \"fas\", \"fa-chair\"], [\"id\", \"flightClass\", \"formControlName\", \"flightClass\", 1, \"form-control\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"checkbox-group\"], [\"for\", \"nonStop\"], [1, \"toggle-switch\", \"small\"], [\"type\", \"checkbox\", \"id\", \"nonStop\", \"formControlName\", \"nonStop\", 1, \"toggle-input\"], [\"for\", \"nonStop\", 1, \"toggle-label\"], [1, \"toggle-inner\"], [1, \"search-card\"], [\"type\", \"hidden\", \"formControlName\", \"productType\", \"value\", \"3\"], [\"type\", \"hidden\", \"formControlName\", \"serviceTypes\", \"value\", \"['1']\"], [1, \"single-line-form\"], [\"for\", \"departureLocation\"], [1, \"fas\", \"fa-plane-departure\"], [\"type\", \"text\", \"id\", \"departureLocation\", \"formControlName\", \"departureLocation\", \"placeholder\", \"City or airport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [3, \"displayWith\"], [\"departureAuto\", \"matAutocomplete\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"swap-button-container\"], [\"type\", \"button\", \"title\", \"Swap departure and arrival locations\", 1, \"swap-locations-btn\", 3, \"click\"], [\"for\", \"arrivalLocation\"], [1, \"fas\", \"fa-plane-arrival\"], [\"type\", \"text\", \"id\", \"arrivalLocation\", \"formControlName\", \"arrivalLocation\", \"placeholder\", \"City or airport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [\"arrivalAuto\", \"matAutocomplete\"], [\"for\", \"departureDate\"], [1, \"fas\", \"fa-calendar-alt\"], [\"type\", \"date\", \"id\", \"departureDate\", \"formControlName\", \"departureDate\", 1, \"form-control\", 3, \"min\"], [\"class\", \"form-group\", 4, \"ngIf\"], [1, \"search-button-container\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [\"class\", \"fas fa-search\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [1, \"advanced-options-container\"], [1, \"fas\", \"fa-cog\"], [1, \"advanced-options\"], [1, \"form-row\"], [\"for\", \"culture\"], [1, \"fas\", \"fa-language\"], [\"id\", \"culture\", \"formControlName\", \"culture\", 1, \"form-control\"], [\"value\", \"en-US\"], [\"value\", \"fr-FR\"], [\"for\", \"currency\"], [1, \"fas\", \"fa-money-bill-wave\"], [\"id\", \"currency\", \"formControlName\", \"currency\", 1, \"form-control\"], [\"value\", \"EUR\"], [\"value\", \"USD\"], [\"for\", \"flightBaggageGetOption\"], [1, \"fas\", \"fa-suitcase\"], [\"id\", \"flightBaggageGetOption\", \"formControlName\", \"flightBaggageGetOption\", 1, \"form-control\"], [3, \"value\"], [1, \"form-row\", \"checkbox-options\"], [\"type\", \"checkbox\", \"id\", \"acceptPendingProviders\", \"formControlName\", \"acceptPendingProviders\", 1, \"toggle-input\"], [\"for\", \"acceptPendingProviders\", 1, \"toggle-label\"], [1, \"toggle-switch-label\"], [\"type\", \"checkbox\", \"id\", \"forceFlightBundlePackage\", \"formControlName\", \"forceFlightBundlePackage\", 1, \"toggle-input\"], [\"for\", \"forceFlightBundlePackage\", 1, \"toggle-label\"], [\"type\", \"checkbox\", \"id\", \"disablePackageOfferTotalPrice\", \"formControlName\", \"disablePackageOfferTotalPrice\", 1, \"toggle-input\"], [\"for\", \"disablePackageOfferTotalPrice\", 1, \"toggle-label\"], [\"type\", \"checkbox\", \"id\", \"calculateFlightFees\", \"formControlName\", \"calculateFlightFees\", 1, \"toggle-input\"], [\"for\", \"calculateFlightFees\", 1, \"toggle-label\"], [\"class\", \"search-results-container\", 4, \"ngIf\"], [1, \"passenger-type-row\"], [1, \"passenger-type-info\"], [1, \"passenger-type-name\"], [1, \"passenger-type-description\"], [1, \"passenger-count-control\"], [\"type\", \"button\", 1, \"passenger-count-btn\", \"decrease\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-minus\"], [1, \"passenger-count\"], [\"type\", \"button\", 1, \"passenger-count-btn\", \"increase\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"location-option\"], [1, \"location-name\"], [1, \"location-details\"], [\"class\", \"location-code\", 4, \"ngIf\"], [\"class\", \"location-city\", 4, \"ngIf\"], [1, \"location-type\"], [1, \"fas\", 3, \"ngClass\"], [1, \"location-code\"], [1, \"location-city\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-circle\"], [\"for\", \"returnDate\"], [\"type\", \"date\", \"id\", \"returnDate\", \"formControlName\", \"returnDate\", 1, \"form-control\", 3, \"min\"], [1, \"fas\", \"fa-search\"], [1, \"spinner-container\"], [1, \"spinner\"], [1, \"search-results-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"search-results-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-animation\"], [1, \"plane-loader\"], [1, \"fas\", \"fa-plane\"], [1, \"cloud\"], [1, \"error-container\"], [1, \"error-icon\"], [1, \"retry-button\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"search-results-content\"], [1, \"results-header\"], [1, \"results-title\"], [\"class\", \"results-filters\", 4, \"ngIf\"], [1, \"search-results-layout\"], [\"class\", \"sidebar-filters\", 4, \"ngIf\"], [1, \"flight-list\"], [\"class\", \"flight-card\", 3, \"unavailable\", \"round-trip\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"results-count\"], [\"class\", \"filter-label\", 4, \"ngIf\"], [1, \"filter-label\"], [1, \"results-filters\"], [1, \"filter-buttons\"], [\"class\", \"filter-button\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"filter-button\", 3, \"click\"], [1, \"sidebar-filters\"], [1, \"sidebar-header\"], [1, \"clear-filters-btn\", 3, \"click\"], [1, \"filter-section\"], [1, \"filter-section-header\", 3, \"click\"], [1, \"filter-section-content\"], [1, \"filter-option\"], [1, \"checkbox-container\"], [\"type\", \"checkbox\", 3, \"checked\", \"change\"], [1, \"checkmark\"], [1, \"option-label\"], [\"class\", \"option-price\", 4, \"ngIf\"], [1, \"time-label\"], [\"class\", \"filter-section\", 4, \"ngIf\"], [1, \"option-price\"], [\"class\", \"filter-option\", 4, \"ngFor\", \"ngForOf\"], [1, \"flight-card\"], [\"class\", \"round-trip-badge\", 4, \"ngIf\"], [1, \"flight-header\"], [1, \"airline-info\"], [1, \"airline-logo-container\"], [\"alt\", \"Airline logo\", \"class\", \"airline-logo\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"fas fa-plane airline-icon\", 4, \"ngIf\"], [1, \"airline-details\"], [1, \"airline-name\"], [1, \"flight-number\"], [\"class\", \"provider-info\", 4, \"ngIf\"], [1, \"flight-badges\"], [\"class\", \"flight-badge\", 4, \"ngIf\"], [\"class\", \"flight-badge branded\", 4, \"ngIf\"], [1, \"flight-price\"], [1, \"price-label\"], [1, \"price\"], [\"class\", \"availability\", 4, \"ngIf\"], [\"class\", \"expiration\", 4, \"ngIf\"], [\"class\", \"flight-details\", 4, \"ngIf\"], [\"class\", \"flight-details round-trip-details\", 4, \"ngIf\"], [1, \"flight-features\"], [\"class\", \"feature-group\", 4, \"ngIf\"], [1, \"price-breakdown-section\"], [1, \"price-breakdown-details\"], [1, \"price-breakdown-summary\"], [\"class\", \"price-breakdown-content\", 4, \"ngIf\"], [\"class\", \"segments-section\", 4, \"ngIf\"], [\"class\", \"branded-fare-section\", 4, \"ngIf\"], [1, \"flight-actions\"], [1, \"view-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [1, \"select-button\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-check-circle\"], [1, \"round-trip-badge\"], [\"alt\", \"Airline logo\", 1, \"airline-logo\", 3, \"src\"], [1, \"fas\", \"fa-plane\", \"airline-icon\"], [1, \"provider-info\"], [1, \"fas\", \"fa-tag\"], [1, \"flight-badge\"], [1, \"fas\", \"fa-bolt\"], [1, \"flight-badge\", \"branded\"], [1, \"fas\", \"fa-certificate\"], [1, \"availability\"], [1, \"expiration\"], [1, \"fas\", \"fa-clock\"], [1, \"flight-details\"], [1, \"flight-route\"], [1, \"departure\"], [1, \"time\"], [1, \"location\"], [1, \"airport-code\"], [1, \"city-name\"], [1, \"flight-duration\"], [1, \"duration-line\"], [1, \"dot\", \"departure-dot\"], [1, \"line-container\"], [1, \"line\"], [1, \"plane-icon\"], [1, \"dot\", \"arrival-dot\"], [1, \"duration-text\"], [\"class\", \"stops\", 4, \"ngIf\"], [\"class\", \"stops direct\", 4, \"ngIf\"], [1, \"arrival\"], [1, \"stops\"], [1, \"stop-count\"], [1, \"stops\", \"direct\"], [1, \"flight-details\", \"round-trip-details\"], [1, \"date-info-container\", 3, \"innerHTML\"], [1, \"round-trip-section\", \"outbound\"], [1, \"round-trip-header\"], [1, \"round-trip-label\"], [1, \"time\", 3, \"innerHTML\"], [\"class\", \"round-trip-section inbound\", 4, \"ngIf\"], [\"class\", \"stay-duration\", 4, \"ngIf\"], [1, \"round-trip-section\", \"inbound\"], [1, \"stay-duration\"], [1, \"fas\", \"fa-hotel\"], [1, \"feature-group\"], [1, \"baggage-details\"], [\"class\", \"baggage-item cabin\", 4, \"ngIf\"], [\"class\", \"feature\", 4, \"ngIf\"], [\"class\", \"baggage-item checked\", 4, \"ngFor\", \"ngForOf\"], [1, \"baggage-item\", \"checked\"], [1, \"baggage-info\"], [1, \"baggage-type\"], [\"class\", \"baggage-specs\", 4, \"ngIf\"], [1, \"baggage-specs\"], [\"class\", \"baggage-item cabin\", 4, \"ngFor\", \"ngForOf\"], [1, \"baggage-item\", \"cabin\"], [1, \"fas\", \"fa-briefcase\"], [1, \"feature\"], [1, \"fas\", \"fa-concierge-bell\"], [\"class\", \"feature\", 4, \"ngFor\", \"ngForOf\"], [1, \"source-info\"], [1, \"price-breakdown-content\"], [\"class\", \"breakdown-group\", 4, \"ngIf\"], [1, \"breakdown-group\"], [\"class\", \"breakdown-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"breakdown-item service-fee\", 4, \"ngIf\"], [1, \"breakdown-total\"], [1, \"total-label\"], [1, \"total-amount\"], [1, \"breakdown-item\"], [1, \"passenger-type\"], [1, \"item-price\"], [1, \"breakdown-item\", \"service-fee\"], [1, \"fee-label\"], [1, \"fee-amount\"], [1, \"segments-section\"], [1, \"segments-details\"], [1, \"segments-summary\"], [1, \"segments-content\"], [\"class\", \"segment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"segment-item\"], [1, \"segment-header\"], [1, \"segment-number\"], [1, \"segment-airline\"], [1, \"segment-flight\"], [1, \"segment-route\"], [1, \"segment-departure\"], [1, \"segment-duration\"], [1, \"segment-arrival\"], [\"class\", \"layover-info\", 4, \"ngIf\"], [1, \"layover-info\"], [1, \"fas\", \"fa-hourglass-half\"], [1, \"branded-fare-section\"], [1, \"branded-fare-details\"], [1, \"branded-fare-summary\"], [1, \"branded-fare-content\"], [\"class\", \"branded-fare-description\", 4, \"ngIf\"], [\"class\", \"branded-fare-features\", 4, \"ngIf\"], [1, \"branded-fare-description\"], [1, \"branded-fare-features\"], [\"class\", \"feature-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature-item\"], [1, \"feature-name\"], [\"class\", \"feature-description\", 4, \"ngIf\"], [1, \"feature-description\"], [1, \"no-results\"], [1, \"no-results-icon\"], [\"style\", \"background-color: #fff8e6; border-left: 4px solid #f59e0b; padding: 15px; margin: 0 0 20px 0; border-radius: 0 8px 8px 0; display: flex; align-items: center; gap: 10px; max-width: 500px; text-align: left;\", 4, \"ngIf\"], [1, \"no-results-suggestions\"], [1, \"suggestion\"], [1, \"fas\", \"fa-map-marker-alt\"], [\"class\", \"suggestion\", 4, \"ngIf\"], [2, \"background-color\", \"#fff8e6\", \"border-left\", \"4px solid #f59e0b\", \"padding\", \"15px\", \"margin\", \"0 0 20px 0\", \"border-radius\", \"0 8px 8px 0\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"10px\", \"max-width\", \"500px\", \"text-align\", \"left\"], [1, \"fas\", \"fa-info-circle\", 2, \"color\", \"#f59e0b\", \"font-size\", \"18px\"], [2, \"color\", \"#92400e\", \"font-size\", \"14px\", \"line-height\", \"1.5\"]],\n        template: function SearchPriceComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n            i0.ɵɵtext(4, \"Find Your Perfect Flight\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\", 4);\n            i0.ɵɵtext(6, \"Search and compare flights to destinations worldwide\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 5);\n            i0.ɵɵelement(8, \"img\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10);\n            i0.ɵɵelement(13, \"i\", 11);\n            i0.ɵɵelementStart(14, \"span\", 12);\n            i0.ɵɵtext(15, \"TravelEase\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"div\", 13)(17, \"div\", 14)(18, \"h2\");\n            i0.ɵɵtext(19, \"Search Flights\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"p\");\n            i0.ɵɵtext(21, \"Find the best deals for your next trip\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 15)(23, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_23_listener() {\n              return ctx.setSearchType(ctx.SEARCH_TYPE_ONE_WAY);\n            });\n            i0.ɵɵelement(24, \"i\", 17);\n            i0.ɵɵtext(25, \" One Way \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_26_listener() {\n              return ctx.setSearchType(ctx.SEARCH_TYPE_ROUND_TRIP);\n            });\n            i0.ɵɵelement(27, \"i\", 18);\n            i0.ɵɵtext(28, \" Round Trip \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(29, \"form\", 19);\n            i0.ɵɵlistener(\"ngSubmit\", function SearchPriceComponent_Template_form_ngSubmit_29_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵelementStart(30, \"div\", 20)(31, \"div\", 21)(32, \"label\", 22);\n            i0.ɵɵtext(33, \"Passengers\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"div\", 23);\n            i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_div_click_34_listener($event) {\n              return ctx.togglePassengerDropdown($event);\n            });\n            i0.ɵɵelement(35, \"i\", 24);\n            i0.ɵɵelementStart(36, \"div\", 25)(37, \"span\");\n            i0.ɵɵtext(38);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(39, \"i\", 26);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 27);\n            i0.ɵɵtemplate(41, SearchPriceComponent_div_41_Template, 15, 7, \"div\", 28);\n            i0.ɵɵelementStart(42, \"div\", 29)(43, \"button\", 30);\n            i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_43_listener() {\n              return ctx.closePassengerDropdown();\n            });\n            i0.ɵɵtext(44, \"Apply\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(45, \"div\", 31)(46, \"label\", 32);\n            i0.ɵɵtext(47, \"Class\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\", 33);\n            i0.ɵɵelement(49, \"i\", 34);\n            i0.ɵɵelementStart(50, \"select\", 35);\n            i0.ɵɵtemplate(51, SearchPriceComponent_option_51_Template, 2, 2, \"option\", 36);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(52, \"div\", 37)(53, \"label\", 38);\n            i0.ɵɵtext(54, \"Direct Flight\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"div\", 39);\n            i0.ɵɵelement(56, \"input\", 40);\n            i0.ɵɵelementStart(57, \"label\", 41);\n            i0.ɵɵelement(58, \"span\", 42);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(59, \"div\", 43);\n            i0.ɵɵelement(60, \"input\", 44)(61, \"input\", 45);\n            i0.ɵɵelementStart(62, \"div\", 46)(63, \"div\", 31)(64, \"label\", 47);\n            i0.ɵɵtext(65, \"From\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"div\", 33);\n            i0.ɵɵelement(67, \"i\", 48);\n            i0.ɵɵelementStart(68, \"input\", 49);\n            i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_68_listener() {\n              return ctx.showAllDepartureLocations();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(69, \"mat-autocomplete\", 50, 51);\n            i0.ɵɵtemplate(71, SearchPriceComponent_mat_option_71_Template, 10, 12, \"mat-option\", 36);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(72, SearchPriceComponent_div_72_Template, 3, 0, \"div\", 52);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"div\", 53)(74, \"button\", 54);\n            i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_74_listener() {\n              return ctx.swapLocations();\n            });\n            i0.ɵɵelement(75, \"i\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(76, \"div\", 31)(77, \"label\", 55);\n            i0.ɵɵtext(78, \"To\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"div\", 33);\n            i0.ɵɵelement(80, \"i\", 56);\n            i0.ɵɵelementStart(81, \"input\", 57);\n            i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_81_listener() {\n              return ctx.showAllArrivalLocations();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(82, \"mat-autocomplete\", 50, 58);\n            i0.ɵɵtemplate(84, SearchPriceComponent_mat_option_84_Template, 10, 12, \"mat-option\", 36);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(85, SearchPriceComponent_div_85_Template, 3, 0, \"div\", 52);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(86, \"div\", 46)(87, \"div\", 31)(88, \"label\", 59);\n            i0.ɵɵtext(89);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(90, \"div\", 33);\n            i0.ɵɵelement(91, \"i\", 60)(92, \"input\", 61);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(93, SearchPriceComponent_div_93_Template, 3, 0, \"div\", 52);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(94, SearchPriceComponent_div_94_Template, 7, 2, \"div\", 62);\n            i0.ɵɵelementStart(95, \"div\", 63)(96, \"button\", 64);\n            i0.ɵɵtemplate(97, SearchPriceComponent_i_97_Template, 1, 0, \"i\", 65);\n            i0.ɵɵtemplate(98, SearchPriceComponent_span_98_Template, 2, 0, \"span\", 66);\n            i0.ɵɵtemplate(99, SearchPriceComponent_div_99_Template, 2, 0, \"div\", 67);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(100, \"div\", 68)(101, \"details\")(102, \"summary\");\n            i0.ɵɵelement(103, \"i\", 69);\n            i0.ɵɵtext(104, \" Advanced Options \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(105, \"div\", 70)(106, \"div\", 71)(107, \"div\", 31)(108, \"label\", 72);\n            i0.ɵɵtext(109, \"Language\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(110, \"div\", 33);\n            i0.ɵɵelement(111, \"i\", 73);\n            i0.ɵɵelementStart(112, \"select\", 74)(113, \"option\", 75);\n            i0.ɵɵtext(114, \"English (US)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(115, \"option\", 76);\n            i0.ɵɵtext(116, \"Fran\\u00E7ais\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(117, \"div\", 31)(118, \"label\", 77);\n            i0.ɵɵtext(119, \"Currency\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(120, \"div\", 33);\n            i0.ɵɵelement(121, \"i\", 78);\n            i0.ɵɵelementStart(122, \"select\", 79)(123, \"option\", 80);\n            i0.ɵɵtext(124, \"Euro (\\u20AC)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(125, \"option\", 81);\n            i0.ɵɵtext(126, \"Dollar ($)\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(127, \"div\", 31)(128, \"label\", 82);\n            i0.ɵɵtext(129, \"Baggage Options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(130, \"div\", 33);\n            i0.ɵɵelement(131, \"i\", 83);\n            i0.ɵɵelementStart(132, \"select\", 84)(133, \"option\", 85);\n            i0.ɵɵtext(134, \"All options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(135, \"option\", 85);\n            i0.ɵɵtext(136, \"Baggage included only\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(137, \"option\", 85);\n            i0.ɵɵtext(138, \"No baggage only\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(139, \"div\", 86)(140, \"div\", 37)(141, \"div\", 39);\n            i0.ɵɵelement(142, \"input\", 87);\n            i0.ɵɵelementStart(143, \"label\", 88);\n            i0.ɵɵelement(144, \"span\", 42);\n            i0.ɵɵelementStart(145, \"span\", 89);\n            i0.ɵɵtext(146, \"Accept pending providers\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(147, \"div\", 37)(148, \"div\", 39);\n            i0.ɵɵelement(149, \"input\", 90);\n            i0.ɵɵelementStart(150, \"label\", 91);\n            i0.ɵɵelement(151, \"span\", 42);\n            i0.ɵɵelementStart(152, \"span\", 89);\n            i0.ɵɵtext(153, \"Force flight bundle package\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(154, \"div\", 37)(155, \"div\", 39);\n            i0.ɵɵelement(156, \"input\", 92);\n            i0.ɵɵelementStart(157, \"label\", 93);\n            i0.ɵɵelement(158, \"span\", 42);\n            i0.ɵɵelementStart(159, \"span\", 89);\n            i0.ɵɵtext(160, \"Disable package offer total price\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(161, \"div\", 37)(162, \"div\", 39);\n            i0.ɵɵelement(163, \"input\", 94);\n            i0.ɵɵelementStart(164, \"label\", 95);\n            i0.ɵɵelement(165, \"span\", 42);\n            i0.ɵɵelementStart(166, \"span\", 89);\n            i0.ɵɵtext(167, \"Calculate flight fees\");\n            i0.ɵɵelementEnd()()()()()()()()()();\n            i0.ɵɵtemplate(168, SearchPriceComponent_div_168_Template, 4, 3, \"div\", 96);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            const _r2 = i0.ɵɵreference(70);\n            const _r5 = i0.ɵɵreference(83);\n            let tmp_10_0;\n            let tmp_14_0;\n            let tmp_17_0;\n            i0.ɵɵadvance(23);\n            i0.ɵɵclassProp(\"active\", ctx.currentSearchType === ctx.SEARCH_TYPE_ONE_WAY);\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"active\", ctx.currentSearchType === ctx.SEARCH_TYPE_ROUND_TRIP);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\"\", ctx.getTotalPassengers(), \" Passenger(s)\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"show\", ctx.showPassengerDropdown);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.passengerTypes);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"matAutocomplete\", _r2);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.departureLocations);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx.searchForm.get(\"departureLocation\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.searchForm.get(\"departureLocation\")) == null ? null : tmp_10_0.touched));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"matAutocomplete\", _r5);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.arrivalLocations);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_14_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.currentSearchType === ctx.SEARCH_TYPE_ROUND_TRIP ? \"Departure\" : \"Date\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"min\", ctx.minDate);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx.searchForm.get(\"departureDate\")) == null ? null : tmp_17_0.invalid) && ((tmp_17_0 = ctx.searchForm.get(\"departureDate\")) == null ? null : tmp_17_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentSearchType === ctx.SEARCH_TYPE_ROUND_TRIP);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(34);\n            i0.ɵɵproperty(\"value\", 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 2);\n            i0.ɵɵadvance(31);\n            i0.ɵɵproperty(\"ngIf\", ctx.hasSearched);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatAutocomplete, i7.MatOption, i6.MatAutocompleteTrigger, i5.SlicePipe, i5.CurrencyPipe],\n        styles: [\"*{box-sizing:border-box}.search-price-container{display:flex;flex-direction:column;padding:10spx;width:100%;max-width:100%;margin:0;position:relative;z-index:1;box-sizing:border-box;overflow-x:hidden}@media (min-width: 992px){.search-price-container{padding:0;margin:0}}.search-price-container:before{content:\\\"\\\";position:fixed;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(var(--primary-color-rgb),.03) 0%,rgba(var(--primary-color-rgb),0) 50%),radial-gradient(circle at top right,rgba(var(--secondary-color-rgb),.03) 0%,rgba(var(--secondary-color-rgb),0) 70%);z-index:-1;pointer-events:none}.page-header{display:flex;flex-direction:column;align-items:center;text-align:center;margin-bottom:30px;position:relative;overflow:hidden;border-radius:20px;box-shadow:0 10px 30px #0000001a}.page-header:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(to bottom,rgba(0,0,0,.4),rgba(0,0,0,.1));z-index:1}.header-content{max-width:800px;padding:40px 20px;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:2;color:#fff;text-shadow:0 2px 4px rgba(0,0,0,.3)}.page-title{font-size:36px;font-weight:700;color:#fff;margin-bottom:15px;position:relative;display:inline-block}.page-title:after{content:\\\"\\\";position:absolute;bottom:-10px;left:50%;transform:translate(-50%);width:60px;height:3px;background:white;border-radius:3px}.page-subtitle{font-size:18px;color:#ffffffe6;line-height:1.5;max-width:600px;margin:20px auto 0}.header-illustration{width:100%;height:300px;overflow:hidden}.header-illustration img{width:100%;height:100%;object-fit:cover;object-position:center;transition:transform 10s ease}.page-header:hover .header-illustration img{transform:scale(1.1)}@media (min-width: 992px){.page-header{display:none}.search-price-container{flex-direction:column;align-items:stretch;padding:0;margin:0;width:100%}.search-content{display:flex;flex-direction:column;width:100%;gap:0;max-width:1100px;margin:0 auto}.search-form-container{position:sticky;top:0;width:100%;max-height:none;overflow:visible;margin-bottom:20px;padding:0;border-radius:0;box-shadow:0 2px 10px #0000001a;z-index:100;background-color:#fff}.search-results-container{width:100%;padding:20px;max-width:1100px;margin:0 auto}}.search-form-container{background-color:#fff;border-radius:10px;box-shadow:0 5px 15px #0000001a;padding:25px;margin-bottom:30px}.sidebar-logo{display:none}@media (min-width: 992px){.search-form-container{padding:0;margin-bottom:20px;border-radius:0}.sidebar-logo{display:block;background-color:var(--primary-color);color:#fff;padding:15px 20px;text-align:left}.logo-container{display:flex;align-items:center;gap:10px;max-width:1100px;margin:0 auto;width:100%}.logo-icon{font-size:22px}.logo-text{font-size:22px;font-weight:700;letter-spacing:.5px}}.search-form-header{margin-bottom:30px;text-align:center;padding:25px 20px 20px;position:relative}.search-form-header h2{color:#2989d8;margin-bottom:10px;font-size:28px;font-weight:700;letter-spacing:.5px;position:relative;display:inline-block;text-shadow:0 1px 2px rgba(41,137,216,.1);transition:all .3s ease}.search-form-header h2:after{content:\\\"\\\";position:absolute;bottom:-5px;left:50%;transform:translate(-50%);width:50px;height:3px;background:linear-gradient(90deg,#2989d8,#00c9ff);border-radius:3px;transition:width .3s ease}.search-form-header:hover h2:after{width:80px}.search-form-header p{color:#64748b;font-size:16px;line-height:1.5;max-width:600px;margin:0 auto 15px;font-weight:400;letter-spacing:.2px}.passenger-class-options{display:flex;justify-content:center;gap:15px;margin-top:20px;flex-wrap:wrap;background-color:#2989d80d;padding:15px;border-radius:8px}.passenger-class-options .form-group{min-width:120px;flex:1;max-width:150px;margin-bottom:0}.passenger-class-options .form-group.checkbox-group{display:flex;flex-direction:column;align-items:flex-start}.passengers-selector{position:relative;min-width:180px!important}.passengers-display{display:flex;justify-content:space-between;align-items:center;cursor:pointer;padding-right:15px}.passengers-dropdown{position:absolute;top:100%;left:0;width:300px;background-color:#fff;border-radius:8px;box-shadow:0 5px 20px #00000026;z-index:1000;padding:15px;margin-top:5px;display:none;animation:fadeIn .2s ease-in-out}.passengers-dropdown.show{display:block}.passenger-type-row{display:flex;justify-content:space-between;align-items:center;padding:12px 0;border-bottom:1px solid rgba(0,0,0,.05)}.passenger-type-row:last-child{border-bottom:none}.passenger-type-info{flex:1}.passenger-type-name{font-weight:500;color:#333;font-size:14px}.passenger-type-description{color:#777;font-size:12px;margin-top:2px}.passenger-count-control{display:flex;align-items:center;gap:10px}.passenger-count-btn{width:28px;height:28px;border-radius:50%;border:1px solid #ddd;background-color:#fff;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}.passenger-count-btn:hover:not(:disabled){background-color:#f5f5f5;border-color:#2989d8;color:#2989d8}.passenger-count-btn:disabled{opacity:.5;cursor:not-allowed}.passenger-count{font-weight:500;min-width:20px;text-align:center}.passengers-dropdown-footer{margin-top:15px;display:flex;justify-content:flex-end}.apply-btn{background-color:#2989d8;color:#fff;border:none;border-radius:4px;padding:8px 15px;font-size:13px;font-weight:500;cursor:pointer;transition:all .2s ease}.apply-btn:hover{background-color:#1e70b7}@keyframes fadeIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}.passenger-class-options label{color:#2989d8;font-size:13px;margin-bottom:5px;display:block;text-align:left;font-weight:500}.passenger-class-options .input-with-icon{position:relative}.passenger-class-options .form-control{background-color:#fff;border:1px solid rgba(41,137,216,.2);color:#333;font-size:13px;padding:8px 10px 8px 30px;border-radius:6px;transition:all .3s ease}.passenger-class-options .form-control:focus{border-color:#2989d8;box-shadow:0 0 0 2px #2989d81a}.passenger-class-options .input-with-icon i{position:absolute;left:10px;top:50%;transform:translateY(-50%);color:#2989d8;font-size:14px}.passenger-class-options .toggle-switch{position:relative;display:inline-block;width:100%;height:34px;margin-top:5px}.passenger-class-options .toggle-switch.small{height:28px}.passenger-class-options .toggle-input{opacity:0;width:0;height:0}.passenger-class-options .toggle-label{position:relative;display:block;height:28px;cursor:pointer;background-color:#e9e9e9;transition:.4s;border-radius:34px;padding-right:35px;padding-left:10px;line-height:28px;color:#666;font-size:12px;text-align:right}.passenger-class-options .toggle-label:before{position:absolute;content:\\\"\\\";height:20px;width:20px;left:4px;bottom:4px;background-color:#fff;transition:.4s;border-radius:50%}.passenger-class-options .toggle-input:checked+.toggle-label{background-color:#2989d8;color:#fff;text-align:left;padding-right:10px;padding-left:35px}.passenger-class-options .toggle-input:checked+.toggle-label:before{transform:translate(26px)}.passenger-class-options .toggle-switch-label{font-weight:500}@media (min-width: 992px){.search-form-header{max-width:1100px;margin:0 auto 20px;text-align:left;padding:22px 20px 20px;display:flex;flex-wrap:wrap;justify-content:space-between;align-items:center}.search-form-header h2:after{left:0;transform:none}.search-form-header .header-text{flex:1;max-width:50%}.passenger-class-options{margin-top:0;justify-content:flex-end;background-color:transparent;padding:0;flex:1;max-width:50%}}.search-form{display:flex;flex-direction:column;gap:15px;max-width:1100px;margin:0 auto}.form-group{margin-bottom:15px}.form-row{display:flex;gap:15px}.half-width{flex:1}label{display:block;margin-bottom:5px;font-weight:500;color:#333;font-size:14px}.form-control{width:100%;padding:10px 12px;border:1px solid #ddd;border-radius:5px;font-size:14px;transition:border-color .3s}.form-control:focus{border-color:#2989d8;outline:none;box-shadow:0 0 0 2px #2989d833}.checkbox-group{display:flex;align-items:center}.checkbox-group input{margin-right:8px}.search-button{padding:12px;background-color:#2989d8;color:#fff;border:none;border-radius:5px;font-size:16px;font-weight:600;cursor:pointer;transition:background-color .3s;display:flex;justify-content:center;align-items:center;margin-top:10px}.search-button:hover{background-color:#1e5799}.search-button:disabled{background-color:#b3d4f0;cursor:not-allowed}.error-message{color:#e74c3c;font-size:12px;margin-top:5px}details{margin-top:10px;margin-bottom:15px}summary{cursor:pointer;color:#2989d8;font-weight:500;padding:5px 0}summary:hover{text-decoration:underline}.advanced-options{margin-top:10px;padding:15px;background-color:#f8f9fa;border-radius:5px;border:1px solid #eee}::ng-deep .mat-autocomplete-panel{max-height:300px!important}::ng-deep .mat-option{height:auto!important;line-height:1.2!important;padding:10px 16px!important}::ng-deep .mat-option small{color:#666;display:block;margin-top:2px}.search-results-container{background-color:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:10px;box-shadow:0 5px 15px #00000014,0 0 0 1px rgba(var(--primary-color-rgb),.05);padding:20px;position:relative;overflow-x:hidden;border:1px solid rgba(var(--primary-color-rgb),.08);box-sizing:border-box;width:100%;max-width:1100px;margin:0 auto}.search-results-layout{display:flex;gap:20px;width:100%}.sidebar-filters{width:280px;flex-shrink:0;background-color:#fff;border-radius:8px;box-shadow:0 2px 8px #0000000d;overflow:hidden;border:1px solid rgba(0,0,0,.08);position:sticky;top:100px;max-height:calc(100vh - 120px);overflow-y:auto}.sidebar-header{display:flex;justify-content:space-between;align-items:center;padding:15px;border-bottom:1px solid rgba(0,0,0,.08);background-color:#f9f9f9}.sidebar-header h3{margin:0;font-size:18px;color:var(--primary-dark)}.clear-filters-btn{background:none;border:none;color:var(--primary-color);font-size:14px;cursor:pointer;padding:0;text-decoration:underline;transition:color .3s}.clear-filters-btn:hover{color:var(--secondary-color)}.filter-section{border-bottom:1px solid rgba(0,0,0,.08)}.filter-section:last-child{border-bottom:none}.filter-section-header{display:flex;justify-content:space-between;align-items:center;padding:15px;cursor:pointer;transition:background-color .3s}.filter-section-header:hover{background-color:rgba(var(--primary-color-rgb),.03)}.filter-section-header h4{margin:0;font-size:16px;color:#333}.filter-section-header i{color:#999;transition:transform .3s}.filter-section-content{max-height:0;overflow:hidden;transition:max-height .3s ease}.filter-section-content.expanded{max-height:500px;padding:0 15px 15px}.time-label{font-size:13px;color:#999;margin-bottom:8px;text-transform:uppercase}.filter-option{margin-bottom:10px}.filter-option:last-child{margin-bottom:0}.checkbox-container{display:flex;align-items:center;justify-content:space-between;position:relative;padding-left:30px;cursor:pointer;font-size:14px;-webkit-user-select:none;user-select:none;width:100%}.checkbox-container input{position:absolute;opacity:0;cursor:pointer;height:0;width:0}.checkmark{position:absolute;left:0;top:0;height:18px;width:18px;background-color:#fff;border:1px solid #ddd;border-radius:3px;transition:all .2s ease}.checkbox-container:hover input~.checkmark{border-color:var(--primary-color)}.checkbox-container input:checked~.checkmark{background-color:var(--primary-color);border-color:var(--primary-color)}.checkmark:after{content:\\\"\\\";position:absolute;display:none}.checkbox-container input:checked~.checkmark:after{display:block}.checkbox-container .checkmark:after{left:6px;top:2px;width:4px;height:9px;border:solid white;border-width:0 2px 2px 0;transform:rotate(45deg)}.option-label{flex:1;margin-right:10px}.option-price{color:var(--primary-color);font-weight:600;font-size:13px}.search-results-container:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:6px;background:linear-gradient(90deg,var(--primary-color),var(--secondary-color));z-index:1}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:60px;text-align:center}.loading-container p{margin-top:20px;color:var(--primary-color);font-weight:500;animation:pulse 1.5s infinite}.spinner{width:30px;height:30px;border:3px solid rgba(var(--primary-color-rgb),.2);border-radius:50%;border-top-color:var(--primary-color);animation:spin 1s cubic-bezier(.6,.2,.4,.8) infinite;box-shadow:0 0 10px rgba(var(--primary-color-rgb),.1)}.spinner.large{width:50px;height:50px;border-width:4px}@keyframes spin{to{transform:rotate(360deg)}}.error-container{padding:30px;background-color:#e74c3c0d;border-radius:16px;text-align:center;border:1px solid rgba(231,76,60,.1);box-shadow:0 5px 15px #e74c3c0d;animation:scaleIn .4s cubic-bezier(.165,.84,.44,1)}.error-container h4{color:#e74c3c;margin-bottom:10px;font-size:18px}.error-container p{color:#0009}.results-header{margin-bottom:30px;position:relative;padding-bottom:15px}.results-header:after{content:\\\"\\\";position:absolute;bottom:0;left:0;width:100%;height:1px;background:linear-gradient(90deg,rgba(var(--primary-color-rgb),.1) 0%,rgba(var(--primary-color-rgb),.05) 50%,rgba(var(--primary-color-rgb),0) 100%)}.results-filters{margin-top:15px}.filter-buttons{display:flex;gap:10px;flex-wrap:wrap}.filter-button{padding:10px 16px;border-radius:50px;border:1px solid rgba(var(--primary-color-rgb),.1);background-color:#fff;color:#666;font-size:14px;font-weight:500;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:8px;box-shadow:0 2px 5px #0000000d}.filter-button i{font-size:14px;color:rgba(var(--primary-color-rgb),.7);transition:all .3s ease}.filter-button:hover{background-color:rgba(var(--primary-color-rgb),.05);border-color:rgba(var(--primary-color-rgb),.2);transform:translateY(-2px);box-shadow:0 4px 8px #00000014}.filter-button.active{background:linear-gradient(135deg,var(--primary-color),var(--primary-dark));color:#fff;border-color:transparent;box-shadow:0 4px 10px rgba(var(--primary-color-rgb),.2)}.filter-button.active i{color:#fff}@keyframes pulse-filter{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}.filter-button.active{animation:pulse-filter 2s infinite}.provider-info{display:block;font-size:12px;color:#0009;margin-top:4px}.availability{font-size:12px;display:block;margin-top:4px;font-weight:500}.availability i.fa-check-circle{color:#4caf50}.availability i.fa-check-circle+span{color:#4caf50}.availability i.fa-exclamation-triangle{color:#f44336}.availability i.fa-exclamation-triangle+span{color:#f44336}.expiration{color:#ff9800;font-size:12px;display:block;margin-top:4px}.flight-badge.branded{background-color:#9c27b0}.feature-group{margin-bottom:15px}.feature-group h4{font-size:14px;margin-bottom:8px;color:#000000b3;display:flex;align-items:center;gap:6px}.feature-group .feature{font-size:13px;color:#0009;margin-bottom:4px;padding-left:20px}.offer-id{font-family:monospace;background-color:#0000000d;padding:2px 6px;border-radius:4px;font-size:12px}.price-breakdown-section{margin:15px 0}.price-breakdown-details{border:1px solid rgba(0,0,0,.1);border-radius:8px;overflow:hidden}.price-breakdown-summary{padding:12px 15px;background-color:#00000005;cursor:pointer;display:flex;align-items:center;gap:8px;font-weight:500;color:#000000b3}.price-breakdown-summary:hover{background-color:#0000000d}.price-breakdown-content{padding:15px}.breakdown-group h4{font-size:14px;margin-bottom:10px;color:#000000b3}.breakdown-item{display:flex;justify-content:space-between;margin-bottom:8px;font-size:13px;color:#0009}.breakdown-item.service-fee{color:#ff5722}.breakdown-total{display:flex;justify-content:space-between;margin-top:10px;padding-top:10px;border-top:1px dashed rgba(0,0,0,.1);font-weight:600;color:#000c}.segments-section{margin:15px 0}.segments-details{border:1px solid rgba(0,0,0,.1);border-radius:8px;overflow:hidden}.segments-summary{padding:12px 15px;background-color:#00000005;cursor:pointer;display:flex;align-items:center;gap:8px;font-weight:500;color:#000000b3}.segments-summary:hover{background-color:#0000000d}.segments-content{padding:15px}.segment-item{margin-bottom:20px;padding-bottom:15px;border-bottom:1px dashed rgba(0,0,0,.1)}.segment-item:last-child{margin-bottom:0;padding-bottom:0;border-bottom:none}.segment-header{display:flex;justify-content:space-between;margin-bottom:10px;font-size:14px;color:#000000b3}.segment-number{font-weight:600}.segment-route{display:flex;align-items:center;gap:15px;margin-bottom:10px}.layover-info{display:flex;align-items:center;gap:8px;font-size:13px;color:#ff9800;margin-top:10px;padding:8px;background-color:#ff98000d;border-radius:4px}.branded-fare-section{margin:15px 0}.branded-fare-details{border:1px solid rgba(0,0,0,.1);border-radius:8px;overflow:hidden}.branded-fare-summary{padding:12px 15px;background-color:#9c27b00d;cursor:pointer;display:flex;align-items:center;gap:8px;font-weight:500;color:#9c27b0}.branded-fare-summary:hover{background-color:#9c27b01a}.branded-fare-content{padding:15px}.branded-fare-description{margin-bottom:15px;font-size:14px;color:#000000b3}.branded-fare-features h4{font-size:14px;margin-bottom:10px;color:#000000b3}.feature-item{margin-bottom:10px}.feature-name{font-weight:500;margin-bottom:4px;color:#000000b3}.feature-description{font-size:13px;color:#0009}.results-header h3{color:var(--primary-dark);margin-bottom:8px;font-size:24px;font-weight:600}.results-header p{color:#0009;font-size:14px}.results-count{font-weight:700;color:var(--primary-color)}.filter-label{font-size:.9em;font-style:italic;color:#666;margin-left:5px}.flight-list{display:flex;flex-direction:column;gap:25px;flex:1}@media (max-width: 992px){.search-results-layout{flex-direction:column}.sidebar-filters{width:100%;position:relative;top:0;max-height:none;margin-bottom:20px}}.flight-card{background-color:#fff;border-radius:8px;overflow:hidden;transition:all .3s ease;box-shadow:0 2px 8px #0000000d,0 0 0 1px #00000008;position:relative;width:100%;box-sizing:border-box}.flight-card:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(var(--primary-color-rgb),.02) 0%,rgba(var(--primary-color-rgb),0) 100%);pointer-events:none}.flight-card:hover{transform:translateY(-8px) scale(1.01);box-shadow:0 15px 30px #00000014,0 0 0 1px rgba(var(--primary-color-rgb),.05)}.flight-card.unavailable{opacity:.7;transform:none!important;box-shadow:0 5px 15px #00000008!important}.flight-header{display:flex;justify-content:space-between;align-items:center;padding:15px;background-color:#f9f9f9;border-bottom:1px solid #eaeaea;position:relative;flex-wrap:wrap}.flight-header:after{content:\\\"\\\";position:absolute;bottom:-1px;left:0;width:100%;height:1px;background:linear-gradient(90deg,rgba(var(--primary-color-rgb),0) 0%,rgba(var(--primary-color-rgb),.1) 50%,rgba(var(--primary-color-rgb),0) 100%)}.airline-info{display:flex;align-items:center;gap:15px}.airline-logo{width:40px;height:40px;object-fit:contain;padding:5px;background-color:#fff;border-radius:50%;box-shadow:0 3px 10px #0000000d;transition:transform .3s ease}.flight-card:hover .airline-logo{transform:scale(1.1)}.airline-name{font-weight:600;font-size:16px;color:var(--primary-dark);transition:color .3s ease}.flight-card:hover .airline-name{color:var(--primary-color)}.flight-price{text-align:right;position:relative}.price{font-size:24px;font-weight:700;color:var(--primary-color);display:block;transition:all .3s ease;position:relative}.flight-card:hover .price{color:var(--secondary-color);transform:scale(1.05)}.price:before{content:\\\"\\\";position:absolute;bottom:-3px;left:0;width:0;height:2px;background-color:var(--secondary-color);transition:width .3s ease}.flight-card:hover .price:before{width:100%}.availability{font-size:13px;color:#e74c3c;font-weight:500;margin-top:5px}.flight-details{padding:15px;position:relative;width:100%;box-sizing:border-box}.flight-route{display:flex;flex-wrap:wrap;justify-content:space-between;align-items:center;margin-bottom:15px;position:relative;width:100%}@media (max-width: 768px){.flight-route{flex-direction:column;align-items:flex-start;gap:15px}}.departure,.arrival{flex:1;position:relative;transition:transform .3s ease}.flight-card:hover .departure{transform:translate(-5px)}.flight-card:hover .arrival{transform:translate(5px)}.time{font-size:22px;font-weight:700;margin-bottom:8px;color:var(--primary-dark);display:flex;align-items:center;gap:8px}.time i{color:var(--primary-color);font-size:18px;opacity:0;transform:translateY(5px);transition:all .3s ease}.flight-card:hover .departure .time i,.flight-card:hover .arrival .time i{opacity:1;transform:translateY(0)}.location{font-size:15px;color:#0009;font-weight:500;transition:color .3s ease}.flight-card:hover .location{color:var(--primary-color)}.location small{display:block;font-size:13px;color:#0006;margin-top:3px}.flight-duration{flex:1;text-align:center;padding:0 20px;position:relative;transition:transform .3s ease}.flight-card:hover .flight-duration{transform:translateY(-5px)}.duration-line{display:flex;align-items:center;justify-content:center;margin-bottom:10px;position:relative}.dot{width:10px;height:10px;background-color:var(--primary-color);border-radius:50%;position:relative;z-index:1;transition:all .3s ease;box-shadow:0 0 0 4px rgba(var(--primary-color-rgb),.1)}.flight-card:hover .dot{background-color:var(--secondary-color);transform:scale(1.2);box-shadow:0 0 0 6px rgba(var(--secondary-color-rgb),.15)}.line{flex:1;height:2px;background:linear-gradient(90deg,var(--primary-color),var(--primary-light));margin:0 8px;position:relative;transition:height .3s ease,background .3s ease}.flight-card:hover .line{height:3px;background:linear-gradient(90deg,var(--primary-color),var(--secondary-color))}.duration-text{font-size:16px;font-weight:600;margin-bottom:8px;color:var(--primary-dark);transition:color .3s ease}.flight-card:hover .duration-text{color:var(--primary-color)}.stops{font-size:13px;color:#00000080;font-weight:500;padding:4px 12px;background-color:rgba(var(--primary-color-rgb),.05);border-radius:50px;display:inline-block;transition:all .3s ease}.flight-card:hover .stops{background-color:rgba(var(--primary-color-rgb),.1);color:var(--primary-color)}.flight-info{display:flex;justify-content:space-between;font-size:14px;color:#00000080;margin-top:20px;padding-top:15px;border-top:1px dashed rgba(var(--primary-color-rgb),.1)}.flight-actions{padding:20px 25px;border-top:1px solid rgba(var(--primary-color-rgb),.08);display:flex;gap:15px;justify-content:flex-end;align-items:center;position:relative}.flight-actions:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:1px;background:linear-gradient(90deg,rgba(var(--primary-color-rgb),0) 0%,rgba(var(--primary-color-rgb),.1) 50%,rgba(var(--primary-color-rgb),0) 100%)}.view-details-button{padding:10px 18px;background-color:rgba(var(--primary-color-rgb),.05);color:var(--primary-dark);border:1px solid rgba(var(--primary-color-rgb),.1);border-radius:50px;font-size:14px;font-weight:600;cursor:pointer;transition:all .3s cubic-bezier(.175,.885,.32,1.275);display:flex;align-items:center;gap:8px;position:relative;overflow:hidden}.view-details-button:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(var(--primary-color-rgb),.1) 0%,rgba(var(--primary-color-rgb),0) 100%);opacity:0;transition:opacity .3s ease}.view-details-button i{font-size:16px;transition:transform .3s ease}.view-details-button:hover{background-color:rgba(var(--primary-color-rgb),.1);color:var(--primary-color);transform:translateY(-2px);box-shadow:0 4px 10px rgba(var(--primary-color-rgb),.1)}.view-details-button:hover:before{opacity:1}.view-details-button:hover i{transform:translate(3px)}.select-button{padding:10px 24px;background:linear-gradient(135deg,var(--primary-color),var(--primary-dark));color:#fff;border:none;border-radius:50px;font-size:15px;font-weight:600;cursor:pointer;transition:all .3s cubic-bezier(.175,.885,.32,1.275);display:flex;align-items:center;gap:10px;box-shadow:0 4px 12px rgba(var(--primary-color-rgb),.2);position:relative;overflow:hidden}.select-button:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(255,255,255,.2) 0%,rgba(255,255,255,0) 50%);z-index:1}.select-button:after{content:\\\"\\\";position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle,rgba(255,255,255,.3) 0%,rgba(255,255,255,0) 60%);opacity:0;transform:scale(.5);transition:transform .8s ease,opacity .8s ease;z-index:1}.select-button i{font-size:16px;transition:transform .3s ease;position:relative;z-index:2}.select-button span{position:relative;z-index:2}.select-button:hover{background:linear-gradient(135deg,var(--secondary-color),var(--primary-color));transform:translateY(-3px) scale(1.02);box-shadow:0 8px 20px rgba(var(--primary-color-rgb),.3)}.select-button:hover:after{opacity:1;transform:scale(1)}.select-button:hover i{transform:translate(3px);animation:pulse 1s infinite}.select-button:active{transform:translateY(-1px) scale(1);box-shadow:0 4px 12px rgba(var(--primary-color-rgb),.2)}.select-button:disabled{background:linear-gradient(135deg,#b0b0b0,#d0d0d0);cursor:not-allowed;transform:none;box-shadow:0 2px 8px #0000001a;opacity:.7}.baggage-details{display:flex;flex-direction:column;gap:10px;margin-top:8px}.baggage-item{display:flex;align-items:center;padding:8px 12px;border-radius:6px;background-color:#f8f9fa;border:1px solid #e9ecef}.baggage-item.checked{background-color:#e7f5ff;border-color:#c5e1f9}.baggage-item.cabin{background-color:#f3f0ff;border-color:#e5dbff}.baggage-item i{font-size:16px;margin-right:10px;color:#4a6fa5}.baggage-item.cabin i{color:#6741d9}.baggage-info{display:flex;flex-direction:column}.baggage-type{font-weight:600;font-size:14px;color:#343a40}.baggage-specs{font-size:12px;color:#6c757d;margin-top:2px}.feature-group{margin-bottom:15px;padding-bottom:15px;border-bottom:1px solid #e9ecef}.feature-group:last-child{border-bottom:none;margin-bottom:0;padding-bottom:0}.feature-group h4{display:flex;align-items:center;font-size:14px;font-weight:600;margin-bottom:8px;color:#495057}.feature-group h4 i{margin-right:8px;color:#4a6fa5}.feature{margin-top:5px;font-size:13px;color:#495057}.text-success{color:#28a745}.text-danger{color:#dc3545}.source-info{font-size:11px;color:#6c757d;font-style:italic;margin-left:5px}.date-info-message{margin:10px 0;padding:10px 15px;border-radius:8px;background-color:#ff98001a;border:1px solid rgba(255,152,0,.2);color:#ff9800;font-size:14px;display:flex;align-items:center;gap:10px;animation:fadeIn .3s ease-in-out}.date-info-message i{font-size:16px;color:#ff9800}.date-info-message.date-info-success{background-color:#4caf501a;border:1px solid rgba(76,175,80,.2);color:#4caf50}.date-info-message.date-info-success i{color:#4caf50}.date-differs{color:#ff9800;font-weight:700;cursor:help;position:relative;margin-left:3px}.date-differs:hover:after{content:attr(title);position:absolute;bottom:100%;left:50%;transform:translate(-50%);background-color:#000c;color:#fff;padding:5px 10px;border-radius:4px;font-size:12px;white-space:nowrap;z-index:10;margin-bottom:5px}.flight-type-tabs{display:flex;justify-content:center;margin-bottom:20px;box-shadow:0 2px 10px #0000000d;border-radius:8px;overflow:hidden;width:100%;max-width:400px;margin-left:auto;margin-right:auto}.flight-type-tab{flex:1;padding:12px 20px;background-color:#f9f9f9;border:1px solid #e0e0e0;cursor:pointer;transition:all .3s ease;font-weight:600;color:#666;text-align:center;position:relative;overflow:hidden;display:flex;align-items:center;justify-content:center;gap:8px}.flight-type-tab i{font-size:16px;transition:transform .3s ease}.flight-type-tab:first-child{border-top-left-radius:8px;border-bottom-left-radius:8px;border-right:none}.flight-type-tab:last-child{border-top-right-radius:8px;border-bottom-right-radius:8px;border-left:none}.flight-type-tab.active{background:linear-gradient(135deg,#2989d8,#1e70b7);color:#fff;border-color:transparent;box-shadow:0 4px 15px #2989d833;z-index:1}.flight-type-tab.active i{transform:scale(1.2)}.flight-type-tab:hover:not(.active){background-color:#f0f0f0;transform:translateY(-2px)}.flight-type-tab:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:3px;background:linear-gradient(90deg,#2989d8,#00c9ff);transform:scaleX(0);transition:transform .3s ease;transform-origin:left}.flight-type-tab.active:before{transform:scaleX(1)}\\n\", \".search-form *{box-sizing:border-box;max-width:100%}.search-form input,.search-form select,.search-form button{max-width:100%;overflow:hidden;text-overflow:ellipsis}.search-card{background:linear-gradient(135deg,#ffffff,#f8fcff);border-radius:12px;box-shadow:0 10px 30px #2989d81a,0 1px 5px #2989d80d,inset 0 0 0 1px #ffffffe6;padding:22px;position:relative;margin-bottom:30px;border:1px solid rgba(41,137,216,.08);display:flex;flex-direction:column;width:100%;box-sizing:border-box;overflow:hidden;transition:all .4s cubic-bezier(.165,.84,.44,1);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);max-width:1100px;margin-left:auto;margin-right:auto}.search-card:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:radial-gradient(circle at top right,rgba(64,169,255,.08) 0%,rgba(64,169,255,0) 70%),radial-gradient(circle at bottom left,rgba(0,201,255,.08) 0%,rgba(0,201,255,0) 70%);z-index:0;pointer-events:none}.search-card:after{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:4px;background:linear-gradient(90deg,#2989d8,#00c9ff,#2989d8);background-size:200% 100%;animation:gradientShift 8s ease infinite;z-index:1;border-radius:16px 16px 0 0}@keyframes gradientShift{0%{background-position:0% 50%}50%{background-position:100% 50%}to{background-position:0% 50%}}.search-card:hover{box-shadow:0 15px 40px #2989d826,0 1px 10px #2989d81a,inset 0 0 0 1px #fff;transform:translateY(-2px)}.single-line-form{display:flex;flex-direction:column;gap:12px;width:100%}@media (min-width: 992px){.search-card{flex-direction:row;flex-wrap:nowrap;align-items:flex-end;padding:18px;height:auto;border-radius:8px;box-shadow:0 5px 15px #2989d814;max-width:1100px;margin:0 auto 25px;border:1px solid rgba(41,137,216,.08)}.single-line-form{flex-direction:row;align-items:flex-end;flex-wrap:nowrap;gap:12px;width:100%}.single-line-form .form-group:nth-child(1),.single-line-form .form-group:nth-child(3){flex:1.6;z-index:4}.single-line-form .form-group:nth-child(7),.single-line-form .form-group:nth-child(8),.single-line-form .form-group:nth-child(9){flex:.8}.single-line-form .swap-button-container{flex:0 0 auto;margin:0;padding:0 5px;z-index:6}.form-group{min-width:0;margin-bottom:0;flex:1;position:relative;z-index:3;padding:0 5px}.form-group.checkbox-group{flex:0 0 auto}.search-button-container{flex:0 0 auto;margin-top:0;margin-left:10px;text-align:center;padding-left:5px}.search-button{width:auto;white-space:nowrap;padding:10px 18px;height:42px;font-size:14px}}.trip-type-selector{display:flex;margin-bottom:20px;position:relative;border-bottom:1px solid #e7e7e7;padding-bottom:0}.trip-type-option{display:flex;align-items:center;gap:8px;padding:12px 20px;cursor:pointer;transition:all .2s ease;font-weight:500;color:#333;background-color:transparent;border:none;border-bottom:3px solid transparent;position:relative;min-width:100px;justify-content:center}.trip-type-option i{color:#666;font-size:16px;transition:color .2s ease}.trip-type-option span{transition:color .2s ease}.trip-type-option.selected{color:var(--primary-color);font-weight:600;border-bottom:3px solid var(--primary-color);background-color:transparent}.trip-type-option.selected i{color:var(--primary-color)}.trip-type-option:not(.selected):hover{color:var(--primary-color);border-bottom-color:rgba(var(--primary-color-rgb),.3)}.trip-type-option:not(.selected):hover i{color:var(--primary-color)}.trip-type-option.disabled{opacity:.5;cursor:not-allowed}.form-row{display:flex;gap:16px;margin-bottom:20px;position:relative}.locations-row{position:relative}.form-group{flex:1;position:relative;margin-bottom:20px;transition:transform .3s ease}.form-group:hover{transform:translateY(-2px)}.location-type-selector{display:none}label{display:block;margin-bottom:8px;font-weight:500;color:#334155;font-size:14px;letter-spacing:.3px;transition:all .3s ease;position:relative;padding-left:2px}label:after{content:\\\"\\\";position:absolute;bottom:-2px;left:0;width:0;height:1px;background:linear-gradient(90deg,#2989d8,transparent);transition:width .3s ease;opacity:.5}.form-group:hover label:after{width:30px}.input-with-icon{position:relative;display:flex;align-items:center;z-index:1}.input-with-icon i{position:absolute;left:14px;color:#a0a8b0;font-size:15px;z-index:2;transition:all .3s ease}.form-control{width:100%;padding:12px 14px 12px 40px;border:1px solid rgba(41,137,216,.15);border-radius:8px;font-size:14px;font-weight:400;letter-spacing:.2px;transition:all .3s cubic-bezier(.165,.84,.44,1);background-color:#fffc;color:#334155;height:42px;box-shadow:0 2px 6px #00000005,inset 0 0 0 1px #ffffffb3;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px)}.form-control:hover{border-color:#2989d84d;background-color:#fffffff2;box-shadow:0 4px 10px #00000008,inset 0 0 0 1px #fffc}.form-control:focus{outline:none;border-color:#2989d880;background-color:#fff;box-shadow:0 4px 15px #2989d81a,inset 0 0 0 1px #fff;transform:translateY(-1px)}.form-control::placeholder{color:#94a3b8;opacity:.7;transition:opacity .3s ease}.form-control:focus::placeholder{opacity:.5}.form-group:focus-within label{color:#2989d8;transform:translateY(-1px);text-shadow:0 0 1px rgba(41,137,216,.1);transition:all .3s ease}.form-group:focus-within .input-with-icon i{color:#2989d8;transform:scale(1.1)}@media (min-width: 992px){.single-line-form .form-group{margin-bottom:0}.single-line-form label{font-size:12px;margin-bottom:4px}.single-line-form .form-control,.single-line-form .input-with-icon i{font-size:14px}.single-line-form .error-message{position:absolute;font-size:11px;bottom:-18px;left:0;white-space:nowrap}}.swap-button-container{display:flex;align-items:flex-end;justify-content:center;padding-bottom:10px;position:relative;z-index:5}.swap-locations-btn{width:36px;height:36px;border-radius:50%;background:linear-gradient(135deg,#2989d8,#00c9ff);color:#fff;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;box-shadow:0 4px 12px #2989d833,inset 0 1px 2px #ffffff4d;transition:all .4s cubic-bezier(.175,.885,.32,1.275);position:relative;overflow:hidden;z-index:10;margin-bottom:8px}.swap-locations-btn:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:radial-gradient(circle at center,rgba(255,255,255,.3) 0%,rgba(255,255,255,0) 70%);opacity:0;transition:opacity .3s ease}.swap-locations-btn:hover{background:linear-gradient(135deg,#00c9ff,#2989d8);transform:scale(1.1);box-shadow:0 6px 20px #2989d859,inset 0 1px 3px #fff6}.swap-locations-btn:hover:before{opacity:1}.swap-locations-btn:active{transform:scale(.95);box-shadow:0 2px 10px #2989d833,inset 0 1px 2px #fff3}.swap-locations-btn i{font-size:0;position:relative}.swap-locations-btn:after{content:\\\"\\\";position:absolute;top:50%;left:50%;width:24px;height:24px;transform:translate(-50%,-50%);background-image:url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='white' d='M7.5 21.5l1.5-1.5-4.5-4.5H16v-2H4.5L9 9 7.5 7.5 1 14l6.5 7.5zm9-15l-1.5 1.5 4.5 4.5H8v2h11.5L15 19l1.5 1.5L23 14l-6.5-7.5z'/%3E%3C/svg%3E\\\");background-repeat:no-repeat;background-position:center;transition:transform .4s cubic-bezier(.175,.885,.32,1.275)}.swap-locations-btn:hover:after{transform:translate(-50%,-50%) rotate(180deg)}@media (min-width: 992px){.swap-button-container{padding-bottom:10px}}.location-option{display:flex;flex-direction:column;gap:4px}.location-name{font-weight:500}.location-details{display:flex;gap:12px;font-size:13px;color:#0009}.location-code{font-weight:500}.location-type{display:flex;align-items:center;gap:4px}.location-type i{font-size:12px;color:#2989d8}.toggle-switch{position:relative;display:inline-flex;align-items:center;cursor:pointer}.toggle-input{opacity:0;width:0;height:0;position:absolute}.toggle-label{display:flex;align-items:center;gap:12px;cursor:pointer;transition:all .3s ease}.toggle-inner{position:relative;display:inline-block;width:52px;height:26px;background:linear-gradient(135deg,#e0e0e0,#f5f5f5);border-radius:50px;transition:all .4s cubic-bezier(.175,.885,.32,1.275);box-shadow:inset 0 1px 3px #0000001a,0 1px 2px #ffffff80;border:1px solid rgba(0,0,0,.05);overflow:hidden}.toggle-inner:before{content:\\\"\\\";position:absolute;left:2px;top:2px;width:20px;height:20px;background:linear-gradient(135deg,#ffffff,#f0f0f0);border-radius:50%;transition:all .4s cubic-bezier(.175,.885,.32,1.275);box-shadow:0 2px 5px #00000026;z-index:2}.toggle-inner:after{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(90deg,rgba(41,137,216,0),rgba(41,137,216,0));opacity:0;transition:all .4s ease;z-index:1}.toggle-input:checked+.toggle-label .toggle-inner{background:linear-gradient(135deg,rgba(41,137,216,.8),rgba(0,201,255,.8));border-color:#2989d833}.toggle-input:checked+.toggle-label .toggle-inner:before{transform:translate(26px);background:linear-gradient(135deg,#ffffff,#f8fcff);box-shadow:0 2px 5px #2989d84d,0 0 0 1px #fffc}.toggle-input:checked+.toggle-label .toggle-inner:after{background:linear-gradient(90deg,rgba(41,137,216,.2),rgba(0,201,255,.2));opacity:1}.toggle-switch-label{font-size:14px;color:#334155;font-weight:500;transition:all .3s ease;letter-spacing:.2px}.toggle-input:checked+.toggle-label .toggle-switch-label{color:#2989d8}.toggle-switch:hover .toggle-inner:before{transform:scale(1.05);box-shadow:0 3px 8px #0003}.toggle-input:checked+.toggle-label:hover .toggle-inner:before{transform:translate(26px) scale(1.05);box-shadow:0 3px 8px #2989d866}.toggle-switch.small .toggle-inner{width:44px;height:22px}.toggle-switch.small .toggle-inner:before{width:16px;height:16px}.toggle-switch.small .toggle-input:checked+.toggle-label .toggle-inner:before{transform:translate(22px)}.toggle-switch.small .toggle-input:checked+.toggle-label:hover .toggle-inner:before{transform:translate(22px) scale(1.05)}.search-button-container{margin-top:20px;display:flex;justify-content:center;position:relative}@media (min-width: 992px){.search-button-container{margin-top:0}}.search-button{background:linear-gradient(135deg,#2989d8,#00c9ff);color:#fff;border:none;border-radius:12px;padding:14px 28px;font-size:16px;font-weight:600;letter-spacing:.5px;cursor:pointer;transition:all .4s cubic-bezier(.175,.885,.32,1.275);display:flex;align-items:center;gap:10px;min-width:140px;justify-content:center;box-shadow:0 6px 20px #2989d840,inset 0 1px 2px #ffffff4d;position:relative;overflow:hidden}.search-button:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(255,255,255,.2) 0%,rgba(255,255,255,0) 50%);z-index:1}.search-button:after{content:\\\"\\\";position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle,rgba(255,255,255,.3) 0%,rgba(255,255,255,0) 60%);opacity:0;transform:scale(.5);transition:transform .8s ease,opacity .8s ease;z-index:1}.search-button i,.search-button span{position:relative;z-index:2;transition:all .3s ease}@media (min-width: 992px){.search-button{padding:0 24px;font-size:15px;min-width:120px;height:48px}.single-line-form .search-button-container{margin-top:29px}}.search-button:hover:not(:disabled){background:linear-gradient(135deg,#00c9ff,#2989d8);transform:translateY(-3px) scale(1.02);box-shadow:0 10px 25px #2989d859,inset 0 1px 3px #fff6}.search-button:hover:after{opacity:1;transform:scale(1)}.search-button:hover i{transform:translate(-3px)}.search-button:hover span{transform:translate(3px)}.search-button:active:not(:disabled){transform:translateY(-1px) scale(.98);box-shadow:0 5px 15px #2989d833,inset 0 1px 2px #fff3}.search-button:disabled{background:linear-gradient(135deg,#b0b0b0,#d0d0d0);cursor:not-allowed;transform:none;box-shadow:0 2px 8px #0000001a;opacity:.7}.search-button i{font-size:18px}.advanced-options-container{margin-top:25px;position:relative;max-width:1100px;margin-left:auto;margin-right:auto}.advanced-options-container:before{content:\\\"\\\";position:absolute;top:-15px;left:0;width:100%;height:1px;background:linear-gradient(90deg,rgba(41,137,216,0) 0%,rgba(41,137,216,.15) 50%,rgba(41,137,216,0) 100%)}.advanced-options-container summary{cursor:pointer;color:#2989d8;font-weight:600;display:flex;align-items:center;justify-content:center;gap:10px;padding:12px 25px;transition:all .4s cubic-bezier(.175,.885,.32,1.275);outline:none;border-radius:50px;background:linear-gradient(135deg,rgba(41,137,216,.08) 0%,rgba(0,201,255,.08) 100%);box-shadow:0 4px 15px #2989d81a,inset 0 1px 2px #ffffff80;width:-moz-fit-content;width:fit-content;margin:0 auto;position:relative;overflow:hidden;border:1px solid rgba(41,137,216,.1);letter-spacing:.3px}.advanced-options-container summary:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(255,255,255,.2) 0%,rgba(255,255,255,0) 100%);opacity:0;transition:opacity .3s ease}.advanced-options-container summary:hover{color:#00c9ff;background:linear-gradient(135deg,rgba(41,137,216,.1) 0%,rgba(0,201,255,.1) 100%);transform:translateY(-3px);box-shadow:0 8px 20px #2989d826,inset 0 1px 3px #fff9}.advanced-options-container summary:hover:before{opacity:1}.advanced-options-container summary i{font-size:18px;transition:all .4s cubic-bezier(.175,.885,.32,1.275)}.advanced-options-container[open] summary i{transform:rotate(180deg)}.advanced-options{margin-top:25px;padding:30px;background:linear-gradient(135deg,rgba(255,255,255,.9),rgba(248,252,255,.9));border-radius:16px;border:1px solid rgba(41,137,216,.1);box-shadow:0 10px 30px #2989d81a,inset 0 1px 8px #fffc;position:relative;animation:scaleIn .5s cubic-bezier(.165,.84,.44,1);overflow:hidden;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}@keyframes scaleIn{0%{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}.advanced-options:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:radial-gradient(circle at top right,rgba(41,137,216,.05) 0%,rgba(41,137,216,0) 70%),radial-gradient(circle at bottom left,rgba(0,201,255,.05) 0%,rgba(0,201,255,0) 70%);pointer-events:none}.checkbox-options{display:flex;flex-wrap:wrap;gap:20px}.checkbox-options .form-group{flex:1 0 45%;transition:transform .3s ease}.checkbox-options .form-group:hover{transform:translateY(-2px)}.error-message{color:#e74c3c;font-size:13px;margin-top:6px;display:flex;align-items:center;gap:6px}.error-message i{font-size:14px}.spinner-container{display:flex;align-items:center;justify-content:center}.spinner{width:20px;height:20px;border:2px solid rgba(255,255,255,.3);border-radius:50%;border-top-color:#fff;animation:spin .8s linear infinite}@keyframes spin{to{transform:rotate(360deg)}}.search-results-container{background-color:#fff;border-radius:16px;box-shadow:0 8px 24px #0000001f;padding:24px;margin-top:24px}.loading-animation{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px}.plane-loader{position:relative;width:200px;height:100px;margin-bottom:24px}.plane-loader i{position:absolute;font-size:32px;color:#2989d8;animation:fly 3s infinite linear;top:40%;left:0}.cloud{position:absolute;width:50px;height:20px;background-color:#0000000d;border-radius:20px}.cloud:nth-child(2){top:20%;left:20%;animation:cloud 8s infinite linear}.cloud:nth-child(3){top:60%;left:40%;animation:cloud 6s infinite linear}.cloud:nth-child(4){top:40%;left:60%;animation:cloud 10s infinite linear}@keyframes fly{0%{transform:translate(0) rotate(0)}to{transform:translate(200px) rotate(0)}}@keyframes cloud{0%{transform:translate(0)}to{transform:translate(-200px)}}.loading-animation p{color:#0009;font-size:16px;font-weight:500}.results-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid rgba(0,0,0,.08)}.results-title h3{font-size:20px;font-weight:600;color:#000c;margin-bottom:4px}.results-count{font-weight:600;color:#2989d8}.filter-option{display:flex;align-items:center;gap:8px;color:#0009}.filter-select{padding:8px 12px;border:1px solid rgba(0,0,0,.12);border-radius:6px;font-size:14px;color:#000c;background-color:#fff}.flight-card{background-color:#fff;border-radius:12px;box-shadow:0 4px 16px #00000014;overflow:hidden;transition:transform .3s ease,box-shadow .3s ease;margin-bottom:20px;border:1px solid rgba(0,0,0,.05)}.flight-card:hover{transform:translateY(-4px);box-shadow:0 8px 24px #0000001f}.flight-header{display:flex;justify-content:space-between;align-items:center;padding:16px 20px;background-color:#00000005;border-bottom:1px solid rgba(0,0,0,.05)}.airline-info{display:flex;align-items:center;gap:12px}.airline-logo-container{width:40px;height:40px;display:flex;align-items:center;justify-content:center;background-color:#fff;border-radius:8px;box-shadow:0 2px 6px #0000001a}.airline-logo{max-width:32px;max-height:32px;object-fit:contain}.airline-icon{font-size:20px;color:#2989d8}.airline-details{display:flex;flex-direction:column}.airline-name{font-weight:600;color:#000c;font-size:15px}.flight-number{font-size:13px;color:#0009}.flight-badges{display:flex;gap:8px}.flight-badge{display:flex;align-items:center;gap:4px;padding:4px 8px;background-color:#2989d81a;color:#2989d8;border-radius:4px;font-size:12px;font-weight:500}.flight-price{text-align:right}.price-label{display:block;font-size:12px;color:#0009;margin-bottom:4px}.price{font-size:22px;font-weight:700;color:#2989d8}.availability{display:flex;align-items:center;gap:4px;color:#e74c3c;font-size:12px;margin-top:4px}.flight-details{padding:20px}.flight-route{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.departure,.arrival{flex:1}.time{font-size:20px;font-weight:700;color:#000c;margin-bottom:6px}.location{display:flex;flex-direction:column}.airport-code{font-weight:600;color:#000000b3;font-size:15px}.city-name{color:#0009;font-size:13px}.flight-duration{flex:1;text-align:center;padding:0 20px}.duration-line{position:relative;display:flex;align-items:center;justify-content:center;margin-bottom:10px}.dot{width:10px;height:10px;background-color:#2989d8;border-radius:50%;z-index:1}.departure-dot{background-color:#4caf50}.arrival-dot{background-color:#f44336}.line-container{flex:1;position:relative;height:20px;display:flex;align-items:center}.line{width:100%;height:2px;background-color:#2989d8}.plane-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#2989d8;font-size:14px}.duration-text{display:flex;align-items:center;justify-content:center;gap:6px;font-size:14px;font-weight:600;color:#000000b3;margin-bottom:6px}.stops{font-size:13px;color:#0009}.stop-count{font-weight:600;color:#f44336}.stops.direct{color:#4caf50;font-weight:500}.flight-features{display:flex;gap:16px;margin-top:16px;padding-top:16px;border-top:1px dashed rgba(0,0,0,.1)}.feature{display:flex;align-items:center;gap:6px;font-size:13px;color:#000000b3}.feature i{color:#2989d8;font-size:14px}.flight-actions{display:flex;gap:12px;justify-content:flex-end;padding:16px 20px;background-color:#00000005;border-top:1px solid rgba(0,0,0,.05)}.view-details-button{display:flex;align-items:center;gap:6px;padding:10px 16px;background-color:#0000000d;color:#000000b3;border:none;border-radius:6px;font-size:14px;font-weight:500;cursor:pointer;transition:all .2s ease}.view-details-button:hover{background-color:#0000001a}.select-button{display:flex;align-items:center;gap:6px;padding:10px 16px;background-color:#2989d8;color:#fff;border:none;border-radius:6px;font-size:14px;font-weight:500;cursor:pointer;transition:all .2s ease;box-shadow:0 2px 8px #2989d84d}.select-button:hover{background-color:#1e5799;transform:translateY(-2px);box-shadow:0 4px 12px #2989d866}.select-button:disabled{background-color:#0000001a;color:#0006;cursor:not-allowed;transform:none;box-shadow:none}.no-results{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 20px;text-align:center}.no-results-icon{width:80px;height:80px;background-color:#0000000d;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:20px}.no-results-icon i{font-size:32px;color:#0000004d}.no-results h3{font-size:20px;font-weight:600;color:#000c;margin-bottom:8px}.no-results p{color:#0009;margin-bottom:24px;max-width:500px}.no-results-suggestions{display:flex;flex-wrap:wrap;gap:16px;justify-content:center}.suggestion{display:flex;align-items:center;gap:8px;padding:10px 16px;background-color:#0000000d;border-radius:8px;color:#000000b3;font-size:14px}.suggestion i{color:#2989d8}.error-container{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 20px;text-align:center}.error-icon{width:80px;height:80px;background-color:#e74c3c1a;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:20px}.error-icon i{font-size:32px;color:#e74c3c}.error-container h3{font-size:20px;font-weight:600;color:#000c;margin-bottom:8px}.error-container .error-message{color:#0009;margin-bottom:24px;font-size:15px;justify-content:center}.retry-button{display:flex;align-items:center;gap:8px;padding:12px 20px;background-color:#2989d8;color:#fff;border:none;border-radius:8px;font-size:15px;font-weight:500;cursor:pointer;transition:all .2s ease;box-shadow:0 2px 8px #2989d84d}.retry-button:hover{background-color:#1e5799;transform:translateY(-2px);box-shadow:0 4px 12px #2989d866}@media (max-width: 768px){.form-row{flex-direction:column;gap:16px}.swap-locations-btn{display:none}.flight-route{flex-direction:column;gap:20px}.departure,.arrival{text-align:center}.flight-duration{margin:16px 0}.flight-header{flex-direction:column;gap:16px}.airline-info{width:100%;justify-content:center}.flight-badges{justify-content:center}.flight-price{text-align:center;width:100%}.flight-features{flex-direction:column;gap:12px;align-items:center}.flight-actions{flex-direction:column}.view-details-button,.select-button{width:100%;justify-content:center}}@media (max-width: 576px){.page-header{flex-direction:column;gap:24px}.header-content{text-align:center}.page-title{font-size:1.75rem}.checkbox-options .form-group{flex:1 0 100%}}\\n\", \".flight-type-tabs{display:flex;margin-bottom:20px;border-radius:8px;overflow:hidden;background-color:#ffffff1a;box-shadow:0 2px 4px #0000001a}.flight-type-tab{flex:1;padding:12px 15px;background:transparent;border:none;color:#fff;font-weight:500;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;gap:8px}.flight-type-tab:hover{background-color:#fff3}.flight-type-tab.active{background-color:#ffffff4d;box-shadow:inset 0 -3px #fff}.flight-type-tab i{font-size:14px}.return-date-field{transition:all .3s ease;opacity:1;height:auto;overflow:hidden}.return-date-field.hidden{opacity:0;height:0;margin:0;padding:0}\\n\", \".round-trip-badge{position:absolute;top:-10px;right:20px;background-color:#3498db;color:#fff;padding:5px 10px;border-radius:15px;font-size:12px;font-weight:700;box-shadow:0 2px 4px #0003;z-index:1}.round-trip-badge i{margin-right:5px}.flight-card.round-trip{position:relative;border-left:4px solid #3498db;padding-top:20px}.round-trip-details{display:flex;flex-direction:column;gap:20px}.round-trip-section{border:1px solid #e0e0e0;border-radius:8px;padding:15px;background-color:#f9f9f9}.round-trip-section.outbound{border-left:3px solid #4CAF50}.round-trip-section.inbound{border-left:3px solid #FF5722}.round-trip-header{display:flex;align-items:center;margin-bottom:10px;padding-bottom:5px;border-bottom:1px dashed #ccc}.round-trip-header i{margin-right:8px;color:#555}.round-trip-label{font-weight:700;color:#333}.stay-duration{display:flex;align-items:center;justify-content:center;padding:10px;background-color:#e8f4fd;border-radius:8px;color:#06c;font-weight:500}.stay-duration i{margin-right:8px;color:#06c}@media (max-width: 768px){.round-trip-details{gap:15px}.round-trip-section{padding:10px}.round-trip-badge{font-size:10px;padding:3px 8px}}\\n\"],\n        encapsulation: 2\n      });\n    }\n  }\n  return SearchPriceComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}