{"ast": null, "code": "import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction LoginComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction LoginComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"div\", 43);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(authService, router, snackBar) {\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.credentials = {\n      Agency: '',\n      User: '',\n      Password: ''\n    };\n    this.loading = false;\n    this.error = '';\n    this.hidePassword = true;\n  }\n  ngOnInit() {\n    // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n    this.authService.logout();\n    // Animation d'entrée\n    document.body.classList.add('login-page-active');\n  }\n  ngOnDestroy() {\n    // Nettoyage de la classe lors de la destruction du composant\n    document.body.classList.remove('login-page-active');\n  }\n  onSubmit() {\n    if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n      this.error = 'Veuillez remplir tous les champs requis.';\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    this.authService.login(this.credentials).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.header.success) {\n          this.snackBar.open('Connexion réussie! Bienvenue.', 'Fermer', {\n            duration: 3000,\n            panelClass: 'success-snackbar',\n            verticalPosition: 'top'\n          });\n          this.router.navigate(['/accueil']);\n        } else {\n          this.error = 'Échec de l\\'authentification. Veuillez vérifier vos identifiants.';\n        }\n      },\n      error: err => {\n        this.loading = false;\n        this.error = 'Une erreur s\\'est produite lors de la connexion. Veuillez réessayer.';\n        console.error('Login error:', err);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 58,\n      vars: 9,\n      consts: [[1, \"login-page\"], [1, \"login-background\"], [1, \"login-shapes\"], [1, \"shape\", \"shape-1\"], [1, \"shape\", \"shape-2\"], [1, \"shape\", \"shape-3\"], [1, \"shape\", \"shape-4\"], [1, \"shape\", \"shape-5\"], [1, \"login-illustration\"], [\"src\", \"assets/images/login-illustration.svg\", \"alt\", \"Travel Illustration\"], [1, \"login-container\"], [1, \"login-card\", \"animate-slide-up\"], [1, \"login-logo\"], [1, \"logo-icon\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"login-header\"], [1, \"login-form\", 3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"agency\"], [1, \"input-with-icon\"], [1, \"fas\", \"fa-building\"], [\"type\", \"text\", \"id\", \"agency\", \"name\", \"agency\", \"required\", \"\", \"placeholder\", \"Entrez le nom de votre agence\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"input-focus-border\"], [\"for\", \"username\"], [1, \"fas\", \"fa-user\"], [\"type\", \"text\", \"id\", \"username\", \"name\", \"username\", \"required\", \"\", \"placeholder\", \"Entrez votre nom d'utilisateur\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [1, \"fas\", \"fa-lock\"], [\"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"placeholder\", \"Entrez votre mot de passe\", 1, \"form-control\", 3, \"type\", \"ngModel\", \"ngModelChange\"], [\"type\", \"button\", 1, \"password-toggle\", 3, \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"login-footer\"], [1, \"footer-decoration\"], [1, \"decoration-line\"], [1, \"fas\", \"fa-globe-americas\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"spinner-container\"], [1, \"spinner\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8);\n          i0.ɵɵelement(9, \"img\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13);\n          i0.ɵɵelement(14, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"h1\");\n          i0.ɵɵtext(16, \"E-Tourism\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 15)(18, \"h2\");\n          i0.ɵɵtext(19, \"Bienvenue\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\");\n          i0.ɵɵtext(21, \"Connectez-vous pour acc\\u00E9der \\u00E0 votre compte\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"form\", 16, 17);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_22_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(24, \"div\", 18)(25, \"label\", 19);\n          i0.ɵɵtext(26, \"Agence\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 20);\n          i0.ɵɵelement(28, \"i\", 21);\n          i0.ɵɵelementStart(29, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_29_listener($event) {\n            return ctx.credentials.Agency = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"div\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 18)(32, \"label\", 24);\n          i0.ɵɵtext(33, \"Nom d'utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 20);\n          i0.ɵɵelement(35, \"i\", 25);\n          i0.ɵɵelementStart(36, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_36_listener($event) {\n            return ctx.credentials.User = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"div\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 18)(39, \"label\", 27);\n          i0.ɵɵtext(40, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 20);\n          i0.ɵɵelement(42, \"i\", 28);\n          i0.ɵɵelementStart(43, \"input\", 29);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_43_listener($event) {\n            return ctx.credentials.Password = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_44_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelement(45, \"i\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"div\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(47, LoginComponent_div_47_Template, 4, 1, \"div\", 32);\n          i0.ɵɵelementStart(48, \"button\", 33);\n          i0.ɵɵtemplate(49, LoginComponent_div_49_Template, 2, 0, \"div\", 34);\n          i0.ɵɵtemplate(50, LoginComponent_span_50_Template, 2, 0, \"span\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 36)(52, \"div\", 37);\n          i0.ɵɵelement(53, \"div\", 38)(54, \"i\", 39)(55, \"div\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\");\n          i0.ɵɵtext(57, \"\\u00A9 2023 E-Tourism. Tous droits r\\u00E9serv\\u00E9s.\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(23);\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngModel\", ctx.credentials.Agency);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.credentials.User);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\")(\"ngModel\", ctx.credentials.Password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.hidePassword ? \"fa-eye\" : \"fa-eye-slash\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !_r0.form.valid || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm],\n      styles: [\".login-page[_ngcontent-%COMP%] {\\n  position: relative;\\n  min-height: 100vh;\\n  width: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.login-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, var(--ocean-blue) 0%, var(--primary-color) 60%, var(--sky-blue) 100%);\\n  z-index: -1;\\n}\\n\\n.login-shapes[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.shape[_ngcontent-%COMP%] {\\n  position: absolute;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 50%;\\n}\\n\\n.shape-1[_ngcontent-%COMP%] {\\n  width: 600px;\\n  height: 600px;\\n  top: -300px;\\n  right: -200px;\\n  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 70%, transparent 100%);\\n  animation: _ngcontent-%COMP%_float 20s infinite alternate ease-in-out;\\n}\\n\\n.shape-2[_ngcontent-%COMP%] {\\n  width: 400px;\\n  height: 400px;\\n  bottom: -200px;\\n  left: -100px;\\n  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 70%, transparent 100%);\\n  animation: _ngcontent-%COMP%_float 25s infinite alternate-reverse ease-in-out;\\n}\\n\\n.shape-3[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: 300px;\\n  top: 20%;\\n  right: 10%;\\n  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 70%, transparent 100%);\\n  animation: _ngcontent-%COMP%_pulse 15s infinite alternate ease-in-out;\\n}\\n\\n.shape-4[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  bottom: 15%;\\n  right: 20%;\\n  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 70%, transparent 100%);\\n  animation: _ngcontent-%COMP%_float 18s infinite alternate-reverse ease-in-out;\\n}\\n\\n.shape-5[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  top: 30%;\\n  left: 15%;\\n  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 70%, transparent 100%);\\n  animation: _ngcontent-%COMP%_pulse 12s infinite alternate ease-in-out;\\n}\\n\\n.login-illustration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 5%;\\n  right: 5%;\\n  width: 40%;\\n  max-width: 500px;\\n  opacity: 0.9;\\n  animation: _ngcontent-%COMP%_float 6s infinite alternate ease-in-out;\\n  display: none; \\n\\n}\\n\\n.login-illustration[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0% {\\n    transform: translateY(0) rotate(0deg);\\n  }\\n  100% {\\n    transform: translateY(20px) rotate(3deg);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n    opacity: 0.5;\\n  }\\n  100% {\\n    transform: scale(1.2);\\n    opacity: 0.2;\\n  }\\n}\\n\\n.login-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  width: 100%;\\n  padding: var(--spacing-md);\\n  z-index: 1;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 450px;\\n  background-color: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: var(--border-radius-large);\\n  box-shadow: var(--elevation-4), 0 10px 30px rgba(0, 0, 0, 0.1);\\n  padding: var(--spacing-xl);\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_cardAppear 0.8s var(--transition-bounce) forwards;\\n}\\n\\n@keyframes _ngcontent-%COMP%_cardAppear {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(30px) scale(0.95);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n\\n.login-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin-bottom: var(--spacing-lg);\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 80px;\\n  height: 80px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\\n  border-radius: 50%;\\n  margin-bottom: var(--spacing-md);\\n  box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.3);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  color: white;\\n}\\n\\n.login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.75rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, var(--primary-dark), var(--accent-color));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  margin: 0;\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: var(--spacing-lg);\\n}\\n\\n.login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: var(--text-primary);\\n  margin-bottom: var(--spacing-xs);\\n  font-size: 1.75rem;\\n  font-weight: 600;\\n}\\n\\n.login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-md);\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-md);\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: var(--spacing-xs);\\n  font-weight: 500;\\n  color: var(--text-primary);\\n  font-size: 0.9rem;\\n  transition: color var(--transition-fast);\\n}\\n\\n.input-with-icon[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.input-with-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 16px;\\n  color: var(--primary-color);\\n  font-size: 1rem;\\n  transition: all var(--transition-fast);\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px 14px 14px 45px;\\n  border: 1px solid var(--divider-color);\\n  border-radius: var(--border-radius-medium);\\n  font-size: 1rem;\\n  transition: all var(--transition-fast);\\n  background-color: var(--surface-color);\\n  color: var(--text-primary);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus    + .input-focus-border[_ngcontent-%COMP%] {\\n  transform: scaleX(1);\\n}\\n\\n.input-focus-border[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 2px;\\n  background: linear-gradient(to right, var(--primary-color), var(--accent-color));\\n  transform: scaleX(0);\\n  transform-origin: left;\\n  transition: transform var(--transition-medium);\\n}\\n\\n.form-control[_ngcontent-%COMP%]::placeholder {\\n  color: var(--text-hint);\\n}\\n\\n.input-with-icon[_ngcontent-%COMP%]:focus-within   i[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n}\\n\\n.password-toggle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 12px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  background: none;\\n  border: none;\\n  color: var(--text-secondary);\\n  cursor: pointer;\\n  padding: 8px;\\n  font-size: 1rem;\\n  transition: all var(--transition-fast);\\n  border-radius: 50%;\\n  z-index: 2;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 36px;\\n  width: 36px;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  color: var(--error-color);\\n  background-color: rgba(244, 67, 54, 0.1);\\n  padding: var(--spacing-sm) var(--spacing-md);\\n  border-radius: var(--border-radius-medium);\\n  margin-bottom: var(--spacing-sm);\\n  animation: _ngcontent-%COMP%_shake 0.5s cubic-bezier(.36,.07,.19,.97) both;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shake {\\n  10%, 90% { transform: translate3d(-1px, 0, 0); }\\n  20%, 80% { transform: translate3d(2px, 0, 0); }\\n  30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }\\n  40%, 60% { transform: translate3d(3px, 0, 0); }\\n}\\n\\n.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 50px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  margin-top: var(--spacing-md);\\n  position: relative;\\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\\n  color: white;\\n  border: none;\\n  border-radius: var(--border-radius-medium);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  overflow: hidden;\\n  box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.login-button[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: all 0.6s;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled)::before {\\n  left: 100%;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(var(--primary-color-rgb), 0.4);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(1px);\\n  box-shadow: 0 2px 10px rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  background: linear-gradient(135deg, #ccc, #ddd);\\n  cursor: not-allowed;\\n  box-shadow: none;\\n}\\n\\n.spinner-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  border-top-color: white;\\n  animation: _ngcontent-%COMP%_spin 0.8s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n.login-footer[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-xl);\\n  text-align: center;\\n}\\n\\n.footer-decoration[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.decoration-line[_ngcontent-%COMP%] {\\n  height: 1px;\\n  width: 60px;\\n  background: linear-gradient(to right, transparent, var(--divider-color));\\n}\\n\\n.decoration-line[_ngcontent-%COMP%]:last-child {\\n  background: linear-gradient(to left, transparent, var(--divider-color));\\n}\\n\\n.footer-decoration[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 1.2rem;\\n  margin: 0 var(--spacing-sm);\\n}\\n\\n.login-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-hint);\\n  font-size: 0.75rem;\\n  margin: 0;\\n}\\n\\n\\n\\n@media (min-width: 768px) {\\n  .login-illustration[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n\\n  .login-container[_ngcontent-%COMP%] {\\n    margin-left: -10%;\\n  }\\n}\\n\\n@media (max-width: 767px) {\\n  .login-card[_ngcontent-%COMP%] {\\n    max-width: 90%;\\n  }\\n}\\n\\n@media (max-width: 500px) {\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg);\\n  }\\n\\n  .logo-icon[_ngcontent-%COMP%] {\\n    width: 70px;\\n    height: 70px;\\n  }\\n\\n  .logo-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .form-control[_ngcontent-%COMP%] {\\n    padding: 12px 12px 12px 40px;\\n  }\\n}\\n\\n\\n\\nbody.login-page-active[_nghost-%COMP%], body.login-page-active   [_nghost-%COMP%] {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9sb2dpbi9sb2dpbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usa0JBQWtCO0VBQ2xCLGlCQUFpQjtFQUNqQixXQUFXO0VBQ1gsZ0JBQWdCO0VBQ2hCLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLE1BQU07RUFDTixPQUFPO0VBQ1AsV0FBVztFQUNYLFlBQVk7RUFDWix5R0FBeUc7RUFDekcsV0FBVztBQUNiOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLE1BQU07RUFDTixPQUFPO0VBQ1AsV0FBVztFQUNYLFlBQVk7RUFDWixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsMENBQTBDO0VBQzFDLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLFlBQVk7RUFDWixhQUFhO0VBQ2IsV0FBVztFQUNYLGFBQWE7RUFDYiwySEFBMkg7RUFDM0gsbURBQW1EO0FBQ3JEOztBQUVBO0VBQ0UsWUFBWTtFQUNaLGFBQWE7RUFDYixjQUFjO0VBQ2QsWUFBWTtFQUNaLDJIQUEySDtFQUMzSCwyREFBMkQ7QUFDN0Q7O0FBRUE7RUFDRSxZQUFZO0VBQ1osYUFBYTtFQUNiLFFBQVE7RUFDUixVQUFVO0VBQ1YsNEhBQTRIO0VBQzVILG1EQUFtRDtBQUNyRDs7QUFFQTtFQUNFLFlBQVk7RUFDWixhQUFhO0VBQ2IsV0FBVztFQUNYLFVBQVU7RUFDViw0SEFBNEg7RUFDNUgsMkRBQTJEO0FBQzdEOztBQUVBO0VBQ0UsWUFBWTtFQUNaLGFBQWE7RUFDYixRQUFRO0VBQ1IsU0FBUztFQUNULDRIQUE0SDtFQUM1SCxtREFBbUQ7QUFDckQ7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsVUFBVTtFQUNWLFNBQVM7RUFDVCxVQUFVO0VBQ1YsZ0JBQWdCO0VBQ2hCLFlBQVk7RUFDWixrREFBa0Q7RUFDbEQsYUFBYSxFQUFFLGdDQUFnQztBQUNqRDs7QUFFQTtFQUNFLFdBQVc7RUFDWCxZQUFZO0FBQ2Q7O0FBRUE7RUFDRTtJQUNFLHFDQUFxQztFQUN2QztFQUNBO0lBQ0Usd0NBQXdDO0VBQzFDO0FBQ0Y7O0FBRUE7RUFDRTtJQUNFLG1CQUFtQjtJQUNuQixZQUFZO0VBQ2Q7RUFDQTtJQUNFLHFCQUFxQjtJQUNyQixZQUFZO0VBQ2Q7QUFDRjs7QUFFQTtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsbUJBQW1CO0VBQ25CLFdBQVc7RUFDWCwwQkFBMEI7RUFDMUIsVUFBVTtBQUNaOztBQUVBO0VBQ0UsV0FBVztFQUNYLGdCQUFnQjtFQUNoQiwyQ0FBMkM7RUFDM0MsbUNBQTJCO1VBQTNCLDJCQUEyQjtFQUMzQix5Q0FBeUM7RUFDekMsOERBQThEO0VBQzlELDBCQUEwQjtFQUMxQixnQkFBZ0I7RUFDaEIsNERBQTREO0FBQzlEOztBQUVBO0VBQ0U7SUFDRSxVQUFVO0lBQ1YsdUNBQXVDO0VBQ3pDO0VBQ0E7SUFDRSxVQUFVO0lBQ1YsaUNBQWlDO0VBQ25DO0FBQ0Y7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtFQUNuQixnQ0FBZ0M7QUFDbEM7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2QixXQUFXO0VBQ1gsWUFBWTtFQUNaLDhFQUE4RTtFQUM5RSxrQkFBa0I7RUFDbEIsZ0NBQWdDO0VBQ2hDLDBEQUEwRDtFQUMxRCxrQkFBa0I7RUFDbEIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixNQUFNO0VBQ04sT0FBTztFQUNQLFdBQVc7RUFDWCxZQUFZO0VBQ1osa0dBQWtHO0VBQ2xHLDhCQUE4QjtBQUNoQzs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixZQUFZO0FBQ2Q7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsZ0JBQWdCO0VBQ2hCLDZFQUE2RTtFQUM3RSw2QkFBNkI7RUFDN0Isb0NBQW9DO0VBQ3BDLFNBQVM7QUFDWDs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixnQ0FBZ0M7QUFDbEM7O0FBRUE7RUFDRSwwQkFBMEI7RUFDMUIsZ0NBQWdDO0VBQ2hDLGtCQUFrQjtFQUNsQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSw0QkFBNEI7RUFDNUIsZUFBZTtFQUNmLFNBQVM7QUFDWDs7QUFFQTtFQUNFLGFBQWE7RUFDYixzQkFBc0I7RUFDdEIsc0JBQXNCO0FBQ3hCOztBQUVBO0VBQ0UsZ0NBQWdDO0FBQ2xDOztBQUVBO0VBQ0UsY0FBYztFQUNkLGdDQUFnQztFQUNoQyxnQkFBZ0I7RUFDaEIsMEJBQTBCO0VBQzFCLGlCQUFpQjtFQUNqQix3Q0FBd0M7QUFDMUM7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsYUFBYTtFQUNiLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixVQUFVO0VBQ1YsMkJBQTJCO0VBQzNCLGVBQWU7RUFDZixzQ0FBc0M7QUFDeEM7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsNEJBQTRCO0VBQzVCLHNDQUFzQztFQUN0QywwQ0FBMEM7RUFDMUMsZUFBZTtFQUNmLHNDQUFzQztFQUN0QyxzQ0FBc0M7RUFDdEMsMEJBQTBCO0VBQzFCLHlDQUF5QztBQUMzQzs7QUFFQTtFQUNFLGFBQWE7RUFDYixrQ0FBa0M7RUFDbEMsMERBQTBEO0FBQzVEOztBQUVBO0VBQ0Usb0JBQW9CO0FBQ3RCOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLFNBQVM7RUFDVCxPQUFPO0VBQ1AsV0FBVztFQUNYLFdBQVc7RUFDWCxnRkFBZ0Y7RUFDaEYsb0JBQW9CO0VBQ3BCLHNCQUFzQjtFQUN0Qiw4Q0FBOEM7QUFDaEQ7O0FBRUE7RUFDRSx1QkFBdUI7QUFDekI7O0FBRUE7RUFDRSwwQkFBMEI7QUFDNUI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsV0FBVztFQUNYLFFBQVE7RUFDUiwyQkFBMkI7RUFDM0IsZ0JBQWdCO0VBQ2hCLFlBQVk7RUFDWiw0QkFBNEI7RUFDNUIsZUFBZTtFQUNmLFlBQVk7RUFDWixlQUFlO0VBQ2Ysc0NBQXNDO0VBQ3RDLGtCQUFrQjtFQUNsQixVQUFVO0VBQ1YsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsWUFBWTtFQUNaLFdBQVc7QUFDYjs7QUFFQTtFQUNFLDJCQUEyQjtFQUMzQixzREFBc0Q7QUFDeEQ7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHNCQUFzQjtFQUN0Qix5QkFBeUI7RUFDekIsd0NBQXdDO0VBQ3hDLDRDQUE0QztFQUM1QywwQ0FBMEM7RUFDMUMsZ0NBQWdDO0VBQ2hDLHdEQUF3RDtBQUMxRDs7QUFFQTtFQUNFLFdBQVcsa0NBQWtDLEVBQUU7RUFDL0MsV0FBVyxpQ0FBaUMsRUFBRTtFQUM5QyxnQkFBZ0Isa0NBQWtDLEVBQUU7RUFDcEQsV0FBVyxpQ0FBaUMsRUFBRTtBQUNoRDs7QUFFQTtFQUNFLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLFdBQVc7RUFDWCxZQUFZO0VBQ1osZUFBZTtFQUNmLGdCQUFnQjtFQUNoQiw2QkFBNkI7RUFDN0Isa0JBQWtCO0VBQ2xCLDhFQUE4RTtFQUM5RSxZQUFZO0VBQ1osWUFBWTtFQUNaLDBDQUEwQztFQUMxQyxlQUFlO0VBQ2Ysc0NBQXNDO0VBQ3RDLGdCQUFnQjtFQUNoQiwwREFBMEQ7QUFDNUQ7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLE1BQU07RUFDTixXQUFXO0VBQ1gsV0FBVztFQUNYLFlBQVk7RUFDWixzRkFBc0Y7RUFDdEYsb0JBQW9CO0FBQ3RCOztBQUVBO0VBQ0UsVUFBVTtBQUNaOztBQUVBO0VBQ0UsMkJBQTJCO0VBQzNCLDBEQUEwRDtBQUM1RDs7QUFFQTtFQUNFLDBCQUEwQjtFQUMxQiwwREFBMEQ7QUFDNUQ7O0FBRUE7RUFDRSwrQ0FBK0M7RUFDL0MsbUJBQW1CO0VBQ25CLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixNQUFNO0VBQ04sT0FBTztFQUNQLFFBQVE7RUFDUixTQUFTO0VBQ1QsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7QUFDekI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLDBDQUEwQztFQUMxQyxrQkFBa0I7RUFDbEIsdUJBQXVCO0VBQ3ZCLG9DQUFvQztBQUN0Qzs7QUFFQTtFQUNFLEtBQUsseUJBQXlCLEVBQUU7QUFDbEM7O0FBRUE7RUFDRSw2QkFBNkI7RUFDN0Isa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsZ0NBQWdDO0FBQ2xDOztBQUVBO0VBQ0UsV0FBVztFQUNYLFdBQVc7RUFDWCx3RUFBd0U7QUFDMUU7O0FBRUE7RUFDRSx1RUFBdUU7QUFDekU7O0FBRUE7RUFDRSwyQkFBMkI7RUFDM0IsaUJBQWlCO0VBQ2pCLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLHVCQUF1QjtFQUN2QixrQkFBa0I7RUFDbEIsU0FBUztBQUNYOztBQUVBLHNCQUFzQjtBQUN0QjtFQUNFO0lBQ0UsY0FBYztFQUNoQjs7RUFFQTtJQUNFLGlCQUFpQjtFQUNuQjtBQUNGOztBQUVBO0VBQ0U7SUFDRSxjQUFjO0VBQ2hCO0FBQ0Y7O0FBRUE7RUFDRTtJQUNFLDBCQUEwQjtFQUM1Qjs7RUFFQTtJQUNFLFdBQVc7SUFDWCxZQUFZO0VBQ2Q7O0VBRUE7SUFDRSxlQUFlO0VBQ2pCOztFQUVBO0lBQ0UsaUJBQWlCO0VBQ25COztFQUVBO0lBQ0UsaUJBQWlCO0VBQ25COztFQUVBO0lBQ0UsNEJBQTRCO0VBQzlCO0FBQ0Y7O0FBRUEsMkJBQTJCO0FBQzNCO0VBQ0Usa0NBQWtDO0FBQ3BDOztBQUVBO0VBQ0U7SUFDRSw0QkFBNEI7RUFDOUI7RUFDQTtJQUNFLDJCQUEyQjtFQUM3QjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmxvZ2luLXBhZ2Uge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICB3aWR0aDogMTAwJTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG59XG5cbi5sb2dpbi1iYWNrZ3JvdW5kIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHZhcigtLW9jZWFuLWJsdWUpIDAlLCB2YXIoLS1wcmltYXJ5LWNvbG9yKSA2MCUsIHZhcigtLXNreS1ibHVlKSAxMDAlKTtcbiAgei1pbmRleDogLTE7XG59XG5cbi5sb2dpbi1zaGFwZXMge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLnNoYXBlIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbn1cblxuLnNoYXBlLTEge1xuICB3aWR0aDogNjAwcHg7XG4gIGhlaWdodDogNjAwcHg7XG4gIHRvcDogLTMwMHB4O1xuICByaWdodDogLTIwMHB4O1xuICBiYWNrZ3JvdW5kOiByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IGNlbnRlciwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIDAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpIDcwJSwgdHJhbnNwYXJlbnQgMTAwJSk7XG4gIGFuaW1hdGlvbjogZmxvYXQgMjBzIGluZmluaXRlIGFsdGVybmF0ZSBlYXNlLWluLW91dDtcbn1cblxuLnNoYXBlLTIge1xuICB3aWR0aDogNDAwcHg7XG4gIGhlaWdodDogNDAwcHg7XG4gIGJvdHRvbTogLTIwMHB4O1xuICBsZWZ0OiAtMTAwcHg7XG4gIGJhY2tncm91bmQ6IHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgY2VudGVyLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSkgNzAlLCB0cmFuc3BhcmVudCAxMDAlKTtcbiAgYW5pbWF0aW9uOiBmbG9hdCAyNXMgaW5maW5pdGUgYWx0ZXJuYXRlLXJldmVyc2UgZWFzZS1pbi1vdXQ7XG59XG5cbi5zaGFwZS0zIHtcbiAgd2lkdGg6IDMwMHB4O1xuICBoZWlnaHQ6IDMwMHB4O1xuICB0b3A6IDIwJTtcbiAgcmlnaHQ6IDEwJTtcbiAgYmFja2dyb3VuZDogcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCBjZW50ZXIsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wOCkgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wMykgNzAlLCB0cmFuc3BhcmVudCAxMDAlKTtcbiAgYW5pbWF0aW9uOiBwdWxzZSAxNXMgaW5maW5pdGUgYWx0ZXJuYXRlIGVhc2UtaW4tb3V0O1xufVxuXG4uc2hhcGUtNCB7XG4gIHdpZHRoOiAyMDBweDtcbiAgaGVpZ2h0OiAyMDBweDtcbiAgYm90dG9tOiAxNSU7XG4gIHJpZ2h0OiAyMCU7XG4gIGJhY2tncm91bmQ6IHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgY2VudGVyLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDgpIDAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDMpIDcwJSwgdHJhbnNwYXJlbnQgMTAwJSk7XG4gIGFuaW1hdGlvbjogZmxvYXQgMThzIGluZmluaXRlIGFsdGVybmF0ZS1yZXZlcnNlIGVhc2UtaW4tb3V0O1xufVxuXG4uc2hhcGUtNSB7XG4gIHdpZHRoOiAxNTBweDtcbiAgaGVpZ2h0OiAxNTBweDtcbiAgdG9wOiAzMCU7XG4gIGxlZnQ6IDE1JTtcbiAgYmFja2dyb3VuZDogcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCBjZW50ZXIsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wOCkgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wMykgNzAlLCB0cmFuc3BhcmVudCAxMDAlKTtcbiAgYW5pbWF0aW9uOiBwdWxzZSAxMnMgaW5maW5pdGUgYWx0ZXJuYXRlIGVhc2UtaW4tb3V0O1xufVxuXG4ubG9naW4taWxsdXN0cmF0aW9uIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBib3R0b206IDUlO1xuICByaWdodDogNSU7XG4gIHdpZHRoOiA0MCU7XG4gIG1heC13aWR0aDogNTAwcHg7XG4gIG9wYWNpdHk6IDAuOTtcbiAgYW5pbWF0aW9uOiBmbG9hdCA2cyBpbmZpbml0ZSBhbHRlcm5hdGUgZWFzZS1pbi1vdXQ7XG4gIGRpc3BsYXk6IG5vbmU7IC8qIEhpZGRlbiBvbiBtb2JpbGUgYnkgZGVmYXVsdCAqL1xufVxuXG4ubG9naW4taWxsdXN0cmF0aW9uIGltZyB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IGF1dG87XG59XG5cbkBrZXlmcmFtZXMgZmxvYXQge1xuICAwJSB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApIHJvdGF0ZSgwZGVnKTtcbiAgfVxuICAxMDAlIHtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMjBweCkgcm90YXRlKDNkZWcpO1xuICB9XG59XG5cbkBrZXlmcmFtZXMgcHVsc2Uge1xuICAwJSB7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxKTtcbiAgICBvcGFjaXR5OiAwLjU7XG4gIH1cbiAgMTAwJSB7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjIpO1xuICAgIG9wYWNpdHk6IDAuMjtcbiAgfVxufVxuXG4ubG9naW4tY29udGFpbmVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIHdpZHRoOiAxMDAlO1xuICBwYWRkaW5nOiB2YXIoLS1zcGFjaW5nLW1kKTtcbiAgei1pbmRleDogMTtcbn1cblxuLmxvZ2luLWNhcmQge1xuICB3aWR0aDogMTAwJTtcbiAgbWF4LXdpZHRoOiA0NTBweDtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzLWxhcmdlKTtcbiAgYm94LXNoYWRvdzogdmFyKC0tZWxldmF0aW9uLTQpLCAwIDEwcHggMzBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIHBhZGRpbmc6IHZhcigtLXNwYWNpbmcteGwpO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBhbmltYXRpb246IGNhcmRBcHBlYXIgMC44cyB2YXIoLS10cmFuc2l0aW9uLWJvdW5jZSkgZm9yd2FyZHM7XG59XG5cbkBrZXlmcmFtZXMgY2FyZEFwcGVhciB7XG4gIDAlIHtcbiAgICBvcGFjaXR5OiAwO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgzMHB4KSBzY2FsZSgwLjk1KTtcbiAgfVxuICAxMDAlIHtcbiAgICBvcGFjaXR5OiAxO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKSBzY2FsZSgxKTtcbiAgfVxufVxuXG4ubG9naW4tbG9nbyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNpbmctbGcpO1xufVxuXG4ubG9nby1pY29uIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIHdpZHRoOiA4MHB4O1xuICBoZWlnaHQ6IDgwcHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHZhcigtLXByaW1hcnktY29sb3IpLCB2YXIoLS1hY2NlbnQtY29sb3IpKTtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBtYXJnaW4tYm90dG9tOiB2YXIoLS1zcGFjaW5nLW1kKTtcbiAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKHZhcigtLXByaW1hcnktY29sb3ItcmdiKSwgMC4zKTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuXG4ubG9nby1pY29uOjpiZWZvcmUge1xuICBjb250ZW50OiAnJztcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgdHJhbnNwYXJlbnQgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKSA1MCUsIHRyYW5zcGFyZW50IDEwMCUpO1xuICBhbmltYXRpb246IHNoaW1tZXIgMnMgaW5maW5pdGU7XG59XG5cbi5sb2dvLWljb24gaSB7XG4gIGZvbnQtc2l6ZTogMi41cmVtO1xuICBjb2xvcjogd2hpdGU7XG59XG5cbi5sb2dpbi1sb2dvIGgxIHtcbiAgZm9udC1zaXplOiAxLjc1cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCB2YXIoLS1wcmltYXJ5LWRhcmspLCB2YXIoLS1hY2NlbnQtY29sb3IpKTtcbiAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XG4gIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDtcbiAgbWFyZ2luOiAwO1xufVxuXG4ubG9naW4taGVhZGVyIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiB2YXIoLS1zcGFjaW5nLWxnKTtcbn1cblxuLmxvZ2luLWhlYWRlciBoMiB7XG4gIGNvbG9yOiB2YXIoLS10ZXh0LXByaW1hcnkpO1xuICBtYXJnaW4tYm90dG9tOiB2YXIoLS1zcGFjaW5nLXhzKTtcbiAgZm9udC1zaXplOiAxLjc1cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4ubG9naW4taGVhZGVyIHAge1xuICBjb2xvcjogdmFyKC0tdGV4dC1zZWNvbmRhcnkpO1xuICBmb250LXNpemU6IDFyZW07XG4gIG1hcmdpbjogMDtcbn1cblxuLmxvZ2luLWZvcm0ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IHZhcigtLXNwYWNpbmctbWQpO1xufVxuXG4uZm9ybS1ncm91cCB7XG4gIG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNpbmctbWQpO1xufVxuXG5sYWJlbCB7XG4gIGRpc3BsYXk6IGJsb2NrO1xuICBtYXJnaW4tYm90dG9tOiB2YXIoLS1zcGFjaW5nLXhzKTtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgY29sb3I6IHZhcigtLXRleHQtcHJpbWFyeSk7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xuICB0cmFuc2l0aW9uOiBjb2xvciB2YXIoLS10cmFuc2l0aW9uLWZhc3QpO1xufVxuXG4uaW5wdXQtd2l0aC1pY29uIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xufVxuXG4uaW5wdXQtd2l0aC1pY29uIGkge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGxlZnQ6IDE2cHg7XG4gIGNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgZm9udC1zaXplOiAxcmVtO1xuICB0cmFuc2l0aW9uOiBhbGwgdmFyKC0tdHJhbnNpdGlvbi1mYXN0KTtcbn1cblxuLmZvcm0tY29udHJvbCB7XG4gIHdpZHRoOiAxMDAlO1xuICBwYWRkaW5nOiAxNHB4IDE0cHggMTRweCA0NXB4O1xuICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1kaXZpZGVyLWNvbG9yKTtcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tYm9yZGVyLXJhZGl1cy1tZWRpdW0pO1xuICBmb250LXNpemU6IDFyZW07XG4gIHRyYW5zaXRpb246IGFsbCB2YXIoLS10cmFuc2l0aW9uLWZhc3QpO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1zdXJmYWNlLWNvbG9yKTtcbiAgY29sb3I6IHZhcigtLXRleHQtcHJpbWFyeSk7XG4gIGJveC1zaGFkb3c6IDAgMnB4IDVweCByZ2JhKDAsIDAsIDAsIDAuMDIpO1xufVxuXG4uZm9ybS1jb250cm9sOmZvY3VzIHtcbiAgb3V0bGluZTogbm9uZTtcbiAgYm9yZGVyLWNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgYm94LXNoYWRvdzogMCAwIDAgM3B4IHJnYmEodmFyKC0tcHJpbWFyeS1jb2xvci1yZ2IpLCAwLjE1KTtcbn1cblxuLmZvcm0tY29udHJvbDpmb2N1cyArIC5pbnB1dC1mb2N1cy1ib3JkZXIge1xuICB0cmFuc2Zvcm06IHNjYWxlWCgxKTtcbn1cblxuLmlucHV0LWZvY3VzLWJvcmRlciB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgYm90dG9tOiAwO1xuICBsZWZ0OiAwO1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAycHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgdmFyKC0tcHJpbWFyeS1jb2xvciksIHZhcigtLWFjY2VudC1jb2xvcikpO1xuICB0cmFuc2Zvcm06IHNjYWxlWCgwKTtcbiAgdHJhbnNmb3JtLW9yaWdpbjogbGVmdDtcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIHZhcigtLXRyYW5zaXRpb24tbWVkaXVtKTtcbn1cblxuLmZvcm0tY29udHJvbDo6cGxhY2Vob2xkZXIge1xuICBjb2xvcjogdmFyKC0tdGV4dC1oaW50KTtcbn1cblxuLmlucHV0LXdpdGgtaWNvbjpmb2N1cy13aXRoaW4gaSB7XG4gIGNvbG9yOiB2YXIoLS1hY2NlbnQtY29sb3IpO1xufVxuXG4ucGFzc3dvcmQtdG9nZ2xlIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICByaWdodDogMTJweDtcbiAgdG9wOiA1MCU7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTtcbiAgYmFja2dyb3VuZDogbm9uZTtcbiAgYm9yZGVyOiBub25lO1xuICBjb2xvcjogdmFyKC0tdGV4dC1zZWNvbmRhcnkpO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHBhZGRpbmc6IDhweDtcbiAgZm9udC1zaXplOiAxcmVtO1xuICB0cmFuc2l0aW9uOiBhbGwgdmFyKC0tdHJhbnNpdGlvbi1mYXN0KTtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICB6LWluZGV4OiAyO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgaGVpZ2h0OiAzNnB4O1xuICB3aWR0aDogMzZweDtcbn1cblxuLnBhc3N3b3JkLXRvZ2dsZTpob3ZlciB7XG4gIGNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSh2YXIoLS1wcmltYXJ5LWNvbG9yLXJnYiksIDAuMDUpO1xufVxuXG4uZXJyb3ItbWVzc2FnZSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogdmFyKC0tc3BhY2luZy1zbSk7XG4gIGNvbG9yOiB2YXIoLS1lcnJvci1jb2xvcik7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjQ0LCA2NywgNTQsIDAuMSk7XG4gIHBhZGRpbmc6IHZhcigtLXNwYWNpbmctc20pIHZhcigtLXNwYWNpbmctbWQpO1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzLW1lZGl1bSk7XG4gIG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNpbmctc20pO1xuICBhbmltYXRpb246IHNoYWtlIDAuNXMgY3ViaWMtYmV6aWVyKC4zNiwuMDcsLjE5LC45NykgYm90aDtcbn1cblxuQGtleWZyYW1lcyBzaGFrZSB7XG4gIDEwJSwgOTAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGUzZCgtMXB4LCAwLCAwKTsgfVxuICAyMCUsIDgwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlM2QoMnB4LCAwLCAwKTsgfVxuICAzMCUsIDUwJSwgNzAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGUzZCgtM3B4LCAwLCAwKTsgfVxuICA0MCUsIDYwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlM2QoM3B4LCAwLCAwKTsgfVxufVxuXG4uZXJyb3ItbWVzc2FnZSBpIHtcbiAgZm9udC1zaXplOiAxLjFyZW07XG59XG5cbi5sb2dpbi1idXR0b24ge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiA1MHB4O1xuICBmb250LXNpemU6IDFyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIG1hcmdpbi10b3A6IHZhcigtLXNwYWNpbmctbWQpO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHZhcigtLXByaW1hcnktY29sb3IpLCB2YXIoLS1hY2NlbnQtY29sb3IpKTtcbiAgY29sb3I6IHdoaXRlO1xuICBib3JkZXI6IG5vbmU7XG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLWJvcmRlci1yYWRpdXMtbWVkaXVtKTtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgdmFyKC0tdHJhbnNpdGlvbi1mYXN0KTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKHZhcigtLXByaW1hcnktY29sb3ItcmdiKSwgMC4zKTtcbn1cblxuLmxvZ2luLWJ1dHRvbjo6YmVmb3JlIHtcbiAgY29udGVudDogJyc7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAtMTAwJTtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpLCB0cmFuc3BhcmVudCk7XG4gIHRyYW5zaXRpb246IGFsbCAwLjZzO1xufVxuXG4ubG9naW4tYnV0dG9uOmhvdmVyOm5vdCg6ZGlzYWJsZWQpOjpiZWZvcmUge1xuICBsZWZ0OiAxMDAlO1xufVxuXG4ubG9naW4tYnV0dG9uOmhvdmVyOm5vdCg6ZGlzYWJsZWQpIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICBib3gtc2hhZG93OiAwIDZweCAyMHB4IHJnYmEodmFyKC0tcHJpbWFyeS1jb2xvci1yZ2IpLCAwLjQpO1xufVxuXG4ubG9naW4tYnV0dG9uOmFjdGl2ZTpub3QoOmRpc2FibGVkKSB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgxcHgpO1xuICBib3gtc2hhZG93OiAwIDJweCAxMHB4IHJnYmEodmFyKC0tcHJpbWFyeS1jb2xvci1yZ2IpLCAwLjMpO1xufVxuXG4ubG9naW4tYnV0dG9uOmRpc2FibGVkIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2NjYywgI2RkZCk7XG4gIGN1cnNvcjogbm90LWFsbG93ZWQ7XG4gIGJveC1zaGFkb3c6IG5vbmU7XG59XG5cbi5zcGlubmVyLWNvbnRhaW5lciB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgYm90dG9tOiAwO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbn1cblxuLnNwaW5uZXIge1xuICB3aWR0aDogMjRweDtcbiAgaGVpZ2h0OiAyNHB4O1xuICBib3JkZXI6IDNweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgYm9yZGVyLXRvcC1jb2xvcjogd2hpdGU7XG4gIGFuaW1hdGlvbjogc3BpbiAwLjhzIGxpbmVhciBpbmZpbml0ZTtcbn1cblxuQGtleWZyYW1lcyBzcGluIHtcbiAgdG8geyB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOyB9XG59XG5cbi5sb2dpbi1mb290ZXIge1xuICBtYXJnaW4tdG9wOiB2YXIoLS1zcGFjaW5nLXhsKTtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuXG4uZm9vdGVyLWRlY29yYXRpb24ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogdmFyKC0tc3BhY2luZy1tZCk7XG59XG5cbi5kZWNvcmF0aW9uLWxpbmUge1xuICBoZWlnaHQ6IDFweDtcbiAgd2lkdGg6IDYwcHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgdHJhbnNwYXJlbnQsIHZhcigtLWRpdmlkZXItY29sb3IpKTtcbn1cblxuLmRlY29yYXRpb24tbGluZTpsYXN0LWNoaWxkIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIGxlZnQsIHRyYW5zcGFyZW50LCB2YXIoLS1kaXZpZGVyLWNvbG9yKSk7XG59XG5cbi5mb290ZXItZGVjb3JhdGlvbiBpIHtcbiAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICBmb250LXNpemU6IDEuMnJlbTtcbiAgbWFyZ2luOiAwIHZhcigtLXNwYWNpbmctc20pO1xufVxuXG4ubG9naW4tZm9vdGVyIHAge1xuICBjb2xvcjogdmFyKC0tdGV4dC1oaW50KTtcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xuICBtYXJnaW46IDA7XG59XG5cbi8qIFJlc3BvbnNpdmUgc3R5bGVzICovXG5AbWVkaWEgKG1pbi13aWR0aDogNzY4cHgpIHtcbiAgLmxvZ2luLWlsbHVzdHJhdGlvbiB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gIH1cblxuICAubG9naW4tY29udGFpbmVyIHtcbiAgICBtYXJnaW4tbGVmdDogLTEwJTtcbiAgfVxufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNzY3cHgpIHtcbiAgLmxvZ2luLWNhcmQge1xuICAgIG1heC13aWR0aDogOTAlO1xuICB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA1MDBweCkge1xuICAubG9naW4tY2FyZCB7XG4gICAgcGFkZGluZzogdmFyKC0tc3BhY2luZy1sZyk7XG4gIH1cblxuICAubG9nby1pY29uIHtcbiAgICB3aWR0aDogNzBweDtcbiAgICBoZWlnaHQ6IDcwcHg7XG4gIH1cblxuICAubG9nby1pY29uIGkge1xuICAgIGZvbnQtc2l6ZTogMnJlbTtcbiAgfVxuXG4gIC5sb2dpbi1sb2dvIGgxIHtcbiAgICBmb250LXNpemU6IDEuNXJlbTtcbiAgfVxuXG4gIC5sb2dpbi1oZWFkZXIgaDIge1xuICAgIGZvbnQtc2l6ZTogMS41cmVtO1xuICB9XG5cbiAgLmZvcm0tY29udHJvbCB7XG4gICAgcGFkZGluZzogMTJweCAxMnB4IDEycHggNDBweDtcbiAgfVxufVxuXG4vKiBBbmltYXRpb24gcG91ciBsZSBib2R5ICovXG46aG9zdC1jb250ZXh0KGJvZHkubG9naW4tcGFnZS1hY3RpdmUpIHtcbiAgYW5pbWF0aW9uOiBmYWRlSW4gMC41cyBlYXNlLWluLW91dDtcbn1cblxuQGtleWZyYW1lcyBzaGltbWVyIHtcbiAgMCUge1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtMTAwJSk7XG4gIH1cbiAgMTAwJSB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDEwMCUpO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      data: {\n        animation: [trigger('fadeInOut', [transition(':enter', [style({\n          opacity: 0\n        }), animate('0.5s ease-in-out', style({\n          opacity: 1\n        }))]), transition(':leave', [animate('0.5s ease-in-out', style({\n          opacity: 0\n        }))])])]\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["trigger", "transition", "style", "animate", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "LoginComponent", "constructor", "authService", "router", "snackBar", "credentials", "Agency", "User", "Password", "loading", "hidePassword", "ngOnInit", "logout", "document", "body", "classList", "add", "ngOnDestroy", "remove", "onSubmit", "login", "subscribe", "next", "response", "header", "success", "open", "duration", "panelClass", "verticalPosition", "navigate", "err", "console", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_22_listener", "LoginComponent_Template_input_ngModelChange_29_listener", "$event", "LoginComponent_Template_input_ngModelChange_36_listener", "LoginComponent_Template_input_ngModelChange_43_listener", "LoginComponent_Template_button_click_44_listener", "ɵɵtemplate", "LoginComponent_div_47_Template", "LoginComponent_div_49_Template", "LoginComponent_span_50_Template", "ɵɵproperty", "_r0", "form", "valid", "opacity"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { AuthRequest } from '../../models/auth-request.model';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { trigger, transition, style, animate } from '@angular/animations';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css'],\n  animations: [\n    trigger('fadeInOut', [\n      transition(':enter', [\n        style({ opacity: 0 }),\n        animate('0.5s ease-in-out', style({ opacity: 1 }))\n      ]),\n      transition(':leave', [\n        animate('0.5s ease-in-out', style({ opacity: 0 }))\n      ])\n    ])\n  ]\n})\nexport class LoginComponent implements OnInit, OnDestroy {\n  credentials: AuthRequest = {\n    Agency: '',\n    User: '',\n    Password: ''\n  };\n\n  loading = false;\n  error = '';\n  hidePassword = true;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) { }\n\n  ngOnInit(): void {\n    // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n    this.authService.logout();\n\n    // Animation d'entrée\n    document.body.classList.add('login-page-active');\n  }\n\n  ngOnDestroy(): void {\n    // Nettoyage de la classe lors de la destruction du composant\n    document.body.classList.remove('login-page-active');\n  }\n\n  onSubmit(): void {\n    if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n      this.error = 'Veuillez remplir tous les champs requis.';\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    this.authService.login(this.credentials)\n      .subscribe({\n        next: (response) => {\n          this.loading = false;\n          if (response.header.success) {\n            this.snackBar.open('Connexion réussie! Bienvenue.', 'Fermer', {\n              duration: 3000,\n              panelClass: 'success-snackbar',\n              verticalPosition: 'top'\n            });\n            this.router.navigate(['/accueil']);\n          } else {\n            this.error = 'Échec de l\\'authentification. Veuillez vérifier vos identifiants.';\n          }\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = 'Une erreur s\\'est produite lors de la connexion. Veuillez réessayer.';\n          console.error('Login error:', err);\n        }\n      });\n  }\n}\n", "<div class=\"login-page\">\n  <div class=\"login-background\">\n    <div class=\"login-shapes\">\n      <div class=\"shape shape-1\"></div>\n      <div class=\"shape shape-2\"></div>\n      <div class=\"shape shape-3\"></div>\n      <div class=\"shape shape-4\"></div>\n      <div class=\"shape shape-5\"></div>\n    </div>\n    <div class=\"login-illustration\">\n      <img src=\"assets/images/login-illustration.svg\" alt=\"Travel Illustration\">\n    </div>\n  </div>\n\n  <div class=\"login-container\">\n    <div class=\"login-card animate-slide-up\">\n      <div class=\"login-logo\">\n        <div class=\"logo-icon\">\n          <i class=\"fas fa-plane-departure\"></i>\n        </div>\n        <h1>E-Tourism</h1>\n      </div>\n\n      <div class=\"login-header\">\n        <h2>Bienvenue</h2>\n        <p>Connectez-vous pour accéder à votre compte</p>\n      </div>\n\n      <form (ngSubmit)=\"onSubmit()\" #loginForm=\"ngForm\" class=\"login-form\">\n        <div class=\"form-group\">\n          <label for=\"agency\">Agence</label>\n          <div class=\"input-with-icon\">\n            <i class=\"fas fa-building\"></i>\n            <input\n              type=\"text\"\n              id=\"agency\"\n              name=\"agency\"\n              [(ngModel)]=\"credentials.Agency\"\n              required\n              class=\"form-control\"\n              placeholder=\"Entrez le nom de votre agence\">\n            <div class=\"input-focus-border\"></div>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"username\">Nom d'utilisateur</label>\n          <div class=\"input-with-icon\">\n            <i class=\"fas fa-user\"></i>\n            <input\n              type=\"text\"\n              id=\"username\"\n              name=\"username\"\n              [(ngModel)]=\"credentials.User\"\n              required\n              class=\"form-control\"\n              placeholder=\"Entrez votre nom d'utilisateur\">\n            <div class=\"input-focus-border\"></div>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"password\">Mot de passe</label>\n          <div class=\"input-with-icon\">\n            <i class=\"fas fa-lock\"></i>\n            <input\n              [type]=\"hidePassword ? 'password' : 'text'\"\n              id=\"password\"\n              name=\"password\"\n              [(ngModel)]=\"credentials.Password\"\n              required\n              class=\"form-control\"\n              placeholder=\"Entrez votre mot de passe\">\n            <button\n              type=\"button\"\n              class=\"password-toggle\"\n              (click)=\"hidePassword = !hidePassword\">\n              <i class=\"fas\" [ngClass]=\"hidePassword ? 'fa-eye' : 'fa-eye-slash'\"></i>\n            </button>\n            <div class=\"input-focus-border\"></div>\n          </div>\n        </div>\n\n        <div *ngIf=\"error\" class=\"error-message\">\n          <i class=\"fas fa-exclamation-circle\"></i>\n          <span>{{ error }}</span>\n        </div>\n\n        <button\n          type=\"submit\"\n          [disabled]=\"!loginForm.form.valid || loading\"\n          class=\"login-button\">\n          <div *ngIf=\"loading\" class=\"spinner-container\">\n            <div class=\"spinner\"></div>\n          </div>\n          <span *ngIf=\"!loading\">Se connecter</span>\n        </button>\n      </form>\n\n      <div class=\"login-footer\">\n        <div class=\"footer-decoration\">\n          <div class=\"decoration-line\"></div>\n          <i class=\"fas fa-globe-americas\"></i>\n          <div class=\"decoration-line\"></div>\n        </div>\n        <p>© 2023 E-Tourism. Tous droits réservés.</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAKA,SAASA,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;;;;;;;;;IC8EjEC,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAAE,SAAA,YAAyC;IACzCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAlBJ,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAOjBR,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;ADxEpD,OAAM,MAAOK,cAAc;EAWzBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAblB,KAAAC,WAAW,GAAgB;MACzBC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE;KACX;IAED,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAV,KAAK,GAAG,EAAE;IACV,KAAAW,YAAY,GAAG,IAAI;EAMf;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACT,WAAW,CAACU,MAAM,EAAE;IAEzB;IACAC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAClD;EAEAC,WAAWA,CAAA;IACT;IACAJ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACG,MAAM,CAAC,mBAAmB,CAAC;EACrD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACd,WAAW,CAACC,MAAM,IAAI,CAAC,IAAI,CAACD,WAAW,CAACE,IAAI,IAAI,CAAC,IAAI,CAACF,WAAW,CAACG,QAAQ,EAAE;MACpF,IAAI,CAACT,KAAK,GAAG,0CAA0C;MACvD;;IAGF,IAAI,CAACU,OAAO,GAAG,IAAI;IACnB,IAAI,CAACV,KAAK,GAAG,EAAE;IAEf,IAAI,CAACG,WAAW,CAACkB,KAAK,CAAC,IAAI,CAACf,WAAW,CAAC,CACrCgB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACd,OAAO,GAAG,KAAK;QACpB,IAAIc,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;UAC3B,IAAI,CAACrB,QAAQ,CAACsB,IAAI,CAAC,+BAA+B,EAAE,QAAQ,EAAE;YAC5DC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,kBAAkB;YAC9BC,gBAAgB,EAAE;WACnB,CAAC;UACF,IAAI,CAAC1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;SACnC,MAAM;UACL,IAAI,CAAC/B,KAAK,GAAG,mEAAmE;;MAEpF,CAAC;MACDA,KAAK,EAAGgC,GAAG,IAAI;QACb,IAAI,CAACtB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACV,KAAK,GAAG,sEAAsE;QACnFiC,OAAO,CAACjC,KAAK,CAAC,cAAc,EAAEgC,GAAG,CAAC;MACpC;KACD,CAAC;EACN;;;uBA5DW/B,cAAc,EAAAT,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAA0C,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAA0C,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAdvC,cAAc;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvB3BvD,EAAA,CAAAC,cAAA,aAAwB;UAGlBD,EAAA,CAAAE,SAAA,aAAiC;UAKnCF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,aAAgC;UAC9BD,EAAA,CAAAE,SAAA,aAA0E;UAC5EF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAC,cAAA,eAA6B;UAIrBD,EAAA,CAAAE,SAAA,aAAsC;UACxCF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGpBJ,EAAA,CAAAC,cAAA,eAA0B;UACpBD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,4DAA0C;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAGnDJ,EAAA,CAAAC,cAAA,oBAAqE;UAA/DD,EAAA,CAAAyD,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAC3B5B,EAAA,CAAAC,cAAA,eAAwB;UACFD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAClCJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,SAAA,aAA+B;UAC/BF,EAAA,CAAAC,cAAA,iBAO8C;UAH5CD,EAAA,CAAAyD,UAAA,2BAAAE,wDAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAC,MAAA,GAAA6C,MAAA;UAAA,EAAgC;UAJlC5D,EAAA,CAAAI,YAAA,EAO8C;UAC9CJ,EAAA,CAAAE,SAAA,eAAsC;UACxCF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAG,MAAA,yBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC/CJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,SAAA,aAA2B;UAC3BF,EAAA,CAAAC,cAAA,iBAO+C;UAH7CD,EAAA,CAAAyD,UAAA,2BAAAI,wDAAAD,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAE,IAAA,GAAA4C,MAAA;UAAA,EAA8B;UAJhC5D,EAAA,CAAAI,YAAA,EAO+C;UAC/CJ,EAAA,CAAAE,SAAA,eAAsC;UACxCF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC1CJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,SAAA,aAA2B;UAC3BF,EAAA,CAAAC,cAAA,iBAO0C;UAHxCD,EAAA,CAAAyD,UAAA,2BAAAK,wDAAAF,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAG,QAAA,GAAA2C,MAAA;UAAA,EAAkC;UAJpC5D,EAAA,CAAAI,YAAA,EAO0C;UAC1CJ,EAAA,CAAAC,cAAA,kBAGyC;UAAvCD,EAAA,CAAAyD,UAAA,mBAAAM,iDAAA;YAAA,OAAAP,GAAA,CAAArC,YAAA,IAAAqC,GAAA,CAAArC,YAAA;UAAA,EAAsC;UACtCnB,EAAA,CAAAE,SAAA,aAAwE;UAC1EF,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,SAAA,eAAsC;UACxCF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAgE,UAAA,KAAAC,8BAAA,kBAGM;UAENjE,EAAA,CAAAC,cAAA,kBAGuB;UACrBD,EAAA,CAAAgE,UAAA,KAAAE,8BAAA,kBAEM;UACNlE,EAAA,CAAAgE,UAAA,KAAAG,+BAAA,mBAA0C;UAC5CnE,EAAA,CAAAI,YAAA,EAAS;UAGXJ,EAAA,CAAAC,cAAA,eAA0B;UAEtBD,EAAA,CAAAE,SAAA,eAAmC;UAGrCF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,8DAAuC;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;;UApExCJ,EAAA,CAAAK,SAAA,IAAgC;UAAhCL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAA1C,WAAA,CAAAC,MAAA,CAAgC;UAgBhCf,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAA1C,WAAA,CAAAE,IAAA,CAA8B;UAa9BhB,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAArC,YAAA,uBAA2C,YAAAqC,GAAA,CAAA1C,WAAA,CAAAG,QAAA;UAW5BjB,EAAA,CAAAK,SAAA,GAAoD;UAApDL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAArC,YAAA,6BAAoD;UAMnEnB,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAAhD,KAAA,CAAW;UAOfR,EAAA,CAAAK,SAAA,GAA6C;UAA7CL,EAAA,CAAAoE,UAAA,cAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,IAAAf,GAAA,CAAAtC,OAAA,CAA6C;UAEvClB,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAAtC,OAAA,CAAa;UAGZlB,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAoE,UAAA,UAAAZ,GAAA,CAAAtC,OAAA,CAAc;;;;;;mBDpFjB,CACVtB,OAAO,CAAC,WAAW,EAAE,CACnBC,UAAU,CAAC,QAAQ,EAAE,CACnBC,KAAK,CAAC;UAAE0E,OAAO,EAAE;QAAC,CAAE,CAAC,EACrBzE,OAAO,CAAC,kBAAkB,EAAED,KAAK,CAAC;UAAE0E,OAAO,EAAE;QAAC,CAAE,CAAC,CAAC,CACnD,CAAC,EACF3E,UAAU,CAAC,QAAQ,EAAE,CACnBE,OAAO,CAAC,kBAAkB,EAAED,KAAK,CAAC;UAAE0E,OAAO,EAAE;QAAC,CAAE,CAAC,CAAC,CACnD,CAAC,CACH,CAAC;MACH;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}