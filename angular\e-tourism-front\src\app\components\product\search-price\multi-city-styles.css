/* Styles pour les vols multi-destinations */

/* Badge pour indiquer un vol multi-destinations */
.multi-city-badge {
  position: absolute;
  top: -10px;
  right: 20px;
  background-color: #9c27b0;
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.multi-city-badge i {
  margin-right: 5px;
}

/* Styles pour la carte de vol multi-destinations */
.flight-card.multi-city {
  position: relative;
  border-left: 4px solid #9c27b0;
  padding-top: 20px;
}

/* Sections des segments */
.multi-city-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.multi-city-segment {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  background-color: #f9f9f9;
  border-left: 3px solid #9c27b0;
}

.multi-city-segment-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px dashed #ccc;
}

.multi-city-segment-header i {
  margin-right: 8px;
  color: #9c27b0;
}

.multi-city-segment-label {
  font-weight: bold;
  color: #333;
}

/* Styles pour le formulaire multi-city */
.multi-city-segments {
  width: 100%;
  margin-top: 15px;
}

.segments-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.segment-row {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-left: 3px solid #2989d8;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.segment-header h4 {
  margin: 0;
  color: #2989d8;
  font-size: 16px;
  font-weight: 600;
}

.remove-segment-btn {
  background: none;
  border: none;
  color: #ff5252;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.remove-segment-btn:hover {
  background-color: #ffeeee;
}

.segment-fields {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.add-segment-container {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.add-segment-btn {
  background-color: #2989d8;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s;
}

.add-segment-btn:hover {
  background-color: #1e5799;
}

.add-segment-btn i {
  font-size: 12px;
}

/* Responsive design */
@media (max-width: 768px) {
  .multi-city-details {
    gap: 15px;
  }

  .multi-city-segment {
    padding: 10px;
  }

  .multi-city-badge {
    font-size: 10px;
    padding: 3px 8px;
  }

  .segment-fields {
    grid-template-columns: 1fr;
  }
}
