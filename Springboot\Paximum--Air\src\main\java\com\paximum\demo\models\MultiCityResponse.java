package com.paximum.demo.models;

import java.util.List;

public class MultiCityResponse {
 

    public MultiCityResponse() {}
    private Header header;
    private Body body;

    public Header getHeader() {
        return header;
    }

    public void setHeader(Header header) {
        this.header = header;
    }

    public Body getBody() {
        return body;
    }

    public void setBody(Body body) {
        this.body = body;
    }

    public static class Header {
        private String requestId;
        private boolean success;
        private String responseTime;
        private List<Message> messages;

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getResponseTime() {
            return responseTime;
        }

        public void setResponseTime(String responseTime) {
            this.responseTime = responseTime;
        }

        public List<Message> getMessages() {
            return messages;
        }

        public void setMessages(List<Message> messages) {
            this.messages = messages;
        }
    }

    public static class Message {
        private int id;
        private String code;
        private int messageType;
        private String message;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public int getMessageType() {
            return messageType;
        }

        public void setMessageType(int messageType) {
            this.messageType = messageType;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    public static class Body {
        private String searchId;
        private String expiresOn;
        private List<Flight> flights;
        private Details details;

        public String getSearchId() {
            return searchId;
        }

        public void setSearchId(String searchId) {
            this.searchId = searchId;
        }

        public String getExpiresOn() {
            return expiresOn;
        }

        public void setExpiresOn(String expiresOn) {
            this.expiresOn = expiresOn;
        }

        public List<Flight> getFlights() {
            return flights;
        }

        public void setFlights(List<Flight> flights) {
            this.flights = flights;
        }

        public Details getDetails() {
            return details;
        }

        public void setDetails(Details details) {
            this.details = details;
        }
    }

    public static class Flight {
        private int provider;
        private String id;
        private List<FlightItem> items;
        private List<Offer> offers;
        private List<String> groupKeys;

        public int getProvider() {
            return provider;
        }

        public void setProvider(int provider) {
            this.provider = provider;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public List<FlightItem> getItems() {
            return items;
        }

        public void setItems(List<FlightItem> items) {
            this.items = items;
        }

        public List<Offer> getOffers() {
            return offers;
        }

        public void setOffers(List<Offer> offers) {
            this.offers = offers;
        }

        public List<String> getGroupKeys() {
            return groupKeys;
        }

        public void setGroupKeys(List<String> groupKeys) {
            this.groupKeys = groupKeys;
        }
    }

    public static class FlightItem {
        private int segmentNumber;
        private String flightNo;
        private String pnlName;
        private String flightDate;
        private Airline airline;
        private Airline marketingAirline;
        private int duration;
        private int dayChange;
        private Location departure;
        private Location arrival;
        private FlightClass flightClass;
        private int route;
        private List<Segment> segments;
        private int stopCount;
        private Provider flightProvider;
        private List<BaggageInformation> baggageInformations;
        private List<Passenger> passengers;
        private int flightType;

        public int getSegmentNumber() {
            return segmentNumber;
        }

        public void setSegmentNumber(int segmentNumber) {
            this.segmentNumber = segmentNumber;
        }

        public String getFlightNo() {
            return flightNo;
        }

        public void setFlightNo(String flightNo) {
            this.flightNo = flightNo;
        }

        public String getPnlName() {
            return pnlName;
        }

        public void setPnlName(String pnlName) {
            this.pnlName = pnlName;
        }

        public String getFlightDate() {
            return flightDate;
        }

        public void setFlightDate(String flightDate) {
            this.flightDate = flightDate;
        }

        public Airline getAirline() {
            return airline;
        }

        public void setAirline(Airline airline) {
            this.airline = airline;
        }

        public Airline getMarketingAirline() {
            return marketingAirline;
        }

        public void setMarketingAirline(Airline marketingAirline) {
            this.marketingAirline = marketingAirline;
        }

        public int getDuration() {
            return duration;
        }

        public void setDuration(int duration) {
            this.duration = duration;
        }

        public int getDayChange() {
            return dayChange;
        }

        public void setDayChange(int dayChange) {
            this.dayChange = dayChange;
        }

        public Location getDeparture() {
            return departure;
        }

        public void setDeparture(Location departure) {
            this.departure = departure;
        }

        public Location getArrival() {
            return arrival;
        }

        public void setArrival(Location arrival) {
            this.arrival = arrival;
        }

        public FlightClass getFlightClass() {
            return flightClass;
        }

        public void setFlightClass(FlightClass flightClass) {
            this.flightClass = flightClass;
        }

        public int getRoute() {
            return route;
        }

        public void setRoute(int route) {
            this.route = route;
        }

        public List<Segment> getSegments() {
            return segments;
        }

        public void setSegments(List<Segment> segments) {
            this.segments = segments;
        }

        public int getStopCount() {
            return stopCount;
        }

        public void setStopCount(int stopCount) {
            this.stopCount = stopCount;
        }

        public Provider getFlightProvider() {
            return flightProvider;
        }

        public void setFlightProvider(Provider flightProvider) {
            this.flightProvider = flightProvider;
        }

        public List<BaggageInformation> getBaggageInformations() {
            return baggageInformations;
        }

        public void setBaggageInformations(List<BaggageInformation> baggageInformations) {
            this.baggageInformations = baggageInformations;
        }

        public List<Passenger> getPassengers() {
            return passengers;
        }

        public void setPassengers(List<Passenger> passengers) {
            this.passengers = passengers;
        }

        public int getFlightType() {
            return flightType;
        }

        public void setFlightType(int flightType) {
            this.flightType = flightType;
        }
    }

    public static class Airline {
        private String internationalCode;
        private String thumbnail;
        private String thumbnailFull;
        private String logo;
        private String logoFull;
        private String id;
        private String name;

        public String getInternationalCode() {
            return internationalCode;
        }

        public void setInternationalCode(String internationalCode) {
            this.internationalCode = internationalCode;
        }

        public String getThumbnail() {
            return thumbnail;
        }

        public void setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
        }

        public String getThumbnailFull() {
            return thumbnailFull;
        }

        public void setThumbnailFull(String thumbnailFull) {
            this.thumbnailFull = thumbnailFull;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }

        public String getLogoFull() {
            return logoFull;
        }

        public void setLogoFull(String logoFull) {
            this.logoFull = logoFull;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class Location {
        private Country country;
        private City city;
        private Airport airport;
        private String date;
        private GeoLocation geoLocation;

        public Country getCountry() {
            return country;
        }

        public void setCountry(Country country) {
            this.country = country;
        }

        public City getCity() {
            return city;
        }

        public void setCity(City city) {
            this.city = city;
        }

        public Airport getAirport() {
            return airport;
        }

        public void setAirport(Airport airport) {
            this.airport = airport;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public GeoLocation getGeoLocation() {
            return geoLocation;
        }

        public void setGeoLocation(GeoLocation geoLocation) {
            this.geoLocation = geoLocation;
        }
    }

    public static class Country {
        private String name;
        private int provider;
        private boolean isTopRegion;
        private String id;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getProvider() {
            return provider;
        }

        public void setProvider(int provider) {
            this.provider = provider;
        }

        public boolean isTopRegion() {
            return isTopRegion;
        }

        public void setTopRegion(boolean isTopRegion) {
            this.isTopRegion = isTopRegion;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }

    public static class City {
        private String name;
        private int provider;
        private boolean isTopRegion;
        private String id;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getProvider() {
            return provider;
        }

        public void setProvider(int provider) {
            this.provider = provider;
        }

        public boolean isTopRegion() {
            return isTopRegion;
        }

        public void setTopRegion(boolean isTopRegion) {
            this.isTopRegion = isTopRegion;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }

    public static class Airport {
        private GeoLocation geolocation;
        private String name;
        private String id;
        private String code;

        public GeoLocation getGeolocation() {
            return geolocation;
        }

        public void setGeolocation(GeoLocation geolocation) {
            this.geolocation = geolocation;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }

    public static class GeoLocation {
        private double longitude;
        private double latitude;

        public double getLongitude() {
            return longitude;
        }

        public void setLongitude(double longitude) {
            this.longitude = longitude;
        }

        public double getLatitude() {
            return latitude;
        }

        public void setLatitude(double latitude) {
            this.latitude = latitude;
        }
    }

    public static class FlightClass {
        private int type;
        private String name;
        private String id;
        private String code;

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }

    public static class Segment {
        private String id;
        private String flightNo;
        private String pnlName;
        private FlightClass flightClass;
        private Airline airline;
        private Airline marketingAirline;
        private Location departure;
        private Location arrival;
        private int duration;
        private List<BaggageInformation> baggageInformations;
        private List<Service> services;
        private String aircraft;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getFlightNo() {
            return flightNo;
        }

        public void setFlightNo(String flightNo) {
            this.flightNo = flightNo;
        }

        public String getPnlName() {
            return pnlName;
        }

        public void setPnlName(String pnlName) {
            this.pnlName = pnlName;
        }

        public FlightClass getFlightClass() {
            return flightClass;
        }

        public void setFlightClass(FlightClass flightClass) {
            this.flightClass = flightClass;
        }

        public Airline getAirline() {
            return airline;
        }

        public void setAirline(Airline airline) {
            this.airline = airline;
        }

        public Airline getMarketingAirline() {
            return marketingAirline;
        }

        public void setMarketingAirline(Airline marketingAirline) {
            this.marketingAirline = marketingAirline;
        }

        public Location getDeparture() {
            return departure;
        }

        public void setDeparture(Location departure) {
            this.departure = departure;
        }

        public Location getArrival() {
            return arrival;
        }

        public void setArrival(Location arrival) {
            this.arrival = arrival;
        }

        public int getDuration() {
            return duration;
        }

        public void setDuration(int duration) {
            this.duration = duration;
        }

        public List<BaggageInformation> getBaggageInformations() {
            return baggageInformations;
        }

        public void setBaggageInformations(List<BaggageInformation> baggageInformations) {
            this.baggageInformations = baggageInformations;
        }

        public List<Service> getServices() {
            return services;
        }

        public void setServices(List<Service> services) {
            this.services = services;
        }

        public String getAircraft() {
            return aircraft;
        }

        public void setAircraft(String aircraft) {
            this.aircraft = aircraft;
        }
    }

    public static class BaggageInformation {
        private String segmentId;
        private int weight;
        private int piece;
        private int baggageType;
        private int unitType;
        private int passengerType;
        private String description;

        public String getSegmentId() {
            return segmentId;
        }

        public void setSegmentId(String segmentId) {
            this.segmentId = segmentId;
        }

        public int getWeight() {
            return weight;
        }

        public void setWeight(int weight) {
            this.weight = weight;
        }

        public int getPiece() {
            return piece;
        }

        public void setPiece(int piece) {
            this.piece = piece;
        }

        public int getBaggageType() {
            return baggageType;
        }

        public void setBaggageType(int baggageType) {
            this.baggageType = baggageType;
        }

        public int getUnitType() {
            return unitType;
        }

        public void setUnitType(int unitType) {
            this.unitType = unitType;
        }

        public int getPassengerType() {
            return passengerType;
        }

        public void setPassengerType(int passengerType) {
            this.passengerType = passengerType;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }

    public static class Service {
        private List<String> segments;
        private List<Explanation> explanations;
        private String thumbnail;
        private String thumbnailFull;
        private String id;
        private String name;

        public List<String> getSegments() {
            return segments;
        }

        public void setSegments(List<String> segments) {
            this.segments = segments;
        }

        public List<Explanation> getExplanations() {
            return explanations;
        }

        public void setExplanations(List<Explanation> explanations) {
            this.explanations = explanations;
        }

        public String getThumbnail() {
            return thumbnail;
        }

        public void setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
        }

        public String getThumbnailFull() {
            return thumbnailFull;
        }

        public void setThumbnailFull(String thumbnailFull) {
            this.thumbnailFull = thumbnailFull;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class Explanation {
        private String text;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }
    }

    public static class Passenger {
        private int type;
        private int count;

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }
    }

    public static class Offer {
        private int segmentNumber;
        private Price singleAdultPrice;
        private PriceBreakDown priceBreakDown;
        private Price serviceFee;
        private SeatInfo seatInfo;
        private List<FlightClassInformation> flightClassInformations;
        private List<BaggageInformation> baggageInformations;
        private List<Service> services;
        private ReservableInfo reservableInfo;
        private List<String> groupKeys;
        private List<OfferId> offerIds;
        private boolean isPackageOffer;
        private boolean hasBrand;
        private int route;
        private Provider flightProvider;
        private FlightBrandInfo flightBrandInfo;
        private String expiresOn;
        private Price price;
        private int provider;

        public int getSegmentNumber() {
            return segmentNumber;
        }

        public void setSegmentNumber(int segmentNumber) {
            this.segmentNumber = segmentNumber;
        }

        public Price getSingleAdultPrice() {
            return singleAdultPrice;
        }

        public void setSingleAdultPrice(Price singleAdultPrice) {
            this.singleAdultPrice = singleAdultPrice;
        }

        public PriceBreakDown getPriceBreakDown() {
            return priceBreakDown;
        }

        public void setPriceBreakDown(PriceBreakDown priceBreakDown) {
            this.priceBreakDown = priceBreakDown;
        }

        public Price getServiceFee() {
            return serviceFee;
        }

        public void setServiceFee(Price serviceFee) {
            this.serviceFee = serviceFee;
        }

        public SeatInfo getSeatInfo() {
            return seatInfo;
        }

        public void setSeatInfo(SeatInfo seatInfo) {
            this.seatInfo = seatInfo;
        }

        public List<FlightClassInformation> getFlightClassInformations() {
            return flightClassInformations;
        }

        public void setFlightClassInformations(List<FlightClassInformation> flightClassInformations) {
            this.flightClassInformations = flightClassInformations;
        }

        public List<BaggageInformation> getBaggageInformations() {
            return baggageInformations;
        }

        public void setBaggageInformations(List<BaggageInformation> baggageInformations) {
            this.baggageInformations = baggageInformations;
        }

        public List<Service> getServices() {
            return services;
        }

        public void setServices(List<Service> services) {
            this.services = services;
        }

        public ReservableInfo getReservableInfo() {
            return reservableInfo;
        }

        public void setReservableInfo(ReservableInfo reservableInfo) {
            this.reservableInfo = reservableInfo;
        }

        public List<String> getGroupKeys() {
            return groupKeys;
        }

        public void setGroupKeys(List<String> groupKeys) {
            this.groupKeys = groupKeys;
        }

        public List<OfferId> getOfferIds() {
            return offerIds;
        }

        public void setOfferIds(List<OfferId> offerIds) {
            this.offerIds = offerIds;
        }

        public boolean isPackageOffer() {
            return isPackageOffer;
        }

        public void setPackageOffer(boolean isPackageOffer) {
            this.isPackageOffer = isPackageOffer;
        }

        public boolean isHasBrand() {
            return hasBrand;
        }

        public void setHasBrand(boolean hasBrand) {
            this.hasBrand = hasBrand;
        }

        public int getRoute() {
            return route;
        }

        public void setRoute(int route) {
            this.route = route;
        }

        public Provider getFlightProvider() {
            return flightProvider;
        }

        public void setFlightProvider(Provider flightProvider) {
            this.flightProvider = flightProvider;
        }

        public FlightBrandInfo getFlightBrandInfo() {
            return flightBrandInfo;
        }

        public void setFlightBrandInfo(FlightBrandInfo flightBrandInfo) {
            this.flightBrandInfo = flightBrandInfo;
        }

        public String getExpiresOn() {
            return expiresOn;
        }

        public void setExpiresOn(String expiresOn) {
            this.expiresOn = expiresOn;
        }

        public Price getPrice() {
            return price;
        }

        public void setPrice(Price price) {
            this.price = price;
        }

        public int getProvider() {
            return provider;
        }

        public void setProvider(int provider) {
            this.provider = provider;
        }
    }

    public static class Price {
        private double amount;
        private String currency;

        public double getAmount() {
            return amount;
        }

        public void setAmount(double amount) {
            this.amount = amount;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }

    public static class PriceBreakDown {
        private List<PriceBreakDownItem> items;

        public List<PriceBreakDownItem> getItems() {
            return items;
        }

        public void setItems(List<PriceBreakDownItem> items) {
            this.items = items;
        }
    }

    public static class PriceBreakDownItem {
        private int passengerType;
        private int passengerCount;
        private Price price;
        private Price airportTax;

        public int getPassengerType() {
            return passengerType;
        }

        public void setPassengerType(int passengerType) {
            this.passengerType = passengerType;
        }

        public int getPassengerCount() {
            return passengerCount;
        }

        public void setPassengerCount(int passengerCount) {
            this.passengerCount = passengerCount;
        }

        public Price getPrice() {
            return price;
        }

        public void setPrice(Price price) {
            this.price = price;
        }

        public Price getAirportTax() {
            return airportTax;
        }

        public void setAirportTax(Price airportTax) {
            this.airportTax = airportTax;
        }
    }

    public static class SeatInfo {
        private int availableSeatCount;
        private int availableSeatCountType;

        public int getAvailableSeatCount() {
            return availableSeatCount;
        }

        public void setAvailableSeatCount(int availableSeatCount) {
            this.availableSeatCount = availableSeatCount;
        }

        public int getAvailableSeatCountType() {
            return availableSeatCountType;
        }

        public void setAvailableSeatCountType(int availableSeatCountType) {
            this.availableSeatCountType = availableSeatCountType;
        }
    }

    public static class FlightClassInformation {
        private int type;
        private String segmentId;
        private String name;
        private String id;
        private String code;

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getSegmentId() {
            return segmentId;
        }

        public void setSegmentId(String segmentId) {
            this.segmentId = segmentId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }

    public static class ReservableInfo {
        private boolean reservable;
        private String optionDate;

        public boolean isReservable() {
            return reservable;
        }

        public void setReservable(boolean reservable) {
            this.reservable = reservable;
        }

        public String getOptionDate() {
            return optionDate;
        }

        public void setOptionDate(String optionDate) {
            this.optionDate = optionDate;
        }
    }

    public static class OfferId {
        private String groupKey;
        private String offerId;
        private Provider provider;

        public String getGroupKey() {
            return groupKey;
        }

        public void setGroupKey(String groupKey) {
            this.groupKey = groupKey;
        }

        public String getOfferId() {
            return offerId;
        }

        public void setOfferId(String offerId) {
            this.offerId = offerId;
        }

        public Provider getProvider() {
            return provider;
        }

        public void setProvider(Provider provider) {
            this.provider = provider;
        }
    }

    public static class FlightBrandInfo {
        private String id;
        private String name;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class Details {
        private int flightResponseListType;
        private boolean enablePaging;

        public int getFlightResponseListType() {
            return flightResponseListType;
        }

        public void setFlightResponseListType(int flightResponseListType) {
            this.flightResponseListType = flightResponseListType;
        }

        public boolean isEnablePaging() {
            return enablePaging;
        }

        public void setEnablePaging(boolean enablePaging) {
            this.enablePaging = enablePaging;
        }
    }

    public static class Provider {
        private String displayName;
        private String id;
        private String name;

        public String getDisplayName() {
            return displayName;
        }

        public void setDisplayName(String displayName) {
            this.displayName = displayName;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }}
