<div class="booking-transaction-container">
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">Réservation de vol</h1>
      <p class="page-subtitle">Complétez votre réservation en quelques étapes simples</p>
    </div>
    <div class="header-illustration">
      <img src="assets/images/booking-banner.jpg" alt="Réservation de vol">
    </div>
  </div>

  <!-- Stepper pour les étapes de réservation -->
  <mat-horizontal-stepper [linear]="true" #stepper [selectedIndex]="currentStep - 1">
    <!-- Étape 1: Démarrer la transaction (masquée si déjà démarrée automatiquement) -->
    <mat-step [completed]="beginResponse !== null" [editable]="!transactionId">
      <ng-template matStepLabel>{{ transactionId ? 'Réservation démarrée' : 'Démarrer la réservation' }}</ng-template>

      <div class="step-content">
        <div class="step-header">
          <h2>Démarrer votre réservation</h2>
          <p>Nous allons commencer le processus de réservation pour les vols sélectionnés.</p>
        </div>

        <div *ngIf="errorMessage" class="error-message">
          <mat-icon>error</mat-icon>
          <span>{{ errorMessage }}</span>
        </div>

        <div *ngIf="offerIds.length === 0" class="error-message">
          <mat-icon>error</mat-icon>
          <span>Aucun vol n'a été sélectionné. Veuillez retourner à la page de recherche et sélectionner un vol.</span>
          <button mat-raised-button color="primary" routerLink="/search-price">
            Retour à la recherche
          </button>
        </div>

        <div *ngIf="offerIds.length > 0">
          <div class="offer-summary">
            <h3>Résumé de votre sélection</h3>
            <div class="offer-ids">
              <p>Offres sélectionnées: {{ offerIds.length }}</p>
              <div class="offer-card-container">
                <div class="offer-card" *ngFor="let offerId of offerIds">
                  <div class="offer-card-header">
                    <i class="fas fa-plane"></i>
                    <span class="offer-title">{{ formatOfferId(offerId) }}</span>
                  </div>
                  <div class="offer-card-content">
                    <p class="offer-info">Votre vol est prêt à être réservé</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <form [formGroup]="beginTransactionForm" (ngSubmit)="beginTransaction()">
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Devise</mat-label>
                <mat-select formControlName="currency" placeholder="Sélectionnez une devise">
                  <mat-option value="EUR">Euro (EUR)</mat-option>
                  <mat-option value="USD">Dollar américain (USD)</mat-option>
                  <mat-option value="GBP">Livre sterling (GBP)</mat-option>
                </mat-select>
                <mat-hint>Devise utilisée pour la transaction</mat-hint>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Culture</mat-label>
                <mat-select formControlName="culture" placeholder="Sélectionnez une langue">
                  <mat-option value="fr-FR">Français (FR)</mat-option>
                  <mat-option value="en-US">Anglais (US)</mat-option>
                  <mat-option value="en-GB">Anglais (GB)</mat-option>
                </mat-select>
                <mat-hint>Langue utilisée pour la transaction</mat-hint>
              </mat-form-field>
            </div>

            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="isLoading">
                <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
                <span *ngIf="!isLoading">Démarrer la réservation</span>
              </button>
              <button mat-button type="button" routerLink="/get-offer">Annuler</button>
            </div>
          </form>
        </div>

        <!-- Affichage de la réponse de début de transaction -->
        <div *ngIf="beginResponse && beginResponse.body" class="response-summary">
          <h3>Détails de la transaction</h3>
          <div class="transaction-details">
            <!-- ID de transaction masqué pour une interface plus professionnelle -->
            <p><strong>Expire le:</strong> {{ formatDate(beginResponse.body.expiresOn) }}</p>
            <p><strong>Statut:</strong> {{ beginResponse.body.status === 1 ? 'Active' : beginResponse.body.status || 'N/A' }}</p>
          </div>
        </div>
      </div>
    </mat-step>

    <!-- Étape 2: Définir les informations de réservation -->
    <mat-step [completed]="infoResponse !== null">
      <ng-template matStepLabel>Informations voyageurs</ng-template>

      <div class="step-content">
        <div class="step-header">
          <h2>Informations des voyageurs</h2>
          <p>Veuillez fournir les informations pour tous les voyageurs.</p>
        </div>

        <div *ngIf="errorMessage" class="error-message">
          <mat-icon>error</mat-icon>
          <span>{{ errorMessage }}</span>
        </div>

        <form [formGroup]="reservationInfoForm" (ngSubmit)="setReservationInfo()">
          <div class="form-section">
            <h3>Informations générales</h3>
            <!-- Champ transactionId masqué mais toujours présent dans le formulaire -->
            <input type="hidden" formControlName="transactionId">

            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Note de réservation</mat-label>
                <textarea matInput formControlName="reservationNote" rows="3"></textarea>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Numéro de réservation d'agence</mat-label>
                <input matInput formControlName="agencyReservationNumber">
              </mat-form-field>
            </div>
          </div>

          <div class="form-section">
            <div class="section-header">
              <h3>Voyageurs</h3>
              <button mat-mini-fab color="primary" type="button" (click)="addTraveller()" aria-label="Ajouter un voyageur">
                <mat-icon>add</mat-icon>
              </button>
            </div>

            <div formArrayName="travellers">
              <mat-accordion>
                <mat-expansion-panel *ngFor="let traveller of travellers.controls; let i = index" [expanded]="i === 0">
                  <mat-expansion-panel-header>
                    <mat-panel-title>
                      Voyageur {{ i + 1 }}
                    </mat-panel-title>
                    <mat-panel-description *ngIf="traveller.get('name')?.value || traveller.get('surname')?.value">
                      {{ traveller.get('name')?.value }} {{ traveller.get('surname')?.value }}
                    </mat-panel-description>
                  </mat-expansion-panel-header>

                  <div [formGroupName]="i">
                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Type de voyageur</mat-label>
                        <mat-select formControlName="type" placeholder="Sélectionnez un type">
                          <mat-option *ngFor="let type of passengerTypes" [value]="type.value">
                            {{ type.label }}
                          </mat-option>
                        </mat-select>
                        <mat-hint>Veuillez sélectionner un type de voyageur</mat-hint>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Titre</mat-label>
                        <mat-select formControlName="title" placeholder="Sélectionnez un titre">
                          <mat-option *ngFor="let title of travellerTitles" [value]="title.value">
                            {{ title.label }}
                          </mat-option>
                        </mat-select>
                        <mat-hint>M., Mme, etc.</mat-hint>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Type de passager</mat-label>
                        <mat-select formControlName="passengerType" placeholder="Sélectionnez un type">
                          <mat-option *ngFor="let type of passengerTypes" [value]="type.value">
                            {{ type.label }}
                          </mat-option>
                        </mat-select>
                        <mat-hint>Adulte, Enfant, etc.</mat-hint>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Prénom</mat-label>
                        <input matInput formControlName="name" required>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Nom</mat-label>
                        <input matInput formControlName="surname" required>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Date de naissance</mat-label>
                        <input matInput [matDatepicker]="birthDatePicker" formControlName="birthDate" required>
                        <mat-datepicker-toggle matSuffix [for]="birthDatePicker"></mat-datepicker-toggle>
                        <mat-datepicker #birthDatePicker></mat-datepicker>
                      </mat-form-field>
                    </div>

                    <!-- Ligne 1: Nationalité -->
                    <div formGroupName="nationality" class="country-selector-container">
                      <h4>Nationalité</h4>
                      <!-- Sélecteur de pays avec drapeaux -->
                      <app-country-selector
                        [label]="'Sélectionnez votre nationalité'"
                        [placeholder]="'Rechercher un pays'"
                        [required]="true"
                        (countrySelected)="onCountrySelected($event, i, 'nationality')">
                      </app-country-selector>

                      <!-- Champ pour stocker le code pays -->
                      <mat-form-field appearance="outline">
                        <mat-label>Code pays</mat-label>
                        <input matInput formControlName="twoLetterCode" required placeholder="Code pays">
                        <mat-hint>Sélectionnez un pays ci-dessus ou entrez le code à 2 lettres (ex: FR, US, GB)</mat-hint>
                        <mat-error *ngIf="traveller.get('nationality.twoLetterCode')?.invalid">
                          Code pays invalide (2 lettres requis)
                        </mat-error>
                      </mat-form-field>
                    </div>

                    <!-- Ligne 2: Genre et Numéro d'identité -->
                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Genre</mat-label>
                        <mat-select formControlName="gender">
                          <mat-option *ngFor="let gender of genderOptions" [value]="gender.value">
                            {{ gender.label }}
                          </mat-option>
                        </mat-select>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Numéro d'identité</mat-label>
                        <input matInput formControlName="identityNumber">
                      </mat-form-field>
                    </div>

                    <div class="form-section">
                      <h4>Informations de passeport</h4>
                      <div formGroupName="passportInfo">
                        <div class="form-row">
                          <mat-form-field appearance="outline">
                            <mat-label>Numéro de série</mat-label>
                            <input matInput formControlName="serial">
                          </mat-form-field>

                          <mat-form-field appearance="outline">
                            <mat-label>Numéro de passeport</mat-label>
                            <input matInput formControlName="number" required>
                            <mat-hint>Minimum 5 caractères</mat-hint>
                            <mat-error *ngIf="traveller.get('passportInfo.number')?.invalid">
                              Numéro de passeport invalide (minimum 5 caractères)
                            </mat-error>
                          </mat-form-field>
                        </div>

                        <div class="form-row">
                          <mat-form-field appearance="outline">
                            <mat-label>Date d'expiration</mat-label>
                            <input matInput [matDatepicker]="expireDatePicker" formControlName="expireDate" required>
                            <mat-datepicker-toggle matSuffix [for]="expireDatePicker"></mat-datepicker-toggle>
                            <mat-datepicker #expireDatePicker></mat-datepicker>
                            <mat-hint>Doit être dans le futur</mat-hint>
                            <mat-error *ngIf="traveller.get('passportInfo.expireDate')?.errors?.['expireDateInvalid']">
                              La date d'expiration doit être dans le futur
                            </mat-error>
                            <mat-error *ngIf="traveller.get('passportInfo.expireDate')?.errors?.['required']">
                              La date d'expiration est requise
                            </mat-error>
                          </mat-form-field>

                          <mat-form-field appearance="outline">
                            <mat-label>Date d'émission</mat-label>
                            <input matInput [matDatepicker]="issueDatePicker" formControlName="issueDate" required>
                            <mat-datepicker-toggle matSuffix [for]="issueDatePicker"></mat-datepicker-toggle>
                            <mat-datepicker #issueDatePicker></mat-datepicker>
                          </mat-form-field>
                        </div>

                        <div class="form-row">
                          <div class="country-selector-container">
                            <h5>Pays de citoyenneté</h5>
                            <!-- Sélecteur de pays avec drapeaux -->
                            <app-country-selector
                              [label]="'Pays de citoyenneté'"
                              [placeholder]="'Rechercher un pays'"
                              [required]="true"
                              (countrySelected)="onCountrySelected($event, i, 'citizenshipCountryCode')">
                            </app-country-selector>

                            <!-- Champ pour stocker le code pays -->
                            <mat-form-field appearance="outline">
                              <mat-label>Code pays de citoyenneté</mat-label>
                              <input matInput formControlName="citizenshipCountryCode" required placeholder="Code pays">
                              <mat-hint>Sélectionnez un pays ci-dessus ou entrez le code à 2 lettres (ex: FR, US, GB)</mat-hint>
                              <mat-error *ngIf="traveller.get('passportInfo.citizenshipCountryCode')?.invalid">
                                Code pays invalide (2 lettres requis)
                              </mat-error>
                            </mat-form-field>
                          </div>

                          <div class="country-selector-container">
                            <h5>Pays d'émission du passeport</h5>
                            <!-- Sélecteur de pays avec drapeaux -->
                            <app-country-selector
                              [label]="'Pays d\'émission'"
                              [placeholder]="'Rechercher un pays'"
                              [required]="true"
                              (countrySelected)="onCountrySelected($event, i, 'issueCountryCode')">
                            </app-country-selector>

                            <!-- Champ pour stocker le code pays -->
                            <mat-form-field appearance="outline">
                              <mat-label>Code pays d'émission</mat-label>
                              <input matInput formControlName="issueCountryCode" required placeholder="Code pays">
                              <mat-hint>Sélectionnez un pays ci-dessus ou entrez le code à 2 lettres (ex: FR, US, GB)</mat-hint>
                              <mat-error *ngIf="traveller.get('passportInfo.issueCountryCode')?.invalid">
                                Code pays invalide (2 lettres requis)
                              </mat-error>
                            </mat-form-field>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="form-section">
                      <h4>Adresse</h4>
                      <div formGroupName="address">
                        <div class="form-row">
                          <div formGroupName="contactPhone">
                            <mat-form-field appearance="outline">
                              <mat-label>Code pays</mat-label>
                              <input matInput formControlName="countryCode" required>
                            </mat-form-field>

                            <mat-form-field appearance="outline">
                              <mat-label>Indicatif régional</mat-label>
                              <input matInput formControlName="areaCode">
                            </mat-form-field>

                            <mat-form-field appearance="outline">
                              <mat-label>Numéro de téléphone</mat-label>
                              <input matInput formControlName="phoneNumber" required>
                            </mat-form-field>
                          </div>
                        </div>

                        <div class="form-row">
                          <mat-form-field appearance="outline" class="full-width">
                            <mat-label>Email</mat-label>
                            <input matInput formControlName="email" required type="email">
                          </mat-form-field>
                        </div>

                        <div class="form-row">
                          <mat-form-field appearance="outline" class="full-width">
                            <mat-label>Adresse</mat-label>
                            <input matInput formControlName="address" required>
                          </mat-form-field>
                        </div>

                        <div class="form-row">
                          <mat-form-field appearance="outline">
                            <mat-label>Code postal</mat-label>
                            <input matInput formControlName="zipCode" required>
                          </mat-form-field>

                          <div formGroupName="city">
                            <mat-form-field appearance="outline">
                              <mat-label>ID de ville</mat-label>
                              <input matInput formControlName="id" required>
                            </mat-form-field>

                            <mat-form-field appearance="outline">
                              <mat-label>Nom de ville</mat-label>
                              <input matInput formControlName="name" required>
                            </mat-form-field>
                          </div>
                        </div>

                        <div class="form-row">
                          <div formGroupName="country" class="country-selector-container">
                            <h5>Pays de résidence</h5>
                            <!-- Sélecteur de pays avec drapeaux -->
                            <app-country-selector
                              [label]="'Pays de résidence'"
                              [placeholder]="'Rechercher un pays'"
                              [required]="true"
                              (countrySelected)="onCountrySelected($event, i, 'country')">
                            </app-country-selector>

                            <!-- Champs pour stocker le code et le nom du pays -->
                            <div class="form-row">
                              <mat-form-field appearance="outline">
                                <mat-label>Code pays</mat-label>
                                <input matInput formControlName="id" required placeholder="Code pays">
                                <mat-hint>Code pays à 2 lettres (ex: FR, US, GB)</mat-hint>
                              </mat-form-field>

                              <mat-form-field appearance="outline">
                                <mat-label>Nom de pays</mat-label>
                                <input matInput formControlName="name" required placeholder="Nom du pays">
                                <mat-hint>Nom complet du pays (ex: France, États-Unis)</mat-hint>
                              </mat-form-field>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="form-row traveller-actions">
                      <mat-checkbox formControlName="isLeader">Chef de groupe</mat-checkbox>
                      <button mat-button color="warn" type="button" (click)="removeTraveller(i)" *ngIf="travellers.length > 1">
                        <mat-icon>delete</mat-icon> Supprimer ce voyageur
                      </button>
                    </div>
                  </div>
                </mat-expansion-panel>
              </mat-accordion>
            </div>
          </div>

          <div class="form-actions">
            <button mat-raised-button color="primary" type="submit" [disabled]="isLoading || reservationInfoForm.invalid">
              <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
              <span *ngIf="!isLoading">Enregistrer les informations</span>
            </button>
            <button mat-button type="button" (click)="previousStep()">Retour</button>
          </div>
        </form>

        <!-- Affichage de la réponse d'informations de réservation -->
        <div *ngIf="infoResponse && infoResponse.body" class="response-summary">
          <h3>Informations de réservation enregistrées</h3>
          <div class="reservation-details">
            <p><strong>Nombre de voyageurs:</strong> {{ infoResponse.body.reservationData && infoResponse.body.reservationData.travellers ? infoResponse.body.reservationData.travellers.length : 0 }}</p>
            <!-- ID de transaction masqué pour une interface plus professionnelle -->
          </div>
        </div>
      </div>
    </mat-step>

    <!-- Étape 3: Finaliser la transaction -->
    <mat-step [completed]="commitResponse !== null">
      <ng-template matStepLabel>Paiement et confirmation</ng-template>

      <div class="step-content">
        <div class="step-header">
          <h2>Paiement et confirmation</h2>
          <p>Finalisez votre réservation en effectuant le paiement.</p>
        </div>

        <div *ngIf="errorMessage" class="error-message">
          <mat-icon>error</mat-icon>
          <span>{{ errorMessage }}</span>
        </div>

        <form [formGroup]="commitTransactionForm" (ngSubmit)="commitTransaction()">
          <div class="form-section">
            <h3>Informations de paiement</h3>
            <!-- Champ transactionId masqué mais toujours présent dans le formulaire -->
            <input type="hidden" formControlName="transactionId">

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Option de paiement</mat-label>
                <mat-select formControlName="paymentOption">
                  <mat-option [value]="1">Carte de crédit</mat-option>
                  <mat-option [value]="2">Virement bancaire</mat-option>
                  <mat-option [value]="3">PayPal</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div formGroupName="paymentInformation">
              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Nom du titulaire</mat-label>
                  <input matInput formControlName="accountName">
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Type de paiement</mat-label>
                  <mat-select formControlName="paymentTypeId">
                    <mat-option [value]="1">Carte de crédit</mat-option>
                    <mat-option [value]="2">Virement bancaire</mat-option>
                    <mat-option [value]="3">PayPal</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <div class="form-row">
                <div formGroupName="paymentPrice">
                  <mat-form-field appearance="outline">
                    <mat-label>Montant</mat-label>
                    <input matInput formControlName="amount" type="number" required>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>Devise</mat-label>
                    <mat-select formControlName="currency">
                      <mat-option value="EUR">Euro (EUR)</mat-option>
                      <mat-option value="USD">Dollar américain (USD)</mat-option>
                      <mat-option value="GBP">Livre sterling (GBP)</mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Nombre de versements</mat-label>
                  <input matInput formControlName="installmentCount">
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Date de paiement</mat-label>
                  <input matInput [matDatepicker]="paymentDatePicker" formControlName="paymentDate">
                  <mat-datepicker-toggle matSuffix [for]="paymentDatePicker"></mat-datepicker-toggle>
                  <mat-datepicker #paymentDatePicker></mat-datepicker>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Type de reçu</mat-label>
                  <input matInput formControlName="receiptType">
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Référence</mat-label>
                  <input matInput formControlName="reference">
                </mat-form-field>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button mat-raised-button color="primary" type="submit" [disabled]="isLoading || commitTransactionForm.invalid">
              <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
              <span *ngIf="!isLoading">Finaliser la réservation</span>
            </button>
            <button mat-button type="button" (click)="previousStep()">Retour</button>
          </div>
        </form>

        <!-- Affichage de la réponse de finalisation de transaction -->
        <div *ngIf="commitResponse && commitResponse.body" class="response-summary success-response">
          <h3>Réservation confirmée!</h3>
          <div class="confirmation-details">
            <p><strong>Numéro de réservation:</strong> {{ commitResponse.body.reservationNumber || 'N/A' }}</p>
            <!-- ID de transaction masqué pour une interface plus professionnelle -->

            <div class="confirmation-message">
              <mat-icon>check_circle</mat-icon>
              <p>Votre réservation a été confirmée avec succès. Vous recevrez bientôt un email de confirmation avec tous les détails de votre voyage.</p>
            </div>

            <div class="confirmation-actions">
              <button mat-raised-button color="primary" routerLink="/search-price">
                Retour à la recherche
              </button>
            </div>
          </div>
        </div>
      </div>
    </mat-step>
  </mat-horizontal-stepper>
</div>
