{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport class BookingService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.apiUrl = 'http://localhost:8080/booking';\n  }\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request) {\n    // Utiliser l'endpoint unifié pour toutes les actions de transaction\n    const endpoint = '/booking-transaction';\n    const requestBody = request; // Envoyer l'objet BookingTransactionRequest complet\n    // Récupérer le token d'authentification\n    const token = this.authService.getToken();\n    if (!token) {\n      throw new Error('Vous devez être connecté pour effectuer cette action.');\n    }\n    // Créer les en-têtes avec le token d'authentification\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n    console.log(`Envoi de la requête ${request.action} à ${endpoint}:`, requestBody);\n    return this.http.post(`${this.apiUrl}${endpoint}`, requestBody, {\n      headers\n    }).pipe(map(response => {\n      console.log(`Réponse reçue de ${endpoint}:`, response);\n      // Vérifier si la réponse est valide\n      if (!response) {\n        throw new Error('Réponse invalide du serveur');\n      }\n      // Vérifier si la réponse est au format BookingTransactionResponse\n      // ou si c'est une réponse directe du type spécifique (BeginTransactionResponse, etc.)\n      let bookingResponse;\n      // Si la réponse a déjà le format BookingTransactionResponse\n      if (response.action && (response.beginResponse && request.action === 'begin' || response.infoResponse && request.action === 'info' || response.commitResponse && request.action === 'commit')) {\n        bookingResponse = response;\n      }\n      // Si la réponse est une réponse directe (non encapsulée)\n      else {\n        bookingResponse = {\n          action: request.action\n        };\n        // Adapter la réponse selon l'action\n        switch (request.action) {\n          case 'begin':\n            // Si c'est une réponse BeginTransactionResponse directe\n            if (response.header && response.body && response.body.transactionId) {\n              bookingResponse.beginResponse = response;\n            }\n            // Si c'est un format simplifié ou personnalisé\n            else if (response.transactionId) {\n              bookingResponse.beginResponse = {\n                header: {\n                  requestId: response.requestId || '',\n                  success: true,\n                  responseTime: response.responseTime || new Date().toISOString(),\n                  messages: response.messages || []\n                },\n                body: {\n                  transactionId: response.transactionId,\n                  expiresOn: response.expiresOn || new Date(Date.now() + 3600000).toISOString(),\n                  status: response.status || 1,\n                  transactionType: response.transactionType || 1,\n                  reservationData: response.reservationData || {\n                    travellers: [],\n                    reservationInfo: null,\n                    services: [],\n                    paymentDetail: null,\n                    invoices: []\n                  }\n                }\n              };\n            } else {\n              throw new Error('Format de réponse non reconnu pour l\\'action begin');\n            }\n            break;\n          case 'info':\n            // Si c'est une réponse SetReservationInfoResponse directe\n            if (response.header && response.body && response.body.transactionId) {\n              bookingResponse.infoResponse = response;\n            } else {\n              throw new Error('Format de réponse non reconnu pour l\\'action info');\n            }\n            break;\n          case 'commit':\n            // Si c'est une réponse CommitTransactionResponse directe\n            if (response.header && response.body && (response.body.reservationNumber || response.body.transactionId)) {\n              bookingResponse.commitResponse = response;\n            } else {\n              throw new Error('Format de réponse non reconnu pour l\\'action commit');\n            }\n            break;\n        }\n      }\n      return response;\n    }), catchError(error => {\n      console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);\n      // Améliorer le message d'erreur pour faciliter le débogage\n      let errorMessage = 'Une erreur est survenue lors de la transaction de réservation';\n      if (error.error && error.error.message) {\n        // Erreur provenant du backend avec un message\n        errorMessage = `Erreur: ${error.error.message}`;\n      } else if (error.message) {\n        // Erreur avec un message simple\n        errorMessage = error.message;\n      } else if (error.status) {\n        // Erreur HTTP\n        switch (error.status) {\n          case 401:\n            errorMessage = 'Vous n\\'êtes pas autorisé à effectuer cette action. Veuillez vous reconnecter.';\n            break;\n          case 403:\n            errorMessage = 'Accès refusé. Vous n\\'avez pas les droits nécessaires pour effectuer cette action.';\n            break;\n          case 404:\n            errorMessage = 'Le service de réservation est introuvable. Veuillez contacter l\\'administrateur.';\n            break;\n          case 500:\n            errorMessage = 'Erreur interne du serveur. Veuillez réessayer ultérieurement.';\n            break;\n          default:\n            errorMessage = `Erreur HTTP ${error.status}: ${error.statusText}`;\n        }\n      }\n      // Créer une nouvelle erreur avec un message plus descriptif\n      const enhancedError = new Error(errorMessage);\n      enhancedError.name = 'BookingTransactionError';\n      // Conserver les détails de l'erreur originale\n      enhancedError.originalError = error;\n      throw enhancedError;\n    }));\n  }\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds, currency = 'EUR', culture = 'fr-FR') {\n    const request = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n    return this.bookingTransaction(request);\n  }\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request) {\n    const bookingRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request) {\n    const bookingRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId) {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId) {\n    return {\n      transactionId,\n      paymentOption: 1,\n      paymentInformation: {\n        paymentTypeId: 1,\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n  static {\n    this.ɵfac = function BookingService_Factory(t) {\n      return new (t || BookingService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BookingService,\n      factory: BookingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "catchError", "map", "BookingService", "constructor", "http", "authService", "apiUrl", "bookingTransaction", "request", "endpoint", "requestBody", "token", "getToken", "Error", "headers", "console", "log", "action", "post", "pipe", "response", "bookingResponse", "beginResponse", "infoResponse", "commitResponse", "header", "body", "transactionId", "requestId", "success", "responseTime", "Date", "toISOString", "messages", "expiresOn", "now", "status", "transactionType", "reservationData", "travellers", "reservationInfo", "services", "paymentDetail", "invoices", "reservationNumber", "error", "errorMessage", "message", "statusText", "enhancedError", "name", "originalError", "beginTransaction", "offerIds", "currency", "culture", "beginRequest", "setReservationInfo", "bookingRequest", "infoRequest", "commitTransaction", "commitRequest", "createDefaultReservationInfoRequest", "reservationNote", "agencyReservationNumber", "createDefaultCommitTransactionRequest", "paymentOption", "paymentInformation", "paymentTypeId", "paymentPrice", "amount", "paymentDate", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\booking.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { AuthService } from './auth.service';\nimport {\n  BookingTransactionRequest,\n  BookingTransactionResponse,\n  BeginTransactionRequest,\n  BeginTransactionResponse,\n  SetReservationInfoRequest,\n  SetReservationInfoResponse,\n  CommitTransactionRequest,\n  CommitTransactionResponse\n} from '../models/booking';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class BookingService {\n  private apiUrl = 'http://localhost:8080/booking';\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) { }\n\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request: BookingTransactionRequest): Observable<BookingTransactionResponse> {\n    // Utiliser l'endpoint unifié pour toutes les actions de transaction\n    const endpoint = '/booking-transaction';\n    const requestBody = request; // Envoyer l'objet BookingTransactionRequest complet\n\n    // Récupérer le token d'authentification\n    const token = this.authService.getToken();\n    if (!token) {\n      throw new Error('Vous devez être connecté pour effectuer cette action.');\n    }\n\n    // Créer les en-têtes avec le token d'authentification\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n\n    console.log(`Envoi de la requête ${request.action} à ${endpoint}:`, requestBody);\n\n    return this.http.post<BookingTransactionResponse>(\n      `${this.apiUrl}${endpoint}`,\n      requestBody,\n      { headers }\n    ).pipe(\n      map(response => {\n        console.log(`Réponse reçue de ${endpoint}:`, response);\n\n        // Vérifier si la réponse est valide\n        if (!response) {\n          throw new Error('Réponse invalide du serveur');\n        }\n\n        // Vérifier si la réponse est au format BookingTransactionResponse\n        // ou si c'est une réponse directe du type spécifique (BeginTransactionResponse, etc.)\n        let bookingResponse: BookingTransactionResponse;\n\n        // Si la réponse a déjà le format BookingTransactionResponse\n        if (response.action &&\n            ((response.beginResponse && request.action === 'begin') ||\n             (response.infoResponse && request.action === 'info') ||\n             (response.commitResponse && request.action === 'commit'))) {\n\n          bookingResponse = response;\n        }\n        // Si la réponse est une réponse directe (non encapsulée)\n        else {\n          bookingResponse = {\n            action: request.action\n          };\n\n          // Adapter la réponse selon l'action\n          switch (request.action) {\n            case 'begin':\n              // Si c'est une réponse BeginTransactionResponse directe\n              if (response.header && response.body && response.body.transactionId) {\n                bookingResponse.beginResponse = response;\n              }\n              // Si c'est un format simplifié ou personnalisé\n              else if (response.transactionId) {\n                bookingResponse.beginResponse = {\n                  header: {\n                    requestId: response.requestId || '',\n                    success: true,\n                    responseTime: response.responseTime || new Date().toISOString(),\n                    messages: response.messages || []\n                  },\n                  body: {\n                    transactionId: response.transactionId,\n                    expiresOn: response.expiresOn || new Date(Date.now() + 3600000).toISOString(),\n                    status: response.status || 1,\n                    transactionType: response.transactionType || 1,\n                    reservationData: response.reservationData || {\n                      travellers: [],\n                      reservationInfo: null,\n                      services: [],\n                      paymentDetail: null,\n                      invoices: []\n                    }\n                  }\n                };\n              } else {\n                throw new Error('Format de réponse non reconnu pour l\\'action begin');\n              }\n              break;\n\n            case 'info':\n              // Si c'est une réponse SetReservationInfoResponse directe\n              if (response.header && response.body && response.body.transactionId) {\n                bookingResponse.infoResponse = response;\n              } else {\n                throw new Error('Format de réponse non reconnu pour l\\'action info');\n              }\n              break;\n\n            case 'commit':\n              // Si c'est une réponse CommitTransactionResponse directe\n              if (response.header && response.body &&\n                 (response.body.reservationNumber || response.body.transactionId)) {\n                bookingResponse.commitResponse = response;\n              } else {\n                throw new Error('Format de réponse non reconnu pour l\\'action commit');\n              }\n              break;\n          }\n        }\n\n        return response;\n      }),\n      catchError(error => {\n        console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);\n\n        // Améliorer le message d'erreur pour faciliter le débogage\n        let errorMessage = 'Une erreur est survenue lors de la transaction de réservation';\n\n        if (error.error && error.error.message) {\n          // Erreur provenant du backend avec un message\n          errorMessage = `Erreur: ${error.error.message}`;\n        } else if (error.message) {\n          // Erreur avec un message simple\n          errorMessage = error.message;\n        } else if (error.status) {\n          // Erreur HTTP\n          switch (error.status) {\n            case 401:\n              errorMessage = 'Vous n\\'êtes pas autorisé à effectuer cette action. Veuillez vous reconnecter.';\n              break;\n            case 403:\n              errorMessage = 'Accès refusé. Vous n\\'avez pas les droits nécessaires pour effectuer cette action.';\n              break;\n            case 404:\n              errorMessage = 'Le service de réservation est introuvable. Veuillez contacter l\\'administrateur.';\n              break;\n            case 500:\n              errorMessage = 'Erreur interne du serveur. Veuillez réessayer ultérieurement.';\n              break;\n            default:\n              errorMessage = `Erreur HTTP ${error.status}: ${error.statusText}`;\n          }\n        }\n\n        // Créer une nouvelle erreur avec un message plus descriptif\n        const enhancedError = new Error(errorMessage);\n        enhancedError.name = 'BookingTransactionError';\n\n        // Conserver les détails de l'erreur originale\n        (enhancedError as any).originalError = error;\n\n        throw enhancedError;\n      })\n    );\n  }\n\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds: string[], currency: string = 'EUR', culture: string = 'fr-FR'): Observable<BookingTransactionResponse> {\n    const request: BookingTransactionRequest = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n\n    return this.bookingTransaction(request);\n  }\n\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request: SetReservationInfoRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request: CommitTransactionRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId: string): SetReservationInfoRequest {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId: string): CommitTransactionRequest {\n    return {\n      transactionId,\n      paymentOption: 1, // Option de paiement par défaut\n      paymentInformation: {\n        paymentTypeId: 1, // Type de paiement par défaut\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;;AAgBhD,OAAM,MAAOC,cAAc;EAGzBC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAJb,KAAAC,MAAM,GAAG,+BAA+B;EAK5C;EAEJ;;;;;EAKAC,kBAAkBA,CAACC,OAAkC;IACnD;IACA,MAAMC,QAAQ,GAAG,sBAAsB;IACvC,MAAMC,WAAW,GAAGF,OAAO,CAAC,CAAC;IAE7B;IACA,MAAMG,KAAK,GAAG,IAAI,CAACN,WAAW,CAACO,QAAQ,EAAE;IACzC,IAAI,CAACD,KAAK,EAAE;MACV,MAAM,IAAIE,KAAK,CAAC,uDAAuD,CAAC;;IAG1E;IACA,MAAMC,OAAO,GAAG,IAAIf,WAAW,CAAC;MAC9B,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAE,UAAUY,KAAK;KACjC,CAAC;IAEFI,OAAO,CAACC,GAAG,CAAC,uBAAuBR,OAAO,CAACS,MAAM,MAAMR,QAAQ,GAAG,EAAEC,WAAW,CAAC;IAEhF,OAAO,IAAI,CAACN,IAAI,CAACc,IAAI,CACnB,GAAG,IAAI,CAACZ,MAAM,GAAGG,QAAQ,EAAE,EAC3BC,WAAW,EACX;MAAEI;IAAO,CAAE,CACZ,CAACK,IAAI,CACJlB,GAAG,CAACmB,QAAQ,IAAG;MACbL,OAAO,CAACC,GAAG,CAAC,oBAAoBP,QAAQ,GAAG,EAAEW,QAAQ,CAAC;MAEtD;MACA,IAAI,CAACA,QAAQ,EAAE;QACb,MAAM,IAAIP,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA;MACA,IAAIQ,eAA2C;MAE/C;MACA,IAAID,QAAQ,CAACH,MAAM,KACbG,QAAQ,CAACE,aAAa,IAAId,OAAO,CAACS,MAAM,KAAK,OAAO,IACpDG,QAAQ,CAACG,YAAY,IAAIf,OAAO,CAACS,MAAM,KAAK,MAAO,IACnDG,QAAQ,CAACI,cAAc,IAAIhB,OAAO,CAACS,MAAM,KAAK,QAAS,CAAC,EAAE;QAE9DI,eAAe,GAAGD,QAAQ;;MAE5B;MAAA,KACK;QACHC,eAAe,GAAG;UAChBJ,MAAM,EAAET,OAAO,CAACS;SACjB;QAED;QACA,QAAQT,OAAO,CAACS,MAAM;UACpB,KAAK,OAAO;YACV;YACA,IAAIG,QAAQ,CAACK,MAAM,IAAIL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACC,aAAa,EAAE;cACnEN,eAAe,CAACC,aAAa,GAAGF,QAAQ;;YAE1C;YAAA,KACK,IAAIA,QAAQ,CAACO,aAAa,EAAE;cAC/BN,eAAe,CAACC,aAAa,GAAG;gBAC9BG,MAAM,EAAE;kBACNG,SAAS,EAAER,QAAQ,CAACQ,SAAS,IAAI,EAAE;kBACnCC,OAAO,EAAE,IAAI;kBACbC,YAAY,EAAEV,QAAQ,CAACU,YAAY,IAAI,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;kBAC/DC,QAAQ,EAAEb,QAAQ,CAACa,QAAQ,IAAI;iBAChC;gBACDP,IAAI,EAAE;kBACJC,aAAa,EAAEP,QAAQ,CAACO,aAAa;kBACrCO,SAAS,EAAEd,QAAQ,CAACc,SAAS,IAAI,IAAIH,IAAI,CAACA,IAAI,CAACI,GAAG,EAAE,GAAG,OAAO,CAAC,CAACH,WAAW,EAAE;kBAC7EI,MAAM,EAAEhB,QAAQ,CAACgB,MAAM,IAAI,CAAC;kBAC5BC,eAAe,EAAEjB,QAAQ,CAACiB,eAAe,IAAI,CAAC;kBAC9CC,eAAe,EAAElB,QAAQ,CAACkB,eAAe,IAAI;oBAC3CC,UAAU,EAAE,EAAE;oBACdC,eAAe,EAAE,IAAI;oBACrBC,QAAQ,EAAE,EAAE;oBACZC,aAAa,EAAE,IAAI;oBACnBC,QAAQ,EAAE;;;eAGf;aACF,MAAM;cACL,MAAM,IAAI9B,KAAK,CAAC,oDAAoD,CAAC;;YAEvE;UAEF,KAAK,MAAM;YACT;YACA,IAAIO,QAAQ,CAACK,MAAM,IAAIL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACC,aAAa,EAAE;cACnEN,eAAe,CAACE,YAAY,GAAGH,QAAQ;aACxC,MAAM;cACL,MAAM,IAAIP,KAAK,CAAC,mDAAmD,CAAC;;YAEtE;UAEF,KAAK,QAAQ;YACX;YACA,IAAIO,QAAQ,CAACK,MAAM,IAAIL,QAAQ,CAACM,IAAI,KAChCN,QAAQ,CAACM,IAAI,CAACkB,iBAAiB,IAAIxB,QAAQ,CAACM,IAAI,CAACC,aAAa,CAAC,EAAE;cACnEN,eAAe,CAACG,cAAc,GAAGJ,QAAQ;aAC1C,MAAM;cACL,MAAM,IAAIP,KAAK,CAAC,qDAAqD,CAAC;;YAExE;;;MAIN,OAAOO,QAAQ;IACjB,CAAC,CAAC,EACFpB,UAAU,CAAC6C,KAAK,IAAG;MACjB9B,OAAO,CAAC8B,KAAK,CAAC,iDAAiDrC,OAAO,CAACS,MAAM,IAAI,EAAE4B,KAAK,CAAC;MAEzF;MACA,IAAIC,YAAY,GAAG,+DAA+D;MAElF,IAAID,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACE,OAAO,EAAE;QACtC;QACAD,YAAY,GAAG,WAAWD,KAAK,CAACA,KAAK,CAACE,OAAO,EAAE;OAChD,MAAM,IAAIF,KAAK,CAACE,OAAO,EAAE;QACxB;QACAD,YAAY,GAAGD,KAAK,CAACE,OAAO;OAC7B,MAAM,IAAIF,KAAK,CAACT,MAAM,EAAE;QACvB;QACA,QAAQS,KAAK,CAACT,MAAM;UAClB,KAAK,GAAG;YACNU,YAAY,GAAG,gFAAgF;YAC/F;UACF,KAAK,GAAG;YACNA,YAAY,GAAG,oFAAoF;YACnG;UACF,KAAK,GAAG;YACNA,YAAY,GAAG,kFAAkF;YACjG;UACF,KAAK,GAAG;YACNA,YAAY,GAAG,+DAA+D;YAC9E;UACF;YACEA,YAAY,GAAG,eAAeD,KAAK,CAACT,MAAM,KAAKS,KAAK,CAACG,UAAU,EAAE;;;MAIvE;MACA,MAAMC,aAAa,GAAG,IAAIpC,KAAK,CAACiC,YAAY,CAAC;MAC7CG,aAAa,CAACC,IAAI,GAAG,yBAAyB;MAE9C;MACCD,aAAqB,CAACE,aAAa,GAAGN,KAAK;MAE5C,MAAMI,aAAa;IACrB,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;EAOAG,gBAAgBA,CAACC,QAAkB,EAAEC,QAAA,GAAmB,KAAK,EAAEC,OAAA,GAAkB,OAAO;IACtF,MAAM/C,OAAO,GAA8B;MACzCS,MAAM,EAAE,OAAO;MACfuC,YAAY,EAAE;QACZH,QAAQ;QACRC,QAAQ;QACRC;;KAEH;IAED,OAAO,IAAI,CAAChD,kBAAkB,CAACC,OAAO,CAAC;EACzC;EAEA;;;;;EAKAiD,kBAAkBA,CAACjD,OAAkC;IACnD,MAAMkD,cAAc,GAA8B;MAChDzC,MAAM,EAAE,MAAM;MACd0C,WAAW,EAAEnD;KACd;IAED,OAAO,IAAI,CAACD,kBAAkB,CAACmD,cAAc,CAAC;EAChD;EAEA;;;;;EAKAE,iBAAiBA,CAACpD,OAAiC;IACjD,MAAMkD,cAAc,GAA8B;MAChDzC,MAAM,EAAE,QAAQ;MAChB4C,aAAa,EAAErD;KAChB;IAED,OAAO,IAAI,CAACD,kBAAkB,CAACmD,cAAc,CAAC;EAChD;EAEA;;;;;EAKAI,mCAAmCA,CAACnC,aAAqB;IACvD,OAAO;MACLA,aAAa;MACbY,UAAU,EAAE,EAAE;MACdwB,eAAe,EAAE,EAAE;MACnBC,uBAAuB,EAAE;KAC1B;EACH;EAEA;;;;;EAKAC,qCAAqCA,CAACtC,aAAqB;IACzD,OAAO;MACLA,aAAa;MACbuC,aAAa,EAAE,CAAC;MAChBC,kBAAkB,EAAE;QAClBC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;UACZC,MAAM,EAAE,CAAC;UACThB,QAAQ,EAAE;SACX;QACDiB,WAAW,EAAE,IAAIxC,IAAI,EAAE,CAACC,WAAW;;KAEtC;EACH;;;uBArPW9B,cAAc,EAAAsE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAd3E,cAAc;MAAA4E,OAAA,EAAd5E,cAAc,CAAA6E,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}