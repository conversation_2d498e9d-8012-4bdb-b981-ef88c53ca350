import { FlightBaggageGetOption, FlightClassType, LocationType, ProductType } from './enums.model';

export interface OneWayRequest {
  ProductType: ProductType;
  ServiceTypes: string[];
  CheckIn: string;
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
  Passengers: Passenger[];
  showOnlyNonStopFlight: boolean;
  additionalParameters?: AdditionalParameters;
  acceptPendingProviders: boolean;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  calculateFlightFees: boolean;
  flightClasses: FlightClassType[];
  Culture: string;
  Currency: string;
}

export interface Location {
  id: string;
  type: LocationType;
}

export interface Passenger {
  type: number;
  count: number;
}

export interface AdditionalParameters {
  getOptionsParameters?: GetOptionsParameters;
  CorporateCodes?: CorporateCode[];
}

export interface GetOptionsParameters {
  flightBaggageGetOption: FlightBaggageGetOption;
}

export interface CorporateCode {
  Code: string;
  Rule: Rule;
}

export interface Rule {
  Airline: string;
  Supplier: string;
}

// Modèle pour les emplacements (utilisé pour l'autocomplétion)
export interface LocationOption {
  id: string;
  name: string;
  type: LocationType;
  code?: string;
  country?: string;
  city?: string;
}
