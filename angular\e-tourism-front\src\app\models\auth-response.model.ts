export interface AuthResponse {
  header: {
    requestId: string;
    success: boolean;
    responseTime: string;
    messages: Message[];
  };
  body: {
    token: string;
    expiresOn: string;
    tokenId: number;
    userInfo: UserInfo;
    loggedInWithMasterKey: boolean;
  };
}

export interface Message {
  id: number;
  code: string;
  messageType: number;
  message: string;
}

export interface UserInfo {
  code: string;
  name: string;
  agency: any;
  office: any;
  operator: any;
  market: any;
  mainAgency: any;
  webSiteId: number;
  marketWebSiteId: number;
  allowChangePassword: boolean;
  allowCreateNewUser: boolean;
  allowCreateAgency: boolean;
  allowMakeReservation: boolean;
  allowEditReservation: boolean;
  allowCancelReservation: boolean;
  allowB2BUpdate: boolean;
  mobile: string;
  email: string;
  allowApiAccess: boolean;
  lastAccessDate: string;
  zohoTicketUpdate: boolean;
  id: string;
}
