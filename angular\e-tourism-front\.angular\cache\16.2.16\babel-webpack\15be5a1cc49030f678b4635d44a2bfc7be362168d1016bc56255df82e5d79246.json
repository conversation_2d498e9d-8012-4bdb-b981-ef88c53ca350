{"ast": null, "code": "import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/card\";\nexport let AccueilComponent = /*#__PURE__*/(() => {\n  class AccueilComponent {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n      this.userName = '';\n      // Pour l'animation séquentielle des cartes\n      this.featureCardDelay = [0, 100, 200, 300];\n    }\n    ngOnInit() {\n      const userInfo = this.authService.getUserInfo();\n      if (userInfo) {\n        this.userName = userInfo.name || 'User';\n      }\n    }\n    navigateToSearchPrice() {\n      this.router.navigate(['/search-price']);\n    }\n    static {\n      this.ɵfac = function AccueilComponent_Factory(t) {\n        return new (t || AccueilComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AccueilComponent,\n        selectors: [[\"app-accueil\"]],\n        decls: 61,\n        vars: 0,\n        consts: [[1, \"accueil-container\"], [1, \"hero-section\"], [1, \"hero-content\", \"animate-fade-in\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [\"mat-flat-button\", \"\", \"color\", \"accent\", 1, \"hero-cta\", 3, \"click\"], [1, \"hero-image\"], [\"src\", \"assets/images/travel-illustration.svg\", \"alt\", \"Travel illustration\", 1, \"animate-slide-up\"], [1, \"features-section\"], [1, \"section-title\"], [1, \"features-grid\"], [1, \"feature-card\", \"animate-fade-in\"], [1, \"feature-icon\"], [1, \"feature-title\"], [1, \"feature-description\"], [1, \"cta-section\"], [1, \"cta-content\"], [1, \"cta-title\"], [1, \"cta-description\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", 1, \"cta-button\", 3, \"click\"]],\n        template: function AccueilComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n            i0.ɵɵtext(4, \"Discover the World with E-Tourism\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\", 4);\n            i0.ɵɵtext(6, \"Find and book your perfect flights for your next adventure\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function AccueilComponent_Template_button_click_7_listener() {\n              return ctx.navigateToSearchPrice();\n            });\n            i0.ɵɵelementStart(8, \"mat-icon\");\n            i0.ɵɵtext(9, \"flight_takeoff\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(10, \" Book a Flight \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 6);\n            i0.ɵɵelement(12, \"img\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 8)(14, \"h2\", 9);\n            i0.ɵɵtext(15, \"Why Choose Us\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 10)(17, \"mat-card\", 11)(18, \"mat-card-content\")(19, \"div\", 12)(20, \"mat-icon\");\n            i0.ɵɵtext(21, \"search\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"h3\", 13);\n            i0.ɵɵtext(23, \"Easy Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"p\", 14);\n            i0.ɵɵtext(25, \"Find flights quickly with our powerful search engine\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(26, \"mat-card\", 11)(27, \"mat-card-content\")(28, \"div\", 12)(29, \"mat-icon\");\n            i0.ɵɵtext(30, \"compare_arrows\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"h3\", 13);\n            i0.ɵɵtext(32, \"Compare Options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"p\", 14);\n            i0.ɵɵtext(34, \"Compare different flight options to find the best deal\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(35, \"mat-card\", 11)(36, \"mat-card-content\")(37, \"div\", 12)(38, \"mat-icon\");\n            i0.ɵɵtext(39, \"credit_card\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"h3\", 13);\n            i0.ɵɵtext(41, \"Secure Booking\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"p\", 14);\n            i0.ɵɵtext(43, \"Book your flights securely with our trusted platform\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(44, \"mat-card\", 11)(45, \"mat-card-content\")(46, \"div\", 12)(47, \"mat-icon\");\n            i0.ɵɵtext(48, \"support_agent\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"h3\", 13);\n            i0.ɵɵtext(50, \"24/7 Support\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"p\", 14);\n            i0.ɵɵtext(52, \"Get assistance anytime with our dedicated support team\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(53, \"div\", 15)(54, \"div\", 16)(55, \"h2\", 17);\n            i0.ɵɵtext(56, \"Ready to Start Your Journey?\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"p\", 18);\n            i0.ɵɵtext(58, \"Book your flight now and embark on your next adventure\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function AccueilComponent_Template_button_click_59_listener() {\n              return ctx.navigateToSearchPrice();\n            });\n            i0.ɵɵtext(60, \" Search Flights \");\n            i0.ɵɵelementEnd()()()();\n          }\n        },\n        dependencies: [i3.MatButton, i4.MatIcon, i5.MatCard, i5.MatCardContent],\n        styles: [\".accueil-container[_ngcontent-%COMP%]{width:100%}.hero-section[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;padding:var(--spacing-xl) 0;background:linear-gradient(135deg,rgba(var(--primary-color-rgb),.05) 0%,rgba(var(--primary-color-rgb),.1) 100%);border-radius:var(--border-radius-large);overflow:hidden;margin-bottom:var(--spacing-xxl)}.hero-content[_ngcontent-%COMP%]{flex:1;padding:0 var(--spacing-xl)}.hero-title[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;color:var(--primary-dark);margin-bottom:var(--spacing-md);line-height:1.2}.hero-subtitle[_ngcontent-%COMP%]{font-size:1.25rem;color:var(--text-secondary);margin-bottom:var(--spacing-lg);max-width:600px}.hero-cta[_ngcontent-%COMP%]{padding:var(--spacing-sm) var(--spacing-lg);font-size:1rem;font-weight:500;display:flex;align-items:center;gap:var(--spacing-sm);border-radius:var(--border-radius-medium);transition:transform var(--transition-fast)}.hero-cta[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.hero-image[_ngcontent-%COMP%]{flex:1;display:flex;justify-content:center;align-items:center;padding:var(--spacing-lg)}.hero-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;height:auto;max-height:400px}.features-section[_ngcontent-%COMP%]{padding:var(--spacing-xl) 0;margin-bottom:var(--spacing-xxl)}.section-title[_ngcontent-%COMP%]{text-align:center;font-size:2rem;font-weight:600;color:var(--text-primary);margin-bottom:var(--spacing-xl)}.features-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:var(--spacing-lg)}.feature-card[_ngcontent-%COMP%]{border-radius:var(--border-radius-medium);box-shadow:var(--elevation-2);transition:transform var(--transition-medium),box-shadow var(--transition-medium);overflow:hidden;height:100%}.feature-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:var(--elevation-4)}.feature-icon[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;width:60px;height:60px;background-color:rgba(var(--primary-color-rgb),.1);border-radius:50%;margin:0 auto var(--spacing-md)}.feature-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:30px;height:30px;width:30px;color:var(--primary-color)}.feature-title[_ngcontent-%COMP%]{text-align:center;font-size:1.25rem;font-weight:500;color:var(--text-primary);margin-bottom:var(--spacing-sm)}.feature-description[_ngcontent-%COMP%]{text-align:center;color:var(--text-secondary);font-size:1rem;line-height:1.5}.cta-section[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-color) 0%,var(--primary-dark) 100%);border-radius:var(--border-radius-large);padding:var(--spacing-xl);margin-bottom:var(--spacing-xxl);color:#fff;text-align:center}.cta-content[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}.cta-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:600;margin-bottom:var(--spacing-md)}.cta-description[_ngcontent-%COMP%]{font-size:1.1rem;margin-bottom:var(--spacing-lg);opacity:.9}.cta-button[_ngcontent-%COMP%]{padding:var(--spacing-sm) var(--spacing-xl);font-size:1rem;font-weight:500;background-color:#fff;color:var(--primary-color)}@media (max-width: 992px){.hero-section[_ngcontent-%COMP%]{flex-direction:column;padding:var(--spacing-lg) 0}.hero-content[_ngcontent-%COMP%]{text-align:center;padding:var(--spacing-lg);order:2}.hero-image[_ngcontent-%COMP%]{order:1;margin-bottom:var(--spacing-lg)}.hero-title[_ngcontent-%COMP%]{font-size:2rem}.hero-subtitle[_ngcontent-%COMP%]{font-size:1.1rem;margin-left:auto;margin-right:auto}.hero-cta[_ngcontent-%COMP%]{margin:0 auto}.section-title[_ngcontent-%COMP%], .cta-title[_ngcontent-%COMP%]{font-size:1.75rem}.cta-description[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 576px){.hero-title[_ngcontent-%COMP%]{font-size:1.75rem}.hero-subtitle[_ngcontent-%COMP%]{font-size:1rem}.features-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.cta-section[_ngcontent-%COMP%]{padding:var(--spacing-lg)}.cta-title[_ngcontent-%COMP%]{font-size:1.5rem}}\"],\n        data: {\n          animation: [trigger('fadeIn', [transition(':enter', [style({\n            opacity: 0\n          }), animate('500ms ease-in', style({\n            opacity: 1\n          }))])]), trigger('slideUp', [transition(':enter', [style({\n            transform: 'translateY(20px)',\n            opacity: 0\n          }), animate('500ms ease-out', style({\n            transform: 'translateY(0)',\n            opacity: 1\n          }))])])]\n        }\n      });\n    }\n  }\n  return AccueilComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}