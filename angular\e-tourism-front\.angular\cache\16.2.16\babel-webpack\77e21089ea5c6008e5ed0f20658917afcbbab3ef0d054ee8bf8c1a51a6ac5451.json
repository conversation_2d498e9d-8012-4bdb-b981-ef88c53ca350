{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nexport let AuthInterceptor = /*#__PURE__*/(() => {\n  class AuthInterceptor {\n    constructor(authService) {\n      this.authService = authService;\n    }\n    intercept(request, next) {\n      const token = this.authService.getToken();\n      if (token) {\n        // Exclure les requêtes d'authentification\n        if (!request.url.includes('/auth/login')) {\n          request = request.clone({\n            setHeaders: {\n              Authorization: `Bearer ${token}`\n            }\n          });\n        }\n      }\n      return next.handle(request);\n    }\n    static {\n      this.ɵfac = function AuthInterceptor_Factory(t) {\n        return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthInterceptor,\n        factory: AuthInterceptor.ɵfac\n      });\n    }\n  }\n  return AuthInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}