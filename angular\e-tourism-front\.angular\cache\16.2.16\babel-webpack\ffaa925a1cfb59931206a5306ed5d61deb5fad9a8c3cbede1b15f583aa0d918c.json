{"ast": null, "code": "import { PassengerType } from '../models/enums.model';\nimport * as i0 from \"@angular/core\";\nexport class SharedDataService {\n  constructor() {\n    // Stockage des informations de passagers\n    this.passengerCounts = {\n      [PassengerType.Adult]: 1,\n      [PassengerType.Child]: 0,\n      [PassengerType.Infant]: 0\n    };\n  }\n  /**\n   * Définir le nombre de passagers par type\n   * @param passengerCounts Objet contenant le nombre de passagers par type\n   */\n  setPassengerCounts(passengerCounts) {\n    this.passengerCounts = {\n      ...passengerCounts\n    };\n  }\n  /**\n   * Obtenir le nombre de passagers par type\n   * @returns Objet contenant le nombre de passagers par type\n   */\n  getPassengerCounts() {\n    return {\n      ...this.passengerCounts\n    };\n  }\n  /**\n   * Obtenir le nombre total de passagers\n   * @returns Nombre total de passagers\n   */\n  getTotalPassengers() {\n    return Object.values(this.passengerCounts).reduce((sum, count) => sum + count, 0);\n  }\n  /**\n   * Obtenir un tableau de passagers pour l'API\n   * @returns Tableau de passagers pour l'API\n   */\n  getPassengersArray() {\n    const passengers = [];\n    // Ajouter chaque type de passager avec un nombre > 0\n    Object.entries(this.passengerCounts).forEach(([type, count]) => {\n      if (count > 0) {\n        passengers.push({\n          type: parseInt(type),\n          count: count\n        });\n      }\n    });\n    // S'assurer qu'au moins un passager est inclus\n    if (passengers.length === 0) {\n      passengers.push({\n        type: PassengerType.Adult,\n        count: 1\n      });\n    }\n    return passengers;\n  }\n  static {\n    this.ɵfac = function SharedDataService_Factory(t) {\n      return new (t || SharedDataService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SharedDataService,\n      factory: SharedDataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["PassengerType", "SharedDataService", "constructor", "passengerCounts", "Adult", "Child", "Infant", "set<PERSON>assengerCounts", "getPassengerCounts", "getTotalPassengers", "Object", "values", "reduce", "sum", "count", "getPassengersArray", "passengers", "entries", "for<PERSON>ach", "type", "push", "parseInt", "length", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\shared-data.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { PassengerType } from '../models/enums.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SharedDataService {\n  // Stockage des informations de passagers\n  private passengerCounts: { [key: number]: number } = {\n    [PassengerType.Adult]: 1,\n    [PassengerType.Child]: 0,\n    [PassengerType.Infant]: 0\n  };\n\n  constructor() { }\n\n  /**\n   * Définir le nombre de passagers par type\n   * @param passengerCounts Objet contenant le nombre de passagers par type\n   */\n  setPassengerCounts(passengerCounts: { [key: number]: number }): void {\n    this.passengerCounts = { ...passengerCounts };\n  }\n\n  /**\n   * Obtenir le nombre de passagers par type\n   * @returns Objet contenant le nombre de passagers par type\n   */\n  getPassengerCounts(): { [key: number]: number } {\n    return { ...this.passengerCounts };\n  }\n\n  /**\n   * Obtenir le nombre total de passagers\n   * @returns Nombre total de passagers\n   */\n  getTotalPassengers(): number {\n    return Object.values(this.passengerCounts).reduce((sum, count) => sum + count, 0);\n  }\n\n  /**\n   * Obtenir un tableau de passagers pour l'API\n   * @returns Tableau de passagers pour l'API\n   */\n  getPassengersArray(): any[] {\n    const passengers = [];\n\n    // Ajouter chaque type de passager avec un nombre > 0\n    Object.entries(this.passengerCounts).forEach(([type, count]) => {\n      if (count > 0) {\n        passengers.push({\n          type: parseInt(type),\n          count: count\n        });\n      }\n    });\n\n    // S'assurer qu'au moins un passager est inclus\n    if (passengers.length === 0) {\n      passengers.push({\n        type: PassengerType.Adult,\n        count: 1\n      });\n    }\n\n    return passengers;\n  }\n}\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,uBAAuB;;AAKrD,OAAM,MAAOC,iBAAiB;EAQ5BC,YAAA;IAPA;IACQ,KAAAC,eAAe,GAA8B;MACnD,CAACH,aAAa,CAACI,KAAK,GAAG,CAAC;MACxB,CAACJ,aAAa,CAACK,KAAK,GAAG,CAAC;MACxB,CAACL,aAAa,CAACM,MAAM,GAAG;KACzB;EAEe;EAEhB;;;;EAIAC,kBAAkBA,CAACJ,eAA0C;IAC3D,IAAI,CAACA,eAAe,GAAG;MAAE,GAAGA;IAAe,CAAE;EAC/C;EAEA;;;;EAIAK,kBAAkBA,CAAA;IAChB,OAAO;MAAE,GAAG,IAAI,CAACL;IAAe,CAAE;EACpC;EAEA;;;;EAIAM,kBAAkBA,CAAA;IAChB,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACR,eAAe,CAAC,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;EACnF;EAEA;;;;EAIAC,kBAAkBA,CAAA;IAChB,MAAMC,UAAU,GAAG,EAAE;IAErB;IACAN,MAAM,CAACO,OAAO,CAAC,IAAI,CAACd,eAAe,CAAC,CAACe,OAAO,CAAC,CAAC,CAACC,IAAI,EAAEL,KAAK,CAAC,KAAI;MAC7D,IAAIA,KAAK,GAAG,CAAC,EAAE;QACbE,UAAU,CAACI,IAAI,CAAC;UACdD,IAAI,EAAEE,QAAQ,CAACF,IAAI,CAAC;UACpBL,KAAK,EAAEA;SACR,CAAC;;IAEN,CAAC,CAAC;IAEF;IACA,IAAIE,UAAU,CAACM,MAAM,KAAK,CAAC,EAAE;MAC3BN,UAAU,CAACI,IAAI,CAAC;QACdD,IAAI,EAAEnB,aAAa,CAACI,KAAK;QACzBU,KAAK,EAAE;OACR,CAAC;;IAGJ,OAAOE,UAAU;EACnB;;;uBA5DWf,iBAAiB;IAAA;EAAA;;;aAAjBA,iBAAiB;MAAAsB,OAAA,EAAjBtB,iBAAiB,CAAAuB,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}