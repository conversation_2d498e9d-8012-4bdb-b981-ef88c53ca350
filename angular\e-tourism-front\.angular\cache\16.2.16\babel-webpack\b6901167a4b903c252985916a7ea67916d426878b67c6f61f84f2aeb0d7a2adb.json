{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/product.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/autocomplete\";\nimport * as i6 from \"@angular/material/core\";\nfunction SearchPriceComponent_mat_option_40_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 117);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r8.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_40_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 118);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r8.city);\n  }\n}\nconst _c0 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"fa-flag\": a0,\n    \"fa-city\": a1,\n    \"fa-building\": a2,\n    \"fa-home\": a3,\n    \"fa-plane\": a4\n  };\n};\nfunction SearchPriceComponent_mat_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33)(1, \"div\", 110)(2, \"div\", 111);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 112);\n    i0.ɵɵtemplate(5, SearchPriceComponent_mat_option_40_span_5_Template, 2, 1, \"span\", 113);\n    i0.ɵɵtemplate(6, SearchPriceComponent_mat_option_40_span_6_Template, 2, 1, \"span\", 114);\n    i0.ɵɵelementStart(7, \"span\", 115);\n    i0.ɵɵelement(8, \"i\", 116);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r8.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r8.type === 5 && location_r8.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r8.type === 1, location_r8.type === 2, location_r8.type === 3, location_r8.type === 4, location_r8.type === 5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r8.type === 1 ? \"Pays\" : location_r8.type === 2 ? \"Ville\" : location_r8.type === 3 ? \"Ville\" : location_r8.type === 4 ? \"Village\" : location_r8.type === 5 ? \"A\\u00E9roport\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_mat_option_61_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 117);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r13.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_61_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 118);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r13.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33)(1, \"div\", 110)(2, \"div\", 111);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 112);\n    i0.ɵɵtemplate(5, SearchPriceComponent_mat_option_61_span_5_Template, 2, 1, \"span\", 113);\n    i0.ɵɵtemplate(6, SearchPriceComponent_mat_option_61_span_6_Template, 2, 1, \"span\", 114);\n    i0.ɵɵelementStart(7, \"span\", 115);\n    i0.ɵɵelement(8, \"i\", 116);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r13);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r13.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r13.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r13.type === 5 && location_r13.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r13.type === 1, location_r13.type === 2, location_r13.type === 3, location_r13.type === 4, location_r13.type === 5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r13.type === 1 ? \"Pays\" : location_r13.type === 2 ? \"Ville\" : location_r13.type === 3 ? \"Ville\" : location_r13.type === 4 ? \"Village\" : location_r13.type === 5 ? \"A\\u00E9roport\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_option_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", flightClass_r18.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(flightClass_r18.label);\n  }\n}\nfunction SearchPriceComponent_span_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Rechercher\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119);\n    i0.ɵɵelement(1, \"div\", 120);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_203_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125)(1, \"div\", 126)(2, \"div\", 127);\n    i0.ɵɵelement(3, \"i\", 128)(4, \"div\", 129)(5, \"div\", 129)(6, \"div\", 129);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Searching for the best flights...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SearchPriceComponent_div_203_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 130)(1, \"div\", 131);\n    i0.ɵɵelement(2, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Oops! Something went wrong\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 133);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_203_div_2_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.onSearch());\n    });\n    i0.ɵɵelement(8, \"i\", 135);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r20.errorMessage);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No flights found for your search. Please modify your criteria.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"span\", 143);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" flights found \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.searchResults.length);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"div\", 145);\n    i0.ɵɵelement(2, \"i\", 146);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Sort by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"select\", 147)(6, \"option\", 148);\n    i0.ɵɵtext(7, \"Price (lowest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"option\", 149);\n    i0.ɵɵtext(9, \"Duration (shortest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 150);\n    i0.ɵɵtext(11, \"Departure (earliest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 151);\n    i0.ɵɵtext(13, \"Arrival (earliest first)\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 202);\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", flight_r29.items[0].airline.thumbnailFull, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 203);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 204);\n    i0.ɵɵelement(1, \"i\", 205);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r29.items && flight_r29.items[0] && flight_r29.items[0].flightProvider && flight_r29.items[0].flightProvider.displayName || flight_r29.items && flight_r29.items[0] && flight_r29.items[0].flightProvider && flight_r29.items[0].flightProvider.name || \"Provider \" + flight_r29.provider, \" \");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 206);\n    i0.ɵɵelement(1, \"i\", 207);\n    i0.ɵɵtext(2, \" Direct \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 206);\n    i0.ɵɵelement(1, \"i\", 208);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r29.items[0].flightClass.name, \" \");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 209);\n    i0.ɵɵelement(1, \"i\", 210);\n    i0.ɵɵtext(2, \" Branded Fare \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"fa-check-circle\": a0,\n    \"fa-exclamation-triangle\": a1\n  };\n};\nfunction SearchPriceComponent_div_203_div_3_div_9_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 211);\n    i0.ɵɵelement(1, \"i\", 116);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    const ctx_r36 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c1, ctx_r36.isFlightAvailable(flight_r29), !ctx_r36.isFlightAvailable(flight_r29)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r36.isFlightAvailable(flight_r29) ? \"Available\" : \"Not available\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 212);\n    i0.ɵɵelement(1, \"i\", 185);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    const ctx_r37 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Expires: \", ctx_r37.formatExpirationDate(flight_r29.offers[0].expiresOn), \" \");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 213)(1, \"span\", 214);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r29.items[0].stopCount);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" stop\", flight_r29.items[0].stopCount > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 215);\n    i0.ɵɵtext(1, \" Direct flight \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_div_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 225);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r57 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r57.weight, \" kg\");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 225);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r57 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r57.piece, \" piece(s)\");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 221);\n    i0.ɵɵelement(1, \"i\", 94);\n    i0.ɵɵelementStart(2, \"div\", 222)(3, \"span\", 223);\n    i0.ɵɵtext(4, \"Checked Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_div_1_span_5_Template, 2, 1, \"span\", 224);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_div_1_span_6_Template, 2, 1, \"span\", 224);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const baggage_r57 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", baggage_r57.weight > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", baggage_r57.piece > 0);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_div_1_Template, 7, 2, \"div\", 220);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r52 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, ctx_r52.filterBaggageByType(flight_r29.items[0].baggageInformations, 2), 0, 2));\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_div_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 225);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r64 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r64.piece, \" piece(s)\");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 225);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r64 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", baggage_r64.weight, \" kg\");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 227);\n    i0.ɵɵelement(1, \"i\", 228);\n    i0.ɵɵelementStart(2, \"div\", 222)(3, \"span\", 223);\n    i0.ɵɵtext(4, \"Cabin Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_div_1_span_5_Template, 2, 1, \"span\", 224);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_div_1_span_6_Template, 2, 1, \"span\", 224);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const baggage_r64 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", baggage_r64.piece > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", baggage_r64.weight > 0);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_div_1_Template, 7, 2, \"div\", 226);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r53 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, ctx_r53.filterBaggageByType(flight_r29.items[0].baggageInformations, 1), 0, 1));\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_55_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 227);\n    i0.ɵɵelement(1, \"i\", 228);\n    i0.ɵɵelementStart(2, \"div\", 222)(3, \"span\", 223);\n    i0.ɵɵtext(4, \"Cabin Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 225);\n    i0.ɵɵtext(6, \"Included\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_55_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 229)(1, \"span\");\n    i0.ɵɵtext(2, \"No detailed baggage information available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 216)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 94);\n    i0.ɵɵtext(3, \" Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 217);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_Template, 3, 5, \"ng-container\", 67);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_Template, 3, 5, \"ng-container\", 67);\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_203_div_3_div_9_div_55_div_7_Template, 7, 0, \"div\", 218);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, SearchPriceComponent_div_203_div_3_div_9_div_55_div_8_Template, 3, 0, \"div\", 219);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.items[0].baggageInformations && flight_r29.items[0].baggageInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.items[0].baggageInformations && flight_r29.items[0].baggageInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !flight_r29.items[0].baggageInformations || flight_r29.items[0].baggageInformations.length === 0 || ctx_r40.filterBaggageByType(flight_r29.items[0].baggageInformations, 1).length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !flight_r29.items[0].baggageInformations || flight_r29.items[0].baggageInformations.length === 0);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_56_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 229)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const service_r72 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(service_r72.name || \"Service\");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 216)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 230);\n    i0.ɵɵtext(3, \" Services\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_203_div_3_div_9_div_56_div_4_Template, 3, 1, \"div\", 231);\n    i0.ɵɵpipe(5, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(5, 1, flight_r29.items[0].services, 0, 3));\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_57_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 229)(1, \"span\");\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"small\", 232);\n    i0.ɵɵtext(5, \"(from API response)\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(flight_r29.offers[0].reservableInfo.reservable ? \"fas fa-check-circle text-success\" : \"fas fa-times-circle text-danger\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r29.offers[0].reservableInfo.reservable ? \"Reservable\" : \"Not reservable\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 216)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 205);\n    i0.ɵɵtext(3, \" Offer Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_203_div_3_div_9_div_57_div_4_Template, 6, 3, \"div\", 219);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.offers[0].reservableInfo);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_63_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 241)(1, \"span\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 242);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r80 = ctx.$implicit;\n    const ctx_r78 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r78.getPassengerTypeName(item_r80.passengerType), \" (x\", item_r80.passengerCount, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 3, item_r80.price.amount, item_r80.price.currency));\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_63_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 243)(1, \"span\", 244);\n    i0.ɵɵtext(2, \"Service Fee\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 245);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, flight_r29.offers[0].serviceFee.amount, flight_r29.offers[0].serviceFee.currency));\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_63_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 235)(1, \"h4\");\n    i0.ɵɵtext(2, \"Price Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_203_div_3_div_9_div_63_div_1_div_3_Template, 6, 6, \"div\", 236);\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_203_div_3_div_9_div_63_div_1_div_4_Template, 6, 4, \"div\", 237);\n    i0.ɵɵelementStart(5, \"div\", 238)(6, \"span\", 239);\n    i0.ɵɵtext(7, \"Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 240);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", flight_r29.offers[0].priceBreakDown.items);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.offers[0].serviceFee);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 3, flight_r29.offers[0].price.amount, flight_r29.offers[0].price.currency));\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 233);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_203_div_3_div_9_div_63_div_1_Template, 11, 6, \"div\", 234);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.offers[0].priceBreakDown && flight_r29.offers[0].priceBreakDown.items);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_64_div_6_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 262);\n    i0.ɵɵelement(1, \"i\", 263);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r88 = i0.ɵɵnextContext();\n    const segment_r85 = ctx_r88.$implicit;\n    const i_r86 = ctx_r88.index;\n    const flight_r29 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r87 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r87.calculateLayoverTime(segment_r85, flight_r29.items[0].segments[i_r86 + 1]), \" layover in \", segment_r85.arrival && segment_r85.arrival.city && segment_r85.arrival.city.name || \"connecting city\", \"\");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_64_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 252)(1, \"div\", 253)(2, \"span\", 254);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 255);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 256);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 257)(9, \"div\", 258)(10, \"div\", 173);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 174)(13, \"span\", 175);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 176);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 259)(18, \"div\", 178);\n    i0.ɵɵelement(19, \"span\", 179);\n    i0.ɵɵelementStart(20, \"div\", 180);\n    i0.ɵɵelement(21, \"span\", 181);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"span\", 183);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 184);\n    i0.ɵɵelement(24, \"i\", 185);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 260)(27, \"div\", 173);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 174)(30, \"span\", 175);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 176);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(34, SearchPriceComponent_div_203_div_3_div_9_div_64_div_6_div_34_Template, 4, 2, \"div\", 261);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r85 = ctx.$implicit;\n    const i_r86 = ctx.index;\n    const flight_r29 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r84 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Segment \", i_r86 + 1, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r85.airline && segment_r85.airline.name || \"Airline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r85.flightNo);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(segment_r85.departure ? ctx_r84.formatDate(segment_r85.departure.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r85.departure && segment_r85.departure.airport && segment_r85.departure.airport.code || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r85.departure && segment_r85.departure.city && segment_r85.departure.city.name || \"N/A\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r84.formatDuration(segment_r85.duration), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r85.arrival ? ctx_r84.formatDate(segment_r85.arrival.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r85.arrival && segment_r85.arrival.airport && segment_r85.arrival.airport.code || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r85.arrival && segment_r85.arrival.city && segment_r85.arrival.city.name || \"N/A\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r86 < flight_r29.items[0].segments.length - 1);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 246)(1, \"details\", 247)(2, \"summary\", 248);\n    i0.ɵɵelement(3, \"i\", 249);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 250);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_203_div_3_div_9_div_64_div_6_Template, 35, 11, \"div\", 251);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Flight Segments (\", flight_r29.items[0].segments.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", flight_r29.items[0].segments);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_65_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 270);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r29.offers[0].brandedFare.description, \" \");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_65_div_7_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 276);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r96 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", feature_r96.explanations[0].text, \" \");\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_65_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 273)(1, \"div\", 274);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_203_div_3_div_9_div_65_div_7_div_3_div_3_Template, 2, 1, \"div\", 275);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r96 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r96.commercialName || \"Feature\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", feature_r96.explanations && feature_r96.explanations.length > 0);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_65_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 271)(1, \"h4\");\n    i0.ɵɵtext(2, \"Features\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_203_div_3_div_9_div_65_div_7_div_3_Template, 4, 2, \"div\", 272);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", flight_r29.offers[0].brandedFare.features);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 264)(1, \"details\", 265)(2, \"summary\", 266);\n    i0.ɵɵelement(3, \"i\", 210);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 267);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_203_div_3_div_9_div_65_div_6_Template, 2, 1, \"div\", 268);\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_203_div_3_div_9_div_65_div_7_Template, 4, 1, \"div\", 269);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", flight_r29.offers[0].brandedFare.name || \"Branded Fare\", \" Details \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.offers[0].brandedFare.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.offers[0].brandedFare.features && flight_r29.offers[0].brandedFare.features.length > 0);\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r102 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 152)(1, \"div\", 153)(2, \"div\", 154)(3, \"div\", 155);\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_203_div_3_div_9_img_4_Template, 1, 1, \"img\", 156);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_203_div_3_div_9_i_5_Template, 1, 0, \"i\", 157);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 158)(7, \"span\", 159);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 160);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, SearchPriceComponent_div_203_div_3_div_9_span_11_Template, 3, 1, \"span\", 161);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 162);\n    i0.ɵɵtemplate(13, SearchPriceComponent_div_203_div_3_div_9_span_13_Template, 3, 0, \"span\", 163);\n    i0.ɵɵtemplate(14, SearchPriceComponent_div_203_div_3_div_9_span_14_Template, 3, 1, \"span\", 163);\n    i0.ɵɵtemplate(15, SearchPriceComponent_div_203_div_3_div_9_span_15_Template, 3, 0, \"span\", 164);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 165)(17, \"span\", 166);\n    i0.ɵɵtext(18, \"Price per person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 167);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, SearchPriceComponent_div_203_div_3_div_9_span_21_Template, 3, 5, \"span\", 168);\n    i0.ɵɵtemplate(22, SearchPriceComponent_div_203_div_3_div_9_span_22_Template, 3, 1, \"span\", 169);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 170)(24, \"div\", 171)(25, \"div\", 172)(26, \"div\", 173);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 174)(29, \"span\", 175);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 176);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 177)(34, \"div\", 178);\n    i0.ɵɵelement(35, \"span\", 179);\n    i0.ɵɵelementStart(36, \"div\", 180);\n    i0.ɵɵelement(37, \"span\", 181);\n    i0.ɵɵelementStart(38, \"span\", 182);\n    i0.ɵɵelement(39, \"i\", 128);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(40, \"span\", 183);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 184);\n    i0.ɵɵelement(42, \"i\", 185);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(44, SearchPriceComponent_div_203_div_3_div_9_div_44_Template, 4, 2, \"div\", 186);\n    i0.ɵɵtemplate(45, SearchPriceComponent_div_203_div_3_div_9_div_45_Template, 2, 0, \"div\", 187);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 188)(47, \"div\", 173);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 174)(50, \"span\", 175);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"span\", 176);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(54, \"div\", 189);\n    i0.ɵɵtemplate(55, SearchPriceComponent_div_203_div_3_div_9_div_55_Template, 9, 4, \"div\", 190);\n    i0.ɵɵtemplate(56, SearchPriceComponent_div_203_div_3_div_9_div_56_Template, 6, 5, \"div\", 190);\n    i0.ɵɵtemplate(57, SearchPriceComponent_div_203_div_3_div_9_div_57_Template, 5, 1, \"div\", 190);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 191)(59, \"details\", 192)(60, \"summary\", 193);\n    i0.ɵɵelement(61, \"i\", 89);\n    i0.ɵɵtext(62, \" Price Breakdown \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(63, SearchPriceComponent_div_203_div_3_div_9_div_63_Template, 2, 1, \"div\", 194);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(64, SearchPriceComponent_div_203_div_3_div_9_div_64_Template, 7, 2, \"div\", 195);\n    i0.ɵɵtemplate(65, SearchPriceComponent_div_203_div_3_div_9_div_65_Template, 8, 3, \"div\", 196);\n    i0.ɵɵelementStart(66, \"div\", 197)(67, \"button\", 198);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_203_div_3_div_9_Template_button_click_67_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r102);\n      const flight_r29 = restoredCtx.$implicit;\n      const ctx_r101 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r101.showAllDetails(flight_r29));\n    });\n    i0.ɵɵelement(68, \"i\", 199);\n    i0.ɵɵtext(69, \" View All Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"button\", 200);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_203_div_3_div_9_Template_button_click_70_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r102);\n      const flight_r29 = restoredCtx.$implicit;\n      const ctx_r103 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r103.selectThisFlight(flight_r29));\n    });\n    i0.ɵɵelement(71, \"i\", 201);\n    i0.ɵɵtext(72, \" Select This Flight \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r29 = ctx.$implicit;\n    const ctx_r27 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"unavailable\", !ctx_r27.isFlightAvailable(flight_r29));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.items && flight_r29.items[0] && flight_r29.items[0].airline && flight_r29.items[0].airline.thumbnailFull);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(flight_r29.items && flight_r29.items[0] && flight_r29.items[0].airline && flight_r29.items[0].airline.thumbnailFull));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r29.items && flight_r29.items[0] && flight_r29.items[0].airline ? flight_r29.items[0].airline.name : \"Airline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r29.items && flight_r29.items[0] ? flight_r29.items[0].flightNo : \"N/A\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.provider || flight_r29.items && flight_r29.items[0] && flight_r29.items[0].flightProvider);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.items && flight_r29.items[0] && flight_r29.items[0].stopCount === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.items && flight_r29.items[0] && flight_r29.items[0].flightClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.offers && flight_r29.offers[0] && flight_r29.offers[0].hasBrand);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r27.getMinPrice(flight_r29));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.offers && flight_r29.offers.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.offers && flight_r29.offers.length > 0 && flight_r29.offers[0].expiresOn);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(flight_r29.items && flight_r29.items[0] && flight_r29.items[0].departure ? ctx_r27.formatDate(flight_r29.items[0].departure.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r29.items && flight_r29.items[0] && flight_r29.items[0].departure && flight_r29.items[0].departure.airport ? flight_r29.items[0].departure.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r29.items && flight_r29.items[0] && flight_r29.items[0].departure && flight_r29.items[0].departure.city ? flight_r29.items[0].departure.city.name : \"N/A\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", flight_r29.items && flight_r29.items[0] ? ctx_r27.formatDuration(flight_r29.items[0].duration) : \"N/A\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.items && flight_r29.items[0] && flight_r29.items[0].stopCount > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.items && flight_r29.items[0] && flight_r29.items[0].stopCount === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r29.items && flight_r29.items[0] && flight_r29.items[0].arrival ? ctx_r27.formatDate(flight_r29.items[0].arrival.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r29.items && flight_r29.items[0] && flight_r29.items[0].arrival && flight_r29.items[0].arrival.airport ? flight_r29.items[0].arrival.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r29.items && flight_r29.items[0] && flight_r29.items[0].arrival && flight_r29.items[0].arrival.city ? flight_r29.items[0].arrival.city.name : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.items && flight_r29.items[0]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.items && flight_r29.items[0] && flight_r29.items[0].services && flight_r29.items[0].services.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.offers && flight_r29.offers.length > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.offers && flight_r29.offers[0]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.items && flight_r29.items[0] && flight_r29.items[0].segments && flight_r29.items[0].segments.length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r29.offers && flight_r29.offers[0] && flight_r29.offers[0].brandedFare);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !flight_r29.offers || flight_r29.offers.length === 0 || !ctx_r27.isFlightAvailable(flight_r29));\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 277)(1, \"div\", 278);\n    i0.ɵɵelement(2, \"i\", 279);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No flights found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"We couldn't find any flights matching your search criteria. Try adjusting your search parameters.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 280)(8, \"div\", 281);\n    i0.ɵɵelement(9, \"i\", 282);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Try different dates\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 281);\n    i0.ɵɵelement(13, \"i\", 283);\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Try nearby airports\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 281);\n    i0.ɵɵelement(17, \"i\", 128);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Include flights with stops\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SearchPriceComponent_div_203_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 136)(1, \"div\", 137)(2, \"div\", 138)(3, \"h3\");\n    i0.ɵɵtext(4, \"Flight Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_203_div_3_p_5_Template, 2, 0, \"p\", 67);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_203_div_3_p_6_Template, 4, 1, \"p\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_203_div_3_div_7_Template, 14, 0, \"div\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 140);\n    i0.ɵɵtemplate(9, SearchPriceComponent_div_203_div_3_div_9_Template, 73, 29, \"div\", 141);\n    i0.ɵɵtemplate(10, SearchPriceComponent_div_203_div_3_div_10_Template, 20, 0, \"div\", 142);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.searchResults.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r21.searchResults);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r21.isLoading && !ctx_r21.errorMessage && ctx_r21.searchResults.length === 0);\n  }\n}\nfunction SearchPriceComponent_div_203_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_203_div_1_Template, 9, 0, \"div\", 122);\n    i0.ɵɵtemplate(2, SearchPriceComponent_div_203_div_2_Template, 10, 1, \"div\", 123);\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_203_div_3_Template, 11, 5, \"div\", 124);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.isLoading && ctx_r7.errorMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.isLoading && !ctx_r7.errorMessage);\n  }\n}\nexport class SearchPriceComponent {\n  constructor(fb, productService, router) {\n    this.fb = fb;\n    this.productService = productService;\n    this.router = router;\n    this.departureLocations = [];\n    this.arrivalLocations = [];\n    this.isLoading = false;\n    this.searchResults = [];\n    this.hasSearched = false;\n    this.errorMessage = '';\n    this.lastSearchId = '';\n    this.isPassengerDropdownOpen = false;\n    // Passenger type options\n    this.passengerTypes = [{\n      value: PassengerType.Adult,\n      label: 'Adult'\n    }, {\n      value: PassengerType.Child,\n      label: 'Child'\n    }, {\n      value: PassengerType.Infant,\n      label: 'Infant'\n    }];\n    // Flight class options\n    this.flightClasses = [{\n      value: FlightClassType.PROMO,\n      label: 'Promo'\n    }, {\n      value: FlightClassType.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClassType.BUSINESS,\n      label: 'Business'\n    }];\n    this.closePassengerDropdown = () => {\n      this.isPassengerDropdownOpen = false;\n      document.removeEventListener('click', this.closePassengerDropdown);\n    };\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required],\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required],\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required],\n      // Options de vol\n      flightClass: [0, Validators.required],\n      nonStop: [false],\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n  ngOnInit() {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight) {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n    header.appendChild(logo);\n    header.appendChild(title);\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n        generalInfo.appendChild(airlineInfo);\n      }\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n      routeSection.appendChild(routeVisual);\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n          segmentsList.appendChild(segmentItem);\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n            segmentsList.appendChild(layover);\n          }\n        });\n        routeSection.appendChild(segmentsList);\n      }\n    }\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n          const baggageContainer = document.createElement('div');\n          baggageContainer.style.display = 'flex';\n          baggageContainer.style.flexDirection = 'column';\n          baggageContainer.style.gap = '10px';\n          baggageContainer.style.marginBottom = '15px';\n          // Filtrer et regrouper les bagages par type\n          const checkedBaggage = offer.baggageInformations.filter(b => b.baggageType === 2);\n          const cabinBaggage = offer.baggageInformations.filter(b => b.baggageType === 1);\n          const handBaggage = offer.baggageInformations.filter(b => b.baggageType === 3);\n          // Bagages en soute\n          if (checkedBaggage.length > 0) {\n            checkedBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#e7f5ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #c5e1f9';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-suitcase\" style=\"color: #4a6fa5; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Checked Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n          // Bagages cabine\n          if (cabinBaggage.length > 0) {\n            cabinBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#f3f0ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #e5dbff';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Cabin Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          } else {\n            // Ajouter un bagage cabine par défaut si aucun n'est spécifié\n            const baggageItem = document.createElement('div');\n            baggageItem.style.display = 'flex';\n            baggageItem.style.alignItems = 'center';\n            baggageItem.style.padding = '10px 15px';\n            baggageItem.style.backgroundColor = '#f3f0ff';\n            baggageItem.style.borderRadius = '6px';\n            baggageItem.style.border = '1px solid #e5dbff';\n            const baggageIcon = document.createElement('div');\n            baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n            const baggageInfo = document.createElement('div');\n            baggageInfo.style.display = 'flex';\n            baggageInfo.style.flexDirection = 'column';\n            const baggageType = document.createElement('div');\n            baggageType.textContent = 'Cabin Baggage';\n            baggageType.style.fontWeight = '600';\n            baggageType.style.fontSize = '14px';\n            const baggageDetails = document.createElement('div');\n            baggageDetails.style.fontSize = '12px';\n            baggageDetails.style.color = '#666';\n            baggageDetails.textContent = 'Included';\n            baggageInfo.appendChild(baggageType);\n            baggageInfo.appendChild(baggageDetails);\n            baggageItem.appendChild(baggageIcon);\n            baggageItem.appendChild(baggageInfo);\n            baggageContainer.appendChild(baggageItem);\n          }\n          // Bagages à main\n          if (handBaggage.length > 0) {\n            handBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#fff4e6';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #ffe8cc';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-shopping-bag\" style=\"color: #e8590c; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Hand Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n          offerItem.appendChild(baggageContainer);\n        }\n        offerItem.appendChild(offerDetails);\n        offersList.appendChild(offerItem);\n      });\n      offersSection.appendChild(offersList);\n    }\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n  // Méthode utilitaire pour créer une section\n  createSection(title, iconClass) {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n    return section;\n  }\n  // Méthode utilitaire pour créer une ligne d'information\n  createInfoRow(label, value) {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n    return row;\n  }\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight) {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n  preloadLocations() {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n  setupAutocomplete() {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.departureLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('departureLocation')?.setValue('');\n      });\n    });\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.arrivalLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('arrivalLocation')?.setValue('');\n      });\n    });\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  displayLocation(location) {\n    if (!location) return '';\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n  onSearch() {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n    const formValue = this.searchForm.value;\n    // Créer la requête de recherche entièrement dynamique\n    const request = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [{\n        id: formValue.departureLocation?.id || '',\n        type: formValue.departureLocationType\n      }],\n      ArrivalLocations: [{\n        id: formValue.arrivalLocation?.id || '',\n        type: formValue.arrivalLocationType\n      }],\n      Passengers: [{\n        type: formValue.passengerType,\n        count: formValue.passengerCount\n      }],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n    this.productService.searchPrice(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.header.success) {\n          this.searchResults = response.body.flights;\n          // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n          console.log('Full API Response:', JSON.stringify(response, null, 2));\n          // Analyser les données de disponibilité\n          if (response.body && response.body.flights && response.body.flights.length > 0) {\n            console.group('Availability Analysis');\n            console.log('Total flights:', response.body.flights.length);\n            // Compter les vols avec des offres\n            const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n            console.log('Flights with offers:', flightsWithOffers.length);\n            // Analyser les valeurs de disponibilité\n            const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability !== undefined ? o.availability : o.seatInfo ? o.seatInfo.availableSeatCount : 0));\n            console.log('Availability values:', availabilityValues);\n            // Compter les différentes valeurs de disponibilité\n            const availabilityCounts = availabilityValues.reduce((acc, val) => {\n              if (val !== undefined) {\n                acc[val] = (acc[val] || 0) + 1;\n              }\n              return acc;\n            }, {});\n            console.log('Availability counts:', availabilityCounts);\n            // Vérifier les vols réservables\n            const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true));\n            console.log('Reservable flights:', reservableFlights.length);\n            console.groupEnd();\n          }\n          // Vérifier si searchId existe dans le corps de la réponse\n          if (response.body && response.body.searchId) {\n            this.lastSearchId = response.body.searchId;\n            console.log('Search ID found in body.searchId:', this.lastSearchId);\n          }\n          // Vérifier si searchId existe dans l'en-tête de la réponse\n          else if (response.header && response.header.requestId) {\n            this.lastSearchId = response.header.requestId;\n            console.log('Search ID found in header.requestId:', this.lastSearchId);\n          }\n          // Vérifier d'autres emplacements possibles\n          else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n            this.lastSearchId = response.body.flights[0].id;\n            console.log('Using flight ID as search ID:', this.lastSearchId);\n          } else {\n            console.error('No search ID found in the response!');\n            console.log('Response structure:', Object.keys(response));\n            if (response.body) console.log('Body structure:', Object.keys(response.body));\n            if (response.header) console.log('Header structure:', Object.keys(response.header));\n          }\n        } else {\n          this.errorMessage = 'The search could not be completed. Please try again.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred during the search. Please try again.';\n        console.error('Error searching flights:', error);\n      }\n    });\n  }\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  // Formater la durée en heures et minutes\n  formatDuration(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n  // Formater la date pour l'affichage\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n    const minOffer = flight.offers.reduce((min, offer) => offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight) {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    return availabilityValue > 0;\n  }\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations() {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations() {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations() {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString) {\n    if (!dateString) return 'N/A';\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType) {\n    switch (baggageType) {\n      case 1:\n        return 'Cabin Baggage';\n      case 2:\n        return 'Checked Baggage';\n      case 3:\n        return 'Hand Baggage';\n      default:\n        return 'Baggage';\n    }\n  }\n  // Filtrer les bagages par type\n  filterBaggageByType(baggageInformations, type) {\n    if (!baggageInformations || !Array.isArray(baggageInformations)) {\n      return [];\n    }\n    return baggageInformations.filter(baggage => baggage.baggageType === type);\n  }\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType) {\n    switch (passengerType) {\n      case 1:\n        return 'Adult';\n      case 2:\n        return 'Child';\n      case 3:\n        return 'Infant';\n      default:\n        return 'Passenger';\n    }\n  }\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment, nextSegment) {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date || !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n  // Méthodes pour le nouveau design de recherche\n  togglePassengerDropdown(event) {\n    event.stopPropagation();\n    this.isPassengerDropdownOpen = !this.isPassengerDropdownOpen;\n    // Fermer le dropdown quand on clique ailleurs\n    if (this.isPassengerDropdownOpen) {\n      setTimeout(() => {\n        document.addEventListener('click', this.closePassengerDropdown);\n      }, 0);\n    }\n  }\n  increasePassengerCount() {\n    const currentCount = this.searchForm.get('passengerCount')?.value || 1;\n    if (currentCount < 9) {\n      this.searchForm.get('passengerCount')?.setValue(currentCount + 1);\n    }\n  }\n  decreasePassengerCount() {\n    const currentCount = this.searchForm.get('passengerCount')?.value || 1;\n    if (currentCount > 1) {\n      this.searchForm.get('passengerCount')?.setValue(currentCount - 1);\n    }\n  }\n  getFlightClassName() {\n    const flightClassValue = this.searchForm.get('flightClass')?.value;\n    const flightClass = this.flightClasses.find(fc => fc.value === flightClassValue);\n    return flightClass ? flightClass.label : 'Économie';\n  }\n  static {\n    this.ɵfac = function SearchPriceComponent_Factory(t) {\n      return new (t || SearchPriceComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchPriceComponent,\n      selectors: [[\"app-search-price\"]],\n      decls: 204,\n      vars: 32,\n      consts: [[1, \"search-price-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-illustration\"], [\"src\", \"assets/images/airplane-banner.jpg\", \"alt\", \"Airplane in the sky\"], [1, \"search-content\"], [1, \"search-form-container\"], [1, \"sidebar-logo\"], [1, \"logo-container\"], [1, \"fas\", \"fa-plane-departure\", \"logo-icon\"], [1, \"logo-text\"], [1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"search-card\"], [\"type\", \"hidden\", \"formControlName\", \"productType\", \"value\", \"3\"], [\"type\", \"hidden\", \"formControlName\", \"serviceTypes\", \"value\", \"['1']\"], [1, \"trip-type-selector\"], [1, \"trip-type-option\"], [\"type\", \"radio\", \"id\", \"roundTrip\", \"name\", \"tripType\", \"value\", \"roundTrip\", \"checked\", \"\"], [\"for\", \"roundTrip\"], [\"type\", \"radio\", \"id\", \"oneWay\", \"name\", \"tripType\", \"value\", \"oneWay\"], [\"for\", \"oneWay\"], [\"type\", \"radio\", \"id\", \"multiCity\", \"name\", \"tripType\", \"value\", \"multiCity\"], [\"for\", \"multiCity\"], [1, \"horizontal-search-form\"], [1, \"search-field\", \"location-field\"], [\"for\", \"departureLocation\"], [\"type\", \"text\", \"id\", \"departureLocation\", \"formControlName\", \"departureLocation\", \"placeholder\", \"Pays, ville ou a\\u00E9roport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [3, \"displayWith\"], [\"departureAuto\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"departureLocationType\", \"formControlName\", \"departureLocationType\", \"aria-label\", \"Departure Location Type\", 1, \"form-control\", \"location-type-selector\"], [3, \"value\"], [1, \"swap-button-container\"], [\"type\", \"button\", 1, \"swap-locations-btn\", 3, \"click\"], [1, \"fas\", \"fa-exchange-alt\"], [\"for\", \"arrivalLocation\"], [\"type\", \"text\", \"id\", \"arrivalLocation\", \"formControlName\", \"arrivalLocation\", \"placeholder\", \"Pays, ville ou a\\u00E9roport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [\"arrivalAuto\", \"matAutocomplete\"], [\"id\", \"arrivalLocationType\", \"formControlName\", \"arrivalLocationType\", \"aria-label\", \"Arrival Location Type\", 1, \"form-control\", \"location-type-selector\"], [1, \"search-field\", \"date-field\"], [\"for\", \"departureDate\"], [\"type\", \"date\", \"id\", \"departureDate\", \"formControlName\", \"departureDate\", 1, \"form-control\", 3, \"min\"], [\"for\", \"returnDate\"], [\"type\", \"text\", \"id\", \"returnDate\", \"placeholder\", \"(Aller simple)\", \"disabled\", \"\", 1, \"form-control\"], [1, \"search-field\", \"passengers-field\"], [\"for\", \"passengerDropdown\"], [\"type\", \"button\", \"id\", \"passengerDropdown\", 1, \"dropdown-toggle\", \"form-control\", 3, \"click\"], [1, \"dropdown-menu\", \"passenger-dropdown\"], [1, \"dropdown-header\"], [1, \"cabin-class-info\"], [1, \"flight-class-selection\"], [\"id\", \"flightClass\", \"formControlName\", \"flightClass\", 1, \"form-control\"], [1, \"passenger-type\"], [1, \"passenger-type-header\"], [1, \"passenger-type-description\"], [1, \"passenger-count-control\"], [\"type\", \"button\", 1, \"passenger-count-btn\", \"decrease\", 3, \"click\"], [1, \"fas\", \"fa-minus\"], [1, \"passenger-count\"], [\"type\", \"button\", 1, \"passenger-count-btn\", \"increase\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [\"type\", \"button\", \"disabled\", \"\", 1, \"passenger-count-btn\", \"decrease\"], [\"type\", \"button\", \"disabled\", \"\", 1, \"passenger-count-btn\", \"increase\"], [1, \"search-button-container\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [1, \"additional-options\"], [1, \"option-checkbox\"], [\"type\", \"checkbox\", \"id\", \"nearbyAirportsDeparture\", 1, \"custom-checkbox\"], [\"for\", \"nearbyAirportsDeparture\"], [\"type\", \"checkbox\", \"id\", \"nearbyAirportsArrival\", 1, \"custom-checkbox\"], [\"for\", \"nearbyAirportsArrival\"], [\"type\", \"checkbox\", \"id\", \"directFlightsOnly\", \"formControlName\", \"nonStop\", 1, \"custom-checkbox\"], [\"for\", \"directFlightsOnly\"], [1, \"advanced-options-container\"], [1, \"fas\", \"fa-cog\"], [1, \"advanced-options\"], [1, \"form-row\"], [1, \"form-group\"], [\"for\", \"culture\"], [1, \"input-with-icon\"], [1, \"fas\", \"fa-language\"], [\"id\", \"culture\", \"formControlName\", \"culture\", 1, \"form-control\"], [\"value\", \"en-US\"], [\"value\", \"fr-FR\"], [\"for\", \"currency\"], [1, \"fas\", \"fa-money-bill-wave\"], [\"id\", \"currency\", \"formControlName\", \"currency\", 1, \"form-control\"], [\"value\", \"EUR\"], [\"value\", \"USD\"], [\"for\", \"flightBaggageGetOption\"], [1, \"fas\", \"fa-suitcase\"], [\"id\", \"flightBaggageGetOption\", \"formControlName\", \"flightBaggageGetOption\", 1, \"form-control\"], [1, \"form-row\", \"checkbox-options\"], [1, \"form-group\", \"checkbox-group\"], [1, \"toggle-switch\", \"small\"], [\"type\", \"checkbox\", \"id\", \"acceptPendingProviders\", \"formControlName\", \"acceptPendingProviders\", 1, \"toggle-input\"], [\"for\", \"acceptPendingProviders\", 1, \"toggle-label\"], [1, \"toggle-inner\"], [1, \"toggle-switch-label\"], [\"type\", \"checkbox\", \"id\", \"forceFlightBundlePackage\", \"formControlName\", \"forceFlightBundlePackage\", 1, \"toggle-input\"], [\"for\", \"forceFlightBundlePackage\", 1, \"toggle-label\"], [\"type\", \"checkbox\", \"id\", \"disablePackageOfferTotalPrice\", \"formControlName\", \"disablePackageOfferTotalPrice\", 1, \"toggle-input\"], [\"for\", \"disablePackageOfferTotalPrice\", 1, \"toggle-label\"], [\"type\", \"checkbox\", \"id\", \"calculateFlightFees\", \"formControlName\", \"calculateFlightFees\", 1, \"toggle-input\"], [\"for\", \"calculateFlightFees\", 1, \"toggle-label\"], [\"class\", \"search-results-container\", 4, \"ngIf\"], [1, \"location-option\"], [1, \"location-name\"], [1, \"location-details\"], [\"class\", \"location-code\", 4, \"ngIf\"], [\"class\", \"location-city\", 4, \"ngIf\"], [1, \"location-type\"], [1, \"fas\", 3, \"ngClass\"], [1, \"location-code\"], [1, \"location-city\"], [1, \"spinner-container\"], [1, \"spinner\"], [1, \"search-results-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"search-results-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-animation\"], [1, \"plane-loader\"], [1, \"fas\", \"fa-plane\"], [1, \"cloud\"], [1, \"error-container\"], [1, \"error-icon\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"error-message\"], [1, \"retry-button\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"search-results-content\"], [1, \"results-header\"], [1, \"results-title\"], [\"class\", \"results-filters\", 4, \"ngIf\"], [1, \"flight-list\"], [\"class\", \"flight-card\", 3, \"unavailable\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"results-count\"], [1, \"results-filters\"], [1, \"filter-option\"], [1, \"fas\", \"fa-sort-amount-down\"], [1, \"filter-select\"], [\"value\", \"price\"], [\"value\", \"duration\"], [\"value\", \"departure\"], [\"value\", \"arrival\"], [1, \"flight-card\"], [1, \"flight-header\"], [1, \"airline-info\"], [1, \"airline-logo-container\"], [\"alt\", \"Airline logo\", \"class\", \"airline-logo\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"fas fa-plane airline-icon\", 4, \"ngIf\"], [1, \"airline-details\"], [1, \"airline-name\"], [1, \"flight-number\"], [\"class\", \"provider-info\", 4, \"ngIf\"], [1, \"flight-badges\"], [\"class\", \"flight-badge\", 4, \"ngIf\"], [\"class\", \"flight-badge branded\", 4, \"ngIf\"], [1, \"flight-price\"], [1, \"price-label\"], [1, \"price\"], [\"class\", \"availability\", 4, \"ngIf\"], [\"class\", \"expiration\", 4, \"ngIf\"], [1, \"flight-details\"], [1, \"flight-route\"], [1, \"departure\"], [1, \"time\"], [1, \"location\"], [1, \"airport-code\"], [1, \"city-name\"], [1, \"flight-duration\"], [1, \"duration-line\"], [1, \"dot\", \"departure-dot\"], [1, \"line-container\"], [1, \"line\"], [1, \"plane-icon\"], [1, \"dot\", \"arrival-dot\"], [1, \"duration-text\"], [1, \"fas\", \"fa-clock\"], [\"class\", \"stops\", 4, \"ngIf\"], [\"class\", \"stops direct\", 4, \"ngIf\"], [1, \"arrival\"], [1, \"flight-features\"], [\"class\", \"feature-group\", 4, \"ngIf\"], [1, \"price-breakdown-section\"], [1, \"price-breakdown-details\"], [1, \"price-breakdown-summary\"], [\"class\", \"price-breakdown-content\", 4, \"ngIf\"], [\"class\", \"segments-section\", 4, \"ngIf\"], [\"class\", \"branded-fare-section\", 4, \"ngIf\"], [1, \"flight-actions\"], [1, \"view-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [1, \"select-button\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-check-circle\"], [\"alt\", \"Airline logo\", 1, \"airline-logo\", 3, \"src\"], [1, \"fas\", \"fa-plane\", \"airline-icon\"], [1, \"provider-info\"], [1, \"fas\", \"fa-tag\"], [1, \"flight-badge\"], [1, \"fas\", \"fa-bolt\"], [1, \"fas\", \"fa-chair\"], [1, \"flight-badge\", \"branded\"], [1, \"fas\", \"fa-certificate\"], [1, \"availability\"], [1, \"expiration\"], [1, \"stops\"], [1, \"stop-count\"], [1, \"stops\", \"direct\"], [1, \"feature-group\"], [1, \"baggage-details\"], [\"class\", \"baggage-item cabin\", 4, \"ngIf\"], [\"class\", \"feature\", 4, \"ngIf\"], [\"class\", \"baggage-item checked\", 4, \"ngFor\", \"ngForOf\"], [1, \"baggage-item\", \"checked\"], [1, \"baggage-info\"], [1, \"baggage-type\"], [\"class\", \"baggage-specs\", 4, \"ngIf\"], [1, \"baggage-specs\"], [\"class\", \"baggage-item cabin\", 4, \"ngFor\", \"ngForOf\"], [1, \"baggage-item\", \"cabin\"], [1, \"fas\", \"fa-briefcase\"], [1, \"feature\"], [1, \"fas\", \"fa-concierge-bell\"], [\"class\", \"feature\", 4, \"ngFor\", \"ngForOf\"], [1, \"source-info\"], [1, \"price-breakdown-content\"], [\"class\", \"breakdown-group\", 4, \"ngIf\"], [1, \"breakdown-group\"], [\"class\", \"breakdown-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"breakdown-item service-fee\", 4, \"ngIf\"], [1, \"breakdown-total\"], [1, \"total-label\"], [1, \"total-amount\"], [1, \"breakdown-item\"], [1, \"item-price\"], [1, \"breakdown-item\", \"service-fee\"], [1, \"fee-label\"], [1, \"fee-amount\"], [1, \"segments-section\"], [1, \"segments-details\"], [1, \"segments-summary\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"segments-content\"], [\"class\", \"segment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"segment-item\"], [1, \"segment-header\"], [1, \"segment-number\"], [1, \"segment-airline\"], [1, \"segment-flight\"], [1, \"segment-route\"], [1, \"segment-departure\"], [1, \"segment-duration\"], [1, \"segment-arrival\"], [\"class\", \"layover-info\", 4, \"ngIf\"], [1, \"layover-info\"], [1, \"fas\", \"fa-hourglass-half\"], [1, \"branded-fare-section\"], [1, \"branded-fare-details\"], [1, \"branded-fare-summary\"], [1, \"branded-fare-content\"], [\"class\", \"branded-fare-description\", 4, \"ngIf\"], [\"class\", \"branded-fare-features\", 4, \"ngIf\"], [1, \"branded-fare-description\"], [1, \"branded-fare-features\"], [\"class\", \"feature-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature-item\"], [1, \"feature-name\"], [\"class\", \"feature-description\", 4, \"ngIf\"], [1, \"feature-description\"], [1, \"no-results\"], [1, \"no-results-icon\"], [1, \"fas\", \"fa-search\"], [1, \"no-results-suggestions\"], [1, \"suggestion\"], [1, \"fas\", \"fa-calendar-alt\"], [1, \"fas\", \"fa-map-marker-alt\"]],\n      template: function SearchPriceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Find Your Perfect Flight\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Search and compare flights to destinations worldwide\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵelement(8, \"img\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10);\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementStart(14, \"span\", 12);\n          i0.ɵɵtext(15, \"TravelEase\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"form\", 13);\n          i0.ɵɵlistener(\"ngSubmit\", function SearchPriceComponent_Template_form_ngSubmit_16_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(17, \"div\", 14);\n          i0.ɵɵelement(18, \"input\", 15)(19, \"input\", 16);\n          i0.ɵɵelementStart(20, \"div\", 17)(21, \"div\", 18);\n          i0.ɵɵelement(22, \"input\", 19);\n          i0.ɵɵelementStart(23, \"label\", 20);\n          i0.ɵɵtext(24, \"Aller-retour\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 18);\n          i0.ɵɵelement(26, \"input\", 21);\n          i0.ɵɵelementStart(27, \"label\", 22);\n          i0.ɵɵtext(28, \"Aller simple\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 18);\n          i0.ɵɵelement(30, \"input\", 23);\n          i0.ɵɵelementStart(31, \"label\", 24);\n          i0.ɵɵtext(32, \"Multi-destinations\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"div\", 25)(34, \"div\", 26)(35, \"label\", 27);\n          i0.ɵɵtext(36, \"De\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"input\", 28);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_37_listener() {\n            return ctx.showAllDepartureLocations();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"mat-autocomplete\", 29, 30);\n          i0.ɵɵtemplate(40, SearchPriceComponent_mat_option_40_Template, 10, 12, \"mat-option\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"select\", 32)(42, \"option\", 33);\n          i0.ɵɵtext(43, \"Country (1)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"option\", 33);\n          i0.ɵɵtext(45, \"City (2)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"option\", 33);\n          i0.ɵɵtext(47, \"Town (3)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"option\", 33);\n          i0.ɵɵtext(49, \"Village (4)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"option\", 33);\n          i0.ɵɵtext(51, \"Airport (5)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"div\", 34)(53, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_53_listener() {\n            return ctx.swapLocations();\n          });\n          i0.ɵɵelement(54, \"i\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 26)(56, \"label\", 37);\n          i0.ɵɵtext(57, \"\\u00C0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"input\", 38);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_58_listener() {\n            return ctx.showAllArrivalLocations();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"mat-autocomplete\", 29, 39);\n          i0.ɵɵtemplate(61, SearchPriceComponent_mat_option_61_Template, 10, 12, \"mat-option\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"select\", 40)(63, \"option\", 33);\n          i0.ɵɵtext(64, \"Country (1)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"option\", 33);\n          i0.ɵɵtext(66, \"City (2)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"option\", 33);\n          i0.ɵɵtext(68, \"Town (3)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"option\", 33);\n          i0.ɵɵtext(70, \"Village (4)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"option\", 33);\n          i0.ɵɵtext(72, \"Airport (5)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"div\", 41)(74, \"label\", 42);\n          i0.ɵɵtext(75, \"D\\u00E9part\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(76, \"input\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\", 41)(78, \"label\", 44);\n          i0.ɵɵtext(79, \"Retour\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(80, \"input\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"div\", 46)(82, \"label\", 47);\n          i0.ɵɵtext(83, \"Voyageurs et classe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"button\", 48);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_84_listener($event) {\n            return ctx.togglePassengerDropdown($event);\n          });\n          i0.ɵɵtext(85);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"div\", 49)(87, \"div\", 50);\n          i0.ɵɵtext(88, \"Classe de cabine\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"div\", 51);\n          i0.ɵɵtext(90, \" Nous pouvons seulement afficher les prix en classe \\u00E9conomique pour cette recherche. Pour voir les options pour la classe affaires, la classe \\u00E9conomique premium et la premi\\u00E8re classe, veuillez indiquer les dates et la destination de votre voyage. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"div\", 52)(92, \"select\", 53);\n          i0.ɵɵtemplate(93, SearchPriceComponent_option_93_Template, 2, 2, \"option\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"div\", 54)(95, \"div\", 55);\n          i0.ɵɵtext(96, \"Adultes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"div\", 56);\n          i0.ɵɵtext(98, \"18 ans et plus\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"div\", 57)(100, \"button\", 58);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_100_listener() {\n            return ctx.decreasePassengerCount();\n          });\n          i0.ɵɵelement(101, \"i\", 59);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"span\", 60);\n          i0.ɵɵtext(103);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"button\", 61);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_104_listener() {\n            return ctx.increasePassengerCount();\n          });\n          i0.ɵɵelement(105, \"i\", 62);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(106, \"div\", 54)(107, \"div\", 55);\n          i0.ɵɵtext(108, \"Enfants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"div\", 56);\n          i0.ɵɵtext(110, \"0 \\u00E0 17 ans\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"div\", 57)(112, \"button\", 63);\n          i0.ɵɵelement(113, \"i\", 59);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(114, \"span\", 60);\n          i0.ɵɵtext(115, \"0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"button\", 64);\n          i0.ɵɵelement(117, \"i\", 62);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(118, \"div\", 65)(119, \"button\", 66);\n          i0.ɵɵtemplate(120, SearchPriceComponent_span_120_Template, 2, 0, \"span\", 67);\n          i0.ɵɵtemplate(121, SearchPriceComponent_div_121_Template, 2, 0, \"div\", 68);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(122, \"div\", 69)(123, \"div\", 70);\n          i0.ɵɵelement(124, \"input\", 71);\n          i0.ɵɵelementStart(125, \"label\", 72);\n          i0.ɵɵtext(126, \"Ajouter des a\\u00E9roports \\u00E0 proximit\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(127, \"div\", 70);\n          i0.ɵɵelement(128, \"input\", 73);\n          i0.ɵɵelementStart(129, \"label\", 74);\n          i0.ɵɵtext(130, \"Ajouter des a\\u00E9roports \\u00E0 proximit\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(131, \"div\", 70);\n          i0.ɵɵelement(132, \"input\", 75);\n          i0.ɵɵelementStart(133, \"label\", 76);\n          i0.ɵɵtext(134, \"Vols directs uniquement\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(135, \"div\", 77)(136, \"details\")(137, \"summary\");\n          i0.ɵɵelement(138, \"i\", 78);\n          i0.ɵɵtext(139, \" Advanced Options \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(140, \"div\", 79)(141, \"div\", 80)(142, \"div\", 81)(143, \"label\", 82);\n          i0.ɵɵtext(144, \"Language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(145, \"div\", 83);\n          i0.ɵɵelement(146, \"i\", 84);\n          i0.ɵɵelementStart(147, \"select\", 85)(148, \"option\", 86);\n          i0.ɵɵtext(149, \"English (US)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(150, \"option\", 87);\n          i0.ɵɵtext(151, \"Fran\\u00E7ais\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(152, \"div\", 81)(153, \"label\", 88);\n          i0.ɵɵtext(154, \"Currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"div\", 83);\n          i0.ɵɵelement(156, \"i\", 89);\n          i0.ɵɵelementStart(157, \"select\", 90)(158, \"option\", 91);\n          i0.ɵɵtext(159, \"Euro (\\u20AC)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(160, \"option\", 92);\n          i0.ɵɵtext(161, \"Dollar ($)\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(162, \"div\", 81)(163, \"label\", 93);\n          i0.ɵɵtext(164, \"Baggage Options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(165, \"div\", 83);\n          i0.ɵɵelement(166, \"i\", 94);\n          i0.ɵɵelementStart(167, \"select\", 95)(168, \"option\", 33);\n          i0.ɵɵtext(169, \"All options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(170, \"option\", 33);\n          i0.ɵɵtext(171, \"Baggage included only\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(172, \"option\", 33);\n          i0.ɵɵtext(173, \"No baggage only\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(174, \"div\", 96)(175, \"div\", 97)(176, \"div\", 98);\n          i0.ɵɵelement(177, \"input\", 99);\n          i0.ɵɵelementStart(178, \"label\", 100);\n          i0.ɵɵelement(179, \"span\", 101);\n          i0.ɵɵelementStart(180, \"span\", 102);\n          i0.ɵɵtext(181, \"Accept pending providers\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(182, \"div\", 97)(183, \"div\", 98);\n          i0.ɵɵelement(184, \"input\", 103);\n          i0.ɵɵelementStart(185, \"label\", 104);\n          i0.ɵɵelement(186, \"span\", 101);\n          i0.ɵɵelementStart(187, \"span\", 102);\n          i0.ɵɵtext(188, \"Force flight bundle package\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(189, \"div\", 97)(190, \"div\", 98);\n          i0.ɵɵelement(191, \"input\", 105);\n          i0.ɵɵelementStart(192, \"label\", 106);\n          i0.ɵɵelement(193, \"span\", 101);\n          i0.ɵɵelementStart(194, \"span\", 102);\n          i0.ɵɵtext(195, \"Disable package offer total price\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(196, \"div\", 97)(197, \"div\", 98);\n          i0.ɵɵelement(198, \"input\", 107);\n          i0.ɵɵelementStart(199, \"label\", 108);\n          i0.ɵɵelement(200, \"span\", 101);\n          i0.ɵɵelementStart(201, \"span\", 102);\n          i0.ɵɵtext(202, \"Calculate flight fees\");\n          i0.ɵɵelementEnd()()()()()()()()()();\n          i0.ɵɵtemplate(203, SearchPriceComponent_div_203_Template, 4, 3, \"div\", 109);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(39);\n          const _r2 = i0.ɵɵreference(60);\n          let tmp_18_0;\n          let tmp_21_0;\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.departureLocations);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 3);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 5);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matAutocomplete\", _r2);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrivalLocations);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 3);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 5);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"min\", ctx.minDate);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate3(\" \", (tmp_18_0 = ctx.searchForm.get(\"passengerCount\")) == null ? null : tmp_18_0.value, \" \", ((tmp_18_0 = ctx.searchForm.get(\"passengerCount\")) == null ? null : tmp_18_0.value) > 1 ? \"adultes\" : \"adulte\", \", \", ctx.getFlightClassName(), \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"show\", ctx.isPassengerDropdownOpen);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate((tmp_21_0 = ctx.searchForm.get(\"passengerCount\")) == null ? null : tmp_21_0.value);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"disabled\", ctx.searchForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(47);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(31);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasSearched);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.MatAutocomplete, i6.MatOption, i5.MatAutocompleteTrigger, i4.SlicePipe, i4.CurrencyPipe],\n      styles: [\"/* Styles globaux pour \\u00E9viter le d\\u00E9filement horizontal */\\n* {\\n  box-sizing: border-box;\\n}\\n\\n/* Conteneur principal - \\u00C9vite le d\\u00E9filement horizontal */\\n.search-price-container {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 10px;\\n  width: 100%;\\n  max-width: 100%; /* Utilisation de toute la largeur de l'\\u00E9cran */\\n  margin: 0;\\n  position: relative;\\n  z-index: 1;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-price-container {\\n    padding: 0;\\n    margin: 0;\\n  }\\n}\\n\\n.search-price-container::before {\\n  content: '';\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background:\\n    linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.03) 0%, rgba(var(--primary-color-rgb), 0) 50%),\\n    radial-gradient(circle at top right, rgba(var(--secondary-color-rgb), 0.03) 0%, rgba(var(--secondary-color-rgb), 0) 70%);\\n  z-index: -1;\\n  pointer-events: none;\\n}\\n\\n/* En-t\\u00EAte de page */\\n.page-header {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  margin-bottom: 30px;\\n  position: relative;\\n  overflow: hidden;\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.page-header::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.1));\\n  z-index: 1;\\n}\\n\\n.header-content {\\n  max-width: 800px;\\n  padding: 40px 20px;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 2;\\n  color: white;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n.page-title {\\n  font-size: 36px;\\n  font-weight: 700;\\n  color: white;\\n  margin-bottom: 15px;\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.page-title::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: white;\\n  border-radius: 3px;\\n}\\n\\n.page-subtitle {\\n  font-size: 18px;\\n  color: rgba(255, 255, 255, 0.9);\\n  line-height: 1.5;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  margin-top: 20px;\\n}\\n\\n.header-illustration {\\n  width: 100%;\\n  height: 300px;\\n  overflow: hidden;\\n}\\n\\n.header-illustration img {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n  transition: transform 10s ease;\\n}\\n\\n.page-header:hover .header-illustration img {\\n  transform: scale(1.1);\\n}\\n\\n/* Disposition pour les \\u00E9crans larges - Carte de recherche horizontale en haut */\\n@media (min-width: 992px) {\\n  .page-header {\\n    display: none;\\n  }\\n\\n  .search-price-container {\\n    flex-direction: column;\\n    align-items: stretch;\\n    padding: 0;\\n    margin: 0;\\n    width: 100%;\\n  }\\n\\n  .search-content {\\n    display: flex;\\n    flex-direction: column;\\n    width: 100%;\\n    gap: 0;\\n  }\\n\\n  .search-form-container {\\n    position: sticky;\\n    top: 0;\\n    width: 100%;\\n    max-height: none;\\n    overflow: visible;\\n    margin-bottom: 20px;\\n    padding: 0;\\n    border-radius: 0;\\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n    z-index: 100;\\n    background-color: white;\\n  }\\n\\n  .search-results-container {\\n    width: 100%;\\n    margin-left: 0;\\n    padding: 20px;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n  }\\n}\\n\\n/* Formulaire de recherche */\\n.search-form-container {\\n  background-color: white;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  padding: 25px;\\n  margin-bottom: 30px;\\n}\\n\\n/* Logo pour la version desktop */\\n.sidebar-logo {\\n  display: none;\\n}\\n\\n/* Style sp\\u00E9cifique pour les \\u00E9crans larges */\\n@media (min-width: 992px) {\\n  .search-form-container {\\n    padding: 0;\\n    margin-bottom: 20px;\\n    border-radius: 0;\\n  }\\n\\n  .sidebar-logo {\\n    display: block;\\n    background-color: var(--primary-color);\\n    color: white;\\n    padding: 15px 20px;\\n    text-align: left;\\n  }\\n\\n  .logo-container {\\n    display: flex;\\n    align-items: center;\\n    gap: 10px;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n    width: 100%;\\n  }\\n\\n  .logo-icon {\\n    font-size: 22px;\\n  }\\n\\n  .logo-text {\\n    font-size: 22px;\\n    font-weight: 700;\\n    letter-spacing: 0.5px;\\n  }\\n}\\n\\n.search-form-header {\\n  margin-bottom: 25px;\\n  text-align: center;\\n  padding: 20px 20px 0 20px;\\n}\\n\\n.search-form-header h2 {\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n  font-size: 26px;\\n  font-weight: 600;\\n}\\n\\n.search-form-header p {\\n  color: #666;\\n  font-size: 15px;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-form-header {\\n    max-width: 1200px;\\n    margin: 0 auto 15px auto;\\n    text-align: left;\\n    padding: 20px 20px 0 20px;\\n  }\\n}\\n\\n.search-form {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.form-group {\\n  margin-bottom: 15px;\\n}\\n\\n.form-row {\\n  display: flex;\\n  gap: 15px;\\n}\\n\\n.half-width {\\n  flex: 1;\\n}\\n\\nlabel {\\n  display: block;\\n  margin-bottom: 5px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 14px;\\n}\\n\\n.form-control {\\n  width: 100%;\\n  padding: 10px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 5px;\\n  font-size: 14px;\\n  transition: border-color 0.3s;\\n}\\n\\n.form-control:focus {\\n  border-color: #2989d8;\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(41, 137, 216, 0.2);\\n}\\n\\n.checkbox-group {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.checkbox-group input {\\n  margin-right: 8px;\\n}\\n\\n.search-button {\\n  padding: 12px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 5px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.3s;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-top: 10px;\\n}\\n\\n.search-button:hover {\\n  background-color: #1e5799;\\n}\\n\\n.search-button:disabled {\\n  background-color: #b3d4f0;\\n  cursor: not-allowed;\\n}\\n\\n.error-message {\\n  color: #e74c3c;\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n\\n/* Options avanc\\u00E9es */\\ndetails {\\n  margin-top: 10px;\\n  margin-bottom: 15px;\\n}\\n\\nsummary {\\n  cursor: pointer;\\n  color: #2989d8;\\n  font-weight: 500;\\n  padding: 5px 0;\\n}\\n\\nsummary:hover {\\n  text-decoration: underline;\\n}\\n\\n.advanced-options {\\n  margin-top: 10px;\\n  padding: 15px;\\n  background-color: #f8f9fa;\\n  border-radius: 5px;\\n  border: 1px solid #eee;\\n}\\n\\n/* Styles pour les listes d\\u00E9roulantes */\\n::ng-deep .mat-autocomplete-panel {\\n  max-height: 300px !important;\\n}\\n\\n::ng-deep .mat-option {\\n  height: auto !important;\\n  line-height: 1.2 !important;\\n  padding: 10px 16px !important;\\n}\\n\\n::ng-deep .mat-option small {\\n  color: #666;\\n  display: block;\\n  margin-top: 2px;\\n}\\n\\n/* R\\u00E9sultats de recherche - Design optimis\\u00E9 pour \\u00E9viter le d\\u00E9filement horizontal */\\n.search-results-container {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  box-shadow:\\n    0 5px 15px rgba(0, 0, 0, 0.08),\\n    0 0 0 1px rgba(var(--primary-color-rgb), 0.05);\\n  padding: 20px;\\n  position: relative;\\n  overflow-x: hidden;\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  box-sizing: border-box;\\n  width: 100%;\\n}\\n\\n.search-results-container::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 6px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\\n  z-index: 1;\\n}\\n\\n.loading-container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px;\\n  text-align: center;\\n}\\n\\n.loading-container p {\\n  margin-top: 20px;\\n  color: var(--primary-color);\\n  font-weight: 500;\\n  animation: pulse 1.5s infinite;\\n}\\n\\n.spinner {\\n  width: 30px;\\n  height: 30px;\\n  border: 3px solid rgba(var(--primary-color-rgb), 0.2);\\n  border-radius: 50%;\\n  border-top-color: var(--primary-color);\\n  animation: spin 1s cubic-bezier(0.6, 0.2, 0.4, 0.8) infinite;\\n  box-shadow: 0 0 10px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.spinner.large {\\n  width: 50px;\\n  height: 50px;\\n  border-width: 4px;\\n}\\n\\n@keyframes spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n.error-container {\\n  padding: 30px;\\n  background-color: rgba(231, 76, 60, 0.05);\\n  border-radius: 16px;\\n  text-align: center;\\n  border: 1px solid rgba(231, 76, 60, 0.1);\\n  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.05);\\n  animation: scaleIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\\n}\\n\\n.error-container h4 {\\n  color: #e74c3c;\\n  margin-bottom: 10px;\\n  font-size: 18px;\\n}\\n\\n.error-container p {\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.results-header {\\n  margin-bottom: 30px;\\n  position: relative;\\n  padding-bottom: 15px;\\n}\\n\\n.results-header::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0.1) 0%,\\n    rgba(var(--primary-color-rgb), 0.05) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n/* Provider information */\\n.provider-info {\\n  display: block;\\n  font-size: 12px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-top: 4px;\\n}\\n\\n/* Availability information */\\n.availability {\\n  font-size: 12px;\\n  display: block;\\n  margin-top: 4px;\\n  font-weight: 500;\\n}\\n\\n.availability i.fa-check-circle {\\n  color: #4CAF50;\\n}\\n\\n.availability i.fa-check-circle + span {\\n  color: #4CAF50;\\n}\\n\\n.availability i.fa-exclamation-triangle {\\n  color: #F44336;\\n}\\n\\n.availability i.fa-exclamation-triangle + span {\\n  color: #F44336;\\n}\\n\\n/* Expiration information */\\n.expiration {\\n  color: #FF9800;\\n  font-size: 12px;\\n  display: block;\\n  margin-top: 4px;\\n}\\n\\n/* Branded fare badge */\\n.flight-badge.branded {\\n  background-color: #9C27B0;\\n}\\n\\n/* Feature groups */\\n.feature-group {\\n  margin-bottom: 15px;\\n}\\n\\n.feature-group h4 {\\n  font-size: 14px;\\n  margin-bottom: 8px;\\n  color: rgba(0, 0, 0, 0.7);\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.feature-group .feature {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 4px;\\n  padding-left: 20px;\\n}\\n\\n/* Offer ID */\\n.offer-id {\\n  font-family: monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n}\\n\\n/* Price breakdown section */\\n.price-breakdown-section {\\n  margin: 15px 0;\\n}\\n\\n.price-breakdown-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.price-breakdown-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.price-breakdown-summary:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.price-breakdown-content {\\n  padding: 15px;\\n}\\n\\n.breakdown-group h4 {\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.breakdown-item {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 8px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.breakdown-item.service-fee {\\n  color: #FF5722;\\n}\\n\\n.breakdown-total {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-top: 10px;\\n  padding-top: 10px;\\n  border-top: 1px dashed rgba(0, 0, 0, 0.1);\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n}\\n\\n/* Segments section */\\n.segments-section {\\n  margin: 15px 0;\\n}\\n\\n.segments-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.segments-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.segments-summary:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.segments-content {\\n  padding: 15px;\\n}\\n\\n.segment-item {\\n  margin-bottom: 20px;\\n  padding-bottom: 15px;\\n  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);\\n}\\n\\n.segment-item:last-child {\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n  border-bottom: none;\\n}\\n\\n.segment-header {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 10px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.segment-number {\\n  font-weight: 600;\\n}\\n\\n.segment-route {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 10px;\\n}\\n\\n.layover-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 13px;\\n  color: #FF9800;\\n  margin-top: 10px;\\n  padding: 8px;\\n  background-color: rgba(255, 152, 0, 0.05);\\n  border-radius: 4px;\\n}\\n\\n/* Branded fare section */\\n.branded-fare-section {\\n  margin: 15px 0;\\n}\\n\\n.branded-fare-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.branded-fare-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(156, 39, 176, 0.05);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: #9C27B0;\\n}\\n\\n.branded-fare-summary:hover {\\n  background-color: rgba(156, 39, 176, 0.1);\\n}\\n\\n.branded-fare-content {\\n  padding: 15px;\\n}\\n\\n.branded-fare-description {\\n  margin-bottom: 15px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.branded-fare-features h4 {\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature-item {\\n  margin-bottom: 10px;\\n}\\n\\n.feature-name {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature-description {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.results-header h3 {\\n  color: var(--primary-dark);\\n  margin-bottom: 8px;\\n  font-size: 24px;\\n  font-weight: 600;\\n}\\n\\n.results-header p {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 14px;\\n}\\n\\n.flight-list {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 25px;\\n}\\n\\n.flight-card {\\n  background-color: white;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  box-shadow:\\n    0 2px 8px rgba(0, 0, 0, 0.05),\\n    0 0 0 1px rgba(0, 0, 0, 0.03);\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.flight-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.02) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  pointer-events: none;\\n}\\n\\n.flight-card:hover {\\n  transform: translateY(-8px) scale(1.01);\\n  box-shadow:\\n    0 15px 30px rgba(0, 0, 0, 0.08),\\n    0 0 0 1px rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n.flight-card.unavailable {\\n  opacity: 0.7;\\n  transform: none !important;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03) !important;\\n}\\n\\n.flight-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px;\\n  background-color: #f9f9f9;\\n  border-bottom: 1px solid #eaeaea;\\n  position: relative;\\n  flex-wrap: wrap;\\n}\\n\\n.flight-header::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -1px;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.airline-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.airline-logo {\\n  width: 40px;\\n  height: 40px;\\n  object-fit: contain;\\n  padding: 5px;\\n  background-color: white;\\n  border-radius: 50%;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .airline-logo {\\n  transform: scale(1.1);\\n}\\n\\n.airline-name {\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: var(--primary-dark);\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .airline-name {\\n  color: var(--primary-color);\\n}\\n\\n.flight-price {\\n  text-align: right;\\n  position: relative;\\n}\\n\\n.price {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: var(--primary-color);\\n  display: block;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n\\n.flight-card:hover .price {\\n  color: var(--secondary-color);\\n  transform: scale(1.05);\\n}\\n\\n.price::before {\\n  content: '';\\n  position: absolute;\\n  bottom: -3px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background-color: var(--secondary-color);\\n  transition: width 0.3s ease;\\n}\\n\\n.flight-card:hover .price::before {\\n  width: 100%;\\n}\\n\\n.availability {\\n  font-size: 13px;\\n  color: #e74c3c;\\n  font-weight: 500;\\n  margin-top: 5px;\\n}\\n\\n.flight-details {\\n  padding: 15px;\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.flight-route {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n  position: relative;\\n  width: 100%;\\n}\\n\\n@media (max-width: 768px) {\\n  .flight-route {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n}\\n\\n.departure, .arrival {\\n  flex: 1;\\n  position: relative;\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .departure {\\n  transform: translateX(-5px);\\n}\\n\\n.flight-card:hover .arrival {\\n  transform: translateX(5px);\\n}\\n\\n.time {\\n  font-size: 22px;\\n  font-weight: 700;\\n  margin-bottom: 8px;\\n  color: var(--primary-dark);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.time i {\\n  color: var(--primary-color);\\n  font-size: 18px;\\n  opacity: 0;\\n  transform: translateY(5px);\\n  transition: all 0.3s ease;\\n}\\n\\n.flight-card:hover .departure .time i {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.flight-card:hover .arrival .time i {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.location {\\n  font-size: 15px;\\n  color: rgba(0, 0, 0, 0.6);\\n  font-weight: 500;\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .location {\\n  color: var(--primary-color);\\n}\\n\\n.location small {\\n  display: block;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.4);\\n  margin-top: 3px;\\n}\\n\\n.flight-duration {\\n  flex: 1;\\n  text-align: center;\\n  padding: 0 20px;\\n  position: relative;\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .flight-duration {\\n  transform: translateY(-5px);\\n}\\n\\n.duration-line {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 10px;\\n  position: relative;\\n}\\n\\n.dot {\\n  width: 10px;\\n  height: 10px;\\n  background-color: var(--primary-color);\\n  border-radius: 50%;\\n  position: relative;\\n  z-index: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0 0 4px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.flight-card:hover .dot {\\n  background-color: var(--secondary-color);\\n  transform: scale(1.2);\\n  box-shadow: 0 0 0 6px rgba(var(--secondary-color-rgb), 0.15);\\n}\\n\\n.line {\\n  flex: 1;\\n  height: 2px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));\\n  margin: 0 8px;\\n  position: relative;\\n  transition: height 0.3s ease, background 0.3s ease;\\n}\\n\\n.flight-card:hover .line {\\n  height: 3px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\\n}\\n\\n.duration-text {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n  color: var(--primary-dark);\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .duration-text {\\n  color: var(--primary-color);\\n}\\n\\n.stops {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.5);\\n  font-weight: 500;\\n  padding: 4px 12px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  border-radius: 50px;\\n  display: inline-block;\\n  transition: all 0.3s ease;\\n}\\n\\n.flight-card:hover .stops {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n}\\n\\n.flight-info {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.5);\\n  margin-top: 20px;\\n  padding-top: 15px;\\n  border-top: 1px dashed rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.flight-actions {\\n  padding: 20px 25px;\\n  border-top: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  display: flex;\\n  gap: 15px;\\n  justify-content: flex-end;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.flight-actions::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.view-details-button {\\n  padding: 10px 18px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  color: var(--primary-dark);\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.1);\\n  border-radius: 50px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.view-details-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.1) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.view-details-button i {\\n  font-size: 16px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.view-details-button:hover {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.view-details-button:hover::before {\\n  opacity: 1;\\n}\\n\\n.view-details-button:hover i {\\n  transform: translateX(3px);\\n}\\n\\n.select-button {\\n  padding: 10px 24px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  color: white;\\n  border: none;\\n  border-radius: 50px;\\n  font-size: 15px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.select-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);\\n  z-index: 1;\\n}\\n\\n.select-button::after {\\n  content: '';\\n  position: absolute;\\n  top: -50%;\\n  left: -50%;\\n  width: 200%;\\n  height: 200%;\\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 60%);\\n  opacity: 0;\\n  transform: scale(0.5);\\n  transition: transform 0.8s ease, opacity 0.8s ease;\\n  z-index: 1;\\n}\\n\\n.select-button i {\\n  font-size: 16px;\\n  transition: transform 0.3s ease;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.select-button span {\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.select-button:hover {\\n  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));\\n  transform: translateY(-3px) scale(1.02);\\n  box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.select-button:hover::after {\\n  opacity: 1;\\n  transform: scale(1);\\n}\\n\\n.select-button:hover i {\\n  transform: translateX(3px);\\n  animation: pulse 1s infinite;\\n}\\n\\n.select-button:active {\\n  transform: translateY(-1px) scale(1);\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.select-button:disabled {\\n  background: linear-gradient(135deg, #b0b0b0, #d0d0d0);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  opacity: 0.7;\\n}\\n\\n/* Styles pour les informations de bagages am\\u00E9lior\\u00E9es */\\n.baggage-details {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n  margin-top: 8px;\\n}\\n\\n.baggage-item {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n}\\n\\n.baggage-item.checked {\\n  background-color: #e7f5ff;\\n  border-color: #c5e1f9;\\n}\\n\\n.baggage-item.cabin {\\n  background-color: #f3f0ff;\\n  border-color: #e5dbff;\\n}\\n\\n.baggage-item i {\\n  font-size: 16px;\\n  margin-right: 10px;\\n  color: #4a6fa5;\\n}\\n\\n.baggage-item.cabin i {\\n  color: #6741d9;\\n}\\n\\n.baggage-info {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.baggage-type {\\n  font-weight: 600;\\n  font-size: 14px;\\n  color: #343a40;\\n}\\n\\n.baggage-specs {\\n  font-size: 12px;\\n  color: #6c757d;\\n  margin-top: 2px;\\n}\\n\\n/* Am\\u00E9lioration des groupes de caract\\u00E9ristiques */\\n.feature-group {\\n  margin-bottom: 15px;\\n  padding-bottom: 15px;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n\\n.feature-group:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n}\\n\\n.feature-group h4 {\\n  display: flex;\\n  align-items: center;\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n  color: #495057;\\n}\\n\\n.feature-group h4 i {\\n  margin-right: 8px;\\n  color: #4a6fa5;\\n}\\n\\n.feature {\\n  margin-top: 5px;\\n  font-size: 13px;\\n  color: #495057;\\n}\\n\\n/* Am\\u00E9lioration des ic\\u00F4nes de statut */\\n.text-success {\\n  color: #28a745;\\n}\\n\\n.text-danger {\\n  color: #dc3545;\\n}\\n\\n/* Style pour la note explicative */\\n.source-info {\\n  font-size: 11px;\\n  color: #6c757d;\\n  font-style: italic;\\n  margin-left: 5px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"/* Styles globaux pour \\u00E9viter le d\\u00E9filement horizontal */\\n.search-form * {\\n  box-sizing: border-box;\\n  max-width: 100%;\\n}\\n\\n.search-form input,\\n.search-form select,\\n.search-form button {\\n  max-width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n/* Styles pour la carte de recherche - Design professionnel inspir\\u00E9 des agences de voyage */\\n.search-card {\\n  background-color: var(--surface-color, white);\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n  padding: 20px;\\n  position: relative;\\n  margin-bottom: 20px;\\n  border: none;\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  box-sizing: border-box;\\n  overflow: hidden;\\n}\\n\\n/* Barre sup\\u00E9rieure bleue (style Booking.com) */\\n.search-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 4px;\\n  background-color: var(--primary-color);\\n  z-index: 1;\\n}\\n\\n/* Pas d'effet de survol exag\\u00E9r\\u00E9, juste une ombre l\\u00E9g\\u00E8rement plus prononc\\u00E9e */\\n.search-card:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n\\n/* Formulaire sur une seule ligne */\\n.single-line-form {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n  width: 100%;\\n}\\n\\n/* Disposition pour les \\u00E9crans larges - Carte de recherche horizontale */\\n@media (min-width: 992px) {\\n  .search-card {\\n    flex-direction: row;\\n    flex-wrap: nowrap;\\n    align-items: flex-end;\\n    padding: 15px;\\n    height: auto;\\n    border-radius: 0;\\n    box-shadow: none;\\n    margin-bottom: 0;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n  }\\n\\n  .single-line-form {\\n    flex-direction: row;\\n    align-items: flex-end;\\n    flex-wrap: nowrap;\\n    gap: 10px; /* Ajouter de l'espace entre les champs */\\n  }\\n\\n  /* Ajustement pour les champs From et To */\\n  .single-line-form .form-group:nth-child(1),\\n  .single-line-form .form-group:nth-child(5) {\\n    flex: 1.5;\\n    z-index: 4;\\n  }\\n\\n  /* Ajustement pour les autres champs */\\n  .single-line-form .form-group:nth-child(7),\\n  .single-line-form .form-group:nth-child(8),\\n  .single-line-form .form-group:nth-child(9) {\\n    flex: 0.8;\\n  }\\n\\n  /* Ajustement pour le conteneur du bouton d'\\u00E9change */\\n  .single-line-form .swap-button-container {\\n    flex: 0 0 auto;\\n    margin: 0;\\n    padding: 0 5px;\\n    z-index: 6;\\n  }\\n\\n  .form-group {\\n    min-width: 0; /* Permet aux \\u00E9l\\u00E9ments de r\\u00E9tr\\u00E9cir en dessous de leur largeur minimale */\\n    margin-bottom: 0;\\n    flex: 1;\\n    position: relative;\\n    z-index: 3;\\n    padding: 0 5px; /* Ajouter un peu d'espace de chaque c\\u00F4t\\u00E9 */\\n  }\\n\\n  .form-group.checkbox-group {\\n    flex: 0 0 auto;\\n  }\\n\\n  .search-button-container {\\n    flex: 0 0 auto;\\n    margin-top: 0;\\n    margin-left: 10px;\\n    text-align: center;\\n    padding-left: 5px;\\n  }\\n\\n  .search-button {\\n    width: auto;\\n    white-space: nowrap;\\n    padding: 10px 20px;\\n    height: 40px;\\n  }\\n}\\n\\n/* S\\u00E9lecteur de type de voyage - Style onglets (comme Booking.com) */\\n.trip-type-selector {\\n  display: flex;\\n  margin-bottom: 20px;\\n  position: relative;\\n  border-bottom: 1px solid #e7e7e7;\\n  padding-bottom: 0;\\n}\\n\\n.trip-type-option {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 20px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  font-weight: 500;\\n  color: #333;\\n  background-color: transparent;\\n  border: none;\\n  border-bottom: 3px solid transparent;\\n  position: relative;\\n  min-width: 100px;\\n  justify-content: center;\\n}\\n\\n.trip-type-option i {\\n  color: #666;\\n  font-size: 16px;\\n  transition: color 0.2s ease;\\n}\\n\\n.trip-type-option span {\\n  transition: color 0.2s ease;\\n}\\n\\n.trip-type-option.selected {\\n  color: var(--primary-color);\\n  font-weight: 600;\\n  border-bottom: 3px solid var(--primary-color);\\n  background-color: transparent;\\n}\\n\\n.trip-type-option.selected i {\\n  color: var(--primary-color);\\n}\\n\\n.trip-type-option:not(.selected):hover {\\n  color: var(--primary-color);\\n  border-bottom-color: rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.trip-type-option:not(.selected):hover i {\\n  color: var(--primary-color);\\n}\\n\\n.trip-type-option.disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n/* Rang\\u00E9es de formulaire - Style agences de voyage */\\n.form-row {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n\\n.locations-row {\\n  position: relative;\\n}\\n\\n/* Groupes de formulaire */\\n.form-group {\\n  flex: 1;\\n  position: relative;\\n}\\n\\n.location-type-selector {\\n  display: none; /* Cach\\u00E9 mais fonctionnel */\\n}\\n\\n/* \\u00C9tiquettes */\\nlabel {\\n  display: block;\\n  margin-bottom: 6px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 14px;\\n}\\n\\n/* Champs de saisie avec ic\\u00F4nes */\\n.input-with-icon {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.input-with-icon i {\\n  position: absolute;\\n  left: 12px;\\n  color: #666;\\n  font-size: 16px;\\n  z-index: 2;\\n}\\n\\n.form-control {\\n  width: 100%;\\n  padding: 12px 12px 12px 40px;\\n  border: 1px solid #e7e7e7;\\n  border-radius: 4px;\\n  font-size: 15px;\\n  transition: border-color 0.2s ease, box-shadow 0.2s ease;\\n  background-color: white;\\n  color: #333;\\n  height: 40px;\\n}\\n\\n.form-control:focus {\\n  outline: none;\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.form-control::placeholder {\\n  color: #999;\\n}\\n\\n/* Effet de focus sur le groupe entier */\\n.form-group:focus-within label {\\n  color: var(--primary-color);\\n}\\n\\n.form-group:focus-within .input-with-icon i {\\n  color: var(--primary-color);\\n}\\n\\n/* Ajustements pour le formulaire sur une seule ligne */\\n@media (min-width: 992px) {\\n  .single-line-form .form-group {\\n    margin-bottom: 0;\\n  }\\n\\n  .single-line-form label {\\n    font-size: 12px;\\n    margin-bottom: 4px;\\n  }\\n\\n  .single-line-form .form-control {\\n    font-size: 14px;\\n  }\\n\\n  .single-line-form .input-with-icon i {\\n    font-size: 14px;\\n  }\\n\\n  .single-line-form .error-message {\\n    position: absolute;\\n    font-size: 11px;\\n    bottom: -18px;\\n    left: 0;\\n    white-space: nowrap;\\n  }\\n}\\n\\n/* Conteneur du bouton d'\\u00E9change */\\n.swap-button-container {\\n  display: flex;\\n  align-items: flex-end;\\n  justify-content: center;\\n  padding-bottom: 10px; /* Aligner avec les champs de formulaire */\\n  position: relative;\\n  z-index: 5;\\n}\\n\\n/* Bouton d'\\u00E9change de lieux - Style agences de voyage */\\n.swap-locations-btn {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: var(--primary-color);\\n  color: white;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n\\n.swap-locations-btn:hover {\\n  background-color: var(--primary-dark);\\n  transform: rotate(180deg);\\n}\\n\\n.swap-locations-btn:active {\\n  transform: scale(0.95) rotate(180deg);\\n}\\n\\n.swap-locations-btn i {\\n  font-size: 16px;\\n}\\n\\n@media (min-width: 992px) {\\n  .swap-button-container {\\n    padding-bottom: 10px;\\n  }\\n}\\n\\n/* Options d'emplacement dans l'autocompl\\u00E9tion */\\n.location-option {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.location-name {\\n  font-weight: 500;\\n}\\n\\n.location-details {\\n  display: flex;\\n  gap: 12px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.location-code {\\n  font-weight: 500;\\n}\\n\\n.location-type {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.location-type i {\\n  font-size: 12px;\\n  color: #2989d8;\\n}\\n\\n/* Interrupteurs \\u00E0 bascule */\\n.toggle-switch {\\n  position: relative;\\n  display: inline-flex;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n\\n.toggle-input {\\n  opacity: 0;\\n  width: 0;\\n  height: 0;\\n  position: absolute;\\n}\\n\\n.toggle-label {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n}\\n\\n.toggle-inner {\\n  position: relative;\\n  display: inline-block;\\n  width: 50px;\\n  height: 24px;\\n  background-color: rgba(0, 0, 0, 0.12);\\n  border-radius: 12px;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.toggle-inner:before {\\n  content: '';\\n  position: absolute;\\n  left: 2px;\\n  top: 2px;\\n  width: 20px;\\n  height: 20px;\\n  background-color: white;\\n  border-radius: 50%;\\n  transition: transform 0.2s ease;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\\n}\\n\\n.toggle-input:checked + .toggle-label .toggle-inner {\\n  background-color: #2989d8;\\n}\\n\\n.toggle-input:checked + .toggle-label .toggle-inner:before {\\n  transform: translateX(26px);\\n}\\n\\n.toggle-switch-label {\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.8);\\n}\\n\\n.toggle-switch.small .toggle-inner {\\n  width: 40px;\\n  height: 20px;\\n}\\n\\n.toggle-switch.small .toggle-inner:before {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.toggle-switch.small .toggle-input:checked + .toggle-label .toggle-inner:before {\\n  transform: translateX(20px);\\n}\\n\\n/* Conteneur du bouton de recherche - Style agences de voyage */\\n.search-button-container {\\n  margin-top: 20px;\\n  display: flex;\\n  justify-content: center;\\n  position: relative;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-button-container {\\n    margin-top: 0;\\n  }\\n}\\n\\n/* Bouton de recherche - Style Booking.com */\\n.search-button {\\n  background-color: var(--primary-color);\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 12px 24px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 140px;\\n  justify-content: center;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (min-width: 992px) {\\n  .search-button {\\n    padding: 0 20px;\\n    font-size: 15px;\\n    min-width: 100px;\\n    height: 40px;\\n  }\\n\\n  .single-line-form .search-button-container {\\n    margin-top: 21px; /* Aligner avec les champs de formulaire (hauteur du label + marge) */\\n  }\\n}\\n\\n.search-button:hover:not(:disabled) {\\n  background-color: var(--primary-dark);\\n}\\n\\n.search-button:active:not(:disabled) {\\n  transform: translateY(1px);\\n}\\n\\n.search-button:disabled {\\n  background-color: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n.search-button i {\\n  font-size: 16px;\\n}\\n\\n/* Options avanc\\u00E9es - Design riche */\\n.advanced-options-container {\\n  margin-top: 25px;\\n  position: relative;\\n}\\n\\n.advanced-options-container::before {\\n  content: '';\\n  position: absolute;\\n  top: -10px;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.advanced-options-container summary {\\n  cursor: pointer;\\n  color: var(--primary-color);\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  padding: 12px 20px;\\n  transition: all 0.3s ease;\\n  outline: none;\\n  border-radius: 50px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.05);\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  margin: 0 auto;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.advanced-options-container summary::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.1) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.advanced-options-container summary:hover {\\n  color: var(--secondary-color);\\n  background-color: rgba(var(--primary-color-rgb), 0.08);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.advanced-options-container summary:hover::before {\\n  opacity: 1;\\n}\\n\\n.advanced-options-container summary i {\\n  font-size: 18px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.advanced-options-container[open] summary i {\\n  transform: rotate(180deg);\\n}\\n\\n.advanced-options {\\n  margin-top: 20px;\\n  padding: 25px;\\n  background-color: rgba(var(--primary-color-rgb), 0.03);\\n  border-radius: 16px;\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  box-shadow:\\n    inset 0 1px 8px rgba(var(--primary-color-rgb), 0.05),\\n    0 5px 15px rgba(0, 0, 0, 0.03);\\n  position: relative;\\n  animation: scaleIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\\n  overflow: hidden;\\n}\\n\\n.advanced-options::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background:\\n    radial-gradient(circle at top right, rgba(var(--primary-color-rgb), 0.05) 0%, rgba(var(--primary-color-rgb), 0) 70%),\\n    radial-gradient(circle at bottom left, rgba(var(--secondary-color-rgb), 0.05) 0%, rgba(var(--secondary-color-rgb), 0) 70%);\\n  pointer-events: none;\\n}\\n\\n.checkbox-options {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n}\\n\\n.checkbox-options .form-group {\\n  flex: 1 0 45%;\\n  transition: transform 0.3s ease;\\n}\\n\\n.checkbox-options .form-group:hover {\\n  transform: translateY(-2px);\\n}\\n\\n/* Messages d'erreur */\\n.error-message {\\n  color: #e74c3c;\\n  font-size: 13px;\\n  margin-top: 6px;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.error-message i {\\n  font-size: 14px;\\n}\\n\\n/* Animation de chargement */\\n.spinner-container {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spinner {\\n  width: 20px;\\n  height: 20px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  border-top-color: white;\\n  animation: spin 0.8s linear infinite;\\n}\\n\\n@keyframes spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n/* Styles pour les r\\u00E9sultats de recherche */\\n.search-results-container {\\n  background-color: white;\\n  border-radius: 16px;\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\\n  padding: 24px;\\n  margin-top: 24px;\\n}\\n\\n/* Animation de chargement personnalis\\u00E9e */\\n.loading-animation {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n}\\n\\n.plane-loader {\\n  position: relative;\\n  width: 200px;\\n  height: 100px;\\n  margin-bottom: 24px;\\n}\\n\\n.plane-loader i {\\n  position: absolute;\\n  font-size: 32px;\\n  color: #2989d8;\\n  animation: fly 3s infinite linear;\\n  top: 40%;\\n  left: 0;\\n}\\n\\n.cloud {\\n  position: absolute;\\n  width: 50px;\\n  height: 20px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 20px;\\n}\\n\\n.cloud:nth-child(2) {\\n  top: 20%;\\n  left: 20%;\\n  animation: cloud 8s infinite linear;\\n}\\n\\n.cloud:nth-child(3) {\\n  top: 60%;\\n  left: 40%;\\n  animation: cloud 6s infinite linear;\\n}\\n\\n.cloud:nth-child(4) {\\n  top: 40%;\\n  left: 60%;\\n  animation: cloud 10s infinite linear;\\n}\\n\\n@keyframes fly {\\n  0% {\\n    transform: translateX(0) rotate(0);\\n  }\\n  100% {\\n    transform: translateX(200px) rotate(0);\\n  }\\n}\\n\\n@keyframes cloud {\\n  0% {\\n    transform: translateX(0);\\n  }\\n  100% {\\n    transform: translateX(-200px);\\n  }\\n}\\n\\n.loading-animation p {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n/* Styles pour les r\\u00E9sultats */\\n.results-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.08);\\n}\\n\\n.results-title h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 4px;\\n}\\n\\n.results-count {\\n  font-weight: 600;\\n  color: #2989d8;\\n}\\n\\n.filter-option {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.filter-select {\\n  padding: 8px 12px;\\n  border: 1px solid rgba(0, 0, 0, 0.12);\\n  border-radius: 6px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.8);\\n  background-color: white;\\n}\\n\\n/* Carte de vol */\\n.flight-card {\\n  background-color: white;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n  margin-bottom: 20px;\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.flight-card:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\\n}\\n\\n.flight-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.airline-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.airline-logo-container {\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n.airline-logo {\\n  max-width: 32px;\\n  max-height: 32px;\\n  object-fit: contain;\\n}\\n\\n.airline-icon {\\n  font-size: 20px;\\n  color: #2989d8;\\n}\\n\\n.airline-details {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.airline-name {\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  font-size: 15px;\\n}\\n\\n.flight-number {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.flight-badges {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.flight-badge {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 4px 8px;\\n  background-color: rgba(41, 137, 216, 0.1);\\n  color: #2989d8;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.flight-price {\\n  text-align: right;\\n}\\n\\n.price-label {\\n  display: block;\\n  font-size: 12px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 4px;\\n}\\n\\n.price {\\n  font-size: 22px;\\n  font-weight: 700;\\n  color: #2989d8;\\n}\\n\\n.availability {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  color: #e74c3c;\\n  font-size: 12px;\\n  margin-top: 4px;\\n}\\n\\n.flight-details {\\n  padding: 20px;\\n}\\n\\n.flight-route {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.departure, .arrival {\\n  flex: 1;\\n}\\n\\n.time {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 6px;\\n}\\n\\n.location {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.airport-code {\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.7);\\n  font-size: 15px;\\n}\\n\\n.city-name {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 13px;\\n}\\n\\n.flight-duration {\\n  flex: 1;\\n  text-align: center;\\n  padding: 0 20px;\\n}\\n\\n.duration-line {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 10px;\\n}\\n\\n.dot {\\n  width: 10px;\\n  height: 10px;\\n  background-color: #2989d8;\\n  border-radius: 50%;\\n  z-index: 1;\\n}\\n\\n.departure-dot {\\n  background-color: #4CAF50;\\n}\\n\\n.arrival-dot {\\n  background-color: #F44336;\\n}\\n\\n.line-container {\\n  flex: 1;\\n  position: relative;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.line {\\n  width: 100%;\\n  height: 2px;\\n  background-color: #2989d8;\\n}\\n\\n.plane-icon {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  color: #2989d8;\\n  font-size: 14px;\\n}\\n\\n.duration-text {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.7);\\n  margin-bottom: 6px;\\n}\\n\\n.stops {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.stop-count {\\n  font-weight: 600;\\n  color: #F44336;\\n}\\n\\n.stops.direct {\\n  color: #4CAF50;\\n  font-weight: 500;\\n}\\n\\n.flight-features {\\n  display: flex;\\n  gap: 16px;\\n  margin-top: 16px;\\n  padding-top: 16px;\\n  border-top: 1px dashed rgba(0, 0, 0, 0.1);\\n}\\n\\n.feature {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature i {\\n  color: #2989d8;\\n  font-size: 14px;\\n}\\n\\n.flight-actions {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n  padding: 16px 20px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.view-details-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 10px 16px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: rgba(0, 0, 0, 0.7);\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.view-details-button:hover {\\n  background-color: rgba(0, 0, 0, 0.1);\\n}\\n\\n.select-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 10px 16px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(41, 137, 216, 0.3);\\n}\\n\\n.select-button:hover {\\n  background-color: #1e5799;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(41, 137, 216, 0.4);\\n}\\n\\n.select-button:disabled {\\n  background-color: rgba(0, 0, 0, 0.1);\\n  color: rgba(0, 0, 0, 0.4);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n/* Message pas de r\\u00E9sultats */\\n.no-results {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.no-results-icon {\\n  width: 80px;\\n  height: 80px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n}\\n\\n.no-results-icon i {\\n  font-size: 32px;\\n  color: rgba(0, 0, 0, 0.3);\\n}\\n\\n.no-results h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 8px;\\n}\\n\\n.no-results p {\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 24px;\\n  max-width: 500px;\\n}\\n\\n.no-results-suggestions {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  justify-content: center;\\n}\\n\\n.suggestion {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 10px 16px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 8px;\\n  color: rgba(0, 0, 0, 0.7);\\n  font-size: 14px;\\n}\\n\\n.suggestion i {\\n  color: #2989d8;\\n}\\n\\n/* Message d'erreur */\\n.error-container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.error-icon {\\n  width: 80px;\\n  height: 80px;\\n  background-color: rgba(231, 76, 60, 0.1);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n}\\n\\n.error-icon i {\\n  font-size: 32px;\\n  color: #e74c3c;\\n}\\n\\n.error-container h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 8px;\\n}\\n\\n.error-container .error-message {\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 24px;\\n  font-size: 15px;\\n  justify-content: center;\\n}\\n\\n.retry-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 20px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 15px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(41, 137, 216, 0.3);\\n}\\n\\n.retry-button:hover {\\n  background-color: #1e5799;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(41, 137, 216, 0.4);\\n}\\n\\n/* Styles responsifs */\\n@media (max-width: 768px) {\\n  .form-row {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .swap-locations-btn {\\n    display: none;\\n  }\\n\\n  .flight-route {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n\\n  .departure, .arrival {\\n    text-align: center;\\n  }\\n\\n  .flight-duration {\\n    margin: 16px 0;\\n  }\\n\\n  .flight-header {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .airline-info {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n\\n  .flight-badges {\\n    justify-content: center;\\n  }\\n\\n  .flight-price {\\n    text-align: center;\\n    width: 100%;\\n  }\\n\\n  .flight-features {\\n    flex-direction: column;\\n    gap: 12px;\\n    align-items: center;\\n  }\\n\\n  .flight-actions {\\n    flex-direction: column;\\n  }\\n\\n  .view-details-button, .select-button {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .page-header {\\n    flex-direction: column;\\n    gap: 24px;\\n  }\\n\\n  .header-content {\\n    text-align: center;\\n  }\\n\\n  .page-title {\\n    font-size: 1.75rem;\\n  }\\n\\n  .checkbox-options .form-group {\\n    flex: 1 0 100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"/******/ (() => { // webpackBootstrap\\n/******/ \\t\\\"use strict\\\";\\n/******/ \\t\\n/******/ \\t\\n/******/ })()\\n;\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "of", "FlightClassType", "LocationType", "PassengerType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "location_r8", "code", "city", "ɵɵtemplate", "SearchPriceComponent_mat_option_40_span_5_Template", "SearchPriceComponent_mat_option_40_span_6_Template", "ɵɵelement", "ɵɵproperty", "name", "type", "ɵɵpureFunction5", "_c0", "ɵɵtextInterpolate1", "location_r13", "SearchPriceComponent_mat_option_61_span_5_Template", "SearchPriceComponent_mat_option_61_span_6_Template", "flightClass_r18", "value", "label", "ɵɵlistener", "SearchPriceComponent_div_203_div_2_Template_button_click_7_listener", "ɵɵrestoreView", "_r23", "ctx_r22", "ɵɵnextContext", "ɵɵresetView", "onSearch", "ctx_r20", "errorMessage", "ctx_r25", "searchResults", "length", "flight_r29", "items", "airline", "thumbnailFull", "ɵɵsanitizeUrl", "flightProvider", "displayName", "provider", "flightClass", "ɵɵpureFunction2", "_c1", "ctx_r36", "isFlightAvailable", "ctx_r37", "formatExpirationDate", "offers", "expiresOn", "stopCount", "baggage_r57", "weight", "piece", "SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_div_1_span_5_Template", "SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_div_1_span_6_Template", "ɵɵelementContainerStart", "SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_div_1_Template", "ɵɵelementContainerEnd", "ɵɵpipeBind3", "ctx_r52", "filterBaggageByType", "baggageInformations", "baggage_r64", "SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_div_1_span_5_Template", "SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_div_1_span_6_Template", "SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_div_1_Template", "ctx_r53", "SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_5_Template", "SearchPriceComponent_div_203_div_3_div_9_div_55_ng_container_6_Template", "SearchPriceComponent_div_203_div_3_div_9_div_55_div_7_Template", "SearchPriceComponent_div_203_div_3_div_9_div_55_div_8_Template", "ctx_r40", "service_r72", "SearchPriceComponent_div_203_div_3_div_9_div_56_div_4_Template", "services", "ɵɵclassMap", "reservableInfo", "reservable", "SearchPriceComponent_div_203_div_3_div_9_div_57_div_4_Template", "ɵɵtextInterpolate2", "ctx_r78", "getPassengerTypeName", "item_r80", "passengerType", "passengerCount", "ɵɵpipeBind2", "price", "amount", "currency", "serviceFee", "SearchPriceComponent_div_203_div_3_div_9_div_63_div_1_div_3_Template", "SearchPriceComponent_div_203_div_3_div_9_div_63_div_1_div_4_Template", "priceBreakDown", "SearchPriceComponent_div_203_div_3_div_9_div_63_div_1_Template", "ctx_r87", "calculateLayoverTime", "segment_r85", "segments", "i_r86", "arrival", "SearchPriceComponent_div_203_div_3_div_9_div_64_div_6_div_34_Template", "flightNo", "departure", "ctx_r84", "formatDate", "date", "airport", "formatDuration", "duration", "SearchPriceComponent_div_203_div_3_div_9_div_64_div_6_Template", "brandedFare", "description", "feature_r96", "explanations", "text", "SearchPriceComponent_div_203_div_3_div_9_div_65_div_7_div_3_div_3_Template", "commercialName", "SearchPriceComponent_div_203_div_3_div_9_div_65_div_7_div_3_Template", "features", "SearchPriceComponent_div_203_div_3_div_9_div_65_div_6_Template", "SearchPriceComponent_div_203_div_3_div_9_div_65_div_7_Template", "SearchPriceComponent_div_203_div_3_div_9_img_4_Template", "SearchPriceComponent_div_203_div_3_div_9_i_5_Template", "SearchPriceComponent_div_203_div_3_div_9_span_11_Template", "SearchPriceComponent_div_203_div_3_div_9_span_13_Template", "SearchPriceComponent_div_203_div_3_div_9_span_14_Template", "SearchPriceComponent_div_203_div_3_div_9_span_15_Template", "SearchPriceComponent_div_203_div_3_div_9_span_21_Template", "SearchPriceComponent_div_203_div_3_div_9_span_22_Template", "SearchPriceComponent_div_203_div_3_div_9_div_44_Template", "SearchPriceComponent_div_203_div_3_div_9_div_45_Template", "SearchPriceComponent_div_203_div_3_div_9_div_55_Template", "SearchPriceComponent_div_203_div_3_div_9_div_56_Template", "SearchPriceComponent_div_203_div_3_div_9_div_57_Template", "SearchPriceComponent_div_203_div_3_div_9_div_63_Template", "SearchPriceComponent_div_203_div_3_div_9_div_64_Template", "SearchPriceComponent_div_203_div_3_div_9_div_65_Template", "SearchPriceComponent_div_203_div_3_div_9_Template_button_click_67_listener", "restoredCtx", "_r102", "$implicit", "ctx_r101", "showAllDetails", "SearchPriceComponent_div_203_div_3_div_9_Template_button_click_70_listener", "ctx_r103", "selectThisFlight", "ɵɵclassProp", "ctx_r27", "<PERSON><PERSON><PERSON>", "getMinPrice", "SearchPriceComponent_div_203_div_3_p_5_Template", "SearchPriceComponent_div_203_div_3_p_6_Template", "SearchPriceComponent_div_203_div_3_div_7_Template", "SearchPriceComponent_div_203_div_3_div_9_Template", "SearchPriceComponent_div_203_div_3_div_10_Template", "ctx_r21", "isLoading", "SearchPriceComponent_div_203_div_1_Template", "SearchPriceComponent_div_203_div_2_Template", "SearchPriceComponent_div_203_div_3_Template", "ctx_r7", "SearchPriceComponent", "constructor", "fb", "productService", "router", "departureLocations", "arrivalLocations", "hasSearched", "lastSearchId", "isPassengerDropdownOpen", "passengerTypes", "Adult", "Child", "Infant", "flightClasses", "PROMO", "ECONOMY", "BUSINESS", "closePassengerDropdown", "document", "removeEventListener", "minDate", "Date", "toISOString", "split", "searchForm", "group", "productType", "required", "serviceTypes", "departureLocation", "departureLocationType", "arrivalLocation", "arrivalLocationType", "departureDate", "min", "max", "nonStop", "culture", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightBaggageGetOption", "ngOnInit", "setupAutocomplete", "preloadLocations", "console", "log", "flight", "modalDiv", "createElement", "style", "position", "top", "left", "width", "height", "backgroundColor", "zIndex", "display", "justifyContent", "alignItems", "modalContent", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "color", "fontFamily", "closeButton", "innerHTML", "right", "border", "background", "fontSize", "cursor", "transition", "on<PERSON><PERSON>ver", "onmouseout", "onclick", "body", "<PERSON><PERSON><PERSON><PERSON>", "header", "marginBottom", "paddingBottom", "borderBottom", "logo", "title", "textContent", "margin", "fontWeight", "append<PERSON><PERSON><PERSON>", "detailsContainer", "flexDirection", "gap", "generalInfo", "createSection", "item", "airlineInfo", "airlineLogo", "src", "alt", "marginRight", "airlineIcon", "airlineName", "internationalCode", "flightNumberRow", "createInfoRow", "flightDateRow", "flightDate", "toLocaleDateString", "durationRow", "classRow", "stopsRow", "routeSection", "routeVisual", "textAlign", "flex", "departureTime", "toLocaleTimeString", "hour", "minute", "departureAirport", "marginTop", "departureCity", "connectionLine", "line", "plane", "marginLeft", "arrivalTime", "arrivalAirport", "arrivalCity", "segmentsTitle", "segmentsList", "for<PERSON>ach", "segment", "index", "segmentItem", "segmentHeader", "segmentTitle", "segmentDuration", "segmentRoute", "segmentDeparture", "depTime", "depAirport", "arrow", "segmentArrival", "arrTime", "arrAirport", "layover", "currentArrival", "getTime", "nextDeparture", "layoverTime", "Math", "floor", "offersSection", "offersList", "offer", "offerItem", "offerHeader", "offerTitle", "offerPrice", "offerDetails", "gridTemplateColumns", "availabilityValue", "availability", "undefined", "seatInfo", "availableSeatCount", "expires", "toLocaleString", "baggageTitle", "baggageContainer", "checkedBaggage", "filter", "b", "baggageType", "cabinBaggage", "handBaggage", "baggage", "baggageItem", "baggageIcon", "baggageInfo", "baggageDetails", "detailsText", "servicesSection", "servicesList", "listStyle", "service", "serviceItem", "iconClass", "section", "section<PERSON><PERSON><PERSON>", "icon", "className", "sectionTitle", "row", "labelElement", "valueElement", "offerId", "id", "searchId", "navigate", "queryParams", "error", "get", "getLocationsByType", "subscribe", "locations", "valueChanges", "locationType", "setValue", "pipe", "location", "toLowerCase", "includes", "displayLocation", "displayText", "Airport", "invalid", "markFormGroupTouched", "formValue", "request", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "ArrivalLocations", "Passengers", "count", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "searchPrice", "next", "response", "success", "flights", "JSON", "stringify", "flightsWithOffers", "f", "availabilityValues", "flatMap", "o", "availabilityCounts", "reduce", "acc", "val", "reservableFlights", "some", "groupEnd", "requestId", "Object", "keys", "messages", "message", "formGroup", "values", "controls", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "minutes", "hours", "mins", "dateString", "weekday", "day", "month", "min<PERSON>ffer", "formattedAmount", "showAllDepartureLocations", "input", "getElementById", "focus", "dispatchEvent", "Event", "showAllArrivalLocations", "swapLocations", "patchValue", "getBaggageTypeName", "Array", "isArray", "currentSegment", "nextSegment", "diffMs", "diffMins", "togglePassengerDropdown", "event", "stopPropagation", "setTimeout", "addEventListener", "increasePassengerCount", "currentCount", "decreasePassengerCount", "getFlightClassName", "flightClassValue", "find", "fc", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProductService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "SearchPriceComponent_Template", "rf", "ctx", "SearchPriceComponent_Template_form_ngSubmit_16_listener", "SearchPriceComponent_Template_input_click_37_listener", "SearchPriceComponent_mat_option_40_Template", "SearchPriceComponent_Template_button_click_53_listener", "SearchPriceComponent_Template_input_click_58_listener", "SearchPriceComponent_mat_option_61_Template", "SearchPriceComponent_Template_button_click_84_listener", "$event", "SearchPriceComponent_option_93_Template", "SearchPriceComponent_Template_button_click_100_listener", "SearchPriceComponent_Template_button_click_104_listener", "SearchPriceComponent_span_120_Template", "SearchPriceComponent_div_121_Template", "SearchPriceComponent_div_203_Template", "_r0", "bind", "_r2", "ɵɵtextInterpolate3", "tmp_18_0", "tmp_21_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { Observable, of } from 'rxjs';\nimport { ProductService } from '../../../services/product.service';\nimport { LocationOption } from '../../../models/price-search-request.model';\nimport { PriceSearchRequest } from '../../../models/price-search-request.model';\nimport { PriceSearchResponse, Flight } from '../../../models/price-search-response.model';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css', './search-card-new.css'],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchPriceComponent implements OnInit {\n  searchForm: FormGroup;\n  departureLocations: LocationOption[] = [];\n  arrivalLocations: LocationOption[] = [];\n  isLoading = false;\n  searchResults: Flight[] = [];\n  hasSearched = false;\n  errorMessage = '';\n  lastSearchId = '';\n  isPassengerDropdownOpen = false;\n\n  // Passenger type options\n  passengerTypes = [\n    { value: PassengerType.Adult, label: 'Adult' },\n    { value: PassengerType.Child, label: 'Child' },\n    { value: PassengerType.Infant, label: 'Infant' }\n  ];\n\n  // Flight class options\n  flightClasses = [\n    { value: FlightClassType.PROMO, label: 'Promo' },\n    { value: FlightClassType.ECONOMY, label: 'Economy' },\n    { value: FlightClassType.BUSINESS, label: 'Business' }\n  ];\n\n  // Date minimale (aujourd'hui)\n  minDate: string;\n\n  constructor(\n    private fb: FormBuilder,\n    private productService: ProductService,\n    private router: Router\n  ) {\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required], // Flight par défaut\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required], // Type 2 (City) par défaut\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required], // Type 5 (Airport) par défaut\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required], // Adult par défaut\n\n      // Options de vol\n      flightClass: [0, Validators.required], // PROMO par défaut\n      nonStop: [false],\n\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n\n  ngOnInit(): void {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight: Flight): void {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n\n    header.appendChild(logo);\n    header.appendChild(title);\n\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n\n        generalInfo.appendChild(airlineInfo);\n      }\n\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n\n      routeSection.appendChild(routeVisual);\n\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n\n          segmentsList.appendChild(segmentItem);\n\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n\n            segmentsList.appendChild(layover);\n          }\n        });\n\n        routeSection.appendChild(segmentsList);\n      }\n    }\n\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability :\n                                 (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n\n          const baggageContainer = document.createElement('div');\n          baggageContainer.style.display = 'flex';\n          baggageContainer.style.flexDirection = 'column';\n          baggageContainer.style.gap = '10px';\n          baggageContainer.style.marginBottom = '15px';\n\n          // Filtrer et regrouper les bagages par type\n          const checkedBaggage = offer.baggageInformations.filter(b => b.baggageType === 2);\n          const cabinBaggage = offer.baggageInformations.filter(b => b.baggageType === 1);\n          const handBaggage = offer.baggageInformations.filter(b => b.baggageType === 3);\n\n          // Bagages en soute\n          if (checkedBaggage.length > 0) {\n            checkedBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#e7f5ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #c5e1f9';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-suitcase\" style=\"color: #4a6fa5; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Checked Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n\n          // Bagages cabine\n          if (cabinBaggage.length > 0) {\n            cabinBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#f3f0ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #e5dbff';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Cabin Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          } else {\n            // Ajouter un bagage cabine par défaut si aucun n'est spécifié\n            const baggageItem = document.createElement('div');\n            baggageItem.style.display = 'flex';\n            baggageItem.style.alignItems = 'center';\n            baggageItem.style.padding = '10px 15px';\n            baggageItem.style.backgroundColor = '#f3f0ff';\n            baggageItem.style.borderRadius = '6px';\n            baggageItem.style.border = '1px solid #e5dbff';\n\n            const baggageIcon = document.createElement('div');\n            baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n\n            const baggageInfo = document.createElement('div');\n            baggageInfo.style.display = 'flex';\n            baggageInfo.style.flexDirection = 'column';\n\n            const baggageType = document.createElement('div');\n            baggageType.textContent = 'Cabin Baggage';\n            baggageType.style.fontWeight = '600';\n            baggageType.style.fontSize = '14px';\n\n            const baggageDetails = document.createElement('div');\n            baggageDetails.style.fontSize = '12px';\n            baggageDetails.style.color = '#666';\n            baggageDetails.textContent = 'Included';\n\n            baggageInfo.appendChild(baggageType);\n            baggageInfo.appendChild(baggageDetails);\n\n            baggageItem.appendChild(baggageIcon);\n            baggageItem.appendChild(baggageInfo);\n            baggageContainer.appendChild(baggageItem);\n          }\n\n          // Bagages à main\n          if (handBaggage.length > 0) {\n            handBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#fff4e6';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #ffe8cc';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-shopping-bag\" style=\"color: #e8590c; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Hand Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n\n          offerItem.appendChild(baggageContainer);\n        }\n\n        offerItem.appendChild(offerDetails);\n\n        offersList.appendChild(offerItem);\n      });\n\n      offersSection.appendChild(offersList);\n    }\n\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n\n  // Méthode utilitaire pour créer une section\n  private createSection(title: string, iconClass: string): HTMLElement {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n\n    return section;\n  }\n\n  // Méthode utilitaire pour créer une ligne d'information\n  private createInfoRow(label: string, value: string): HTMLElement {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n\n    return row;\n  }\n\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight: Flight): void {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n\n  preloadLocations(): void {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n\n  setupAutocomplete(): void {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.departureLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('departureLocation')?.setValue('');\n        });\n      });\n\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.arrivalLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('arrivalLocation')?.setValue('');\n        });\n      });\n\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.departureLocations = locations;\n      });\n\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n  }\n\n  displayLocation(location: LocationOption | null): string {\n    if (!location) return '';\n\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n\n  onSearch(): void {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n\n    const formValue = this.searchForm.value;\n\n    // Créer la requête de recherche entièrement dynamique\n    const request: PriceSearchRequest = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [\n        {\n          id: formValue.departureLocation?.id || '',\n          type: formValue.departureLocationType\n        }\n      ],\n      ArrivalLocations: [\n        {\n          id: formValue.arrivalLocation?.id || '',\n          type: formValue.arrivalLocationType\n        }\n      ],\n      Passengers: [\n        {\n          type: formValue.passengerType,\n          count: formValue.passengerCount\n        }\n      ],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n\n    this.productService.searchPrice(request)\n      .subscribe({\n        next: (response: PriceSearchResponse) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            console.log('Full API Response:', JSON.stringify(response, null, 2));\n\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o =>\n                o.availability !== undefined ? o.availability : (o.seatInfo ? o.seatInfo.availableSeatCount : 0)\n              ));\n              console.log('Availability values:', availabilityValues);\n\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc, val) => {\n                if (val !== undefined) {\n                  acc[val] = (acc[val] || 0) + 1;\n                }\n                return acc;\n              }, {} as Record<number, number>);\n              console.log('Availability counts:', availabilityCounts);\n\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f =>\n                f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true)\n              );\n              console.log('Reservable flights:', reservableFlights.length);\n\n              console.groupEnd();\n            }\n\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n  }\n\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup: FormGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n\n  // Formater la durée en heures et minutes\n  formatDuration(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n\n  // Formater la date pour l'affichage\n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight: Flight): string {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n\n    const minOffer = flight.offers.reduce((min, offer) =>\n      offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight: Flight): boolean {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability :\n                             (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n    return availabilityValue > 0;\n  }\n\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations(): void {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations(): void {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations(): void {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType: number): string {\n    switch (baggageType) {\n      case 1: return 'Cabin Baggage';\n      case 2: return 'Checked Baggage';\n      case 3: return 'Hand Baggage';\n      default: return 'Baggage';\n    }\n  }\n\n  // Filtrer les bagages par type\n  filterBaggageByType(baggageInformations: any[], type: number): any[] {\n    if (!baggageInformations || !Array.isArray(baggageInformations)) {\n      return [];\n    }\n    return baggageInformations.filter(baggage => baggage.baggageType === type);\n  }\n\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType: number): string {\n    switch (passengerType) {\n      case 1: return 'Adult';\n      case 2: return 'Child';\n      case 3: return 'Infant';\n      default: return 'Passenger';\n    }\n  }\n\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment: any, nextSegment: any): string {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date ||\n        !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n\n  // Méthodes pour le nouveau design de recherche\n  togglePassengerDropdown(event: Event): void {\n    event.stopPropagation();\n    this.isPassengerDropdownOpen = !this.isPassengerDropdownOpen;\n\n    // Fermer le dropdown quand on clique ailleurs\n    if (this.isPassengerDropdownOpen) {\n      setTimeout(() => {\n        document.addEventListener('click', this.closePassengerDropdown);\n      }, 0);\n    }\n  }\n\n  closePassengerDropdown = (): void => {\n    this.isPassengerDropdownOpen = false;\n    document.removeEventListener('click', this.closePassengerDropdown);\n  }\n\n  increasePassengerCount(): void {\n    const currentCount = this.searchForm.get('passengerCount')?.value || 1;\n    if (currentCount < 9) {\n      this.searchForm.get('passengerCount')?.setValue(currentCount + 1);\n    }\n  }\n\n  decreasePassengerCount(): void {\n    const currentCount = this.searchForm.get('passengerCount')?.value || 1;\n    if (currentCount > 1) {\n      this.searchForm.get('passengerCount')?.setValue(currentCount - 1);\n    }\n  }\n\n  getFlightClassName(): string {\n    const flightClassValue = this.searchForm.get('flightClass')?.value;\n    const flightClass = this.flightClasses.find(fc => fc.value === flightClassValue);\n    return flightClass ? flightClass.label : 'Économie';\n  }\n}\n", "<div class=\"search-price-container\">\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <h1 class=\"page-title\">Find Your Perfect Flight</h1>\n      <p class=\"page-subtitle\">Search and compare flights to destinations worldwide</p>\n    </div>\n    <div class=\"header-illustration\">\n      <img src=\"assets/images/airplane-banner.jpg\" alt=\"Airplane in the sky\">\n    </div>\n  </div>\n\n  <div class=\"search-content\">\n    <div class=\"search-form-container\">\n      <!-- Logo pour la version desktop -->\n      <div class=\"sidebar-logo\">\n        <div class=\"logo-container\">\n          <i class=\"fas fa-plane-departure logo-icon\"></i>\n          <span class=\"logo-text\">TravelEase</span>\n        </div>\n      </div>\n\n      <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSearch()\" class=\"search-form\">\n        <!-- Main Search Fields -->\n        <div class=\"search-card\">\n          <!-- Hidden Product Type and Service Types -->\n          <input type=\"hidden\" formControlName=\"productType\" value=\"3\">\n          <input type=\"hidden\" formControlName=\"serviceTypes\" value=\"['1']\">\n\n          <!-- Trip Type Selection -->\n          <div class=\"trip-type-selector\">\n            <div class=\"trip-type-option\">\n              <input type=\"radio\" id=\"roundTrip\" name=\"tripType\" value=\"roundTrip\" checked>\n              <label for=\"roundTrip\">Aller-retour</label>\n            </div>\n            <div class=\"trip-type-option\">\n              <input type=\"radio\" id=\"oneWay\" name=\"tripType\" value=\"oneWay\">\n              <label for=\"oneWay\">Aller simple</label>\n            </div>\n            <div class=\"trip-type-option\">\n              <input type=\"radio\" id=\"multiCity\" name=\"tripType\" value=\"multiCity\">\n              <label for=\"multiCity\">Multi-destinations</label>\n            </div>\n          </div>\n\n          <!-- Horizontal Search Form -->\n          <div class=\"horizontal-search-form\">\n            <!-- Departure Location -->\n            <div class=\"search-field location-field\">\n              <label for=\"departureLocation\">De</label>\n              <input\n                type=\"text\"\n                id=\"departureLocation\"\n                formControlName=\"departureLocation\"\n                placeholder=\"Pays, ville ou aéroport\"\n                [matAutocomplete]=\"departureAuto\"\n                class=\"form-control\"\n                (click)=\"showAllDepartureLocations()\"\n              >\n              <mat-autocomplete #departureAuto=\"matAutocomplete\" [displayWith]=\"displayLocation.bind(this)\">\n                <mat-option *ngFor=\"let location of departureLocations\" [value]=\"location\">\n                  <div class=\"location-option\">\n                    <div class=\"location-name\">{{ location.name }}</div>\n                    <div class=\"location-details\">\n                      <span *ngIf=\"location.code\" class=\"location-code\">{{ location.code }}</span>\n                      <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">{{ location.city }}</span>\n                      <span class=\"location-type\">\n                        <i class=\"fas\"\n                          [ngClass]=\"{\n                            'fa-flag': location.type === 1,\n                            'fa-city': location.type === 2,\n                            'fa-building': location.type === 3,\n                            'fa-home': location.type === 4,\n                            'fa-plane': location.type === 5\n                          }\"></i>\n                        {{ location.type === 1 ? 'Pays' :\n                           location.type === 2 ? 'Ville' :\n                           location.type === 3 ? 'Ville' :\n                           location.type === 4 ? 'Village' :\n                           location.type === 5 ? 'Aéroport' : '' }}\n                      </span>\n                    </div>\n                  </div>\n                </mat-option>\n              </mat-autocomplete>\n              <!-- Hidden Location Type Selector -->\n              <select\n                id=\"departureLocationType\"\n                formControlName=\"departureLocationType\"\n                class=\"form-control location-type-selector\"\n                aria-label=\"Departure Location Type\"\n              >\n                <option [value]=\"1\">Country (1)</option>\n                <option [value]=\"2\">City (2)</option>\n                <option [value]=\"3\">Town (3)</option>\n                <option [value]=\"4\">Village (4)</option>\n                <option [value]=\"5\">Airport (5)</option>\n              </select>\n            </div>\n\n            <!-- Swap Button -->\n            <div class=\"swap-button-container\">\n              <button type=\"button\" class=\"swap-locations-btn\" (click)=\"swapLocations()\">\n                <i class=\"fas fa-exchange-alt\"></i>\n              </button>\n            </div>\n\n            <!-- Arrival Location -->\n            <div class=\"search-field location-field\">\n              <label for=\"arrivalLocation\">À</label>\n              <input\n                type=\"text\"\n                id=\"arrivalLocation\"\n                formControlName=\"arrivalLocation\"\n                placeholder=\"Pays, ville ou aéroport\"\n                [matAutocomplete]=\"arrivalAuto\"\n                class=\"form-control\"\n                (click)=\"showAllArrivalLocations()\"\n              >\n              <mat-autocomplete #arrivalAuto=\"matAutocomplete\" [displayWith]=\"displayLocation.bind(this)\">\n                <mat-option *ngFor=\"let location of arrivalLocations\" [value]=\"location\">\n                  <div class=\"location-option\">\n                    <div class=\"location-name\">{{ location.name }}</div>\n                    <div class=\"location-details\">\n                      <span *ngIf=\"location.code\" class=\"location-code\">{{ location.code }}</span>\n                      <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">{{ location.city }}</span>\n                      <span class=\"location-type\">\n                        <i class=\"fas\"\n                          [ngClass]=\"{\n                            'fa-flag': location.type === 1,\n                            'fa-city': location.type === 2,\n                            'fa-building': location.type === 3,\n                            'fa-home': location.type === 4,\n                            'fa-plane': location.type === 5\n                          }\"></i>\n                        {{ location.type === 1 ? 'Pays' :\n                           location.type === 2 ? 'Ville' :\n                           location.type === 3 ? 'Ville' :\n                           location.type === 4 ? 'Village' :\n                           location.type === 5 ? 'Aéroport' : '' }}\n                      </span>\n                    </div>\n                  </div>\n                </mat-option>\n              </mat-autocomplete>\n              <!-- Hidden Location Type Selector -->\n              <select\n                id=\"arrivalLocationType\"\n                formControlName=\"arrivalLocationType\"\n                class=\"form-control location-type-selector\"\n                aria-label=\"Arrival Location Type\"\n              >\n                <option [value]=\"1\">Country (1)</option>\n                <option [value]=\"2\">City (2)</option>\n                <option [value]=\"3\">Town (3)</option>\n                <option [value]=\"4\">Village (4)</option>\n                <option [value]=\"5\">Airport (5)</option>\n              </select>\n            </div>\n\n            <!-- Departure Date -->\n            <div class=\"search-field date-field\">\n              <label for=\"departureDate\">Départ</label>\n              <input\n                type=\"date\"\n                id=\"departureDate\"\n                formControlName=\"departureDate\"\n                [min]=\"minDate\"\n                class=\"form-control\"\n              >\n            </div>\n\n            <!-- Return Date (disabled for one-way) -->\n            <div class=\"search-field date-field\">\n              <label for=\"returnDate\">Retour</label>\n              <input\n                type=\"text\"\n                id=\"returnDate\"\n                class=\"form-control\"\n                placeholder=\"(Aller simple)\"\n                disabled\n              >\n            </div>\n\n            <!-- Passengers and Class Dropdown -->\n            <div class=\"search-field passengers-field\">\n              <label for=\"passengerDropdown\">Voyageurs et classe</label>\n              <button type=\"button\" class=\"dropdown-toggle form-control\" id=\"passengerDropdown\" (click)=\"togglePassengerDropdown($event)\">\n                {{ searchForm.get('passengerCount')?.value }} {{ searchForm.get('passengerCount')?.value > 1 ? 'adultes' : 'adulte' }}, {{ getFlightClassName() }}\n              </button>\n              <div class=\"dropdown-menu passenger-dropdown\" [class.show]=\"isPassengerDropdownOpen\">\n                <div class=\"dropdown-header\">Classe de cabine</div>\n                <div class=\"cabin-class-info\">\n                  Nous pouvons seulement afficher les prix en classe économique pour cette recherche.\n                  Pour voir les options pour la classe affaires, la classe économique premium et la première classe, veuillez indiquer les dates et la destination de votre voyage.\n                </div>\n\n                <!-- Flight Class Selection -->\n                <div class=\"flight-class-selection\">\n                  <select\n                    id=\"flightClass\"\n                    formControlName=\"flightClass\"\n                    class=\"form-control\"\n                  >\n                    <option *ngFor=\"let flightClass of flightClasses\" [value]=\"flightClass.value\">{{ flightClass.label }}</option>\n                  </select>\n                </div>\n\n                <!-- Passenger Types -->\n                <div class=\"passenger-type\">\n                  <div class=\"passenger-type-header\">Adultes</div>\n                  <div class=\"passenger-type-description\">18 ans et plus</div>\n                  <div class=\"passenger-count-control\">\n                    <button type=\"button\" class=\"passenger-count-btn decrease\" (click)=\"decreasePassengerCount()\">\n                      <i class=\"fas fa-minus\"></i>\n                    </button>\n                    <span class=\"passenger-count\">{{ searchForm.get('passengerCount')?.value }}</span>\n                    <button type=\"button\" class=\"passenger-count-btn increase\" (click)=\"increasePassengerCount()\">\n                      <i class=\"fas fa-plus\"></i>\n                    </button>\n                  </div>\n                </div>\n\n                <div class=\"passenger-type\">\n                  <div class=\"passenger-type-header\">Enfants</div>\n                  <div class=\"passenger-type-description\">0 à 17 ans</div>\n                  <div class=\"passenger-count-control\">\n                    <button type=\"button\" class=\"passenger-count-btn decrease\" disabled>\n                      <i class=\"fas fa-minus\"></i>\n                    </button>\n                    <span class=\"passenger-count\">0</span>\n                    <button type=\"button\" class=\"passenger-count-btn increase\" disabled>\n                      <i class=\"fas fa-plus\"></i>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Search Button -->\n            <div class=\"search-button-container\">\n              <button\n                type=\"submit\"\n                class=\"search-button\"\n                [disabled]=\"searchForm.invalid || isLoading\"\n              >\n                <span *ngIf=\"!isLoading\">Rechercher</span>\n                <div *ngIf=\"isLoading\" class=\"spinner-container\">\n                  <div class=\"spinner\"></div>\n                </div>\n              </button>\n            </div>\n          </div>\n\n          <!-- Additional Options -->\n          <div class=\"additional-options\">\n            <div class=\"option-checkbox\">\n              <input type=\"checkbox\" id=\"nearbyAirportsDeparture\" class=\"custom-checkbox\">\n              <label for=\"nearbyAirportsDeparture\">Ajouter des aéroports à proximité</label>\n            </div>\n\n            <div class=\"option-checkbox\">\n              <input type=\"checkbox\" id=\"nearbyAirportsArrival\" class=\"custom-checkbox\">\n              <label for=\"nearbyAirportsArrival\">Ajouter des aéroports à proximité</label>\n            </div>\n\n            <div class=\"option-checkbox\">\n              <input type=\"checkbox\" id=\"directFlightsOnly\" formControlName=\"nonStop\" class=\"custom-checkbox\">\n              <label for=\"directFlightsOnly\">Vols directs uniquement</label>\n            </div>\n          </div>\n        </div>\n\n        <!-- Advanced Options -->\n        <div class=\"advanced-options-container\">\n          <details>\n            <summary>\n              <i class=\"fas fa-cog\"></i> Advanced Options\n            </summary>\n            <div class=\"advanced-options\">\n              <div class=\"form-row\">\n                <!-- Culture -->\n                <div class=\"form-group\">\n                  <label for=\"culture\">Language</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-language\"></i>\n                    <select\n                      id=\"culture\"\n                      formControlName=\"culture\"\n                      class=\"form-control\"\n                    >\n                      <option value=\"en-US\">English (US)</option>\n                      <option value=\"fr-FR\">Français</option>\n                    </select>\n                  </div>\n                </div>\n\n                <!-- Currency -->\n                <div class=\"form-group\">\n                  <label for=\"currency\">Currency</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-money-bill-wave\"></i>\n                    <select\n                      id=\"currency\"\n                      formControlName=\"currency\"\n                      class=\"form-control\"\n                    >\n                      <option value=\"EUR\">Euro (€)</option>\n                      <option value=\"USD\">Dollar ($)</option>\n                    </select>\n                  </div>\n                </div>\n\n                <!-- Baggage Options -->\n                <div class=\"form-group\">\n                  <label for=\"flightBaggageGetOption\">Baggage Options</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-suitcase\"></i>\n                    <select\n                      id=\"flightBaggageGetOption\"\n                      formControlName=\"flightBaggageGetOption\"\n                      class=\"form-control\"\n                    >\n                      <option [value]=\"0\">All options</option>\n                      <option [value]=\"1\">Baggage included only</option>\n                      <option [value]=\"2\">No baggage only</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Additional Options -->\n              <div class=\"form-row checkbox-options\">\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"acceptPendingProviders\"\n                      formControlName=\"acceptPendingProviders\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"acceptPendingProviders\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Accept pending providers</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"forceFlightBundlePackage\"\n                      formControlName=\"forceFlightBundlePackage\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"forceFlightBundlePackage\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Force flight bundle package</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"disablePackageOfferTotalPrice\"\n                      formControlName=\"disablePackageOfferTotalPrice\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"disablePackageOfferTotalPrice\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Disable package offer total price</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"calculateFlightFees\"\n                      formControlName=\"calculateFlightFees\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"calculateFlightFees\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Calculate flight fees</span>\n                    </label>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </details>\n        </div>\n      </form>\n    </div>\n\n    <!-- Search Results -->\n    <div class=\"search-results-container\" *ngIf=\"hasSearched\">\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-animation\">\n          <div class=\"plane-loader\">\n            <i class=\"fas fa-plane\"></i>\n            <div class=\"cloud\"></div>\n            <div class=\"cloud\"></div>\n            <div class=\"cloud\"></div>\n          </div>\n          <p>Searching for the best flights...</p>\n        </div>\n      </div>\n\n      <div *ngIf=\"!isLoading && errorMessage\" class=\"error-container\">\n        <div class=\"error-icon\">\n          <i class=\"fas fa-exclamation-circle\"></i>\n        </div>\n        <h3>Oops! Something went wrong</h3>\n        <p class=\"error-message\">{{ errorMessage }}</p>\n        <button class=\"retry-button\" (click)=\"onSearch()\">\n          <i class=\"fas fa-redo\"></i> Try Again\n        </button>\n      </div>\n\n      <div *ngIf=\"!isLoading && !errorMessage\" class=\"search-results-content\">\n        <div class=\"results-header\">\n          <div class=\"results-title\">\n            <h3>Flight Options</h3>\n            <p *ngIf=\"searchResults.length === 0\">No flights found for your search. Please modify your criteria.</p>\n            <p *ngIf=\"searchResults.length > 0\">\n              <span class=\"results-count\">{{ searchResults.length }}</span> flights found\n            </p>\n          </div>\n\n          <div class=\"results-filters\" *ngIf=\"searchResults.length > 0\">\n            <div class=\"filter-option\">\n              <i class=\"fas fa-sort-amount-down\"></i>\n              <span>Sort by:</span>\n              <select class=\"filter-select\">\n                <option value=\"price\">Price (lowest first)</option>\n                <option value=\"duration\">Duration (shortest first)</option>\n                <option value=\"departure\">Departure (earliest first)</option>\n                <option value=\"arrival\">Arrival (earliest first)</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"flight-list\">\n          <div *ngFor=\"let flight of searchResults\" class=\"flight-card\" [class.unavailable]=\"!isFlightAvailable(flight)\">\n            <div class=\"flight-header\">\n              <div class=\"airline-info\">\n                <div class=\"airline-logo-container\">\n                  <img *ngIf=\"flight.items && flight.items[0] && flight.items[0].airline && flight.items[0].airline.thumbnailFull\"\n                       [src]=\"flight.items[0].airline.thumbnailFull\"\n                       alt=\"Airline logo\"\n                       class=\"airline-logo\">\n                  <i *ngIf=\"!(flight.items && flight.items[0] && flight.items[0].airline && flight.items[0].airline.thumbnailFull)\"\n                     class=\"fas fa-plane airline-icon\"></i>\n                </div>\n                <div class=\"airline-details\">\n                  <span class=\"airline-name\">{{ flight.items && flight.items[0] && flight.items[0].airline ? flight.items[0].airline.name : 'Airline' }}</span>\n                  <span class=\"flight-number\">{{ flight.items && flight.items[0] ? flight.items[0].flightNo : 'N/A' }}</span>\n                  <!-- Provider information -->\n                  <span class=\"provider-info\" *ngIf=\"flight.provider || (flight.items && flight.items[0] && flight.items[0].flightProvider)\">\n                    <i class=\"fas fa-tag\"></i>\n                    {{ (flight.items && flight.items[0] && flight.items[0].flightProvider && flight.items[0].flightProvider.displayName) ||\n                       (flight.items && flight.items[0] && flight.items[0].flightProvider && flight.items[0].flightProvider.name) ||\n                       'Provider ' + flight.provider }}\n                  </span>\n                </div>\n              </div>\n\n              <div class=\"flight-badges\">\n                <span class=\"flight-badge\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount === 0\">\n                  <i class=\"fas fa-bolt\"></i> Direct\n                </span>\n                <span class=\"flight-badge\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].flightClass\">\n                  <i class=\"fas fa-chair\"></i> {{ flight.items[0].flightClass.name }}\n                </span>\n                <!-- Branded fare badge -->\n                <span class=\"flight-badge branded\" *ngIf=\"flight.offers && flight.offers[0] && flight.offers[0].hasBrand\">\n                  <i class=\"fas fa-certificate\"></i> Branded Fare\n                </span>\n              </div>\n\n              <div class=\"flight-price\">\n                <span class=\"price-label\">Price per person</span>\n                <span class=\"price\">{{ getMinPrice(flight) }}</span>\n                <!-- Availability information - Afficher Available ou Not available -->\n                <span class=\"availability\" *ngIf=\"flight.offers && flight.offers.length > 0\">\n                  <i class=\"fas\" [ngClass]=\"{'fa-check-circle': isFlightAvailable(flight), 'fa-exclamation-triangle': !isFlightAvailable(flight)}\"></i>\n                  {{ isFlightAvailable(flight) ? 'Available' : 'Not available' }}\n                </span>\n\n                <!-- Expiration information - Afficher exactement la valeur de l'API -->\n                <span class=\"expiration\" *ngIf=\"flight.offers && flight.offers.length > 0 && flight.offers[0].expiresOn\">\n                  <i class=\"fas fa-clock\"></i> Expires: {{ formatExpirationDate(flight.offers[0].expiresOn) }}\n                </span>\n              </div>\n            </div>\n\n            <div class=\"flight-details\">\n              <div class=\"flight-route\">\n                <div class=\"departure\">\n                  <div class=\"time\">{{ flight.items && flight.items[0] && flight.items[0].departure ? formatDate(flight.items[0].departure.date) : 'N/A' }}</div>\n                  <div class=\"location\">\n                    <span class=\"airport-code\">{{ flight.items && flight.items[0] && flight.items[0].departure && flight.items[0].departure.airport ? flight.items[0].departure.airport.code : 'N/A' }}</span>\n                    <span class=\"city-name\">{{ flight.items && flight.items[0] && flight.items[0].departure && flight.items[0].departure.city ? flight.items[0].departure.city.name : 'N/A' }}</span>\n                  </div>\n                </div>\n\n                <div class=\"flight-duration\">\n                  <div class=\"duration-line\">\n                    <span class=\"dot departure-dot\"></span>\n                    <div class=\"line-container\">\n                      <span class=\"line\"></span>\n                      <span class=\"plane-icon\">\n                        <i class=\"fas fa-plane\"></i>\n                      </span>\n                    </div>\n                    <span class=\"dot arrival-dot\"></span>\n                  </div>\n                  <div class=\"duration-text\">\n                    <i class=\"fas fa-clock\"></i>\n                    {{ flight.items && flight.items[0] ? formatDuration(flight.items[0].duration) : 'N/A' }}\n                  </div>\n                  <div class=\"stops\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount > 0\">\n                    <span class=\"stop-count\">{{ flight.items[0].stopCount }}</span> stop{{ flight.items[0].stopCount > 1 ? 's' : '' }}\n                  </div>\n                  <div class=\"stops direct\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount === 0\">\n                    Direct flight\n                  </div>\n                </div>\n\n                <div class=\"arrival\">\n                  <div class=\"time\">{{ flight.items && flight.items[0] && flight.items[0].arrival ? formatDate(flight.items[0].arrival.date) : 'N/A' }}</div>\n                  <div class=\"location\">\n                    <span class=\"airport-code\">{{ flight.items && flight.items[0] && flight.items[0].arrival && flight.items[0].arrival.airport ? flight.items[0].arrival.airport.code : 'N/A' }}</span>\n                    <span class=\"city-name\">{{ flight.items && flight.items[0] && flight.items[0].arrival && flight.items[0].arrival.city ? flight.items[0].arrival.city.name : 'N/A' }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"flight-features\">\n                <!-- Baggage Information - Amélioré avec plus de détails et meilleure organisation -->\n                <div *ngIf=\"flight.items && flight.items[0]\" class=\"feature-group\">\n                  <h4><i class=\"fas fa-suitcase\"></i> Baggage</h4>\n                  <div class=\"baggage-details\">\n                    <!-- Bagages en soute (type 2) -->\n                    <ng-container *ngIf=\"flight.items[0].baggageInformations && flight.items[0].baggageInformations.length > 0\">\n                      <div class=\"baggage-item checked\"\n                           *ngFor=\"let baggage of filterBaggageByType(flight.items[0].baggageInformations, 2) | slice:0:2\">\n                        <i class=\"fas fa-suitcase\"></i>\n                        <div class=\"baggage-info\">\n                          <span class=\"baggage-type\">Checked Baggage</span>\n                          <span class=\"baggage-specs\" *ngIf=\"baggage.weight > 0\">{{ baggage.weight }} kg</span>\n                          <span class=\"baggage-specs\" *ngIf=\"baggage.piece > 0\">{{ baggage.piece }} piece(s)</span>\n                        </div>\n                      </div>\n                    </ng-container>\n\n                    <!-- Bagages cabine (type 1) -->\n                    <ng-container *ngIf=\"flight.items[0].baggageInformations && flight.items[0].baggageInformations.length > 0\">\n                      <div class=\"baggage-item cabin\"\n                           *ngFor=\"let baggage of filterBaggageByType(flight.items[0].baggageInformations, 1) | slice:0:1\">\n                        <i class=\"fas fa-briefcase\"></i>\n                        <div class=\"baggage-info\">\n                          <span class=\"baggage-type\">Cabin Baggage</span>\n                          <span class=\"baggage-specs\" *ngIf=\"baggage.piece > 0\">{{ baggage.piece }} piece(s)</span>\n                          <span class=\"baggage-specs\" *ngIf=\"baggage.weight > 0\">{{ baggage.weight }} kg</span>\n                        </div>\n                      </div>\n                    </ng-container>\n\n                    <!-- Bagage cabine par défaut si aucun n'est spécifié -->\n                    <div class=\"baggage-item cabin\" *ngIf=\"!flight.items[0].baggageInformations ||\n                                                           flight.items[0].baggageInformations.length === 0 ||\n                                                           filterBaggageByType(flight.items[0].baggageInformations, 1).length === 0\">\n                      <i class=\"fas fa-briefcase\"></i>\n                      <div class=\"baggage-info\">\n                        <span class=\"baggage-type\">Cabin Baggage</span>\n                        <span class=\"baggage-specs\">Included</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Message si aucune information de bagage n'est disponible -->\n                  <div *ngIf=\"!flight.items[0].baggageInformations || flight.items[0].baggageInformations.length === 0\" class=\"feature\">\n                    <span>No detailed baggage information available</span>\n                  </div>\n                </div>\n\n                <!-- Services Information -->\n                <div *ngIf=\"flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0\" class=\"feature-group\">\n                  <h4><i class=\"fas fa-concierge-bell\"></i> Services</h4>\n                  <div *ngFor=\"let service of flight.items[0].services | slice:0:3\" class=\"feature\">\n                    <span>{{ service.name || 'Service' }}</span>\n                  </div>\n                </div>\n\n                <!-- Offer Information - Clarification de la réservabilité -->\n                <div *ngIf=\"flight.offers && flight.offers.length > 0\" class=\"feature-group\">\n                  <h4><i class=\"fas fa-tag\"></i> Offer Details</h4>\n                  <div class=\"feature\" *ngIf=\"flight.offers[0].reservableInfo\">\n                    <span>\n                      <i [class]=\"flight.offers[0].reservableInfo.reservable ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger'\"></i>\n                      {{ flight.offers[0].reservableInfo.reservable ? 'Reservable' : 'Not reservable' }}\n                      <small class=\"source-info\">(from API response)</small>\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Price Breakdown Section (Collapsible) -->\n            <div class=\"price-breakdown-section\">\n              <details class=\"price-breakdown-details\">\n                <summary class=\"price-breakdown-summary\">\n                  <i class=\"fas fa-money-bill-wave\"></i> Price Breakdown\n                </summary>\n                <div class=\"price-breakdown-content\" *ngIf=\"flight.offers && flight.offers[0]\">\n                  <!-- Price Breakdown -->\n                  <div class=\"breakdown-group\" *ngIf=\"flight.offers[0].priceBreakDown && flight.offers[0].priceBreakDown.items\">\n                    <h4>Price Details</h4>\n                    <div class=\"breakdown-item\" *ngFor=\"let item of flight.offers[0].priceBreakDown.items\">\n                      <span class=\"passenger-type\">\n                        {{ getPassengerTypeName(item.passengerType) }} (x{{ item.passengerCount }})\n                      </span>\n                      <span class=\"item-price\">{{ item.price.amount | currency:item.price.currency }}</span>\n                    </div>\n                    <div class=\"breakdown-item service-fee\" *ngIf=\"flight.offers[0].serviceFee\">\n                      <span class=\"fee-label\">Service Fee</span>\n                      <span class=\"fee-amount\">{{ flight.offers[0].serviceFee.amount | currency:flight.offers[0].serviceFee.currency }}</span>\n                    </div>\n                    <div class=\"breakdown-total\">\n                      <span class=\"total-label\">Total</span>\n                      <span class=\"total-amount\">{{ flight.offers[0].price.amount | currency:flight.offers[0].price.currency }}</span>\n                    </div>\n                  </div>\n                </div>\n              </details>\n            </div>\n\n            <!-- Segment Information for Connecting Flights (Collapsible) -->\n            <div class=\"segments-section\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].segments && flight.items[0].segments.length > 1\">\n              <details class=\"segments-details\">\n                <summary class=\"segments-summary\">\n                  <i class=\"fas fa-plane-departure\"></i> Flight Segments ({{ flight.items[0].segments.length }})\n                </summary>\n                <div class=\"segments-content\">\n                  <div class=\"segment-item\" *ngFor=\"let segment of flight.items[0].segments; let i = index\">\n                    <div class=\"segment-header\">\n                      <span class=\"segment-number\">Segment {{ i + 1 }}</span>\n                      <span class=\"segment-airline\">{{ segment.airline && segment.airline.name || 'Airline' }}</span>\n                      <span class=\"segment-flight\">{{ segment.flightNo }}</span>\n                    </div>\n                    <div class=\"segment-route\">\n                      <div class=\"segment-departure\">\n                        <div class=\"time\">{{ segment.departure ? formatDate(segment.departure.date) : 'N/A' }}</div>\n                        <div class=\"location\">\n                          <span class=\"airport-code\">{{ segment.departure && segment.departure.airport && segment.departure.airport.code || 'N/A' }}</span>\n                          <span class=\"city-name\">{{ segment.departure && segment.departure.city && segment.departure.city.name || 'N/A' }}</span>\n                        </div>\n                      </div>\n                      <div class=\"segment-duration\">\n                        <div class=\"duration-line\">\n                          <span class=\"dot departure-dot\"></span>\n                          <div class=\"line-container\">\n                            <span class=\"line\"></span>\n                          </div>\n                          <span class=\"dot arrival-dot\"></span>\n                        </div>\n                        <div class=\"duration-text\">\n                          <i class=\"fas fa-clock\"></i> {{ formatDuration(segment.duration) }}\n                        </div>\n                      </div>\n                      <div class=\"segment-arrival\">\n                        <div class=\"time\">{{ segment.arrival ? formatDate(segment.arrival.date) : 'N/A' }}</div>\n                        <div class=\"location\">\n                          <span class=\"airport-code\">{{ segment.arrival && segment.arrival.airport && segment.arrival.airport.code || 'N/A' }}</span>\n                          <span class=\"city-name\">{{ segment.arrival && segment.arrival.city && segment.arrival.city.name || 'N/A' }}</span>\n                        </div>\n                      </div>\n                    </div>\n                    <!-- Layover information if not the last segment -->\n                    <div class=\"layover-info\" *ngIf=\"i < flight.items[0].segments.length - 1\">\n                      <i class=\"fas fa-hourglass-half\"></i>\n                      <span>{{ calculateLayoverTime(segment, flight.items[0].segments[i+1]) }} layover in {{ segment.arrival && segment.arrival.city && segment.arrival.city.name || 'connecting city' }}</span>\n                    </div>\n                  </div>\n                </div>\n              </details>\n            </div>\n\n            <!-- Branded Fare Information (if available) -->\n            <div class=\"branded-fare-section\" *ngIf=\"flight.offers && flight.offers[0] && flight.offers[0].brandedFare\">\n              <details class=\"branded-fare-details\">\n                <summary class=\"branded-fare-summary\">\n                  <i class=\"fas fa-certificate\"></i> {{ flight.offers[0].brandedFare.name || 'Branded Fare' }} Details\n                </summary>\n                <div class=\"branded-fare-content\">\n                  <div class=\"branded-fare-description\" *ngIf=\"flight.offers[0].brandedFare.description\">\n                    {{ flight.offers[0].brandedFare.description }}\n                  </div>\n                  <div class=\"branded-fare-features\" *ngIf=\"flight.offers[0].brandedFare.features && flight.offers[0].brandedFare.features.length > 0\">\n                    <h4>Features</h4>\n                    <div class=\"feature-item\" *ngFor=\"let feature of flight.offers[0].brandedFare.features\">\n                      <div class=\"feature-name\">{{ feature.commercialName || 'Feature' }}</div>\n                      <div class=\"feature-description\" *ngIf=\"feature.explanations && feature.explanations.length > 0\">\n                        {{ feature.explanations[0].text }}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </details>\n            </div>\n\n            <div class=\"flight-actions\">\n              <button class=\"view-details-button\" (click)=\"showAllDetails(flight)\">\n                <i class=\"fas fa-info-circle\"></i>\n                View All Details\n              </button>\n              <button class=\"select-button\" [disabled]=\"!flight.offers || flight.offers.length === 0 || !isFlightAvailable(flight)\" (click)=\"selectThisFlight(flight)\">\n                <i class=\"fas fa-check-circle\"></i>\n                Select This Flight\n              </button>\n            </div>\n          </div>\n\n          <!-- No Results Message -->\n          <div class=\"no-results\" *ngIf=\"!isLoading && !errorMessage && searchResults.length === 0\">\n            <div class=\"no-results-icon\">\n              <i class=\"fas fa-search\"></i>\n            </div>\n            <h3>No flights found</h3>\n            <p>We couldn't find any flights matching your search criteria. Try adjusting your search parameters.</p>\n            <div class=\"no-results-suggestions\">\n              <div class=\"suggestion\">\n                <i class=\"fas fa-calendar-alt\"></i>\n                <span>Try different dates</span>\n              </div>\n              <div class=\"suggestion\">\n                <i class=\"fas fa-map-marker-alt\"></i>\n                <span>Try nearby airports</span>\n              </div>\n              <div class=\"suggestion\">\n                <i class=\"fas fa-plane\"></i>\n                <span>Include flights with stops</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnF,SAAqBC,EAAE,QAAQ,MAAM;AAKrC,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;;;;;;;;;;ICsDpEC,EAAA,CAAAC,cAAA,gBAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,WAAA,CAAAC,IAAA,CAAmB;;;;;IACrEP,EAAA,CAAAC,cAAA,gBAAyE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,WAAA,CAAAE,IAAA,CAAmB;;;;;;;;;;;;;;IALlGR,EAAA,CAAAC,cAAA,qBAA2E;IAE5CD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAS,UAAA,IAAAC,kDAAA,oBAA4E;IAC5EV,EAAA,CAAAS,UAAA,IAAAE,kDAAA,oBAAmG;IACnGX,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAY,SAAA,aAOS;IACTZ,EAAA,CAAAE,MAAA,GAKF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IApB2CH,EAAA,CAAAa,UAAA,UAAAP,WAAA,CAAkB;IAE3CN,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,WAAA,CAAAQ,IAAA,CAAmB;IAErCd,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAa,UAAA,SAAAP,WAAA,CAAAC,IAAA,CAAmB;IACnBP,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAa,UAAA,SAAAP,WAAA,CAAAS,IAAA,UAAAT,WAAA,CAAAE,IAAA,CAA0C;IAG7CR,EAAA,CAAAI,SAAA,GAME;IANFJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAX,WAAA,CAAAS,IAAA,QAAAT,WAAA,CAAAS,IAAA,QAAAT,WAAA,CAAAS,IAAA,QAAAT,WAAA,CAAAS,IAAA,QAAAT,WAAA,CAAAS,IAAA,QAME;IACJf,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAkB,kBAAA,MAAAZ,WAAA,CAAAS,IAAA,kBAAAT,WAAA,CAAAS,IAAA,mBAAAT,WAAA,CAAAS,IAAA,mBAAAT,WAAA,CAAAS,IAAA,qBAAAT,WAAA,CAAAS,IAAA,mCAKF;;;;;IA4CAf,EAAA,CAAAC,cAAA,gBAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAc,YAAA,CAAAZ,IAAA,CAAmB;;;;;IACrEP,EAAA,CAAAC,cAAA,gBAAyE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAc,YAAA,CAAAX,IAAA,CAAmB;;;;;IALlGR,EAAA,CAAAC,cAAA,qBAAyE;IAE1CD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAS,UAAA,IAAAW,kDAAA,oBAA4E;IAC5EpB,EAAA,CAAAS,UAAA,IAAAY,kDAAA,oBAAmG;IACnGrB,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAY,SAAA,aAOS;IACTZ,EAAA,CAAAE,MAAA,GAKF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IApByCH,EAAA,CAAAa,UAAA,UAAAM,YAAA,CAAkB;IAEzCnB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAc,YAAA,CAAAL,IAAA,CAAmB;IAErCd,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAa,UAAA,SAAAM,YAAA,CAAAZ,IAAA,CAAmB;IACnBP,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAa,UAAA,SAAAM,YAAA,CAAAJ,IAAA,UAAAI,YAAA,CAAAX,IAAA,CAA0C;IAG7CR,EAAA,CAAAI,SAAA,GAME;IANFJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAE,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAME;IACJf,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAkB,kBAAA,MAAAC,YAAA,CAAAJ,IAAA,kBAAAI,YAAA,CAAAJ,IAAA,mBAAAI,YAAA,CAAAJ,IAAA,mBAAAI,YAAA,CAAAJ,IAAA,qBAAAI,YAAA,CAAAJ,IAAA,mCAKF;;;;;IAgEFf,EAAA,CAAAC,cAAA,iBAA8E;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA5DH,EAAA,CAAAa,UAAA,UAAAS,eAAA,CAAAC,KAAA,CAA2B;IAACvB,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAiB,eAAA,CAAAE,KAAA,CAAuB;;;;;IA0CzGxB,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC1CH,EAAA,CAAAC,cAAA,eAAiD;IAC/CD,EAAA,CAAAY,SAAA,eAA2B;IAC7BZ,EAAA,CAAAG,YAAA,EAAM;;;;;IAwJhBH,EAAA,CAAAC,cAAA,eAAiD;IAG3CD,EAAA,CAAAY,SAAA,aAA4B;IAI9BZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAI5CH,EAAA,CAAAC,cAAA,eAAgE;IAE5DD,EAAA,CAAAY,SAAA,aAAyC;IAC3CZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/CH,EAAA,CAAAC,cAAA,kBAAkD;IAArBD,EAAA,CAAAyB,UAAA,mBAAAC,oEAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAC/ChC,EAAA,CAAAY,SAAA,aAA2B;IAACZ,EAAA,CAAAE,MAAA,kBAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHgBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAA4B,OAAA,CAAAC,YAAA,CAAkB;;;;;IAUvClC,EAAA,CAAAC,cAAA,QAAsC;IAAAD,EAAA,CAAAE,MAAA,qEAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACxGH,EAAA,CAAAC,cAAA,QAAoC;IACND,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,sBAChE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAD0BH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAA8B,OAAA,CAAAC,aAAA,CAAAC,MAAA,CAA0B;;;;;IAI1DrC,EAAA,CAAAC,cAAA,eAA8D;IAE1DD,EAAA,CAAAY,SAAA,aAAuC;IACvCZ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAC,cAAA,kBAA8B;IACND,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnDH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC3DH,EAAA,CAAAC,cAAA,mBAA0B;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7DH,EAAA,CAAAC,cAAA,mBAAwB;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAWvDH,EAAA,CAAAY,SAAA,eAG0B;;;;IAFrBZ,EAAA,CAAAa,UAAA,QAAAyB,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,EAAAzC,EAAA,CAAA0C,aAAA,CAA6C;;;;;IAGlD1C,EAAA,CAAAY,SAAA,aACyC;;;;;IAMzCZ,EAAA,CAAAC,cAAA,gBAA2H;IACzHD,EAAA,CAAAY,SAAA,aAA0B;IAC1BZ,EAAA,CAAAE,MAAA,GAGF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHLH,EAAA,CAAAI,SAAA,GAGF;IAHEJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAI,cAAA,IAAAL,UAAA,CAAAC,KAAA,IAAAI,cAAA,CAAAC,WAAA,IAAAN,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAI,cAAA,IAAAL,UAAA,CAAAC,KAAA,IAAAI,cAAA,CAAA7B,IAAA,kBAAAwB,UAAA,CAAAO,QAAA,MAGF;;;;;IAKF7C,EAAA,CAAAC,cAAA,gBAAsG;IACpGD,EAAA,CAAAY,SAAA,aAA2B;IAACZ,EAAA,CAAAE,MAAA,eAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAAkG;IAChGD,EAAA,CAAAY,SAAA,aAA4B;IAACZ,EAAA,CAAAE,MAAA,GAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADwBH,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAC,KAAA,IAAAO,WAAA,CAAAhC,IAAA,MAC/B;;;;;IAEAd,EAAA,CAAAC,cAAA,gBAA0G;IACxGD,EAAA,CAAAY,SAAA,aAAkC;IAACZ,EAAA,CAAAE,MAAA,qBACrC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;;;;IAOPH,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAY,SAAA,aAAqI;IACrIZ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFUH,EAAA,CAAAI,SAAA,GAAiH;IAAjHJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAA+C,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,iBAAA,CAAAZ,UAAA,IAAAW,OAAA,CAAAC,iBAAA,CAAAZ,UAAA,GAAiH;IAChItC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAA+B,OAAA,CAAAC,iBAAA,CAAAZ,UAAA,uCACF;;;;;IAGAtC,EAAA,CAAAC,cAAA,gBAAyG;IACvGD,EAAA,CAAAY,SAAA,aAA4B;IAACZ,EAAA,CAAAE,MAAA,GAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADwBH,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAAkB,kBAAA,eAAAiC,OAAA,CAAAC,oBAAA,CAAAd,UAAA,CAAAe,MAAA,IAAAC,SAAA,OAC/B;;;;;IA6BEtD,EAAA,CAAAC,cAAA,eAA4F;IACjED,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,GAClE;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADqBH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAgB,SAAA,CAA+B;IAAQvD,EAAA,CAAAI,SAAA,GAClE;IADkEJ,EAAA,CAAAkB,kBAAA,UAAAoB,UAAA,CAAAC,KAAA,IAAAgB,SAAA,qBAClE;;;;;IACAvD,EAAA,CAAAC,cAAA,eAAqG;IACnGD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAwBEH,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9BH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAkB,kBAAA,KAAAsC,WAAA,CAAAC,MAAA,QAAuB;;;;;IAC9EzD,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAnCH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAkB,kBAAA,KAAAsC,WAAA,CAAAE,KAAA,cAA4B;;;;;IANtF1D,EAAA,CAAAC,cAAA,eACqG;IACnGD,EAAA,CAAAY,SAAA,YAA+B;IAC/BZ,EAAA,CAAAC,cAAA,eAA0B;IACGD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAS,UAAA,IAAAkD,oFAAA,oBAAqF;IACrF3D,EAAA,CAAAS,UAAA,IAAAmD,oFAAA,oBAAyF;IAC3F5D,EAAA,CAAAG,YAAA,EAAM;;;;IAFyBH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAa,UAAA,SAAA2C,WAAA,CAAAC,MAAA,KAAwB;IACxBzD,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAa,UAAA,SAAA2C,WAAA,CAAAE,KAAA,KAAuB;;;;;IAP1D1D,EAAA,CAAA6D,uBAAA,GAA4G;IAC1G7D,EAAA,CAAAS,UAAA,IAAAqD,6EAAA,mBAQM;;IACR9D,EAAA,CAAA+D,qBAAA,EAAe;;;;;IARY/D,EAAA,CAAAI,SAAA,GAA0E;IAA1EJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAgE,WAAA,OAAAC,OAAA,CAAAC,mBAAA,CAAA5B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,YAA0E;;;;;IAiB/FnE,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAnCH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAkB,kBAAA,KAAAkD,WAAA,CAAAV,KAAA,cAA4B;;;;;IAClF1D,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9BH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAkB,kBAAA,KAAAkD,WAAA,CAAAX,MAAA,QAAuB;;;;;IANlFzD,EAAA,CAAAC,cAAA,eACqG;IACnGD,EAAA,CAAAY,SAAA,aAAgC;IAChCZ,EAAA,CAAAC,cAAA,eAA0B;IACGD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAS,UAAA,IAAA4D,oFAAA,oBAAyF;IACzFrE,EAAA,CAAAS,UAAA,IAAA6D,oFAAA,oBAAqF;IACvFtE,EAAA,CAAAG,YAAA,EAAM;;;;IAFyBH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAa,UAAA,SAAAuD,WAAA,CAAAV,KAAA,KAAuB;IACvB1D,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAa,UAAA,SAAAuD,WAAA,CAAAX,MAAA,KAAwB;;;;;IAP3DzD,EAAA,CAAA6D,uBAAA,GAA4G;IAC1G7D,EAAA,CAAAS,UAAA,IAAA8D,6EAAA,mBAQM;;IACRvE,EAAA,CAAA+D,qBAAA,EAAe;;;;;IARY/D,EAAA,CAAAI,SAAA,GAA0E;IAA1EJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAgE,WAAA,OAAAQ,OAAA,CAAAN,mBAAA,CAAA5B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,YAA0E;;;;;IAWrGnE,EAAA,CAAAC,cAAA,eAEiH;IAC/GD,EAAA,CAAAY,SAAA,aAAgC;IAChCZ,EAAA,CAAAC,cAAA,eAA0B;IACGD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAMjDH,EAAA,CAAAC,cAAA,eAAsH;IAC9GD,EAAA,CAAAE,MAAA,gDAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA3C1DH,EAAA,CAAAC,cAAA,eAAmE;IAC7DD,EAAA,CAAAY,SAAA,YAA+B;IAACZ,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,eAA6B;IAE3BD,EAAA,CAAAS,UAAA,IAAAgE,uEAAA,2BAUe;IAGfzE,EAAA,CAAAS,UAAA,IAAAiE,uEAAA,2BAUe;IAGf1E,EAAA,CAAAS,UAAA,IAAAkE,8DAAA,mBAQM;IACR3E,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAS,UAAA,IAAAmE,8DAAA,mBAEM;IACR5E,EAAA,CAAAG,YAAA,EAAM;;;;;IAzCaH,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,IAAA7B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,CAAA9B,MAAA,KAA2F;IAa3FrC,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,IAAA7B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,CAAA9B,MAAA,KAA2F;IAazErC,EAAA,CAAAI,SAAA,GAE8E;IAF9EJ,EAAA,CAAAa,UAAA,UAAAyB,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,IAAA7B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,CAAA9B,MAAA,UAAAwC,OAAA,CAAAX,mBAAA,CAAA5B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,KAAA9B,MAAA,OAE8E;IAU3GrC,EAAA,CAAAI,SAAA,GAA8F;IAA9FJ,EAAA,CAAAa,UAAA,UAAAyB,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,IAAA7B,UAAA,CAAAC,KAAA,IAAA4B,mBAAA,CAAA9B,MAAA,OAA8F;;;;;IAQpGrC,EAAA,CAAAC,cAAA,eAAkF;IAC1ED,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtCH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAAyE,WAAA,CAAAhE,IAAA,cAA+B;;;;;IAHzCd,EAAA,CAAAC,cAAA,eAAsI;IAChID,EAAA,CAAAY,SAAA,aAAqC;IAACZ,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAA,CAAAS,UAAA,IAAAsE,8DAAA,mBAEM;;IACR/E,EAAA,CAAAG,YAAA,EAAM;;;;IAHqBH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAgE,WAAA,OAAA1B,UAAA,CAAAC,KAAA,IAAAyC,QAAA,QAAuC;;;;;IAQhEhF,EAAA,CAAAC,cAAA,eAA6D;IAEzDD,EAAA,CAAAY,SAAA,QAAqI;IACrIZ,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAFnDH,EAAA,CAAAI,SAAA,GAA6H;IAA7HJ,EAAA,CAAAiF,UAAA,CAAA3C,UAAA,CAAAe,MAAA,IAAA6B,cAAA,CAAAC,UAAA,0EAA6H;IAChInF,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAe,MAAA,IAAA6B,cAAA,CAAAC,UAAA,wCACA;;;;;IANNnF,EAAA,CAAAC,cAAA,eAA6E;IACvED,EAAA,CAAAY,SAAA,aAA0B;IAACZ,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAS,UAAA,IAAA2E,8DAAA,mBAMM;IACRpF,EAAA,CAAAG,YAAA,EAAM;;;;IAPkBH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAe,MAAA,IAAA6B,cAAA,CAAqC;;;;;IAqBzDlF,EAAA,CAAAC,cAAA,eAAuF;IAEnFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAsD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFpFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAqF,kBAAA,MAAAC,OAAA,CAAAC,oBAAA,CAAAC,QAAA,CAAAC,aAAA,UAAAD,QAAA,CAAAE,cAAA,OACF;IACyB1F,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2F,WAAA,OAAAH,QAAA,CAAAI,KAAA,CAAAC,MAAA,EAAAL,QAAA,CAAAI,KAAA,CAAAE,QAAA,EAAsD;;;;;IAEjF9F,EAAA,CAAAC,cAAA,eAA4E;IAClDD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAwF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA/FH,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2F,WAAA,OAAArD,UAAA,CAAAe,MAAA,IAAA0C,UAAA,CAAAF,MAAA,EAAAvD,UAAA,CAAAe,MAAA,IAAA0C,UAAA,CAAAD,QAAA,EAAwF;;;;;IAVrH9F,EAAA,CAAAC,cAAA,eAA8G;IACxGD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAS,UAAA,IAAAuF,oEAAA,mBAKM;IACNhG,EAAA,CAAAS,UAAA,IAAAwF,oEAAA,mBAGM;IACNjG,EAAA,CAAAC,cAAA,eAA6B;IACDD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA8E;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAZrEH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAa,UAAA,YAAAyB,UAAA,CAAAe,MAAA,IAAA6C,cAAA,CAAA3D,KAAA,CAAwC;IAM5CvC,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAe,MAAA,IAAA0C,UAAA,CAAiC;IAM7C/F,EAAA,CAAAI,SAAA,GAA8E;IAA9EJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2F,WAAA,QAAArD,UAAA,CAAAe,MAAA,IAAAuC,KAAA,CAAAC,MAAA,EAAAvD,UAAA,CAAAe,MAAA,IAAAuC,KAAA,CAAAE,QAAA,EAA8E;;;;;IAhB/G9F,EAAA,CAAAC,cAAA,eAA+E;IAE7ED,EAAA,CAAAS,UAAA,IAAA0F,8DAAA,oBAgBM;IACRnG,EAAA,CAAAG,YAAA,EAAM;;;;IAjB0BH,EAAA,CAAAI,SAAA,GAA8E;IAA9EJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAe,MAAA,IAAA6C,cAAA,IAAA5D,UAAA,CAAAe,MAAA,IAAA6C,cAAA,CAAA3D,KAAA,CAA8E;;;;;IA+D1GvC,EAAA,CAAAC,cAAA,eAA0E;IACxED,EAAA,CAAAY,SAAA,aAAqC;IACrCZ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA6K;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;IAApLH,EAAA,CAAAI,SAAA,GAA6K;IAA7KJ,EAAA,CAAAqF,kBAAA,KAAAe,OAAA,CAAAC,oBAAA,CAAAC,WAAA,EAAAhE,UAAA,CAAAC,KAAA,IAAAgE,QAAA,CAAAC,KAAA,wBAAAF,WAAA,CAAAG,OAAA,IAAAH,WAAA,CAAAG,OAAA,CAAAjG,IAAA,IAAA8F,WAAA,CAAAG,OAAA,CAAAjG,IAAA,CAAAM,IAAA,0BAA6K;;;;;IArCvLd,EAAA,CAAAC,cAAA,eAA0F;IAEzDD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/FH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5DH,EAAA,CAAAC,cAAA,eAA2B;IAELD,EAAA,CAAAE,MAAA,IAAoE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5FH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAA+F;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjIH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAyF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG5HH,EAAA,CAAAC,cAAA,gBAA8B;IAE1BD,EAAA,CAAAY,SAAA,iBAAuC;IACvCZ,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAY,SAAA,iBAA0B;IAC5BZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAY,SAAA,iBAAqC;IACvCZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAY,SAAA,cAA4B;IAACZ,EAAA,CAAAE,MAAA,IAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,gBAA6B;IACTD,EAAA,CAAAE,MAAA,IAAgE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxFH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAAyF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3HH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAmF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKxHH,EAAA,CAAAS,UAAA,KAAAiG,qEAAA,mBAGM;IACR1G,EAAA,CAAAG,YAAA,EAAM;;;;;;;IArC2BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAkB,kBAAA,aAAAsF,KAAA,SAAmB;IAClBxG,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,iBAAA,CAAAiG,WAAA,CAAA9D,OAAA,IAAA8D,WAAA,CAAA9D,OAAA,CAAA1B,IAAA,cAA0D;IAC3Dd,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAiG,WAAA,CAAAK,QAAA,CAAsB;IAI/B3G,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAK,iBAAA,CAAAiG,WAAA,CAAAM,SAAA,GAAAC,OAAA,CAAAC,UAAA,CAAAR,WAAA,CAAAM,SAAA,CAAAG,IAAA,UAAoE;IAEzD/G,EAAA,CAAAI,SAAA,GAA+F;IAA/FJ,EAAA,CAAAK,iBAAA,CAAAiG,WAAA,CAAAM,SAAA,IAAAN,WAAA,CAAAM,SAAA,CAAAI,OAAA,IAAAV,WAAA,CAAAM,SAAA,CAAAI,OAAA,CAAAzG,IAAA,UAA+F;IAClGP,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAK,iBAAA,CAAAiG,WAAA,CAAAM,SAAA,IAAAN,WAAA,CAAAM,SAAA,CAAApG,IAAA,IAAA8F,WAAA,CAAAM,SAAA,CAAApG,IAAA,CAAAM,IAAA,UAAyF;IAYpFd,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAAkB,kBAAA,MAAA2F,OAAA,CAAAI,cAAA,CAAAX,WAAA,CAAAY,QAAA,OAC/B;IAGkBlH,EAAA,CAAAI,SAAA,GAAgE;IAAhEJ,EAAA,CAAAK,iBAAA,CAAAiG,WAAA,CAAAG,OAAA,GAAAI,OAAA,CAAAC,UAAA,CAAAR,WAAA,CAAAG,OAAA,CAAAM,IAAA,UAAgE;IAErD/G,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAK,iBAAA,CAAAiG,WAAA,CAAAG,OAAA,IAAAH,WAAA,CAAAG,OAAA,CAAAO,OAAA,IAAAV,WAAA,CAAAG,OAAA,CAAAO,OAAA,CAAAzG,IAAA,UAAyF;IAC5FP,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAK,iBAAA,CAAAiG,WAAA,CAAAG,OAAA,IAAAH,WAAA,CAAAG,OAAA,CAAAjG,IAAA,IAAA8F,WAAA,CAAAG,OAAA,CAAAjG,IAAA,CAAAM,IAAA,UAAmF;IAKtFd,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAa,UAAA,SAAA2F,KAAA,GAAAlE,UAAA,CAAAC,KAAA,IAAAgE,QAAA,CAAAlE,MAAA,KAA6C;;;;;IAzChFrC,EAAA,CAAAC,cAAA,eAAyI;IAGnID,EAAA,CAAAY,SAAA,aAAsC;IAACZ,EAAA,CAAAE,MAAA,GACzC;IAAAF,EAAA,CAAAG,YAAA,EAAU;IACVH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAS,UAAA,IAAA0G,8DAAA,qBAuCM;IACRnH,EAAA,CAAAG,YAAA,EAAM;;;;IA3CmCH,EAAA,CAAAI,SAAA,GACzC;IADyCJ,EAAA,CAAAkB,kBAAA,uBAAAoB,UAAA,CAAAC,KAAA,IAAAgE,QAAA,CAAAlE,MAAA,OACzC;IAEgDrC,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAa,UAAA,YAAAyB,UAAA,CAAAC,KAAA,IAAAgE,QAAA,CAA6B;;;;;IAmD3EvG,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAC,WAAA,MACF;;;;;IAKIrH,EAAA,CAAAC,cAAA,eAAiG;IAC/FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAoG,WAAA,CAAAC,YAAA,IAAAC,IAAA,MACF;;;;;IAJFxH,EAAA,CAAAC,cAAA,eAAwF;IAC5DD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzEH,EAAA,CAAAS,UAAA,IAAAgH,0EAAA,mBAEM;IACRzH,EAAA,CAAAG,YAAA,EAAM;;;;IAJsBH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,iBAAA,CAAAiH,WAAA,CAAAI,cAAA,cAAyC;IACjC1H,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAa,UAAA,SAAAyG,WAAA,CAAAC,YAAA,IAAAD,WAAA,CAAAC,YAAA,CAAAlF,MAAA,KAA6D;;;;;IAJnGrC,EAAA,CAAAC,cAAA,eAAqI;IAC/HD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAS,UAAA,IAAAkH,oEAAA,mBAKM;IACR3H,EAAA,CAAAG,YAAA,EAAM;;;;IAN0CH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAa,UAAA,YAAAyB,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAQ,QAAA,CAAwC;;;;;IAX9F5H,EAAA,CAAAC,cAAA,eAA4G;IAGtGD,EAAA,CAAAY,SAAA,aAAkC;IAACZ,EAAA,CAAAE,MAAA,GACrC;IAAAF,EAAA,CAAAG,YAAA,EAAU;IACVH,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAS,UAAA,IAAAoH,8DAAA,mBAEM;IACN7H,EAAA,CAAAS,UAAA,IAAAqH,8DAAA,mBAQM;IACR9H,EAAA,CAAAG,YAAA,EAAM;;;;IAf+BH,EAAA,CAAAI,SAAA,GACrC;IADqCJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAtG,IAAA,gCACrC;IAEyCd,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAC,WAAA,CAA8C;IAGjDrH,EAAA,CAAAI,SAAA,GAA+F;IAA/FJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAQ,QAAA,IAAAtF,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAAQ,QAAA,CAAAvF,MAAA,KAA+F;;;;;;IAhQ3IrC,EAAA,CAAAC,cAAA,eAA+G;IAIvGD,EAAA,CAAAS,UAAA,IAAAsH,uDAAA,mBAG0B;IAC1B/H,EAAA,CAAAS,UAAA,IAAAuH,qDAAA,iBACyC;IAC3ChI,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACAD,EAAA,CAAAE,MAAA,GAA2G;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7IH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAwE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE3GH,EAAA,CAAAS,UAAA,KAAAwH,yDAAA,oBAKO;IACTjI,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAS,UAAA,KAAAyH,yDAAA,oBAEO;IACPlI,EAAA,CAAAS,UAAA,KAAA0H,yDAAA,oBAEO;IAEPnI,EAAA,CAAAS,UAAA,KAAA2H,yDAAA,oBAEO;IACTpI,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAA0B;IACED,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAS,UAAA,KAAA4H,yDAAA,oBAGO;IAGPrI,EAAA,CAAAS,UAAA,KAAA6H,yDAAA,oBAEO;IACTtI,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAA4B;IAGJD,EAAA,CAAAE,MAAA,IAAuH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/IH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAAwJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1LH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAkJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIrLH,EAAA,CAAAC,cAAA,gBAA6B;IAEzBD,EAAA,CAAAY,SAAA,iBAAuC;IACvCZ,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAY,SAAA,iBAA0B;IAC1BZ,EAAA,CAAAC,cAAA,iBAAyB;IACvBD,EAAA,CAAAY,SAAA,cAA4B;IAC9BZ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAY,SAAA,iBAAqC;IACvCZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAY,SAAA,cAA4B;IAC5BZ,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAS,UAAA,KAAA8H,wDAAA,mBAEM;IACNvI,EAAA,CAAAS,UAAA,KAAA+H,wDAAA,mBAEM;IACRxI,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAAqB;IACDD,EAAA,CAAAE,MAAA,IAAmH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3IH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAAkJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpLH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA4I;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKjLH,EAAA,CAAAC,cAAA,gBAA6B;IAE3BD,EAAA,CAAAS,UAAA,KAAAgI,wDAAA,mBA6CM;IAGNzI,EAAA,CAAAS,UAAA,KAAAiI,wDAAA,mBAKM;IAGN1I,EAAA,CAAAS,UAAA,KAAAkI,wDAAA,mBASM;IACR3I,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,gBAAqC;IAG/BD,EAAA,CAAAY,SAAA,aAAsC;IAACZ,EAAA,CAAAE,MAAA,yBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAU;IACVH,EAAA,CAAAS,UAAA,KAAAmI,wDAAA,mBAmBM;IACR5I,EAAA,CAAAG,YAAA,EAAU;IAIZH,EAAA,CAAAS,UAAA,KAAAoI,wDAAA,mBAgDM;IAGN7I,EAAA,CAAAS,UAAA,KAAAqI,wDAAA,mBAoBM;IAEN9I,EAAA,CAAAC,cAAA,gBAA4B;IACUD,EAAA,CAAAyB,UAAA,mBAAAsH,2EAAA;MAAA,MAAAC,WAAA,GAAAhJ,EAAA,CAAA2B,aAAA,CAAAsH,KAAA;MAAA,MAAA3G,UAAA,GAAA0G,WAAA,CAAAE,SAAA;MAAA,MAAAC,QAAA,GAAAnJ,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAoH,QAAA,CAAAC,cAAA,CAAA9G,UAAA,CAAsB;IAAA,EAAC;IAClEtC,EAAA,CAAAY,SAAA,cAAkC;IAClCZ,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAyJ;IAAnCD,EAAA,CAAAyB,UAAA,mBAAA4H,2EAAA;MAAA,MAAAL,WAAA,GAAAhJ,EAAA,CAAA2B,aAAA,CAAAsH,KAAA;MAAA,MAAA3G,UAAA,GAAA0G,WAAA,CAAAE,SAAA;MAAA,MAAAI,QAAA,GAAAtJ,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAuH,QAAA,CAAAC,gBAAA,CAAAjH,UAAA,CAAwB;IAAA,EAAC;IACtJtC,EAAA,CAAAY,SAAA,cAAmC;IACnCZ,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IArRiDH,EAAA,CAAAwJ,WAAA,iBAAAC,OAAA,CAAAvG,iBAAA,CAAAZ,UAAA,EAAgD;IAIhGtC,EAAA,CAAAI,SAAA,GAAyG;IAAzGJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,IAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,CAAyG;IAI3GzC,EAAA,CAAAI,SAAA,GAA4G;IAA5GJ,EAAA,CAAAa,UAAA,WAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,IAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,EAA4G;IAIrFzC,EAAA,CAAAI,SAAA,GAA2G;IAA3GJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,GAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAA1B,IAAA,aAA2G;IAC1Gd,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,MAAAD,UAAA,CAAAC,KAAA,IAAAoE,QAAA,SAAwE;IAEvE3G,EAAA,CAAAI,SAAA,GAA4F;IAA5FJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAO,QAAA,IAAAP,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAI,cAAA,CAA4F;IAU/F3C,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAgB,SAAA,OAAwE;IAGxEvD,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAO,WAAA,CAAoE;IAI5D9C,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,OAAAf,UAAA,CAAAe,MAAA,IAAAqG,QAAA,CAAoE;IAOpF1J,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAoJ,OAAA,CAAAE,WAAA,CAAArH,UAAA,EAAyB;IAEjBtC,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,CAAAhB,MAAA,KAA+C;IAMjDrC,EAAA,CAAAI,SAAA,GAA6E;IAA7EJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,CAAAhB,MAAA,QAAAC,UAAA,CAAAe,MAAA,IAAAC,SAAA,CAA6E;IASnFtD,EAAA,CAAAI,SAAA,GAAuH;IAAvHJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAqE,SAAA,GAAA6C,OAAA,CAAA3C,UAAA,CAAAxE,UAAA,CAAAC,KAAA,IAAAqE,SAAA,CAAAG,IAAA,UAAuH;IAE5G/G,EAAA,CAAAI,SAAA,GAAwJ;IAAxJJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAqE,SAAA,IAAAtE,UAAA,CAAAC,KAAA,IAAAqE,SAAA,CAAAI,OAAA,GAAA1E,UAAA,CAAAC,KAAA,IAAAqE,SAAA,CAAAI,OAAA,CAAAzG,IAAA,SAAwJ;IAC3JP,EAAA,CAAAI,SAAA,GAAkJ;IAAlJJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAqE,SAAA,IAAAtE,UAAA,CAAAC,KAAA,IAAAqE,SAAA,CAAApG,IAAA,GAAA8B,UAAA,CAAAC,KAAA,IAAAqE,SAAA,CAAApG,IAAA,CAAAM,IAAA,SAAkJ;IAiB1Kd,EAAA,CAAAI,SAAA,IACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,MAAAkH,OAAA,CAAAxC,cAAA,CAAA3E,UAAA,CAAAC,KAAA,IAAA2E,QAAA,eACF;IACoBlH,EAAA,CAAAI,SAAA,GAAsE;IAAtEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAgB,SAAA,KAAsE;IAG/DvD,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAgB,SAAA,OAAwE;IAMjFvD,EAAA,CAAAI,SAAA,GAAmH;IAAnHJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAkE,OAAA,GAAAgD,OAAA,CAAA3C,UAAA,CAAAxE,UAAA,CAAAC,KAAA,IAAAkE,OAAA,CAAAM,IAAA,UAAmH;IAExG/G,EAAA,CAAAI,SAAA,GAAkJ;IAAlJJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAkE,OAAA,IAAAnE,UAAA,CAAAC,KAAA,IAAAkE,OAAA,CAAAO,OAAA,GAAA1E,UAAA,CAAAC,KAAA,IAAAkE,OAAA,CAAAO,OAAA,CAAAzG,IAAA,SAAkJ;IACrJP,EAAA,CAAAI,SAAA,GAA4I;IAA5IJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAkE,OAAA,IAAAnE,UAAA,CAAAC,KAAA,IAAAkE,OAAA,CAAAjG,IAAA,GAAA8B,UAAA,CAAAC,KAAA,IAAAkE,OAAA,CAAAjG,IAAA,CAAAM,IAAA,SAA4I;IAOlKd,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,IAAqC;IAgDrCvC,EAAA,CAAAI,SAAA,GAAwG;IAAxGJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAyC,QAAA,IAAA1C,UAAA,CAAAC,KAAA,IAAAyC,QAAA,CAAA3C,MAAA,KAAwG;IAQxGrC,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,CAAAhB,MAAA,KAA+C;IAmBfrC,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,IAAuC;IAwBlDrD,EAAA,CAAAI,SAAA,GAAwG;IAAxGJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAgE,QAAA,IAAAjE,UAAA,CAAAC,KAAA,IAAAgE,QAAA,CAAAlE,MAAA,KAAwG;IAmDpGrC,EAAA,CAAAI,SAAA,GAAuE;IAAvEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,OAAAf,UAAA,CAAAe,MAAA,IAAA+D,WAAA,CAAuE;IA2B1EpH,EAAA,CAAAI,SAAA,GAAuF;IAAvFJ,EAAA,CAAAa,UAAA,cAAAyB,UAAA,CAAAe,MAAA,IAAAf,UAAA,CAAAe,MAAA,CAAAhB,MAAA,WAAAoH,OAAA,CAAAvG,iBAAA,CAAAZ,UAAA,EAAuF;;;;;IAQzHtC,EAAA,CAAAC,cAAA,eAA0F;IAEtFD,EAAA,CAAAY,SAAA,aAA6B;IAC/BZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wGAAiG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxGH,EAAA,CAAAC,cAAA,eAAoC;IAEhCD,EAAA,CAAAY,SAAA,aAAmC;IACnCZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElCH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAY,SAAA,cAAqC;IACrCZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElCH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAY,SAAA,cAA4B;IAC5BZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IApUjDH,EAAA,CAAAC,cAAA,eAAwE;IAG9DD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAS,UAAA,IAAAmJ,+CAAA,gBAAwG;IACxG5J,EAAA,CAAAS,UAAA,IAAAoJ,+CAAA,gBAEI;IACN7J,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAS,UAAA,IAAAqJ,iDAAA,oBAWM;IACR9J,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAS,UAAA,IAAAsJ,iDAAA,qBAuRM;IAGN/J,EAAA,CAAAS,UAAA,KAAAuJ,kDAAA,oBAoBM;IACRhK,EAAA,CAAAG,YAAA,EAAM;;;;IApUEH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAa,UAAA,SAAAoJ,OAAA,CAAA7H,aAAA,CAAAC,MAAA,OAAgC;IAChCrC,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAa,UAAA,SAAAoJ,OAAA,CAAA7H,aAAA,CAAAC,MAAA,KAA8B;IAKNrC,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAa,UAAA,SAAAoJ,OAAA,CAAA7H,aAAA,CAAAC,MAAA,KAA8B;IAepCrC,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAa,UAAA,YAAAoJ,OAAA,CAAA7H,aAAA,CAAgB;IA0RfpC,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAa,UAAA,UAAAoJ,OAAA,CAAAC,SAAA,KAAAD,OAAA,CAAA/H,YAAA,IAAA+H,OAAA,CAAA7H,aAAA,CAAAC,MAAA,OAA+D;;;;;IA3U9FrC,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAAS,UAAA,IAAA0J,2CAAA,mBAUM;IAENnK,EAAA,CAAAS,UAAA,IAAA2J,2CAAA,oBASM;IAENpK,EAAA,CAAAS,UAAA,IAAA4J,2CAAA,oBAyUM;IACRrK,EAAA,CAAAG,YAAA,EAAM;;;;IAjWEH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAa,UAAA,SAAAyJ,MAAA,CAAAJ,SAAA,CAAe;IAYflK,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAa,UAAA,UAAAyJ,MAAA,CAAAJ,SAAA,IAAAI,MAAA,CAAApI,YAAA,CAAgC;IAWhClC,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAa,UAAA,UAAAyJ,MAAA,CAAAJ,SAAA,KAAAI,MAAA,CAAApI,YAAA,CAAiC;;;ADtZ7C,OAAM,MAAOqI,oBAAoB;EA4B/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IA7BhB,KAAAC,kBAAkB,GAAqB,EAAE;IACzC,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAAX,SAAS,GAAG,KAAK;IACjB,KAAA9H,aAAa,GAAa,EAAE;IAC5B,KAAA0I,WAAW,GAAG,KAAK;IACnB,KAAA5I,YAAY,GAAG,EAAE;IACjB,KAAA6I,YAAY,GAAG,EAAE;IACjB,KAAAC,uBAAuB,GAAG,KAAK;IAE/B;IACA,KAAAC,cAAc,GAAG,CACf;MAAE1J,KAAK,EAAExB,aAAa,CAACmL,KAAK;MAAE1J,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAExB,aAAa,CAACoL,KAAK;MAAE3J,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAExB,aAAa,CAACqL,MAAM;MAAE5J,KAAK,EAAE;IAAQ,CAAE,CACjD;IAED;IACA,KAAA6J,aAAa,GAAG,CACd;MAAE9J,KAAK,EAAE1B,eAAe,CAACyL,KAAK;MAAE9J,KAAK,EAAE;IAAO,CAAE,EAChD;MAAED,KAAK,EAAE1B,eAAe,CAAC0L,OAAO;MAAE/J,KAAK,EAAE;IAAS,CAAE,EACpD;MAAED,KAAK,EAAE1B,eAAe,CAAC2L,QAAQ;MAAEhK,KAAK,EAAE;IAAU,CAAE,CACvD;IAuqCD,KAAAiK,sBAAsB,GAAG,MAAW;MAClC,IAAI,CAACT,uBAAuB,GAAG,KAAK;MACpCU,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,sBAAsB,CAAC;IACpE,CAAC;IAhqCC;IACA,IAAI,CAACG,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAErD;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACvB,EAAE,CAACwB,KAAK,CAAC;MAC9B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAE3M,UAAU,CAAC4M,QAAQ,CAAC;MACrCC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE7M,UAAU,CAAC4M,QAAQ,CAAC;MAC1CE,iBAAiB,EAAE,CAAC,EAAE,EAAE9M,UAAU,CAAC4M,QAAQ,CAAC;MAC5CG,qBAAqB,EAAE,CAAC,CAAC,EAAE/M,UAAU,CAAC4M,QAAQ,CAAC;MAC/CI,eAAe,EAAE,CAAC,EAAE,EAAEhN,UAAU,CAAC4M,QAAQ,CAAC;MAC1CK,mBAAmB,EAAE,CAAC,CAAC,EAAEjN,UAAU,CAAC4M,QAAQ,CAAC;MAC7CM,aAAa,EAAE,CAAC,IAAI,CAACb,OAAO,EAAErM,UAAU,CAAC4M,QAAQ,CAAC;MAClDzG,cAAc,EAAE,CAAC,CAAC,EAAE,CAACnG,UAAU,CAAC4M,QAAQ,EAAE5M,UAAU,CAACmN,GAAG,CAAC,CAAC,CAAC,EAAEnN,UAAU,CAACoN,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChFlH,aAAa,EAAE,CAAC,CAAC,EAAElG,UAAU,CAAC4M,QAAQ,CAAC;MAEvC;MACArJ,WAAW,EAAE,CAAC,CAAC,EAAEvD,UAAU,CAAC4M,QAAQ,CAAC;MACrCS,OAAO,EAAE,CAAC,KAAK,CAAC;MAEhB;MACAC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClB/G,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBgH,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,wBAAwB,EAAE,CAAC,KAAK,CAAC;MACjCC,6BAA6B,EAAE,CAAC,IAAI,CAAC;MACrCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAE5B;MACAC,sBAAsB,EAAE,CAAC,CAAC;KAC3B,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACAC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EACjF;EAEA;EACAnE,cAAcA,CAACoE,MAAc;IAC3B;IACA,MAAMC,QAAQ,GAAG/B,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;IAC9CD,QAAQ,CAACE,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCH,QAAQ,CAACE,KAAK,CAACE,GAAG,GAAG,GAAG;IACxBJ,QAAQ,CAACE,KAAK,CAACG,IAAI,GAAG,GAAG;IACzBL,QAAQ,CAACE,KAAK,CAACI,KAAK,GAAG,MAAM;IAC7BN,QAAQ,CAACE,KAAK,CAACK,MAAM,GAAG,MAAM;IAC9BP,QAAQ,CAACE,KAAK,CAACM,eAAe,GAAG,oBAAoB;IACrDR,QAAQ,CAACE,KAAK,CAACO,MAAM,GAAG,MAAM;IAC9BT,QAAQ,CAACE,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC/BV,QAAQ,CAACE,KAAK,CAACS,cAAc,GAAG,QAAQ;IACxCX,QAAQ,CAACE,KAAK,CAACU,UAAU,GAAG,QAAQ;IAEpC;IACA,MAAMC,YAAY,GAAG5C,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;IAClDY,YAAY,CAACX,KAAK,CAACM,eAAe,GAAG,OAAO;IAC5CK,YAAY,CAACX,KAAK,CAACY,OAAO,GAAG,MAAM;IACnCD,YAAY,CAACX,KAAK,CAACa,YAAY,GAAG,MAAM;IACxCF,YAAY,CAACX,KAAK,CAACc,QAAQ,GAAG,KAAK;IACnCH,YAAY,CAACX,KAAK,CAACe,SAAS,GAAG,KAAK;IACpCJ,YAAY,CAACX,KAAK,CAACgB,QAAQ,GAAG,MAAM;IACpCL,YAAY,CAACX,KAAK,CAACC,QAAQ,GAAG,UAAU;IACxCU,YAAY,CAACX,KAAK,CAACiB,SAAS,GAAG,gCAAgC;IAC/DN,YAAY,CAACX,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCP,YAAY,CAACX,KAAK,CAACmB,UAAU,GAAG,mBAAmB;IAEnD;IACA,MAAMC,WAAW,GAAGrD,QAAQ,CAACgC,aAAa,CAAC,QAAQ,CAAC;IACpDqB,WAAW,CAACC,SAAS,GAAG,SAAS;IACjCD,WAAW,CAACpB,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvCmB,WAAW,CAACpB,KAAK,CAACE,GAAG,GAAG,MAAM;IAC9BkB,WAAW,CAACpB,KAAK,CAACsB,KAAK,GAAG,MAAM;IAChCF,WAAW,CAACpB,KAAK,CAACuB,MAAM,GAAG,MAAM;IACjCH,WAAW,CAACpB,KAAK,CAACwB,UAAU,GAAG,MAAM;IACrCJ,WAAW,CAACpB,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACnCL,WAAW,CAACpB,KAAK,CAAC0B,MAAM,GAAG,SAAS;IACpCN,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnCE,WAAW,CAACpB,KAAK,CAAC2B,UAAU,GAAG,YAAY;IAC3CP,WAAW,CAACQ,WAAW,GAAG,MAAMR,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnEE,WAAW,CAACS,UAAU,GAAG,MAAMT,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IAClEE,WAAW,CAACU,OAAO,GAAG,MAAM/D,QAAQ,CAACgE,IAAI,CAACC,WAAW,CAAClC,QAAQ,CAAC;IAE/D;IACA,MAAMmC,MAAM,GAAGlE,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;IAC5CkC,MAAM,CAACjC,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC7ByB,MAAM,CAACjC,KAAK,CAACU,UAAU,GAAG,QAAQ;IAClCuB,MAAM,CAACjC,KAAK,CAACkC,YAAY,GAAG,MAAM;IAClCD,MAAM,CAACjC,KAAK,CAACmC,aAAa,GAAG,MAAM;IACnCF,MAAM,CAACjC,KAAK,CAACoC,YAAY,GAAG,gBAAgB;IAE5C,MAAMC,IAAI,GAAGtE,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;IAC1CsC,IAAI,CAAChB,SAAS,GAAG,2FAA2F;IAE5G,MAAMiB,KAAK,GAAGvE,QAAQ,CAACgC,aAAa,CAAC,IAAI,CAAC;IAC1CuC,KAAK,CAACC,WAAW,GAAG,yBAAyB;IAC7CD,KAAK,CAACtC,KAAK,CAACwC,MAAM,GAAG,GAAG;IACxBF,KAAK,CAACtC,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC7Ba,KAAK,CAACtC,KAAK,CAACyC,UAAU,GAAG,KAAK;IAC9BH,KAAK,CAACtC,KAAK,CAACkB,KAAK,GAAG,SAAS;IAE7Be,MAAM,CAACS,WAAW,CAACL,IAAI,CAAC;IACxBJ,MAAM,CAACS,WAAW,CAACJ,KAAK,CAAC;IAEzB;IACA,MAAMK,gBAAgB,GAAG5E,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;IACtD4C,gBAAgB,CAAC3C,KAAK,CAACQ,OAAO,GAAG,MAAM;IACvCmC,gBAAgB,CAAC3C,KAAK,CAAC4C,aAAa,GAAG,QAAQ;IAC/CD,gBAAgB,CAAC3C,KAAK,CAAC6C,GAAG,GAAG,MAAM;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9E;IACA,IAAIlD,MAAM,CAACjL,KAAK,IAAIiL,MAAM,CAACjL,KAAK,CAACF,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMsO,IAAI,GAAGnD,MAAM,CAACjL,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,IAAIoO,IAAI,CAACnO,OAAO,EAAE;QAChB,MAAMoO,WAAW,GAAGlF,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QACjDkD,WAAW,CAACjD,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCyC,WAAW,CAACjD,KAAK,CAACU,UAAU,GAAG,QAAQ;QACvCuC,WAAW,CAACjD,KAAK,CAACkC,YAAY,GAAG,MAAM;QAEvC;QACA,IAAIc,IAAI,CAACnO,OAAO,CAACC,aAAa,EAAE;UAC9B,MAAMoO,WAAW,GAAGnF,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;UACjDmD,WAAW,CAACC,GAAG,GAAGH,IAAI,CAACnO,OAAO,CAACC,aAAa;UAC5CoO,WAAW,CAACE,GAAG,GAAGJ,IAAI,CAACnO,OAAO,CAAC1B,IAAI;UACnC+P,WAAW,CAAClD,KAAK,CAACK,MAAM,GAAG,MAAM;UACjC6C,WAAW,CAAClD,KAAK,CAACqD,WAAW,GAAG,MAAM;UACtCJ,WAAW,CAACP,WAAW,CAACQ,WAAW,CAAC;SACrC,MAAM;UACL,MAAMI,WAAW,GAAGvF,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;UACjDuD,WAAW,CAACjC,SAAS,GAAG,wFAAwF;UAChH4B,WAAW,CAACP,WAAW,CAACY,WAAW,CAAC;;QAGtC,MAAMC,WAAW,GAAGxF,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QACjDwD,WAAW,CAAClC,SAAS,GAAG,WAAW2B,IAAI,CAACnO,OAAO,CAAC1B,IAAI,cAAc6P,IAAI,CAACnO,OAAO,CAAC2O,iBAAiB,GAAG;QACnGD,WAAW,CAACvD,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCwB,WAAW,CAACP,WAAW,CAACa,WAAW,CAAC;QAEpCT,WAAW,CAACJ,WAAW,CAACO,WAAW,CAAC;;MAGtC;MACA,MAAMQ,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC,eAAe,EAAEV,IAAI,CAAChK,QAAQ,IAAI,KAAK,CAAC;MACnF8J,WAAW,CAACJ,WAAW,CAACe,eAAe,CAAC;MAExC;MACA,MAAME,aAAa,GAAG,IAAI,CAACD,aAAa,CAAC,aAAa,EAAE,IAAIxF,IAAI,CAAC8E,IAAI,CAACY,UAAU,CAAC,CAACC,kBAAkB,EAAE,CAAC;MACvGf,WAAW,CAACJ,WAAW,CAACiB,aAAa,CAAC;MAEtC;MACA,MAAMG,WAAW,GAAG,IAAI,CAACJ,aAAa,CAAC,UAAU,EAAE,IAAI,CAACpK,cAAc,CAAC0J,IAAI,CAACzJ,QAAQ,CAAC,CAAC;MACtFuJ,WAAW,CAACJ,WAAW,CAACoB,WAAW,CAAC;MAEpC;MACA,IAAId,IAAI,CAAC7N,WAAW,EAAE;QACpB,MAAM4O,QAAQ,GAAG,IAAI,CAACL,aAAa,CAAC,OAAO,EAAE,GAAGV,IAAI,CAAC7N,WAAW,CAAChC,IAAI,KAAK6P,IAAI,CAAC7N,WAAW,CAACvC,IAAI,GAAG,CAAC;QACnGkQ,WAAW,CAACJ,WAAW,CAACqB,QAAQ,CAAC;;MAGnC;MACA,MAAMC,QAAQ,GAAG,IAAI,CAACN,aAAa,CAAC,OAAO,EAAEV,IAAI,CAACpN,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,GAAGoN,IAAI,CAACpN,SAAS,UAAU,CAAC;MAClHkN,WAAW,CAACJ,WAAW,CAACsB,QAAQ,CAAC;;IAGnC;IACA,MAAMC,YAAY,GAAG,IAAI,CAAClB,aAAa,CAAC,eAAe,EAAE,UAAU,CAAC;IAEpE,IAAIlD,MAAM,CAACjL,KAAK,IAAIiL,MAAM,CAACjL,KAAK,CAACF,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMsO,IAAI,GAAGnD,MAAM,CAACjL,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,MAAMsP,WAAW,GAAGnG,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;MACjDmE,WAAW,CAAClE,KAAK,CAACQ,OAAO,GAAG,MAAM;MAClC0D,WAAW,CAAClE,KAAK,CAACU,UAAU,GAAG,QAAQ;MACvCwD,WAAW,CAAClE,KAAK,CAACS,cAAc,GAAG,eAAe;MAClDyD,WAAW,CAAClE,KAAK,CAACwC,MAAM,GAAG,QAAQ;MACnC0B,WAAW,CAAClE,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEvC;MACA,MAAMhH,SAAS,GAAG8E,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;MAC/C9G,SAAS,CAAC+G,KAAK,CAACmE,SAAS,GAAG,QAAQ;MACpClL,SAAS,CAAC+G,KAAK,CAACoE,IAAI,GAAG,GAAG;MAE1B,IAAIpB,IAAI,CAAC/J,SAAS,EAAE;QAClB,MAAMoL,aAAa,GAAGtG,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QACnDsE,aAAa,CAACrE,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC4C,aAAa,CAACrE,KAAK,CAACyC,UAAU,GAAG,MAAM;QACvC4B,aAAa,CAAC9B,WAAW,GAAG,IAAIrE,IAAI,CAAC8E,IAAI,CAAC/J,SAAS,CAACG,IAAI,CAAC,CAACkL,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAErH,MAAMC,gBAAgB,GAAG1G,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QACtD0E,gBAAgB,CAACzE,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACxCgD,gBAAgB,CAACzE,KAAK,CAAC0E,SAAS,GAAG,KAAK;QACxCD,gBAAgB,CAACpD,SAAS,GAAG,WAAW2B,IAAI,CAAC/J,SAAS,CAACI,OAAO,EAAEzG,IAAI,IAAI,KAAK,WAAW;QAExF,MAAM+R,aAAa,GAAG5G,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QACnD4E,aAAa,CAAC3E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCkD,aAAa,CAAC3E,KAAK,CAACkB,KAAK,GAAG,MAAM;QAClCyD,aAAa,CAACpC,WAAW,GAAGS,IAAI,CAAC/J,SAAS,CAACpG,IAAI,EAAEM,IAAI,IAAI,KAAK;QAE9D8F,SAAS,CAACyJ,WAAW,CAAC2B,aAAa,CAAC;QACpCpL,SAAS,CAACyJ,WAAW,CAAC+B,gBAAgB,CAAC;QACvCxL,SAAS,CAACyJ,WAAW,CAACiC,aAAa,CAAC;;MAGtC;MACA,MAAMC,cAAc,GAAG7G,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;MACpD6E,cAAc,CAAC5E,KAAK,CAACoE,IAAI,GAAG,GAAG;MAC/BQ,cAAc,CAAC5E,KAAK,CAACQ,OAAO,GAAG,MAAM;MACrCoE,cAAc,CAAC5E,KAAK,CAACU,UAAU,GAAG,QAAQ;MAC1CkE,cAAc,CAAC5E,KAAK,CAACS,cAAc,GAAG,QAAQ;MAC9CmE,cAAc,CAAC5E,KAAK,CAACY,OAAO,GAAG,QAAQ;MAEvC,MAAMiE,IAAI,GAAG9G,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;MAC1C8E,IAAI,CAAC7E,KAAK,CAACK,MAAM,GAAG,KAAK;MACzBwE,IAAI,CAAC7E,KAAK,CAACM,eAAe,GAAG,MAAM;MACnCuE,IAAI,CAAC7E,KAAK,CAACI,KAAK,GAAG,MAAM;MACzByE,IAAI,CAAC7E,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEhC,MAAM6E,KAAK,GAAG/G,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;MAC3C+E,KAAK,CAACzD,SAAS,GAAG,iGAAiG;MACnHyD,KAAK,CAAC9E,KAAK,CAACC,QAAQ,GAAG,UAAU;MACjC6E,KAAK,CAAC9E,KAAK,CAACE,GAAG,GAAG,MAAM;MACxB4E,KAAK,CAAC9E,KAAK,CAACG,IAAI,GAAG,KAAK;MACxB2E,KAAK,CAAC9E,KAAK,CAAC+E,UAAU,GAAG,MAAM;MAC/BD,KAAK,CAAC9E,KAAK,CAACM,eAAe,GAAG,OAAO;MACrCwE,KAAK,CAAC9E,KAAK,CAACY,OAAO,GAAG,OAAO;MAE7BiE,IAAI,CAACnC,WAAW,CAACoC,KAAK,CAAC;MACvBF,cAAc,CAAClC,WAAW,CAACmC,IAAI,CAAC;MAEhC;MACA,MAAM/L,OAAO,GAAGiF,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;MAC7CjH,OAAO,CAACkH,KAAK,CAACmE,SAAS,GAAG,QAAQ;MAClCrL,OAAO,CAACkH,KAAK,CAACoE,IAAI,GAAG,GAAG;MAExB,IAAIpB,IAAI,CAAClK,OAAO,EAAE;QAChB,MAAMkM,WAAW,GAAGjH,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QACjDiF,WAAW,CAAChF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCuD,WAAW,CAAChF,KAAK,CAACyC,UAAU,GAAG,MAAM;QACrCuC,WAAW,CAACzC,WAAW,GAAG,IAAIrE,IAAI,CAAC8E,IAAI,CAAClK,OAAO,CAACM,IAAI,CAAC,CAACkL,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAEjH,MAAMS,cAAc,GAAGlH,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QACpDkF,cAAc,CAACjF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACtCwD,cAAc,CAACjF,KAAK,CAAC0E,SAAS,GAAG,KAAK;QACtCO,cAAc,CAAC5D,SAAS,GAAG,WAAW2B,IAAI,CAAClK,OAAO,CAACO,OAAO,EAAEzG,IAAI,IAAI,KAAK,WAAW;QAEpF,MAAMsS,WAAW,GAAGnH,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QACjDmF,WAAW,CAAClF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCyD,WAAW,CAAClF,KAAK,CAACkB,KAAK,GAAG,MAAM;QAChCgE,WAAW,CAAC3C,WAAW,GAAGS,IAAI,CAAClK,OAAO,CAACjG,IAAI,EAAEM,IAAI,IAAI,KAAK;QAE1D2F,OAAO,CAAC4J,WAAW,CAACsC,WAAW,CAAC;QAChClM,OAAO,CAAC4J,WAAW,CAACuC,cAAc,CAAC;QACnCnM,OAAO,CAAC4J,WAAW,CAACwC,WAAW,CAAC;;MAGlChB,WAAW,CAACxB,WAAW,CAACzJ,SAAS,CAAC;MAClCiL,WAAW,CAACxB,WAAW,CAACkC,cAAc,CAAC;MACvCV,WAAW,CAACxB,WAAW,CAAC5J,OAAO,CAAC;MAEhCmL,YAAY,CAACvB,WAAW,CAACwB,WAAW,CAAC;MAErC;MACA,IAAIlB,IAAI,CAACpK,QAAQ,IAAIoK,IAAI,CAACpK,QAAQ,CAAClE,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMyQ,aAAa,GAAGpH,QAAQ,CAACgC,aAAa,CAAC,IAAI,CAAC;QAClDoF,aAAa,CAAC5C,WAAW,GAAG,iBAAiB;QAC7C4C,aAAa,CAACnF,KAAK,CAAC0E,SAAS,GAAG,MAAM;QACtCS,aAAa,CAACnF,KAAK,CAACkC,YAAY,GAAG,MAAM;QACzCiD,aAAa,CAACnF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC0D,aAAa,CAACnF,KAAK,CAACyC,UAAU,GAAG,KAAK;QACtCwB,YAAY,CAACvB,WAAW,CAACyC,aAAa,CAAC;QAEvC,MAAMC,YAAY,GAAGrH,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QAClDqF,YAAY,CAACpF,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnC4E,YAAY,CAACpF,KAAK,CAAC4C,aAAa,GAAG,QAAQ;QAC3CwC,YAAY,CAACpF,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/BG,IAAI,CAACpK,QAAQ,CAACyM,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAI;UACvC,MAAMC,WAAW,GAAGzH,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;UACjDyF,WAAW,CAACxF,KAAK,CAACY,OAAO,GAAG,MAAM;UAClC4E,WAAW,CAACxF,KAAK,CAACM,eAAe,GAAG,SAAS;UAC7CkF,WAAW,CAACxF,KAAK,CAACa,YAAY,GAAG,KAAK;UACtC2E,WAAW,CAACxF,KAAK,CAACuB,MAAM,GAAG,gBAAgB;UAE3C,MAAMkE,aAAa,GAAG1H,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;UACnD0F,aAAa,CAACzF,KAAK,CAACQ,OAAO,GAAG,MAAM;UACpCiF,aAAa,CAACzF,KAAK,CAACS,cAAc,GAAG,eAAe;UACpDgF,aAAa,CAACzF,KAAK,CAACkC,YAAY,GAAG,MAAM;UACzCuD,aAAa,CAACzF,KAAK,CAACmC,aAAa,GAAG,MAAM;UAC1CsD,aAAa,CAACzF,KAAK,CAACoC,YAAY,GAAG,gBAAgB;UAEnD,MAAMsD,YAAY,GAAG3H,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;UAClD2F,YAAY,CAACrE,SAAS,GAAG,mBAAmBkE,KAAK,GAAG,CAAC,cAAcD,OAAO,CAACzQ,OAAO,EAAE1B,IAAI,IAAI,SAAS,IAAImS,OAAO,CAACtM,QAAQ,EAAE;UAE3H,MAAM2M,eAAe,GAAG5H,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;UACrD4F,eAAe,CAACpD,WAAW,GAAG,IAAI,CAACjJ,cAAc,CAACgM,OAAO,CAAC/L,QAAQ,CAAC;UACnEoM,eAAe,CAAC3F,KAAK,CAACkB,KAAK,GAAG,MAAM;UAEpCuE,aAAa,CAAC/C,WAAW,CAACgD,YAAY,CAAC;UACvCD,aAAa,CAAC/C,WAAW,CAACiD,eAAe,CAAC;UAC1CH,WAAW,CAAC9C,WAAW,CAAC+C,aAAa,CAAC;UAEtC;UACA,MAAMG,YAAY,GAAG7H,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;UAClD6F,YAAY,CAAC5F,KAAK,CAACQ,OAAO,GAAG,MAAM;UACnCoF,YAAY,CAAC5F,KAAK,CAACU,UAAU,GAAG,QAAQ;UACxCkF,YAAY,CAAC5F,KAAK,CAACS,cAAc,GAAG,eAAe;UAEnD;UACA,MAAMoF,gBAAgB,GAAG9H,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;UACtD8F,gBAAgB,CAAC7F,KAAK,CAACoE,IAAI,GAAG,GAAG;UAEjC,IAAIkB,OAAO,CAACrM,SAAS,EAAE;YACrB,MAAM6M,OAAO,GAAG/H,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;YAC7C+F,OAAO,CAAC9F,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCqD,OAAO,CAACvD,WAAW,GAAG,IAAIrE,IAAI,CAACoH,OAAO,CAACrM,SAAS,CAACG,IAAI,CAAC,CAACkL,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAElH,MAAMuB,UAAU,GAAGhI,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;YAChDgG,UAAU,CAAC/F,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClCsE,UAAU,CAACxD,WAAW,GAAG,GAAG+C,OAAO,CAACrM,SAAS,CAACI,OAAO,EAAEzG,IAAI,IAAI,KAAK,KAAK0S,OAAO,CAACrM,SAAS,CAACpG,IAAI,EAAEM,IAAI,IAAI,KAAK,GAAG;YAEjH0S,gBAAgB,CAACnD,WAAW,CAACoD,OAAO,CAAC;YACrCD,gBAAgB,CAACnD,WAAW,CAACqD,UAAU,CAAC;;UAG1C;UACA,MAAMC,KAAK,GAAGjI,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;UAC3CiG,KAAK,CAAC3E,SAAS,GAAG,qFAAqF;UAEvG;UACA,MAAM4E,cAAc,GAAGlI,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;UACpDkG,cAAc,CAACjG,KAAK,CAACoE,IAAI,GAAG,GAAG;UAC/B6B,cAAc,CAACjG,KAAK,CAACmE,SAAS,GAAG,OAAO;UAExC,IAAImB,OAAO,CAACxM,OAAO,EAAE;YACnB,MAAMoN,OAAO,GAAGnI,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;YAC7CmG,OAAO,CAAClG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCyD,OAAO,CAAC3D,WAAW,GAAG,IAAIrE,IAAI,CAACoH,OAAO,CAACxM,OAAO,CAACM,IAAI,CAAC,CAACkL,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAEhH,MAAM2B,UAAU,GAAGpI,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;YAChDoG,UAAU,CAACnG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClC0E,UAAU,CAAC5D,WAAW,GAAG,GAAG+C,OAAO,CAACxM,OAAO,CAACO,OAAO,EAAEzG,IAAI,IAAI,KAAK,KAAK0S,OAAO,CAACxM,OAAO,CAACjG,IAAI,EAAEM,IAAI,IAAI,KAAK,GAAG;YAE7G8S,cAAc,CAACvD,WAAW,CAACwD,OAAO,CAAC;YACnCD,cAAc,CAACvD,WAAW,CAACyD,UAAU,CAAC;;UAGxCP,YAAY,CAAClD,WAAW,CAACmD,gBAAgB,CAAC;UAC1CD,YAAY,CAAClD,WAAW,CAACsD,KAAK,CAAC;UAC/BJ,YAAY,CAAClD,WAAW,CAACuD,cAAc,CAAC;UACxCT,WAAW,CAAC9C,WAAW,CAACkD,YAAY,CAAC;UAErCR,YAAY,CAAC1C,WAAW,CAAC8C,WAAW,CAAC;UAErC;UACA,IAAID,KAAK,GAAGvC,IAAI,CAACpK,QAAQ,CAAClE,MAAM,GAAG,CAAC,EAAE;YACpC,MAAM0R,OAAO,GAAGrI,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;YAC7CqG,OAAO,CAACpG,KAAK,CAACmE,SAAS,GAAG,QAAQ;YAClCiC,OAAO,CAACpG,KAAK,CAACY,OAAO,GAAG,MAAM;YAC9BwF,OAAO,CAACpG,KAAK,CAACkB,KAAK,GAAG,SAAS;YAC/BkF,OAAO,CAACpG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAE/B;YACA,MAAM4E,cAAc,GAAG,IAAInI,IAAI,CAACoH,OAAO,CAACxM,OAAO,EAAEM,IAAI,IAAI,CAAC,CAAC,CAACkN,OAAO,EAAE;YACrE,MAAMC,aAAa,GAAG,IAAIrI,IAAI,CAAC8E,IAAI,CAACpK,QAAQ,CAAC2M,KAAK,GAAG,CAAC,CAAC,CAACtM,SAAS,EAAEG,IAAI,IAAI,CAAC,CAAC,CAACkN,OAAO,EAAE;YACvF,MAAME,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,aAAa,GAAGF,cAAc,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;YAE9ED,OAAO,CAAC/E,SAAS,GAAG,yCAAyC,IAAI,CAAC/H,cAAc,CAACkN,WAAW,CAAC,eAAelB,OAAO,CAACxM,OAAO,EAAEjG,IAAI,EAAEM,IAAI,IAAI,iBAAiB,EAAE;YAE9JiS,YAAY,CAAC1C,WAAW,CAAC0D,OAAO,CAAC;;QAErC,CAAC,CAAC;QAEFnC,YAAY,CAACvB,WAAW,CAAC0C,YAAY,CAAC;;;IAI1C;IACA,MAAMuB,aAAa,GAAG,IAAI,CAAC5D,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAE5D,IAAIlD,MAAM,CAACnK,MAAM,IAAImK,MAAM,CAACnK,MAAM,CAAChB,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAMkS,UAAU,GAAG7I,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;MAChD6G,UAAU,CAAC5G,KAAK,CAACQ,OAAO,GAAG,MAAM;MACjCoG,UAAU,CAAC5G,KAAK,CAAC4C,aAAa,GAAG,QAAQ;MACzCgE,UAAU,CAAC5G,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE7BhD,MAAM,CAACnK,MAAM,CAAC2P,OAAO,CAAC,CAACwB,KAAK,EAAEtB,KAAK,KAAI;QACrC,MAAMuB,SAAS,GAAG/I,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QAC/C+G,SAAS,CAAC9G,KAAK,CAACY,OAAO,GAAG,MAAM;QAChCkG,SAAS,CAAC9G,KAAK,CAACM,eAAe,GAAG,SAAS;QAC3CwG,SAAS,CAAC9G,KAAK,CAACa,YAAY,GAAG,KAAK;QACpCiG,SAAS,CAAC9G,KAAK,CAACuB,MAAM,GAAG,gBAAgB;QAEzC;QACA,MAAMwF,WAAW,GAAGhJ,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QACjDgH,WAAW,CAAC/G,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCuG,WAAW,CAAC/G,KAAK,CAACS,cAAc,GAAG,eAAe;QAClDsG,WAAW,CAAC/G,KAAK,CAACkC,YAAY,GAAG,MAAM;QACvC6E,WAAW,CAAC/G,KAAK,CAACmC,aAAa,GAAG,MAAM;QACxC4E,WAAW,CAAC/G,KAAK,CAACoC,YAAY,GAAG,gBAAgB;QAEjD,MAAM4E,UAAU,GAAGjJ,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QAChDiH,UAAU,CAAC3F,SAAS,GAAG,iBAAiBkE,KAAK,GAAG,CAAC,WAAW;QAC5DyB,UAAU,CAAChH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAElC,MAAMwF,UAAU,GAAGlJ,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QAChDkH,UAAU,CAACjH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAClCwF,UAAU,CAACjH,KAAK,CAACyC,UAAU,GAAG,MAAM;QACpCwE,UAAU,CAACjH,KAAK,CAACkB,KAAK,GAAG,SAAS;QAClC+F,UAAU,CAAC1E,WAAW,GAAG,GAAGsE,KAAK,CAAC5O,KAAK,CAACC,MAAM,IAAI2O,KAAK,CAAC5O,KAAK,CAACE,QAAQ,EAAE;QAExE4O,WAAW,CAACrE,WAAW,CAACsE,UAAU,CAAC;QACnCD,WAAW,CAACrE,WAAW,CAACuE,UAAU,CAAC;QACnCH,SAAS,CAACpE,WAAW,CAACqE,WAAW,CAAC;QAElC;QACA,MAAMG,YAAY,GAAGnJ,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;QAClDmH,YAAY,CAAClH,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnC0G,YAAY,CAAClH,KAAK,CAACmH,mBAAmB,GAAG,uCAAuC;QAChFD,YAAY,CAAClH,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B;QACA,MAAMuE,iBAAiB,GAAGP,KAAK,CAACQ,YAAY,KAAKC,SAAS,GAAGT,KAAK,CAACQ,YAAY,GACrDR,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACC,kBAAkB,GAAG,CAAE;QACjF,MAAMH,YAAY,GAAG,IAAI,CAAC3D,aAAa,CAAC,cAAc,EAAE0D,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,eAAe,CAAC;QAC9GF,YAAY,CAACxE,WAAW,CAAC2E,YAAY,CAAC;QAEtC;QACA,IAAIR,KAAK,CAAClR,SAAS,EAAE;UACnB,MAAM8R,OAAO,GAAG,IAAI,CAAC/D,aAAa,CAAC,YAAY,EAAE,IAAIxF,IAAI,CAAC2I,KAAK,CAAClR,SAAS,CAAC,CAAC+R,cAAc,EAAE,CAAC;UAC5FR,YAAY,CAACxE,WAAW,CAAC+E,OAAO,CAAC;;QAGnC;QACA,IAAIZ,KAAK,CAACpN,WAAW,EAAE;UACrB,MAAMA,WAAW,GAAG,IAAI,CAACiK,aAAa,CAAC,cAAc,EAAEmD,KAAK,CAACpN,WAAW,CAACtG,IAAI,CAAC;UAC9E+T,YAAY,CAACxE,WAAW,CAACjJ,WAAW,CAAC;;QAGvC;QACA,IAAIoN,KAAK,CAACtP,cAAc,EAAE;UACxB,MAAMC,UAAU,GAAG,IAAI,CAACkM,aAAa,CAAC,YAAY,EAAEmD,KAAK,CAACtP,cAAc,CAACC,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC;UACnG0P,YAAY,CAACxE,WAAW,CAAClL,UAAU,CAAC;;QAGtC;QACA,IAAIqP,KAAK,CAACrQ,mBAAmB,IAAIqQ,KAAK,CAACrQ,mBAAmB,CAAC9B,MAAM,GAAG,CAAC,EAAE;UACrE,MAAMiT,YAAY,GAAG5J,QAAQ,CAACgC,aAAa,CAAC,IAAI,CAAC;UACjD4H,YAAY,CAACpF,WAAW,GAAG,qBAAqB;UAChDoF,YAAY,CAAC3H,KAAK,CAAC0E,SAAS,GAAG,MAAM;UACrCiD,YAAY,CAAC3H,KAAK,CAACkC,YAAY,GAAG,MAAM;UACxCyF,YAAY,CAAC3H,KAAK,CAACyB,QAAQ,GAAG,MAAM;UACpCkG,YAAY,CAAC3H,KAAK,CAACyC,UAAU,GAAG,KAAK;UACrCqE,SAAS,CAACpE,WAAW,CAACiF,YAAY,CAAC;UAEnC,MAAMC,gBAAgB,GAAG7J,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;UACtD6H,gBAAgB,CAAC5H,KAAK,CAACQ,OAAO,GAAG,MAAM;UACvCoH,gBAAgB,CAAC5H,KAAK,CAAC4C,aAAa,GAAG,QAAQ;UAC/CgF,gBAAgB,CAAC5H,KAAK,CAAC6C,GAAG,GAAG,MAAM;UACnC+E,gBAAgB,CAAC5H,KAAK,CAACkC,YAAY,GAAG,MAAM;UAE5C;UACA,MAAM2F,cAAc,GAAGhB,KAAK,CAACrQ,mBAAmB,CAACsR,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,CAAC,CAAC;UACjF,MAAMC,YAAY,GAAGpB,KAAK,CAACrQ,mBAAmB,CAACsR,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,CAAC,CAAC;UAC/E,MAAME,WAAW,GAAGrB,KAAK,CAACrQ,mBAAmB,CAACsR,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,CAAC,CAAC;UAE9E;UACA,IAAIH,cAAc,CAACnT,MAAM,GAAG,CAAC,EAAE;YAC7BmT,cAAc,CAACxC,OAAO,CAAC8C,OAAO,IAAG;cAC/B,MAAMC,WAAW,GAAGrK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC4H,WAAW,CAACpI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvC0H,WAAW,CAACpI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCwH,WAAW,CAACpI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C8H,WAAW,CAACpI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCuH,WAAW,CAACpI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM8G,WAAW,GAAGtK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDsI,WAAW,CAAChH,SAAS,GAAG,8FAA8F;cAEtH,MAAMiH,WAAW,GAAGvK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDuI,WAAW,CAACtI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC8H,WAAW,CAACtI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMoF,WAAW,GAAGjK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDiI,WAAW,CAACzF,WAAW,GAAG,iBAAiB;cAC3CyF,WAAW,CAAChI,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCuF,WAAW,CAAChI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM8G,cAAc,GAAGxK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACpDwI,cAAc,CAACvI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC8G,cAAc,CAACvI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAIsH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACrS,MAAM,GAAG,CAAC,EAAE0S,WAAW,IAAI,GAAGL,OAAO,CAACrS,MAAM,KAAK;cAC7D,IAAIqS,OAAO,CAACpS,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIyS,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACpS,KAAK,WAAW;;cAE5CwS,cAAc,CAAChG,WAAW,GAAGiG,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAAC5F,WAAW,CAACsF,WAAW,CAAC;cACpCM,WAAW,CAAC5F,WAAW,CAAC6F,cAAc,CAAC;cAEvCH,WAAW,CAAC1F,WAAW,CAAC2F,WAAW,CAAC;cACpCD,WAAW,CAAC1F,WAAW,CAAC4F,WAAW,CAAC;cACpCV,gBAAgB,CAAClF,WAAW,CAAC0F,WAAW,CAAC;YAC3C,CAAC,CAAC;;UAGJ;UACA,IAAIH,YAAY,CAACvT,MAAM,GAAG,CAAC,EAAE;YAC3BuT,YAAY,CAAC5C,OAAO,CAAC8C,OAAO,IAAG;cAC7B,MAAMC,WAAW,GAAGrK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC4H,WAAW,CAACpI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvC0H,WAAW,CAACpI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCwH,WAAW,CAACpI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C8H,WAAW,CAACpI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCuH,WAAW,CAACpI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM8G,WAAW,GAAGtK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDsI,WAAW,CAAChH,SAAS,GAAG,+FAA+F;cAEvH,MAAMiH,WAAW,GAAGvK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDuI,WAAW,CAACtI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC8H,WAAW,CAACtI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMoF,WAAW,GAAGjK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDiI,WAAW,CAACzF,WAAW,GAAG,eAAe;cACzCyF,WAAW,CAAChI,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCuF,WAAW,CAAChI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM8G,cAAc,GAAGxK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACpDwI,cAAc,CAACvI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC8G,cAAc,CAACvI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAIsH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACrS,MAAM,GAAG,CAAC,EAAE0S,WAAW,IAAI,GAAGL,OAAO,CAACrS,MAAM,KAAK;cAC7D,IAAIqS,OAAO,CAACpS,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIyS,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACpS,KAAK,WAAW;;cAE5CwS,cAAc,CAAChG,WAAW,GAAGiG,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAAC5F,WAAW,CAACsF,WAAW,CAAC;cACpCM,WAAW,CAAC5F,WAAW,CAAC6F,cAAc,CAAC;cAEvCH,WAAW,CAAC1F,WAAW,CAAC2F,WAAW,CAAC;cACpCD,WAAW,CAAC1F,WAAW,CAAC4F,WAAW,CAAC;cACpCV,gBAAgB,CAAClF,WAAW,CAAC0F,WAAW,CAAC;YAC3C,CAAC,CAAC;WACH,MAAM;YACL;YACA,MAAMA,WAAW,GAAGrK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;YACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;YAClC4H,WAAW,CAACpI,KAAK,CAACU,UAAU,GAAG,QAAQ;YACvC0H,WAAW,CAACpI,KAAK,CAACY,OAAO,GAAG,WAAW;YACvCwH,WAAW,CAACpI,KAAK,CAACM,eAAe,GAAG,SAAS;YAC7C8H,WAAW,CAACpI,KAAK,CAACa,YAAY,GAAG,KAAK;YACtCuH,WAAW,CAACpI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;YAE9C,MAAM8G,WAAW,GAAGtK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;YACjDsI,WAAW,CAAChH,SAAS,GAAG,+FAA+F;YAEvH,MAAMiH,WAAW,GAAGvK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;YACjDuI,WAAW,CAACtI,KAAK,CAACQ,OAAO,GAAG,MAAM;YAClC8H,WAAW,CAACtI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;YAE1C,MAAMoF,WAAW,GAAGjK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;YACjDiI,WAAW,CAACzF,WAAW,GAAG,eAAe;YACzCyF,WAAW,CAAChI,KAAK,CAACyC,UAAU,GAAG,KAAK;YACpCuF,WAAW,CAAChI,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAEnC,MAAM8G,cAAc,GAAGxK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;YACpDwI,cAAc,CAACvI,KAAK,CAACyB,QAAQ,GAAG,MAAM;YACtC8G,cAAc,CAACvI,KAAK,CAACkB,KAAK,GAAG,MAAM;YACnCqH,cAAc,CAAChG,WAAW,GAAG,UAAU;YAEvC+F,WAAW,CAAC5F,WAAW,CAACsF,WAAW,CAAC;YACpCM,WAAW,CAAC5F,WAAW,CAAC6F,cAAc,CAAC;YAEvCH,WAAW,CAAC1F,WAAW,CAAC2F,WAAW,CAAC;YACpCD,WAAW,CAAC1F,WAAW,CAAC4F,WAAW,CAAC;YACpCV,gBAAgB,CAAClF,WAAW,CAAC0F,WAAW,CAAC;;UAG3C;UACA,IAAIF,WAAW,CAACxT,MAAM,GAAG,CAAC,EAAE;YAC1BwT,WAAW,CAAC7C,OAAO,CAAC8C,OAAO,IAAG;cAC5B,MAAMC,WAAW,GAAGrK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDqI,WAAW,CAACpI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC4H,WAAW,CAACpI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvC0H,WAAW,CAACpI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCwH,WAAW,CAACpI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C8H,WAAW,CAACpI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCuH,WAAW,CAACpI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM8G,WAAW,GAAGtK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDsI,WAAW,CAAChH,SAAS,GAAG,kGAAkG;cAE1H,MAAMiH,WAAW,GAAGvK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDuI,WAAW,CAACtI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC8H,WAAW,CAACtI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMoF,WAAW,GAAGjK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACjDiI,WAAW,CAACzF,WAAW,GAAG,cAAc;cACxCyF,WAAW,CAAChI,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCuF,WAAW,CAAChI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM8G,cAAc,GAAGxK,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;cACpDwI,cAAc,CAACvI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC8G,cAAc,CAACvI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAIsH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACrS,MAAM,GAAG,CAAC,EAAE0S,WAAW,IAAI,GAAGL,OAAO,CAACrS,MAAM,KAAK;cAC7D,IAAIqS,OAAO,CAACpS,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIyS,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACpS,KAAK,WAAW;;cAE5CwS,cAAc,CAAChG,WAAW,GAAGiG,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAAC5F,WAAW,CAACsF,WAAW,CAAC;cACpCM,WAAW,CAAC5F,WAAW,CAAC6F,cAAc,CAAC;cAEvCH,WAAW,CAAC1F,WAAW,CAAC2F,WAAW,CAAC;cACpCD,WAAW,CAAC1F,WAAW,CAAC4F,WAAW,CAAC;cACpCV,gBAAgB,CAAClF,WAAW,CAAC0F,WAAW,CAAC;YAC3C,CAAC,CAAC;;UAGJtB,SAAS,CAACpE,WAAW,CAACkF,gBAAgB,CAAC;;QAGzCd,SAAS,CAACpE,WAAW,CAACwE,YAAY,CAAC;QAEnCN,UAAU,CAAClE,WAAW,CAACoE,SAAS,CAAC;MACnC,CAAC,CAAC;MAEFH,aAAa,CAACjE,WAAW,CAACkE,UAAU,CAAC;;IAGvC;IACA,IAAI/G,MAAM,CAACjL,KAAK,IAAIiL,MAAM,CAACjL,KAAK,CAAC,CAAC,CAAC,IAAIiL,MAAM,CAACjL,KAAK,CAAC,CAAC,CAAC,CAACyC,QAAQ,IAAIwI,MAAM,CAACjL,KAAK,CAAC,CAAC,CAAC,CAACyC,QAAQ,CAAC3C,MAAM,GAAG,CAAC,EAAE;MACtG,MAAM+T,eAAe,GAAG,IAAI,CAAC1F,aAAa,CAAC,UAAU,EAAE,mBAAmB,CAAC;MAE3E,MAAM2F,YAAY,GAAG3K,QAAQ,CAACgC,aAAa,CAAC,IAAI,CAAC;MACjD2I,YAAY,CAAC1I,KAAK,CAAC2I,SAAS,GAAG,MAAM;MACrCD,YAAY,CAAC1I,KAAK,CAACY,OAAO,GAAG,GAAG;MAChC8H,YAAY,CAAC1I,KAAK,CAACwC,MAAM,GAAG,GAAG;MAC/BkG,YAAY,CAAC1I,KAAK,CAACQ,OAAO,GAAG,MAAM;MACnCkI,YAAY,CAAC1I,KAAK,CAACmH,mBAAmB,GAAG,uCAAuC;MAChFuB,YAAY,CAAC1I,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE/BhD,MAAM,CAACjL,KAAK,CAAC,CAAC,CAAC,CAACyC,QAAQ,CAACgO,OAAO,CAACuD,OAAO,IAAG;QACzC,MAAMC,WAAW,GAAG9K,QAAQ,CAACgC,aAAa,CAAC,IAAI,CAAC;QAChD8I,WAAW,CAAC7I,KAAK,CAACY,OAAO,GAAG,MAAM;QAClCiI,WAAW,CAAC7I,KAAK,CAACM,eAAe,GAAG,SAAS;QAC7CuI,WAAW,CAAC7I,KAAK,CAACa,YAAY,GAAG,KAAK;QACtCgI,WAAW,CAACxH,SAAS,GAAG,2EAA2EuH,OAAO,CAACzV,IAAI,IAAI,SAAS,EAAE;QAC9HuV,YAAY,CAAChG,WAAW,CAACmG,WAAW,CAAC;MACvC,CAAC,CAAC;MAEFJ,eAAe,CAAC/F,WAAW,CAACgG,YAAY,CAAC;MACzC/F,gBAAgB,CAACD,WAAW,CAAC+F,eAAe,CAAC;;IAG/C;IACA9F,gBAAgB,CAACD,WAAW,CAACI,WAAW,CAAC;IACzCH,gBAAgB,CAACD,WAAW,CAACuB,YAAY,CAAC;IAC1CtB,gBAAgB,CAACD,WAAW,CAACiE,aAAa,CAAC;IAE3C;IACAhG,YAAY,CAAC+B,WAAW,CAACtB,WAAW,CAAC;IACrCT,YAAY,CAAC+B,WAAW,CAACT,MAAM,CAAC;IAChCtB,YAAY,CAAC+B,WAAW,CAACC,gBAAgB,CAAC;IAC1C7C,QAAQ,CAAC4C,WAAW,CAAC/B,YAAY,CAAC;IAElC;IACA5C,QAAQ,CAACgE,IAAI,CAACW,WAAW,CAAC5C,QAAQ,CAAC;EACrC;EAEA;EACQiD,aAAaA,CAACT,KAAa,EAAEwG,SAAiB;IACpD,MAAMC,OAAO,GAAGhL,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;IAC7CgJ,OAAO,CAAC/I,KAAK,CAACM,eAAe,GAAG,SAAS;IACzCyI,OAAO,CAAC/I,KAAK,CAACa,YAAY,GAAG,KAAK;IAClCkI,OAAO,CAAC/I,KAAK,CAACY,OAAO,GAAG,MAAM;IAC9BmI,OAAO,CAAC/I,KAAK,CAACiB,SAAS,GAAG,+BAA+B;IAEzD,MAAM+H,aAAa,GAAGjL,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;IACnDiJ,aAAa,CAAChJ,KAAK,CAACQ,OAAO,GAAG,MAAM;IACpCwI,aAAa,CAAChJ,KAAK,CAACU,UAAU,GAAG,QAAQ;IACzCsI,aAAa,CAAChJ,KAAK,CAACkC,YAAY,GAAG,MAAM;IAEzC,MAAM+G,IAAI,GAAGlL,QAAQ,CAACgC,aAAa,CAAC,GAAG,CAAC;IACxCkJ,IAAI,CAACC,SAAS,GAAG,OAAOJ,SAAS,EAAE;IACnCG,IAAI,CAACjJ,KAAK,CAACkB,KAAK,GAAG,SAAS;IAC5B+H,IAAI,CAACjJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC5BwH,IAAI,CAACjJ,KAAK,CAACqD,WAAW,GAAG,MAAM;IAE/B,MAAM8F,YAAY,GAAGpL,QAAQ,CAACgC,aAAa,CAAC,IAAI,CAAC;IACjDoJ,YAAY,CAAC5G,WAAW,GAAGD,KAAK;IAChC6G,YAAY,CAACnJ,KAAK,CAACwC,MAAM,GAAG,GAAG;IAC/B2G,YAAY,CAACnJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpC0H,YAAY,CAACnJ,KAAK,CAACyC,UAAU,GAAG,KAAK;IAErCuG,aAAa,CAACtG,WAAW,CAACuG,IAAI,CAAC;IAC/BD,aAAa,CAACtG,WAAW,CAACyG,YAAY,CAAC;IACvCJ,OAAO,CAACrG,WAAW,CAACsG,aAAa,CAAC;IAElC,OAAOD,OAAO;EAChB;EAEA;EACQrF,aAAaA,CAAC7P,KAAa,EAAED,KAAa;IAChD,MAAMwV,GAAG,GAAGrL,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;IACzCqJ,GAAG,CAACpJ,KAAK,CAACkC,YAAY,GAAG,MAAM;IAE/B,MAAMmH,YAAY,GAAGtL,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;IAClDsJ,YAAY,CAAC9G,WAAW,GAAG1O,KAAK;IAChCwV,YAAY,CAACrJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpC4H,YAAY,CAACrJ,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCmI,YAAY,CAACrJ,KAAK,CAACkC,YAAY,GAAG,KAAK;IAEvC,MAAMoH,YAAY,GAAGvL,QAAQ,CAACgC,aAAa,CAAC,KAAK,CAAC;IAClDuJ,YAAY,CAAC/G,WAAW,GAAG3O,KAAK;IAChC0V,YAAY,CAACtJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAEpC2H,GAAG,CAAC1G,WAAW,CAAC2G,YAAY,CAAC;IAC7BD,GAAG,CAAC1G,WAAW,CAAC4G,YAAY,CAAC;IAE7B,OAAOF,GAAG;EACZ;EAEA;EACAxN,gBAAgBA,CAACiE,MAAc;IAC7B,IAAIA,MAAM,IAAIA,MAAM,CAACnK,MAAM,IAAImK,MAAM,CAACnK,MAAM,CAAChB,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAI6U,OAAO,GAAG1J,MAAM,CAACnK,MAAM,CAAC,CAAC,CAAC,CAAC6T,OAAO,IAAI1J,MAAM,CAACnK,MAAM,CAAC,CAAC,CAAC,CAAC8T,EAAE;MAE7D;MACA,IAAIC,QAAQ,GAAG,IAAI,CAACrM,YAAY;MAEhC;MACA,IAAI,CAACqM,QAAQ,EAAE;QACbA,QAAQ,GAAG5J,MAAM,CAAC2J,EAAE;;MAGtB7J,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE6J,QAAQ,EAAE,cAAc,EAAEF,OAAO,CAAC;MAExF;MACA,IAAI,CAACvM,MAAM,CAAC0M,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UACXF,QAAQ,EAAEA,QAAQ;UAClBF,OAAO,EAAEA;;OAEZ,CAAC;KACH,MAAM;MACL5J,OAAO,CAACiK,KAAK,CAAC,sCAAsC,EAAE/J,MAAM,CAAC;;EAEjE;EAEAH,gBAAgBA,CAAA;IACd;IACA;IACA;EAAA;EAGFD,iBAAiBA,CAAA;IACf;IACA,MAAMd,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAACwL,GAAG,CAAC,uBAAuB,CAAC,EAAEjW,KAAK,IAAI,CAAC;IACtF,MAAMiL,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAACwL,GAAG,CAAC,qBAAqB,CAAC,EAAEjW,KAAK,IAAI,CAAC;IAElF,IAAI,CAACmJ,cAAc,CAAC+M,kBAAkB,CAACnL,qBAAqB,CAAC,CAACoL,SAAS,CAACC,SAAS,IAAG;MAClF,IAAI,CAAC/M,kBAAkB,GAAG+M,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAACjN,cAAc,CAAC+M,kBAAkB,CAACjL,mBAAmB,CAAC,CAACkL,SAAS,CAACC,SAAS,IAAG;MAChF,IAAI,CAAC9M,gBAAgB,GAAG8M,SAAS;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC3L,UAAU,CAACwL,GAAG,CAAC,uBAAuB,CAAC,EAAEI,YAAY,CACvDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAACnN,cAAc,CAAC+M,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAAC/M,kBAAkB,GAAG+M,SAAS;QACnC;QACA,IAAI,CAAC3L,UAAU,CAACwL,GAAG,CAAC,mBAAmB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC9L,UAAU,CAACwL,GAAG,CAAC,qBAAqB,CAAC,EAAEI,YAAY,CACrDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAACnN,cAAc,CAAC+M,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAAC9M,gBAAgB,GAAG8M,SAAS;QACjC;QACA,IAAI,CAAC3L,UAAU,CAACwL,GAAG,CAAC,iBAAiB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACtD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC9L,UAAU,CAACwL,GAAG,CAAC,mBAAmB,CAAC,EAAEI,YAAY,CACnDG,IAAI,CACHvY,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC6B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMsW,YAAY,GAAG,IAAI,CAAC7L,UAAU,CAACwL,GAAG,CAAC,uBAAuB,CAAC,EAAEjW,KAAK,IAAI,CAAC;QAC7E;QACA,IAAIA,KAAK,CAACc,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACqI,cAAc,CAAC+M,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9DpY,GAAG,CAACgY,SAAS,IAAIA,SAAS,CAAClC,MAAM,CAACuC,QAAQ,IACxCA,QAAQ,CAAClX,IAAI,CAACmX,WAAW,EAAE,CAACC,QAAQ,CAAC3W,KAAK,CAAC0W,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACzX,IAAI,IAAIyX,QAAQ,CAACzX,IAAI,CAAC0X,WAAW,EAAE,CAACC,QAAQ,CAAC3W,KAAK,CAAC0W,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAACvN,cAAc,CAAC+M,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAOjY,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA8X,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAAC/M,kBAAkB,GAAG+M,SAAS;IACrC,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC3L,UAAU,CAACwL,GAAG,CAAC,iBAAiB,CAAC,EAAEI,YAAY,CACjDG,IAAI,CACHvY,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC6B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMsW,YAAY,GAAG,IAAI,CAAC7L,UAAU,CAACwL,GAAG,CAAC,qBAAqB,CAAC,EAAEjW,KAAK,IAAI,CAAC;QAC3E;QACA,IAAIA,KAAK,CAACc,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACqI,cAAc,CAAC+M,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9DpY,GAAG,CAACgY,SAAS,IAAIA,SAAS,CAAClC,MAAM,CAACuC,QAAQ,IACxCA,QAAQ,CAAClX,IAAI,CAACmX,WAAW,EAAE,CAACC,QAAQ,CAAC3W,KAAK,CAAC0W,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACzX,IAAI,IAAIyX,QAAQ,CAACzX,IAAI,CAAC0X,WAAW,EAAE,CAACC,QAAQ,CAAC3W,KAAK,CAAC0W,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAACvN,cAAc,CAAC+M,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAOjY,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA8X,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAAC9M,gBAAgB,GAAG8M,SAAS;IACnC,CAAC,CAAC;EACN;EAEAQ,eAAeA,CAACH,QAA+B;IAC7C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAII,WAAW,GAAGJ,QAAQ,CAAClX,IAAI;IAC/B,IAAIkX,QAAQ,CAACzX,IAAI,EAAE;MACjB6X,WAAW,IAAI,KAAKJ,QAAQ,CAACzX,IAAI,GAAG;;IAEtC,IAAIyX,QAAQ,CAACjX,IAAI,KAAKjB,YAAY,CAACuY,OAAO,IAAIL,QAAQ,CAACxX,IAAI,EAAE;MAC3D4X,WAAW,IAAI,MAAMJ,QAAQ,CAACxX,IAAI,EAAE;;IAEtC,OAAO4X,WAAW;EACpB;EAEApW,QAAQA,CAAA;IACN,IAAI,IAAI,CAACgK,UAAU,CAACsM,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACvM,UAAU,CAAC;MAC1C;;IAGF,IAAI,CAAC9B,SAAS,GAAG,IAAI;IACrB,IAAI,CAAChI,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC4I,WAAW,GAAG,IAAI;IAEvB,MAAM0N,SAAS,GAAG,IAAI,CAACxM,UAAU,CAACzK,KAAK;IAEvC;IACA,MAAMkX,OAAO,GAAuB;MAClCC,WAAW,EAAEF,SAAS,CAACtM,WAAW;MAClCyM,YAAY,EAAEH,SAAS,CAACpM,YAAY;MACpCwM,OAAO,EAAEJ,SAAS,CAAC/L,aAAa;MAChCoM,kBAAkB,EAAE,CAClB;QACE1B,EAAE,EAAEqB,SAAS,CAACnM,iBAAiB,EAAE8K,EAAE,IAAI,EAAE;QACzCpW,IAAI,EAAEyX,SAAS,CAAClM;OACjB,CACF;MACDwM,gBAAgB,EAAE,CAChB;QACE3B,EAAE,EAAEqB,SAAS,CAACjM,eAAe,EAAE4K,EAAE,IAAI,EAAE;QACvCpW,IAAI,EAAEyX,SAAS,CAAChM;OACjB,CACF;MACDuM,UAAU,EAAE,CACV;QACEhY,IAAI,EAAEyX,SAAS,CAAC/S,aAAa;QAC7BuT,KAAK,EAAER,SAAS,CAAC9S;OAClB,CACF;MACDuT,qBAAqB,EAAET,SAAS,CAAC5L,OAAO;MACxCsM,oBAAoB,EAAE;QACpBC,oBAAoB,EAAE;UACpBjM,sBAAsB,EAAEsL,SAAS,CAACtL;;OAErC;MACDJ,sBAAsB,EAAE0L,SAAS,CAAC1L,sBAAsB;MACxDC,wBAAwB,EAAEyL,SAAS,CAACzL,wBAAwB;MAC5DC,6BAA6B,EAAEwL,SAAS,CAACxL,6BAA6B;MACtEC,mBAAmB,EAAEuL,SAAS,CAACvL,mBAAmB;MAClD5B,aAAa,EAAE,CAACmN,SAAS,CAAC1V,WAAW,CAAC;MACtCsW,OAAO,EAAEZ,SAAS,CAAC3L,OAAO;MAC1BwM,QAAQ,EAAEb,SAAS,CAAC1S;KACrB;IAED,IAAI,CAAC4E,cAAc,CAAC4O,WAAW,CAACb,OAAO,CAAC,CACrCf,SAAS,CAAC;MACT6B,IAAI,EAAGC,QAA6B,IAAI;QACtC,IAAI,CAACtP,SAAS,GAAG,KAAK;QACtB,IAAIsP,QAAQ,CAAC5J,MAAM,CAAC6J,OAAO,EAAE;UAC3B,IAAI,CAACrX,aAAa,GAAGoX,QAAQ,CAAC9J,IAAI,CAACgK,OAAO;UAE1C;UACApM,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEoM,IAAI,CAACC,SAAS,CAACJ,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEpE;UACA,IAAIA,QAAQ,CAAC9J,IAAI,IAAI8J,QAAQ,CAAC9J,IAAI,CAACgK,OAAO,IAAIF,QAAQ,CAAC9J,IAAI,CAACgK,OAAO,CAACrX,MAAM,GAAG,CAAC,EAAE;YAC9EiL,OAAO,CAACrB,KAAK,CAAC,uBAAuB,CAAC;YACtCqB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiM,QAAQ,CAAC9J,IAAI,CAACgK,OAAO,CAACrX,MAAM,CAAC;YAE3D;YACA,MAAMwX,iBAAiB,GAAGL,QAAQ,CAAC9J,IAAI,CAACgK,OAAO,CAACjE,MAAM,CAACqE,CAAC,IAAIA,CAAC,CAACzW,MAAM,IAAIyW,CAAC,CAACzW,MAAM,CAAChB,MAAM,GAAG,CAAC,CAAC;YAC5FiL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsM,iBAAiB,CAACxX,MAAM,CAAC;YAE7D;YACA,MAAM0X,kBAAkB,GAAGF,iBAAiB,CAACG,OAAO,CAACF,CAAC,IAAIA,CAAC,CAACzW,MAAM,CAAC1D,GAAG,CAACsa,CAAC,IACtEA,CAAC,CAACjF,YAAY,KAAKC,SAAS,GAAGgF,CAAC,CAACjF,YAAY,GAAIiF,CAAC,CAAC/E,QAAQ,GAAG+E,CAAC,CAAC/E,QAAQ,CAACC,kBAAkB,GAAG,CAAE,CACjG,CAAC;YACF7H,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEwM,kBAAkB,CAAC;YAEvD;YACA,MAAMG,kBAAkB,GAAGH,kBAAkB,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAI;cAChE,IAAIA,GAAG,KAAKpF,SAAS,EAAE;gBACrBmF,GAAG,CAACC,GAAG,CAAC,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEhC,OAAOD,GAAG;YACZ,CAAC,EAAE,EAA4B,CAAC;YAChC9M,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE2M,kBAAkB,CAAC;YAEvD;YACA,MAAMI,iBAAiB,GAAGT,iBAAiB,CAACpE,MAAM,CAACqE,CAAC,IAClDA,CAAC,CAACzW,MAAM,CAACkX,IAAI,CAACN,CAAC,IAAIA,CAAC,CAAC/U,cAAc,IAAI+U,CAAC,CAAC/U,cAAc,CAACC,UAAU,KAAK,IAAI,CAAC,CAC7E;YACDmI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE+M,iBAAiB,CAACjY,MAAM,CAAC;YAE5DiL,OAAO,CAACkN,QAAQ,EAAE;;UAGpB;UACA,IAAIhB,QAAQ,CAAC9J,IAAI,IAAI8J,QAAQ,CAAC9J,IAAI,CAAC0H,QAAQ,EAAE;YAC3C,IAAI,CAACrM,YAAY,GAAGyO,QAAQ,CAAC9J,IAAI,CAAC0H,QAAQ;YAC1C9J,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACxC,YAAY,CAAC;;UAErE;UAAA,KACK,IAAIyO,QAAQ,CAAC5J,MAAM,IAAI4J,QAAQ,CAAC5J,MAAM,CAAC6K,SAAS,EAAE;YACrD,IAAI,CAAC1P,YAAY,GAAGyO,QAAQ,CAAC5J,MAAM,CAAC6K,SAAS;YAC7CnN,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACxC,YAAY,CAAC;;UAExE;UAAA,KACK,IAAIyO,QAAQ,CAAC9J,IAAI,IAAI8J,QAAQ,CAAC9J,IAAI,CAACgK,OAAO,IAAIF,QAAQ,CAAC9J,IAAI,CAACgK,OAAO,CAACrX,MAAM,GAAG,CAAC,IAAImX,QAAQ,CAAC9J,IAAI,CAACgK,OAAO,CAAC,CAAC,CAAC,CAACvC,EAAE,EAAE;YAClH,IAAI,CAACpM,YAAY,GAAGyO,QAAQ,CAAC9J,IAAI,CAACgK,OAAO,CAAC,CAAC,CAAC,CAACvC,EAAE;YAC/C7J,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACxC,YAAY,CAAC;WAChE,MAAM;YACLuC,OAAO,CAACiK,KAAK,CAAC,qCAAqC,CAAC;YACpDjK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEmN,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAC,CAAC;YACzD,IAAIA,QAAQ,CAAC9J,IAAI,EAAEpC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEmN,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAC9J,IAAI,CAAC,CAAC;YAC7E,IAAI8J,QAAQ,CAAC5J,MAAM,EAAEtC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEmN,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAC5J,MAAM,CAAC,CAAC;;SAEtF,MAAM;UACL,IAAI,CAAC1N,YAAY,GAAG,sDAAsD;UAC1E,IAAIsX,QAAQ,CAAC5J,MAAM,CAACgL,QAAQ,IAAIpB,QAAQ,CAAC5J,MAAM,CAACgL,QAAQ,CAACvY,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAACH,YAAY,GAAGsX,QAAQ,CAAC5J,MAAM,CAACgL,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACDtD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACrN,SAAS,GAAG,KAAK;QACtB,IAAI,CAAChI,YAAY,GAAG,wDAAwD;QAC5EoL,OAAO,CAACiK,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEA;EACAgB,oBAAoBA,CAACuC,SAAoB;IACvCJ,MAAM,CAACK,MAAM,CAACD,SAAS,CAACE,QAAQ,CAAC,CAAChI,OAAO,CAACiI,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAY3b,SAAS,EAAE;QAChC,IAAI,CAACiZ,oBAAoB,CAAC0C,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA;EACAhU,cAAcA,CAACkU,OAAe;IAC5B,MAAMC,KAAK,GAAGhH,IAAI,CAACC,KAAK,CAAC8G,OAAO,GAAG,EAAE,CAAC;IACtC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,KAAK,KAAKC,IAAI,KAAK;EAC/B;EAEA;EACAvU,UAAUA,CAACwU,UAAkB;IAC3B,MAAMvU,IAAI,GAAG,IAAI8E,IAAI,CAACyP,UAAU,CAAC;IACjC,OAAOvU,IAAI,CAACyK,kBAAkB,CAAC,OAAO,EAAE;MACtC+J,OAAO,EAAE,OAAO;MAChBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACdvJ,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACAxI,WAAWA,CAAC6D,MAAc;IACxB,IAAI,CAACA,MAAM,CAACnK,MAAM,IAAImK,MAAM,CAACnK,MAAM,CAAChB,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd,MAAMqZ,QAAQ,GAAGlO,MAAM,CAACnK,MAAM,CAAC8W,MAAM,CAAC,CAACzN,GAAG,EAAE8H,KAAK,KAC/CA,KAAK,CAAC5O,KAAK,CAACC,MAAM,GAAG6G,GAAG,CAAC9G,KAAK,CAACC,MAAM,GAAG2O,KAAK,GAAG9H,GAAG,EAAEc,MAAM,CAACnK,MAAM,CAAC,CAAC,CAAC,CAAC;IAExE,OAAOqY,QAAQ,CAAC9V,KAAK,CAAC+V,eAAe,IAAI,GAAGD,QAAQ,CAAC9V,KAAK,CAACC,MAAM,IAAI6V,QAAQ,CAAC9V,KAAK,CAACE,QAAQ,EAAE;EAChG;EAEA;EACA5C,iBAAiBA,CAACsK,MAAc;IAC9B;IACA,IAAI,CAACA,MAAM,CAACnK,MAAM,IAAImK,MAAM,CAACnK,MAAM,CAAChB,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd;IACA,MAAMmS,KAAK,GAAGhH,MAAM,CAACnK,MAAM,CAAC,CAAC,CAAC;IAC9B,MAAM0R,iBAAiB,GAAGP,KAAK,CAACQ,YAAY,KAAKC,SAAS,GAAGT,KAAK,CAACQ,YAAY,GACrDR,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IACjF,OAAOJ,iBAAiB,GAAG,CAAC;EAC9B;EAEA;EACA6G,yBAAyBA,CAAA;IACvB,MAAM/D,YAAY,GAAG,IAAI,CAAC7L,UAAU,CAACwL,GAAG,CAAC,uBAAuB,CAAC,EAAEjW,KAAK,IAAI,CAAC;IAC7E,IAAI,CAACmJ,cAAc,CAAC+M,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAAC/M,kBAAkB,GAAG+M,SAAS;MACnC;MACA,MAAMkE,KAAK,GAAGnQ,QAAQ,CAACoQ,cAAc,CAAC,mBAAmB,CAAqB;MAC9E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAC,uBAAuBA,CAAA;IACrB,MAAMrE,YAAY,GAAG,IAAI,CAAC7L,UAAU,CAACwL,GAAG,CAAC,qBAAqB,CAAC,EAAEjW,KAAK,IAAI,CAAC;IAC3E,IAAI,CAACmJ,cAAc,CAAC+M,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAAC9M,gBAAgB,GAAG8M,SAAS;MACjC;MACA,MAAMkE,KAAK,GAAGnQ,QAAQ,CAACoQ,cAAc,CAAC,iBAAiB,CAAqB;MAC5E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAE,aAAaA,CAAA;IACX,MAAM9P,iBAAiB,GAAG,IAAI,CAACL,UAAU,CAACwL,GAAG,CAAC,mBAAmB,CAAC,EAAEjW,KAAK;IACzE,MAAM+K,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAACwL,GAAG,CAAC,uBAAuB,CAAC,EAAEjW,KAAK;IACjF,MAAMgL,eAAe,GAAG,IAAI,CAACP,UAAU,CAACwL,GAAG,CAAC,iBAAiB,CAAC,EAAEjW,KAAK;IACrE,MAAMiL,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAACwL,GAAG,CAAC,qBAAqB,CAAC,EAAEjW,KAAK;IAE7E,IAAI,CAACyK,UAAU,CAACoQ,UAAU,CAAC;MACzB/P,iBAAiB,EAAEE,eAAe;MAClCD,qBAAqB,EAAEE,mBAAmB;MAC1CD,eAAe,EAAEF,iBAAiB;MAClCG,mBAAmB,EAAEF;KACtB,CAAC;EACJ;EAEA;EACAlJ,oBAAoBA,CAACkY,UAAkB;IACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B;IACA,MAAMvU,IAAI,GAAG,IAAI8E,IAAI,CAACyP,UAAU,CAAC;IACjC,OAAOvU,IAAI,CAACsO,cAAc,EAAE;EAC9B;EAEA;EACAgH,kBAAkBA,CAAC1G,WAAmB;IACpC,QAAQA,WAAW;MACjB,KAAK,CAAC;QAAE,OAAO,eAAe;MAC9B,KAAK,CAAC;QAAE,OAAO,iBAAiB;MAChC,KAAK,CAAC;QAAE,OAAO,cAAc;MAC7B;QAAS,OAAO,SAAS;;EAE7B;EAEA;EACAzR,mBAAmBA,CAACC,mBAA0B,EAAEpD,IAAY;IAC1D,IAAI,CAACoD,mBAAmB,IAAI,CAACmY,KAAK,CAACC,OAAO,CAACpY,mBAAmB,CAAC,EAAE;MAC/D,OAAO,EAAE;;IAEX,OAAOA,mBAAmB,CAACsR,MAAM,CAACK,OAAO,IAAIA,OAAO,CAACH,WAAW,KAAK5U,IAAI,CAAC;EAC5E;EAEA;EACAwE,oBAAoBA,CAACE,aAAqB;IACxC,QAAQA,aAAa;MACnB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,WAAW;;EAE/B;EAEA;EACAY,oBAAoBA,CAACmW,cAAmB,EAAEC,WAAgB;IACxD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAAC/V,OAAO,IAAI,CAAC+V,cAAc,CAAC/V,OAAO,CAACM,IAAI,IAC1E,CAAC0V,WAAW,IAAI,CAACA,WAAW,CAAC7V,SAAS,IAAI,CAAC6V,WAAW,CAAC7V,SAAS,CAACG,IAAI,EAAE;MACzE,OAAO,SAAS;;IAGlB,MAAM4L,WAAW,GAAG,IAAI9G,IAAI,CAAC2Q,cAAc,CAAC/V,OAAO,CAACM,IAAI,CAAC,CAACkN,OAAO,EAAE;IACnE,MAAMjC,aAAa,GAAG,IAAInG,IAAI,CAAC4Q,WAAW,CAAC7V,SAAS,CAACG,IAAI,CAAC,CAACkN,OAAO,EAAE;IACpE,MAAMyI,MAAM,GAAG1K,aAAa,GAAGW,WAAW;IAC1C,MAAMgK,QAAQ,GAAGvI,IAAI,CAACC,KAAK,CAACqI,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIC,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,KAAK;KACxB,MAAM;MACL,MAAMvB,KAAK,GAAGhH,IAAI,CAACC,KAAK,CAACsI,QAAQ,GAAG,EAAE,CAAC;MACvC,MAAMtB,IAAI,GAAGsB,QAAQ,GAAG,EAAE;MAC1B,OAAO,GAAGvB,KAAK,KAAKC,IAAI,KAAK;;EAEjC;EAEA;EACAuB,uBAAuBA,CAACC,KAAY;IAClCA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC9R,uBAAuB,GAAG,CAAC,IAAI,CAACA,uBAAuB;IAE5D;IACA,IAAI,IAAI,CAACA,uBAAuB,EAAE;MAChC+R,UAAU,CAAC,MAAK;QACdrR,QAAQ,CAACsR,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACvR,sBAAsB,CAAC;MACjE,CAAC,EAAE,CAAC,CAAC;;EAET;EAOAwR,sBAAsBA,CAAA;IACpB,MAAMC,YAAY,GAAG,IAAI,CAAClR,UAAU,CAACwL,GAAG,CAAC,gBAAgB,CAAC,EAAEjW,KAAK,IAAI,CAAC;IACtE,IAAI2b,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAAClR,UAAU,CAACwL,GAAG,CAAC,gBAAgB,CAAC,EAAEM,QAAQ,CAACoF,YAAY,GAAG,CAAC,CAAC;;EAErE;EAEAC,sBAAsBA,CAAA;IACpB,MAAMD,YAAY,GAAG,IAAI,CAAClR,UAAU,CAACwL,GAAG,CAAC,gBAAgB,CAAC,EAAEjW,KAAK,IAAI,CAAC;IACtE,IAAI2b,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAAClR,UAAU,CAACwL,GAAG,CAAC,gBAAgB,CAAC,EAAEM,QAAQ,CAACoF,YAAY,GAAG,CAAC,CAAC;;EAErE;EAEAE,kBAAkBA,CAAA;IAChB,MAAMC,gBAAgB,GAAG,IAAI,CAACrR,UAAU,CAACwL,GAAG,CAAC,aAAa,CAAC,EAAEjW,KAAK;IAClE,MAAMuB,WAAW,GAAG,IAAI,CAACuI,aAAa,CAACiS,IAAI,CAACC,EAAE,IAAIA,EAAE,CAAChc,KAAK,KAAK8b,gBAAgB,CAAC;IAChF,OAAOva,WAAW,GAAGA,WAAW,CAACtB,KAAK,GAAG,UAAU;EACrD;;;uBArtCW+I,oBAAoB,EAAAvK,EAAA,CAAAwd,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1d,EAAA,CAAAwd,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5d,EAAA,CAAAwd,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApBvT,oBAAoB;MAAAwT,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjCre,EAAA,CAAAC,cAAA,aAAoC;UAGPD,EAAA,CAAAE,MAAA,+BAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpDH,EAAA,CAAAC,cAAA,WAAyB;UAAAD,EAAA,CAAAE,MAAA,2DAAoD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEnFH,EAAA,CAAAC,cAAA,aAAiC;UAC/BD,EAAA,CAAAY,SAAA,aAAuE;UACzEZ,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,aAA4B;UAKpBD,EAAA,CAAAY,SAAA,aAAgD;UAChDZ,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI7CH,EAAA,CAAAC,cAAA,gBAA2E;UAA5CD,EAAA,CAAAyB,UAAA,sBAAA8c,wDAAA;YAAA,OAAYD,GAAA,CAAAtc,QAAA,EAAU;UAAA,EAAC;UAEpDhC,EAAA,CAAAC,cAAA,eAAyB;UAEvBD,EAAA,CAAAY,SAAA,iBAA6D;UAI7DZ,EAAA,CAAAC,cAAA,eAAgC;UAE5BD,EAAA,CAAAY,SAAA,iBAA6E;UAC7EZ,EAAA,CAAAC,cAAA,iBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE7CH,EAAA,CAAAC,cAAA,eAA8B;UAC5BD,EAAA,CAAAY,SAAA,iBAA+D;UAC/DZ,EAAA,CAAAC,cAAA,iBAAoB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE1CH,EAAA,CAAAC,cAAA,eAA8B;UAC5BD,EAAA,CAAAY,SAAA,iBAAqE;UACrEZ,EAAA,CAAAC,cAAA,iBAAuB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAKrDH,EAAA,CAAAC,cAAA,eAAoC;UAGDD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzCH,EAAA,CAAAC,cAAA,iBAQC;UADCD,EAAA,CAAAyB,UAAA,mBAAA+c,sDAAA;YAAA,OAASF,GAAA,CAAA1C,yBAAA,EAA2B;UAAA,EAAC;UAPvC5b,EAAA,CAAAG,YAAA,EAQC;UACDH,EAAA,CAAAC,cAAA,gCAA8F;UAC5FD,EAAA,CAAAS,UAAA,KAAAge,2CAAA,2BAuBa;UACfze,EAAA,CAAAG,YAAA,EAAmB;UAEnBH,EAAA,CAAAC,cAAA,kBAKC;UACqBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAK5CH,EAAA,CAAAC,cAAA,eAAmC;UACgBD,EAAA,CAAAyB,UAAA,mBAAAid,uDAAA;YAAA,OAASJ,GAAA,CAAAnC,aAAA,EAAe;UAAA,EAAC;UACxEnc,EAAA,CAAAY,SAAA,aAAmC;UACrCZ,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAAyC;UACVD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,iBAQC;UADCD,EAAA,CAAAyB,UAAA,mBAAAkd,sDAAA;YAAA,OAASL,GAAA,CAAApC,uBAAA,EAAyB;UAAA,EAAC;UAPrClc,EAAA,CAAAG,YAAA,EAQC;UACDH,EAAA,CAAAC,cAAA,gCAA4F;UAC1FD,EAAA,CAAAS,UAAA,KAAAme,2CAAA,2BAuBa;UACf5e,EAAA,CAAAG,YAAA,EAAmB;UAEnBH,EAAA,CAAAC,cAAA,kBAKC;UACqBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAK5CH,EAAA,CAAAC,cAAA,eAAqC;UACRD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzCH,EAAA,CAAAY,SAAA,iBAMC;UACHZ,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAqC;UACXD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAY,SAAA,iBAMC;UACHZ,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA2C;UACVD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1DH,EAAA,CAAAC,cAAA,kBAA4H;UAA1CD,EAAA,CAAAyB,UAAA,mBAAAod,uDAAAC,MAAA;YAAA,OAASR,GAAA,CAAA1B,uBAAA,CAAAkC,MAAA,CAA+B;UAAA,EAAC;UACzH9e,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,eAAqF;UACtDD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnDH,EAAA,CAAAC,cAAA,eAA8B;UAC5BD,EAAA,CAAAE,MAAA,8QAEF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAoC;UAMhCD,EAAA,CAAAS,UAAA,KAAAse,uCAAA,qBAA8G;UAChH/e,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAA4B;UACSD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC5DH,EAAA,CAAAC,cAAA,eAAqC;UACwBD,EAAA,CAAAyB,UAAA,mBAAAud,wDAAA;YAAA,OAASV,GAAA,CAAAnB,sBAAA,EAAwB;UAAA,EAAC;UAC3Fnd,EAAA,CAAAY,SAAA,cAA4B;UAC9BZ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,KAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClFH,EAAA,CAAAC,cAAA,mBAA8F;UAAnCD,EAAA,CAAAyB,UAAA,mBAAAwd,wDAAA;YAAA,OAASX,GAAA,CAAArB,sBAAA,EAAwB;UAAA,EAAC;UAC3Fjd,EAAA,CAAAY,SAAA,cAA2B;UAC7BZ,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,gBAA4B;UACSD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChDH,EAAA,CAAAC,cAAA,gBAAwC;UAAAD,EAAA,CAAAE,MAAA,wBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxDH,EAAA,CAAAC,cAAA,gBAAqC;UAEjCD,EAAA,CAAAY,SAAA,cAA4B;UAC9BZ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtCH,EAAA,CAAAC,cAAA,mBAAoE;UAClED,EAAA,CAAAY,SAAA,cAA2B;UAC7BZ,EAAA,CAAAG,YAAA,EAAS;UAOjBH,EAAA,CAAAC,cAAA,gBAAqC;UAMjCD,EAAA,CAAAS,UAAA,MAAAye,sCAAA,mBAA0C;UAC1Clf,EAAA,CAAAS,UAAA,MAAA0e,qCAAA,kBAEM;UACRnf,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,gBAAgC;UAE5BD,EAAA,CAAAY,SAAA,kBAA4E;UAC5EZ,EAAA,CAAAC,cAAA,kBAAqC;UAAAD,EAAA,CAAAE,MAAA,yDAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGhFH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAY,SAAA,kBAA0E;UAC1EZ,EAAA,CAAAC,cAAA,kBAAmC;UAAAD,EAAA,CAAAE,MAAA,yDAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAG9EH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAY,SAAA,kBAAgG;UAChGZ,EAAA,CAAAC,cAAA,kBAA+B;UAAAD,EAAA,CAAAE,MAAA,gCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAMpEH,EAAA,CAAAC,cAAA,gBAAwC;UAGlCD,EAAA,CAAAY,SAAA,cAA0B;UAACZ,EAAA,CAAAE,MAAA,2BAC7B;UAAAF,EAAA,CAAAG,YAAA,EAAU;UACVH,EAAA,CAAAC,cAAA,gBAA8B;UAIHD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrCH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAY,SAAA,cAA+B;UAC/BZ,EAAA,CAAAC,cAAA,mBAIC;UACuBD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,mBAAsB;UAAAD,EAAA,CAAAE,MAAA,sBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM7CH,EAAA,CAAAC,cAAA,gBAAwB;UACAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAY,SAAA,cAAsC;UACtCZ,EAAA,CAAAC,cAAA,mBAIC;UACqBD,EAAA,CAAAE,MAAA,sBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM7CH,EAAA,CAAAC,cAAA,gBAAwB;UACcD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3DH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAY,SAAA,cAA+B;UAC/BZ,EAAA,CAAAC,cAAA,mBAIC;UACqBD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClDH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOpDH,EAAA,CAAAC,cAAA,gBAAuC;UAGjCD,EAAA,CAAAY,SAAA,kBAKC;UACDZ,EAAA,CAAAC,cAAA,mBAAyD;UACvDD,EAAA,CAAAY,SAAA,kBAAkC;UAClCZ,EAAA,CAAAC,cAAA,kBAAkC;UAAAD,EAAA,CAAAE,MAAA,iCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKvEH,EAAA,CAAAC,cAAA,gBAAuC;UAEnCD,EAAA,CAAAY,SAAA,mBAKC;UACDZ,EAAA,CAAAC,cAAA,mBAA2D;UACzDD,EAAA,CAAAY,SAAA,kBAAkC;UAClCZ,EAAA,CAAAC,cAAA,kBAAkC;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK1EH,EAAA,CAAAC,cAAA,gBAAuC;UAEnCD,EAAA,CAAAY,SAAA,mBAKC;UACDZ,EAAA,CAAAC,cAAA,mBAAgE;UAC9DD,EAAA,CAAAY,SAAA,kBAAkC;UAClCZ,EAAA,CAAAC,cAAA,kBAAkC;UAAAD,EAAA,CAAAE,MAAA,0CAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKhFH,EAAA,CAAAC,cAAA,gBAAuC;UAEnCD,EAAA,CAAAY,SAAA,mBAKC;UACDZ,EAAA,CAAAC,cAAA,mBAAsD;UACpDD,EAAA,CAAAY,SAAA,kBAAkC;UAClCZ,EAAA,CAAAC,cAAA,kBAAkC;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAYhFH,EAAA,CAAAS,UAAA,MAAA2e,qCAAA,mBAkWM;UACRpf,EAAA,CAAAG,YAAA,EAAM;;;;;;;UA7tBIH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAa,UAAA,cAAAyd,GAAA,CAAAtS,UAAA,CAAwB;UAiCpBhM,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAAa,UAAA,oBAAAwe,GAAA,CAAiC;UAIgBrf,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAa,UAAA,gBAAAyd,GAAA,CAAAnG,eAAA,CAAAmH,IAAA,CAAAhB,GAAA,EAA0C;UAC1Dte,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAa,UAAA,YAAAyd,GAAA,CAAA1T,kBAAA,CAAqB;UAgC9C5K,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UAmBnBb,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAa,UAAA,oBAAA0e,GAAA,CAA+B;UAIgBvf,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAa,UAAA,gBAAAyd,GAAA,CAAAnG,eAAA,CAAAmH,IAAA,CAAAhB,GAAA,EAA0C;UACxDte,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAa,UAAA,YAAAyd,GAAA,CAAAzT,gBAAA,CAAmB;UAgC5C7K,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UAWnBb,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAa,UAAA,QAAAyd,GAAA,CAAA1S,OAAA,CAAe;UAqBf5L,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAwf,kBAAA,OAAAC,QAAA,GAAAnB,GAAA,CAAAtS,UAAA,CAAAwL,GAAA,qCAAAiI,QAAA,CAAAle,KAAA,SAAAke,QAAA,GAAAnB,GAAA,CAAAtS,UAAA,CAAAwL,GAAA,qCAAAiI,QAAA,CAAAle,KAAA,oCAAA+c,GAAA,CAAAlB,kBAAA,QACF;UAC8Cpd,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAwJ,WAAA,SAAA8U,GAAA,CAAAtT,uBAAA,CAAsC;UAc9ChL,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAa,UAAA,YAAAyd,GAAA,CAAAjT,aAAA,CAAgB;UAYlBrL,EAAA,CAAAI,SAAA,IAA6C;UAA7CJ,EAAA,CAAAK,iBAAA,EAAAqf,QAAA,GAAApB,GAAA,CAAAtS,UAAA,CAAAwL,GAAA,qCAAAkI,QAAA,CAAAne,KAAA,CAA6C;UA4B/EvB,EAAA,CAAAI,SAAA,IAA4C;UAA5CJ,EAAA,CAAAa,UAAA,aAAAyd,GAAA,CAAAtS,UAAA,CAAAsM,OAAA,IAAAgG,GAAA,CAAApU,SAAA,CAA4C;UAErClK,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAa,UAAA,UAAAyd,GAAA,CAAApU,SAAA,CAAgB;UACjBlK,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAa,UAAA,SAAAyd,GAAA,CAAApU,SAAA,CAAe;UA4EPlK,EAAA,CAAAI,SAAA,IAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UA2EEb,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAa,UAAA,SAAAyd,GAAA,CAAAxT,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}