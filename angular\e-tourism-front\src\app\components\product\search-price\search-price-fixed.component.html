<!-- <PERSON><PERSON><PERSON> du séjour (pour les vols aller-retour) -->
<div class="stay-duration" *ngIf="getInboundSegments(flight).length > 0 && getOutboundSegments(flight).length > 0">
  <i class="fas fa-hotel"></i>
  <span>Stay duration: {{ calculateLayoverTime(
        {arrival: {date: getOutboundSegments(flight)[getOutboundSegments(flight).length-1]?.arrival?.date}},
        {departure: {date: getInboundSegments(flight)[0]?.departure?.date}}
      ) }}</span>
</div>
