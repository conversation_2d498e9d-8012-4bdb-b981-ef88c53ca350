{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/button\";\nexport class SidebarComponent {\n  constructor() {\n    this.isOpen = false;\n    this.sidebarClose = new EventEmitter();\n  }\n  // Détermine si l'écran est en mode mobile\n  get isMobile() {\n    return window.innerWidth < 768;\n  }\n  // Ferme la sidebar et émet l'événement\n  closeSidebar() {\n    this.sidebarClose.emit();\n  }\n  // Ferme la sidebar uniquement en mode mobile\n  closeSidebarOnMobile() {\n    if (this.isMobile) {\n      this.closeSidebar();\n    }\n  }\n  // Ferme la sidebar si l'utilisateur appuie sur la touche Escape\n  onEscapePress() {\n    if (this.isOpen) {\n      this.closeSidebar();\n    }\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      hostBindings: function SidebarComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.escape\", function SidebarComponent_keydown_escape_HostBindingHandler() {\n            return ctx.onEscapePress();\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        isOpen: \"isOpen\"\n      },\n      outputs: {\n        sidebarClose: \"sidebarClose\"\n      },\n      decls: 48,\n      vars: 2,\n      consts: [[1, \"sidebar\"], [1, \"sidebar-header\"], [1, \"sidebar-brand\"], [1, \"brand-icon\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"brand-name\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"sidebar-nav\"], [1, \"nav-section\"], [1, \"nav-section-title\"], [\"routerLink\", \"/search-price\", \"routerLinkActive\", \"active-link\", 1, \"nav-item\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"nav-item\", \"disabled\"], [1, \"fas\", \"fa-history\"], [1, \"fas\", \"fa-bookmark\"], [1, \"fas\", \"fa-question-circle\"], [1, \"fas\", \"fa-headset\"], [1, \"sidebar-footer\"], [1, \"app-info\"], [1, \"app-logo\"], [1, \"fas\", \"fa-globe-americas\"], [1, \"app-details\"], [1, \"app-name\"], [1, \"app-version\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"i\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"h3\", 5);\n          i0.ɵɵtext(6, \"E-Tourism\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_button_click_7_listener() {\n            return ctx.closeSidebar();\n          });\n          i0.ɵɵelement(8, \"i\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9)(11, \"h4\", 10);\n          i0.ɵɵtext(12, \"Menu principal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"a\", 11);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_a_click_13_listener() {\n            return ctx.closeSidebarOnMobile();\n          });\n          i0.ɵɵelement(14, \"i\", 12);\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Rechercher un vol\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 9)(18, \"h4\", 10);\n          i0.ɵɵtext(19, \"R\\u00E9servations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"a\", 13);\n          i0.ɵɵelement(21, \"i\", 14);\n          i0.ɵɵelementStart(22, \"span\");\n          i0.ɵɵtext(23, \"Historique\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"a\", 13);\n          i0.ɵɵelement(25, \"i\", 15);\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"Favoris\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 9)(29, \"h4\", 10);\n          i0.ɵɵtext(30, \"Aide & Support\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"a\", 13);\n          i0.ɵɵelement(32, \"i\", 16);\n          i0.ɵɵelementStart(33, \"span\");\n          i0.ɵɵtext(34, \"FAQ\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"a\", 13);\n          i0.ɵɵelement(36, \"i\", 17);\n          i0.ɵɵelementStart(37, \"span\");\n          i0.ɵɵtext(38, \"Nous contacter\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(39, \"div\", 18)(40, \"div\", 19)(41, \"div\", 20);\n          i0.ɵɵelement(42, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 22)(44, \"p\", 23);\n          i0.ɵɵtext(45, \"E-Tourism\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"p\", 24);\n          i0.ɵɵtext(47, \"Version 1.0.0\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"sidebar-open\", ctx.isOpen);\n        }\n      },\n      dependencies: [i1.RouterLink, i1.RouterLinkActive, i2.MatIconButton],\n      styles: [\".sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 280px;\\n  height: 100vh;\\n  background-color: white;\\n  box-shadow: var(--elevation-4);\\n  z-index: 1001;\\n  display: flex;\\n  flex-direction: column;\\n  transform: translateX(-100%);\\n  transition: transform var(--transition-medium);\\n  padding-top: 64px; \\n\\n  overflow: hidden;\\n}\\n\\n.sidebar-open[_ngcontent-%COMP%] {\\n  transform: translateX(0);\\n}\\n\\n\\n\\n.sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spacing-md) var(--spacing-lg);\\n  border-bottom: 1px solid var(--divider-color);\\n}\\n\\n.sidebar-brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n}\\n\\n.brand-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  border-radius: 10px;\\n  color: white;\\n  font-size: 1.1rem;\\n  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.3);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);\\n  animation: shimmer 2s infinite;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.brand-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n}\\n\\n.close-button[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  transition: all var(--transition-fast);\\n}\\n\\n.close-button[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n  transform: rotate(90deg);\\n}\\n\\n.close-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n\\n\\n.sidebar-nav[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: var(--spacing-md) var(--spacing-sm);\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-lg);\\n}\\n\\n.nav-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.nav-section-title[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  color: var(--text-secondary);\\n  margin: 0 0 var(--spacing-sm) var(--spacing-lg);\\n  letter-spacing: 0.5px;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  padding: var(--spacing-sm) var(--spacing-lg);\\n  color: var(--text-primary);\\n  text-decoration: none;\\n  border-radius: var(--border-radius-medium);\\n  transition: all var(--transition-fast);\\n  margin: 2px 0;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));\\n  opacity: 0;\\n  transition: opacity var(--transition-fast);\\n}\\n\\n.nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  color: var(--primary-color);\\n}\\n\\n.nav-item[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n}\\n\\n.nav-item.active-link[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n  font-weight: 500;\\n}\\n\\n.nav-item.active-link[_ngcontent-%COMP%]::before {\\n  opacity: 1;\\n}\\n\\n.nav-item.active-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n}\\n\\n.nav-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: var(--text-secondary);\\n  width: 24px;\\n  text-align: center;\\n  transition: color var(--transition-fast);\\n}\\n\\n.nav-item.disabled[_ngcontent-%COMP%] {\\n  color: var(--text-disabled);\\n  pointer-events: none;\\n}\\n\\n.nav-item.disabled[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--text-disabled);\\n}\\n\\n\\n\\n.sidebar-footer[_ngcontent-%COMP%] {\\n  padding: var(--spacing-md) var(--spacing-lg);\\n  border-top: 1px solid var(--divider-color);\\n}\\n\\n.app-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n}\\n\\n.app-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  background: linear-gradient(135deg, var(--primary-light), var(--accent-color));\\n  border-radius: 50%;\\n  color: white;\\n  font-size: 1.1rem;\\n  box-shadow: 0 2px 4px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.app-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.app-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n}\\n\\n.app-version[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.75rem;\\n  color: var(--text-hint);\\n}\\n\\n\\n\\n.scrolled[_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%], .scrolled   [_nghost-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n  padding-top: 56px; \\n\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    width: 300px;\\n  }\\n}\\n\\n.sidebar-backdrop[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  z-index: 999;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity var(--transition-medium), visibility var(--transition-medium);\\n}\\n\\n.sidebar-backdrop-visible[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n\\n\\n@media (min-width: 1200px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    transform: translateX(0);\\n  }\\n\\n  .sidebar-backdrop[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "SidebarComponent", "constructor", "isOpen", "sidebarClose", "isMobile", "window", "innerWidth", "closeSidebar", "emit", "closeSidebarOnMobile", "onEscapePress", "selectors", "hostBindings", "SidebarComponent_HostBindings", "rf", "ctx", "i0", "ɵɵresolveDocument", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "SidebarComponent_Template_button_click_7_listener", "SidebarComponent_Template_a_click_13_listener", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\sidebar\\sidebar.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, HostListener } from '@angular/core';\n\n@Component({\n  selector: 'app-sidebar',\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.css']\n})\nexport class SidebarComponent {\n  @Input() isOpen = false;\n  @Output() sidebarClose = new EventEmitter<void>();\n\n  // Détermine si l'écran est en mode mobile\n  private get isMobile(): boolean {\n    return window.innerWidth < 768;\n  }\n\n  // Ferme la sidebar et émet l'événement\n  closeSidebar(): void {\n    this.sidebarClose.emit();\n  }\n\n  // Ferme la sidebar uniquement en mode mobile\n  closeSidebarOnMobile(): void {\n    if (this.isMobile) {\n      this.closeSidebar();\n    }\n  }\n\n  // Ferme la sidebar si l'utilisateur appuie sur la touche Escape\n  @HostListener('document:keydown.escape')\n  onEscapePress(): void {\n    if (this.isOpen) {\n      this.closeSidebar();\n    }\n  }\n}\n", "<div class=\"sidebar\" [class.sidebar-open]=\"isOpen\">\n  <div class=\"sidebar-header\">\n    <div class=\"sidebar-brand\">\n      <div class=\"brand-icon\">\n        <i class=\"fas fa-plane-departure\"></i>\n      </div>\n      <h3 class=\"brand-name\">E-Tourism</h3>\n    </div>\n    <button mat-icon-button class=\"close-button\" (click)=\"closeSidebar()\">\n      <i class=\"fas fa-times\"></i>\n    </button>\n  </div>\n\n  <div class=\"sidebar-nav\">\n    <div class=\"nav-section\">\n      <h4 class=\"nav-section-title\">Menu principal</h4>\n\n      <a class=\"nav-item\" routerLink=\"/search-price\" routerLinkActive=\"active-link\" (click)=\"closeSidebarOnMobile()\">\n        <i class=\"fas fa-search\"></i>\n        <span>Rechercher un vol</span>\n      </a>\n    </div>\n\n    <div class=\"nav-section\">\n      <h4 class=\"nav-section-title\">Réservations</h4>\n\n      <a class=\"nav-item disabled\">\n        <i class=\"fas fa-history\"></i>\n        <span>Historique</span>\n      </a>\n\n      <a class=\"nav-item disabled\">\n        <i class=\"fas fa-bookmark\"></i>\n        <span>Favoris</span>\n      </a>\n    </div>\n\n    <div class=\"nav-section\">\n      <h4 class=\"nav-section-title\">Aide & Support</h4>\n\n      <a class=\"nav-item disabled\">\n        <i class=\"fas fa-question-circle\"></i>\n        <span>FAQ</span>\n      </a>\n\n      <a class=\"nav-item disabled\">\n        <i class=\"fas fa-headset\"></i>\n        <span>Nous contacter</span>\n      </a>\n    </div>\n  </div>\n\n  <div class=\"sidebar-footer\">\n    <div class=\"app-info\">\n      <div class=\"app-logo\">\n        <i class=\"fas fa-globe-americas\"></i>\n      </div>\n      <div class=\"app-details\">\n        <p class=\"app-name\">E-Tourism</p>\n        <p class=\"app-version\">Version 1.0.0</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAsB,eAAe;;;;AAOpF,OAAM,MAAOC,gBAAgB;EAL7BC,YAAA;IAMW,KAAAC,MAAM,GAAG,KAAK;IACb,KAAAC,YAAY,GAAG,IAAIJ,YAAY,EAAQ;;EAEjD;EACA,IAAYK,QAAQA,CAAA;IAClB,OAAOC,MAAM,CAACC,UAAU,GAAG,GAAG;EAChC;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,CAACJ,YAAY,CAACK,IAAI,EAAE;EAC1B;EAEA;EACAC,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACL,QAAQ,EAAE;MACjB,IAAI,CAACG,YAAY,EAAE;;EAEvB;EAEA;EAEAG,aAAaA,CAAA;IACX,IAAI,IAAI,CAACR,MAAM,EAAE;MACf,IAAI,CAACK,YAAY,EAAE;;EAEvB;;;uBA3BWP,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAW,SAAA;MAAAC,YAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAAhBC,GAAA,CAAAL,aAAA,EAAe;UAAA,UAAAM,EAAA,CAAAC,iBAAA;;;;;;;;;;;;;;UCP5BD,EAAA,CAAAE,cAAA,aAAmD;UAI3CF,EAAA,CAAAG,SAAA,WAAsC;UACxCH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,cAAA,YAAuB;UAAAF,EAAA,CAAAK,MAAA,gBAAS;UAAAL,EAAA,CAAAI,YAAA,EAAK;UAEvCJ,EAAA,CAAAE,cAAA,gBAAsE;UAAzBF,EAAA,CAAAM,UAAA,mBAAAC,kDAAA;YAAA,OAASR,GAAA,CAAAR,YAAA,EAAc;UAAA,EAAC;UACnES,EAAA,CAAAG,SAAA,WAA4B;UAC9BH,EAAA,CAAAI,YAAA,EAAS;UAGXJ,EAAA,CAAAE,cAAA,aAAyB;UAESF,EAAA,CAAAK,MAAA,sBAAc;UAAAL,EAAA,CAAAI,YAAA,EAAK;UAEjDJ,EAAA,CAAAE,cAAA,aAA+G;UAAjCF,EAAA,CAAAM,UAAA,mBAAAE,8CAAA;YAAA,OAAST,GAAA,CAAAN,oBAAA,EAAsB;UAAA,EAAC;UAC5GO,EAAA,CAAAG,SAAA,aAA6B;UAC7BH,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAK,MAAA,yBAAiB;UAAAL,EAAA,CAAAI,YAAA,EAAO;UAIlCJ,EAAA,CAAAE,cAAA,cAAyB;UACOF,EAAA,CAAAK,MAAA,yBAAY;UAAAL,EAAA,CAAAI,YAAA,EAAK;UAE/CJ,EAAA,CAAAE,cAAA,aAA6B;UAC3BF,EAAA,CAAAG,SAAA,aAA8B;UAC9BH,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAK,MAAA,kBAAU;UAAAL,EAAA,CAAAI,YAAA,EAAO;UAGzBJ,EAAA,CAAAE,cAAA,aAA6B;UAC3BF,EAAA,CAAAG,SAAA,aAA+B;UAC/BH,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAK,MAAA,eAAO;UAAAL,EAAA,CAAAI,YAAA,EAAO;UAIxBJ,EAAA,CAAAE,cAAA,cAAyB;UACOF,EAAA,CAAAK,MAAA,sBAAc;UAAAL,EAAA,CAAAI,YAAA,EAAK;UAEjDJ,EAAA,CAAAE,cAAA,aAA6B;UAC3BF,EAAA,CAAAG,SAAA,aAAsC;UACtCH,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAK,MAAA,WAAG;UAAAL,EAAA,CAAAI,YAAA,EAAO;UAGlBJ,EAAA,CAAAE,cAAA,aAA6B;UAC3BF,EAAA,CAAAG,SAAA,aAA8B;UAC9BH,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAK,MAAA,sBAAc;UAAAL,EAAA,CAAAI,YAAA,EAAO;UAKjCJ,EAAA,CAAAE,cAAA,eAA4B;UAGtBF,EAAA,CAAAG,SAAA,aAAqC;UACvCH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,cAAA,eAAyB;UACHF,EAAA,CAAAK,MAAA,iBAAS;UAAAL,EAAA,CAAAI,YAAA,EAAI;UACjCJ,EAAA,CAAAE,cAAA,aAAuB;UAAAF,EAAA,CAAAK,MAAA,qBAAa;UAAAL,EAAA,CAAAI,YAAA,EAAI;;;UA3D3BJ,EAAA,CAAAS,WAAA,iBAAAV,GAAA,CAAAb,MAAA,CAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}