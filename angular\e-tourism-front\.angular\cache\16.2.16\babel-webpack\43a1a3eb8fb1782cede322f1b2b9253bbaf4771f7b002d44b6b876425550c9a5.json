{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../navbar/navbar.component\";\nexport class MainLayoutComponent {\n  constructor() {\n    this.isSidebarOpen = false;\n  }\n  ngOnInit() {\n    // Vérifier la taille de l'écran au chargement\n    this.checkScreenSize();\n  }\n  // Méthode pour basculer l'état de la sidebar\n  toggleSidebar() {\n    this.isSidebarOpen = !this.isSidebarOpen;\n    // Ajouter/supprimer la classe pour empêcher le défilement du body quand la sidebar est ouverte sur mobile\n    if (this.isSidebarOpen && window.innerWidth < 768) {\n      document.body.classList.add('sidebar-open-mobile');\n    } else {\n      document.body.classList.remove('sidebar-open-mobile');\n    }\n  }\n  // Méthode pour fermer la sidebar\n  closeSidebar() {\n    if (this.isSidebarOpen) {\n      this.isSidebarOpen = false;\n      document.body.classList.remove('sidebar-open-mobile');\n    }\n  }\n  // Écouter les changements de taille d'écran\n  checkScreenSize() {\n    // Fermer automatiquement la sidebar sur les petits écrans lors du redimensionnement\n    if (window.innerWidth >= 768 && this.isSidebarOpen) {\n      document.body.classList.remove('sidebar-open-mobile');\n    }\n  }\n  static {\n    this.ɵfac = function MainLayoutComponent_Factory(t) {\n      return new (t || MainLayoutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MainLayoutComponent,\n      selectors: [[\"app-main-layout\"]],\n      hostBindings: function MainLayoutComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function MainLayoutComponent_resize_HostBindingHandler() {\n            return ctx.checkScreenSize();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      decls: 5,\n      vars: 0,\n      consts: [[1, \"main-layout\"], [1, \"main-content\"], [1, \"content-container\"]],\n      template: function MainLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-navbar\");\n          i0.ɵɵelementStart(2, \"main\", 1)(3, \"div\", 2);\n          i0.ɵɵelement(4, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i2.NavbarComponent],\n      styles: [\".main-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 100vh;\\n  width: 100%;\\n  position: relative;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-top: 64px; \\n\\n}\\n\\n.content-container[_ngcontent-%COMP%] {\\n  padding: var(--spacing-lg);\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  width: 100%;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .content-container[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbGF5b3V0L21haW4tbGF5b3V0L21haW4tbGF5b3V0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLGlCQUFpQjtFQUNqQixXQUFXO0VBQ1gsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsT0FBTztFQUNQLGlCQUFpQixFQUFFLHlCQUF5QjtBQUM5Qzs7QUFFQTtFQUNFLDBCQUEwQjtFQUMxQixpQkFBaUI7RUFDakIsY0FBYztFQUNkLFdBQVc7QUFDYjs7QUFFQSxzQkFBc0I7QUFDdEI7RUFDRTtJQUNFLDBCQUEwQjtFQUM1QjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLm1haW4tbGF5b3V0IHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgbWluLWhlaWdodDogMTAwdmg7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG59XHJcblxyXG4ubWFpbi1jb250ZW50IHtcclxuICBmbGV4OiAxO1xyXG4gIHBhZGRpbmctdG9wOiA2NHB4OyAvKiBIYXV0ZXVyIGRlIGxhIG5hdmJhciAqL1xyXG59XHJcblxyXG4uY29udGVudC1jb250YWluZXIge1xyXG4gIHBhZGRpbmc6IHZhcigtLXNwYWNpbmctbGcpO1xyXG4gIG1heC13aWR0aDogMTIwMHB4O1xyXG4gIG1hcmdpbjogMCBhdXRvO1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcblxyXG4vKiBSZXNwb25zaXZlIHN0eWxlcyAqL1xyXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAuY29udGVudC1jb250YWluZXIge1xyXG4gICAgcGFkZGluZzogdmFyKC0tc3BhY2luZy1tZCk7XHJcbiAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MainLayoutComponent", "constructor", "isSidebarOpen", "ngOnInit", "checkScreenSize", "toggleSidebar", "window", "innerWidth", "document", "body", "classList", "add", "remove", "closeSidebar", "selectors", "hostBindings", "MainLayoutComponent_HostBindings", "rf", "ctx", "i0", "ɵɵresolveWindow", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\main-layout\\main-layout.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\main-layout\\main-layout.component.html"], "sourcesContent": ["import { Component, OnInit, HostListener } from '@angular/core';\n\n@Component({\n  selector: 'app-main-layout',\n  templateUrl: './main-layout.component.html',\n  styleUrls: ['./main-layout.component.css']\n})\nexport class MainLayoutComponent implements OnInit {\n  isSidebarOpen = false;\n\n  constructor() { }\n\n  ngOnInit(): void {\n    // Vérifier la taille de l'écran au chargement\n    this.checkScreenSize();\n  }\n\n  // Méthode pour basculer l'état de la sidebar\n  toggleSidebar(): void {\n    this.isSidebarOpen = !this.isSidebarOpen;\n\n    // Ajouter/supprimer la classe pour empêcher le défilement du body quand la sidebar est ouverte sur mobile\n    if (this.isSidebarOpen && window.innerWidth < 768) {\n      document.body.classList.add('sidebar-open-mobile');\n    } else {\n      document.body.classList.remove('sidebar-open-mobile');\n    }\n  }\n\n  // Méthode pour fermer la sidebar\n  closeSidebar(): void {\n    if (this.isSidebarOpen) {\n      this.isSidebarOpen = false;\n      document.body.classList.remove('sidebar-open-mobile');\n    }\n  }\n\n  // Écouter les changements de taille d'écran\n  @HostListener('window:resize')\n  checkScreenSize(): void {\n    // Fermer automatiquement la sidebar sur les petits écrans lors du redimensionnement\n    if (window.innerWidth >= 768 && this.isSidebarOpen) {\n      document.body.classList.remove('sidebar-open-mobile');\n    }\n  }\n}\n", "<div class=\"main-layout\">\n  <app-navbar></app-navbar>\n\n  <main class=\"main-content\">\n    <div class=\"content-container\">\n      <router-outlet></router-outlet>\n    </div>\n  </main>\n</div>\n"], "mappings": ";;;AAOA,OAAM,MAAOA,mBAAmB;EAG9BC,YAAA;IAFA,KAAAC,aAAa,GAAG,KAAK;EAEL;EAEhBC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACH,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IAExC;IACA,IAAI,IAAI,CAACA,aAAa,IAAII,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;MACjDC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;KACnD,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,qBAAqB,CAAC;;EAEzD;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACX,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,GAAG,KAAK;MAC1BM,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,qBAAqB,CAAC;;EAEzD;EAEA;EAEAR,eAAeA,CAAA;IACb;IACA,IAAIE,MAAM,CAACC,UAAU,IAAI,GAAG,IAAI,IAAI,CAACL,aAAa,EAAE;MAClDM,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,qBAAqB,CAAC;;EAEzD;;;uBArCWZ,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAc,SAAA;MAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAAnBC,GAAA,CAAAd,eAAA,EAAiB;UAAA,UAAAe,EAAA,CAAAC,eAAA;;;;;;;;UCP9BD,EAAA,CAAAE,cAAA,aAAyB;UACvBF,EAAA,CAAAG,SAAA,iBAAyB;UAEzBH,EAAA,CAAAE,cAAA,cAA2B;UAEvBF,EAAA,CAAAG,SAAA,oBAA+B;UACjCH,EAAA,CAAAI,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}