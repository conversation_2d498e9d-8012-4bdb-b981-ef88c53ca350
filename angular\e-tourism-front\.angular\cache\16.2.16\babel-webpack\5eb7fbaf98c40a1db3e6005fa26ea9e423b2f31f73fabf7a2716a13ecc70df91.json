{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport class BookingService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.apiUrl = 'http://localhost:8080/booking';\n  }\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request) {\n    // Utiliser l'endpoint unique /booking-transaction pour toutes les actions\n    const endpoint = '/booking-transaction';\n    // Utiliser directement l'objet de requête complet avec le champ action\n    const requestBody = request;\n    // Récupérer le token d'authentification\n    const token = this.authService.getToken();\n    if (!token) {\n      throw new Error('Vous devez être connecté pour effectuer cette action.');\n    }\n    // Créer les en-têtes avec le token d'authentification\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n    return this.http.post(`${this.apiUrl}${endpoint}`, requestBody, {\n      headers\n    }).pipe(map(response => {\n      // Construire la réponse au format BookingTransactionResponse\n      const bookingResponse = {\n        action: request.action\n      };\n      switch (request.action) {\n        case 'begin':\n          bookingResponse.beginResponse = response;\n          break;\n        case 'info':\n          bookingResponse.infoResponse = response;\n          break;\n        case 'commit':\n          bookingResponse.commitResponse = response;\n          break;\n      }\n      return bookingResponse;\n    }), catchError(error => {\n      console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);\n      throw error;\n    }));\n  }\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds, currency = 'EUR', culture = 'fr-FR') {\n    const request = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n    return this.bookingTransaction(request);\n  }\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request) {\n    const bookingRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request) {\n    const bookingRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId) {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId) {\n    return {\n      transactionId,\n      paymentOption: 1,\n      paymentInformation: {\n        paymentTypeId: 1,\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n  static {\n    this.ɵfac = function BookingService_Factory(t) {\n      return new (t || BookingService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BookingService,\n      factory: BookingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "catchError", "map", "BookingService", "constructor", "http", "authService", "apiUrl", "bookingTransaction", "request", "endpoint", "requestBody", "token", "getToken", "Error", "headers", "post", "pipe", "response", "bookingResponse", "action", "beginResponse", "infoResponse", "commitResponse", "error", "console", "beginTransaction", "offerIds", "currency", "culture", "beginRequest", "setReservationInfo", "bookingRequest", "infoRequest", "commitTransaction", "commitRequest", "createDefaultReservationInfoRequest", "transactionId", "travellers", "reservationNote", "agencyReservationNumber", "createDefaultCommitTransactionRequest", "paymentOption", "paymentInformation", "paymentTypeId", "paymentPrice", "amount", "paymentDate", "Date", "toISOString", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\booking.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { AuthService } from './auth.service';\nimport {\n  BookingTransactionRequest,\n  BookingTransactionResponse,\n  BeginTransactionRequest,\n  BeginTransactionResponse,\n  SetReservationInfoRequest,\n  SetReservationInfoResponse,\n  CommitTransactionRequest,\n  CommitTransactionResponse\n} from '../models/booking';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class BookingService {\n  private apiUrl = 'http://localhost:8080/booking';\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) { }\n\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request: BookingTransactionRequest): Observable<BookingTransactionResponse> {\n    // Utiliser l'endpoint unique /booking-transaction pour toutes les actions\n    const endpoint = '/booking-transaction';\n\n    // Utiliser directement l'objet de requête complet avec le champ action\n    const requestBody = request;\n\n    // Récupérer le token d'authentification\n    const token = this.authService.getToken();\n    if (!token) {\n      throw new Error('Vous devez être connecté pour effectuer cette action.');\n    }\n\n    // Créer les en-têtes avec le token d'authentification\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n\n    return this.http.post<any>(\n      `${this.apiUrl}${endpoint}`,\n      requestBody,\n      { headers }\n    ).pipe(\n      map(response => {\n        // Construire la réponse au format BookingTransactionResponse\n        const bookingResponse: BookingTransactionResponse = {\n          action: request.action\n        };\n\n        switch (request.action) {\n          case 'begin':\n            bookingResponse.beginResponse = response;\n            break;\n          case 'info':\n            bookingResponse.infoResponse = response;\n            break;\n          case 'commit':\n            bookingResponse.commitResponse = response;\n            break;\n        }\n\n        return bookingResponse;\n      }),\n      catchError(error => {\n        console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);\n        throw error;\n      })\n    );\n  }\n\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds: string[], currency: string = 'EUR', culture: string = 'fr-FR'): Observable<BookingTransactionResponse> {\n    const request: BookingTransactionRequest = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n\n    return this.bookingTransaction(request);\n  }\n\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request: SetReservationInfoRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request: CommitTransactionRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId: string): SetReservationInfoRequest {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId: string): CommitTransactionRequest {\n    return {\n      transactionId,\n      paymentOption: 1, // Option de paiement par défaut\n      paymentInformation: {\n        paymentTypeId: 1, // Type de paiement par défaut\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;;AAgBhD,OAAM,MAAOC,cAAc;EAGzBC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAJb,KAAAC,MAAM,GAAG,+BAA+B;EAK5C;EAEJ;;;;;EAKAC,kBAAkBA,CAACC,OAAkC;IACnD;IACA,MAAMC,QAAQ,GAAG,sBAAsB;IAEvC;IACA,MAAMC,WAAW,GAAGF,OAAO;IAE3B;IACA,MAAMG,KAAK,GAAG,IAAI,CAACN,WAAW,CAACO,QAAQ,EAAE;IACzC,IAAI,CAACD,KAAK,EAAE;MACV,MAAM,IAAIE,KAAK,CAAC,uDAAuD,CAAC;;IAG1E;IACA,MAAMC,OAAO,GAAG,IAAIf,WAAW,CAAC;MAC9B,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAE,UAAUY,KAAK;KACjC,CAAC;IAEF,OAAO,IAAI,CAACP,IAAI,CAACW,IAAI,CACnB,GAAG,IAAI,CAACT,MAAM,GAAGG,QAAQ,EAAE,EAC3BC,WAAW,EACX;MAAEI;IAAO,CAAE,CACZ,CAACE,IAAI,CACJf,GAAG,CAACgB,QAAQ,IAAG;MACb;MACA,MAAMC,eAAe,GAA+B;QAClDC,MAAM,EAAEX,OAAO,CAACW;OACjB;MAED,QAAQX,OAAO,CAACW,MAAM;QACpB,KAAK,OAAO;UACVD,eAAe,CAACE,aAAa,GAAGH,QAAQ;UACxC;QACF,KAAK,MAAM;UACTC,eAAe,CAACG,YAAY,GAAGJ,QAAQ;UACvC;QACF,KAAK,QAAQ;UACXC,eAAe,CAACI,cAAc,GAAGL,QAAQ;UACzC;;MAGJ,OAAOC,eAAe;IACxB,CAAC,CAAC,EACFlB,UAAU,CAACuB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,iDAAiDf,OAAO,CAACW,MAAM,IAAI,EAAEI,KAAK,CAAC;MACzF,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;EAOAE,gBAAgBA,CAACC,QAAkB,EAAEC,QAAA,GAAmB,KAAK,EAAEC,OAAA,GAAkB,OAAO;IACtF,MAAMpB,OAAO,GAA8B;MACzCW,MAAM,EAAE,OAAO;MACfU,YAAY,EAAE;QACZH,QAAQ;QACRC,QAAQ;QACRC;;KAEH;IAED,OAAO,IAAI,CAACrB,kBAAkB,CAACC,OAAO,CAAC;EACzC;EAEA;;;;;EAKAsB,kBAAkBA,CAACtB,OAAkC;IACnD,MAAMuB,cAAc,GAA8B;MAChDZ,MAAM,EAAE,MAAM;MACda,WAAW,EAAExB;KACd;IAED,OAAO,IAAI,CAACD,kBAAkB,CAACwB,cAAc,CAAC;EAChD;EAEA;;;;;EAKAE,iBAAiBA,CAACzB,OAAiC;IACjD,MAAMuB,cAAc,GAA8B;MAChDZ,MAAM,EAAE,QAAQ;MAChBe,aAAa,EAAE1B;KAChB;IAED,OAAO,IAAI,CAACD,kBAAkB,CAACwB,cAAc,CAAC;EAChD;EAEA;;;;;EAKAI,mCAAmCA,CAACC,aAAqB;IACvD,OAAO;MACLA,aAAa;MACbC,UAAU,EAAE,EAAE;MACdC,eAAe,EAAE,EAAE;MACnBC,uBAAuB,EAAE;KAC1B;EACH;EAEA;;;;;EAKAC,qCAAqCA,CAACJ,aAAqB;IACzD,OAAO;MACLA,aAAa;MACbK,aAAa,EAAE,CAAC;MAChBC,kBAAkB,EAAE;QAClBC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;UACZC,MAAM,EAAE,CAAC;UACTlB,QAAQ,EAAE;SACX;QACDmB,WAAW,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;;KAEtC;EACH;;;uBAhJW9C,cAAc,EAAA+C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAdpD,cAAc;MAAAqD,OAAA,EAAdrD,cAAc,CAAAsD,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}