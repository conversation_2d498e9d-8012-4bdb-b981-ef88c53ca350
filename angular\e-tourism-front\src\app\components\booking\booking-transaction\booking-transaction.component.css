.booking-transaction-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #1a73e8, #0d47a1);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
  padding: 40px;
  color: white;
}

.page-title {
  font-size: 32px;
  margin: 0 0 10px;
  font-weight: 600;
}

.page-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.header-illustration {
  flex: 1;
  height: 200px;
  overflow: hidden;
}

.header-illustration img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.step-content {
  padding: 20px 0;
}

.step-header {
  margin-bottom: 20px;
}

.step-header h2 {
  font-size: 24px;
  margin: 0 0 10px;
  color: #1a73e8;
}

.step-header p {
  font-size: 16px;
  margin: 0;
  color: #5f6368;
}

.form-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3, .form-section h3 {
  font-size: 18px;
  margin: 0;
  color: #202124;
}

.form-section h4 {
  font-size: 16px;
  margin: 0 0 15px;
  color: #5f6368;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row > * {
  flex: 1;
  min-width: 200px;
}

.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 20px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #fdeded;
  color: #d32f2f;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.error-message mat-icon {
  color: #d32f2f;
}

.button-spinner {
  margin-right: 8px;
}

.traveller-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.response-summary {
  background-color: #e8f0fe;
  border-radius: 8px;
  padding: 20px;
  margin-top: 30px;
}

.response-summary h3 {
  font-size: 18px;
  margin: 0 0 15px;
  color: #1a73e8;
}

.transaction-details, .reservation-details, .confirmation-details {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.transaction-details p, .reservation-details p, .confirmation-details p {
  margin: 0;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.success-response {
  background-color: #e6f4ea;
}

.confirmation-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin: 20px 0;
  grid-column: 1 / -1;
}

.confirmation-message mat-icon {
  color: #34a853;
  font-size: 24px;
}

.confirmation-message p {
  margin: 0;
  border: none;
}

.confirmation-actions {
  grid-column: 1 / -1;
  margin-top: 20px;
  text-align: center;
}

.offer-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.offer-summary h3 {
  font-size: 18px;
  margin: 0 0 15px;
  color: #202124;
}

.offer-ids {
  margin-bottom: 15px;
}

.offer-ids p {
  margin: 0 0 10px;
  font-weight: 500;
}

.offer-card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 15px;
}

.offer-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  width: 100%;
  transition: transform 0.2s, box-shadow 0.2s;
  border-left: 4px solid #1a73e8;
}

.offer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.offer-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #1a73e8;
}

.offer-card-header i {
  margin-right: 12px;
  font-size: 20px;
}

.offer-title {
  font-weight: 500;
  font-size: 16px;
}

.offer-card-content {
  color: #5f6368;
}

.offer-info {
  margin: 0;
  font-size: 14px;
}

/* Styles pour les sélecteurs de pays */
.country-selector-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f7ff;
  border-radius: 8px;
  border-left: 4px solid #1a73e8;
}

.country-selector-container h4,
.country-selector-container h5 {
  margin: 0 0 5px;
  color: #1a73e8;
  font-weight: 500;
}

.country-selector-container h5 {
  font-size: 14px;
}

.country-selector-container .form-row {
  margin-bottom: 0;
}

/* Animation pour les sélecteurs de pays */
app-country-selector {
  transition: transform 0.2s, box-shadow 0.2s;
}

app-country-selector:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive styles */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
  }

  .header-content {
    padding: 30px;
  }

  .header-illustration {
    width: 100%;
  }

  .form-row > * {
    min-width: 100%;
  }

  .country-selector-container {
    min-width: 100%;
  }
}
