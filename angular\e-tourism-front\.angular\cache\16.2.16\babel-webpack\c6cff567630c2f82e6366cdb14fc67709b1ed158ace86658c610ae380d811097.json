{"ast": null, "code": "/**\n * Énumérations basées sur la documentation de l'API Paximum\n */\n// Types de passagers\nexport var PassengerType;\n(function (PassengerType) {\n  PassengerType[PassengerType[\"Adult\"] = 1] = \"Adult\";\n  PassengerType[PassengerType[\"Child\"] = 2] = \"Child\";\n  PassengerType[PassengerType[\"Infant\"] = 3] = \"Infant\";\n  PassengerType[PassengerType[\"Senior\"] = 4] = \"Senior\";\n  PassengerType[PassengerType[\"Student\"] = 5] = \"Student\";\n  PassengerType[PassengerType[\"Young\"] = 6] = \"Young\";\n  PassengerType[PassengerType[\"Military\"] = 7] = \"Military\";\n  PassengerType[PassengerType[\"Teacher\"] = 8] = \"Teacher\";\n  PassengerType[PassengerType[\"Medical\"] = 9] = \"Medical\";\n})(PassengerType || (PassengerType = {}));\n// Types de voyageurs\nexport var TravellerType;\n(function (TravellerType) {\n  TravellerType[TravellerType[\"Adult\"] = 1] = \"Adult\";\n  TravellerType[TravellerType[\"Child\"] = 2] = \"Child\";\n  TravellerType[TravellerType[\"Infant\"] = 3] = \"Infant\";\n})(TravellerType || (TravellerType = {}));\n// Titres des voyageurs\nexport var TravellerTitle;\n(function (TravellerTitle) {\n  TravellerTitle[TravellerTitle[\"Mr\"] = 1] = \"Mr\";\n  TravellerTitle[TravellerTitle[\"Ms\"] = 2] = \"Ms\";\n  TravellerTitle[TravellerTitle[\"Mrs\"] = 3] = \"Mrs\";\n  TravellerTitle[TravellerTitle[\"Miss\"] = 4] = \"Miss\";\n  TravellerTitle[TravellerTitle[\"Child\"] = 5] = \"Child\";\n  TravellerTitle[TravellerTitle[\"Infant\"] = 6] = \"Infant\";\n})(TravellerTitle || (TravellerTitle = {}));\n// Genre\nexport var Gender;\n(function (Gender) {\n  Gender[Gender[\"Female\"] = 0] = \"Female\";\n  Gender[Gender[\"Male\"] = 1] = \"Male\";\n})(Gender || (Gender = {}));\n// Types de messages\nexport var MessageType;\n(function (MessageType) {\n  MessageType[MessageType[\"Error\"] = 1] = \"Error\";\n  MessageType[MessageType[\"Success\"] = 2] = \"Success\";\n  MessageType[MessageType[\"Information\"] = 3] = \"Information\";\n  MessageType[MessageType[\"Warning\"] = 4] = \"Warning\";\n})(MessageType || (MessageType = {}));\n// Codes de messages\nexport var MessageCode;\n(function (MessageCode) {\n  MessageCode[MessageCode[\"OperationSuccessful\"] = 1] = \"OperationSuccessful\";\n})(MessageCode || (MessageCode = {}));\n// Types de produits\nexport var ProductType;\n(function (ProductType) {\n  ProductType[ProductType[\"HolidayPackage\"] = 1] = \"HolidayPackage\";\n  ProductType[ProductType[\"Hotel\"] = 2] = \"Hotel\";\n  ProductType[ProductType[\"Flight\"] = 3] = \"Flight\";\n  ProductType[ProductType[\"Excursion\"] = 4] = \"Excursion\";\n  ProductType[ProductType[\"Transfer\"] = 5] = \"Transfer\";\n  ProductType[ProductType[\"Tour\"] = 6] = \"Tour\";\n  ProductType[ProductType[\"Cruise\"] = 7] = \"Cruise\";\n  ProductType[ProductType[\"Transport\"] = 8] = \"Transport\";\n  ProductType[ProductType[\"Ferry\"] = 9] = \"Ferry\";\n  ProductType[ProductType[\"Visa\"] = 10] = \"Visa\";\n  ProductType[ProductType[\"AdditionalService\"] = 11] = \"AdditionalService\";\n  ProductType[ProductType[\"Insurance\"] = 12] = \"Insurance\";\n  ProductType[ProductType[\"Dynamic\"] = 13] = \"Dynamic\";\n  ProductType[ProductType[\"Renting\"] = 14] = \"Renting\";\n})(ProductType || (ProductType = {}));\n// Types de réponses d'autocomplétion\nexport var AutocompleteResponseType;\n(function (AutocompleteResponseType) {\n  AutocompleteResponseType[AutocompleteResponseType[\"City\"] = 1] = \"City\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Hotel\"] = 2] = \"Hotel\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Airport\"] = 3] = \"Airport\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Town\"] = 4] = \"Town\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Village\"] = 5] = \"Village\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Excursion\"] = 6] = \"Excursion\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Category\"] = 7] = \"Category\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Country\"] = 8] = \"Country\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Transfer\"] = 9] = \"Transfer\";\n  AutocompleteResponseType[AutocompleteResponseType[\"ExcursionPackage\"] = 10] = \"ExcursionPackage\";\n})(AutocompleteResponseType || (AutocompleteResponseType = {}));\n// Types de localisation\nexport var LocationType;\n(function (LocationType) {\n  LocationType[LocationType[\"Country\"] = 1] = \"Country\";\n  LocationType[LocationType[\"City\"] = 2] = \"City\";\n  LocationType[LocationType[\"Town\"] = 3] = \"Town\";\n  LocationType[LocationType[\"Village\"] = 4] = \"Village\";\n  LocationType[LocationType[\"Airport\"] = 5] = \"Airport\";\n})(LocationType || (LocationType = {}));\n// Types de fichiers\nexport var FileType;\n(function (FileType) {\n  FileType[FileType[\"Image\"] = 1] = \"Image\";\n  FileType[FileType[\"PDF\"] = 2] = \"PDF\";\n})(FileType || (FileType = {}));\n// Types de documents\nexport var DocumentType;\n(function (DocumentType) {\n  DocumentType[DocumentType[\"Invoice\"] = 1] = \"Invoice\";\n  DocumentType[DocumentType[\"Voucher\"] = 2] = \"Voucher\";\n  DocumentType[DocumentType[\"Insurance\"] = 3] = \"Insurance\";\n  DocumentType[DocumentType[\"FlightTicket\"] = 4] = \"FlightTicket\";\n  DocumentType[DocumentType[\"Contract\"] = 5] = \"Contract\";\n  DocumentType[DocumentType[\"AgencyConfirmation\"] = 6] = \"AgencyConfirmation\";\n  DocumentType[DocumentType[\"ExcursionVoucher\"] = 7] = \"ExcursionVoucher\";\n  DocumentType[DocumentType[\"TransferVoucher\"] = 8] = \"TransferVoucher\";\n  DocumentType[DocumentType[\"Receipt\"] = 9] = \"Receipt\";\n})(DocumentType || (DocumentType = {}));\n// Options de paiement\nexport var PaymentOption;\n(function (PaymentOption) {\n  PaymentOption[PaymentOption[\"Undefined\"] = -1] = \"Undefined\";\n  PaymentOption[PaymentOption[\"Cash\"] = 0] = \"Cash\";\n  PaymentOption[PaymentOption[\"OpenAccount\"] = 1] = \"OpenAccount\";\n  PaymentOption[PaymentOption[\"AgencyCredit\"] = 2] = \"AgencyCredit\";\n  PaymentOption[PaymentOption[\"CreditCard\"] = 3] = \"CreditCard\";\n  PaymentOption[PaymentOption[\"BankTransfer\"] = 4] = \"BankTransfer\";\n  PaymentOption[PaymentOption[\"ExternalCreditCard\"] = 5] = \"ExternalCreditCard\";\n  PaymentOption[PaymentOption[\"Optional\"] = 6] = \"Optional\";\n  PaymentOption[PaymentOption[\"Manuel\"] = 7] = \"Manuel\";\n  PaymentOption[PaymentOption[\"MolliePaymentGateway\"] = 8] = \"MolliePaymentGateway\";\n  PaymentOption[PaymentOption[\"AntePAYPaymentGateway\"] = 9] = \"AntePAYPaymentGateway\";\n  PaymentOption[PaymentOption[\"AllSecurePaymentGateway\"] = 10] = \"AllSecurePaymentGateway\";\n  PaymentOption[PaymentOption[\"SaferpayPaymentGateway\"] = 11] = \"SaferpayPaymentGateway\";\n  PaymentOption[PaymentOption[\"MultiCreditCard\"] = 12] = \"MultiCreditCard\";\n  PaymentOption[PaymentOption[\"SberBankPaymentIntegration\"] = 13] = \"SberBankPaymentIntegration\";\n  PaymentOption[PaymentOption[\"AlfaBankPaymentIntegration\"] = 14] = \"AlfaBankPaymentIntegration\";\n  PaymentOption[PaymentOption[\"KlarnaPaymentIntegration\"] = 15] = \"KlarnaPaymentIntegration\";\n  PaymentOption[PaymentOption[\"NovaPaymentIntegration\"] = 16] = \"NovaPaymentIntegration\";\n  PaymentOption[PaymentOption[\"RevoPaymentIntegration\"] = 17] = \"RevoPaymentIntegration\";\n  PaymentOption[PaymentOption[\"BankartPaymentIntegration\"] = 18] = \"BankartPaymentIntegration\";\n  PaymentOption[PaymentOption[\"CurrentAgencyHasLimitParentNot\"] = 19] = \"CurrentAgencyHasLimitParentNot\";\n  PaymentOption[PaymentOption[\"SafeChargePaymentGateway\"] = 20] = \"SafeChargePaymentGateway\";\n  PaymentOption[PaymentOption[\"BoricaPaymentIntegration\"] = 21] = \"BoricaPaymentIntegration\";\n  PaymentOption[PaymentOption[\"PayTabsPaymentGateway\"] = 22] = \"PayTabsPaymentGateway\";\n  PaymentOption[PaymentOption[\"Aaib\"] = 23] = \"Aaib\";\n  PaymentOption[PaymentOption[\"Stripe\"] = 24] = \"Stripe\";\n  PaymentOption[PaymentOption[\"PayTabs2PaymentGateway\"] = 25] = \"PayTabs2PaymentGateway\";\n  PaymentOption[PaymentOption[\"CibBank\"] = 26] = \"CibBank\";\n  PaymentOption[PaymentOption[\"VubBank\"] = 27] = \"VubBank\";\n  PaymentOption[PaymentOption[\"Moneta\"] = 28] = \"Moneta\";\n  PaymentOption[PaymentOption[\"GiroGate\"] = 29] = \"GiroGate\";\n  PaymentOption[PaymentOption[\"Monri\"] = 30] = \"Monri\";\n  PaymentOption[PaymentOption[\"Maib\"] = 31] = \"Maib\";\n  PaymentOption[PaymentOption[\"NGenius\"] = 32] = \"NGenius\";\n  PaymentOption[PaymentOption[\"EcommPay\"] = 33] = \"EcommPay\";\n  PaymentOption[PaymentOption[\"EcomCMIBankIntesa\"] = 34] = \"EcomCMIBankIntesa\";\n  PaymentOption[PaymentOption[\"Moka\"] = 35] = \"Moka\";\n  PaymentOption[PaymentOption[\"Adyen\"] = 36] = \"Adyen\";\n  PaymentOption[PaymentOption[\"Mellat\"] = 37] = \"Mellat\";\n  PaymentOption[PaymentOption[\"SigmaPay\"] = 39] = \"SigmaPay\";\n  PaymentOption[PaymentOption[\"FibBank\"] = 40] = \"FibBank\";\n  PaymentOption[PaymentOption[\"EMoney\"] = 42] = \"EMoney\";\n  PaymentOption[PaymentOption[\"FastPay\"] = 43] = \"FastPay\";\n})(PaymentOption || (PaymentOption = {}));\n// Fournisseurs de paiement\nexport var PaymentProvider;\n(function (PaymentProvider) {\n  PaymentProvider[PaymentProvider[\"Iyzico\"] = 1000] = \"Iyzico\";\n  PaymentProvider[PaymentProvider[\"NestPay\"] = 1001] = \"NestPay\";\n  PaymentProvider[PaymentProvider[\"DPay\"] = 1002] = \"DPay\";\n  PaymentProvider[PaymentProvider[\"Mellay\"] = 1004] = \"Mellay\";\n  PaymentProvider[PaymentProvider[\"MIGS\"] = 1005] = \"MIGS\";\n  PaymentProvider[PaymentProvider[\"EST\"] = 1006] = \"EST\";\n  PaymentProvider[PaymentProvider[\"Garanti\"] = 1008] = \"Garanti\";\n  PaymentProvider[PaymentProvider[\"Denizbank\"] = 1009] = \"Denizbank\";\n  PaymentProvider[PaymentProvider[\"YapiKredi\"] = 1010] = \"YapiKredi\";\n  PaymentProvider[PaymentProvider[\"AlfaBank\"] = 1011] = \"AlfaBank\";\n  PaymentProvider[PaymentProvider[\"Mollie\"] = 1012] = \"Mollie\";\n  PaymentProvider[PaymentProvider[\"AllSecure\"] = 1014] = \"AllSecure\";\n  PaymentProvider[PaymentProvider[\"Saferpay\"] = 1016] = \"Saferpay\";\n  PaymentProvider[PaymentProvider[\"Sberbank\"] = 1017] = \"Sberbank\";\n  PaymentProvider[PaymentProvider[\"Klarna\"] = 1018] = \"Klarna\";\n  PaymentProvider[PaymentProvider[\"Finansbank\"] = 1019] = \"Finansbank\";\n  PaymentProvider[PaymentProvider[\"Revo\"] = 1021] = \"Revo\";\n  PaymentProvider[PaymentProvider[\"Bankart\"] = 1022] = \"Bankart\";\n  PaymentProvider[PaymentProvider[\"SafeCharge\"] = 1023] = \"SafeCharge\";\n  PaymentProvider[PaymentProvider[\"Borica\"] = 1024] = \"Borica\";\n  PaymentProvider[PaymentProvider[\"PayTabs\"] = 1025] = \"PayTabs\";\n  PaymentProvider[PaymentProvider[\"Aaib\"] = 1026] = \"Aaib\";\n  PaymentProvider[PaymentProvider[\"Stripe\"] = 1027] = \"Stripe\";\n  PaymentProvider[PaymentProvider[\"PayTabsV2\"] = 1028] = \"PayTabsV2\";\n  PaymentProvider[PaymentProvider[\"CIBBank\"] = 1029] = \"CIBBank\";\n  PaymentProvider[PaymentProvider[\"VUBBank\"] = 1030] = \"VUBBank\";\n  PaymentProvider[PaymentProvider[\"Vakifbank\"] = 1031] = \"Vakifbank\";\n  PaymentProvider[PaymentProvider[\"Moneta\"] = 1032] = \"Moneta\";\n  PaymentProvider[PaymentProvider[\"GiroGate\"] = 1033] = \"GiroGate\";\n  PaymentProvider[PaymentProvider[\"Param\"] = 1034] = \"Param\";\n  PaymentProvider[PaymentProvider[\"Monri\"] = 1035] = \"Monri\";\n  PaymentProvider[PaymentProvider[\"Maib\"] = 1036] = \"Maib\";\n  PaymentProvider[PaymentProvider[\"NGenius\"] = 1037] = \"NGenius\";\n  PaymentProvider[PaymentProvider[\"EcommPay\"] = 1038] = \"EcommPay\";\n  PaymentProvider[PaymentProvider[\"EcomCMIBankIntesa\"] = 1039] = \"EcomCMIBankIntesa\";\n  PaymentProvider[PaymentProvider[\"Moka\"] = 1040] = \"Moka\";\n  PaymentProvider[PaymentProvider[\"AdyenBank\"] = 1041] = \"AdyenBank\";\n  PaymentProvider[PaymentProvider[\"SigmaPay\"] = 1043] = \"SigmaPay\";\n  PaymentProvider[PaymentProvider[\"FibBank\"] = 1044] = \"FibBank\";\n  PaymentProvider[PaymentProvider[\"EMoney\"] = 1046] = \"EMoney\";\n  PaymentProvider[PaymentProvider[\"FastPay\"] = 1047] = \"FastPay\";\n})(PaymentProvider || (PaymentProvider = {}));\n// Type de transaction de paiement\nexport var PaymentTransactionType;\n(function (PaymentTransactionType) {\n  PaymentTransactionType[PaymentTransactionType[\"Undefined\"] = 0] = \"Undefined\";\n  PaymentTransactionType[PaymentTransactionType[\"OpenModal\"] = 1] = \"OpenModal\";\n  PaymentTransactionType[PaymentTransactionType[\"PostForm\"] = 2] = \"PostForm\";\n  PaymentTransactionType[PaymentTransactionType[\"PostUrl\"] = 3] = \"PostUrl\";\n  PaymentTransactionType[PaymentTransactionType[\"Continue\"] = 4] = \"Continue\";\n  PaymentTransactionType[PaymentTransactionType[\"RedirectToPostUrl\"] = 5] = \"RedirectToPostUrl\";\n  PaymentTransactionType[PaymentTransactionType[\"RenderHtml\"] = 6] = \"RenderHtml\";\n})(PaymentTransactionType || (PaymentTransactionType = {}));\n// Type de réponse de transaction de paiement\nexport var PaymentTransactionResponseType;\n(function (PaymentTransactionResponseType) {\n  PaymentTransactionResponseType[PaymentTransactionResponseType[\"WaitPaymentStatus\"] = 0] = \"WaitPaymentStatus\";\n  PaymentTransactionResponseType[PaymentTransactionResponseType[\"CheckPaymentStatus\"] = 1] = \"CheckPaymentStatus\";\n  PaymentTransactionResponseType[PaymentTransactionResponseType[\"CheckCommitTransactionResponse\"] = 2] = \"CheckCommitTransactionResponse\";\n})(PaymentTransactionResponseType || (PaymentTransactionResponseType = {}));\n// Statut de paiement\nexport var PaymentStatus;\n(function (PaymentStatus) {\n  PaymentStatus[PaymentStatus[\"None\"] = 1] = \"None\";\n  PaymentStatus[PaymentStatus[\"UnPaid\"] = 2] = \"UnPaid\";\n  PaymentStatus[PaymentStatus[\"PartlyPaid\"] = 3] = \"PartlyPaid\";\n  PaymentStatus[PaymentStatus[\"Paid\"] = 4] = \"Paid\";\n  PaymentStatus[PaymentStatus[\"Over\"] = 5] = \"Over\";\n})(PaymentStatus || (PaymentStatus = {}));\n// Statut de transaction de paiement\nexport var PaymentTransactionStatus;\n(function (PaymentTransactionStatus) {\n  PaymentTransactionStatus[PaymentTransactionStatus[\"InComplete\"] = 0] = \"InComplete\";\n  PaymentTransactionStatus[PaymentTransactionStatus[\"Complete\"] = 1] = \"Complete\";\n  PaymentTransactionStatus[PaymentTransactionStatus[\"Errored\"] = 2] = \"Errored\";\n  PaymentTransactionStatus[PaymentTransactionStatus[\"Voided\"] = 3] = \"Voided\";\n  PaymentTransactionStatus[PaymentTransactionStatus[\"Refunded\"] = 4] = \"Refunded\";\n})(PaymentTransactionStatus || (PaymentTransactionStatus = {}));\n// Statut de réservation\nexport var ReservationStatus;\n(function (ReservationStatus) {\n  ReservationStatus[ReservationStatus[\"New\"] = 0] = \"New\";\n  ReservationStatus[ReservationStatus[\"Modified\"] = 1] = \"Modified\";\n  ReservationStatus[ReservationStatus[\"Cancel\"] = 2] = \"Cancel\";\n  ReservationStatus[ReservationStatus[\"CancelX\"] = 3] = \"CancelX\";\n  ReservationStatus[ReservationStatus[\"Draft\"] = 9] = \"Draft\";\n})(ReservationStatus || (ReservationStatus = {}));\n// Statut de confirmation\nexport var ConfirmationStatus;\n(function (ConfirmationStatus) {\n  ConfirmationStatus[ConfirmationStatus[\"Request\"] = 0] = \"Request\";\n  ConfirmationStatus[ConfirmationStatus[\"Confirm\"] = 1] = \"Confirm\";\n  ConfirmationStatus[ConfirmationStatus[\"NoConfirm\"] = 2] = \"NoConfirm\";\n  ConfirmationStatus[ConfirmationStatus[\"NoShow\"] = 3] = \"NoShow\";\n})(ConfirmationStatus || (ConfirmationStatus = {}));\n// Statut du processus de transaction de réservation\nexport var BookingTransactionProcessStatus;\n(function (BookingTransactionProcessStatus) {\n  BookingTransactionProcessStatus[BookingTransactionProcessStatus[\"Waiting\"] = 0] = \"Waiting\";\n  BookingTransactionProcessStatus[BookingTransactionProcessStatus[\"Completed\"] = 1] = \"Completed\";\n  BookingTransactionProcessStatus[BookingTransactionProcessStatus[\"Errored\"] = 2] = \"Errored\";\n})(BookingTransactionProcessStatus || (BookingTransactionProcessStatus = {}));\n// Statut de transaction de réservation\nexport var BookingTransactionStatus;\n(function (BookingTransactionStatus) {\n  BookingTransactionStatus[BookingTransactionStatus[\"Errored\"] = 0] = \"Errored\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Completed\"] = 1] = \"Completed\";\n  BookingTransactionStatus[BookingTransactionStatus[\"ThirdPartyCompleted\"] = 2] = \"ThirdPartyCompleted\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Committing\"] = 3] = \"Committing\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Open\"] = 4] = \"Open\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Expired\"] = 5] = \"Expired\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Cancelled\"] = 6] = \"Cancelled\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Reserved\"] = 7] = \"Reserved\";\n  BookingTransactionStatus[BookingTransactionStatus[\"RollbackFailed\"] = 8] = \"RollbackFailed\";\n  BookingTransactionStatus[BookingTransactionStatus[\"PartialCancel\"] = 9] = \"PartialCancel\";\n  BookingTransactionStatus[BookingTransactionStatus[\"CancelX\"] = 10] = \"CancelX\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Modified\"] = 11] = \"Modified\";\n})(BookingTransactionStatus || (BookingTransactionStatus = {}));\n// Disponibilité de l'offre\nexport var OfferAvailability;\n(function (OfferAvailability) {\n  OfferAvailability[OfferAvailability[\"Available\"] = 1] = \"Available\";\n  OfferAvailability[OfferAvailability[\"Request\"] = 2] = \"Request\";\n  OfferAvailability[OfferAvailability[\"NotAvailable\"] = 3] = \"NotAvailable\";\n})(OfferAvailability || (OfferAvailability = {}));\n// Types de bagages\nexport var BaggageType;\n(function (BaggageType) {\n  BaggageType[BaggageType[\"Checkin\"] = 1] = \"Checkin\";\n  BaggageType[BaggageType[\"Hand\"] = 2] = \"Hand\";\n})(BaggageType || (BaggageType = {}));\n// Direction de transfert\nexport var TransferDirection;\n(function (TransferDirection) {\n  TransferDirection[TransferDirection[\"Forward\"] = 1] = \"Forward\";\n  TransferDirection[TransferDirection[\"Backward\"] = 2] = \"Backward\";\n  TransferDirection[TransferDirection[\"RoundTrip\"] = 3] = \"RoundTrip\";\n})(TransferDirection || (TransferDirection = {}));\n// Types de vols\nexport var FlightType;\n(function (FlightType) {\n  FlightType[FlightType[\"Regular\"] = 0] = \"Regular\";\n  FlightType[FlightType[\"Charter\"] = 1] = \"Charter\";\n})(FlightType || (FlightType = {}));\n// Types d'itinéraires de vol\nexport var FlightRouteType;\n(function (FlightRouteType) {\n  FlightRouteType[FlightRouteType[\"Outbound\"] = 1] = \"Outbound\";\n  FlightRouteType[FlightRouteType[\"Return\"] = 2] = \"Return\";\n})(FlightRouteType || (FlightRouteType = {}));\n// Types de classes de vol\nexport var FlightClassType;\n(function (FlightClassType) {\n  FlightClassType[FlightClassType[\"PROMO\"] = 0] = \"PROMO\";\n  FlightClassType[FlightClassType[\"ECONOMY\"] = 1] = \"ECONOMY\";\n  FlightClassType[FlightClassType[\"BUSINESS\"] = 2] = \"BUSINESS\";\n})(FlightClassType || (FlightClassType = {}));\n// Groupe de service de fonctionnalité de marque de vol\nexport var FlightBrandFeatureServiceGroup;\n(function (FlightBrandFeatureServiceGroup) {\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"BrandedFare\"] = 0] = \"BrandedFare\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"Baggage\"] = 1] = \"Baggage\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"FrequentFlyer\"] = 2] = \"FrequentFlyer\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"InFlightEntertainment\"] = 3] = \"InFlightEntertainment\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"LoungeAccess\"] = 4] = \"LoungeAccess\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"MealBeverage\"] = 5] = \"MealBeverage\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"Pets\"] = 6] = \"Pets\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"Upgrades\"] = 7] = \"Upgrades\";\n})(FlightBrandFeatureServiceGroup || (FlightBrandFeatureServiceGroup = {}));\n// Type de tarification de marque de vol\nexport var FlightBrandPricingType;\n(function (FlightBrandPricingType) {\n  FlightBrandPricingType[FlightBrandPricingType[\"Free\"] = 0] = \"Free\";\n  FlightBrandPricingType[FlightBrandPricingType[\"Chargable\"] = 1] = \"Chargable\";\n  FlightBrandPricingType[FlightBrandPricingType[\"NotOffered\"] = 2] = \"NotOffered\";\n})(FlightBrandPricingType || (FlightBrandPricingType = {}));\n// Option de récupération de bagages de vol\nexport var FlightBaggageGetOption;\n(function (FlightBaggageGetOption) {\n  FlightBaggageGetOption[FlightBaggageGetOption[\"All\"] = 0] = \"All\";\n  FlightBaggageGetOption[FlightBaggageGetOption[\"OnlyBaggage\"] = 1] = \"OnlyBaggage\";\n  FlightBaggageGetOption[FlightBaggageGetOption[\"OnlyNotBaggage\"] = 2] = \"OnlyNotBaggage\";\n})(FlightBaggageGetOption || (FlightBaggageGetOption = {}));\n// Type d'unité de bagage\nexport var BaggageUnitType;\n(function (BaggageUnitType) {\n  BaggageUnitType[BaggageUnitType[\"KG\"] = 0] = \"KG\";\n  BaggageUnitType[BaggageUnitType[\"LB\"] = 1] = \"LB\";\n  BaggageUnitType[BaggageUnitType[\"Piece\"] = 2] = \"Piece\";\n})(BaggageUnitType || (BaggageUnitType = {}));\n// Types de critères de date\nexport var DateCriteriaType;\n(function (DateCriteriaType) {\n  DateCriteriaType[DateCriteriaType[\"SERVICEDATE\"] = 0] = \"SERVICEDATE\";\n  DateCriteriaType[DateCriteriaType[\"REGISTER\"] = 1] = \"REGISTER\";\n  DateCriteriaType[DateCriteriaType[\"CANCELLATIONDEADLINE\"] = 2] = \"CANCELLATIONDEADLINE\";\n  DateCriteriaType[DateCriteriaType[\"OPTIONDATE\"] = 3] = \"OPTIONDATE\";\n  DateCriteriaType[DateCriteriaType[\"CHANGEDATE\"] = 4] = \"CHANGEDATE\";\n})(DateCriteriaType || (DateCriteriaType = {}));\n// Types de prix\nexport var PriceType;\n(function (PriceType) {\n  PriceType[PriceType[\"UNDEFINED\"] = 0] = \"UNDEFINED\";\n  PriceType[PriceType[\"PERPAX\"] = 1] = \"PERPAX\";\n  PriceType[PriceType[\"PERSERVICE\"] = 2] = \"PERSERVICE\";\n  PriceType[PriceType[\"PERROOM\"] = 3] = \"PERROOM\";\n})(PriceType || (PriceType = {}));\n// Types de reçus\nexport var ReceiptType;\n(function (ReceiptType) {\n  ReceiptType[ReceiptType[\"Payment\"] = 0] = \"Payment\";\n  ReceiptType[ReceiptType[\"Refund\"] = 1] = \"Refund\";\n})(ReceiptType || (ReceiptType = {}));", "map": {"version": 3, "names": ["PassengerType", "TravellerType", "TravellerTitle", "Gender", "MessageType", "MessageCode", "ProductType", "AutocompleteResponseType", "LocationType", "FileType", "DocumentType", "PaymentOption", "PaymentProvider", "PaymentTransactionType", "PaymentTransactionResponseType", "PaymentStatus", "PaymentTransactionStatus", "ReservationStatus", "ConfirmationStatus", "BookingTransactionProcessStatus", "BookingTransactionStatus", "OfferAvailability", "BaggageType", "TransferDirection", "FlightType", "FlightRouteType", "FlightClassType", "FlightBrandFeatureServiceGroup", "FlightBrandPricingType", "FlightBaggageGetOption", "BaggageUnitType", "DateCriteriaType", "PriceType", "ReceiptType"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\models\\enums.model.ts"], "sourcesContent": ["/**\n * Énumérations basées sur la documentation de l'API Paximum\n */\n\n// Types de passagers\nexport enum PassengerType {\n  Adult = 1,\n  Child = 2,\n  Infant = 3,\n  Senior = 4,\n  Student = 5,\n  Young = 6,\n  Military = 7,\n  Teacher = 8,\n  Medical = 9\n}\n\n// Types de voyageurs\nexport enum TravellerType {\n  Adult = 1,\n  Child = 2,\n  Infant = 3\n}\n\n// Titres des voyageurs\nexport enum TravellerTitle {\n  Mr = 1,\n  Ms = 2,\n  Mrs = 3,\n  Miss = 4,\n  Child = 5,\n  Infant = 6\n}\n\n// Genre\nexport enum Gender {\n  Female = 0,\n  Male = 1\n}\n\n// Types de messages\nexport enum MessageType {\n  Error = 1,\n  Success = 2,\n  Information = 3,\n  Warning = 4\n}\n\n// Codes de messages\nexport enum MessageCode {\n  OperationSuccessful = 1\n}\n\n// Types de produits\nexport enum ProductType {\n  HolidayPackage = 1,\n  Hotel = 2,\n  Flight = 3,\n  Excursion = 4,\n  Transfer = 5,\n  Tour = 6,\n  Cruise = 7,\n  Transport = 8,\n  Ferry = 9,\n  Visa = 10,\n  AdditionalService = 11,\n  Insurance = 12,\n  Dynamic = 13,\n  Renting = 14\n}\n\n// Types de réponses d'autocomplétion\nexport enum AutocompleteResponseType {\n  City = 1,\n  Hotel = 2,\n  Airport = 3,\n  Town = 4,\n  Village = 5,\n  Excursion = 6,\n  Category = 7,\n  Country = 8,\n  Transfer = 9,\n  ExcursionPackage = 10\n}\n\n// Types de localisation\nexport enum LocationType {\n  Country = 1,\n  City = 2,\n  Town = 3,\n  Village = 4,\n  Airport = 5\n}\n\n// Types de fichiers\nexport enum FileType {\n  Image = 1,\n  PDF = 2\n}\n\n// Types de documents\nexport enum DocumentType {\n  Invoice = 1,\n  Voucher = 2,\n  Insurance = 3,\n  FlightTicket = 4,\n  Contract = 5,\n  AgencyConfirmation = 6,\n  ExcursionVoucher = 7,\n  TransferVoucher = 8,\n  Receipt = 9\n}\n\n// Options de paiement\nexport enum PaymentOption {\n  Undefined = -1,\n  Cash = 0,\n  OpenAccount = 1,\n  AgencyCredit = 2,\n  CreditCard = 3,\n  BankTransfer = 4,\n  ExternalCreditCard = 5,\n  Optional = 6,\n  Manuel = 7,\n  MolliePaymentGateway = 8,\n  AntePAYPaymentGateway = 9,\n  AllSecurePaymentGateway = 10,\n  SaferpayPaymentGateway = 11,\n  MultiCreditCard = 12,\n  SberBankPaymentIntegration = 13,\n  AlfaBankPaymentIntegration = 14,\n  KlarnaPaymentIntegration = 15,\n  NovaPaymentIntegration = 16,\n  RevoPaymentIntegration = 17,\n  BankartPaymentIntegration = 18,\n  CurrentAgencyHasLimitParentNot = 19,\n  SafeChargePaymentGateway = 20,\n  BoricaPaymentIntegration = 21,\n  PayTabsPaymentGateway = 22,\n  Aaib = 23,\n  Stripe = 24,\n  PayTabs2PaymentGateway = 25,\n  CibBank = 26,\n  VubBank = 27,\n  Moneta = 28,\n  GiroGate = 29,\n  Monri = 30,\n  Maib = 31,\n  NGenius = 32,\n  EcommPay = 33,\n  EcomCMIBankIntesa = 34,\n  Moka = 35,\n  Adyen = 36,\n  Mellat = 37,\n  SigmaPay = 39,\n  FibBank = 40,\n  EMoney = 42,\n  FastPay = 43\n}\n\n// Fournisseurs de paiement\nexport enum PaymentProvider {\n  Iyzico = 1000,\n  NestPay = 1001,\n  DPay = 1002,\n  Mellay = 1004,\n  MIGS = 1005,\n  EST = 1006,\n  Garanti = 1008,\n  Denizbank = 1009,\n  YapiKredi = 1010,\n  AlfaBank = 1011,\n  Mollie = 1012,\n  AllSecure = 1014,\n  Saferpay = 1016,\n  Sberbank = 1017,\n  Klarna = 1018,\n  Finansbank = 1019,\n  Revo = 1021,\n  Bankart = 1022,\n  SafeCharge = 1023,\n  Borica = 1024,\n  PayTabs = 1025,\n  Aaib = 1026,\n  Stripe = 1027,\n  PayTabsV2 = 1028,\n  CIBBank = 1029,\n  VUBBank = 1030,\n  Vakifbank = 1031,\n  Moneta = 1032,\n  GiroGate = 1033,\n  Param = 1034,\n  Monri = 1035,\n  Maib = 1036,\n  NGenius = 1037,\n  EcommPay = 1038,\n  EcomCMIBankIntesa = 1039,\n  Moka = 1040,\n  AdyenBank = 1041,\n  SigmaPay = 1043,\n  FibBank = 1044,\n  EMoney = 1046,\n  FastPay = 1047\n}\n\n// Type de transaction de paiement\nexport enum PaymentTransactionType {\n  Undefined = 0,\n  OpenModal = 1,\n  PostForm = 2,\n  PostUrl = 3,\n  Continue = 4,\n  RedirectToPostUrl = 5,\n  RenderHtml = 6\n}\n\n// Type de réponse de transaction de paiement\nexport enum PaymentTransactionResponseType {\n  WaitPaymentStatus = 0,\n  CheckPaymentStatus = 1,\n  CheckCommitTransactionResponse = 2\n}\n\n// Statut de paiement\nexport enum PaymentStatus {\n  None = 1,\n  UnPaid = 2,\n  PartlyPaid = 3,\n  Paid = 4,\n  Over = 5\n}\n\n// Statut de transaction de paiement\nexport enum PaymentTransactionStatus {\n  InComplete = 0,\n  Complete = 1,\n  Errored = 2,\n  Voided = 3,\n  Refunded = 4\n}\n\n// Statut de réservation\nexport enum ReservationStatus {\n  New = 0,\n  Modified = 1,\n  Cancel = 2,\n  CancelX = 3,\n  Draft = 9\n}\n\n// Statut de confirmation\nexport enum ConfirmationStatus {\n  Request = 0,\n  Confirm = 1,\n  NoConfirm = 2,\n  NoShow = 3\n}\n\n// Statut du processus de transaction de réservation\nexport enum BookingTransactionProcessStatus {\n  Waiting = 0,\n  Completed = 1,\n  Errored = 2\n}\n\n// Statut de transaction de réservation\nexport enum BookingTransactionStatus {\n  Errored = 0,\n  Completed = 1,\n  ThirdPartyCompleted = 2,\n  Committing = 3,\n  Open = 4,\n  Expired = 5,\n  Cancelled = 6,\n  Reserved = 7,\n  RollbackFailed = 8,\n  PartialCancel = 9,\n  CancelX = 10,\n  Modified = 11\n}\n\n// Disponibilité de l'offre\nexport enum OfferAvailability {\n  Available = 1,\n  Request = 2,\n  NotAvailable = 3\n}\n\n// Types de bagages\nexport enum BaggageType {\n  Checkin = 1,\n  Hand = 2\n}\n\n// Direction de transfert\nexport enum TransferDirection {\n  Forward = 1,\n  Backward = 2,\n  RoundTrip = 3\n}\n\n// Types de vols\nexport enum FlightType {\n  Regular = 0,\n  Charter = 1\n}\n\n// Types d'itinéraires de vol\nexport enum FlightRouteType {\n  Outbound = 1,\n  Return = 2\n}\n\n// Types de classes de vol\nexport enum FlightClassType {\n  PROMO = 0,\n  ECONOMY = 1,\n  BUSINESS = 2\n}\n\n// Groupe de service de fonctionnalité de marque de vol\nexport enum FlightBrandFeatureServiceGroup {\n  BrandedFare = 0,\n  Baggage = 1,\n  FrequentFlyer = 2,\n  InFlightEntertainment = 3,\n  LoungeAccess = 4,\n  MealBeverage = 5,\n  Pets = 6,\n  Upgrades = 7\n}\n\n// Type de tarification de marque de vol\nexport enum FlightBrandPricingType {\n  Free = 0,\n  Chargable = 1,\n  NotOffered = 2\n}\n\n// Option de récupération de bagages de vol\nexport enum FlightBaggageGetOption {\n  All = 0,\n  OnlyBaggage = 1,\n  OnlyNotBaggage = 2\n}\n\n// Type d'unité de bagage\nexport enum BaggageUnitType {\n  KG = 0,\n  LB = 1,\n  Piece = 2\n}\n\n// Types de critères de date\nexport enum DateCriteriaType {\n  SERVICEDATE = 0,\n  REGISTER = 1,\n  CANCELLATIONDEADLINE = 2,\n  OPTIONDATE = 3,\n  CHANGEDATE = 4\n}\n\n// Types de prix\nexport enum PriceType {\n  UNDEFINED = 0,\n  PERPAX = 1,\n  PERSERVICE = 2,\n  PERROOM = 3\n}\n\n// Types de reçus\nexport enum ReceiptType {\n  Payment = 0,\n  Refund = 1\n}\n"], "mappings": "AAAA;;;AAIA;AACA,WAAYA,aAUX;AAVD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,0BAAU;EACVA,aAAA,CAAAA,aAAA,0BAAU;EACVA,aAAA,CAAAA,aAAA,4BAAW;EACXA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,8BAAY;EACZA,aAAA,CAAAA,aAAA,4BAAW;EACXA,aAAA,CAAAA,aAAA,4BAAW;AACb,CAAC,EAVWA,aAAa,KAAbA,aAAa;AAYzB;AACA,WAAYC,aAIX;AAJD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,0BAAU;AACZ,CAAC,EAJWA,aAAa,KAAbA,aAAa;AAMzB;AACA,WAAYC,cAOX;AAPD,WAAYA,cAAc;EACxBA,cAAA,CAAAA,cAAA,kBAAM;EACNA,cAAA,CAAAA,cAAA,kBAAM;EACNA,cAAA,CAAAA,cAAA,oBAAO;EACPA,cAAA,CAAAA,cAAA,sBAAQ;EACRA,cAAA,CAAAA,cAAA,wBAAS;EACTA,cAAA,CAAAA,cAAA,0BAAU;AACZ,CAAC,EAPWA,cAAc,KAAdA,cAAc;AAS1B;AACA,WAAYC,MAGX;AAHD,WAAYA,MAAM;EAChBA,MAAA,CAAAA,MAAA,0BAAU;EACVA,MAAA,CAAAA,MAAA,sBAAQ;AACV,CAAC,EAHWA,MAAM,KAANA,MAAM;AAKlB;AACA,WAAYC,WAKX;AALD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,wBAAS;EACTA,WAAA,CAAAA,WAAA,4BAAW;EACXA,WAAA,CAAAA,WAAA,oCAAe;EACfA,WAAA,CAAAA,WAAA,4BAAW;AACb,CAAC,EALWA,WAAW,KAAXA,WAAW;AAOvB;AACA,WAAYC,WAEX;AAFD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,oDAAuB;AACzB,CAAC,EAFWA,WAAW,KAAXA,WAAW;AAIvB;AACA,WAAYC,WAeX;AAfD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,0CAAkB;EAClBA,WAAA,CAAAA,WAAA,wBAAS;EACTA,WAAA,CAAAA,WAAA,0BAAU;EACVA,WAAA,CAAAA,WAAA,gCAAa;EACbA,WAAA,CAAAA,WAAA,8BAAY;EACZA,WAAA,CAAAA,WAAA,sBAAQ;EACRA,WAAA,CAAAA,WAAA,0BAAU;EACVA,WAAA,CAAAA,WAAA,gCAAa;EACbA,WAAA,CAAAA,WAAA,wBAAS;EACTA,WAAA,CAAAA,WAAA,uBAAS;EACTA,WAAA,CAAAA,WAAA,iDAAsB;EACtBA,WAAA,CAAAA,WAAA,iCAAc;EACdA,WAAA,CAAAA,WAAA,6BAAY;EACZA,WAAA,CAAAA,WAAA,6BAAY;AACd,CAAC,EAfWA,WAAW,KAAXA,WAAW;AAiBvB;AACA,WAAYC,wBAWX;AAXD,WAAYA,wBAAwB;EAClCA,wBAAA,CAAAA,wBAAA,sBAAQ;EACRA,wBAAA,CAAAA,wBAAA,wBAAS;EACTA,wBAAA,CAAAA,wBAAA,4BAAW;EACXA,wBAAA,CAAAA,wBAAA,sBAAQ;EACRA,wBAAA,CAAAA,wBAAA,4BAAW;EACXA,wBAAA,CAAAA,wBAAA,gCAAa;EACbA,wBAAA,CAAAA,wBAAA,8BAAY;EACZA,wBAAA,CAAAA,wBAAA,4BAAW;EACXA,wBAAA,CAAAA,wBAAA,8BAAY;EACZA,wBAAA,CAAAA,wBAAA,+CAAqB;AACvB,CAAC,EAXWA,wBAAwB,KAAxBA,wBAAwB;AAapC;AACA,WAAYC,YAMX;AAND,WAAYA,YAAY;EACtBA,YAAA,CAAAA,YAAA,4BAAW;EACXA,YAAA,CAAAA,YAAA,sBAAQ;EACRA,YAAA,CAAAA,YAAA,sBAAQ;EACRA,YAAA,CAAAA,YAAA,4BAAW;EACXA,YAAA,CAAAA,YAAA,4BAAW;AACb,CAAC,EANWA,YAAY,KAAZA,YAAY;AAQxB;AACA,WAAYC,QAGX;AAHD,WAAYA,QAAQ;EAClBA,QAAA,CAAAA,QAAA,wBAAS;EACTA,QAAA,CAAAA,QAAA,oBAAO;AACT,CAAC,EAHWA,QAAQ,KAARA,QAAQ;AAKpB;AACA,WAAYC,YAUX;AAVD,WAAYA,YAAY;EACtBA,YAAA,CAAAA,YAAA,4BAAW;EACXA,YAAA,CAAAA,YAAA,4BAAW;EACXA,YAAA,CAAAA,YAAA,gCAAa;EACbA,YAAA,CAAAA,YAAA,sCAAgB;EAChBA,YAAA,CAAAA,YAAA,8BAAY;EACZA,YAAA,CAAAA,YAAA,kDAAsB;EACtBA,YAAA,CAAAA,YAAA,8CAAoB;EACpBA,YAAA,CAAAA,YAAA,4CAAmB;EACnBA,YAAA,CAAAA,YAAA,4BAAW;AACb,CAAC,EAVWA,YAAY,KAAZA,YAAY;AAYxB;AACA,WAAYC,aA4CX;AA5CD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,iCAAc;EACdA,aAAA,CAAAA,aAAA,sBAAQ;EACRA,aAAA,CAAAA,aAAA,oCAAe;EACfA,aAAA,CAAAA,aAAA,sCAAgB;EAChBA,aAAA,CAAAA,aAAA,kCAAc;EACdA,aAAA,CAAAA,aAAA,sCAAgB;EAChBA,aAAA,CAAAA,aAAA,kDAAsB;EACtBA,aAAA,CAAAA,aAAA,8BAAY;EACZA,aAAA,CAAAA,aAAA,0BAAU;EACVA,aAAA,CAAAA,aAAA,sDAAwB;EACxBA,aAAA,CAAAA,aAAA,wDAAyB;EACzBA,aAAA,CAAAA,aAAA,6DAA4B;EAC5BA,aAAA,CAAAA,aAAA,2DAA2B;EAC3BA,aAAA,CAAAA,aAAA,6CAAoB;EACpBA,aAAA,CAAAA,aAAA,mEAA+B;EAC/BA,aAAA,CAAAA,aAAA,mEAA+B;EAC/BA,aAAA,CAAAA,aAAA,+DAA6B;EAC7BA,aAAA,CAAAA,aAAA,2DAA2B;EAC3BA,aAAA,CAAAA,aAAA,2DAA2B;EAC3BA,aAAA,CAAAA,aAAA,iEAA8B;EAC9BA,aAAA,CAAAA,aAAA,2EAAmC;EACnCA,aAAA,CAAAA,aAAA,+DAA6B;EAC7BA,aAAA,CAAAA,aAAA,+DAA6B;EAC7BA,aAAA,CAAAA,aAAA,yDAA0B;EAC1BA,aAAA,CAAAA,aAAA,uBAAS;EACTA,aAAA,CAAAA,aAAA,2BAAW;EACXA,aAAA,CAAAA,aAAA,2DAA2B;EAC3BA,aAAA,CAAAA,aAAA,6BAAY;EACZA,aAAA,CAAAA,aAAA,6BAAY;EACZA,aAAA,CAAAA,aAAA,2BAAW;EACXA,aAAA,CAAAA,aAAA,+BAAa;EACbA,aAAA,CAAAA,aAAA,yBAAU;EACVA,aAAA,CAAAA,aAAA,uBAAS;EACTA,aAAA,CAAAA,aAAA,6BAAY;EACZA,aAAA,CAAAA,aAAA,+BAAa;EACbA,aAAA,CAAAA,aAAA,iDAAsB;EACtBA,aAAA,CAAAA,aAAA,uBAAS;EACTA,aAAA,CAAAA,aAAA,yBAAU;EACVA,aAAA,CAAAA,aAAA,2BAAW;EACXA,aAAA,CAAAA,aAAA,+BAAa;EACbA,aAAA,CAAAA,aAAA,6BAAY;EACZA,aAAA,CAAAA,aAAA,2BAAW;EACXA,aAAA,CAAAA,aAAA,6BAAY;AACd,CAAC,EA5CWA,aAAa,KAAbA,aAAa;AA8CzB;AACA,WAAYC,eA0CX;AA1CD,WAAYA,eAAe;EACzBA,eAAA,CAAAA,eAAA,6BAAa;EACbA,eAAA,CAAAA,eAAA,+BAAc;EACdA,eAAA,CAAAA,eAAA,yBAAW;EACXA,eAAA,CAAAA,eAAA,6BAAa;EACbA,eAAA,CAAAA,eAAA,yBAAW;EACXA,eAAA,CAAAA,eAAA,uBAAU;EACVA,eAAA,CAAAA,eAAA,+BAAc;EACdA,eAAA,CAAAA,eAAA,mCAAgB;EAChBA,eAAA,CAAAA,eAAA,mCAAgB;EAChBA,eAAA,CAAAA,eAAA,iCAAe;EACfA,eAAA,CAAAA,eAAA,6BAAa;EACbA,eAAA,CAAAA,eAAA,mCAAgB;EAChBA,eAAA,CAAAA,eAAA,iCAAe;EACfA,eAAA,CAAAA,eAAA,iCAAe;EACfA,eAAA,CAAAA,eAAA,6BAAa;EACbA,eAAA,CAAAA,eAAA,qCAAiB;EACjBA,eAAA,CAAAA,eAAA,yBAAW;EACXA,eAAA,CAAAA,eAAA,+BAAc;EACdA,eAAA,CAAAA,eAAA,qCAAiB;EACjBA,eAAA,CAAAA,eAAA,6BAAa;EACbA,eAAA,CAAAA,eAAA,+BAAc;EACdA,eAAA,CAAAA,eAAA,yBAAW;EACXA,eAAA,CAAAA,eAAA,6BAAa;EACbA,eAAA,CAAAA,eAAA,mCAAgB;EAChBA,eAAA,CAAAA,eAAA,+BAAc;EACdA,eAAA,CAAAA,eAAA,+BAAc;EACdA,eAAA,CAAAA,eAAA,mCAAgB;EAChBA,eAAA,CAAAA,eAAA,6BAAa;EACbA,eAAA,CAAAA,eAAA,iCAAe;EACfA,eAAA,CAAAA,eAAA,2BAAY;EACZA,eAAA,CAAAA,eAAA,2BAAY;EACZA,eAAA,CAAAA,eAAA,yBAAW;EACXA,eAAA,CAAAA,eAAA,+BAAc;EACdA,eAAA,CAAAA,eAAA,iCAAe;EACfA,eAAA,CAAAA,eAAA,mDAAwB;EACxBA,eAAA,CAAAA,eAAA,yBAAW;EACXA,eAAA,CAAAA,eAAA,mCAAgB;EAChBA,eAAA,CAAAA,eAAA,iCAAe;EACfA,eAAA,CAAAA,eAAA,+BAAc;EACdA,eAAA,CAAAA,eAAA,6BAAa;EACbA,eAAA,CAAAA,eAAA,+BAAc;AAChB,CAAC,EA1CWA,eAAe,KAAfA,eAAe;AA4C3B;AACA,WAAYC,sBAQX;AARD,WAAYA,sBAAsB;EAChCA,sBAAA,CAAAA,sBAAA,gCAAa;EACbA,sBAAA,CAAAA,sBAAA,gCAAa;EACbA,sBAAA,CAAAA,sBAAA,8BAAY;EACZA,sBAAA,CAAAA,sBAAA,4BAAW;EACXA,sBAAA,CAAAA,sBAAA,8BAAY;EACZA,sBAAA,CAAAA,sBAAA,gDAAqB;EACrBA,sBAAA,CAAAA,sBAAA,kCAAc;AAChB,CAAC,EARWA,sBAAsB,KAAtBA,sBAAsB;AAUlC;AACA,WAAYC,8BAIX;AAJD,WAAYA,8BAA8B;EACxCA,8BAAA,CAAAA,8BAAA,gDAAqB;EACrBA,8BAAA,CAAAA,8BAAA,kDAAsB;EACtBA,8BAAA,CAAAA,8BAAA,0EAAkC;AACpC,CAAC,EAJWA,8BAA8B,KAA9BA,8BAA8B;AAM1C;AACA,WAAYC,aAMX;AAND,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,sBAAQ;EACRA,aAAA,CAAAA,aAAA,0BAAU;EACVA,aAAA,CAAAA,aAAA,kCAAc;EACdA,aAAA,CAAAA,aAAA,sBAAQ;EACRA,aAAA,CAAAA,aAAA,sBAAQ;AACV,CAAC,EANWA,aAAa,KAAbA,aAAa;AAQzB;AACA,WAAYC,wBAMX;AAND,WAAYA,wBAAwB;EAClCA,wBAAA,CAAAA,wBAAA,kCAAc;EACdA,wBAAA,CAAAA,wBAAA,8BAAY;EACZA,wBAAA,CAAAA,wBAAA,4BAAW;EACXA,wBAAA,CAAAA,wBAAA,0BAAU;EACVA,wBAAA,CAAAA,wBAAA,8BAAY;AACd,CAAC,EANWA,wBAAwB,KAAxBA,wBAAwB;AAQpC;AACA,WAAYC,iBAMX;AAND,WAAYA,iBAAiB;EAC3BA,iBAAA,CAAAA,iBAAA,oBAAO;EACPA,iBAAA,CAAAA,iBAAA,8BAAY;EACZA,iBAAA,CAAAA,iBAAA,0BAAU;EACVA,iBAAA,CAAAA,iBAAA,4BAAW;EACXA,iBAAA,CAAAA,iBAAA,wBAAS;AACX,CAAC,EANWA,iBAAiB,KAAjBA,iBAAiB;AAQ7B;AACA,WAAYC,kBAKX;AALD,WAAYA,kBAAkB;EAC5BA,kBAAA,CAAAA,kBAAA,4BAAW;EACXA,kBAAA,CAAAA,kBAAA,4BAAW;EACXA,kBAAA,CAAAA,kBAAA,gCAAa;EACbA,kBAAA,CAAAA,kBAAA,0BAAU;AACZ,CAAC,EALWA,kBAAkB,KAAlBA,kBAAkB;AAO9B;AACA,WAAYC,+BAIX;AAJD,WAAYA,+BAA+B;EACzCA,+BAAA,CAAAA,+BAAA,4BAAW;EACXA,+BAAA,CAAAA,+BAAA,gCAAa;EACbA,+BAAA,CAAAA,+BAAA,4BAAW;AACb,CAAC,EAJWA,+BAA+B,KAA/BA,+BAA+B;AAM3C;AACA,WAAYC,wBAaX;AAbD,WAAYA,wBAAwB;EAClCA,wBAAA,CAAAA,wBAAA,4BAAW;EACXA,wBAAA,CAAAA,wBAAA,gCAAa;EACbA,wBAAA,CAAAA,wBAAA,oDAAuB;EACvBA,wBAAA,CAAAA,wBAAA,kCAAc;EACdA,wBAAA,CAAAA,wBAAA,sBAAQ;EACRA,wBAAA,CAAAA,wBAAA,4BAAW;EACXA,wBAAA,CAAAA,wBAAA,gCAAa;EACbA,wBAAA,CAAAA,wBAAA,8BAAY;EACZA,wBAAA,CAAAA,wBAAA,0CAAkB;EAClBA,wBAAA,CAAAA,wBAAA,wCAAiB;EACjBA,wBAAA,CAAAA,wBAAA,6BAAY;EACZA,wBAAA,CAAAA,wBAAA,+BAAa;AACf,CAAC,EAbWA,wBAAwB,KAAxBA,wBAAwB;AAepC;AACA,WAAYC,iBAIX;AAJD,WAAYA,iBAAiB;EAC3BA,iBAAA,CAAAA,iBAAA,gCAAa;EACbA,iBAAA,CAAAA,iBAAA,4BAAW;EACXA,iBAAA,CAAAA,iBAAA,sCAAgB;AAClB,CAAC,EAJWA,iBAAiB,KAAjBA,iBAAiB;AAM7B;AACA,WAAYC,WAGX;AAHD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,4BAAW;EACXA,WAAA,CAAAA,WAAA,sBAAQ;AACV,CAAC,EAHWA,WAAW,KAAXA,WAAW;AAKvB;AACA,WAAYC,iBAIX;AAJD,WAAYA,iBAAiB;EAC3BA,iBAAA,CAAAA,iBAAA,4BAAW;EACXA,iBAAA,CAAAA,iBAAA,8BAAY;EACZA,iBAAA,CAAAA,iBAAA,gCAAa;AACf,CAAC,EAJWA,iBAAiB,KAAjBA,iBAAiB;AAM7B;AACA,WAAYC,UAGX;AAHD,WAAYA,UAAU;EACpBA,UAAA,CAAAA,UAAA,4BAAW;EACXA,UAAA,CAAAA,UAAA,4BAAW;AACb,CAAC,EAHWA,UAAU,KAAVA,UAAU;AAKtB;AACA,WAAYC,eAGX;AAHD,WAAYA,eAAe;EACzBA,eAAA,CAAAA,eAAA,8BAAY;EACZA,eAAA,CAAAA,eAAA,0BAAU;AACZ,CAAC,EAHWA,eAAe,KAAfA,eAAe;AAK3B;AACA,WAAYC,eAIX;AAJD,WAAYA,eAAe;EACzBA,eAAA,CAAAA,eAAA,wBAAS;EACTA,eAAA,CAAAA,eAAA,4BAAW;EACXA,eAAA,CAAAA,eAAA,8BAAY;AACd,CAAC,EAJWA,eAAe,KAAfA,eAAe;AAM3B;AACA,WAAYC,8BASX;AATD,WAAYA,8BAA8B;EACxCA,8BAAA,CAAAA,8BAAA,oCAAe;EACfA,8BAAA,CAAAA,8BAAA,4BAAW;EACXA,8BAAA,CAAAA,8BAAA,wCAAiB;EACjBA,8BAAA,CAAAA,8BAAA,wDAAyB;EACzBA,8BAAA,CAAAA,8BAAA,sCAAgB;EAChBA,8BAAA,CAAAA,8BAAA,sCAAgB;EAChBA,8BAAA,CAAAA,8BAAA,sBAAQ;EACRA,8BAAA,CAAAA,8BAAA,8BAAY;AACd,CAAC,EATWA,8BAA8B,KAA9BA,8BAA8B;AAW1C;AACA,WAAYC,sBAIX;AAJD,WAAYA,sBAAsB;EAChCA,sBAAA,CAAAA,sBAAA,sBAAQ;EACRA,sBAAA,CAAAA,sBAAA,gCAAa;EACbA,sBAAA,CAAAA,sBAAA,kCAAc;AAChB,CAAC,EAJWA,sBAAsB,KAAtBA,sBAAsB;AAMlC;AACA,WAAYC,sBAIX;AAJD,WAAYA,sBAAsB;EAChCA,sBAAA,CAAAA,sBAAA,oBAAO;EACPA,sBAAA,CAAAA,sBAAA,oCAAe;EACfA,sBAAA,CAAAA,sBAAA,0CAAkB;AACpB,CAAC,EAJWA,sBAAsB,KAAtBA,sBAAsB;AAMlC;AACA,WAAYC,eAIX;AAJD,WAAYA,eAAe;EACzBA,eAAA,CAAAA,eAAA,kBAAM;EACNA,eAAA,CAAAA,eAAA,kBAAM;EACNA,eAAA,CAAAA,eAAA,wBAAS;AACX,CAAC,EAJWA,eAAe,KAAfA,eAAe;AAM3B;AACA,WAAYC,gBAMX;AAND,WAAYA,gBAAgB;EAC1BA,gBAAA,CAAAA,gBAAA,oCAAe;EACfA,gBAAA,CAAAA,gBAAA,8BAAY;EACZA,gBAAA,CAAAA,gBAAA,sDAAwB;EACxBA,gBAAA,CAAAA,gBAAA,kCAAc;EACdA,gBAAA,CAAAA,gBAAA,kCAAc;AAChB,CAAC,EANWA,gBAAgB,KAAhBA,gBAAgB;AAQ5B;AACA,WAAYC,SAKX;AALD,WAAYA,SAAS;EACnBA,SAAA,CAAAA,SAAA,gCAAa;EACbA,SAAA,CAAAA,SAAA,0BAAU;EACVA,SAAA,CAAAA,SAAA,kCAAc;EACdA,SAAA,CAAAA,SAAA,4BAAW;AACb,CAAC,EALWA,SAAS,KAATA,SAAS;AAOrB;AACA,WAAYC,WAGX;AAHD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,4BAAW;EACXA,WAAA,CAAAA,WAAA,0BAAU;AACZ,CAAC,EAHWA,WAAW,KAAXA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}