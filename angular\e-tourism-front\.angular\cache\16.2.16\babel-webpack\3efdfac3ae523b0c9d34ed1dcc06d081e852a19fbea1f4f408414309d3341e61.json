{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Travel<PERSON>T<PERSON><PERSON>, Gender, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../services/booking.service\";\nimport * as i4 from \"../../../services/auth.service\";\nimport * as i5 from \"../../../services/shared-data.service\";\nimport * as i6 from \"../../../services/country.service\";\nimport * as i7 from \"@angular/material/snack-bar\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/form-field\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/datepicker\";\nimport * as i14 from \"@angular/material/button\";\nimport * as i15 from \"@angular/material/icon\";\nimport * as i16 from \"@angular/material/progress-spinner\";\nimport * as i17 from \"@angular/material/expansion\";\nimport * as i18 from \"@angular/material/stepper\";\nimport * as i19 from \"../../shared/country-selector/country-selector.component\";\nfunction BookingTransactionComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate(ctx_r1.transactionId ? \"R\\u00E9servation d\\u00E9marr\\u00E9e\" : \"D\\u00E9marrer la r\\u00E9servation\");\n  }\n}\nfunction BookingTransactionComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.errorMessage);\n  }\n}\nfunction BookingTransactionComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Aucun vol n'a \\u00E9t\\u00E9 s\\u00E9lectionn\\u00E9. Veuillez retourner \\u00E0 la page de recherche et s\\u00E9lectionner un vol.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 52);\n    i0.ɵɵtext(6, \" Retour \\u00E0 la recherche \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BookingTransactionComponent_div_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"i\", 65);\n    i0.ɵɵelementStart(3, \"span\", 66);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 67)(6, \"p\", 68);\n    i0.ɵɵtext(7, \"Votre vol est pr\\u00EAt \\u00E0 \\u00EAtre r\\u00E9serv\\u00E9\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const offerId_r21 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r18.formatOfferId(offerId_r21));\n  }\n}\nfunction BookingTransactionComponent_div_21_mat_spinner_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 69);\n  }\n}\nfunction BookingTransactionComponent_div_21_span_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"D\\u00E9marrer la r\\u00E9servation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 53)(2, \"h3\");\n    i0.ɵɵtext(3, \"R\\u00E9sum\\u00E9 de votre s\\u00E9lection\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 54)(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 55);\n    i0.ɵɵtemplate(8, BookingTransactionComponent_div_21_div_8_Template, 8, 1, \"div\", 56);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"form\", 17);\n    i0.ɵɵlistener(\"ngSubmit\", function BookingTransactionComponent_div_21_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.beginTransaction());\n    });\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"mat-form-field\", 23)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"Devise\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-select\", 57)(15, \"mat-option\", 41);\n    i0.ɵɵtext(16, \"Euro (EUR)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-option\", 42);\n    i0.ɵɵtext(18, \"Dollar am\\u00E9ricain (USD)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-option\", 43);\n    i0.ɵɵtext(20, \"Livre sterling (GBP)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-hint\");\n    i0.ɵɵtext(22, \"Devise utilis\\u00E9e pour la transaction\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"mat-form-field\", 23)(24, \"mat-label\");\n    i0.ɵɵtext(25, \"Culture\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-select\", 58)(27, \"mat-option\", 59);\n    i0.ɵɵtext(28, \"Fran\\u00E7ais (FR)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"mat-option\", 60);\n    i0.ɵɵtext(30, \"Anglais (US)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"mat-option\", 61);\n    i0.ɵɵtext(32, \"Anglais (GB)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"mat-hint\");\n    i0.ɵɵtext(34, \"Langue utilis\\u00E9e pour la transaction\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 29)(36, \"button\", 30);\n    i0.ɵɵtemplate(37, BookingTransactionComponent_div_21_mat_spinner_37_Template, 1, 0, \"mat-spinner\", 31);\n    i0.ɵɵtemplate(38, BookingTransactionComponent_div_21_span_38_Template, 2, 0, \"span\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 62);\n    i0.ɵɵtext(40, \"Annuler\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Offres s\\u00E9lectionn\\u00E9es: \", ctx_r4.offerIds.length, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.offerIds);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.beginTransactionForm);\n    i0.ɵɵadvance(27);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isLoading);\n  }\n}\nfunction BookingTransactionComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"h3\");\n    i0.ɵɵtext(2, \"D\\u00E9tails de la transaction\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 71)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Expire le:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"strong\");\n    i0.ɵɵtext(10, \"Statut:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.formatDate(ctx_r5.beginResponse.body.expiresOn), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.beginResponse.body.status === 1 ? \"Active\" : ctx_r5.beginResponse.body.status || \"N/A\", \"\");\n  }\n}\nfunction BookingTransactionComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Informations voyageurs\");\n  }\n}\nfunction BookingTransactionComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r7.errorMessage);\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_panel_description_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-panel-description\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const traveller_r24 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", (tmp_0_0 = traveller_r24.get(\"name\")) == null ? null : tmp_0_0.value, \" \", (tmp_0_0 = traveller_r24.get(\"surname\")) == null ? null : tmp_0_0.value, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r42 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r42.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r42.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r43 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", title_r43.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", title_r43.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r44 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r44.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r44.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code pays invalide (2 lettres requis) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_option_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const gender_r45 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", gender_r45.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", gender_r45.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Num\\u00E9ro de passeport invalide (minimum 5 caract\\u00E8res) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" La date d'expiration doit \\u00EAtre dans le futur \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" La date d'expiration est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code pays invalide (2 lettres requis) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code pays invalide (2 lettres requis) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_button_186_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function BookingTransactionComponent_mat_expansion_panel_56_button_186_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const i_r25 = i0.ɵɵnextContext().index;\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.removeTraveller(i_r25));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Supprimer ce voyageur \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 72)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, BookingTransactionComponent_mat_expansion_panel_56_mat_panel_description_4_Template, 2, 2, \"mat-panel-description\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 73)(6, \"div\", 20)(7, \"mat-form-field\", 23)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Type de voyageur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-select\", 74);\n    i0.ɵɵtemplate(11, BookingTransactionComponent_mat_expansion_panel_56_mat_option_11_Template, 2, 2, \"mat-option\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-hint\");\n    i0.ɵɵtext(13, \"Veuillez s\\u00E9lectionner un type de voyageur\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 23)(15, \"mat-label\");\n    i0.ɵɵtext(16, \"Titre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-select\", 76);\n    i0.ɵɵtemplate(18, BookingTransactionComponent_mat_expansion_panel_56_mat_option_18_Template, 2, 2, \"mat-option\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-hint\");\n    i0.ɵɵtext(20, \"M., Mme, etc.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-form-field\", 23)(22, \"mat-label\");\n    i0.ɵɵtext(23, \"Type de passager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-select\", 77);\n    i0.ɵɵtemplate(25, BookingTransactionComponent_mat_expansion_panel_56_mat_option_25_Template, 2, 2, \"mat-option\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-hint\");\n    i0.ɵɵtext(27, \"Adulte, Enfant, etc.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 20)(29, \"mat-form-field\", 23)(30, \"mat-label\");\n    i0.ɵɵtext(31, \"Pr\\u00E9nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"input\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-form-field\", 23)(34, \"mat-label\");\n    i0.ɵɵtext(35, \"Nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"input\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"mat-form-field\", 23)(38, \"mat-label\");\n    i0.ɵɵtext(39, \"Date de naissance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(40, \"input\", 80)(41, \"mat-datepicker-toggle\", 46)(42, \"mat-datepicker\", null, 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 82)(45, \"h4\");\n    i0.ɵɵtext(46, \"Nationalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"app-country-selector\", 83);\n    i0.ɵɵlistener(\"countrySelected\", function BookingTransactionComponent_mat_expansion_panel_56_Template_app_country_selector_countrySelected_47_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r50);\n      const i_r25 = restoredCtx.index;\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.onCountrySelected($event, i_r25, \"nationality\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"mat-form-field\", 23)(49, \"mat-label\");\n    i0.ɵɵtext(50, \"Code pays\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"input\", 84);\n    i0.ɵɵelementStart(52, \"mat-hint\");\n    i0.ɵɵtext(53, \"S\\u00E9lectionnez un pays ci-dessus ou entrez le code \\u00E0 2 lettres (ex: FR, US, GB)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, BookingTransactionComponent_mat_expansion_panel_56_mat_error_54_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 20)(56, \"mat-form-field\", 23)(57, \"mat-label\");\n    i0.ɵɵtext(58, \"Genre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"mat-select\", 85);\n    i0.ɵɵtemplate(60, BookingTransactionComponent_mat_expansion_panel_56_mat_option_60_Template, 2, 2, \"mat-option\", 75);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"mat-form-field\", 23)(62, \"mat-label\");\n    i0.ɵɵtext(63, \"Num\\u00E9ro d'identit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(64, \"input\", 86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 18)(66, \"h4\");\n    i0.ɵɵtext(67, \"Informations de passeport\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"div\", 87)(69, \"div\", 20)(70, \"mat-form-field\", 23)(71, \"mat-label\");\n    i0.ɵɵtext(72, \"Num\\u00E9ro de s\\u00E9rie\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(73, \"input\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"mat-form-field\", 23)(75, \"mat-label\");\n    i0.ɵɵtext(76, \"Num\\u00E9ro de passeport\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(77, \"input\", 89);\n    i0.ɵɵelementStart(78, \"mat-hint\");\n    i0.ɵɵtext(79, \"Minimum 5 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(80, BookingTransactionComponent_mat_expansion_panel_56_mat_error_80_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(81, \"div\", 20)(82, \"mat-form-field\", 23)(83, \"mat-label\");\n    i0.ɵɵtext(84, \"Date d'expiration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(85, \"input\", 90)(86, \"mat-datepicker-toggle\", 46)(87, \"mat-datepicker\", null, 91);\n    i0.ɵɵelementStart(89, \"mat-hint\");\n    i0.ɵɵtext(90, \"Doit \\u00EAtre dans le futur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(91, BookingTransactionComponent_mat_expansion_panel_56_mat_error_91_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵtemplate(92, BookingTransactionComponent_mat_expansion_panel_56_mat_error_92_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"mat-form-field\", 23)(94, \"mat-label\");\n    i0.ɵɵtext(95, \"Date d'\\u00E9mission\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(96, \"input\", 92)(97, \"mat-datepicker-toggle\", 46)(98, \"mat-datepicker\", null, 93);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(100, \"div\", 20)(101, \"div\", 94)(102, \"h5\");\n    i0.ɵɵtext(103, \"Pays de citoyennet\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"app-country-selector\", 83);\n    i0.ɵɵlistener(\"countrySelected\", function BookingTransactionComponent_mat_expansion_panel_56_Template_app_country_selector_countrySelected_104_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r50);\n      const i_r25 = restoredCtx.index;\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.onCountrySelected($event, i_r25, \"citizenshipCountryCode\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"mat-form-field\", 23)(106, \"mat-label\");\n    i0.ɵɵtext(107, \"Code pays de citoyennet\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(108, \"input\", 95);\n    i0.ɵɵelementStart(109, \"mat-hint\");\n    i0.ɵɵtext(110, \"S\\u00E9lectionnez un pays ci-dessus ou entrez le code \\u00E0 2 lettres (ex: FR, US, GB)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(111, BookingTransactionComponent_mat_expansion_panel_56_mat_error_111_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(112, \"div\", 94)(113, \"h5\");\n    i0.ɵɵtext(114, \"Pays d'\\u00E9mission du passeport\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"app-country-selector\", 83);\n    i0.ɵɵlistener(\"countrySelected\", function BookingTransactionComponent_mat_expansion_panel_56_Template_app_country_selector_countrySelected_115_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r50);\n      const i_r25 = restoredCtx.index;\n      const ctx_r52 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r52.onCountrySelected($event, i_r25, \"issueCountryCode\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(116, \"mat-form-field\", 23)(117, \"mat-label\");\n    i0.ɵɵtext(118, \"Code pays d'\\u00E9mission\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(119, \"input\", 96);\n    i0.ɵɵelementStart(120, \"mat-hint\");\n    i0.ɵɵtext(121, \"S\\u00E9lectionnez un pays ci-dessus ou entrez le code \\u00E0 2 lettres (ex: FR, US, GB)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(122, BookingTransactionComponent_mat_expansion_panel_56_mat_error_122_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(123, \"div\", 18)(124, \"h4\");\n    i0.ɵɵtext(125, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(126, \"div\", 97)(127, \"div\", 20)(128, \"div\", 98)(129, \"mat-form-field\", 23)(130, \"mat-label\");\n    i0.ɵɵtext(131, \"Code pays\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(132, \"input\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(133, \"mat-form-field\", 23)(134, \"mat-label\");\n    i0.ɵɵtext(135, \"Indicatif r\\u00E9gional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(136, \"input\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(137, \"mat-form-field\", 23)(138, \"mat-label\");\n    i0.ɵɵtext(139, \"Num\\u00E9ro de t\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(140, \"input\", 101);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(141, \"div\", 20)(142, \"mat-form-field\", 21)(143, \"mat-label\");\n    i0.ɵɵtext(144, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(145, \"input\", 102);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(146, \"div\", 20)(147, \"mat-form-field\", 21)(148, \"mat-label\");\n    i0.ɵɵtext(149, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(150, \"input\", 103);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(151, \"div\", 20)(152, \"mat-form-field\", 23)(153, \"mat-label\");\n    i0.ɵɵtext(154, \"Code postal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(155, \"input\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(156, \"div\", 105)(157, \"mat-form-field\", 23)(158, \"mat-label\");\n    i0.ɵɵtext(159, \"ID de ville\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(160, \"input\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(161, \"mat-form-field\", 23)(162, \"mat-label\");\n    i0.ɵɵtext(163, \"Nom de ville\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(164, \"input\", 78);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(165, \"div\", 20)(166, \"div\", 107)(167, \"h5\");\n    i0.ɵɵtext(168, \"Pays de r\\u00E9sidence\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(169, \"app-country-selector\", 83);\n    i0.ɵɵlistener(\"countrySelected\", function BookingTransactionComponent_mat_expansion_panel_56_Template_app_country_selector_countrySelected_169_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r50);\n      const i_r25 = restoredCtx.index;\n      const ctx_r53 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r53.onCountrySelected($event, i_r25, \"country\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(170, \"div\", 20)(171, \"mat-form-field\", 23)(172, \"mat-label\");\n    i0.ɵɵtext(173, \"Code pays\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(174, \"input\", 108);\n    i0.ɵɵelementStart(175, \"mat-hint\");\n    i0.ɵɵtext(176, \"Code pays \\u00E0 2 lettres (ex: FR, US, GB)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(177, \"mat-form-field\", 23)(178, \"mat-label\");\n    i0.ɵɵtext(179, \"Nom de pays\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(180, \"input\", 109);\n    i0.ɵɵelementStart(181, \"mat-hint\");\n    i0.ɵɵtext(182, \"Nom complet du pays (ex: France, \\u00C9tats-Unis)\");\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(183, \"div\", 110)(184, \"mat-checkbox\", 111);\n    i0.ɵɵtext(185, \"Chef de groupe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(186, BookingTransactionComponent_mat_expansion_panel_56_button_186_Template, 4, 0, \"button\", 112);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const traveller_r24 = ctx.$implicit;\n    const i_r25 = ctx.index;\n    const _r30 = i0.ɵɵreference(43);\n    const _r34 = i0.ɵɵreference(88);\n    const _r37 = i0.ɵɵreference(99);\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_12_0;\n    let tmp_14_0;\n    let tmp_17_0;\n    let tmp_18_0;\n    let tmp_24_0;\n    let tmp_28_0;\n    i0.ɵɵproperty(\"expanded\", i_r25 === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Voyageur \", i_r25 + 1, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = traveller_r24.get(\"name\")) == null ? null : tmp_2_0.value) || ((tmp_2_0 = traveller_r24.get(\"surname\")) == null ? null : tmp_2_0.value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroupName\", i_r25);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.passengerTypes);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.travellerTitles);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.passengerTypes);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"matDatepicker\", _r30);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r30);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"label\", \"S\\u00E9lectionnez votre nationalit\\u00E9\")(\"placeholder\", \"Rechercher un pays\")(\"required\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_12_0 = traveller_r24.get(\"nationality.twoLetterCode\")) == null ? null : tmp_12_0.invalid);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.genderOptions);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngIf\", (tmp_14_0 = traveller_r24.get(\"passportInfo.number\")) == null ? null : tmp_14_0.invalid);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"matDatepicker\", _r34);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r34);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_17_0 = traveller_r24.get(\"passportInfo.expireDate\")) == null ? null : tmp_17_0.errors == null ? null : tmp_17_0.errors[\"expireDateInvalid\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_18_0 = traveller_r24.get(\"passportInfo.expireDate\")) == null ? null : tmp_18_0.errors == null ? null : tmp_18_0.errors[\"required\"]);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"matDatepicker\", _r37);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r37);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"label\", \"Pays de citoyennet\\u00E9\")(\"placeholder\", \"Rechercher un pays\")(\"required\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_24_0 = traveller_r24.get(\"passportInfo.citizenshipCountryCode\")) == null ? null : tmp_24_0.invalid);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"label\", \"Pays d'\\u00E9mission\")(\"placeholder\", \"Rechercher un pays\")(\"required\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_28_0 = traveller_r24.get(\"passportInfo.issueCountryCode\")) == null ? null : tmp_28_0.invalid);\n    i0.ɵɵadvance(47);\n    i0.ɵɵproperty(\"label\", \"Pays de r\\u00E9sidence\")(\"placeholder\", \"Rechercher un pays\")(\"required\", true);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.travellers.length > 1);\n  }\n}\nfunction BookingTransactionComponent_mat_spinner_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 69);\n  }\n}\nfunction BookingTransactionComponent_span_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Enregistrer les informations\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"h3\");\n    i0.ɵɵtext(2, \"Informations de r\\u00E9servation enregistr\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 114)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Nombre de voyageurs:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.infoResponse.body.reservationData && ctx_r11.infoResponse.body.reservationData.travellers ? ctx_r11.infoResponse.body.reservationData.travellers.length : 0, \"\");\n  }\n}\nfunction BookingTransactionComponent_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Paiement et confirmation\");\n  }\n}\nfunction BookingTransactionComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r13.errorMessage);\n  }\n}\nfunction BookingTransactionComponent_mat_spinner_144_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 69);\n  }\n}\nfunction BookingTransactionComponent_span_145_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Finaliser la r\\u00E9servation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_div_148_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"h3\");\n    i0.ɵɵtext(2, \"R\\u00E9servation confirm\\u00E9e!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 116)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Num\\u00E9ro de r\\u00E9servation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 117)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12, \"Votre r\\u00E9servation a \\u00E9t\\u00E9 confirm\\u00E9e avec succ\\u00E8s. Vous recevrez bient\\u00F4t un email de confirmation avec tous les d\\u00E9tails de votre voyage.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 118)(14, \"button\", 52);\n    i0.ɵɵtext(15, \" Retour \\u00E0 la recherche \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.commitResponse.body.reservationNumber || \"N/A\", \"\");\n  }\n}\nexport let BookingTransactionComponent = /*#__PURE__*/(() => {\n  class BookingTransactionComponent {\n    constructor(fb, route, router, bookingService, authService, sharedDataService, countryService, snackBar) {\n      this.fb = fb;\n      this.route = route;\n      this.router = router;\n      this.bookingService = bookingService;\n      this.authService = authService;\n      this.sharedDataService = sharedDataService;\n      this.countryService = countryService;\n      this.snackBar = snackBar;\n      // Étape actuelle du processus de réservation\n      this.currentStep = 1;\n      // Données de transaction\n      this.transactionId = '';\n      this.offerIds = [];\n      this.searchId = '';\n      this.passengersParam = '';\n      // Informations de passagers\n      this.passengerCounts = {};\n      // Réponses des différentes étapes\n      this.beginResponse = null;\n      this.infoResponse = null;\n      this.commitResponse = null;\n      // États de chargement et d'erreur\n      this.isLoading = false;\n      this.errorMessage = '';\n      // Options pour les formulaires\n      this.travellerTitles = Object.keys(TravellerTitle).filter(key => !isNaN(Number(TravellerTitle[key]))).map(key => ({\n        value: TravellerTitle[key],\n        label: key\n      }));\n      this.genderOptions = Object.keys(Gender).filter(key => !isNaN(Number(Gender[key]))).map(key => ({\n        value: Gender[key],\n        label: key\n      }));\n      this.passengerTypes = Object.keys(PassengerType).filter(key => !isNaN(Number(PassengerType[key]))).map(key => ({\n        value: PassengerType[key],\n        label: key\n      }));\n      // Liste des pays pour le sélecteur\n      this.countries = [];\n      // Initialisation des formulaires sans valeurs par défaut\n      this.beginTransactionForm = this.fb.group({\n        currency: ['', Validators.required],\n        culture: ['', Validators.required]\n      });\n      this.reservationInfoForm = this.fb.group({\n        transactionId: ['', Validators.required],\n        travellers: this.fb.array([]),\n        reservationNote: [''],\n        agencyReservationNumber: ['']\n      });\n      this.commitTransactionForm = this.fb.group({\n        transactionId: ['', Validators.required],\n        paymentOption: [null],\n        paymentInformation: this.fb.group({\n          accountName: [''],\n          paymentTypeId: [null],\n          paymentPrice: this.fb.group({\n            amount: [null, Validators.required],\n            currency: ['', Validators.required]\n          }),\n          installmentCount: [''],\n          paymentDate: [''],\n          receiptType: [''],\n          reference: [''],\n          paymentToken: ['']\n        })\n      });\n    }\n    ngOnInit() {\n      // Charger la liste des pays\n      this.countryService.getCountries().subscribe(countries => {\n        this.countries = countries;\n      });\n      // Récupérer les offerIds, transactionId et informations de passagers depuis les paramètres de l'URL\n      this.route.queryParams.subscribe(params => {\n        // Récupérer les offerIds\n        if (params['offerIds']) {\n          try {\n            this.offerIds = Array.isArray(params['offerIds']) ? params['offerIds'] : [params['offerIds']];\n            if (this.offerIds.length > 0) {\n              console.log('OfferIds récupérés:', this.offerIds);\n            } else {\n              this.errorMessage = 'Aucun ID d\\'offre n\\'a été fourni.';\n            }\n          } catch (error) {\n            console.error('Erreur lors de la récupération des offerIds:', error);\n            this.errorMessage = 'Format d\\'ID d\\'offre invalide.';\n          }\n        } else {\n          this.errorMessage = 'Aucun ID d\\'offre n\\'a été fourni.';\n        }\n        // Récupérer le searchId s'il est présent\n        if (params['searchId']) {\n          this.searchId = params['searchId'];\n          console.log('SearchId récupéré:', this.searchId);\n        }\n        // Récupérer les informations de passagers\n        if (params['passengers']) {\n          this.passengersParam = params['passengers'];\n          try {\n            this.passengerCounts = JSON.parse(this.passengersParam);\n            // Mettre à jour le service partagé avec les informations de passagers\n            this.sharedDataService.setPassengerCounts(this.passengerCounts);\n            console.log('Passenger counts parsed successfully:', this.passengerCounts);\n          } catch (error) {\n            console.error('Error parsing passenger information:', error);\n            // Utiliser les valeurs par défaut du service\n            this.passengerCounts = this.sharedDataService.getPassengerCounts();\n          }\n        } else {\n          // Utiliser les valeurs par défaut du service\n          this.passengerCounts = this.sharedDataService.getPassengerCounts();\n        }\n        // Vérifier si une transaction a déjà été démarrée automatiquement\n        if (params['transactionId'] && params['autoStarted'] === 'true') {\n          console.log('Transaction déjà démarrée avec ID:', params['transactionId']);\n          this.transactionId = params['transactionId'];\n          // Mettre à jour le formulaire d'informations de réservation avec l'ID de transaction\n          this.reservationInfoForm.patchValue({\n            transactionId: this.transactionId\n          });\n          // Créer un objet de réponse simulé pour l'étape 1\n          this.beginResponse = {\n            header: {\n              requestId: '',\n              success: true,\n              responseTime: new Date().toISOString(),\n              messages: []\n            },\n            body: {\n              transactionId: this.transactionId,\n              expiresOn: new Date(Date.now() + 3600000).toISOString(),\n              status: 1,\n              transactionType: 1,\n              reservationData: {\n                travellers: [],\n                reservationInfo: {\n                  bookingNumber: '',\n                  agency: {\n                    code: '',\n                    name: '',\n                    country: {\n                      id: '',\n                      name: ''\n                    },\n                    address: {},\n                    ownAgency: false,\n                    aceExport: false\n                  },\n                  agencyUser: {\n                    id: 0,\n                    name: '',\n                    surname: '',\n                    email: ''\n                  },\n                  beginDate: new Date().toISOString(),\n                  endDate: new Date().toISOString(),\n                  note: '',\n                  salePrice: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  supplementDiscount: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  passengerEB: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  agencyEB: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  passengerAmountToPay: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  agencyAmountToPay: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  discount: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  agencyBalance: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  passengerBalance: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  agencyCommission: {\n                    amount: 0,\n                    currency: 'EUR',\n                    rate: 0\n                  },\n                  brokerCommission: {\n                    amount: 0,\n                    currency: 'EUR',\n                    rate: 0\n                  },\n                  agencySupplementCommission: {\n                    amount: 0,\n                    currency: 'EUR',\n                    rate: 0\n                  },\n                  promotionAmount: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  priceToPay: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  agencyPriceToPay: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  passengerPriceToPay: {\n                    amount: 0,\n                    currency: 'EUR'\n                  },\n                  totalPrice: {\n                    amount: 0,\n                    currency: 'EUR'\n                  }\n                },\n                services: [],\n                paymentDetail: {},\n                invoices: []\n              }\n            }\n          };\n          // Créer dynamiquement les voyageurs en fonction des informations de passagers\n          this.createTravellersFromPassengerCounts();\n          // Passer directement à l'étape 2\n          this.currentStep = 2;\n          // Afficher un message d'information\n          this.snackBar.open('Transaction déjà démarrée. Veuillez compléter les informations des voyageurs.', 'Fermer', {\n            duration: 5000,\n            panelClass: ['info-snackbar']\n          });\n        }\n      });\n    }\n    // Créer dynamiquement les voyageurs en fonction des informations de passagers\n    createTravellersFromPassengerCounts() {\n      // Vider le tableau de voyageurs existant\n      while (this.travellers.length > 0) {\n        this.travellers.removeAt(0);\n      }\n      // Parcourir les types de passagers et créer le nombre correspondant de voyageurs\n      Object.entries(this.passengerCounts).forEach(([typeStr, count]) => {\n        const type = parseInt(typeStr);\n        for (let i = 0; i < count; i++) {\n          // Créer un voyageur avec le type de passager correspondant\n          const travellerForm = this.fb.group({\n            type: [type, Validators.required],\n            title: [null, Validators.required],\n            passengerType: [type, Validators.required],\n            name: ['', [Validators.required, Validators.minLength(2)]],\n            surname: ['', [Validators.required, Validators.minLength(2)]],\n            isLeader: [this.travellers?.length === 0],\n            birthDate: ['', Validators.required],\n            nationality: this.fb.group({\n              twoLetterCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n            }),\n            identityNumber: [''],\n            passportInfo: this.fb.group({\n              serial: [''],\n              number: ['', [Validators.required, Validators.minLength(5)]],\n              expireDate: ['', Validators.required],\n              issueDate: ['', Validators.required],\n              citizenshipCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n              issueCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n            }),\n            address: this.fb.group({\n              contactPhone: this.fb.group({\n                countryCode: ['', Validators.required],\n                areaCode: [''],\n                phoneNumber: ['', [Validators.required, Validators.minLength(5)]]\n              }),\n              email: ['', [Validators.required, Validators.email]],\n              address: ['', [Validators.required, Validators.minLength(5)]],\n              zipCode: ['', [Validators.required, Validators.minLength(3)]],\n              city: this.fb.group({\n                id: ['', Validators.required],\n                name: ['', [Validators.required, Validators.minLength(2)]]\n              }),\n              country: this.fb.group({\n                id: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n                name: ['', [Validators.required, Validators.minLength(2)]]\n              })\n            }),\n            gender: [null, Validators.required]\n          });\n          // Ajouter des validateurs personnalisés\n          this.addCustomValidators(travellerForm);\n          // Ajouter le voyageur au formulaire\n          this.travellers.push(travellerForm);\n        }\n      });\n      // S'assurer qu'il y a au moins un voyageur\n      if (this.travellers.length === 0) {\n        this.addTraveller();\n      }\n    }\n    // Getter pour accéder au FormArray des voyageurs\n    get travellers() {\n      return this.reservationInfoForm.get('travellers');\n    }\n    // Ajouter un nouveau voyageur au formulaire\n    addTraveller() {\n      const travellerForm = this.fb.group({\n        type: [null, Validators.required],\n        title: [null, Validators.required],\n        passengerType: [null, Validators.required],\n        name: ['', [Validators.required, Validators.minLength(2)]],\n        surname: ['', [Validators.required, Validators.minLength(2)]],\n        isLeader: [this.travellers?.length === 0],\n        birthDate: ['', Validators.required],\n        nationality: this.fb.group({\n          twoLetterCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n        }),\n        identityNumber: [''],\n        passportInfo: this.fb.group({\n          serial: [''],\n          number: ['', [Validators.required, Validators.minLength(5)]],\n          expireDate: ['', Validators.required],\n          issueDate: ['', Validators.required],\n          citizenshipCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n          issueCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n        }),\n        address: this.fb.group({\n          contactPhone: this.fb.group({\n            countryCode: ['', Validators.required],\n            areaCode: [''],\n            phoneNumber: ['', [Validators.required, Validators.minLength(5)]]\n          }),\n          email: ['', [Validators.required, Validators.email]],\n          address: ['', [Validators.required, Validators.minLength(5)]],\n          zipCode: ['', [Validators.required, Validators.minLength(3)]],\n          city: this.fb.group({\n            id: ['', Validators.required],\n            name: ['', [Validators.required, Validators.minLength(2)]]\n          }),\n          country: this.fb.group({\n            id: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n            name: ['', [Validators.required, Validators.minLength(2)]]\n          })\n        }),\n        gender: [null, Validators.required] // Genre\n      });\n      // Ajouter des validateurs personnalisés\n      this.addCustomValidators(travellerForm);\n      this.travellers.push(travellerForm);\n    }\n    // Ajouter des validateurs personnalisés au formulaire de voyageur\n    addCustomValidators(form) {\n      // Ne pas synchroniser automatiquement issueCountryCode avec la nationalité\n      // pour permettre une saisie entièrement dynamique\n      const nationalityControl = form.get('nationality.twoLetterCode');\n      const issueCountryCodeControl = form.get('passportInfo.issueCountryCode');\n      // Nous ne synchronisons plus automatiquement les valeurs\n      // L'utilisateur doit remplir tous les champs manuellement\n      // Vérifier que la date d'expiration du passeport est dans le futur\n      const expireDateControl = form.get('passportInfo.expireDate');\n      if (expireDateControl) {\n        expireDateControl.setValidators([Validators.required, control => {\n          const value = control.value;\n          if (!value) return null;\n          const expireDate = new Date(value);\n          const today = new Date();\n          return expireDate > today ? null : {\n            'expireDateInvalid': true\n          };\n        }]);\n      }\n    }\n    // Supprimer un voyageur du formulaire\n    removeTraveller(index) {\n      this.travellers.removeAt(index);\n    }\n    // Démarrer la transaction de réservation\n    beginTransaction() {\n      if (this.beginTransactionForm.invalid) {\n        this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      const formValue = this.beginTransactionForm.value;\n      this.bookingService.beginTransaction(this.offerIds, formValue.currency, formValue.culture).subscribe({\n        next: response => {\n          this.isLoading = false;\n          console.log('Réponse reçue:', response);\n          if (response && response.beginResponse && response.beginResponse.body) {\n            this.beginResponse = response.beginResponse;\n            this.transactionId = response.beginResponse.body.transactionId || '';\n            // Mettre à jour le formulaire d'informations de réservation avec l'ID de transaction\n            this.reservationInfoForm.patchValue({\n              transactionId: this.transactionId\n            });\n            // Ajouter les voyageurs existants s'il y en a dans la réponse\n            if (response.beginResponse.body.reservationData && response.beginResponse.body.reservationData.travellers && response.beginResponse.body.reservationData.travellers.length > 0) {\n              // Vider le tableau de voyageurs existant\n              while (this.travellers.length > 0) {\n                this.travellers.removeAt(0);\n              }\n              // Ajouter les voyageurs de la réponse\n              response.beginResponse.body.reservationData.travellers.forEach(traveller => {\n                this.addTravellerFromResponse(traveller);\n              });\n            } else {\n              // Créer dynamiquement les voyageurs en fonction des informations de passagers\n              this.createTravellersFromPassengerCounts();\n            }\n            // Passer à l'étape suivante\n            this.currentStep = 2;\n            this.snackBar.open('Transaction démarrée avec succès!', 'Fermer', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n          } else {\n            this.errorMessage = 'Réponse de transaction invalide.';\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          console.error('Erreur lors du démarrage de la transaction:', error);\n          this.errorMessage = error.message || 'Une erreur est survenue lors du démarrage de la transaction.';\n          this.snackBar.open(this.errorMessage, 'Fermer', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n    // Ajouter un voyageur à partir de la réponse de l'API\n    addTravellerFromResponse(traveller) {\n      // Utiliser uniquement les données fournies par l'API, sans valeurs par défaut\n      const travellerForm = this.fb.group({\n        travellerId: [traveller.travellerId || ''],\n        type: [traveller.type, Validators.required],\n        title: [traveller.title, Validators.required],\n        passengerType: [traveller.passengerType, Validators.required],\n        name: [traveller.name || '', [Validators.required, Validators.minLength(2)]],\n        surname: [traveller.surname || '', [Validators.required, Validators.minLength(2)]],\n        isLeader: [traveller.isLeader !== undefined ? traveller.isLeader : this.travellers?.length === 0],\n        birthDate: [traveller.birthDate || '', Validators.required],\n        nationality: this.fb.group({\n          twoLetterCode: [traveller.nationality?.twoLetterCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n        }),\n        identityNumber: [traveller.identityNumber || ''],\n        passportInfo: this.fb.group({\n          serial: [traveller.passportInfo?.serial || ''],\n          number: [traveller.passportInfo?.number || '', [Validators.required, Validators.minLength(5)]],\n          expireDate: [traveller.passportInfo?.expireDate || '', Validators.required],\n          issueDate: [traveller.passportInfo?.issueDate || '', Validators.required],\n          citizenshipCountryCode: [traveller.passportInfo?.citizenshipCountryCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n          issueCountryCode: [traveller.passportInfo?.issueCountryCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n        }),\n        address: this.fb.group({\n          contactPhone: this.fb.group({\n            countryCode: [traveller.address?.contactPhone?.countryCode || '', Validators.required],\n            areaCode: [''],\n            phoneNumber: [traveller.address?.contactPhone?.phoneNumber || '', [Validators.required, Validators.minLength(5)]]\n          }),\n          email: [traveller.address?.email || '', [Validators.required, Validators.email]],\n          address: [traveller.address?.address || '', [Validators.required, Validators.minLength(5)]],\n          zipCode: [traveller.address?.zipCode || '', [Validators.required, Validators.minLength(3)]],\n          city: this.fb.group({\n            id: [traveller.address?.city?.id || '', Validators.required],\n            name: [traveller.address?.city?.name || '', [Validators.required, Validators.minLength(2)]]\n          }),\n          country: this.fb.group({\n            id: [traveller.address?.country?.id || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n            name: [traveller.address?.country?.name || '', [Validators.required, Validators.minLength(2)]]\n          })\n        }),\n        gender: [traveller.gender, Validators.required]\n      });\n      // Ajouter des validateurs personnalisés\n      this.addCustomValidators(travellerForm);\n      this.travellers.push(travellerForm);\n    }\n    // Définir les informations de réservation\n    setReservationInfo() {\n      if (this.reservationInfoForm.invalid) {\n        // Vérifier les erreurs spécifiques pour donner des messages plus précis\n        let errorMessage = 'Veuillez remplir tous les champs obligatoires.';\n        // Vérifier les erreurs de passeport\n        const travellers = this.travellers.controls;\n        for (let i = 0; i < travellers.length; i++) {\n          const traveller = travellers[i];\n          // Vérifier les erreurs de date d'expiration du passeport\n          const expireDateControl = traveller.get('passportInfo.expireDate');\n          if (expireDateControl?.errors?.['expireDateInvalid']) {\n            errorMessage = `Voyageur ${i + 1}: La date d'expiration du passeport doit être dans le futur.`;\n            break;\n          }\n          // Vérifier les erreurs de code pays\n          const issueCountryCodeControl = traveller.get('passportInfo.issueCountryCode');\n          if (issueCountryCodeControl?.invalid && issueCountryCodeControl?.touched) {\n            errorMessage = `Voyageur ${i + 1}: Le code pays d'émission du passeport est invalide.`;\n            break;\n          }\n          // Vérifier les erreurs de numéro de passeport\n          const passportNumberControl = traveller.get('passportInfo.number');\n          if (passportNumberControl?.invalid && passportNumberControl?.touched) {\n            errorMessage = `Voyageur ${i + 1}: Le numéro de passeport est invalide.`;\n            break;\n          }\n        }\n        this.snackBar.open(errorMessage, 'Fermer', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      const formValue = this.reservationInfoForm.value;\n      // Formater correctement les dates pour chaque voyageur\n      const formattedTravellers = formValue.travellers.map(traveller => {\n        // Créer un nouvel objet pour éviter de modifier l'original\n        const formattedTraveller = {\n          ...traveller\n        };\n        // Formater la date de naissance\n        if (formattedTraveller.birthDate) {\n          // Convertir en Date si c'est un objet Date ou une chaîne\n          const birthDate = new Date(formattedTraveller.birthDate);\n          if (!isNaN(birthDate.getTime())) {\n            formattedTraveller.birthDate = this.formatDateForApi(birthDate);\n          }\n        }\n        // Formater les dates de passeport si présentes\n        if (formattedTraveller.passportInfo) {\n          const passportInfo = {\n            ...formattedTraveller.passportInfo\n          };\n          if (passportInfo.expireDate) {\n            const expireDate = new Date(passportInfo.expireDate);\n            if (!isNaN(expireDate.getTime())) {\n              passportInfo.expireDate = this.formatDateForApi(expireDate);\n            }\n          }\n          if (passportInfo.issueDate) {\n            const issueDate = new Date(passportInfo.issueDate);\n            if (!isNaN(issueDate.getTime())) {\n              passportInfo.issueDate = this.formatDateForApi(issueDate);\n            }\n          }\n          formattedTraveller.passportInfo = passportInfo;\n        }\n        return formattedTraveller;\n      });\n      // Vérifier si tous les passagers sont des enfants ou des bébés\n      const hasAdult = formattedTravellers.some(traveller => traveller.passengerType === PassengerType.Adult);\n      // Créer la requête d'informations de réservation\n      // Ne pas inclure customerInfo si tous les passagers sont des enfants ou des bébés\n      const request = {\n        transactionId: formValue.transactionId,\n        travellers: formattedTravellers,\n        reservationNote: formValue.reservationNote,\n        agencyReservationNumber: formValue.agencyReservationNumber\n      };\n      // Afficher un message de log pour indiquer si customerInfo est inclus ou non\n      console.log('Requête setReservationInfo - Présence d\\'adultes:', hasAdult);\n      console.log('Requête setReservationInfo - customerInfo non inclus car tous les passagers sont des enfants ou des bébés');\n      this.bookingService.setReservationInfo(request).subscribe({\n        next: response => {\n          this.isLoading = false;\n          console.log('Réponse setReservationInfo reçue:', response);\n          if (response && response.infoResponse && response.infoResponse.body) {\n            this.infoResponse = response.infoResponse;\n            // Mettre à jour le formulaire de finalisation de transaction avec l'ID de transaction\n            this.commitTransactionForm.patchValue({\n              transactionId: this.transactionId\n            });\n            // Mettre à jour le montant du paiement si disponible\n            if (response.infoResponse.body.reservationData && response.infoResponse.body.reservationData.reservationInfo && response.infoResponse.body.reservationData.reservationInfo.priceToPay) {\n              const priceToPay = response.infoResponse.body.reservationData.reservationInfo.priceToPay;\n              this.commitTransactionForm.get('paymentInformation.paymentPrice')?.patchValue({\n                amount: priceToPay.amount,\n                currency: priceToPay.currency\n              });\n            }\n            // Passer à l'étape suivante\n            this.currentStep = 3;\n            this.snackBar.open('Informations de réservation définies avec succès!', 'Fermer', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n          } else {\n            this.errorMessage = 'Réponse d\\'informations de réservation invalide.';\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          console.error('Erreur lors de la définition des informations de réservation:', error);\n          // Analyser le message d'erreur pour des problèmes spécifiques\n          let errorMessage = error.message || 'Une erreur est survenue lors de la définition des informations de réservation.';\n          // Vérifier les erreurs spécifiques liées au passeport\n          if (errorMessage.includes('passport') || errorMessage.includes('issueCountryCode')) {\n            errorMessage = 'Erreur de validation du passeport: Veuillez vérifier que toutes les informations de passeport sont complètes, notamment le code pays d\\'émission.';\n          }\n          // Vérifier les erreurs liées aux dates\n          else if (errorMessage.includes('date') || errorMessage.includes('expireDate')) {\n            errorMessage = 'Erreur de validation des dates: Veuillez vérifier que toutes les dates sont au format correct (YYYY-MM-DD).';\n          }\n          // Vérifier les erreurs liées aux informations personnelles\n          else if (errorMessage.includes('name') || errorMessage.includes('surname')) {\n            errorMessage = 'Erreur de validation des informations personnelles: Veuillez vérifier que tous les noms et prénoms sont correctement renseignés.';\n          }\n          this.errorMessage = errorMessage;\n          this.snackBar.open(this.errorMessage, 'Fermer', {\n            duration: 8000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n    // Finaliser la transaction de réservation\n    commitTransaction() {\n      if (this.commitTransactionForm.invalid) {\n        this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      const formValue = this.commitTransactionForm.value;\n      this.bookingService.commitTransaction(formValue).subscribe({\n        next: response => {\n          this.isLoading = false;\n          console.log('Réponse commitTransaction reçue:', response);\n          if (response && response.commitResponse && response.commitResponse.body) {\n            this.commitResponse = response.commitResponse;\n            this.snackBar.open('Réservation finalisée avec succès!', 'Fermer', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            // Afficher les détails de la réservation finalisée\n            // Vous pourriez rediriger vers une page de confirmation ici\n          } else {\n            this.errorMessage = 'Réponse de finalisation de transaction invalide.';\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          console.error('Erreur lors de la finalisation de la transaction:', error);\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la finalisation de la transaction.';\n          this.snackBar.open(this.errorMessage, 'Fermer', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      });\n    }\n    // Revenir à l'étape précédente\n    previousStep() {\n      // Si la transaction a été démarrée automatiquement, ne pas permettre de revenir à l'étape 1\n      if (this.currentStep > 2 || this.currentStep === 2 && !this.isAutoStarted()) {\n        this.currentStep--;\n      } else {\n        // Si on est à l'étape 2 et que la transaction a été démarrée automatiquement,\n        // proposer de retourner à la page de sélection de vol\n        this.snackBar.open('La transaction a déjà été démarrée. Voulez-vous annuler et retourner à la sélection de vol?', 'Retour', {\n          duration: 5000,\n          panelClass: ['warning-snackbar']\n        }).onAction().subscribe(() => {\n          this.router.navigate(['/get-offer'], {\n            queryParams: {\n              searchId: this.searchId,\n              offerId: this.offerIds[0]\n            }\n          });\n        });\n      }\n    }\n    // Vérifier si la transaction a été démarrée automatiquement\n    isAutoStarted() {\n      return this.route.snapshot.queryParams['autoStarted'] === 'true';\n    }\n    // Formater une date pour l'affichage\n    formatDate(dateString) {\n      if (!dateString) return 'N/A';\n      const date = new Date(dateString);\n      return date.toLocaleDateString();\n    }\n    // Formater une date pour l'API (format ISO 8601: YYYY-MM-DD)\n    formatDateForApi(date) {\n      if (!date) return '';\n      // S'assurer que c'est une instance de Date valide\n      if (!(date instanceof Date) || isNaN(date.getTime())) {\n        console.error('Date invalide:', date);\n        return '';\n      }\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    }\n    // Formater un prix pour l'affichage\n    formatPrice(amount, currency) {\n      return new Intl.NumberFormat('fr-FR', {\n        style: 'currency',\n        currency: currency\n      }).format(amount);\n    }\n    // Gérer la sélection d'un pays\n    onCountrySelected(country, travellerIndex, field) {\n      console.log(`Pays sélectionné pour le voyageur ${travellerIndex}, champ ${field}:`, country);\n      const traveller = this.travellers.at(travellerIndex);\n      // Mettre à jour le champ correspondant\n      switch (field) {\n        case 'nationality':\n          traveller.get('nationality.twoLetterCode')?.setValue(country.code);\n          break;\n        case 'citizenshipCountryCode':\n          traveller.get('passportInfo.citizenshipCountryCode')?.setValue(country.code);\n          break;\n        case 'issueCountryCode':\n          traveller.get('passportInfo.issueCountryCode')?.setValue(country.code);\n          break;\n        case 'country':\n          traveller.get('address.country.id')?.setValue(country.code);\n          traveller.get('address.country.name')?.setValue(country.name);\n          break;\n      }\n    }\n    // Formater un ID d'offre pour l'affichage\n    formatOfferId(offerId) {\n      if (!offerId) return 'N/A';\n      // Extraire les informations pertinentes de l'ID d'offre\n      // Format typique: 13$3$1~^006^~AAABloLbX6oAAAAClhW0YYMc26FHjKULIrlKaQAAAZaC2zyuAAAAALH6UPiglvPwEjLokBT6TDI=~^006^~1~^006^~154.66~^006^~~^006^~154.66~^006^~ODBiZTZiMGQtYWYxYy00MzYzLThmNjctODcyNTA0NjVjZjgz\n      // Extraire le début de l'ID (avant le premier ~)\n      const parts = offerId.split('~');\n      const firstPart = parts[0] || '';\n      // Extraire les informations de base (type de vol, classe, etc.)\n      const basicInfo = firstPart.split('$');\n      // Créer un identifiant court\n      const shortId = offerId.substring(0, 8) + '...' + offerId.substring(offerId.length - 8);\n      return `Vol #${basicInfo[0] || 'N/A'} - Référence: ${shortId}`;\n    }\n    static {\n      this.ɵfac = function BookingTransactionComponent_Factory(t) {\n        return new (t || BookingTransactionComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.BookingService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.SharedDataService), i0.ɵɵdirectiveInject(i6.CountryService), i0.ɵɵdirectiveInject(i7.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: BookingTransactionComponent,\n        selectors: [[\"app-booking-transaction\"]],\n        decls: 149,\n        vars: 31,\n        consts: [[1, \"booking-transaction-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-illustration\"], [\"src\", \"assets/images/booking-banner.jpg\", \"alt\", \"R\\u00E9servation de vol\"], [3, \"linear\", \"selectedIndex\"], [\"stepper\", \"\"], [3, \"completed\", \"editable\"], [\"matStepLabel\", \"\"], [1, \"step-content\"], [1, \"step-header\"], [\"class\", \"error-message\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"response-summary\", 4, \"ngIf\"], [3, \"completed\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"form-section\"], [\"type\", \"hidden\", \"formControlName\", \"transactionId\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"reservationNote\", \"rows\", \"3\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"agencyReservationNumber\"], [1, \"section-header\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", \"type\", \"button\", \"aria-label\", \"Ajouter un voyageur\", 3, \"click\"], [\"formArrayName\", \"travellers\"], [3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"button-spinner\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"formControlName\", \"paymentOption\"], [3, \"value\"], [\"formGroupName\", \"paymentInformation\"], [\"matInput\", \"\", \"formControlName\", \"accountName\"], [\"formControlName\", \"paymentTypeId\"], [\"formGroupName\", \"paymentPrice\"], [\"matInput\", \"\", \"formControlName\", \"amount\", \"type\", \"number\", \"required\", \"\"], [\"formControlName\", \"currency\"], [\"value\", \"EUR\"], [\"value\", \"USD\"], [\"value\", \"GBP\"], [\"matInput\", \"\", \"formControlName\", \"installmentCount\"], [\"matInput\", \"\", \"formControlName\", \"paymentDate\", 3, \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"paymentDatePicker\", \"\"], [\"matInput\", \"\", \"formControlName\", \"receiptType\"], [\"matInput\", \"\", \"formControlName\", \"reference\"], [\"class\", \"response-summary success-response\", 4, \"ngIf\"], [1, \"error-message\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/search-price\"], [1, \"offer-summary\"], [1, \"offer-ids\"], [1, \"offer-card-container\"], [\"class\", \"offer-card\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"currency\", \"placeholder\", \"S\\u00E9lectionnez une devise\"], [\"formControlName\", \"culture\", \"placeholder\", \"S\\u00E9lectionnez une langue\"], [\"value\", \"fr-FR\"], [\"value\", \"en-US\"], [\"value\", \"en-GB\"], [\"mat-button\", \"\", \"type\", \"button\", \"routerLink\", \"/get-offer\"], [1, \"offer-card\"], [1, \"offer-card-header\"], [1, \"fas\", \"fa-plane\"], [1, \"offer-title\"], [1, \"offer-card-content\"], [1, \"offer-info\"], [\"diameter\", \"20\", 1, \"button-spinner\"], [1, \"response-summary\"], [1, \"transaction-details\"], [3, \"expanded\"], [3, \"formGroupName\"], [\"formControlName\", \"type\", \"placeholder\", \"S\\u00E9lectionnez un type\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"title\", \"placeholder\", \"S\\u00E9lectionnez un titre\"], [\"formControlName\", \"passengerType\", \"placeholder\", \"S\\u00E9lectionnez un type\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"surname\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"birthDate\", \"required\", \"\", 3, \"matDatepicker\"], [\"birthDatePicker\", \"\"], [\"formGroupName\", \"nationality\", 1, \"country-selector-container\"], [3, \"label\", \"placeholder\", \"required\", \"countrySelected\"], [\"matInput\", \"\", \"formControlName\", \"twoLetterCode\", \"required\", \"\", \"placeholder\", \"Code pays\"], [\"formControlName\", \"gender\"], [\"matInput\", \"\", \"formControlName\", \"identityNumber\"], [\"formGroupName\", \"passportInfo\"], [\"matInput\", \"\", \"formControlName\", \"serial\"], [\"matInput\", \"\", \"formControlName\", \"number\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"expireDate\", \"required\", \"\", 3, \"matDatepicker\"], [\"expireDatePicker\", \"\"], [\"matInput\", \"\", \"formControlName\", \"issueDate\", \"required\", \"\", 3, \"matDatepicker\"], [\"issueDatePicker\", \"\"], [1, \"country-selector-container\"], [\"matInput\", \"\", \"formControlName\", \"citizenshipCountryCode\", \"required\", \"\", \"placeholder\", \"Code pays\"], [\"matInput\", \"\", \"formControlName\", \"issueCountryCode\", \"required\", \"\", \"placeholder\", \"Code pays\"], [\"formGroupName\", \"address\"], [\"formGroupName\", \"contactPhone\"], [\"matInput\", \"\", \"formControlName\", \"countryCode\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"areaCode\"], [\"matInput\", \"\", \"formControlName\", \"phoneNumber\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"required\", \"\", \"type\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"zipCode\", \"required\", \"\"], [\"formGroupName\", \"city\"], [\"matInput\", \"\", \"formControlName\", \"id\", \"required\", \"\"], [\"formGroupName\", \"country\", 1, \"country-selector-container\"], [\"matInput\", \"\", \"formControlName\", \"id\", \"required\", \"\", \"placeholder\", \"Code pays\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"required\", \"\", \"placeholder\", \"Nom du pays\"], [1, \"form-row\", \"traveller-actions\"], [\"formControlName\", \"isLeader\"], [\"mat-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [1, \"reservation-details\"], [1, \"response-summary\", \"success-response\"], [1, \"confirmation-details\"], [1, \"confirmation-message\"], [1, \"confirmation-actions\"]],\n        template: function BookingTransactionComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n            i0.ɵɵtext(4, \"R\\u00E9servation de vol\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\", 4);\n            i0.ɵɵtext(6, \"Compl\\u00E9tez votre r\\u00E9servation en quelques \\u00E9tapes simples\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 5);\n            i0.ɵɵelement(8, \"img\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"mat-horizontal-stepper\", 7, 8)(11, \"mat-step\", 9);\n            i0.ɵɵtemplate(12, BookingTransactionComponent_ng_template_12_Template, 1, 1, \"ng-template\", 10);\n            i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"h2\");\n            i0.ɵɵtext(16, \"D\\u00E9marrer votre r\\u00E9servation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"p\");\n            i0.ɵɵtext(18, \"Nous allons commencer le processus de r\\u00E9servation pour les vols s\\u00E9lectionn\\u00E9s.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(19, BookingTransactionComponent_div_19_Template, 5, 1, \"div\", 13);\n            i0.ɵɵtemplate(20, BookingTransactionComponent_div_20_Template, 7, 0, \"div\", 13);\n            i0.ɵɵtemplate(21, BookingTransactionComponent_div_21_Template, 41, 6, \"div\", 14);\n            i0.ɵɵtemplate(22, BookingTransactionComponent_div_22_Template, 12, 2, \"div\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(23, \"mat-step\", 16);\n            i0.ɵɵtemplate(24, BookingTransactionComponent_ng_template_24_Template, 1, 0, \"ng-template\", 10);\n            i0.ɵɵelementStart(25, \"div\", 11)(26, \"div\", 12)(27, \"h2\");\n            i0.ɵɵtext(28, \"Informations des voyageurs\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"p\");\n            i0.ɵɵtext(30, \"Veuillez fournir les informations pour tous les voyageurs.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(31, BookingTransactionComponent_div_31_Template, 5, 1, \"div\", 13);\n            i0.ɵɵelementStart(32, \"form\", 17);\n            i0.ɵɵlistener(\"ngSubmit\", function BookingTransactionComponent_Template_form_ngSubmit_32_listener() {\n              return ctx.setReservationInfo();\n            });\n            i0.ɵɵelementStart(33, \"div\", 18)(34, \"h3\");\n            i0.ɵɵtext(35, \"Informations g\\u00E9n\\u00E9rales\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(36, \"input\", 19);\n            i0.ɵɵelementStart(37, \"div\", 20)(38, \"mat-form-field\", 21)(39, \"mat-label\");\n            i0.ɵɵtext(40, \"Note de r\\u00E9servation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(41, \"textarea\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(42, \"div\", 20)(43, \"mat-form-field\", 23)(44, \"mat-label\");\n            i0.ɵɵtext(45, \"Num\\u00E9ro de r\\u00E9servation d'agence\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(46, \"input\", 24);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(47, \"div\", 18)(48, \"div\", 25)(49, \"h3\");\n            i0.ɵɵtext(50, \"Voyageurs\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function BookingTransactionComponent_Template_button_click_51_listener() {\n              return ctx.addTraveller();\n            });\n            i0.ɵɵelementStart(52, \"mat-icon\");\n            i0.ɵɵtext(53, \"add\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(54, \"div\", 27)(55, \"mat-accordion\");\n            i0.ɵɵtemplate(56, BookingTransactionComponent_mat_expansion_panel_56_Template, 187, 33, \"mat-expansion-panel\", 28);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(57, \"div\", 29)(58, \"button\", 30);\n            i0.ɵɵtemplate(59, BookingTransactionComponent_mat_spinner_59_Template, 1, 0, \"mat-spinner\", 31);\n            i0.ɵɵtemplate(60, BookingTransactionComponent_span_60_Template, 2, 0, \"span\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"button\", 32);\n            i0.ɵɵlistener(\"click\", function BookingTransactionComponent_Template_button_click_61_listener() {\n              return ctx.previousStep();\n            });\n            i0.ɵɵtext(62, \"Retour\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(63, BookingTransactionComponent_div_63_Template, 8, 1, \"div\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(64, \"mat-step\", 16);\n            i0.ɵɵtemplate(65, BookingTransactionComponent_ng_template_65_Template, 1, 0, \"ng-template\", 10);\n            i0.ɵɵelementStart(66, \"div\", 11)(67, \"div\", 12)(68, \"h2\");\n            i0.ɵɵtext(69, \"Paiement et confirmation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(70, \"p\");\n            i0.ɵɵtext(71, \"Finalisez votre r\\u00E9servation en effectuant le paiement.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(72, BookingTransactionComponent_div_72_Template, 5, 1, \"div\", 13);\n            i0.ɵɵelementStart(73, \"form\", 17);\n            i0.ɵɵlistener(\"ngSubmit\", function BookingTransactionComponent_Template_form_ngSubmit_73_listener() {\n              return ctx.commitTransaction();\n            });\n            i0.ɵɵelementStart(74, \"div\", 18)(75, \"h3\");\n            i0.ɵɵtext(76, \"Informations de paiement\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(77, \"input\", 19);\n            i0.ɵɵelementStart(78, \"div\", 20)(79, \"mat-form-field\", 23)(80, \"mat-label\");\n            i0.ɵɵtext(81, \"Option de paiement\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(82, \"mat-select\", 33)(83, \"mat-option\", 34);\n            i0.ɵɵtext(84, \"Carte de cr\\u00E9dit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(85, \"mat-option\", 34);\n            i0.ɵɵtext(86, \"Virement bancaire\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(87, \"mat-option\", 34);\n            i0.ɵɵtext(88, \"PayPal\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(89, \"div\", 35)(90, \"div\", 20)(91, \"mat-form-field\", 23)(92, \"mat-label\");\n            i0.ɵɵtext(93, \"Nom du titulaire\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(94, \"input\", 36);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(95, \"mat-form-field\", 23)(96, \"mat-label\");\n            i0.ɵɵtext(97, \"Type de paiement\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"mat-select\", 37)(99, \"mat-option\", 34);\n            i0.ɵɵtext(100, \"Carte de cr\\u00E9dit\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(101, \"mat-option\", 34);\n            i0.ɵɵtext(102, \"Virement bancaire\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(103, \"mat-option\", 34);\n            i0.ɵɵtext(104, \"PayPal\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(105, \"div\", 20)(106, \"div\", 38)(107, \"mat-form-field\", 23)(108, \"mat-label\");\n            i0.ɵɵtext(109, \"Montant\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(110, \"input\", 39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(111, \"mat-form-field\", 23)(112, \"mat-label\");\n            i0.ɵɵtext(113, \"Devise\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(114, \"mat-select\", 40)(115, \"mat-option\", 41);\n            i0.ɵɵtext(116, \"Euro (EUR)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(117, \"mat-option\", 42);\n            i0.ɵɵtext(118, \"Dollar am\\u00E9ricain (USD)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(119, \"mat-option\", 43);\n            i0.ɵɵtext(120, \"Livre sterling (GBP)\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(121, \"div\", 20)(122, \"mat-form-field\", 23)(123, \"mat-label\");\n            i0.ɵɵtext(124, \"Nombre de versements\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(125, \"input\", 44);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(126, \"mat-form-field\", 23)(127, \"mat-label\");\n            i0.ɵɵtext(128, \"Date de paiement\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(129, \"input\", 45)(130, \"mat-datepicker-toggle\", 46)(131, \"mat-datepicker\", null, 47);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(133, \"div\", 20)(134, \"mat-form-field\", 23)(135, \"mat-label\");\n            i0.ɵɵtext(136, \"Type de re\\u00E7u\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(137, \"input\", 48);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(138, \"mat-form-field\", 23)(139, \"mat-label\");\n            i0.ɵɵtext(140, \"R\\u00E9f\\u00E9rence\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(141, \"input\", 49);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(142, \"div\", 29)(143, \"button\", 30);\n            i0.ɵɵtemplate(144, BookingTransactionComponent_mat_spinner_144_Template, 1, 0, \"mat-spinner\", 31);\n            i0.ɵɵtemplate(145, BookingTransactionComponent_span_145_Template, 2, 0, \"span\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(146, \"button\", 32);\n            i0.ɵɵlistener(\"click\", function BookingTransactionComponent_Template_button_click_146_listener() {\n              return ctx.previousStep();\n            });\n            i0.ɵɵtext(147, \"Retour\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(148, BookingTransactionComponent_div_148_Template, 16, 1, \"div\", 50);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            const _r14 = i0.ɵɵreference(132);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"linear\", true)(\"selectedIndex\", ctx.currentStep - 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"completed\", ctx.beginResponse !== null)(\"editable\", !ctx.transactionId);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.offerIds.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.offerIds.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.beginResponse && ctx.beginResponse.body);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"completed\", ctx.infoResponse !== null);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.reservationInfoForm);\n            i0.ɵɵadvance(24);\n            i0.ɵɵproperty(\"ngForOf\", ctx.travellers.controls);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.reservationInfoForm.invalid);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.infoResponse && ctx.infoResponse.body);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"completed\", ctx.commitResponse !== null);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.commitTransactionForm);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 2);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 3);\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 2);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 3);\n            i0.ɵɵadvance(26);\n            i0.ɵɵproperty(\"matDatepicker\", _r14);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"for\", _r14);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.commitTransactionForm.invalid);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.commitResponse && ctx.commitResponse.body);\n          }\n        },\n        dependencies: [i8.NgForOf, i8.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i9.MatOption, i10.MatInput, i11.MatFormField, i11.MatLabel, i11.MatHint, i11.MatError, i11.MatSuffix, i12.MatSelect, i13.MatDatepicker, i13.MatDatepickerInput, i13.MatDatepickerToggle, i14.MatButton, i14.MatMiniFabButton, i15.MatIcon, i16.MatProgressSpinner, i17.MatAccordion, i17.MatExpansionPanel, i17.MatExpansionPanelHeader, i17.MatExpansionPanelTitle, i17.MatExpansionPanelDescription, i18.MatStep, i18.MatStepLabel, i18.MatStepper, i19.CountrySelectorComponent],\n        styles: [\".booking-transaction-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.page-header[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:30px;background:linear-gradient(135deg,#1a73e8,#0d47a1);border-radius:16px;overflow:hidden;box-shadow:0 8px 16px #0000001a}.header-content[_ngcontent-%COMP%]{flex:1;padding:40px;color:#fff}.page-title[_ngcontent-%COMP%]{font-size:32px;margin:0 0 10px;font-weight:600}.page-subtitle[_ngcontent-%COMP%]{font-size:16px;margin:0;opacity:.9}.header-illustration[_ngcontent-%COMP%]{flex:1;height:200px;overflow:hidden}.header-illustration[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.step-content[_ngcontent-%COMP%]{padding:20px 0}.step-header[_ngcontent-%COMP%]{margin-bottom:20px}.step-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:24px;margin:0 0 10px;color:#1a73e8}.step-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:16px;margin:0;color:#5f6368}.form-section[_ngcontent-%COMP%]{background-color:#f8f9fa;border-radius:8px;padding:20px;margin-bottom:20px;box-shadow:0 2px 4px #0000000d}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px}.section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;margin:0;color:#202124}.form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:16px;margin:0 0 15px;color:#5f6368}.form-row[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;margin-bottom:16px}.form-row[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{flex:1;min-width:200px}.full-width[_ngcontent-%COMP%]{width:100%}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:16px;margin-top:20px}.error-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background-color:#fdeded;color:#d32f2f;padding:12px 16px;border-radius:8px;margin-bottom:20px}.error-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#d32f2f}.button-spinner[_ngcontent-%COMP%]{margin-right:8px}.traveller-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.response-summary[_ngcontent-%COMP%]{background-color:#e8f0fe;border-radius:8px;padding:20px;margin-top:30px}.response-summary[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;margin:0 0 15px;color:#1a73e8}.transaction-details[_ngcontent-%COMP%], .reservation-details[_ngcontent-%COMP%], .confirmation-details[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:16px}.transaction-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .reservation-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .confirmation-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;padding:8px 0;border-bottom:1px solid rgba(0,0,0,.1)}.success-response[_ngcontent-%COMP%]{background-color:#e6f4ea}.confirmation-message[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:12px;margin:20px 0;grid-column:1 / -1}.confirmation-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#34a853;font-size:24px}.confirmation-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;border:none}.confirmation-actions[_ngcontent-%COMP%]{grid-column:1 / -1;margin-top:20px;text-align:center}.offer-summary[_ngcontent-%COMP%]{background-color:#f8f9fa;border-radius:8px;padding:20px;margin-bottom:20px}.offer-summary[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;margin:0 0 15px;color:#202124}.offer-ids[_ngcontent-%COMP%]{margin-bottom:15px}.offer-ids[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 10px;font-weight:500}.offer-card-container[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;margin-top:15px}.offer-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:8px;box-shadow:0 2px 8px #0000001a;padding:16px;width:100%;transition:transform .2s,box-shadow .2s;border-left:4px solid #1a73e8}.offer-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.offer-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:12px;color:#1a73e8}.offer-card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:12px;font-size:20px}.offer-title[_ngcontent-%COMP%]{font-weight:500;font-size:16px}.offer-card-content[_ngcontent-%COMP%]{color:#5f6368}.offer-info[_ngcontent-%COMP%]{margin:0;font-size:14px}.country-selector-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px;width:100%;margin-bottom:20px;padding:15px;background-color:#f0f7ff;border-radius:8px;border-left:4px solid #1a73e8}.country-selector-container[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .country-selector-container[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 5px;color:#1a73e8;font-weight:500}.country-selector-container[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:14px}.country-selector-container[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{margin-bottom:0}app-country-selector[_ngcontent-%COMP%]{transition:transform .2s,box-shadow .2s}app-country-selector[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #0000001a}@media (max-width: 768px){.page-header[_ngcontent-%COMP%]{flex-direction:column}.header-content[_ngcontent-%COMP%]{padding:30px}.header-illustration[_ngcontent-%COMP%]{width:100%}.form-row[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{min-width:100%}.country-selector-container[_ngcontent-%COMP%]{min-width:100%}}\"]\n      });\n    }\n  }\n  return BookingTransactionComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}