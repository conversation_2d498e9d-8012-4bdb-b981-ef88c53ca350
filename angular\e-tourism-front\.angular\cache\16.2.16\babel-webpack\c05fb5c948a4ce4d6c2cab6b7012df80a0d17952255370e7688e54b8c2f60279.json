{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport class BookingService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.apiUrl = 'http://localhost:8080/booking';\n  }\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request) {\n    // Utiliser l'endpoint unifié pour toutes les actions de transaction\n    const endpoint = '/booking-transaction';\n    const requestBody = request; // Envoyer l'objet BookingTransactionRequest complet\n    // Récupérer le token d'authentification\n    const token = this.authService.getToken();\n    if (!token) {\n      throw new Error('Vous devez être connecté pour effectuer cette action.');\n    }\n    // Créer les en-têtes avec le token d'authentification\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n    console.log(`Envoi de la requête ${request.action} à ${endpoint}:`, requestBody);\n    return this.http.post(`${this.apiUrl}${endpoint}`, requestBody, {\n      headers\n    }).pipe(map(response => {\n      console.log(`Réponse reçue de ${endpoint}:`, response);\n      // Vérifier si la réponse est valide\n      if (!response) {\n        throw new Error('Réponse invalide du serveur');\n      }\n      // Construire la réponse au format BookingTransactionResponse\n      const bookingResponse = {\n        action: request.action\n      };\n      // Créer une structure de réponse compatible avec le modèle frontend\n      switch (request.action) {\n        case 'begin':\n          // Si la réponse est déjà au format BeginTransactionResponse\n          if (response.header && response.body) {\n            bookingResponse.beginResponse = response;\n          }\n          // Si la réponse est un objet générique avec des données\n          else if (response.data) {\n            bookingResponse.beginResponse = response.data;\n          }\n          // Si la réponse est directement l'objet body\n          else if (response.transactionId) {\n            bookingResponse.beginResponse = {\n              header: {\n                requestId: '',\n                success: true,\n                responseTime: new Date().toISOString(),\n                messages: []\n              },\n              body: {\n                transactionId: response.transactionId,\n                expiresOn: response.expiresOn || new Date(Date.now() + 3600000).toISOString(),\n                status: response.status || 1,\n                transactionType: response.transactionType || 1,\n                reservationData: response.reservationData || {\n                  travellers: [],\n                  reservationInfo: null,\n                  services: [],\n                  paymentDetail: null,\n                  invoices: []\n                }\n              }\n            };\n          }\n          // Si aucun format reconnu\n          else {\n            throw new Error('Format de réponse non reconnu pour l\\'action begin');\n          }\n          break;\n        case 'info':\n          if (response.header && response.body) {\n            bookingResponse.infoResponse = response;\n          } else if (response.data) {\n            bookingResponse.infoResponse = response.data;\n          } else {\n            throw new Error('Format de réponse non reconnu pour l\\'action info');\n          }\n          break;\n        case 'commit':\n          if (response.header && response.body) {\n            bookingResponse.commitResponse = response;\n          } else if (response.data) {\n            bookingResponse.commitResponse = response.data;\n          } else {\n            throw new Error('Format de réponse non reconnu pour l\\'action commit');\n          }\n          break;\n      }\n      return bookingResponse;\n    }), catchError(error => {\n      console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);\n      throw error;\n    }));\n  }\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds, currency = 'EUR', culture = 'fr-FR') {\n    const request = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n    return this.bookingTransaction(request);\n  }\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request) {\n    const bookingRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request) {\n    const bookingRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId) {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId) {\n    return {\n      transactionId,\n      paymentOption: 1,\n      paymentInformation: {\n        paymentTypeId: 1,\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n  static {\n    this.ɵfac = function BookingService_Factory(t) {\n      return new (t || BookingService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BookingService,\n      factory: BookingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "catchError", "map", "BookingService", "constructor", "http", "authService", "apiUrl", "bookingTransaction", "request", "endpoint", "requestBody", "token", "getToken", "Error", "headers", "console", "log", "action", "post", "pipe", "response", "bookingResponse", "header", "body", "beginResponse", "data", "transactionId", "requestId", "success", "responseTime", "Date", "toISOString", "messages", "expiresOn", "now", "status", "transactionType", "reservationData", "travellers", "reservationInfo", "services", "paymentDetail", "invoices", "infoResponse", "commitResponse", "error", "beginTransaction", "offerIds", "currency", "culture", "beginRequest", "setReservationInfo", "bookingRequest", "infoRequest", "commitTransaction", "commitRequest", "createDefaultReservationInfoRequest", "reservationNote", "agencyReservationNumber", "createDefaultCommitTransactionRequest", "paymentOption", "paymentInformation", "paymentTypeId", "paymentPrice", "amount", "paymentDate", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\booking.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { AuthService } from './auth.service';\nimport {\n  BookingTransactionRequest,\n  BookingTransactionResponse,\n  BeginTransactionRequest,\n  BeginTransactionResponse,\n  SetReservationInfoRequest,\n  SetReservationInfoResponse,\n  CommitTransactionRequest,\n  CommitTransactionResponse\n} from '../models/booking';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class BookingService {\n  private apiUrl = 'http://localhost:8080/booking';\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) { }\n\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request: BookingTransactionRequest): Observable<BookingTransactionResponse> {\n    // Utiliser l'endpoint unifié pour toutes les actions de transaction\n    const endpoint = '/booking-transaction';\n    const requestBody = request; // Envoyer l'objet BookingTransactionRequest complet\n\n    // Récupérer le token d'authentification\n    const token = this.authService.getToken();\n    if (!token) {\n      throw new Error('Vous devez être connecté pour effectuer cette action.');\n    }\n\n    // Créer les en-têtes avec le token d'authentification\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n\n    console.log(`Envoi de la requête ${request.action} à ${endpoint}:`, requestBody);\n\n    return this.http.post<any>(\n      `${this.apiUrl}${endpoint}`,\n      requestBody,\n      { headers }\n    ).pipe(\n      map(response => {\n        console.log(`Réponse reçue de ${endpoint}:`, response);\n\n        // Vérifier si la réponse est valide\n        if (!response) {\n          throw new Error('Réponse invalide du serveur');\n        }\n\n        // Construire la réponse au format BookingTransactionResponse\n        const bookingResponse: BookingTransactionResponse = {\n          action: request.action\n        };\n\n        // Créer une structure de réponse compatible avec le modèle frontend\n        switch (request.action) {\n          case 'begin':\n            // Si la réponse est déjà au format BeginTransactionResponse\n            if (response.header && response.body) {\n              bookingResponse.beginResponse = response;\n            }\n            // Si la réponse est un objet générique avec des données\n            else if (response.data) {\n              bookingResponse.beginResponse = response.data;\n            }\n            // Si la réponse est directement l'objet body\n            else if (response.transactionId) {\n              bookingResponse.beginResponse = {\n                header: {\n                  requestId: '',\n                  success: true,\n                  responseTime: new Date().toISOString(),\n                  messages: []\n                },\n                body: {\n                  transactionId: response.transactionId,\n                  expiresOn: response.expiresOn || new Date(Date.now() + 3600000).toISOString(),\n                  status: response.status || 1,\n                  transactionType: response.transactionType || 1,\n                  reservationData: response.reservationData || {\n                    travellers: [],\n                    reservationInfo: null,\n                    services: [],\n                    paymentDetail: null,\n                    invoices: []\n                  }\n                }\n              };\n            }\n            // Si aucun format reconnu\n            else {\n              throw new Error('Format de réponse non reconnu pour l\\'action begin');\n            }\n            break;\n\n          case 'info':\n            if (response.header && response.body) {\n              bookingResponse.infoResponse = response;\n            } else if (response.data) {\n              bookingResponse.infoResponse = response.data;\n            } else {\n              throw new Error('Format de réponse non reconnu pour l\\'action info');\n            }\n            break;\n\n          case 'commit':\n            if (response.header && response.body) {\n              bookingResponse.commitResponse = response;\n            } else if (response.data) {\n              bookingResponse.commitResponse = response.data;\n            } else {\n              throw new Error('Format de réponse non reconnu pour l\\'action commit');\n            }\n            break;\n        }\n\n        return bookingResponse;\n      }),\n      catchError(error => {\n        console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);\n        throw error;\n      })\n    );\n  }\n\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds: string[], currency: string = 'EUR', culture: string = 'fr-FR'): Observable<BookingTransactionResponse> {\n    const request: BookingTransactionRequest = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n\n    return this.bookingTransaction(request);\n  }\n\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request: SetReservationInfoRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request: CommitTransactionRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId: string): SetReservationInfoRequest {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId: string): CommitTransactionRequest {\n    return {\n      transactionId,\n      paymentOption: 1, // Option de paiement par défaut\n      paymentInformation: {\n        paymentTypeId: 1, // Type de paiement par défaut\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;;AAgBhD,OAAM,MAAOC,cAAc;EAGzBC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAJb,KAAAC,MAAM,GAAG,+BAA+B;EAK5C;EAEJ;;;;;EAKAC,kBAAkBA,CAACC,OAAkC;IACnD;IACA,MAAMC,QAAQ,GAAG,sBAAsB;IACvC,MAAMC,WAAW,GAAGF,OAAO,CAAC,CAAC;IAE7B;IACA,MAAMG,KAAK,GAAG,IAAI,CAACN,WAAW,CAACO,QAAQ,EAAE;IACzC,IAAI,CAACD,KAAK,EAAE;MACV,MAAM,IAAIE,KAAK,CAAC,uDAAuD,CAAC;;IAG1E;IACA,MAAMC,OAAO,GAAG,IAAIf,WAAW,CAAC;MAC9B,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAE,UAAUY,KAAK;KACjC,CAAC;IAEFI,OAAO,CAACC,GAAG,CAAC,uBAAuBR,OAAO,CAACS,MAAM,MAAMR,QAAQ,GAAG,EAAEC,WAAW,CAAC;IAEhF,OAAO,IAAI,CAACN,IAAI,CAACc,IAAI,CACnB,GAAG,IAAI,CAACZ,MAAM,GAAGG,QAAQ,EAAE,EAC3BC,WAAW,EACX;MAAEI;IAAO,CAAE,CACZ,CAACK,IAAI,CACJlB,GAAG,CAACmB,QAAQ,IAAG;MACbL,OAAO,CAACC,GAAG,CAAC,oBAAoBP,QAAQ,GAAG,EAAEW,QAAQ,CAAC;MAEtD;MACA,IAAI,CAACA,QAAQ,EAAE;QACb,MAAM,IAAIP,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA,MAAMQ,eAAe,GAA+B;QAClDJ,MAAM,EAAET,OAAO,CAACS;OACjB;MAED;MACA,QAAQT,OAAO,CAACS,MAAM;QACpB,KAAK,OAAO;UACV;UACA,IAAIG,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACG,IAAI,EAAE;YACpCF,eAAe,CAACG,aAAa,GAAGJ,QAAQ;;UAE1C;UAAA,KACK,IAAIA,QAAQ,CAACK,IAAI,EAAE;YACtBJ,eAAe,CAACG,aAAa,GAAGJ,QAAQ,CAACK,IAAI;;UAE/C;UAAA,KACK,IAAIL,QAAQ,CAACM,aAAa,EAAE;YAC/BL,eAAe,CAACG,aAAa,GAAG;cAC9BF,MAAM,EAAE;gBACNK,SAAS,EAAE,EAAE;gBACbC,OAAO,EAAE,IAAI;gBACbC,YAAY,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;gBACtCC,QAAQ,EAAE;eACX;cACDT,IAAI,EAAE;gBACJG,aAAa,EAAEN,QAAQ,CAACM,aAAa;gBACrCO,SAAS,EAAEb,QAAQ,CAACa,SAAS,IAAI,IAAIH,IAAI,CAACA,IAAI,CAACI,GAAG,EAAE,GAAG,OAAO,CAAC,CAACH,WAAW,EAAE;gBAC7EI,MAAM,EAAEf,QAAQ,CAACe,MAAM,IAAI,CAAC;gBAC5BC,eAAe,EAAEhB,QAAQ,CAACgB,eAAe,IAAI,CAAC;gBAC9CC,eAAe,EAAEjB,QAAQ,CAACiB,eAAe,IAAI;kBAC3CC,UAAU,EAAE,EAAE;kBACdC,eAAe,EAAE,IAAI;kBACrBC,QAAQ,EAAE,EAAE;kBACZC,aAAa,EAAE,IAAI;kBACnBC,QAAQ,EAAE;;;aAGf;;UAEH;UAAA,KACK;YACH,MAAM,IAAI7B,KAAK,CAAC,oDAAoD,CAAC;;UAEvE;QAEF,KAAK,MAAM;UACT,IAAIO,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACG,IAAI,EAAE;YACpCF,eAAe,CAACsB,YAAY,GAAGvB,QAAQ;WACxC,MAAM,IAAIA,QAAQ,CAACK,IAAI,EAAE;YACxBJ,eAAe,CAACsB,YAAY,GAAGvB,QAAQ,CAACK,IAAI;WAC7C,MAAM;YACL,MAAM,IAAIZ,KAAK,CAAC,mDAAmD,CAAC;;UAEtE;QAEF,KAAK,QAAQ;UACX,IAAIO,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACG,IAAI,EAAE;YACpCF,eAAe,CAACuB,cAAc,GAAGxB,QAAQ;WAC1C,MAAM,IAAIA,QAAQ,CAACK,IAAI,EAAE;YACxBJ,eAAe,CAACuB,cAAc,GAAGxB,QAAQ,CAACK,IAAI;WAC/C,MAAM;YACL,MAAM,IAAIZ,KAAK,CAAC,qDAAqD,CAAC;;UAExE;;MAGJ,OAAOQ,eAAe;IACxB,CAAC,CAAC,EACFrB,UAAU,CAAC6C,KAAK,IAAG;MACjB9B,OAAO,CAAC8B,KAAK,CAAC,iDAAiDrC,OAAO,CAACS,MAAM,IAAI,EAAE4B,KAAK,CAAC;MACzF,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;EAOAC,gBAAgBA,CAACC,QAAkB,EAAEC,QAAA,GAAmB,KAAK,EAAEC,OAAA,GAAkB,OAAO;IACtF,MAAMzC,OAAO,GAA8B;MACzCS,MAAM,EAAE,OAAO;MACfiC,YAAY,EAAE;QACZH,QAAQ;QACRC,QAAQ;QACRC;;KAEH;IAED,OAAO,IAAI,CAAC1C,kBAAkB,CAACC,OAAO,CAAC;EACzC;EAEA;;;;;EAKA2C,kBAAkBA,CAAC3C,OAAkC;IACnD,MAAM4C,cAAc,GAA8B;MAChDnC,MAAM,EAAE,MAAM;MACdoC,WAAW,EAAE7C;KACd;IAED,OAAO,IAAI,CAACD,kBAAkB,CAAC6C,cAAc,CAAC;EAChD;EAEA;;;;;EAKAE,iBAAiBA,CAAC9C,OAAiC;IACjD,MAAM4C,cAAc,GAA8B;MAChDnC,MAAM,EAAE,QAAQ;MAChBsC,aAAa,EAAE/C;KAChB;IAED,OAAO,IAAI,CAACD,kBAAkB,CAAC6C,cAAc,CAAC;EAChD;EAEA;;;;;EAKAI,mCAAmCA,CAAC9B,aAAqB;IACvD,OAAO;MACLA,aAAa;MACbY,UAAU,EAAE,EAAE;MACdmB,eAAe,EAAE,EAAE;MACnBC,uBAAuB,EAAE;KAC1B;EACH;EAEA;;;;;EAKAC,qCAAqCA,CAACjC,aAAqB;IACzD,OAAO;MACLA,aAAa;MACbkC,aAAa,EAAE,CAAC;MAChBC,kBAAkB,EAAE;QAClBC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;UACZC,MAAM,EAAE,CAAC;UACThB,QAAQ,EAAE;SACX;QACDiB,WAAW,EAAE,IAAInC,IAAI,EAAE,CAACC,WAAW;;KAEtC;EACH;;;uBAzMW7B,cAAc,EAAAgE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAdrE,cAAc;MAAAsE,OAAA,EAAdtE,cAAc,CAAAuE,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}