/* Styles pour les vols aller-retour */

/* Badge pour indiquer un vol aller-retour */
.round-trip-badge {
  position: absolute;
  top: -10px;
  right: 20px;
  background-color: #3498db;
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.round-trip-badge i {
  margin-right: 5px;
}

/* Styles pour la carte de vol aller-retour */
.flight-card.round-trip {
  position: relative;
  border-left: 4px solid #3498db;
  padding-top: 20px;
}

/* Sections aller et retour */
.round-trip-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.round-trip-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  background-color: #f9f9f9;
}

.round-trip-section.outbound {
  border-left: 3px solid #4CAF50; /* Vert pour l'aller */
}

.round-trip-section.inbound {
  border-left: 3px solid #FF5722; /* Orange pour le retour */
}

.round-trip-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px dashed #ccc;
}

.round-trip-header i {
  margin-right: 8px;
  color: #555;
}

.round-trip-label {
  font-weight: bold;
  color: #333;
}

/* Durée du séjour */
.stay-duration {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background-color: #e8f4fd;
  border-radius: 8px;
  color: #0066cc;
  font-weight: 500;
}

.stay-duration i {
  margin-right: 8px;
  color: #0066cc;
}

/* Message d'information sur les dates */
.date-info-container {
  margin-bottom: 10px;
}

.date-info-message {
  background-color: #fff8e6;
  border-left: 4px solid #f59e0b;
  padding: 10px;
  border-radius: 0 4px 4px 0;
  font-size: 13px;
  color: #92400e;
  margin-bottom: 10px;
}

.date-info-message i {
  margin-right: 5px;
  color: #f59e0b;
}

.date-info-success {
  background-color: #f0f9f0;
  border-left: 4px solid #4caf50;
  color: #2e7d32;
}

.date-info-success i {
  color: #4caf50;
}

/* Indicateur de différence de date */
.date-diff {
  position: relative;
}

.date-diff-indicator {
  font-size: 11px;
  color: #e65100;
  font-weight: bold;
  margin-left: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
  .round-trip-details {
    gap: 15px;
  }

  .round-trip-section {
    padding: 10px;
  }

  .round-trip-badge {
    font-size: 10px;
    padding: 3px 8px;
  }
}
