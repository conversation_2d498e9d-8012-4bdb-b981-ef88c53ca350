{"ast": null, "code": "import { FlightClassType } from '../models/enums.model';\nimport * as i0 from \"@angular/core\";\nexport class FlightFilterService {\n  constructor() {}\n  /**\n   * Filtre les vols par classe (Business, Economy, etc.)\n   * @param flights Liste des vols à filtrer\n   * @param classType Type de classe à filtrer (utiliser FlightClassType)\n   * @returns Liste des vols filtrés\n   */\n  filterByClass(flights, classType) {\n    if (!flights || flights.length === 0) {\n      return [];\n    }\n    console.log(`Filtrage des vols par classe: ${classType}`);\n    return flights.filter(flight => {\n      // Vérifier si le vol a des items\n      if (!flight.items || flight.items.length === 0) {\n        return false;\n      }\n      // Vérifier si le vol a des informations de classe\n      if (!flight.items[0].flightClass) {\n        return false;\n      }\n      // Récupérer le type et le nom de la classe\n      const flightClassType = typeof flight.items[0].flightClass.type === 'string' ? parseInt(flight.items[0].flightClass.type) : flight.items[0].flightClass.type;\n      const flightClassName = flight.items[0].flightClass.name || '';\n      // Filtrage pour la classe Business\n      if (classType === FlightClassType.BUSINESS) {\n        const isBusinessClass = flightClassType === FlightClassType.BUSINESS || flightClassName.toUpperCase().includes('BUSINESS');\n        if (!isBusinessClass) {\n          console.log(`Vol filtré: classe ${flightClassType} (${flightClassName}) n'est pas Business`);\n        }\n        return isBusinessClass;\n      }\n      // Filtrage pour la classe Economy\n      if (classType === FlightClassType.ECONOMY) {\n        const isEconomyClass = flightClassType === FlightClassType.ECONOMY || flightClassName.toUpperCase().includes('ECONOMY');\n        if (!isEconomyClass) {\n          console.log(`Vol filtré: classe ${flightClassType} (${flightClassName}) n'est pas Economy`);\n        }\n        return isEconomyClass;\n      }\n      // Pour les autres classes, vérifier l'égalité exacte\n      const matches = flightClassType === classType;\n      if (!matches) {\n        console.log(`Vol filtré: classe ${flightClassType} (${flightClassName}) ne correspond pas à la classe sélectionnée ${classType}`);\n      }\n      return matches;\n    });\n  }\n  /**\n   * Vérifie si un vol est de la classe spécifiée\n   * @param flight Vol à vérifier\n   * @param classType Type de classe à vérifier\n   * @returns true si le vol est de la classe spécifiée\n   */\n  isFlightOfClass(flight, classType) {\n    if (!flight.items || flight.items.length === 0 || !flight.items[0].flightClass) {\n      return false;\n    }\n    const flightClassType = typeof flight.items[0].flightClass.type === 'string' ? parseInt(flight.items[0].flightClass.type) : flight.items[0].flightClass.type;\n    const flightClassName = flight.items[0].flightClass.name || '';\n    // Vérification pour la classe Business\n    if (classType === FlightClassType.BUSINESS) {\n      return flightClassType === FlightClassType.BUSINESS || flightClassName.toUpperCase().includes('BUSINESS');\n    }\n    // Vérification pour la classe Economy\n    if (classType === FlightClassType.ECONOMY) {\n      return flightClassType === FlightClassType.ECONOMY || flightClassName.toUpperCase().includes('ECONOMY');\n    }\n    // Pour les autres classes, vérifier l'égalité exacte\n    return flightClassType === classType;\n  }\n  static {\n    this.ɵfac = function FlightFilterService_Factory(t) {\n      return new (t || FlightFilterService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FlightFilterService,\n      factory: FlightFilterService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["FlightClassType", "FlightFilterService", "constructor", "filterByClass", "flights", "classType", "length", "console", "log", "filter", "flight", "items", "flightClass", "flightClassType", "type", "parseInt", "flightClassName", "name", "BUSINESS", "isBusinessClass", "toUpperCase", "includes", "ECONOMY", "isEconomyClass", "matches", "isFlightOfClass", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\flight-filter.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Flight } from '../models/price-search-response.model';\nimport { FlightClassType } from '../models/enums.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FlightFilterService {\n\n  constructor() { }\n\n  /**\n   * Filtre les vols par classe (Business, Economy, etc.)\n   * @param flights Liste des vols à filtrer\n   * @param classType Type de classe à filtrer (utiliser FlightClassType)\n   * @returns Liste des vols filtrés\n   */\n  filterByClass(flights: Flight[], classType: number): Flight[] {\n    if (!flights || flights.length === 0) {\n      return [];\n    }\n\n    console.log(`Filtrage des vols par classe: ${classType}`);\n    \n    return flights.filter(flight => {\n      // Vérifier si le vol a des items\n      if (!flight.items || flight.items.length === 0) {\n        return false;\n      }\n      \n      // Vérifier si le vol a des informations de classe\n      if (!flight.items[0].flightClass) {\n        return false;\n      }\n\n      // Récupérer le type et le nom de la classe\n      const flightClassType = typeof flight.items[0].flightClass.type === 'string' \n                            ? parseInt(flight.items[0].flightClass.type) \n                            : flight.items[0].flightClass.type;\n      const flightClassName = flight.items[0].flightClass.name || '';\n\n      // Filtrage pour la classe Business\n      if (classType === FlightClassType.BUSINESS) {\n        const isBusinessClass = flightClassType === FlightClassType.BUSINESS || \n                               flightClassName.toUpperCase().includes('BUSINESS');\n        \n        if (!isBusinessClass) {\n          console.log(`Vol filtré: classe ${flightClassType} (${flightClassName}) n'est pas Business`);\n        }\n        \n        return isBusinessClass;\n      }\n      \n      // Filtrage pour la classe Economy\n      if (classType === FlightClassType.ECONOMY) {\n        const isEconomyClass = flightClassType === FlightClassType.ECONOMY || \n                              flightClassName.toUpperCase().includes('ECONOMY');\n        \n        if (!isEconomyClass) {\n          console.log(`Vol filtré: classe ${flightClassType} (${flightClassName}) n'est pas Economy`);\n        }\n        \n        return isEconomyClass;\n      }\n      \n      // Pour les autres classes, vérifier l'égalité exacte\n      const matches = flightClassType === classType;\n      \n      if (!matches) {\n        console.log(`Vol filtré: classe ${flightClassType} (${flightClassName}) ne correspond pas à la classe sélectionnée ${classType}`);\n      }\n      \n      return matches;\n    });\n  }\n\n  /**\n   * Vérifie si un vol est de la classe spécifiée\n   * @param flight Vol à vérifier\n   * @param classType Type de classe à vérifier\n   * @returns true si le vol est de la classe spécifiée\n   */\n  isFlightOfClass(flight: Flight, classType: number): boolean {\n    if (!flight.items || flight.items.length === 0 || !flight.items[0].flightClass) {\n      return false;\n    }\n\n    const flightClassType = typeof flight.items[0].flightClass.type === 'string' \n                          ? parseInt(flight.items[0].flightClass.type) \n                          : flight.items[0].flightClass.type;\n    const flightClassName = flight.items[0].flightClass.name || '';\n\n    // Vérification pour la classe Business\n    if (classType === FlightClassType.BUSINESS) {\n      return flightClassType === FlightClassType.BUSINESS || \n             flightClassName.toUpperCase().includes('BUSINESS');\n    }\n    \n    // Vérification pour la classe Economy\n    if (classType === FlightClassType.ECONOMY) {\n      return flightClassType === FlightClassType.ECONOMY || \n             flightClassName.toUpperCase().includes('ECONOMY');\n    }\n    \n    // Pour les autres classes, vérifier l'égalité exacte\n    return flightClassType === classType;\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,QAAQ,uBAAuB;;AAKvD,OAAM,MAAOC,mBAAmB;EAE9BC,YAAA,GAAgB;EAEhB;;;;;;EAMAC,aAAaA,CAACC,OAAiB,EAAEC,SAAiB;IAChD,IAAI,CAACD,OAAO,IAAIA,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;MACpC,OAAO,EAAE;;IAGXC,OAAO,CAACC,GAAG,CAAC,iCAAiCH,SAAS,EAAE,CAAC;IAEzD,OAAOD,OAAO,CAACK,MAAM,CAACC,MAAM,IAAG;MAC7B;MACA,IAAI,CAACA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACL,MAAM,KAAK,CAAC,EAAE;QAC9C,OAAO,KAAK;;MAGd;MACA,IAAI,CAACI,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;QAChC,OAAO,KAAK;;MAGd;MACA,MAAMC,eAAe,GAAG,OAAOH,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAACE,IAAI,KAAK,QAAQ,GACpDC,QAAQ,CAACL,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAACE,IAAI,CAAC,GAC1CJ,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAACE,IAAI;MACxD,MAAME,eAAe,GAAGN,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAACK,IAAI,IAAI,EAAE;MAE9D;MACA,IAAIZ,SAAS,KAAKL,eAAe,CAACkB,QAAQ,EAAE;QAC1C,MAAMC,eAAe,GAAGN,eAAe,KAAKb,eAAe,CAACkB,QAAQ,IAC7CF,eAAe,CAACI,WAAW,EAAE,CAACC,QAAQ,CAAC,UAAU,CAAC;QAEzE,IAAI,CAACF,eAAe,EAAE;UACpBZ,OAAO,CAACC,GAAG,CAAC,sBAAsBK,eAAe,KAAKG,eAAe,sBAAsB,CAAC;;QAG9F,OAAOG,eAAe;;MAGxB;MACA,IAAId,SAAS,KAAKL,eAAe,CAACsB,OAAO,EAAE;QACzC,MAAMC,cAAc,GAAGV,eAAe,KAAKb,eAAe,CAACsB,OAAO,IAC5CN,eAAe,CAACI,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAAC;QAEvE,IAAI,CAACE,cAAc,EAAE;UACnBhB,OAAO,CAACC,GAAG,CAAC,sBAAsBK,eAAe,KAAKG,eAAe,qBAAqB,CAAC;;QAG7F,OAAOO,cAAc;;MAGvB;MACA,MAAMC,OAAO,GAAGX,eAAe,KAAKR,SAAS;MAE7C,IAAI,CAACmB,OAAO,EAAE;QACZjB,OAAO,CAACC,GAAG,CAAC,sBAAsBK,eAAe,KAAKG,eAAe,gDAAgDX,SAAS,EAAE,CAAC;;MAGnI,OAAOmB,OAAO;IAChB,CAAC,CAAC;EACJ;EAEA;;;;;;EAMAC,eAAeA,CAACf,MAAc,EAAEL,SAAiB;IAC/C,IAAI,CAACK,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACL,MAAM,KAAK,CAAC,IAAI,CAACI,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;MAC9E,OAAO,KAAK;;IAGd,MAAMC,eAAe,GAAG,OAAOH,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAACE,IAAI,KAAK,QAAQ,GACpDC,QAAQ,CAACL,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAACE,IAAI,CAAC,GAC1CJ,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAACE,IAAI;IACxD,MAAME,eAAe,GAAGN,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAACK,IAAI,IAAI,EAAE;IAE9D;IACA,IAAIZ,SAAS,KAAKL,eAAe,CAACkB,QAAQ,EAAE;MAC1C,OAAOL,eAAe,KAAKb,eAAe,CAACkB,QAAQ,IAC5CF,eAAe,CAACI,WAAW,EAAE,CAACC,QAAQ,CAAC,UAAU,CAAC;;IAG3D;IACA,IAAIhB,SAAS,KAAKL,eAAe,CAACsB,OAAO,EAAE;MACzC,OAAOT,eAAe,KAAKb,eAAe,CAACsB,OAAO,IAC3CN,eAAe,CAACI,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAAC;;IAG1D;IACA,OAAOR,eAAe,KAAKR,SAAS;EACtC;;;uBAnGWJ,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAAyB,OAAA,EAAnBzB,mBAAmB,CAAA0B,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}