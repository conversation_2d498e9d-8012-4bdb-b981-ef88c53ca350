<div class="search-price-container">
  <!-- Contenu existant jusqu'à la ligne 919 -->
  <!-- ... -->

  <!-- Correction pour la ligne 919 -->
  <span class="baggage-specs" *ngIf="baggage.piece > 0">{{ baggage.piece }} piece(s)</span>

  <!-- Correction pour la section de durée de séjour -->
  <div class="stay-duration" *ngIf="getInboundSegments(flight).length > 0 && getOutboundSegments(flight).length > 0">
    <i class="fas fa-hotel"></i>
    <span>Stay duration: {{ calculateLayoverTime(
          {arrival: {date: getOutboundSegments(flight)[getOutboundSegments(flight).length-1]?.arrival?.date}},
          {departure: {date: getInboundSegments(flight)[0]?.departure?.date}}
        ) }}</span>
  </div>

  <!-- Assurez-vous que toutes les accolades sont correctement fermées -->
</div>
