{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/product.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/autocomplete\";\nimport * as i6 from \"@angular/material/core\";\nfunction SearchPriceComponent_mat_option_34_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r12.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_34_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 92);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r12.city);\n  }\n}\nconst _c0 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"fa-flag\": a0,\n    \"fa-city\": a1,\n    \"fa-building\": a2,\n    \"fa-home\": a3,\n    \"fa-plane\": a4\n  };\n};\nfunction SearchPriceComponent_mat_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 30)(1, \"div\", 84)(2, \"div\", 85);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 86);\n    i0.ɵɵtemplate(5, SearchPriceComponent_mat_option_34_span_5_Template, 2, 1, \"span\", 87);\n    i0.ɵɵtemplate(6, SearchPriceComponent_mat_option_34_span_6_Template, 2, 1, \"span\", 88);\n    i0.ɵɵelementStart(7, \"span\", 89);\n    i0.ɵɵelement(8, \"i\", 90);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r12);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r12.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r12.type === 5 && location_r12.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r12.type === 1, location_r12.type === 2, location_r12.type === 3, location_r12.type === 4, location_r12.type === 5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r12.type === 1 ? \"Country\" : location_r12.type === 2 ? \"City\" : location_r12.type === 3 ? \"Town\" : location_r12.type === 4 ? \"Village\" : location_r12.type === 5 ? \"Airport\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"i\", 94);\n    i0.ɵɵtext(2, \" Please select a departure location \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_mat_option_59_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r17.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_59_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 92);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r17.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 30)(1, \"div\", 84)(2, \"div\", 85);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 86);\n    i0.ɵɵtemplate(5, SearchPriceComponent_mat_option_59_span_5_Template, 2, 1, \"span\", 87);\n    i0.ɵɵtemplate(6, SearchPriceComponent_mat_option_59_span_6_Template, 2, 1, \"span\", 88);\n    i0.ɵɵelementStart(7, \"span\", 89);\n    i0.ɵɵelement(8, \"i\", 90);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r17);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r17.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r17.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r17.type === 5 && location_r17.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r17.type === 1, location_r17.type === 2, location_r17.type === 3, location_r17.type === 4, location_r17.type === 5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r17.type === 1 ? \"Country\" : location_r17.type === 2 ? \"City\" : location_r17.type === 3 ? \"Town\" : location_r17.type === 4 ? \"Village\" : location_r17.type === 5 ? \"Airport\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"i\", 94);\n    i0.ɵɵtext(2, \" Please select an arrival location \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"i\", 94);\n    i0.ɵɵtext(2, \" Please select a date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_option_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", flightClass_r22.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(flightClass_r22.label);\n  }\n}\nfunction SearchPriceComponent_i_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 95);\n  }\n}\nfunction SearchPriceComponent_span_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Search\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"div\", 97);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_166_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"div\", 103)(2, \"div\", 104);\n    i0.ɵɵelement(3, \"i\", 105)(4, \"div\", 106)(5, \"div\", 106)(6, \"div\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Searching for the best flights...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SearchPriceComponent_div_166_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"div\", 108);\n    i0.ɵɵelement(2, \"i\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Oops! Something went wrong\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 93);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_166_div_2_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.onSearch());\n    });\n    i0.ɵɵelement(8, \"i\", 110);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r24.errorMessage);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No flights found for your search. Please modify your criteria.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"span\", 118);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" flights found \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r29.searchResults.length);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"div\", 120);\n    i0.ɵɵelement(2, \"i\", 121);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Sort by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"select\", 122)(6, \"option\", 123);\n    i0.ɵɵtext(7, \"Price (lowest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"option\", 124);\n    i0.ɵɵtext(9, \"Duration (shortest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 125);\n    i0.ɵɵtext(11, \"Departure (earliest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 126);\n    i0.ɵɵtext(13, \"Arrival (earliest first)\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 179);\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", flight_r33.items[0].airline.thumbnailFull, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 180);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 181);\n    i0.ɵɵelement(1, \"i\", 182);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].flightProvider && flight_r33.items[0].flightProvider.displayName || flight_r33.items && flight_r33.items[0] && flight_r33.items[0].flightProvider && flight_r33.items[0].flightProvider.name || \"Provider \" + flight_r33.provider, \" \");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 183);\n    i0.ɵɵelement(1, \"i\", 184);\n    i0.ɵɵtext(2, \" Direct \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 183);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r33.items[0].flightClass.name, \" \");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 185);\n    i0.ɵɵelement(1, \"i\", 186);\n    i0.ɵɵtext(2, \" Branded Fare \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"fa-check-circle\": a0,\n    \"fa-exclamation-triangle\": a1\n  };\n};\nfunction SearchPriceComponent_div_166_div_3_div_9_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 187);\n    i0.ɵɵelement(1, \"i\", 90);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c1, flight_r33.offers[0].availability > 0, flight_r33.offers[0].availability <= 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Availability: \", flight_r33.offers[0].availability, \" \");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 188);\n    i0.ɵɵelement(1, \"i\", 160);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    const ctx_r41 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Expires: \", ctx_r41.formatExpirationDate(flight_r33.offers[0].expiresOn), \" \");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 189)(1, \"span\", 190);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r33.items[0].stopCount);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" stop\", flight_r33.items[0].stopCount > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 191);\n    i0.ɵɵtext(1, \" Direct flight \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_55_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 195)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const baggage_r58 = ctx.$implicit;\n    const ctx_r56 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r56.getBaggageTypeName(baggage_r58.baggageType));\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_55_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 195)(1, \"span\");\n    i0.ɵɵtext(2, \"No baggage information available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 68);\n    i0.ɵɵtext(3, \" Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_166_div_3_div_9_div_55_div_4_Template, 3, 1, \"div\", 193);\n    i0.ɵɵpipe(5, \"slice\");\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_166_div_3_div_9_div_55_div_6_Template, 3, 0, \"div\", 194);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(5, 2, flight_r33.items[0].baggageInformations, 0, 2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !flight_r33.items[0].baggageInformations || flight_r33.items[0].baggageInformations.length === 0);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_56_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 195)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const service_r61 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(service_r61.name || \"Service\");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 196);\n    i0.ɵɵtext(3, \" Services\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_166_div_3_div_9_div_56_div_4_Template, 3, 1, \"div\", 193);\n    i0.ɵɵpipe(5, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(5, 1, flight_r33.items[0].services, 0, 3));\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_57_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 195)(1, \"span\", 197);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"slice\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", i0.ɵɵpipeBind3(3, 1, flight_r33.offers[0].offerId, 0, 10), \"...\");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_57_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 195)(1, \"span\");\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(flight_r33.offers[0].reservableInfo.reservable ? \"fas fa-check-circle text-success\" : \"fas fa-times-circle text-danger\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r33.offers[0].reservableInfo.reservable ? \"Reservable\" : \"Not reservable\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"h4\");\n    i0.ɵɵelement(2, \"i\", 182);\n    i0.ɵɵtext(3, \" Offer Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_166_div_3_div_9_div_57_div_4_Template, 4, 5, \"div\", 194);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_166_div_3_div_9_div_57_div_5_Template, 4, 3, \"div\", 194);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers[0].offerId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers[0].reservableInfo);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_63_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 206)(1, \"span\", 207);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 208);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r71 = ctx.$implicit;\n    const ctx_r69 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r69.getPassengerTypeName(item_r71.passengerType), \" (x\", item_r71.passengerCount, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 3, item_r71.price.amount, item_r71.price.currency));\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_63_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 209)(1, \"span\", 210);\n    i0.ɵɵtext(2, \"Service Fee\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 211);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, flight_r33.offers[0].serviceFee.amount, flight_r33.offers[0].serviceFee.currency));\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_63_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 200)(1, \"h4\");\n    i0.ɵɵtext(2, \"Price Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_166_div_3_div_9_div_63_div_1_div_3_Template, 6, 6, \"div\", 201);\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_166_div_3_div_9_div_63_div_1_div_4_Template, 6, 4, \"div\", 202);\n    i0.ɵɵelementStart(5, \"div\", 203)(6, \"span\", 204);\n    i0.ɵɵtext(7, \"Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 205);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", flight_r33.offers[0].priceBreakDown.items);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers[0].serviceFee);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 3, flight_r33.offers[0].price.amount, flight_r33.offers[0].price.currency));\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 198);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_166_div_3_div_9_div_63_div_1_Template, 11, 6, \"div\", 199);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers[0].priceBreakDown && flight_r33.offers[0].priceBreakDown.items);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_64_div_6_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 227);\n    i0.ɵɵelement(1, \"i\", 228);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r79 = i0.ɵɵnextContext();\n    const segment_r76 = ctx_r79.$implicit;\n    const i_r77 = ctx_r79.index;\n    const flight_r33 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r78 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r78.calculateLayoverTime(segment_r76, flight_r33.items[0].segments[i_r77 + 1]), \" layover in \", segment_r76.arrival && segment_r76.arrival.city && segment_r76.arrival.city.name || \"connecting city\", \"\");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_64_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 217)(1, \"div\", 218)(2, \"span\", 219);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 220);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 221);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 222)(9, \"div\", 223)(10, \"div\", 148);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 149)(13, \"span\", 150);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 151);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 224)(18, \"div\", 153);\n    i0.ɵɵelement(19, \"span\", 154);\n    i0.ɵɵelementStart(20, \"div\", 155);\n    i0.ɵɵelement(21, \"span\", 156);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"span\", 158);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 159);\n    i0.ɵɵelement(24, \"i\", 160);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 225)(27, \"div\", 148);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 149)(30, \"span\", 150);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 151);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(34, SearchPriceComponent_div_166_div_3_div_9_div_64_div_6_div_34_Template, 4, 2, \"div\", 226);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r76 = ctx.$implicit;\n    const i_r77 = ctx.index;\n    const flight_r33 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r75 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Segment \", i_r77 + 1, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r76.airline && segment_r76.airline.name || \"Airline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r76.flightNo);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(segment_r76.departure ? ctx_r75.formatDate(segment_r76.departure.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r76.departure && segment_r76.departure.airport && segment_r76.departure.airport.code || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r76.departure && segment_r76.departure.city && segment_r76.departure.city.name || \"N/A\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r75.formatDuration(segment_r76.duration), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r76.arrival ? ctx_r75.formatDate(segment_r76.arrival.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(segment_r76.arrival && segment_r76.arrival.airport && segment_r76.arrival.airport.code || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r76.arrival && segment_r76.arrival.city && segment_r76.arrival.city.name || \"N/A\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r77 < flight_r33.items[0].segments.length - 1);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 212)(1, \"details\", 213)(2, \"summary\", 214);\n    i0.ɵɵelement(3, \"i\", 22);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 215);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_166_div_3_div_9_div_64_div_6_Template, 35, 11, \"div\", 216);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Flight Segments (\", flight_r33.items[0].segments.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", flight_r33.items[0].segments);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_65_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 235);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r33.offers[0].brandedFare.description, \" \");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_65_div_7_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 241);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r87 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", feature_r87.explanations[0].text, \" \");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_65_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 238)(1, \"div\", 239);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_166_div_3_div_9_div_65_div_7_div_3_div_3_Template, 2, 1, \"div\", 240);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r87 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r87.commercialName || \"Feature\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", feature_r87.explanations && feature_r87.explanations.length > 0);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_65_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 236)(1, \"h4\");\n    i0.ɵɵtext(2, \"Features\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_166_div_3_div_9_div_65_div_7_div_3_Template, 4, 2, \"div\", 237);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", flight_r33.offers[0].brandedFare.features);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 229)(1, \"details\", 230)(2, \"summary\", 231);\n    i0.ɵɵelement(3, \"i\", 186);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 232);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_166_div_3_div_9_div_65_div_6_Template, 2, 1, \"div\", 233);\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_166_div_3_div_9_div_65_div_7_Template, 4, 1, \"div\", 234);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", flight_r33.offers[0].brandedFare.name || \"Branded Fare\", \" Details \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers[0].brandedFare.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers[0].brandedFare.features && flight_r33.offers[0].brandedFare.features.length > 0);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r93 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 127)(1, \"div\", 128)(2, \"div\", 129)(3, \"div\", 130);\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_166_div_3_div_9_img_4_Template, 1, 1, \"img\", 131);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_166_div_3_div_9_i_5_Template, 1, 0, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 133)(7, \"span\", 134);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 135);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, SearchPriceComponent_div_166_div_3_div_9_span_11_Template, 3, 1, \"span\", 136);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 137);\n    i0.ɵɵtemplate(13, SearchPriceComponent_div_166_div_3_div_9_span_13_Template, 3, 0, \"span\", 138);\n    i0.ɵɵtemplate(14, SearchPriceComponent_div_166_div_3_div_9_span_14_Template, 3, 1, \"span\", 138);\n    i0.ɵɵtemplate(15, SearchPriceComponent_div_166_div_3_div_9_span_15_Template, 3, 0, \"span\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 140)(17, \"span\", 141);\n    i0.ɵɵtext(18, \"Price per person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 142);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, SearchPriceComponent_div_166_div_3_div_9_span_21_Template, 3, 5, \"span\", 143);\n    i0.ɵɵtemplate(22, SearchPriceComponent_div_166_div_3_div_9_span_22_Template, 3, 1, \"span\", 144);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 145)(24, \"div\", 146)(25, \"div\", 147)(26, \"div\", 148);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 149)(29, \"span\", 150);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 151);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 152)(34, \"div\", 153);\n    i0.ɵɵelement(35, \"span\", 154);\n    i0.ɵɵelementStart(36, \"div\", 155);\n    i0.ɵɵelement(37, \"span\", 156);\n    i0.ɵɵelementStart(38, \"span\", 157);\n    i0.ɵɵelement(39, \"i\", 105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(40, \"span\", 158);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 159);\n    i0.ɵɵelement(42, \"i\", 160);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(44, SearchPriceComponent_div_166_div_3_div_9_div_44_Template, 4, 2, \"div\", 161);\n    i0.ɵɵtemplate(45, SearchPriceComponent_div_166_div_3_div_9_div_45_Template, 2, 0, \"div\", 162);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 163)(47, \"div\", 148);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 149)(50, \"span\", 150);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"span\", 151);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(54, \"div\", 164);\n    i0.ɵɵtemplate(55, SearchPriceComponent_div_166_div_3_div_9_div_55_Template, 7, 6, \"div\", 165);\n    i0.ɵɵtemplate(56, SearchPriceComponent_div_166_div_3_div_9_div_56_Template, 6, 5, \"div\", 165);\n    i0.ɵɵtemplate(57, SearchPriceComponent_div_166_div_3_div_9_div_57_Template, 6, 2, \"div\", 165);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 166)(59, \"details\", 167)(60, \"summary\", 168);\n    i0.ɵɵelement(61, \"i\", 63);\n    i0.ɵɵtext(62, \" Price Breakdown \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(63, SearchPriceComponent_div_166_div_3_div_9_div_63_Template, 2, 1, \"div\", 169);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(64, SearchPriceComponent_div_166_div_3_div_9_div_64_Template, 7, 2, \"div\", 170);\n    i0.ɵɵtemplate(65, SearchPriceComponent_div_166_div_3_div_9_div_65_Template, 8, 3, \"div\", 171);\n    i0.ɵɵelementStart(66, \"div\", 172)(67, \"button\", 173);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_166_div_3_div_9_Template_button_click_67_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r93);\n      const flight_r33 = restoredCtx.$implicit;\n      const ctx_r92 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r92.viewOfferDetails(flight_r33));\n    });\n    i0.ɵɵelement(68, \"i\", 174);\n    i0.ɵɵtext(69, \" View Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"button\", 175);\n    i0.ɵɵelement(71, \"i\", 176);\n    i0.ɵɵtext(72, \" Select This Flight \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"button\", 177);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_166_div_3_div_9_Template_button_click_73_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r93);\n      const flight_r33 = restoredCtx.$implicit;\n      const ctx_r94 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r94.debugOfferStructure(flight_r33));\n    });\n    i0.ɵɵelement(74, \"i\", 178);\n    i0.ɵɵtext(75, \" Debug \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r33 = ctx.$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"unavailable\", !ctx_r31.isFlightAvailable(flight_r33));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].airline && flight_r33.items[0].airline.thumbnailFull);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].airline && flight_r33.items[0].airline.thumbnailFull));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].airline ? flight_r33.items[0].airline.name : \"Airline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] ? flight_r33.items[0].flightNo : \"N/A\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.provider || flight_r33.items && flight_r33.items[0] && flight_r33.items[0].flightProvider);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].stopCount === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].flightClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers && flight_r33.offers[0] && flight_r33.offers[0].hasBrand);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r31.getMinPrice(flight_r33));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers && flight_r33.offers.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers && flight_r33.offers.length > 0 && flight_r33.offers[0].expiresOn);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].departure ? ctx_r31.formatDate(flight_r33.items[0].departure.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].departure && flight_r33.items[0].departure.airport ? flight_r33.items[0].departure.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].departure && flight_r33.items[0].departure.city ? flight_r33.items[0].departure.city.name : \"N/A\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", flight_r33.items && flight_r33.items[0] ? ctx_r31.formatDuration(flight_r33.items[0].duration) : \"N/A\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].stopCount > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].stopCount === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].arrival ? ctx_r31.formatDate(flight_r33.items[0].arrival.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].arrival && flight_r33.items[0].arrival.airport ? flight_r33.items[0].arrival.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].arrival && flight_r33.items[0].arrival.city ? flight_r33.items[0].arrival.city.name : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].baggageInformations && flight_r33.items[0].baggageInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].services && flight_r33.items[0].services.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers && flight_r33.offers.length > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers && flight_r33.offers[0]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].segments && flight_r33.items[0].segments.length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.offers && flight_r33.offers[0] && flight_r33.offers[0].brandedFare);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !flight_r33.offers || flight_r33.offers.length === 0 || flight_r33.offers[0].availability === 0);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 242)(1, \"div\", 243);\n    i0.ɵɵelement(2, \"i\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No flights found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"We couldn't find any flights matching your search criteria. Try adjusting your search parameters.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 244)(8, \"div\", 245);\n    i0.ɵɵelement(9, \"i\", 40);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Try different dates\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 245);\n    i0.ɵɵelement(13, \"i\", 246);\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Try nearby airports\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 245);\n    i0.ɵɵelement(17, \"i\", 105);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Include flights with stops\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"div\", 112)(2, \"div\", 113)(3, \"h3\");\n    i0.ɵɵtext(4, \"Flight Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_166_div_3_p_5_Template, 2, 0, \"p\", 51);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_166_div_3_p_6_Template, 4, 1, \"p\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_166_div_3_div_7_Template, 14, 0, \"div\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 115);\n    i0.ɵɵtemplate(9, SearchPriceComponent_div_166_div_3_div_9_Template, 76, 29, \"div\", 116);\n    i0.ɵɵtemplate(10, SearchPriceComponent_div_166_div_3_div_10_Template, 20, 0, \"div\", 117);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.searchResults.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r25.searchResults);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r25.isLoading && !ctx_r25.errorMessage && ctx_r25.searchResults.length === 0);\n  }\n}\nfunction SearchPriceComponent_div_166_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_166_div_1_Template, 9, 0, \"div\", 99);\n    i0.ɵɵtemplate(2, SearchPriceComponent_div_166_div_2_Template, 10, 1, \"div\", 100);\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_166_div_3_Template, 11, 5, \"div\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.isLoading && ctx_r11.errorMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.isLoading && !ctx_r11.errorMessage);\n  }\n}\nexport class SearchPriceComponent {\n  constructor(fb, productService, router) {\n    this.fb = fb;\n    this.productService = productService;\n    this.router = router;\n    this.departureLocations = [];\n    this.arrivalLocations = [];\n    this.isLoading = false;\n    this.searchResults = [];\n    this.hasSearched = false;\n    this.errorMessage = '';\n    this.lastSearchId = '';\n    // Passenger type options\n    this.passengerTypes = [{\n      value: PassengerType.Adult,\n      label: 'Adult'\n    }, {\n      value: PassengerType.Child,\n      label: 'Child'\n    }, {\n      value: PassengerType.Infant,\n      label: 'Infant'\n    }];\n    // Flight class options\n    this.flightClasses = [{\n      value: FlightClassType.PROMO,\n      label: 'Promo'\n    }, {\n      value: FlightClassType.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClassType.BUSINESS,\n      label: 'Business'\n    }];\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required],\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required],\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required],\n      // Options de vol\n      flightClass: [0, Validators.required],\n      nonStop: [false],\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n  ngOnInit() {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n  // Méthode pour déboguer la structure des offres\n  debugOfferStructure(flight) {\n    console.group('Flight Offer Debug');\n    console.log('Flight ID:', flight.id);\n    console.log('Flight Provider:', flight.provider);\n    if (flight.offers && flight.offers.length > 0) {\n      console.log('Number of offers:', flight.offers.length);\n      flight.offers.forEach((offer, index) => {\n        console.group(`Offer ${index + 1}`);\n        console.log('Offer ID:', offer.offerId || offer.id);\n        console.log('Availability:', offer.availability);\n        console.log('Price:', offer.price);\n        console.log('Reservable:', offer.reservableInfo?.reservable);\n        console.log('Expires On:', offer.expiresOn);\n        console.log('Has Brand:', offer.hasBrand);\n        console.log('Full Offer Object:', offer);\n        console.groupEnd();\n      });\n    } else {\n      console.log('No offers available for this flight');\n    }\n    console.groupEnd();\n  }\n  preloadLocations() {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n  setupAutocomplete() {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.departureLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('departureLocation')?.setValue('');\n      });\n    });\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.arrivalLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('arrivalLocation')?.setValue('');\n      });\n    });\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  displayLocation(location) {\n    if (!location) return '';\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n  onSearch() {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n    const formValue = this.searchForm.value;\n    // Créer la requête de recherche entièrement dynamique\n    const request = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [{\n        id: formValue.departureLocation?.id || '',\n        type: formValue.departureLocationType\n      }],\n      ArrivalLocations: [{\n        id: formValue.arrivalLocation?.id || '',\n        type: formValue.arrivalLocationType\n      }],\n      Passengers: [{\n        type: formValue.passengerType,\n        count: formValue.passengerCount\n      }],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n    this.productService.searchPrice(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.header.success) {\n          this.searchResults = response.body.flights;\n          // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n          console.log('Full API Response:', JSON.stringify(response, null, 2));\n          // Analyser les données de disponibilité\n          if (response.body && response.body.flights && response.body.flights.length > 0) {\n            console.group('Availability Analysis');\n            console.log('Total flights:', response.body.flights.length);\n            // Compter les vols avec des offres\n            const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n            console.log('Flights with offers:', flightsWithOffers.length);\n            // Analyser les valeurs de disponibilité\n            const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability));\n            console.log('Availability values:', availabilityValues);\n            // Compter les différentes valeurs de disponibilité\n            const availabilityCounts = availabilityValues.reduce((acc, val) => {\n              acc[val] = (acc[val] || 0) + 1;\n              return acc;\n            }, {});\n            console.log('Availability counts:', availabilityCounts);\n            // Vérifier les vols réservables\n            const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true));\n            console.log('Reservable flights:', reservableFlights.length);\n            console.groupEnd();\n          }\n          // Vérifier si searchId existe dans le corps de la réponse\n          if (response.body && response.body.searchId) {\n            this.lastSearchId = response.body.searchId;\n            console.log('Search ID found in body.searchId:', this.lastSearchId);\n          }\n          // Vérifier si searchId existe dans l'en-tête de la réponse\n          else if (response.header && response.header.requestId) {\n            this.lastSearchId = response.header.requestId;\n            console.log('Search ID found in header.requestId:', this.lastSearchId);\n          }\n          // Vérifier d'autres emplacements possibles\n          else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n            this.lastSearchId = response.body.flights[0].id;\n            console.log('Using flight ID as search ID:', this.lastSearchId);\n          } else {\n            console.error('No search ID found in the response!');\n            console.log('Response structure:', Object.keys(response));\n            if (response.body) console.log('Body structure:', Object.keys(response.body));\n            if (response.header) console.log('Header structure:', Object.keys(response.header));\n          }\n        } else {\n          this.errorMessage = 'The search could not be completed. Please try again.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred during the search. Please try again.';\n        console.error('Error searching flights:', error);\n      }\n    });\n  }\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  // Formater la durée en heures et minutes\n  formatDuration(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n  // Formater la date pour l'affichage\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n    const minOffer = flight.offers.reduce((min, offer) => offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight) {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n    // Utiliser uniquement la valeur availability telle qu'elle est fournie par l'API\n    return flight.offers.some(offer => offer.availability > 0);\n  }\n  // Naviguer vers la page de détails de l'offre\n  viewOfferDetails(flight) {\n    console.log('Flight object:', flight);\n    if (flight && flight.offers && flight.offers.length > 0) {\n      console.log('Flight offers:', flight.offers);\n      // Vérifier la structure exacte de l'offre\n      const firstOffer = flight.offers[0];\n      console.log('First offer structure:', Object.keys(firstOffer));\n      console.log('Full offer object:', firstOffer);\n      // Essayer de trouver l'ID de l'offre dans différents champs possibles\n      let offerId;\n      // Vérifier d'abord le champ offerId direct\n      if (firstOffer.offerId) {\n        offerId = firstOffer.offerId;\n        console.log('Using offer.offerId:', offerId);\n      }\n      // Vérifier ensuite le tableau offerIds\n      else if (firstOffer.offerIds && firstOffer.offerIds.length > 0 && firstOffer.offerIds[0].offerId) {\n        offerId = firstOffer.offerIds[0].offerId;\n        console.log('Using offer.offerIds[0].offerId:', offerId);\n      }\n      // Vérifier ensuite le champ id\n      else if (firstOffer.id) {\n        offerId = firstOffer.id;\n        console.log('Using offer.id:', offerId);\n      } else if (flight.id) {\n        offerId = flight.id;\n        console.log('Using flight.id as fallback:', offerId);\n      } else {\n        // Générer un ID aléatoire comme dernier recours\n        offerId = 'offer-' + Math.random().toString(36).substring(2, 15);\n        console.log('Generated random offerId as last resort:', offerId);\n      }\n      console.log('Selected offerId:', offerId);\n      // Vérifier si l'ID de recherche est disponible\n      if (!this.lastSearchId) {\n        console.error('SearchId is missing or empty!');\n        // Essayer de trouver un ID alternatif\n        if (flight.id) {\n          console.log('Using flight.id as searchId:', flight.id);\n          this.lastSearchId = flight.id;\n        } else if (offerId) {\n          console.log('Using offerId as searchId:', offerId);\n          this.lastSearchId = offerId;\n        } else {\n          // Générer un ID de recherche aléatoire comme dernier recours\n          this.lastSearchId = 'search-' + Math.random().toString(36).substring(2, 15);\n          console.log('Generated random searchId as last resort:', this.lastSearchId);\n        }\n      }\n      console.log('Navigating to get-offer with searchId:', this.lastSearchId, 'and offerId:', offerId);\n      // S'assurer que les deux IDs sont définis\n      if (!offerId) {\n        alert('Offer ID is missing. Cannot view details.');\n        return;\n      }\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: this.lastSearchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations() {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations() {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations() {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString) {\n    if (!dateString) return 'N/A';\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType) {\n    switch (baggageType) {\n      case 1:\n        return 'Cabin Baggage';\n      case 2:\n        return 'Checked Baggage';\n      case 3:\n        return 'Hand Baggage';\n      default:\n        return 'Baggage';\n    }\n  }\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType) {\n    switch (passengerType) {\n      case 1:\n        return 'Adult';\n      case 2:\n        return 'Child';\n      case 3:\n        return 'Infant';\n      default:\n        return 'Passenger';\n    }\n  }\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment, nextSegment) {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date || !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n  static {\n    this.ɵfac = function SearchPriceComponent_Factory(t) {\n      return new (t || SearchPriceComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchPriceComponent,\n      selectors: [[\"app-search-price\"]],\n      decls: 167,\n      vars: 30,\n      consts: [[1, \"search-price-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-illustration\"], [\"src\", \"assets/images/airplane-banner.jpg\", \"alt\", \"Airplane in the sky\"], [1, \"search-content\"], [1, \"search-form-container\"], [1, \"sidebar-logo\"], [1, \"logo-container\"], [1, \"fas\", \"fa-plane-departure\", \"logo-icon\"], [1, \"logo-text\"], [1, \"search-form-header\"], [1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"search-card\"], [\"type\", \"hidden\", \"formControlName\", \"productType\", \"value\", \"3\"], [\"type\", \"hidden\", \"formControlName\", \"serviceTypes\", \"value\", \"['1']\"], [1, \"single-line-form\"], [1, \"form-group\"], [\"for\", \"departureLocation\"], [1, \"input-with-icon\"], [1, \"fas\", \"fa-plane-departure\"], [\"type\", \"text\", \"id\", \"departureLocation\", \"formControlName\", \"departureLocation\", \"placeholder\", \"City or airport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [3, \"displayWith\"], [\"departureAuto\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"form-group\", \"location-type-selector\"], [\"id\", \"departureLocationType\", \"formControlName\", \"departureLocationType\", \"aria-label\", \"Departure Location Type\", 1, \"form-control\"], [3, \"value\"], [1, \"swap-button-container\"], [\"type\", \"button\", 1, \"swap-locations-btn\", 3, \"click\"], [1, \"fas\", \"fa-exchange-alt\"], [\"for\", \"arrivalLocation\"], [1, \"fas\", \"fa-plane-arrival\"], [\"type\", \"text\", \"id\", \"arrivalLocation\", \"formControlName\", \"arrivalLocation\", \"placeholder\", \"City or airport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [\"arrivalAuto\", \"matAutocomplete\"], [\"id\", \"arrivalLocationType\", \"formControlName\", \"arrivalLocationType\", \"aria-label\", \"Arrival Location Type\", 1, \"form-control\"], [\"for\", \"departureDate\"], [1, \"fas\", \"fa-calendar-alt\"], [\"type\", \"date\", \"id\", \"departureDate\", \"formControlName\", \"departureDate\", 1, \"form-control\", 3, \"min\"], [\"for\", \"passengerCount\"], [1, \"fas\", \"fa-user-friends\"], [\"type\", \"number\", \"id\", \"passengerCount\", \"formControlName\", \"passengerCount\", \"min\", \"1\", \"max\", \"9\", 1, \"form-control\"], [\"for\", \"flightClass\"], [1, \"fas\", \"fa-chair\"], [\"id\", \"flightClass\", \"formControlName\", \"flightClass\", 1, \"form-control\"], [1, \"search-button-container\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [\"class\", \"fas fa-search\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [1, \"advanced-options-container\"], [1, \"fas\", \"fa-cog\"], [1, \"advanced-options\"], [1, \"form-row\"], [\"for\", \"culture\"], [1, \"fas\", \"fa-language\"], [\"id\", \"culture\", \"formControlName\", \"culture\", 1, \"form-control\"], [\"value\", \"en-US\"], [\"value\", \"fr-FR\"], [\"for\", \"currency\"], [1, \"fas\", \"fa-money-bill-wave\"], [\"id\", \"currency\", \"formControlName\", \"currency\", 1, \"form-control\"], [\"value\", \"EUR\"], [\"value\", \"USD\"], [\"for\", \"flightBaggageGetOption\"], [1, \"fas\", \"fa-suitcase\"], [\"id\", \"flightBaggageGetOption\", \"formControlName\", \"flightBaggageGetOption\", 1, \"form-control\"], [1, \"form-row\", \"checkbox-options\"], [1, \"form-group\", \"checkbox-group\"], [1, \"toggle-switch\", \"small\"], [\"type\", \"checkbox\", \"id\", \"acceptPendingProviders\", \"formControlName\", \"acceptPendingProviders\", 1, \"toggle-input\"], [\"for\", \"acceptPendingProviders\", 1, \"toggle-label\"], [1, \"toggle-inner\"], [1, \"toggle-switch-label\"], [\"type\", \"checkbox\", \"id\", \"forceFlightBundlePackage\", \"formControlName\", \"forceFlightBundlePackage\", 1, \"toggle-input\"], [\"for\", \"forceFlightBundlePackage\", 1, \"toggle-label\"], [\"type\", \"checkbox\", \"id\", \"disablePackageOfferTotalPrice\", \"formControlName\", \"disablePackageOfferTotalPrice\", 1, \"toggle-input\"], [\"for\", \"disablePackageOfferTotalPrice\", 1, \"toggle-label\"], [\"type\", \"checkbox\", \"id\", \"calculateFlightFees\", \"formControlName\", \"calculateFlightFees\", 1, \"toggle-input\"], [\"for\", \"calculateFlightFees\", 1, \"toggle-label\"], [\"class\", \"search-results-container\", 4, \"ngIf\"], [1, \"location-option\"], [1, \"location-name\"], [1, \"location-details\"], [\"class\", \"location-code\", 4, \"ngIf\"], [\"class\", \"location-city\", 4, \"ngIf\"], [1, \"location-type\"], [1, \"fas\", 3, \"ngClass\"], [1, \"location-code\"], [1, \"location-city\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"fas\", \"fa-search\"], [1, \"spinner-container\"], [1, \"spinner\"], [1, \"search-results-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"search-results-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-animation\"], [1, \"plane-loader\"], [1, \"fas\", \"fa-plane\"], [1, \"cloud\"], [1, \"error-container\"], [1, \"error-icon\"], [1, \"retry-button\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"search-results-content\"], [1, \"results-header\"], [1, \"results-title\"], [\"class\", \"results-filters\", 4, \"ngIf\"], [1, \"flight-list\"], [\"class\", \"flight-card\", 3, \"unavailable\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"results-count\"], [1, \"results-filters\"], [1, \"filter-option\"], [1, \"fas\", \"fa-sort-amount-down\"], [1, \"filter-select\"], [\"value\", \"price\"], [\"value\", \"duration\"], [\"value\", \"departure\"], [\"value\", \"arrival\"], [1, \"flight-card\"], [1, \"flight-header\"], [1, \"airline-info\"], [1, \"airline-logo-container\"], [\"alt\", \"Airline logo\", \"class\", \"airline-logo\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"fas fa-plane airline-icon\", 4, \"ngIf\"], [1, \"airline-details\"], [1, \"airline-name\"], [1, \"flight-number\"], [\"class\", \"provider-info\", 4, \"ngIf\"], [1, \"flight-badges\"], [\"class\", \"flight-badge\", 4, \"ngIf\"], [\"class\", \"flight-badge branded\", 4, \"ngIf\"], [1, \"flight-price\"], [1, \"price-label\"], [1, \"price\"], [\"class\", \"availability\", 4, \"ngIf\"], [\"class\", \"expiration\", 4, \"ngIf\"], [1, \"flight-details\"], [1, \"flight-route\"], [1, \"departure\"], [1, \"time\"], [1, \"location\"], [1, \"airport-code\"], [1, \"city-name\"], [1, \"flight-duration\"], [1, \"duration-line\"], [1, \"dot\", \"departure-dot\"], [1, \"line-container\"], [1, \"line\"], [1, \"plane-icon\"], [1, \"dot\", \"arrival-dot\"], [1, \"duration-text\"], [1, \"fas\", \"fa-clock\"], [\"class\", \"stops\", 4, \"ngIf\"], [\"class\", \"stops direct\", 4, \"ngIf\"], [1, \"arrival\"], [1, \"flight-features\"], [\"class\", \"feature-group\", 4, \"ngIf\"], [1, \"price-breakdown-section\"], [1, \"price-breakdown-details\"], [1, \"price-breakdown-summary\"], [\"class\", \"price-breakdown-content\", 4, \"ngIf\"], [\"class\", \"segments-section\", 4, \"ngIf\"], [\"class\", \"branded-fare-section\", 4, \"ngIf\"], [1, \"flight-actions\"], [1, \"view-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [1, \"select-button\", 3, \"disabled\"], [1, \"fas\", \"fa-check-circle\"], [\"title\", \"Debug Offer Structure\", 1, \"debug-button\", 3, \"click\"], [1, \"fas\", \"fa-bug\"], [\"alt\", \"Airline logo\", 1, \"airline-logo\", 3, \"src\"], [1, \"fas\", \"fa-plane\", \"airline-icon\"], [1, \"provider-info\"], [1, \"fas\", \"fa-tag\"], [1, \"flight-badge\"], [1, \"fas\", \"fa-bolt\"], [1, \"flight-badge\", \"branded\"], [1, \"fas\", \"fa-certificate\"], [1, \"availability\"], [1, \"expiration\"], [1, \"stops\"], [1, \"stop-count\"], [1, \"stops\", \"direct\"], [1, \"feature-group\"], [\"class\", \"feature\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"feature\", 4, \"ngIf\"], [1, \"feature\"], [1, \"fas\", \"fa-concierge-bell\"], [1, \"offer-id\"], [1, \"price-breakdown-content\"], [\"class\", \"breakdown-group\", 4, \"ngIf\"], [1, \"breakdown-group\"], [\"class\", \"breakdown-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"breakdown-item service-fee\", 4, \"ngIf\"], [1, \"breakdown-total\"], [1, \"total-label\"], [1, \"total-amount\"], [1, \"breakdown-item\"], [1, \"passenger-type\"], [1, \"item-price\"], [1, \"breakdown-item\", \"service-fee\"], [1, \"fee-label\"], [1, \"fee-amount\"], [1, \"segments-section\"], [1, \"segments-details\"], [1, \"segments-summary\"], [1, \"segments-content\"], [\"class\", \"segment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"segment-item\"], [1, \"segment-header\"], [1, \"segment-number\"], [1, \"segment-airline\"], [1, \"segment-flight\"], [1, \"segment-route\"], [1, \"segment-departure\"], [1, \"segment-duration\"], [1, \"segment-arrival\"], [\"class\", \"layover-info\", 4, \"ngIf\"], [1, \"layover-info\"], [1, \"fas\", \"fa-hourglass-half\"], [1, \"branded-fare-section\"], [1, \"branded-fare-details\"], [1, \"branded-fare-summary\"], [1, \"branded-fare-content\"], [\"class\", \"branded-fare-description\", 4, \"ngIf\"], [\"class\", \"branded-fare-features\", 4, \"ngIf\"], [1, \"branded-fare-description\"], [1, \"branded-fare-features\"], [\"class\", \"feature-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature-item\"], [1, \"feature-name\"], [\"class\", \"feature-description\", 4, \"ngIf\"], [1, \"feature-description\"], [1, \"no-results\"], [1, \"no-results-icon\"], [1, \"no-results-suggestions\"], [1, \"suggestion\"], [1, \"fas\", \"fa-map-marker-alt\"]],\n      template: function SearchPriceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Find Your Perfect Flight\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Search and compare flights to destinations worldwide\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵelement(8, \"img\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10);\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementStart(14, \"span\", 12);\n          i0.ɵɵtext(15, \"TravelEase\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 13)(17, \"h2\");\n          i0.ɵɵtext(18, \"Search Flights\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"p\");\n          i0.ɵɵtext(20, \"Enter your travel details below\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"form\", 14);\n          i0.ɵɵlistener(\"ngSubmit\", function SearchPriceComponent_Template_form_ngSubmit_21_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(22, \"div\", 15);\n          i0.ɵɵelement(23, \"input\", 16)(24, \"input\", 17);\n          i0.ɵɵelementStart(25, \"div\", 18)(26, \"div\", 19)(27, \"label\", 20);\n          i0.ɵɵtext(28, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 21);\n          i0.ɵɵelement(30, \"i\", 22);\n          i0.ɵɵelementStart(31, \"input\", 23);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_31_listener() {\n            return ctx.showAllDepartureLocations();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"mat-autocomplete\", 24, 25);\n          i0.ɵɵtemplate(34, SearchPriceComponent_mat_option_34_Template, 10, 12, \"mat-option\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, SearchPriceComponent_div_35_Template, 3, 0, \"div\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 28)(37, \"select\", 29)(38, \"option\", 30);\n          i0.ɵɵtext(39, \"Country (1)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"option\", 30);\n          i0.ɵɵtext(41, \"City (2)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"option\", 30);\n          i0.ɵɵtext(43, \"Town (3)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"option\", 30);\n          i0.ɵɵtext(45, \"Village (4)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"option\", 30);\n          i0.ɵɵtext(47, \"Airport (5)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 31)(49, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_49_listener() {\n            return ctx.swapLocations();\n          });\n          i0.ɵɵelement(50, \"i\", 33);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 19)(52, \"label\", 34);\n          i0.ɵɵtext(53, \"To\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 21);\n          i0.ɵɵelement(55, \"i\", 35);\n          i0.ɵɵelementStart(56, \"input\", 36);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_56_listener() {\n            return ctx.showAllArrivalLocations();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"mat-autocomplete\", 24, 37);\n          i0.ɵɵtemplate(59, SearchPriceComponent_mat_option_59_Template, 10, 12, \"mat-option\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(60, SearchPriceComponent_div_60_Template, 3, 0, \"div\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 28)(62, \"select\", 38)(63, \"option\", 30);\n          i0.ɵɵtext(64, \"Country (1)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"option\", 30);\n          i0.ɵɵtext(66, \"City (2)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"option\", 30);\n          i0.ɵɵtext(68, \"Town (3)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"option\", 30);\n          i0.ɵɵtext(70, \"Village (4)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"option\", 30);\n          i0.ɵɵtext(72, \"Airport (5)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"div\", 19)(74, \"label\", 39);\n          i0.ɵɵtext(75, \"Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 21);\n          i0.ɵɵelement(77, \"i\", 40)(78, \"input\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(79, SearchPriceComponent_div_79_Template, 3, 0, \"div\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"div\", 19)(81, \"label\", 42);\n          i0.ɵɵtext(82, \"Passengers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"div\", 21);\n          i0.ɵɵelement(84, \"i\", 43)(85, \"input\", 44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"div\", 19)(87, \"label\", 45);\n          i0.ɵɵtext(88, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"div\", 21);\n          i0.ɵɵelement(90, \"i\", 46);\n          i0.ɵɵelementStart(91, \"select\", 47);\n          i0.ɵɵtemplate(92, SearchPriceComponent_option_92_Template, 2, 2, \"option\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(93, \"div\", 48)(94, \"button\", 49);\n          i0.ɵɵtemplate(95, SearchPriceComponent_i_95_Template, 1, 0, \"i\", 50);\n          i0.ɵɵtemplate(96, SearchPriceComponent_span_96_Template, 2, 0, \"span\", 51);\n          i0.ɵɵtemplate(97, SearchPriceComponent_div_97_Template, 2, 0, \"div\", 52);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(98, \"div\", 53)(99, \"details\")(100, \"summary\");\n          i0.ɵɵelement(101, \"i\", 54);\n          i0.ɵɵtext(102, \" Advanced Options \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"div\", 55)(104, \"div\", 56)(105, \"div\", 19)(106, \"label\", 57);\n          i0.ɵɵtext(107, \"Language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"div\", 21);\n          i0.ɵɵelement(109, \"i\", 58);\n          i0.ɵɵelementStart(110, \"select\", 59)(111, \"option\", 60);\n          i0.ɵɵtext(112, \"English (US)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"option\", 61);\n          i0.ɵɵtext(114, \"Fran\\u00E7ais\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(115, \"div\", 19)(116, \"label\", 62);\n          i0.ɵɵtext(117, \"Currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"div\", 21);\n          i0.ɵɵelement(119, \"i\", 63);\n          i0.ɵɵelementStart(120, \"select\", 64)(121, \"option\", 65);\n          i0.ɵɵtext(122, \"Euro (\\u20AC)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(123, \"option\", 66);\n          i0.ɵɵtext(124, \"Dollar ($)\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(125, \"div\", 19)(126, \"label\", 67);\n          i0.ɵɵtext(127, \"Baggage Options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"div\", 21);\n          i0.ɵɵelement(129, \"i\", 68);\n          i0.ɵɵelementStart(130, \"select\", 69)(131, \"option\", 30);\n          i0.ɵɵtext(132, \"All options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(133, \"option\", 30);\n          i0.ɵɵtext(134, \"Baggage included only\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(135, \"option\", 30);\n          i0.ɵɵtext(136, \"No baggage only\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(137, \"div\", 70)(138, \"div\", 71)(139, \"div\", 72);\n          i0.ɵɵelement(140, \"input\", 73);\n          i0.ɵɵelementStart(141, \"label\", 74);\n          i0.ɵɵelement(142, \"span\", 75);\n          i0.ɵɵelementStart(143, \"span\", 76);\n          i0.ɵɵtext(144, \"Accept pending providers\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(145, \"div\", 71)(146, \"div\", 72);\n          i0.ɵɵelement(147, \"input\", 77);\n          i0.ɵɵelementStart(148, \"label\", 78);\n          i0.ɵɵelement(149, \"span\", 75);\n          i0.ɵɵelementStart(150, \"span\", 76);\n          i0.ɵɵtext(151, \"Force flight bundle package\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(152, \"div\", 71)(153, \"div\", 72);\n          i0.ɵɵelement(154, \"input\", 79);\n          i0.ɵɵelementStart(155, \"label\", 80);\n          i0.ɵɵelement(156, \"span\", 75);\n          i0.ɵɵelementStart(157, \"span\", 76);\n          i0.ɵɵtext(158, \"Disable package offer total price\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(159, \"div\", 71)(160, \"div\", 72);\n          i0.ɵɵelement(161, \"input\", 81);\n          i0.ɵɵelementStart(162, \"label\", 82);\n          i0.ɵɵelement(163, \"span\", 75);\n          i0.ɵɵelementStart(164, \"span\", 76);\n          i0.ɵɵtext(165, \"Calculate flight fees\");\n          i0.ɵɵelementEnd()()()()()()()()()();\n          i0.ɵɵtemplate(166, SearchPriceComponent_div_166_Template, 4, 3, \"div\", 83);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(33);\n          const _r3 = i0.ɵɵreference(58);\n          let tmp_4_0;\n          let tmp_13_0;\n          let tmp_20_0;\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.departureLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.searchForm.get(\"departureLocation\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.searchForm.get(\"departureLocation\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 3);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 5);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"matAutocomplete\", _r3);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrivalLocations);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_13_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 3);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 5);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"min\", ctx.minDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = ctx.searchForm.get(\"departureDate\")) == null ? null : tmp_20_0.invalid) && ((tmp_20_0 = ctx.searchForm.get(\"departureDate\")) == null ? null : tmp_20_0.touched));\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.searchForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(31);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasSearched);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i5.MatAutocomplete, i6.MatOption, i5.MatAutocompleteTrigger, i4.SlicePipe, i4.CurrencyPipe],\n      styles: [\"/* Styles globaux pour \\u00E9viter le d\\u00E9filement horizontal */\\n* {\\n  box-sizing: border-box;\\n}\\n\\n/* Conteneur principal - \\u00C9vite le d\\u00E9filement horizontal */\\n.search-price-container {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 10px;\\n  width: 100%;\\n  max-width: 100%; /* Utilisation de toute la largeur de l'\\u00E9cran */\\n  margin: 0;\\n  position: relative;\\n  z-index: 1;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-price-container {\\n    padding: 0;\\n    margin: 0;\\n  }\\n}\\n\\n.search-price-container::before {\\n  content: '';\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background:\\n    linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.03) 0%, rgba(var(--primary-color-rgb), 0) 50%),\\n    radial-gradient(circle at top right, rgba(var(--secondary-color-rgb), 0.03) 0%, rgba(var(--secondary-color-rgb), 0) 70%);\\n  z-index: -1;\\n  pointer-events: none;\\n}\\n\\n/* En-t\\u00EAte de page */\\n.page-header {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  margin-bottom: 30px;\\n  position: relative;\\n  overflow: hidden;\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.page-header::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.1));\\n  z-index: 1;\\n}\\n\\n.header-content {\\n  max-width: 800px;\\n  padding: 40px 20px;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 2;\\n  color: white;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n.page-title {\\n  font-size: 36px;\\n  font-weight: 700;\\n  color: white;\\n  margin-bottom: 15px;\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.page-title::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: white;\\n  border-radius: 3px;\\n}\\n\\n.page-subtitle {\\n  font-size: 18px;\\n  color: rgba(255, 255, 255, 0.9);\\n  line-height: 1.5;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  margin-top: 20px;\\n}\\n\\n.header-illustration {\\n  width: 100%;\\n  height: 300px;\\n  overflow: hidden;\\n}\\n\\n.header-illustration img {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n  transition: transform 10s ease;\\n}\\n\\n.page-header:hover .header-illustration img {\\n  transform: scale(1.1);\\n}\\n\\n/* Disposition pour les \\u00E9crans larges - Carte de recherche horizontale en haut */\\n@media (min-width: 992px) {\\n  .page-header {\\n    display: none;\\n  }\\n\\n  .search-price-container {\\n    flex-direction: column;\\n    align-items: stretch;\\n    padding: 0;\\n    margin: 0;\\n    width: 100%;\\n  }\\n\\n  .search-content {\\n    display: flex;\\n    flex-direction: column;\\n    width: 100%;\\n    gap: 0;\\n  }\\n\\n  .search-form-container {\\n    position: sticky;\\n    top: 0;\\n    width: 100%;\\n    max-height: none;\\n    overflow: visible;\\n    margin-bottom: 20px;\\n    padding: 0;\\n    border-radius: 0;\\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n    z-index: 100;\\n    background-color: white;\\n  }\\n\\n  .search-results-container {\\n    width: 100%;\\n    margin-left: 0;\\n    padding: 20px;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n  }\\n}\\n\\n/* Formulaire de recherche */\\n.search-form-container {\\n  background-color: white;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  padding: 25px;\\n  margin-bottom: 30px;\\n}\\n\\n/* Logo pour la version desktop */\\n.sidebar-logo {\\n  display: none;\\n}\\n\\n/* Style sp\\u00E9cifique pour les \\u00E9crans larges */\\n@media (min-width: 992px) {\\n  .search-form-container {\\n    padding: 0;\\n    margin-bottom: 20px;\\n    border-radius: 0;\\n  }\\n\\n  .sidebar-logo {\\n    display: block;\\n    background-color: var(--primary-color);\\n    color: white;\\n    padding: 15px 20px;\\n    text-align: left;\\n  }\\n\\n  .logo-container {\\n    display: flex;\\n    align-items: center;\\n    gap: 10px;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n    width: 100%;\\n  }\\n\\n  .logo-icon {\\n    font-size: 22px;\\n  }\\n\\n  .logo-text {\\n    font-size: 22px;\\n    font-weight: 700;\\n    letter-spacing: 0.5px;\\n  }\\n}\\n\\n.search-form-header {\\n  margin-bottom: 25px;\\n  text-align: center;\\n  padding: 20px 20px 0 20px;\\n}\\n\\n.search-form-header h2 {\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n  font-size: 26px;\\n  font-weight: 600;\\n}\\n\\n.search-form-header p {\\n  color: #666;\\n  font-size: 15px;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-form-header {\\n    max-width: 1200px;\\n    margin: 0 auto 15px auto;\\n    text-align: left;\\n    padding: 20px 20px 0 20px;\\n  }\\n}\\n\\n.search-form {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.form-group {\\n  margin-bottom: 15px;\\n}\\n\\n.form-row {\\n  display: flex;\\n  gap: 15px;\\n}\\n\\n.half-width {\\n  flex: 1;\\n}\\n\\nlabel {\\n  display: block;\\n  margin-bottom: 5px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 14px;\\n}\\n\\n.form-control {\\n  width: 100%;\\n  padding: 10px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 5px;\\n  font-size: 14px;\\n  transition: border-color 0.3s;\\n}\\n\\n.form-control:focus {\\n  border-color: #2989d8;\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(41, 137, 216, 0.2);\\n}\\n\\n.checkbox-group {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.checkbox-group input {\\n  margin-right: 8px;\\n}\\n\\n.search-button {\\n  padding: 12px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 5px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.3s;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-top: 10px;\\n}\\n\\n.search-button:hover {\\n  background-color: #1e5799;\\n}\\n\\n.search-button:disabled {\\n  background-color: #b3d4f0;\\n  cursor: not-allowed;\\n}\\n\\n.error-message {\\n  color: #e74c3c;\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n\\n/* Options avanc\\u00E9es */\\ndetails {\\n  margin-top: 10px;\\n  margin-bottom: 15px;\\n}\\n\\nsummary {\\n  cursor: pointer;\\n  color: #2989d8;\\n  font-weight: 500;\\n  padding: 5px 0;\\n}\\n\\nsummary:hover {\\n  text-decoration: underline;\\n}\\n\\n.advanced-options {\\n  margin-top: 10px;\\n  padding: 15px;\\n  background-color: #f8f9fa;\\n  border-radius: 5px;\\n  border: 1px solid #eee;\\n}\\n\\n/* Styles pour les listes d\\u00E9roulantes */\\n::ng-deep .mat-autocomplete-panel {\\n  max-height: 300px !important;\\n}\\n\\n::ng-deep .mat-option {\\n  height: auto !important;\\n  line-height: 1.2 !important;\\n  padding: 10px 16px !important;\\n}\\n\\n::ng-deep .mat-option small {\\n  color: #666;\\n  display: block;\\n  margin-top: 2px;\\n}\\n\\n/* R\\u00E9sultats de recherche - Design optimis\\u00E9 pour \\u00E9viter le d\\u00E9filement horizontal */\\n.search-results-container {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  box-shadow:\\n    0 5px 15px rgba(0, 0, 0, 0.08),\\n    0 0 0 1px rgba(var(--primary-color-rgb), 0.05);\\n  padding: 20px;\\n  position: relative;\\n  overflow-x: hidden;\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  box-sizing: border-box;\\n  width: 100%;\\n}\\n\\n.search-results-container::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 6px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\\n  z-index: 1;\\n}\\n\\n.loading-container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px;\\n  text-align: center;\\n}\\n\\n.loading-container p {\\n  margin-top: 20px;\\n  color: var(--primary-color);\\n  font-weight: 500;\\n  animation: pulse 1.5s infinite;\\n}\\n\\n.spinner {\\n  width: 30px;\\n  height: 30px;\\n  border: 3px solid rgba(var(--primary-color-rgb), 0.2);\\n  border-radius: 50%;\\n  border-top-color: var(--primary-color);\\n  animation: spin 1s cubic-bezier(0.6, 0.2, 0.4, 0.8) infinite;\\n  box-shadow: 0 0 10px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.spinner.large {\\n  width: 50px;\\n  height: 50px;\\n  border-width: 4px;\\n}\\n\\n@keyframes spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n.error-container {\\n  padding: 30px;\\n  background-color: rgba(231, 76, 60, 0.05);\\n  border-radius: 16px;\\n  text-align: center;\\n  border: 1px solid rgba(231, 76, 60, 0.1);\\n  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.05);\\n  animation: scaleIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\\n}\\n\\n.error-container h4 {\\n  color: #e74c3c;\\n  margin-bottom: 10px;\\n  font-size: 18px;\\n}\\n\\n.error-container p {\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.results-header {\\n  margin-bottom: 30px;\\n  position: relative;\\n  padding-bottom: 15px;\\n}\\n\\n.results-header::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0.1) 0%,\\n    rgba(var(--primary-color-rgb), 0.05) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n/* Provider information */\\n.provider-info {\\n  display: block;\\n  font-size: 12px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-top: 4px;\\n}\\n\\n/* Availability information */\\n.availability {\\n  font-size: 12px;\\n  display: block;\\n  margin-top: 4px;\\n}\\n\\n.availability i.fa-check-circle {\\n  color: #4CAF50;\\n}\\n\\n.availability i.fa-exclamation-triangle {\\n  color: #F44336;\\n}\\n\\n/* Expiration information */\\n.expiration {\\n  color: #FF9800;\\n  font-size: 12px;\\n  display: block;\\n  margin-top: 4px;\\n}\\n\\n/* Branded fare badge */\\n.flight-badge.branded {\\n  background-color: #9C27B0;\\n}\\n\\n/* Feature groups */\\n.feature-group {\\n  margin-bottom: 15px;\\n}\\n\\n.feature-group h4 {\\n  font-size: 14px;\\n  margin-bottom: 8px;\\n  color: rgba(0, 0, 0, 0.7);\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.feature-group .feature {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 4px;\\n  padding-left: 20px;\\n}\\n\\n/* Offer ID */\\n.offer-id {\\n  font-family: monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n}\\n\\n/* Price breakdown section */\\n.price-breakdown-section {\\n  margin: 15px 0;\\n}\\n\\n.price-breakdown-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.price-breakdown-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.price-breakdown-summary:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.price-breakdown-content {\\n  padding: 15px;\\n}\\n\\n.breakdown-group h4 {\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.breakdown-item {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 8px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.breakdown-item.service-fee {\\n  color: #FF5722;\\n}\\n\\n.breakdown-total {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-top: 10px;\\n  padding-top: 10px;\\n  border-top: 1px dashed rgba(0, 0, 0, 0.1);\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n}\\n\\n/* Segments section */\\n.segments-section {\\n  margin: 15px 0;\\n}\\n\\n.segments-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.segments-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.segments-summary:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.segments-content {\\n  padding: 15px;\\n}\\n\\n.segment-item {\\n  margin-bottom: 20px;\\n  padding-bottom: 15px;\\n  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);\\n}\\n\\n.segment-item:last-child {\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n  border-bottom: none;\\n}\\n\\n.segment-header {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 10px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.segment-number {\\n  font-weight: 600;\\n}\\n\\n.segment-route {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 10px;\\n}\\n\\n.layover-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 13px;\\n  color: #FF9800;\\n  margin-top: 10px;\\n  padding: 8px;\\n  background-color: rgba(255, 152, 0, 0.05);\\n  border-radius: 4px;\\n}\\n\\n/* Branded fare section */\\n.branded-fare-section {\\n  margin: 15px 0;\\n}\\n\\n.branded-fare-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.branded-fare-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(156, 39, 176, 0.05);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: #9C27B0;\\n}\\n\\n.branded-fare-summary:hover {\\n  background-color: rgba(156, 39, 176, 0.1);\\n}\\n\\n.branded-fare-content {\\n  padding: 15px;\\n}\\n\\n.branded-fare-description {\\n  margin-bottom: 15px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.branded-fare-features h4 {\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature-item {\\n  margin-bottom: 10px;\\n}\\n\\n.feature-name {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature-description {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n/* Bouton de d\\u00E9bogage */\\n.debug-button {\\n  background-color: #9C27B0;\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 8px 12px;\\n  font-size: 14px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin-top: 10px;\\n  transition: background-color 0.3s;\\n}\\n\\n.debug-button:hover {\\n  background-color: #7B1FA2;\\n}\\n\\n.debug-button i {\\n  font-size: 14px;\\n}\\n\\n.results-header h3 {\\n  color: var(--primary-dark);\\n  margin-bottom: 8px;\\n  font-size: 24px;\\n  font-weight: 600;\\n}\\n\\n.results-header p {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 14px;\\n}\\n\\n.flight-list {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 25px;\\n}\\n\\n.flight-card {\\n  background-color: white;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  box-shadow:\\n    0 2px 8px rgba(0, 0, 0, 0.05),\\n    0 0 0 1px rgba(0, 0, 0, 0.03);\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.flight-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.02) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  pointer-events: none;\\n}\\n\\n.flight-card:hover {\\n  transform: translateY(-8px) scale(1.01);\\n  box-shadow:\\n    0 15px 30px rgba(0, 0, 0, 0.08),\\n    0 0 0 1px rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n.flight-card.unavailable {\\n  opacity: 0.7;\\n  transform: none !important;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03) !important;\\n}\\n\\n.flight-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px;\\n  background-color: #f9f9f9;\\n  border-bottom: 1px solid #eaeaea;\\n  position: relative;\\n  flex-wrap: wrap;\\n}\\n\\n.flight-header::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -1px;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.airline-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.airline-logo {\\n  width: 40px;\\n  height: 40px;\\n  object-fit: contain;\\n  padding: 5px;\\n  background-color: white;\\n  border-radius: 50%;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .airline-logo {\\n  transform: scale(1.1);\\n}\\n\\n.airline-name {\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: var(--primary-dark);\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .airline-name {\\n  color: var(--primary-color);\\n}\\n\\n.flight-price {\\n  text-align: right;\\n  position: relative;\\n}\\n\\n.price {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: var(--primary-color);\\n  display: block;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n\\n.flight-card:hover .price {\\n  color: var(--secondary-color);\\n  transform: scale(1.05);\\n}\\n\\n.price::before {\\n  content: '';\\n  position: absolute;\\n  bottom: -3px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background-color: var(--secondary-color);\\n  transition: width 0.3s ease;\\n}\\n\\n.flight-card:hover .price::before {\\n  width: 100%;\\n}\\n\\n.availability {\\n  font-size: 13px;\\n  color: #e74c3c;\\n  font-weight: 500;\\n  margin-top: 5px;\\n}\\n\\n.flight-details {\\n  padding: 15px;\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.flight-route {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n  position: relative;\\n  width: 100%;\\n}\\n\\n@media (max-width: 768px) {\\n  .flight-route {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n}\\n\\n.departure, .arrival {\\n  flex: 1;\\n  position: relative;\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .departure {\\n  transform: translateX(-5px);\\n}\\n\\n.flight-card:hover .arrival {\\n  transform: translateX(5px);\\n}\\n\\n.time {\\n  font-size: 22px;\\n  font-weight: 700;\\n  margin-bottom: 8px;\\n  color: var(--primary-dark);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.time i {\\n  color: var(--primary-color);\\n  font-size: 18px;\\n  opacity: 0;\\n  transform: translateY(5px);\\n  transition: all 0.3s ease;\\n}\\n\\n.flight-card:hover .departure .time i {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.flight-card:hover .arrival .time i {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.location {\\n  font-size: 15px;\\n  color: rgba(0, 0, 0, 0.6);\\n  font-weight: 500;\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .location {\\n  color: var(--primary-color);\\n}\\n\\n.location small {\\n  display: block;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.4);\\n  margin-top: 3px;\\n}\\n\\n.flight-duration {\\n  flex: 1;\\n  text-align: center;\\n  padding: 0 20px;\\n  position: relative;\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .flight-duration {\\n  transform: translateY(-5px);\\n}\\n\\n.duration-line {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 10px;\\n  position: relative;\\n}\\n\\n.dot {\\n  width: 10px;\\n  height: 10px;\\n  background-color: var(--primary-color);\\n  border-radius: 50%;\\n  position: relative;\\n  z-index: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0 0 4px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.flight-card:hover .dot {\\n  background-color: var(--secondary-color);\\n  transform: scale(1.2);\\n  box-shadow: 0 0 0 6px rgba(var(--secondary-color-rgb), 0.15);\\n}\\n\\n.line {\\n  flex: 1;\\n  height: 2px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));\\n  margin: 0 8px;\\n  position: relative;\\n  transition: height 0.3s ease, background 0.3s ease;\\n}\\n\\n.flight-card:hover .line {\\n  height: 3px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\\n}\\n\\n.duration-text {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n  color: var(--primary-dark);\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .duration-text {\\n  color: var(--primary-color);\\n}\\n\\n.stops {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.5);\\n  font-weight: 500;\\n  padding: 4px 12px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  border-radius: 50px;\\n  display: inline-block;\\n  transition: all 0.3s ease;\\n}\\n\\n.flight-card:hover .stops {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n}\\n\\n.flight-info {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.5);\\n  margin-top: 20px;\\n  padding-top: 15px;\\n  border-top: 1px dashed rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.flight-actions {\\n  padding: 20px 25px;\\n  border-top: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  display: flex;\\n  gap: 15px;\\n  justify-content: flex-end;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.flight-actions::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.view-details-button {\\n  padding: 10px 18px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  color: var(--primary-dark);\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.1);\\n  border-radius: 50px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.view-details-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.1) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.view-details-button i {\\n  font-size: 16px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.view-details-button:hover {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.view-details-button:hover::before {\\n  opacity: 1;\\n}\\n\\n.view-details-button:hover i {\\n  transform: translateX(3px);\\n}\\n\\n.select-button {\\n  padding: 10px 24px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  color: white;\\n  border: none;\\n  border-radius: 50px;\\n  font-size: 15px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.select-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);\\n  z-index: 1;\\n}\\n\\n.select-button::after {\\n  content: '';\\n  position: absolute;\\n  top: -50%;\\n  left: -50%;\\n  width: 200%;\\n  height: 200%;\\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 60%);\\n  opacity: 0;\\n  transform: scale(0.5);\\n  transition: transform 0.8s ease, opacity 0.8s ease;\\n  z-index: 1;\\n}\\n\\n.select-button i {\\n  font-size: 16px;\\n  transition: transform 0.3s ease;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.select-button span {\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.select-button:hover {\\n  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));\\n  transform: translateY(-3px) scale(1.02);\\n  box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.select-button:hover::after {\\n  opacity: 1;\\n  transform: scale(1);\\n}\\n\\n.select-button:hover i {\\n  transform: translateX(3px);\\n  animation: pulse 1s infinite;\\n}\\n\\n.select-button:active {\\n  transform: translateY(-1px) scale(1);\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.select-button:disabled {\\n  background: linear-gradient(135deg, #b0b0b0, #d0d0d0);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  opacity: 0.7;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"/* Styles globaux pour \\u00E9viter le d\\u00E9filement horizontal */\\n.search-form * {\\n  box-sizing: border-box;\\n  max-width: 100%;\\n}\\n\\n.search-form input,\\n.search-form select,\\n.search-form button {\\n  max-width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n/* Styles pour la carte de recherche - Design professionnel inspir\\u00E9 des agences de voyage */\\n.search-card {\\n  background-color: var(--surface-color, white);\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n  padding: 20px;\\n  position: relative;\\n  margin-bottom: 20px;\\n  border: none;\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  box-sizing: border-box;\\n  overflow: hidden;\\n}\\n\\n/* Barre sup\\u00E9rieure bleue (style Booking.com) */\\n.search-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 4px;\\n  background-color: var(--primary-color);\\n  z-index: 1;\\n}\\n\\n/* Pas d'effet de survol exag\\u00E9r\\u00E9, juste une ombre l\\u00E9g\\u00E8rement plus prononc\\u00E9e */\\n.search-card:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n\\n/* Formulaire sur une seule ligne */\\n.single-line-form {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n  width: 100%;\\n}\\n\\n/* Disposition pour les \\u00E9crans larges - Carte de recherche horizontale */\\n@media (min-width: 992px) {\\n  .search-card {\\n    flex-direction: row;\\n    flex-wrap: nowrap;\\n    align-items: flex-end;\\n    padding: 15px;\\n    height: auto;\\n    border-radius: 0;\\n    box-shadow: none;\\n    margin-bottom: 0;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n  }\\n\\n  .single-line-form {\\n    flex-direction: row;\\n    align-items: flex-end;\\n    flex-wrap: nowrap;\\n    gap: 10px; /* Ajouter de l'espace entre les champs */\\n  }\\n\\n  /* Ajustement pour les champs From et To */\\n  .single-line-form .form-group:nth-child(1),\\n  .single-line-form .form-group:nth-child(5) {\\n    flex: 1.5;\\n    z-index: 4;\\n  }\\n\\n  /* Ajustement pour les autres champs */\\n  .single-line-form .form-group:nth-child(7),\\n  .single-line-form .form-group:nth-child(8),\\n  .single-line-form .form-group:nth-child(9) {\\n    flex: 0.8;\\n  }\\n\\n  /* Ajustement pour le conteneur du bouton d'\\u00E9change */\\n  .single-line-form .swap-button-container {\\n    flex: 0 0 auto;\\n    margin: 0;\\n    padding: 0 5px;\\n    z-index: 6;\\n  }\\n\\n  .form-group {\\n    min-width: 0; /* Permet aux \\u00E9l\\u00E9ments de r\\u00E9tr\\u00E9cir en dessous de leur largeur minimale */\\n    margin-bottom: 0;\\n    flex: 1;\\n    position: relative;\\n    z-index: 3;\\n    padding: 0 5px; /* Ajouter un peu d'espace de chaque c\\u00F4t\\u00E9 */\\n  }\\n\\n  .form-group.checkbox-group {\\n    flex: 0 0 auto;\\n  }\\n\\n  .search-button-container {\\n    flex: 0 0 auto;\\n    margin-top: 0;\\n    margin-left: 10px;\\n    text-align: center;\\n    padding-left: 5px;\\n  }\\n\\n  .search-button {\\n    width: auto;\\n    white-space: nowrap;\\n    padding: 10px 20px;\\n    height: 40px;\\n  }\\n}\\n\\n/* S\\u00E9lecteur de type de voyage - Style onglets (comme Booking.com) */\\n.trip-type-selector {\\n  display: flex;\\n  margin-bottom: 20px;\\n  position: relative;\\n  border-bottom: 1px solid #e7e7e7;\\n  padding-bottom: 0;\\n}\\n\\n.trip-type-option {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 20px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  font-weight: 500;\\n  color: #333;\\n  background-color: transparent;\\n  border: none;\\n  border-bottom: 3px solid transparent;\\n  position: relative;\\n  min-width: 100px;\\n  justify-content: center;\\n}\\n\\n.trip-type-option i {\\n  color: #666;\\n  font-size: 16px;\\n  transition: color 0.2s ease;\\n}\\n\\n.trip-type-option span {\\n  transition: color 0.2s ease;\\n}\\n\\n.trip-type-option.selected {\\n  color: var(--primary-color);\\n  font-weight: 600;\\n  border-bottom: 3px solid var(--primary-color);\\n  background-color: transparent;\\n}\\n\\n.trip-type-option.selected i {\\n  color: var(--primary-color);\\n}\\n\\n.trip-type-option:not(.selected):hover {\\n  color: var(--primary-color);\\n  border-bottom-color: rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.trip-type-option:not(.selected):hover i {\\n  color: var(--primary-color);\\n}\\n\\n.trip-type-option.disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n/* Rang\\u00E9es de formulaire - Style agences de voyage */\\n.form-row {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n\\n.locations-row {\\n  position: relative;\\n}\\n\\n/* Groupes de formulaire */\\n.form-group {\\n  flex: 1;\\n  position: relative;\\n}\\n\\n.location-type-selector {\\n  display: none; /* Cach\\u00E9 mais fonctionnel */\\n}\\n\\n/* \\u00C9tiquettes */\\nlabel {\\n  display: block;\\n  margin-bottom: 6px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 14px;\\n}\\n\\n/* Champs de saisie avec ic\\u00F4nes */\\n.input-with-icon {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.input-with-icon i {\\n  position: absolute;\\n  left: 12px;\\n  color: #666;\\n  font-size: 16px;\\n  z-index: 2;\\n}\\n\\n.form-control {\\n  width: 100%;\\n  padding: 12px 12px 12px 40px;\\n  border: 1px solid #e7e7e7;\\n  border-radius: 4px;\\n  font-size: 15px;\\n  transition: border-color 0.2s ease, box-shadow 0.2s ease;\\n  background-color: white;\\n  color: #333;\\n  height: 40px;\\n}\\n\\n.form-control:focus {\\n  outline: none;\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.form-control::placeholder {\\n  color: #999;\\n}\\n\\n/* Effet de focus sur le groupe entier */\\n.form-group:focus-within label {\\n  color: var(--primary-color);\\n}\\n\\n.form-group:focus-within .input-with-icon i {\\n  color: var(--primary-color);\\n}\\n\\n/* Ajustements pour le formulaire sur une seule ligne */\\n@media (min-width: 992px) {\\n  .single-line-form .form-group {\\n    margin-bottom: 0;\\n  }\\n\\n  .single-line-form label {\\n    font-size: 12px;\\n    margin-bottom: 4px;\\n  }\\n\\n  .single-line-form .form-control {\\n    font-size: 14px;\\n  }\\n\\n  .single-line-form .input-with-icon i {\\n    font-size: 14px;\\n  }\\n\\n  .single-line-form .error-message {\\n    position: absolute;\\n    font-size: 11px;\\n    bottom: -18px;\\n    left: 0;\\n    white-space: nowrap;\\n  }\\n}\\n\\n/* Conteneur du bouton d'\\u00E9change */\\n.swap-button-container {\\n  display: flex;\\n  align-items: flex-end;\\n  justify-content: center;\\n  padding-bottom: 10px; /* Aligner avec les champs de formulaire */\\n  position: relative;\\n  z-index: 5;\\n}\\n\\n/* Bouton d'\\u00E9change de lieux - Style agences de voyage */\\n.swap-locations-btn {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: var(--primary-color);\\n  color: white;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n\\n.swap-locations-btn:hover {\\n  background-color: var(--primary-dark);\\n  transform: rotate(180deg);\\n}\\n\\n.swap-locations-btn:active {\\n  transform: scale(0.95) rotate(180deg);\\n}\\n\\n.swap-locations-btn i {\\n  font-size: 16px;\\n}\\n\\n@media (min-width: 992px) {\\n  .swap-button-container {\\n    padding-bottom: 10px;\\n  }\\n}\\n\\n/* Options d'emplacement dans l'autocompl\\u00E9tion */\\n.location-option {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.location-name {\\n  font-weight: 500;\\n}\\n\\n.location-details {\\n  display: flex;\\n  gap: 12px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.location-code {\\n  font-weight: 500;\\n}\\n\\n.location-type {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.location-type i {\\n  font-size: 12px;\\n  color: #2989d8;\\n}\\n\\n/* Interrupteurs \\u00E0 bascule */\\n.toggle-switch {\\n  position: relative;\\n  display: inline-flex;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n\\n.toggle-input {\\n  opacity: 0;\\n  width: 0;\\n  height: 0;\\n  position: absolute;\\n}\\n\\n.toggle-label {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n}\\n\\n.toggle-inner {\\n  position: relative;\\n  display: inline-block;\\n  width: 50px;\\n  height: 24px;\\n  background-color: rgba(0, 0, 0, 0.12);\\n  border-radius: 12px;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.toggle-inner:before {\\n  content: '';\\n  position: absolute;\\n  left: 2px;\\n  top: 2px;\\n  width: 20px;\\n  height: 20px;\\n  background-color: white;\\n  border-radius: 50%;\\n  transition: transform 0.2s ease;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\\n}\\n\\n.toggle-input:checked + .toggle-label .toggle-inner {\\n  background-color: #2989d8;\\n}\\n\\n.toggle-input:checked + .toggle-label .toggle-inner:before {\\n  transform: translateX(26px);\\n}\\n\\n.toggle-switch-label {\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.8);\\n}\\n\\n.toggle-switch.small .toggle-inner {\\n  width: 40px;\\n  height: 20px;\\n}\\n\\n.toggle-switch.small .toggle-inner:before {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.toggle-switch.small .toggle-input:checked + .toggle-label .toggle-inner:before {\\n  transform: translateX(20px);\\n}\\n\\n/* Conteneur du bouton de recherche - Style agences de voyage */\\n.search-button-container {\\n  margin-top: 20px;\\n  display: flex;\\n  justify-content: center;\\n  position: relative;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-button-container {\\n    margin-top: 0;\\n  }\\n}\\n\\n/* Bouton de recherche - Style Booking.com */\\n.search-button {\\n  background-color: var(--primary-color);\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 12px 24px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 140px;\\n  justify-content: center;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (min-width: 992px) {\\n  .search-button {\\n    padding: 0 20px;\\n    font-size: 15px;\\n    min-width: 100px;\\n    height: 40px;\\n  }\\n\\n  .single-line-form .search-button-container {\\n    margin-top: 21px; /* Aligner avec les champs de formulaire (hauteur du label + marge) */\\n  }\\n}\\n\\n.search-button:hover:not(:disabled) {\\n  background-color: var(--primary-dark);\\n}\\n\\n.search-button:active:not(:disabled) {\\n  transform: translateY(1px);\\n}\\n\\n.search-button:disabled {\\n  background-color: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n.search-button i {\\n  font-size: 16px;\\n}\\n\\n/* Options avanc\\u00E9es - Design riche */\\n.advanced-options-container {\\n  margin-top: 25px;\\n  position: relative;\\n}\\n\\n.advanced-options-container::before {\\n  content: '';\\n  position: absolute;\\n  top: -10px;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.advanced-options-container summary {\\n  cursor: pointer;\\n  color: var(--primary-color);\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  padding: 12px 20px;\\n  transition: all 0.3s ease;\\n  outline: none;\\n  border-radius: 50px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.05);\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  margin: 0 auto;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.advanced-options-container summary::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.1) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.advanced-options-container summary:hover {\\n  color: var(--secondary-color);\\n  background-color: rgba(var(--primary-color-rgb), 0.08);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.advanced-options-container summary:hover::before {\\n  opacity: 1;\\n}\\n\\n.advanced-options-container summary i {\\n  font-size: 18px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.advanced-options-container[open] summary i {\\n  transform: rotate(180deg);\\n}\\n\\n.advanced-options {\\n  margin-top: 20px;\\n  padding: 25px;\\n  background-color: rgba(var(--primary-color-rgb), 0.03);\\n  border-radius: 16px;\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  box-shadow:\\n    inset 0 1px 8px rgba(var(--primary-color-rgb), 0.05),\\n    0 5px 15px rgba(0, 0, 0, 0.03);\\n  position: relative;\\n  animation: scaleIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\\n  overflow: hidden;\\n}\\n\\n.advanced-options::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background:\\n    radial-gradient(circle at top right, rgba(var(--primary-color-rgb), 0.05) 0%, rgba(var(--primary-color-rgb), 0) 70%),\\n    radial-gradient(circle at bottom left, rgba(var(--secondary-color-rgb), 0.05) 0%, rgba(var(--secondary-color-rgb), 0) 70%);\\n  pointer-events: none;\\n}\\n\\n.checkbox-options {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n}\\n\\n.checkbox-options .form-group {\\n  flex: 1 0 45%;\\n  transition: transform 0.3s ease;\\n}\\n\\n.checkbox-options .form-group:hover {\\n  transform: translateY(-2px);\\n}\\n\\n/* Messages d'erreur */\\n.error-message {\\n  color: #e74c3c;\\n  font-size: 13px;\\n  margin-top: 6px;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.error-message i {\\n  font-size: 14px;\\n}\\n\\n/* Animation de chargement */\\n.spinner-container {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spinner {\\n  width: 20px;\\n  height: 20px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  border-top-color: white;\\n  animation: spin 0.8s linear infinite;\\n}\\n\\n@keyframes spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n/* Styles pour les r\\u00E9sultats de recherche */\\n.search-results-container {\\n  background-color: white;\\n  border-radius: 16px;\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\\n  padding: 24px;\\n  margin-top: 24px;\\n}\\n\\n/* Animation de chargement personnalis\\u00E9e */\\n.loading-animation {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n}\\n\\n.plane-loader {\\n  position: relative;\\n  width: 200px;\\n  height: 100px;\\n  margin-bottom: 24px;\\n}\\n\\n.plane-loader i {\\n  position: absolute;\\n  font-size: 32px;\\n  color: #2989d8;\\n  animation: fly 3s infinite linear;\\n  top: 40%;\\n  left: 0;\\n}\\n\\n.cloud {\\n  position: absolute;\\n  width: 50px;\\n  height: 20px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 20px;\\n}\\n\\n.cloud:nth-child(2) {\\n  top: 20%;\\n  left: 20%;\\n  animation: cloud 8s infinite linear;\\n}\\n\\n.cloud:nth-child(3) {\\n  top: 60%;\\n  left: 40%;\\n  animation: cloud 6s infinite linear;\\n}\\n\\n.cloud:nth-child(4) {\\n  top: 40%;\\n  left: 60%;\\n  animation: cloud 10s infinite linear;\\n}\\n\\n@keyframes fly {\\n  0% {\\n    transform: translateX(0) rotate(0);\\n  }\\n  100% {\\n    transform: translateX(200px) rotate(0);\\n  }\\n}\\n\\n@keyframes cloud {\\n  0% {\\n    transform: translateX(0);\\n  }\\n  100% {\\n    transform: translateX(-200px);\\n  }\\n}\\n\\n.loading-animation p {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n/* Styles pour les r\\u00E9sultats */\\n.results-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.08);\\n}\\n\\n.results-title h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 4px;\\n}\\n\\n.results-count {\\n  font-weight: 600;\\n  color: #2989d8;\\n}\\n\\n.filter-option {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.filter-select {\\n  padding: 8px 12px;\\n  border: 1px solid rgba(0, 0, 0, 0.12);\\n  border-radius: 6px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.8);\\n  background-color: white;\\n}\\n\\n/* Carte de vol */\\n.flight-card {\\n  background-color: white;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n  margin-bottom: 20px;\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.flight-card:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\\n}\\n\\n.flight-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.airline-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.airline-logo-container {\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n.airline-logo {\\n  max-width: 32px;\\n  max-height: 32px;\\n  object-fit: contain;\\n}\\n\\n.airline-icon {\\n  font-size: 20px;\\n  color: #2989d8;\\n}\\n\\n.airline-details {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.airline-name {\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  font-size: 15px;\\n}\\n\\n.flight-number {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.flight-badges {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.flight-badge {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 4px 8px;\\n  background-color: rgba(41, 137, 216, 0.1);\\n  color: #2989d8;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.flight-price {\\n  text-align: right;\\n}\\n\\n.price-label {\\n  display: block;\\n  font-size: 12px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 4px;\\n}\\n\\n.price {\\n  font-size: 22px;\\n  font-weight: 700;\\n  color: #2989d8;\\n}\\n\\n.availability {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  color: #e74c3c;\\n  font-size: 12px;\\n  margin-top: 4px;\\n}\\n\\n.flight-details {\\n  padding: 20px;\\n}\\n\\n.flight-route {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.departure, .arrival {\\n  flex: 1;\\n}\\n\\n.time {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 6px;\\n}\\n\\n.location {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.airport-code {\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.7);\\n  font-size: 15px;\\n}\\n\\n.city-name {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 13px;\\n}\\n\\n.flight-duration {\\n  flex: 1;\\n  text-align: center;\\n  padding: 0 20px;\\n}\\n\\n.duration-line {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 10px;\\n}\\n\\n.dot {\\n  width: 10px;\\n  height: 10px;\\n  background-color: #2989d8;\\n  border-radius: 50%;\\n  z-index: 1;\\n}\\n\\n.departure-dot {\\n  background-color: #4CAF50;\\n}\\n\\n.arrival-dot {\\n  background-color: #F44336;\\n}\\n\\n.line-container {\\n  flex: 1;\\n  position: relative;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.line {\\n  width: 100%;\\n  height: 2px;\\n  background-color: #2989d8;\\n}\\n\\n.plane-icon {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  color: #2989d8;\\n  font-size: 14px;\\n}\\n\\n.duration-text {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.7);\\n  margin-bottom: 6px;\\n}\\n\\n.stops {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.stop-count {\\n  font-weight: 600;\\n  color: #F44336;\\n}\\n\\n.stops.direct {\\n  color: #4CAF50;\\n  font-weight: 500;\\n}\\n\\n.flight-features {\\n  display: flex;\\n  gap: 16px;\\n  margin-top: 16px;\\n  padding-top: 16px;\\n  border-top: 1px dashed rgba(0, 0, 0, 0.1);\\n}\\n\\n.feature {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature i {\\n  color: #2989d8;\\n  font-size: 14px;\\n}\\n\\n.flight-actions {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n  padding: 16px 20px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.view-details-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 10px 16px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: rgba(0, 0, 0, 0.7);\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.view-details-button:hover {\\n  background-color: rgba(0, 0, 0, 0.1);\\n}\\n\\n.select-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 10px 16px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(41, 137, 216, 0.3);\\n}\\n\\n.select-button:hover {\\n  background-color: #1e5799;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(41, 137, 216, 0.4);\\n}\\n\\n.select-button:disabled {\\n  background-color: rgba(0, 0, 0, 0.1);\\n  color: rgba(0, 0, 0, 0.4);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n/* Message pas de r\\u00E9sultats */\\n.no-results {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.no-results-icon {\\n  width: 80px;\\n  height: 80px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n}\\n\\n.no-results-icon i {\\n  font-size: 32px;\\n  color: rgba(0, 0, 0, 0.3);\\n}\\n\\n.no-results h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 8px;\\n}\\n\\n.no-results p {\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 24px;\\n  max-width: 500px;\\n}\\n\\n.no-results-suggestions {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  justify-content: center;\\n}\\n\\n.suggestion {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 10px 16px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 8px;\\n  color: rgba(0, 0, 0, 0.7);\\n  font-size: 14px;\\n}\\n\\n.suggestion i {\\n  color: #2989d8;\\n}\\n\\n/* Message d'erreur */\\n.error-container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n}\\n\\n.error-icon {\\n  width: 80px;\\n  height: 80px;\\n  background-color: rgba(231, 76, 60, 0.1);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n}\\n\\n.error-icon i {\\n  font-size: 32px;\\n  color: #e74c3c;\\n}\\n\\n.error-container h3 {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n  margin-bottom: 8px;\\n}\\n\\n.error-container .error-message {\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 24px;\\n  font-size: 15px;\\n  justify-content: center;\\n}\\n\\n.retry-button {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 20px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 15px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(41, 137, 216, 0.3);\\n}\\n\\n.retry-button:hover {\\n  background-color: #1e5799;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(41, 137, 216, 0.4);\\n}\\n\\n/* Styles responsifs */\\n@media (max-width: 768px) {\\n  .form-row {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .swap-locations-btn {\\n    display: none;\\n  }\\n\\n  .flight-route {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n\\n  .departure, .arrival {\\n    text-align: center;\\n  }\\n\\n  .flight-duration {\\n    margin: 16px 0;\\n  }\\n\\n  .flight-header {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .airline-info {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n\\n  .flight-badges {\\n    justify-content: center;\\n  }\\n\\n  .flight-price {\\n    text-align: center;\\n    width: 100%;\\n  }\\n\\n  .flight-features {\\n    flex-direction: column;\\n    gap: 12px;\\n    align-items: center;\\n  }\\n\\n  .flight-actions {\\n    flex-direction: column;\\n  }\\n\\n  .view-details-button, .select-button {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .page-header {\\n    flex-direction: column;\\n    gap: 24px;\\n  }\\n\\n  .header-content {\\n    text-align: center;\\n  }\\n\\n  .page-title {\\n    font-size: 1.75rem;\\n  }\\n\\n  .checkbox-options .form-group {\\n    flex: 1 0 100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "of", "FlightClassType", "LocationType", "PassengerType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "location_r12", "code", "city", "ɵɵtemplate", "SearchPriceComponent_mat_option_34_span_5_Template", "SearchPriceComponent_mat_option_34_span_6_Template", "ɵɵelement", "ɵɵproperty", "name", "type", "ɵɵpureFunction5", "_c0", "ɵɵtextInterpolate1", "location_r17", "SearchPriceComponent_mat_option_59_span_5_Template", "SearchPriceComponent_mat_option_59_span_6_Template", "flightClass_r22", "value", "label", "ɵɵlistener", "SearchPriceComponent_div_166_div_2_Template_button_click_7_listener", "ɵɵrestoreView", "_r27", "ctx_r26", "ɵɵnextContext", "ɵɵresetView", "onSearch", "ctx_r24", "errorMessage", "ctx_r29", "searchResults", "length", "flight_r33", "items", "airline", "thumbnailFull", "ɵɵsanitizeUrl", "flightProvider", "displayName", "provider", "flightClass", "ɵɵpureFunction2", "_c1", "offers", "availability", "ctx_r41", "formatExpirationDate", "expiresOn", "stopCount", "ctx_r56", "getBaggageTypeName", "baggage_r58", "baggageType", "SearchPriceComponent_div_166_div_3_div_9_div_55_div_4_Template", "SearchPriceComponent_div_166_div_3_div_9_div_55_div_6_Template", "ɵɵpipeBind3", "baggageInformations", "service_r61", "SearchPriceComponent_div_166_div_3_div_9_div_56_div_4_Template", "services", "offerId", "ɵɵclassMap", "reservableInfo", "reservable", "SearchPriceComponent_div_166_div_3_div_9_div_57_div_4_Template", "SearchPriceComponent_div_166_div_3_div_9_div_57_div_5_Template", "ɵɵtextInterpolate2", "ctx_r69", "getPassengerTypeName", "item_r71", "passengerType", "passengerCount", "ɵɵpipeBind2", "price", "amount", "currency", "serviceFee", "SearchPriceComponent_div_166_div_3_div_9_div_63_div_1_div_3_Template", "SearchPriceComponent_div_166_div_3_div_9_div_63_div_1_div_4_Template", "priceBreakDown", "SearchPriceComponent_div_166_div_3_div_9_div_63_div_1_Template", "ctx_r78", "calculateLayoverTime", "segment_r76", "segments", "i_r77", "arrival", "SearchPriceComponent_div_166_div_3_div_9_div_64_div_6_div_34_Template", "flightNo", "departure", "ctx_r75", "formatDate", "date", "airport", "formatDuration", "duration", "SearchPriceComponent_div_166_div_3_div_9_div_64_div_6_Template", "brandedFare", "description", "feature_r87", "explanations", "text", "SearchPriceComponent_div_166_div_3_div_9_div_65_div_7_div_3_div_3_Template", "commercialName", "SearchPriceComponent_div_166_div_3_div_9_div_65_div_7_div_3_Template", "features", "SearchPriceComponent_div_166_div_3_div_9_div_65_div_6_Template", "SearchPriceComponent_div_166_div_3_div_9_div_65_div_7_Template", "SearchPriceComponent_div_166_div_3_div_9_img_4_Template", "SearchPriceComponent_div_166_div_3_div_9_i_5_Template", "SearchPriceComponent_div_166_div_3_div_9_span_11_Template", "SearchPriceComponent_div_166_div_3_div_9_span_13_Template", "SearchPriceComponent_div_166_div_3_div_9_span_14_Template", "SearchPriceComponent_div_166_div_3_div_9_span_15_Template", "SearchPriceComponent_div_166_div_3_div_9_span_21_Template", "SearchPriceComponent_div_166_div_3_div_9_span_22_Template", "SearchPriceComponent_div_166_div_3_div_9_div_44_Template", "SearchPriceComponent_div_166_div_3_div_9_div_45_Template", "SearchPriceComponent_div_166_div_3_div_9_div_55_Template", "SearchPriceComponent_div_166_div_3_div_9_div_56_Template", "SearchPriceComponent_div_166_div_3_div_9_div_57_Template", "SearchPriceComponent_div_166_div_3_div_9_div_63_Template", "SearchPriceComponent_div_166_div_3_div_9_div_64_Template", "SearchPriceComponent_div_166_div_3_div_9_div_65_Template", "SearchPriceComponent_div_166_div_3_div_9_Template_button_click_67_listener", "restoredCtx", "_r93", "$implicit", "ctx_r92", "viewOfferDetails", "SearchPriceComponent_div_166_div_3_div_9_Template_button_click_73_listener", "ctx_r94", "debugOfferStructure", "ɵɵclassProp", "ctx_r31", "isFlightAvailable", "<PERSON><PERSON><PERSON>", "getMinPrice", "SearchPriceComponent_div_166_div_3_p_5_Template", "SearchPriceComponent_div_166_div_3_p_6_Template", "SearchPriceComponent_div_166_div_3_div_7_Template", "SearchPriceComponent_div_166_div_3_div_9_Template", "SearchPriceComponent_div_166_div_3_div_10_Template", "ctx_r25", "isLoading", "SearchPriceComponent_div_166_div_1_Template", "SearchPriceComponent_div_166_div_2_Template", "SearchPriceComponent_div_166_div_3_Template", "ctx_r11", "SearchPriceComponent", "constructor", "fb", "productService", "router", "departureLocations", "arrivalLocations", "hasSearched", "lastSearchId", "passengerTypes", "Adult", "Child", "Infant", "flightClasses", "PROMO", "ECONOMY", "BUSINESS", "minDate", "Date", "toISOString", "split", "searchForm", "group", "productType", "required", "serviceTypes", "departureLocation", "departureLocationType", "arrivalLocation", "arrivalLocationType", "departureDate", "min", "max", "nonStop", "culture", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightBaggageGetOption", "ngOnInit", "setupAutocomplete", "preloadLocations", "console", "log", "flight", "id", "for<PERSON>ach", "offer", "index", "groupEnd", "get", "getLocationsByType", "subscribe", "locations", "valueChanges", "locationType", "setValue", "pipe", "filter", "location", "toLowerCase", "includes", "displayLocation", "displayText", "Airport", "invalid", "markFormGroupTouched", "formValue", "request", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "ArrivalLocations", "Passengers", "count", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "searchPrice", "next", "response", "header", "success", "body", "flights", "JSON", "stringify", "flightsWithOffers", "f", "availabilityValues", "flatMap", "o", "availabilityCounts", "reduce", "acc", "val", "reservableFlights", "some", "searchId", "requestId", "error", "Object", "keys", "messages", "message", "formGroup", "values", "controls", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "minutes", "hours", "Math", "floor", "mins", "dateString", "toLocaleDateString", "weekday", "day", "month", "hour", "minute", "min<PERSON>ffer", "formattedAmount", "firstOffer", "offerIds", "random", "toString", "substring", "alert", "navigate", "queryParams", "showAllDepartureLocations", "input", "document", "getElementById", "focus", "dispatchEvent", "Event", "showAllArrivalLocations", "swapLocations", "patchValue", "toLocaleString", "currentSegment", "nextSegment", "arrivalTime", "getTime", "departureTime", "diffMs", "diffMins", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProductService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "SearchPriceComponent_Template", "rf", "ctx", "SearchPriceComponent_Template_form_ngSubmit_21_listener", "SearchPriceComponent_Template_input_click_31_listener", "SearchPriceComponent_mat_option_34_Template", "SearchPriceComponent_div_35_Template", "SearchPriceComponent_Template_button_click_49_listener", "SearchPriceComponent_Template_input_click_56_listener", "SearchPriceComponent_mat_option_59_Template", "SearchPriceComponent_div_60_Template", "SearchPriceComponent_div_79_Template", "SearchPriceComponent_option_92_Template", "SearchPriceComponent_i_95_Template", "SearchPriceComponent_span_96_Template", "SearchPriceComponent_div_97_Template", "SearchPriceComponent_div_166_Template", "_r0", "bind", "tmp_4_0", "touched", "_r3", "tmp_13_0", "tmp_20_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { Observable, of } from 'rxjs';\nimport { ProductService } from '../../../services/product.service';\nimport { LocationOption } from '../../../models/price-search-request.model';\nimport { PriceSearchRequest } from '../../../models/price-search-request.model';\nimport { PriceSearchResponse, Flight } from '../../../models/price-search-response.model';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css'],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchPriceComponent implements OnInit {\n  searchForm: FormGroup;\n  departureLocations: LocationOption[] = [];\n  arrivalLocations: LocationOption[] = [];\n  isLoading = false;\n  searchResults: Flight[] = [];\n  hasSearched = false;\n  errorMessage = '';\n  lastSearchId = '';\n\n  // Passenger type options\n  passengerTypes = [\n    { value: PassengerType.Adult, label: 'Adult' },\n    { value: PassengerType.Child, label: 'Child' },\n    { value: PassengerType.Infant, label: 'Infant' }\n  ];\n\n  // Flight class options\n  flightClasses = [\n    { value: FlightClassType.PROMO, label: 'Promo' },\n    { value: FlightClassType.ECONOMY, label: 'Economy' },\n    { value: FlightClassType.BUSINESS, label: 'Business' }\n  ];\n\n  // Date minimale (aujourd'hui)\n  minDate: string;\n\n  constructor(\n    private fb: FormBuilder,\n    private productService: ProductService,\n    private router: Router\n  ) {\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required], // Flight par défaut\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required], // Type 2 (City) par défaut\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required], // Type 5 (Airport) par défaut\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required], // Adult par défaut\n\n      // Options de vol\n      flightClass: [0, Validators.required], // PROMO par défaut\n      nonStop: [false],\n\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n\n  ngOnInit(): void {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n\n  // Méthode pour déboguer la structure des offres\n  debugOfferStructure(flight: Flight): void {\n    console.group('Flight Offer Debug');\n    console.log('Flight ID:', flight.id);\n    console.log('Flight Provider:', flight.provider);\n\n    if (flight.offers && flight.offers.length > 0) {\n      console.log('Number of offers:', flight.offers.length);\n\n      flight.offers.forEach((offer, index) => {\n        console.group(`Offer ${index + 1}`);\n        console.log('Offer ID:', offer.offerId || offer.id);\n        console.log('Availability:', offer.availability);\n        console.log('Price:', offer.price);\n        console.log('Reservable:', offer.reservableInfo?.reservable);\n        console.log('Expires On:', offer.expiresOn);\n        console.log('Has Brand:', offer.hasBrand);\n        console.log('Full Offer Object:', offer);\n        console.groupEnd();\n      });\n    } else {\n      console.log('No offers available for this flight');\n    }\n\n    console.groupEnd();\n  }\n\n  preloadLocations(): void {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n\n  setupAutocomplete(): void {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.departureLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('departureLocation')?.setValue('');\n        });\n      });\n\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.arrivalLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('arrivalLocation')?.setValue('');\n        });\n      });\n\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.departureLocations = locations;\n      });\n\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n  }\n\n  displayLocation(location: LocationOption | null): string {\n    if (!location) return '';\n\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n\n  onSearch(): void {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n\n    const formValue = this.searchForm.value;\n\n    // Créer la requête de recherche entièrement dynamique\n    const request: PriceSearchRequest = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [\n        {\n          id: formValue.departureLocation?.id || '',\n          type: formValue.departureLocationType\n        }\n      ],\n      ArrivalLocations: [\n        {\n          id: formValue.arrivalLocation?.id || '',\n          type: formValue.arrivalLocationType\n        }\n      ],\n      Passengers: [\n        {\n          type: formValue.passengerType,\n          count: formValue.passengerCount\n        }\n      ],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n\n    this.productService.searchPrice(request)\n      .subscribe({\n        next: (response: PriceSearchResponse) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            console.log('Full API Response:', JSON.stringify(response, null, 2));\n\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability));\n              console.log('Availability values:', availabilityValues);\n\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc, val) => {\n                acc[val] = (acc[val] || 0) + 1;\n                return acc;\n              }, {} as Record<number, number>);\n              console.log('Availability counts:', availabilityCounts);\n\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f =>\n                f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true)\n              );\n              console.log('Reservable flights:', reservableFlights.length);\n\n              console.groupEnd();\n            }\n\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n  }\n\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup: FormGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n\n  // Formater la durée en heures et minutes\n  formatDuration(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n\n  // Formater la date pour l'affichage\n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight: Flight): string {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n\n    const minOffer = flight.offers.reduce((min, offer) =>\n      offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight: Flight): boolean {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n\n    // Utiliser uniquement la valeur availability telle qu'elle est fournie par l'API\n    return flight.offers.some(offer => offer.availability > 0);\n  }\n\n  // Naviguer vers la page de détails de l'offre\n  viewOfferDetails(flight: Flight): void {\n    console.log('Flight object:', flight);\n\n    if (flight && flight.offers && flight.offers.length > 0) {\n      console.log('Flight offers:', flight.offers);\n\n      // Vérifier la structure exacte de l'offre\n      const firstOffer = flight.offers[0];\n      console.log('First offer structure:', Object.keys(firstOffer));\n      console.log('Full offer object:', firstOffer);\n\n      // Essayer de trouver l'ID de l'offre dans différents champs possibles\n      let offerId;\n\n      // Vérifier d'abord le champ offerId direct\n      if (firstOffer.offerId) {\n        offerId = firstOffer.offerId;\n        console.log('Using offer.offerId:', offerId);\n      }\n      // Vérifier ensuite le tableau offerIds\n      else if (firstOffer.offerIds && firstOffer.offerIds.length > 0 && firstOffer.offerIds[0].offerId) {\n        offerId = firstOffer.offerIds[0].offerId;\n        console.log('Using offer.offerIds[0].offerId:', offerId);\n      }\n      // Vérifier ensuite le champ id\n      else if (firstOffer.id) {\n        offerId = firstOffer.id;\n        console.log('Using offer.id:', offerId);\n      } else if (flight.id) {\n        offerId = flight.id;\n        console.log('Using flight.id as fallback:', offerId);\n      } else {\n        // Générer un ID aléatoire comme dernier recours\n        offerId = 'offer-' + Math.random().toString(36).substring(2, 15);\n        console.log('Generated random offerId as last resort:', offerId);\n      }\n\n      console.log('Selected offerId:', offerId);\n\n      // Vérifier si l'ID de recherche est disponible\n      if (!this.lastSearchId) {\n        console.error('SearchId is missing or empty!');\n\n        // Essayer de trouver un ID alternatif\n        if (flight.id) {\n          console.log('Using flight.id as searchId:', flight.id);\n          this.lastSearchId = flight.id;\n        } else if (offerId) {\n          console.log('Using offerId as searchId:', offerId);\n          this.lastSearchId = offerId;\n        } else {\n          // Générer un ID de recherche aléatoire comme dernier recours\n          this.lastSearchId = 'search-' + Math.random().toString(36).substring(2, 15);\n          console.log('Generated random searchId as last resort:', this.lastSearchId);\n        }\n      }\n\n      console.log('Navigating to get-offer with searchId:', this.lastSearchId, 'and offerId:', offerId);\n\n      // S'assurer que les deux IDs sont définis\n      if (!offerId) {\n        alert('Offer ID is missing. Cannot view details.');\n        return;\n      }\n\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: this.lastSearchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations(): void {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations(): void {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations(): void {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType: number): string {\n    switch (baggageType) {\n      case 1: return 'Cabin Baggage';\n      case 2: return 'Checked Baggage';\n      case 3: return 'Hand Baggage';\n      default: return 'Baggage';\n    }\n  }\n\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType: number): string {\n    switch (passengerType) {\n      case 1: return 'Adult';\n      case 2: return 'Child';\n      case 3: return 'Infant';\n      default: return 'Passenger';\n    }\n  }\n\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment: any, nextSegment: any): string {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date ||\n        !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n}\n", "<div class=\"search-price-container\">\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <h1 class=\"page-title\">Find Your Perfect Flight</h1>\n      <p class=\"page-subtitle\">Search and compare flights to destinations worldwide</p>\n    </div>\n    <div class=\"header-illustration\">\n      <img src=\"assets/images/airplane-banner.jpg\" alt=\"Airplane in the sky\">\n    </div>\n  </div>\n\n  <div class=\"search-content\">\n    <div class=\"search-form-container\">\n      <!-- Logo pour la version desktop -->\n      <div class=\"sidebar-logo\">\n        <div class=\"logo-container\">\n          <i class=\"fas fa-plane-departure logo-icon\"></i>\n          <span class=\"logo-text\">TravelEase</span>\n        </div>\n      </div>\n\n      <div class=\"search-form-header\">\n        <h2>Search Flights</h2>\n        <p>Enter your travel details below</p>\n      </div>\n\n      <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSearch()\" class=\"search-form\">\n        <!-- Main Search Fields -->\n        <div class=\"search-card\">\n          <!-- Hidden Product Type and Service Types -->\n          <input type=\"hidden\" formControlName=\"productType\" value=\"3\">\n          <input type=\"hidden\" formControlName=\"serviceTypes\" value=\"['1']\">\n\n          <!-- Formulaire sur une seule ligne -->\n          <div class=\"single-line-form\">\n            <!-- Departure Location -->\n            <div class=\"form-group\">\n              <label for=\"departureLocation\">From</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-plane-departure\"></i>\n                <input\n                  type=\"text\"\n                  id=\"departureLocation\"\n                  formControlName=\"departureLocation\"\n                  placeholder=\"City or airport\"\n                  [matAutocomplete]=\"departureAuto\"\n                  class=\"form-control\"\n                  (click)=\"showAllDepartureLocations()\"\n                >\n              </div>\n              <mat-autocomplete #departureAuto=\"matAutocomplete\" [displayWith]=\"displayLocation.bind(this)\">\n                <mat-option *ngFor=\"let location of departureLocations\" [value]=\"location\">\n                  <div class=\"location-option\">\n                    <div class=\"location-name\">{{ location.name }}</div>\n                    <div class=\"location-details\">\n                      <span *ngIf=\"location.code\" class=\"location-code\">{{ location.code }}</span>\n                      <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">{{ location.city }}</span>\n                      <span class=\"location-type\">\n                        <i class=\"fas\"\n                          [ngClass]=\"{\n                            'fa-flag': location.type === 1,\n                            'fa-city': location.type === 2,\n                            'fa-building': location.type === 3,\n                            'fa-home': location.type === 4,\n                            'fa-plane': location.type === 5\n                          }\"></i>\n                        {{ location.type === 1 ? 'Country' :\n                           location.type === 2 ? 'City' :\n                           location.type === 3 ? 'Town' :\n                           location.type === 4 ? 'Village' :\n                           location.type === 5 ? 'Airport' : '' }}\n                      </span>\n                    </div>\n                  </div>\n                </mat-option>\n              </mat-autocomplete>\n              <div *ngIf=\"searchForm.get('departureLocation')?.invalid && searchForm.get('departureLocation')?.touched\" class=\"error-message\">\n                <i class=\"fas fa-exclamation-circle\"></i> Please select a departure location\n              </div>\n            </div>\n\n            <!-- Location Type Selector (Hidden but still functional) -->\n            <div class=\"form-group location-type-selector\">\n              <select\n                id=\"departureLocationType\"\n                formControlName=\"departureLocationType\"\n                class=\"form-control\"\n                aria-label=\"Departure Location Type\"\n              >\n                <option [value]=\"1\">Country (1)</option>\n                <option [value]=\"2\">City (2)</option>\n                <option [value]=\"3\">Town (3)</option>\n                <option [value]=\"4\">Village (4)</option>\n                <option [value]=\"5\">Airport (5)</option>\n              </select>\n            </div>\n\n            <!-- Swap Locations Button (Between From and To) -->\n            <div class=\"swap-button-container\">\n              <button type=\"button\" class=\"swap-locations-btn\" (click)=\"swapLocations()\">\n                <i class=\"fas fa-exchange-alt\"></i>\n              </button>\n            </div>\n\n            <!-- Arrival Location -->\n            <div class=\"form-group\">\n              <label for=\"arrivalLocation\">To</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-plane-arrival\"></i>\n                <input\n                  type=\"text\"\n                  id=\"arrivalLocation\"\n                  formControlName=\"arrivalLocation\"\n                  placeholder=\"City or airport\"\n                  [matAutocomplete]=\"arrivalAuto\"\n                  class=\"form-control\"\n                  (click)=\"showAllArrivalLocations()\"\n                >\n              </div>\n              <mat-autocomplete #arrivalAuto=\"matAutocomplete\" [displayWith]=\"displayLocation.bind(this)\">\n                <mat-option *ngFor=\"let location of arrivalLocations\" [value]=\"location\">\n                  <div class=\"location-option\">\n                    <div class=\"location-name\">{{ location.name }}</div>\n                    <div class=\"location-details\">\n                      <span *ngIf=\"location.code\" class=\"location-code\">{{ location.code }}</span>\n                      <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">{{ location.city }}</span>\n                      <span class=\"location-type\">\n                        <i class=\"fas\"\n                          [ngClass]=\"{\n                            'fa-flag': location.type === 1,\n                            'fa-city': location.type === 2,\n                            'fa-building': location.type === 3,\n                            'fa-home': location.type === 4,\n                            'fa-plane': location.type === 5\n                          }\"></i>\n                        {{ location.type === 1 ? 'Country' :\n                           location.type === 2 ? 'City' :\n                           location.type === 3 ? 'Town' :\n                           location.type === 4 ? 'Village' :\n                           location.type === 5 ? 'Airport' : '' }}\n                      </span>\n                    </div>\n                  </div>\n                </mat-option>\n              </mat-autocomplete>\n              <div *ngIf=\"searchForm.get('arrivalLocation')?.invalid && searchForm.get('arrivalLocation')?.touched\" class=\"error-message\">\n                <i class=\"fas fa-exclamation-circle\"></i> Please select an arrival location\n              </div>\n            </div>\n\n            <!-- Location Type Selector (Hidden but still functional) -->\n            <div class=\"form-group location-type-selector\">\n              <select\n                id=\"arrivalLocationType\"\n                formControlName=\"arrivalLocationType\"\n                class=\"form-control\"\n                aria-label=\"Arrival Location Type\"\n              >\n                <option [value]=\"1\">Country (1)</option>\n                <option [value]=\"2\">City (2)</option>\n                <option [value]=\"3\">Town (3)</option>\n                <option [value]=\"4\">Village (4)</option>\n                <option [value]=\"5\">Airport (5)</option>\n              </select>\n            </div>\n\n            <!-- Departure Date -->\n            <div class=\"form-group\">\n              <label for=\"departureDate\">Date</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-calendar-alt\"></i>\n                <input\n                  type=\"date\"\n                  id=\"departureDate\"\n                  formControlName=\"departureDate\"\n                  [min]=\"minDate\"\n                  class=\"form-control\"\n                >\n              </div>\n              <div *ngIf=\"searchForm.get('departureDate')?.invalid && searchForm.get('departureDate')?.touched\" class=\"error-message\">\n                <i class=\"fas fa-exclamation-circle\"></i> Please select a date\n              </div>\n            </div>\n\n            <!-- Passengers -->\n            <div class=\"form-group\">\n              <label for=\"passengerCount\">Passengers</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-user-friends\"></i>\n                <input\n                  type=\"number\"\n                  id=\"passengerCount\"\n                  formControlName=\"passengerCount\"\n                  min=\"1\"\n                  max=\"9\"\n                  class=\"form-control\"\n                >\n              </div>\n            </div>\n\n            <!-- Flight Class -->\n            <div class=\"form-group\">\n              <label for=\"flightClass\">Class</label>\n              <div class=\"input-with-icon\">\n                <i class=\"fas fa-chair\"></i>\n                <select\n                  id=\"flightClass\"\n                  formControlName=\"flightClass\"\n                  class=\"form-control\"\n                >\n                  <option *ngFor=\"let flightClass of flightClasses\" [value]=\"flightClass.value\">{{ flightClass.label }}</option>\n                </select>\n              </div>\n            </div>\n\n            <!-- Search Button -->\n            <div class=\"search-button-container\">\n              <button\n                type=\"submit\"\n                class=\"search-button\"\n                [disabled]=\"searchForm.invalid || isLoading\"\n              >\n                <i class=\"fas fa-search\" *ngIf=\"!isLoading\"></i>\n                <span *ngIf=\"!isLoading\">Search</span>\n                <div *ngIf=\"isLoading\" class=\"spinner-container\">\n                  <div class=\"spinner\"></div>\n                </div>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Advanced Options -->\n        <div class=\"advanced-options-container\">\n          <details>\n            <summary>\n              <i class=\"fas fa-cog\"></i> Advanced Options\n            </summary>\n            <div class=\"advanced-options\">\n              <div class=\"form-row\">\n                <!-- Culture -->\n                <div class=\"form-group\">\n                  <label for=\"culture\">Language</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-language\"></i>\n                    <select\n                      id=\"culture\"\n                      formControlName=\"culture\"\n                      class=\"form-control\"\n                    >\n                      <option value=\"en-US\">English (US)</option>\n                      <option value=\"fr-FR\">Français</option>\n                    </select>\n                  </div>\n                </div>\n\n                <!-- Currency -->\n                <div class=\"form-group\">\n                  <label for=\"currency\">Currency</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-money-bill-wave\"></i>\n                    <select\n                      id=\"currency\"\n                      formControlName=\"currency\"\n                      class=\"form-control\"\n                    >\n                      <option value=\"EUR\">Euro (€)</option>\n                      <option value=\"USD\">Dollar ($)</option>\n                    </select>\n                  </div>\n                </div>\n\n                <!-- Baggage Options -->\n                <div class=\"form-group\">\n                  <label for=\"flightBaggageGetOption\">Baggage Options</label>\n                  <div class=\"input-with-icon\">\n                    <i class=\"fas fa-suitcase\"></i>\n                    <select\n                      id=\"flightBaggageGetOption\"\n                      formControlName=\"flightBaggageGetOption\"\n                      class=\"form-control\"\n                    >\n                      <option [value]=\"0\">All options</option>\n                      <option [value]=\"1\">Baggage included only</option>\n                      <option [value]=\"2\">No baggage only</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Additional Options -->\n              <div class=\"form-row checkbox-options\">\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"acceptPendingProviders\"\n                      formControlName=\"acceptPendingProviders\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"acceptPendingProviders\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Accept pending providers</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"forceFlightBundlePackage\"\n                      formControlName=\"forceFlightBundlePackage\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"forceFlightBundlePackage\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Force flight bundle package</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"disablePackageOfferTotalPrice\"\n                      formControlName=\"disablePackageOfferTotalPrice\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"disablePackageOfferTotalPrice\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Disable package offer total price</span>\n                    </label>\n                  </div>\n                </div>\n\n                <div class=\"form-group checkbox-group\">\n                  <div class=\"toggle-switch small\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"calculateFlightFees\"\n                      formControlName=\"calculateFlightFees\"\n                      class=\"toggle-input\"\n                    >\n                    <label for=\"calculateFlightFees\" class=\"toggle-label\">\n                      <span class=\"toggle-inner\"></span>\n                      <span class=\"toggle-switch-label\">Calculate flight fees</span>\n                    </label>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </details>\n        </div>\n      </form>\n    </div>\n\n    <!-- Search Results -->\n    <div class=\"search-results-container\" *ngIf=\"hasSearched\">\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <div class=\"loading-animation\">\n          <div class=\"plane-loader\">\n            <i class=\"fas fa-plane\"></i>\n            <div class=\"cloud\"></div>\n            <div class=\"cloud\"></div>\n            <div class=\"cloud\"></div>\n          </div>\n          <p>Searching for the best flights...</p>\n        </div>\n      </div>\n\n      <div *ngIf=\"!isLoading && errorMessage\" class=\"error-container\">\n        <div class=\"error-icon\">\n          <i class=\"fas fa-exclamation-circle\"></i>\n        </div>\n        <h3>Oops! Something went wrong</h3>\n        <p class=\"error-message\">{{ errorMessage }}</p>\n        <button class=\"retry-button\" (click)=\"onSearch()\">\n          <i class=\"fas fa-redo\"></i> Try Again\n        </button>\n      </div>\n\n      <div *ngIf=\"!isLoading && !errorMessage\" class=\"search-results-content\">\n        <div class=\"results-header\">\n          <div class=\"results-title\">\n            <h3>Flight Options</h3>\n            <p *ngIf=\"searchResults.length === 0\">No flights found for your search. Please modify your criteria.</p>\n            <p *ngIf=\"searchResults.length > 0\">\n              <span class=\"results-count\">{{ searchResults.length }}</span> flights found\n            </p>\n          </div>\n\n          <div class=\"results-filters\" *ngIf=\"searchResults.length > 0\">\n            <div class=\"filter-option\">\n              <i class=\"fas fa-sort-amount-down\"></i>\n              <span>Sort by:</span>\n              <select class=\"filter-select\">\n                <option value=\"price\">Price (lowest first)</option>\n                <option value=\"duration\">Duration (shortest first)</option>\n                <option value=\"departure\">Departure (earliest first)</option>\n                <option value=\"arrival\">Arrival (earliest first)</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"flight-list\">\n          <div *ngFor=\"let flight of searchResults\" class=\"flight-card\" [class.unavailable]=\"!isFlightAvailable(flight)\">\n            <div class=\"flight-header\">\n              <div class=\"airline-info\">\n                <div class=\"airline-logo-container\">\n                  <img *ngIf=\"flight.items && flight.items[0] && flight.items[0].airline && flight.items[0].airline.thumbnailFull\"\n                       [src]=\"flight.items[0].airline.thumbnailFull\"\n                       alt=\"Airline logo\"\n                       class=\"airline-logo\">\n                  <i *ngIf=\"!(flight.items && flight.items[0] && flight.items[0].airline && flight.items[0].airline.thumbnailFull)\"\n                     class=\"fas fa-plane airline-icon\"></i>\n                </div>\n                <div class=\"airline-details\">\n                  <span class=\"airline-name\">{{ flight.items && flight.items[0] && flight.items[0].airline ? flight.items[0].airline.name : 'Airline' }}</span>\n                  <span class=\"flight-number\">{{ flight.items && flight.items[0] ? flight.items[0].flightNo : 'N/A' }}</span>\n                  <!-- Provider information -->\n                  <span class=\"provider-info\" *ngIf=\"flight.provider || (flight.items && flight.items[0] && flight.items[0].flightProvider)\">\n                    <i class=\"fas fa-tag\"></i>\n                    {{ (flight.items && flight.items[0] && flight.items[0].flightProvider && flight.items[0].flightProvider.displayName) ||\n                       (flight.items && flight.items[0] && flight.items[0].flightProvider && flight.items[0].flightProvider.name) ||\n                       'Provider ' + flight.provider }}\n                  </span>\n                </div>\n              </div>\n\n              <div class=\"flight-badges\">\n                <span class=\"flight-badge\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount === 0\">\n                  <i class=\"fas fa-bolt\"></i> Direct\n                </span>\n                <span class=\"flight-badge\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].flightClass\">\n                  <i class=\"fas fa-chair\"></i> {{ flight.items[0].flightClass.name }}\n                </span>\n                <!-- Branded fare badge -->\n                <span class=\"flight-badge branded\" *ngIf=\"flight.offers && flight.offers[0] && flight.offers[0].hasBrand\">\n                  <i class=\"fas fa-certificate\"></i> Branded Fare\n                </span>\n              </div>\n\n              <div class=\"flight-price\">\n                <span class=\"price-label\">Price per person</span>\n                <span class=\"price\">{{ getMinPrice(flight) }}</span>\n                <!-- Availability information - Afficher exactement la valeur de l'API -->\n                <span class=\"availability\" *ngIf=\"flight.offers && flight.offers.length > 0\">\n                  <i class=\"fas\" [ngClass]=\"{'fa-check-circle': flight.offers[0].availability > 0, 'fa-exclamation-triangle': flight.offers[0].availability <= 0}\"></i>\n                  Availability: {{ flight.offers[0].availability }}\n                </span>\n\n                <!-- Expiration information - Afficher exactement la valeur de l'API -->\n                <span class=\"expiration\" *ngIf=\"flight.offers && flight.offers.length > 0 && flight.offers[0].expiresOn\">\n                  <i class=\"fas fa-clock\"></i> Expires: {{ formatExpirationDate(flight.offers[0].expiresOn) }}\n                </span>\n              </div>\n            </div>\n\n            <div class=\"flight-details\">\n              <div class=\"flight-route\">\n                <div class=\"departure\">\n                  <div class=\"time\">{{ flight.items && flight.items[0] && flight.items[0].departure ? formatDate(flight.items[0].departure.date) : 'N/A' }}</div>\n                  <div class=\"location\">\n                    <span class=\"airport-code\">{{ flight.items && flight.items[0] && flight.items[0].departure && flight.items[0].departure.airport ? flight.items[0].departure.airport.code : 'N/A' }}</span>\n                    <span class=\"city-name\">{{ flight.items && flight.items[0] && flight.items[0].departure && flight.items[0].departure.city ? flight.items[0].departure.city.name : 'N/A' }}</span>\n                  </div>\n                </div>\n\n                <div class=\"flight-duration\">\n                  <div class=\"duration-line\">\n                    <span class=\"dot departure-dot\"></span>\n                    <div class=\"line-container\">\n                      <span class=\"line\"></span>\n                      <span class=\"plane-icon\">\n                        <i class=\"fas fa-plane\"></i>\n                      </span>\n                    </div>\n                    <span class=\"dot arrival-dot\"></span>\n                  </div>\n                  <div class=\"duration-text\">\n                    <i class=\"fas fa-clock\"></i>\n                    {{ flight.items && flight.items[0] ? formatDuration(flight.items[0].duration) : 'N/A' }}\n                  </div>\n                  <div class=\"stops\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount > 0\">\n                    <span class=\"stop-count\">{{ flight.items[0].stopCount }}</span> stop{{ flight.items[0].stopCount > 1 ? 's' : '' }}\n                  </div>\n                  <div class=\"stops direct\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].stopCount === 0\">\n                    Direct flight\n                  </div>\n                </div>\n\n                <div class=\"arrival\">\n                  <div class=\"time\">{{ flight.items && flight.items[0] && flight.items[0].arrival ? formatDate(flight.items[0].arrival.date) : 'N/A' }}</div>\n                  <div class=\"location\">\n                    <span class=\"airport-code\">{{ flight.items && flight.items[0] && flight.items[0].arrival && flight.items[0].arrival.airport ? flight.items[0].arrival.airport.code : 'N/A' }}</span>\n                    <span class=\"city-name\">{{ flight.items && flight.items[0] && flight.items[0].arrival && flight.items[0].arrival.city ? flight.items[0].arrival.city.name : 'N/A' }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"flight-features\">\n                <!-- Baggage Information -->\n                <div *ngIf=\"flight.items && flight.items[0] && flight.items[0].baggageInformations && flight.items[0].baggageInformations.length > 0\" class=\"feature-group\">\n                  <h4><i class=\"fas fa-suitcase\"></i> Baggage</h4>\n                  <div *ngFor=\"let baggage of flight.items[0].baggageInformations | slice:0:2\" class=\"feature\">\n                    <span>{{ getBaggageTypeName(baggage.baggageType) }}</span>\n                  </div>\n                  <div *ngIf=\"!flight.items[0].baggageInformations || flight.items[0].baggageInformations.length === 0\" class=\"feature\">\n                    <span>No baggage information available</span>\n                  </div>\n                </div>\n\n                <!-- Services Information -->\n                <div *ngIf=\"flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0\" class=\"feature-group\">\n                  <h4><i class=\"fas fa-concierge-bell\"></i> Services</h4>\n                  <div *ngFor=\"let service of flight.items[0].services | slice:0:3\" class=\"feature\">\n                    <span>{{ service.name || 'Service' }}</span>\n                  </div>\n                </div>\n\n                <!-- Offer Information -->\n                <div *ngIf=\"flight.offers && flight.offers.length > 0\" class=\"feature-group\">\n                  <h4><i class=\"fas fa-tag\"></i> Offer Details</h4>\n                  <div class=\"feature\" *ngIf=\"flight.offers[0].offerId\">\n                    <span class=\"offer-id\">ID: {{ flight.offers[0].offerId | slice:0:10 }}...</span>\n                  </div>\n                  <div class=\"feature\" *ngIf=\"flight.offers[0].reservableInfo\">\n                    <span>\n                      <i [class]=\"flight.offers[0].reservableInfo.reservable ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger'\"></i>\n                      {{ flight.offers[0].reservableInfo.reservable ? 'Reservable' : 'Not reservable' }}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Price Breakdown Section (Collapsible) -->\n            <div class=\"price-breakdown-section\">\n              <details class=\"price-breakdown-details\">\n                <summary class=\"price-breakdown-summary\">\n                  <i class=\"fas fa-money-bill-wave\"></i> Price Breakdown\n                </summary>\n                <div class=\"price-breakdown-content\" *ngIf=\"flight.offers && flight.offers[0]\">\n                  <!-- Price Breakdown -->\n                  <div class=\"breakdown-group\" *ngIf=\"flight.offers[0].priceBreakDown && flight.offers[0].priceBreakDown.items\">\n                    <h4>Price Details</h4>\n                    <div class=\"breakdown-item\" *ngFor=\"let item of flight.offers[0].priceBreakDown.items\">\n                      <span class=\"passenger-type\">\n                        {{ getPassengerTypeName(item.passengerType) }} (x{{ item.passengerCount }})\n                      </span>\n                      <span class=\"item-price\">{{ item.price.amount | currency:item.price.currency }}</span>\n                    </div>\n                    <div class=\"breakdown-item service-fee\" *ngIf=\"flight.offers[0].serviceFee\">\n                      <span class=\"fee-label\">Service Fee</span>\n                      <span class=\"fee-amount\">{{ flight.offers[0].serviceFee.amount | currency:flight.offers[0].serviceFee.currency }}</span>\n                    </div>\n                    <div class=\"breakdown-total\">\n                      <span class=\"total-label\">Total</span>\n                      <span class=\"total-amount\">{{ flight.offers[0].price.amount | currency:flight.offers[0].price.currency }}</span>\n                    </div>\n                  </div>\n                </div>\n              </details>\n            </div>\n\n            <!-- Segment Information for Connecting Flights (Collapsible) -->\n            <div class=\"segments-section\" *ngIf=\"flight.items && flight.items[0] && flight.items[0].segments && flight.items[0].segments.length > 1\">\n              <details class=\"segments-details\">\n                <summary class=\"segments-summary\">\n                  <i class=\"fas fa-plane-departure\"></i> Flight Segments ({{ flight.items[0].segments.length }})\n                </summary>\n                <div class=\"segments-content\">\n                  <div class=\"segment-item\" *ngFor=\"let segment of flight.items[0].segments; let i = index\">\n                    <div class=\"segment-header\">\n                      <span class=\"segment-number\">Segment {{ i + 1 }}</span>\n                      <span class=\"segment-airline\">{{ segment.airline && segment.airline.name || 'Airline' }}</span>\n                      <span class=\"segment-flight\">{{ segment.flightNo }}</span>\n                    </div>\n                    <div class=\"segment-route\">\n                      <div class=\"segment-departure\">\n                        <div class=\"time\">{{ segment.departure ? formatDate(segment.departure.date) : 'N/A' }}</div>\n                        <div class=\"location\">\n                          <span class=\"airport-code\">{{ segment.departure && segment.departure.airport && segment.departure.airport.code || 'N/A' }}</span>\n                          <span class=\"city-name\">{{ segment.departure && segment.departure.city && segment.departure.city.name || 'N/A' }}</span>\n                        </div>\n                      </div>\n                      <div class=\"segment-duration\">\n                        <div class=\"duration-line\">\n                          <span class=\"dot departure-dot\"></span>\n                          <div class=\"line-container\">\n                            <span class=\"line\"></span>\n                          </div>\n                          <span class=\"dot arrival-dot\"></span>\n                        </div>\n                        <div class=\"duration-text\">\n                          <i class=\"fas fa-clock\"></i> {{ formatDuration(segment.duration) }}\n                        </div>\n                      </div>\n                      <div class=\"segment-arrival\">\n                        <div class=\"time\">{{ segment.arrival ? formatDate(segment.arrival.date) : 'N/A' }}</div>\n                        <div class=\"location\">\n                          <span class=\"airport-code\">{{ segment.arrival && segment.arrival.airport && segment.arrival.airport.code || 'N/A' }}</span>\n                          <span class=\"city-name\">{{ segment.arrival && segment.arrival.city && segment.arrival.city.name || 'N/A' }}</span>\n                        </div>\n                      </div>\n                    </div>\n                    <!-- Layover information if not the last segment -->\n                    <div class=\"layover-info\" *ngIf=\"i < flight.items[0].segments.length - 1\">\n                      <i class=\"fas fa-hourglass-half\"></i>\n                      <span>{{ calculateLayoverTime(segment, flight.items[0].segments[i+1]) }} layover in {{ segment.arrival && segment.arrival.city && segment.arrival.city.name || 'connecting city' }}</span>\n                    </div>\n                  </div>\n                </div>\n              </details>\n            </div>\n\n            <!-- Branded Fare Information (if available) -->\n            <div class=\"branded-fare-section\" *ngIf=\"flight.offers && flight.offers[0] && flight.offers[0].brandedFare\">\n              <details class=\"branded-fare-details\">\n                <summary class=\"branded-fare-summary\">\n                  <i class=\"fas fa-certificate\"></i> {{ flight.offers[0].brandedFare.name || 'Branded Fare' }} Details\n                </summary>\n                <div class=\"branded-fare-content\">\n                  <div class=\"branded-fare-description\" *ngIf=\"flight.offers[0].brandedFare.description\">\n                    {{ flight.offers[0].brandedFare.description }}\n                  </div>\n                  <div class=\"branded-fare-features\" *ngIf=\"flight.offers[0].brandedFare.features && flight.offers[0].brandedFare.features.length > 0\">\n                    <h4>Features</h4>\n                    <div class=\"feature-item\" *ngFor=\"let feature of flight.offers[0].brandedFare.features\">\n                      <div class=\"feature-name\">{{ feature.commercialName || 'Feature' }}</div>\n                      <div class=\"feature-description\" *ngIf=\"feature.explanations && feature.explanations.length > 0\">\n                        {{ feature.explanations[0].text }}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </details>\n            </div>\n\n            <div class=\"flight-actions\">\n              <button class=\"view-details-button\" (click)=\"viewOfferDetails(flight)\">\n                <i class=\"fas fa-info-circle\"></i>\n                View Details\n              </button>\n              <button class=\"select-button\" [disabled]=\"!flight.offers || flight.offers.length === 0 || flight.offers[0].availability === 0\">\n                <i class=\"fas fa-check-circle\"></i>\n                Select This Flight\n              </button>\n              <!-- Bouton de débogage - visible uniquement en développement -->\n              <button class=\"debug-button\" (click)=\"debugOfferStructure(flight)\" title=\"Debug Offer Structure\">\n                <i class=\"fas fa-bug\"></i>\n                Debug\n              </button>\n            </div>\n          </div>\n\n          <!-- No Results Message -->\n          <div class=\"no-results\" *ngIf=\"!isLoading && !errorMessage && searchResults.length === 0\">\n            <div class=\"no-results-icon\">\n              <i class=\"fas fa-search\"></i>\n            </div>\n            <h3>No flights found</h3>\n            <p>We couldn't find any flights matching your search criteria. Try adjusting your search parameters.</p>\n            <div class=\"no-results-suggestions\">\n              <div class=\"suggestion\">\n                <i class=\"fas fa-calendar-alt\"></i>\n                <span>Try different dates</span>\n              </div>\n              <div class=\"suggestion\">\n                <i class=\"fas fa-map-marker-alt\"></i>\n                <span>Try nearby airports</span>\n              </div>\n              <div class=\"suggestion\">\n                <i class=\"fas fa-plane\"></i>\n                <span>Include flights with stops</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnF,SAAqBC,EAAE,QAAQ,MAAM;AAKrC,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;;;;;;;;;;IC8CpEC,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,YAAA,CAAAC,IAAA,CAAmB;;;;;IACrEP,EAAA,CAAAC,cAAA,eAAyE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,YAAA,CAAAE,IAAA,CAAmB;;;;;;;;;;;;;;IALlGR,EAAA,CAAAC,cAAA,qBAA2E;IAE5CD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAS,UAAA,IAAAC,kDAAA,mBAA4E;IAC5EV,EAAA,CAAAS,UAAA,IAAAE,kDAAA,mBAAmG;IACnGX,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAY,SAAA,YAOS;IACTZ,EAAA,CAAAE,MAAA,GAKF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IApB2CH,EAAA,CAAAa,UAAA,UAAAP,YAAA,CAAkB;IAE3CN,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,YAAA,CAAAQ,IAAA,CAAmB;IAErCd,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAa,UAAA,SAAAP,YAAA,CAAAC,IAAA,CAAmB;IACnBP,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAa,UAAA,SAAAP,YAAA,CAAAS,IAAA,UAAAT,YAAA,CAAAE,IAAA,CAA0C;IAG7CR,EAAA,CAAAI,SAAA,GAME;IANFJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAX,YAAA,CAAAS,IAAA,QAAAT,YAAA,CAAAS,IAAA,QAAAT,YAAA,CAAAS,IAAA,QAAAT,YAAA,CAAAS,IAAA,QAAAT,YAAA,CAAAS,IAAA,QAME;IACJf,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAkB,kBAAA,MAAAZ,YAAA,CAAAS,IAAA,qBAAAT,YAAA,CAAAS,IAAA,kBAAAT,YAAA,CAAAS,IAAA,kBAAAT,YAAA,CAAAS,IAAA,qBAAAT,YAAA,CAAAS,IAAA,6BAKF;;;;;IAKRf,EAAA,CAAAC,cAAA,cAAgI;IAC9HD,EAAA,CAAAY,SAAA,YAAyC;IAACZ,EAAA,CAAAE,MAAA,2CAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA8CEH,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAc,YAAA,CAAAZ,IAAA,CAAmB;;;;;IACrEP,EAAA,CAAAC,cAAA,eAAyE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAc,YAAA,CAAAX,IAAA,CAAmB;;;;;IALlGR,EAAA,CAAAC,cAAA,qBAAyE;IAE1CD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAS,UAAA,IAAAW,kDAAA,mBAA4E;IAC5EpB,EAAA,CAAAS,UAAA,IAAAY,kDAAA,mBAAmG;IACnGrB,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAY,SAAA,YAOS;IACTZ,EAAA,CAAAE,MAAA,GAKF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IApByCH,EAAA,CAAAa,UAAA,UAAAM,YAAA,CAAkB;IAEzCnB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAc,YAAA,CAAAL,IAAA,CAAmB;IAErCd,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAa,UAAA,SAAAM,YAAA,CAAAZ,IAAA,CAAmB;IACnBP,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAa,UAAA,SAAAM,YAAA,CAAAJ,IAAA,UAAAI,YAAA,CAAAX,IAAA,CAA0C;IAG7CR,EAAA,CAAAI,SAAA,GAME;IANFJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAE,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAAAI,YAAA,CAAAJ,IAAA,QAME;IACJf,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAkB,kBAAA,MAAAC,YAAA,CAAAJ,IAAA,qBAAAI,YAAA,CAAAJ,IAAA,kBAAAI,YAAA,CAAAJ,IAAA,kBAAAI,YAAA,CAAAJ,IAAA,qBAAAI,YAAA,CAAAJ,IAAA,6BAKF;;;;;IAKRf,EAAA,CAAAC,cAAA,cAA4H;IAC1HD,EAAA,CAAAY,SAAA,YAAyC;IAACZ,EAAA,CAAAE,MAAA,0CAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgCNH,EAAA,CAAAC,cAAA,cAAwH;IACtHD,EAAA,CAAAY,SAAA,YAAyC;IAACZ,EAAA,CAAAE,MAAA,6BAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA6BFH,EAAA,CAAAC,cAAA,iBAA8E;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA5DH,EAAA,CAAAa,UAAA,UAAAS,eAAA,CAAAC,KAAA,CAA2B;IAACvB,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAiB,eAAA,CAAAE,KAAA,CAAuB;;;;;IAYvGxB,EAAA,CAAAY,SAAA,YAAgD;;;;;IAChDZ,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtCH,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAY,SAAA,cAA2B;IAC7BZ,EAAA,CAAAG,YAAA,EAAM;;;;;IAsIhBH,EAAA,CAAAC,cAAA,eAAiD;IAG3CD,EAAA,CAAAY,SAAA,aAA4B;IAI9BZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAI5CH,EAAA,CAAAC,cAAA,eAAgE;IAE5DD,EAAA,CAAAY,SAAA,YAAyC;IAC3CZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/CH,EAAA,CAAAC,cAAA,kBAAkD;IAArBD,EAAA,CAAAyB,UAAA,mBAAAC,oEAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA7B,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAC/ChC,EAAA,CAAAY,SAAA,aAA2B;IAACZ,EAAA,CAAAE,MAAA,kBAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHgBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAA4B,OAAA,CAAAC,YAAA,CAAkB;;;;;IAUvClC,EAAA,CAAAC,cAAA,QAAsC;IAAAD,EAAA,CAAAE,MAAA,qEAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACxGH,EAAA,CAAAC,cAAA,QAAoC;IACND,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,sBAChE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAD0BH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAA8B,OAAA,CAAAC,aAAA,CAAAC,MAAA,CAA0B;;;;;IAI1DrC,EAAA,CAAAC,cAAA,eAA8D;IAE1DD,EAAA,CAAAY,SAAA,aAAuC;IACvCZ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAC,cAAA,kBAA8B;IACND,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnDH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC3DH,EAAA,CAAAC,cAAA,mBAA0B;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7DH,EAAA,CAAAC,cAAA,mBAAwB;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAWvDH,EAAA,CAAAY,SAAA,eAG0B;;;;IAFrBZ,EAAA,CAAAa,UAAA,QAAAyB,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,EAAAzC,EAAA,CAAA0C,aAAA,CAA6C;;;;;IAGlD1C,EAAA,CAAAY,SAAA,aACyC;;;;;IAMzCZ,EAAA,CAAAC,cAAA,gBAA2H;IACzHD,EAAA,CAAAY,SAAA,aAA0B;IAC1BZ,EAAA,CAAAE,MAAA,GAGF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHLH,EAAA,CAAAI,SAAA,GAGF;IAHEJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAI,cAAA,IAAAL,UAAA,CAAAC,KAAA,IAAAI,cAAA,CAAAC,WAAA,IAAAN,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAI,cAAA,IAAAL,UAAA,CAAAC,KAAA,IAAAI,cAAA,CAAA7B,IAAA,kBAAAwB,UAAA,CAAAO,QAAA,MAGF;;;;;IAKF7C,EAAA,CAAAC,cAAA,gBAAsG;IACpGD,EAAA,CAAAY,SAAA,aAA2B;IAACZ,EAAA,CAAAE,MAAA,eAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAAkG;IAChGD,EAAA,CAAAY,SAAA,YAA4B;IAACZ,EAAA,CAAAE,MAAA,GAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADwBH,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAC,KAAA,IAAAO,WAAA,CAAAhC,IAAA,MAC/B;;;;;IAEAd,EAAA,CAAAC,cAAA,gBAA0G;IACxGD,EAAA,CAAAY,SAAA,aAAkC;IAACZ,EAAA,CAAAE,MAAA,qBACrC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;;;;IAOPH,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAY,SAAA,YAAqJ;IACrJZ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFUH,EAAA,CAAAI,SAAA,GAAiI;IAAjIJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAA+C,eAAA,IAAAC,GAAA,EAAAV,UAAA,CAAAW,MAAA,IAAAC,YAAA,MAAAZ,UAAA,CAAAW,MAAA,IAAAC,YAAA,OAAiI;IAChJlD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,oBAAAoB,UAAA,CAAAW,MAAA,IAAAC,YAAA,MACF;;;;;IAGAlD,EAAA,CAAAC,cAAA,gBAAyG;IACvGD,EAAA,CAAAY,SAAA,aAA4B;IAACZ,EAAA,CAAAE,MAAA,GAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADwBH,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAAkB,kBAAA,eAAAiC,OAAA,CAAAC,oBAAA,CAAAd,UAAA,CAAAW,MAAA,IAAAI,SAAA,OAC/B;;;;;IA6BErD,EAAA,CAAAC,cAAA,eAA4F;IACjED,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,GAClE;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADqBH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAe,SAAA,CAA+B;IAAQtD,EAAA,CAAAI,SAAA,GAClE;IADkEJ,EAAA,CAAAkB,kBAAA,UAAAoB,UAAA,CAAAC,KAAA,IAAAe,SAAA,qBAClE;;;;;IACAtD,EAAA,CAAAC,cAAA,eAAqG;IACnGD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBNH,EAAA,CAAAC,cAAA,eAA6F;IACrFD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAApDH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,CAAAkD,OAAA,CAAAC,kBAAA,CAAAC,WAAA,CAAAC,WAAA,EAA6C;;;;;IAErD1D,EAAA,CAAAC,cAAA,eAAsH;IAC9GD,EAAA,CAAAE,MAAA,uCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IANjDH,EAAA,CAAAC,cAAA,eAA4J;IACtJD,EAAA,CAAAY,SAAA,YAA+B;IAACZ,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAS,UAAA,IAAAkD,8DAAA,mBAEM;;IACN3D,EAAA,CAAAS,UAAA,IAAAmD,8DAAA,mBAEM;IACR5D,EAAA,CAAAG,YAAA,EAAM;;;;IANqBH,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAA6D,WAAA,OAAAvB,UAAA,CAAAC,KAAA,IAAAuB,mBAAA,QAAkD;IAGrE9D,EAAA,CAAAI,SAAA,GAA8F;IAA9FJ,EAAA,CAAAa,UAAA,UAAAyB,UAAA,CAAAC,KAAA,IAAAuB,mBAAA,IAAAxB,UAAA,CAAAC,KAAA,IAAAuB,mBAAA,CAAAzB,MAAA,OAA8F;;;;;IAQpGrC,EAAA,CAAAC,cAAA,eAAkF;IAC1ED,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtCH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAA0D,WAAA,CAAAjD,IAAA,cAA+B;;;;;IAHzCd,EAAA,CAAAC,cAAA,eAAsI;IAChID,EAAA,CAAAY,SAAA,aAAqC;IAACZ,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAA,CAAAS,UAAA,IAAAuD,8DAAA,mBAEM;;IACRhE,EAAA,CAAAG,YAAA,EAAM;;;;IAHqBH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAA6D,WAAA,OAAAvB,UAAA,CAAAC,KAAA,IAAA0B,QAAA,QAAuC;;;;;IAQhEjE,EAAA,CAAAC,cAAA,eAAsD;IAC7BD,EAAA,CAAAE,MAAA,GAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzDH,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAkB,kBAAA,SAAAlB,EAAA,CAAA6D,WAAA,OAAAvB,UAAA,CAAAW,MAAA,IAAAiB,OAAA,gBAAkD;;;;;IAE3ElE,EAAA,CAAAC,cAAA,eAA6D;IAEzDD,EAAA,CAAAY,SAAA,QAAqI;IACrIZ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFFH,EAAA,CAAAI,SAAA,GAA6H;IAA7HJ,EAAA,CAAAmE,UAAA,CAAA7B,UAAA,CAAAW,MAAA,IAAAmB,cAAA,CAAAC,UAAA,0EAA6H;IAChIrE,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAW,MAAA,IAAAmB,cAAA,CAAAC,UAAA,wCACF;;;;;IATJrE,EAAA,CAAAC,cAAA,eAA6E;IACvED,EAAA,CAAAY,SAAA,aAA0B;IAACZ,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAS,UAAA,IAAA6D,8DAAA,mBAEM;IACNtE,EAAA,CAAAS,UAAA,IAAA8D,8DAAA,mBAKM;IACRvE,EAAA,CAAAG,YAAA,EAAM;;;;IATkBH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAiB,OAAA,CAA8B;IAG9BlE,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAmB,cAAA,CAAqC;;;;;IAoBzDpE,EAAA,CAAAC,cAAA,eAAuF;IAEnFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAsD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFpFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwE,kBAAA,MAAAC,OAAA,CAAAC,oBAAA,CAAAC,QAAA,CAAAC,aAAA,UAAAD,QAAA,CAAAE,cAAA,OACF;IACyB7E,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA8E,WAAA,OAAAH,QAAA,CAAAI,KAAA,CAAAC,MAAA,EAAAL,QAAA,CAAAI,KAAA,CAAAE,QAAA,EAAsD;;;;;IAEjFjF,EAAA,CAAAC,cAAA,eAA4E;IAClDD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAwF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA/FH,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA8E,WAAA,OAAAxC,UAAA,CAAAW,MAAA,IAAAiC,UAAA,CAAAF,MAAA,EAAA1C,UAAA,CAAAW,MAAA,IAAAiC,UAAA,CAAAD,QAAA,EAAwF;;;;;IAVrHjF,EAAA,CAAAC,cAAA,eAA8G;IACxGD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAS,UAAA,IAAA0E,oEAAA,mBAKM;IACNnF,EAAA,CAAAS,UAAA,IAAA2E,oEAAA,mBAGM;IACNpF,EAAA,CAAAC,cAAA,eAA6B;IACDD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA8E;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAZrEH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAa,UAAA,YAAAyB,UAAA,CAAAW,MAAA,IAAAoC,cAAA,CAAA9C,KAAA,CAAwC;IAM5CvC,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAiC,UAAA,CAAiC;IAM7ClF,EAAA,CAAAI,SAAA,GAA8E;IAA9EJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA8E,WAAA,QAAAxC,UAAA,CAAAW,MAAA,IAAA8B,KAAA,CAAAC,MAAA,EAAA1C,UAAA,CAAAW,MAAA,IAAA8B,KAAA,CAAAE,QAAA,EAA8E;;;;;IAhB/GjF,EAAA,CAAAC,cAAA,eAA+E;IAE7ED,EAAA,CAAAS,UAAA,IAAA6E,8DAAA,oBAgBM;IACRtF,EAAA,CAAAG,YAAA,EAAM;;;;IAjB0BH,EAAA,CAAAI,SAAA,GAA8E;IAA9EJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAoC,cAAA,IAAA/C,UAAA,CAAAW,MAAA,IAAAoC,cAAA,CAAA9C,KAAA,CAA8E;;;;;IA+D1GvC,EAAA,CAAAC,cAAA,eAA0E;IACxED,EAAA,CAAAY,SAAA,aAAqC;IACrCZ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA6K;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;IAApLH,EAAA,CAAAI,SAAA,GAA6K;IAA7KJ,EAAA,CAAAwE,kBAAA,KAAAe,OAAA,CAAAC,oBAAA,CAAAC,WAAA,EAAAnD,UAAA,CAAAC,KAAA,IAAAmD,QAAA,CAAAC,KAAA,wBAAAF,WAAA,CAAAG,OAAA,IAAAH,WAAA,CAAAG,OAAA,CAAApF,IAAA,IAAAiF,WAAA,CAAAG,OAAA,CAAApF,IAAA,CAAAM,IAAA,0BAA6K;;;;;IArCvLd,EAAA,CAAAC,cAAA,eAA0F;IAEzDD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/FH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5DH,EAAA,CAAAC,cAAA,eAA2B;IAELD,EAAA,CAAAE,MAAA,IAAoE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5FH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAA+F;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjIH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAyF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG5HH,EAAA,CAAAC,cAAA,gBAA8B;IAE1BD,EAAA,CAAAY,SAAA,iBAAuC;IACvCZ,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAY,SAAA,iBAA0B;IAC5BZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAY,SAAA,iBAAqC;IACvCZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAY,SAAA,cAA4B;IAACZ,EAAA,CAAAE,MAAA,IAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,gBAA6B;IACTD,EAAA,CAAAE,MAAA,IAAgE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxFH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAAyF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3HH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAmF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKxHH,EAAA,CAAAS,UAAA,KAAAoF,qEAAA,mBAGM;IACR7F,EAAA,CAAAG,YAAA,EAAM;;;;;;;IArC2BH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAkB,kBAAA,aAAAyE,KAAA,SAAmB;IAClB3F,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,iBAAA,CAAAoF,WAAA,CAAAjD,OAAA,IAAAiD,WAAA,CAAAjD,OAAA,CAAA1B,IAAA,cAA0D;IAC3Dd,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAoF,WAAA,CAAAK,QAAA,CAAsB;IAI/B9F,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAK,iBAAA,CAAAoF,WAAA,CAAAM,SAAA,GAAAC,OAAA,CAAAC,UAAA,CAAAR,WAAA,CAAAM,SAAA,CAAAG,IAAA,UAAoE;IAEzDlG,EAAA,CAAAI,SAAA,GAA+F;IAA/FJ,EAAA,CAAAK,iBAAA,CAAAoF,WAAA,CAAAM,SAAA,IAAAN,WAAA,CAAAM,SAAA,CAAAI,OAAA,IAAAV,WAAA,CAAAM,SAAA,CAAAI,OAAA,CAAA5F,IAAA,UAA+F;IAClGP,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAK,iBAAA,CAAAoF,WAAA,CAAAM,SAAA,IAAAN,WAAA,CAAAM,SAAA,CAAAvF,IAAA,IAAAiF,WAAA,CAAAM,SAAA,CAAAvF,IAAA,CAAAM,IAAA,UAAyF;IAYpFd,EAAA,CAAAI,SAAA,GAC/B;IAD+BJ,EAAA,CAAAkB,kBAAA,MAAA8E,OAAA,CAAAI,cAAA,CAAAX,WAAA,CAAAY,QAAA,OAC/B;IAGkBrG,EAAA,CAAAI,SAAA,GAAgE;IAAhEJ,EAAA,CAAAK,iBAAA,CAAAoF,WAAA,CAAAG,OAAA,GAAAI,OAAA,CAAAC,UAAA,CAAAR,WAAA,CAAAG,OAAA,CAAAM,IAAA,UAAgE;IAErDlG,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAK,iBAAA,CAAAoF,WAAA,CAAAG,OAAA,IAAAH,WAAA,CAAAG,OAAA,CAAAO,OAAA,IAAAV,WAAA,CAAAG,OAAA,CAAAO,OAAA,CAAA5F,IAAA,UAAyF;IAC5FP,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAK,iBAAA,CAAAoF,WAAA,CAAAG,OAAA,IAAAH,WAAA,CAAAG,OAAA,CAAApF,IAAA,IAAAiF,WAAA,CAAAG,OAAA,CAAApF,IAAA,CAAAM,IAAA,UAAmF;IAKtFd,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAa,UAAA,SAAA8E,KAAA,GAAArD,UAAA,CAAAC,KAAA,IAAAmD,QAAA,CAAArD,MAAA,KAA6C;;;;;IAzChFrC,EAAA,CAAAC,cAAA,eAAyI;IAGnID,EAAA,CAAAY,SAAA,YAAsC;IAACZ,EAAA,CAAAE,MAAA,GACzC;IAAAF,EAAA,CAAAG,YAAA,EAAU;IACVH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAS,UAAA,IAAA6F,8DAAA,qBAuCM;IACRtG,EAAA,CAAAG,YAAA,EAAM;;;;IA3CmCH,EAAA,CAAAI,SAAA,GACzC;IADyCJ,EAAA,CAAAkB,kBAAA,uBAAAoB,UAAA,CAAAC,KAAA,IAAAmD,QAAA,CAAArD,MAAA,OACzC;IAEgDrC,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAa,UAAA,YAAAyB,UAAA,CAAAC,KAAA,IAAAmD,QAAA,CAA6B;;;;;IAmD3E1F,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAW,MAAA,IAAAsD,WAAA,CAAAC,WAAA,MACF;;;;;IAKIxG,EAAA,CAAAC,cAAA,eAAiG;IAC/FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAuF,WAAA,CAAAC,YAAA,IAAAC,IAAA,MACF;;;;;IAJF3G,EAAA,CAAAC,cAAA,eAAwF;IAC5DD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzEH,EAAA,CAAAS,UAAA,IAAAmG,0EAAA,mBAEM;IACR5G,EAAA,CAAAG,YAAA,EAAM;;;;IAJsBH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,iBAAA,CAAAoG,WAAA,CAAAI,cAAA,cAAyC;IACjC7G,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAa,UAAA,SAAA4F,WAAA,CAAAC,YAAA,IAAAD,WAAA,CAAAC,YAAA,CAAArE,MAAA,KAA6D;;;;;IAJnGrC,EAAA,CAAAC,cAAA,eAAqI;IAC/HD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAS,UAAA,IAAAqG,oEAAA,mBAKM;IACR9G,EAAA,CAAAG,YAAA,EAAM;;;;IAN0CH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAa,UAAA,YAAAyB,UAAA,CAAAW,MAAA,IAAAsD,WAAA,CAAAQ,QAAA,CAAwC;;;;;IAX9F/G,EAAA,CAAAC,cAAA,eAA4G;IAGtGD,EAAA,CAAAY,SAAA,aAAkC;IAACZ,EAAA,CAAAE,MAAA,GACrC;IAAAF,EAAA,CAAAG,YAAA,EAAU;IACVH,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAS,UAAA,IAAAuG,8DAAA,mBAEM;IACNhH,EAAA,CAAAS,UAAA,IAAAwG,8DAAA,mBAQM;IACRjH,EAAA,CAAAG,YAAA,EAAM;;;;IAf+BH,EAAA,CAAAI,SAAA,GACrC;IADqCJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAW,MAAA,IAAAsD,WAAA,CAAAzF,IAAA,gCACrC;IAEyCd,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAsD,WAAA,CAAAC,WAAA,CAA8C;IAGjDxG,EAAA,CAAAI,SAAA,GAA+F;IAA/FJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAsD,WAAA,CAAAQ,QAAA,IAAAzE,UAAA,CAAAW,MAAA,IAAAsD,WAAA,CAAAQ,QAAA,CAAA1E,MAAA,KAA+F;;;;;;IA7N3IrC,EAAA,CAAAC,cAAA,eAA+G;IAIvGD,EAAA,CAAAS,UAAA,IAAAyG,uDAAA,mBAG0B;IAC1BlH,EAAA,CAAAS,UAAA,IAAA0G,qDAAA,iBACyC;IAC3CnH,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACAD,EAAA,CAAAE,MAAA,GAA2G;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7IH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAwE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE3GH,EAAA,CAAAS,UAAA,KAAA2G,yDAAA,oBAKO;IACTpH,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAS,UAAA,KAAA4G,yDAAA,oBAEO;IACPrH,EAAA,CAAAS,UAAA,KAAA6G,yDAAA,oBAEO;IAEPtH,EAAA,CAAAS,UAAA,KAAA8G,yDAAA,oBAEO;IACTvH,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAA0B;IACED,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAS,UAAA,KAAA+G,yDAAA,oBAGO;IAGPxH,EAAA,CAAAS,UAAA,KAAAgH,yDAAA,oBAEO;IACTzH,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAA4B;IAGJD,EAAA,CAAAE,MAAA,IAAuH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/IH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAAwJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1LH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAkJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIrLH,EAAA,CAAAC,cAAA,gBAA6B;IAEzBD,EAAA,CAAAY,SAAA,iBAAuC;IACvCZ,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAY,SAAA,iBAA0B;IAC1BZ,EAAA,CAAAC,cAAA,iBAAyB;IACvBD,EAAA,CAAAY,SAAA,cAA4B;IAC9BZ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAY,SAAA,iBAAqC;IACvCZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAY,SAAA,cAA4B;IAC5BZ,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAS,UAAA,KAAAiH,wDAAA,mBAEM;IACN1H,EAAA,CAAAS,UAAA,KAAAkH,wDAAA,mBAEM;IACR3H,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,gBAAqB;IACDD,EAAA,CAAAE,MAAA,IAAmH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3IH,EAAA,CAAAC,cAAA,gBAAsB;IACOD,EAAA,CAAAE,MAAA,IAAkJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpLH,EAAA,CAAAC,cAAA,iBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA4I;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKjLH,EAAA,CAAAC,cAAA,gBAA6B;IAE3BD,EAAA,CAAAS,UAAA,KAAAmH,wDAAA,mBAQM;IAGN5H,EAAA,CAAAS,UAAA,KAAAoH,wDAAA,mBAKM;IAGN7H,EAAA,CAAAS,UAAA,KAAAqH,wDAAA,mBAWM;IACR9H,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,gBAAqC;IAG/BD,EAAA,CAAAY,SAAA,aAAsC;IAACZ,EAAA,CAAAE,MAAA,yBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAU;IACVH,EAAA,CAAAS,UAAA,KAAAsH,wDAAA,mBAmBM;IACR/H,EAAA,CAAAG,YAAA,EAAU;IAIZH,EAAA,CAAAS,UAAA,KAAAuH,wDAAA,mBAgDM;IAGNhI,EAAA,CAAAS,UAAA,KAAAwH,wDAAA,mBAoBM;IAENjI,EAAA,CAAAC,cAAA,gBAA4B;IACUD,EAAA,CAAAyB,UAAA,mBAAAyG,2EAAA;MAAA,MAAAC,WAAA,GAAAnI,EAAA,CAAA2B,aAAA,CAAAyG,IAAA;MAAA,MAAA9F,UAAA,GAAA6F,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAAtI,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAuG,OAAA,CAAAC,gBAAA,CAAAjG,UAAA,CAAwB;IAAA,EAAC;IACpEtC,EAAA,CAAAY,SAAA,cAAkC;IAClCZ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAA+H;IAC7HD,EAAA,CAAAY,SAAA,cAAmC;IACnCZ,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,mBAAiG;IAApED,EAAA,CAAAyB,UAAA,mBAAA+G,2EAAA;MAAA,MAAAL,WAAA,GAAAnI,EAAA,CAAA2B,aAAA,CAAAyG,IAAA;MAAA,MAAA9F,UAAA,GAAA6F,WAAA,CAAAE,SAAA;MAAA,MAAAI,OAAA,GAAAzI,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAA0G,OAAA,CAAAC,mBAAA,CAAApG,UAAA,CAA2B;IAAA,EAAC;IAChEtC,EAAA,CAAAY,SAAA,cAA0B;IAC1BZ,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAvPiDH,EAAA,CAAA2I,WAAA,iBAAAC,OAAA,CAAAC,iBAAA,CAAAvG,UAAA,EAAgD;IAIhGtC,EAAA,CAAAI,SAAA,GAAyG;IAAzGJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,IAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,CAAyG;IAI3GzC,EAAA,CAAAI,SAAA,GAA4G;IAA5GJ,EAAA,CAAAa,UAAA,WAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,IAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAAC,aAAA,EAA4G;IAIrFzC,EAAA,CAAAI,SAAA,GAA2G;IAA3GJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAC,OAAA,GAAAF,UAAA,CAAAC,KAAA,IAAAC,OAAA,CAAA1B,IAAA,aAA2G;IAC1Gd,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,MAAAD,UAAA,CAAAC,KAAA,IAAAuD,QAAA,SAAwE;IAEvE9F,EAAA,CAAAI,SAAA,GAA4F;IAA5FJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAO,QAAA,IAAAP,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAI,cAAA,CAA4F;IAU/F3C,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAe,SAAA,OAAwE;IAGxEtD,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAO,WAAA,CAAoE;IAI5D9C,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAX,UAAA,CAAAW,MAAA,OAAAX,UAAA,CAAAW,MAAA,IAAA6F,QAAA,CAAoE;IAOpF9I,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAuI,OAAA,CAAAG,WAAA,CAAAzG,UAAA,EAAyB;IAEjBtC,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAX,UAAA,CAAAW,MAAA,CAAAZ,MAAA,KAA+C;IAMjDrC,EAAA,CAAAI,SAAA,GAA6E;IAA7EJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAX,UAAA,CAAAW,MAAA,CAAAZ,MAAA,QAAAC,UAAA,CAAAW,MAAA,IAAAI,SAAA,CAA6E;IASnFrD,EAAA,CAAAI,SAAA,GAAuH;IAAvHJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAwD,SAAA,GAAA6C,OAAA,CAAA3C,UAAA,CAAA3D,UAAA,CAAAC,KAAA,IAAAwD,SAAA,CAAAG,IAAA,UAAuH;IAE5GlG,EAAA,CAAAI,SAAA,GAAwJ;IAAxJJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAwD,SAAA,IAAAzD,UAAA,CAAAC,KAAA,IAAAwD,SAAA,CAAAI,OAAA,GAAA7D,UAAA,CAAAC,KAAA,IAAAwD,SAAA,CAAAI,OAAA,CAAA5F,IAAA,SAAwJ;IAC3JP,EAAA,CAAAI,SAAA,GAAkJ;IAAlJJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAwD,SAAA,IAAAzD,UAAA,CAAAC,KAAA,IAAAwD,SAAA,CAAAvF,IAAA,GAAA8B,UAAA,CAAAC,KAAA,IAAAwD,SAAA,CAAAvF,IAAA,CAAAM,IAAA,SAAkJ;IAiB1Kd,EAAA,CAAAI,SAAA,IACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAoB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,MAAAqG,OAAA,CAAAxC,cAAA,CAAA9D,UAAA,CAAAC,KAAA,IAAA8D,QAAA,eACF;IACoBrG,EAAA,CAAAI,SAAA,GAAsE;IAAtEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAe,SAAA,KAAsE;IAG/DtD,EAAA,CAAAI,SAAA,GAAwE;IAAxEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAe,SAAA,OAAwE;IAMjFtD,EAAA,CAAAI,SAAA,GAAmH;IAAnHJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAqD,OAAA,GAAAgD,OAAA,CAAA3C,UAAA,CAAA3D,UAAA,CAAAC,KAAA,IAAAqD,OAAA,CAAAM,IAAA,UAAmH;IAExGlG,EAAA,CAAAI,SAAA,GAAkJ;IAAlJJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAqD,OAAA,IAAAtD,UAAA,CAAAC,KAAA,IAAAqD,OAAA,CAAAO,OAAA,GAAA7D,UAAA,CAAAC,KAAA,IAAAqD,OAAA,CAAAO,OAAA,CAAA5F,IAAA,SAAkJ;IACrJP,EAAA,CAAAI,SAAA,GAA4I;IAA5IJ,EAAA,CAAAK,iBAAA,CAAAiC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAqD,OAAA,IAAAtD,UAAA,CAAAC,KAAA,IAAAqD,OAAA,CAAApF,IAAA,GAAA8B,UAAA,CAAAC,KAAA,IAAAqD,OAAA,CAAApF,IAAA,CAAAM,IAAA,SAA4I;IAOlKd,EAAA,CAAAI,SAAA,GAA8H;IAA9HJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAuB,mBAAA,IAAAxB,UAAA,CAAAC,KAAA,IAAAuB,mBAAA,CAAAzB,MAAA,KAA8H;IAW9HrC,EAAA,CAAAI,SAAA,GAAwG;IAAxGJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAA0B,QAAA,IAAA3B,UAAA,CAAAC,KAAA,IAAA0B,QAAA,CAAA5B,MAAA,KAAwG;IAQxGrC,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAX,UAAA,CAAAW,MAAA,CAAAZ,MAAA,KAA+C;IAqBfrC,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAX,UAAA,CAAAW,MAAA,IAAuC;IAwBlDjD,EAAA,CAAAI,SAAA,GAAwG;IAAxGJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,OAAAD,UAAA,CAAAC,KAAA,IAAAmD,QAAA,IAAApD,UAAA,CAAAC,KAAA,IAAAmD,QAAA,CAAArD,MAAA,KAAwG;IAmDpGrC,EAAA,CAAAI,SAAA,GAAuE;IAAvEJ,EAAA,CAAAa,UAAA,SAAAyB,UAAA,CAAAW,MAAA,IAAAX,UAAA,CAAAW,MAAA,OAAAX,UAAA,CAAAW,MAAA,IAAAsD,WAAA,CAAuE;IA2B1EvG,EAAA,CAAAI,SAAA,GAAgG;IAAhGJ,EAAA,CAAAa,UAAA,cAAAyB,UAAA,CAAAW,MAAA,IAAAX,UAAA,CAAAW,MAAA,CAAAZ,MAAA,UAAAC,UAAA,CAAAW,MAAA,IAAAC,YAAA,OAAgG;;;;;IAalIlD,EAAA,CAAAC,cAAA,eAA0F;IAEtFD,EAAA,CAAAY,SAAA,YAA6B;IAC/BZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wGAAiG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxGH,EAAA,CAAAC,cAAA,eAAoC;IAEhCD,EAAA,CAAAY,SAAA,YAAmC;IACnCZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElCH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAY,SAAA,cAAqC;IACrCZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElCH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAY,SAAA,cAA4B;IAC5BZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAtSjDH,EAAA,CAAAC,cAAA,eAAwE;IAG9DD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAS,UAAA,IAAAuI,+CAAA,gBAAwG;IACxGhJ,EAAA,CAAAS,UAAA,IAAAwI,+CAAA,gBAEI;IACNjJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAS,UAAA,IAAAyI,iDAAA,oBAWM;IACRlJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAS,UAAA,IAAA0I,iDAAA,qBAyPM;IAGNnJ,EAAA,CAAAS,UAAA,KAAA2I,kDAAA,oBAoBM;IACRpJ,EAAA,CAAAG,YAAA,EAAM;;;;IAtSEH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAa,UAAA,SAAAwI,OAAA,CAAAjH,aAAA,CAAAC,MAAA,OAAgC;IAChCrC,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAa,UAAA,SAAAwI,OAAA,CAAAjH,aAAA,CAAAC,MAAA,KAA8B;IAKNrC,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAa,UAAA,SAAAwI,OAAA,CAAAjH,aAAA,CAAAC,MAAA,KAA8B;IAepCrC,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAa,UAAA,YAAAwI,OAAA,CAAAjH,aAAA,CAAgB;IA4PfpC,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAa,UAAA,UAAAwI,OAAA,CAAAC,SAAA,KAAAD,OAAA,CAAAnH,YAAA,IAAAmH,OAAA,CAAAjH,aAAA,CAAAC,MAAA,OAA+D;;;;;IA7S9FrC,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAS,UAAA,IAAA8I,2CAAA,kBAUM;IAENvJ,EAAA,CAAAS,UAAA,IAAA+I,2CAAA,oBASM;IAENxJ,EAAA,CAAAS,UAAA,IAAAgJ,2CAAA,oBA2SM;IACRzJ,EAAA,CAAAG,YAAA,EAAM;;;;IAnUEH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAa,UAAA,SAAA6I,OAAA,CAAAJ,SAAA,CAAe;IAYftJ,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAa,UAAA,UAAA6I,OAAA,CAAAJ,SAAA,IAAAI,OAAA,CAAAxH,YAAA,CAAgC;IAWhClC,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAa,UAAA,UAAA6I,OAAA,CAAAJ,SAAA,KAAAI,OAAA,CAAAxH,YAAA,CAAiC;;;AD9W7C,OAAM,MAAOyH,oBAAoB;EA2B/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IA5BhB,KAAAC,kBAAkB,GAAqB,EAAE;IACzC,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAAX,SAAS,GAAG,KAAK;IACjB,KAAAlH,aAAa,GAAa,EAAE;IAC5B,KAAA8H,WAAW,GAAG,KAAK;IACnB,KAAAhI,YAAY,GAAG,EAAE;IACjB,KAAAiI,YAAY,GAAG,EAAE;IAEjB;IACA,KAAAC,cAAc,GAAG,CACf;MAAE7I,KAAK,EAAExB,aAAa,CAACsK,KAAK;MAAE7I,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAExB,aAAa,CAACuK,KAAK;MAAE9I,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAExB,aAAa,CAACwK,MAAM;MAAE/I,KAAK,EAAE;IAAQ,CAAE,CACjD;IAED;IACA,KAAAgJ,aAAa,GAAG,CACd;MAAEjJ,KAAK,EAAE1B,eAAe,CAAC4K,KAAK;MAAEjJ,KAAK,EAAE;IAAO,CAAE,EAChD;MAAED,KAAK,EAAE1B,eAAe,CAAC6K,OAAO;MAAElJ,KAAK,EAAE;IAAS,CAAE,EACpD;MAAED,KAAK,EAAE1B,eAAe,CAAC8K,QAAQ;MAAEnJ,KAAK,EAAE;IAAU,CAAE,CACvD;IAUC;IACA,IAAI,CAACoJ,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAErD;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACnB,EAAE,CAACoB,KAAK,CAAC;MAC9B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAE3L,UAAU,CAAC4L,QAAQ,CAAC;MACrCC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE7L,UAAU,CAAC4L,QAAQ,CAAC;MAC1CE,iBAAiB,EAAE,CAAC,EAAE,EAAE9L,UAAU,CAAC4L,QAAQ,CAAC;MAC5CG,qBAAqB,EAAE,CAAC,CAAC,EAAE/L,UAAU,CAAC4L,QAAQ,CAAC;MAC/CI,eAAe,EAAE,CAAC,EAAE,EAAEhM,UAAU,CAAC4L,QAAQ,CAAC;MAC1CK,mBAAmB,EAAE,CAAC,CAAC,EAAEjM,UAAU,CAAC4L,QAAQ,CAAC;MAC7CM,aAAa,EAAE,CAAC,IAAI,CAACb,OAAO,EAAErL,UAAU,CAAC4L,QAAQ,CAAC;MAClDtG,cAAc,EAAE,CAAC,CAAC,EAAE,CAACtF,UAAU,CAAC4L,QAAQ,EAAE5L,UAAU,CAACmM,GAAG,CAAC,CAAC,CAAC,EAAEnM,UAAU,CAACoM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChF/G,aAAa,EAAE,CAAC,CAAC,EAAErF,UAAU,CAAC4L,QAAQ,CAAC;MAEvC;MACArI,WAAW,EAAE,CAAC,CAAC,EAAEvD,UAAU,CAAC4L,QAAQ,CAAC;MACrCS,OAAO,EAAE,CAAC,KAAK,CAAC;MAEhB;MACAC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClB5G,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjB6G,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,wBAAwB,EAAE,CAAC,KAAK,CAAC;MACjCC,6BAA6B,EAAE,CAAC,IAAI,CAAC;MACrCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAE5B;MACAC,sBAAsB,EAAE,CAAC,CAAC;KAC3B,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACAC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EACjF;EAEA;EACA7D,mBAAmBA,CAAC8D,MAAc;IAChCF,OAAO,CAACrB,KAAK,CAAC,oBAAoB,CAAC;IACnCqB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEC,MAAM,CAACC,EAAE,CAAC;IACpCH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,MAAM,CAAC3J,QAAQ,CAAC;IAEhD,IAAI2J,MAAM,CAACvJ,MAAM,IAAIuJ,MAAM,CAACvJ,MAAM,CAACZ,MAAM,GAAG,CAAC,EAAE;MAC7CiK,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,MAAM,CAACvJ,MAAM,CAACZ,MAAM,CAAC;MAEtDmK,MAAM,CAACvJ,MAAM,CAACyJ,OAAO,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAI;QACrCN,OAAO,CAACrB,KAAK,CAAC,SAAS2B,KAAK,GAAG,CAAC,EAAE,CAAC;QACnCN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEI,KAAK,CAACzI,OAAO,IAAIyI,KAAK,CAACF,EAAE,CAAC;QACnDH,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEI,KAAK,CAACzJ,YAAY,CAAC;QAChDoJ,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEI,KAAK,CAAC5H,KAAK,CAAC;QAClCuH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEI,KAAK,CAACvI,cAAc,EAAEC,UAAU,CAAC;QAC5DiI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEI,KAAK,CAACtJ,SAAS,CAAC;QAC3CiJ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEI,KAAK,CAAC7D,QAAQ,CAAC;QACzCwD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,KAAK,CAAC;QACxCL,OAAO,CAACO,QAAQ,EAAE;MACpB,CAAC,CAAC;KACH,MAAM;MACLP,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAGpDD,OAAO,CAACO,QAAQ,EAAE;EACpB;EAEAR,gBAAgBA,CAAA;IACd;IACA;IACA;EAAA;EAGFD,iBAAiBA,CAAA;IACf;IACA,MAAMd,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAAC8B,GAAG,CAAC,uBAAuB,CAAC,EAAEvL,KAAK,IAAI,CAAC;IACtF,MAAMiK,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAAC8B,GAAG,CAAC,qBAAqB,CAAC,EAAEvL,KAAK,IAAI,CAAC;IAElF,IAAI,CAACuI,cAAc,CAACiD,kBAAkB,CAACzB,qBAAqB,CAAC,CAAC0B,SAAS,CAACC,SAAS,IAAG;MAClF,IAAI,CAACjD,kBAAkB,GAAGiD,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAACnD,cAAc,CAACiD,kBAAkB,CAACvB,mBAAmB,CAAC,CAACwB,SAAS,CAACC,SAAS,IAAG;MAChF,IAAI,CAAChD,gBAAgB,GAAGgD,SAAS;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAACjC,UAAU,CAAC8B,GAAG,CAAC,uBAAuB,CAAC,EAAEI,YAAY,CACvDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAACrD,cAAc,CAACiD,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAACjD,kBAAkB,GAAGiD,SAAS;QACnC;QACA,IAAI,CAACjC,UAAU,CAAC8B,GAAG,CAAC,mBAAmB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAACpC,UAAU,CAAC8B,GAAG,CAAC,qBAAqB,CAAC,EAAEI,YAAY,CACrDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAACrD,cAAc,CAACiD,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAAChD,gBAAgB,GAAGgD,SAAS;QACjC;QACA,IAAI,CAACjC,UAAU,CAAC8B,GAAG,CAAC,iBAAiB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACtD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAACpC,UAAU,CAAC8B,GAAG,CAAC,mBAAmB,CAAC,EAAEI,YAAY,CACnDG,IAAI,CACH7N,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC6B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM4L,YAAY,GAAG,IAAI,CAACnC,UAAU,CAAC8B,GAAG,CAAC,uBAAuB,CAAC,EAAEvL,KAAK,IAAI,CAAC;QAC7E;QACA,IAAIA,KAAK,CAACc,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACyH,cAAc,CAACiD,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9D1N,GAAG,CAACsN,SAAS,IAAIA,SAAS,CAACK,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAACzM,IAAI,CAAC0M,WAAW,EAAE,CAACC,QAAQ,CAAClM,KAAK,CAACiM,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAAChN,IAAI,IAAIgN,QAAQ,CAAChN,IAAI,CAACiN,WAAW,EAAE,CAACC,QAAQ,CAAClM,KAAK,CAACiM,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAC1D,cAAc,CAACiD,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAOvN,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAoN,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACjD,kBAAkB,GAAGiD,SAAS;IACrC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACjC,UAAU,CAAC8B,GAAG,CAAC,iBAAiB,CAAC,EAAEI,YAAY,CACjDG,IAAI,CACH7N,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC6B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM4L,YAAY,GAAG,IAAI,CAACnC,UAAU,CAAC8B,GAAG,CAAC,qBAAqB,CAAC,EAAEvL,KAAK,IAAI,CAAC;QAC3E;QACA,IAAIA,KAAK,CAACc,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACyH,cAAc,CAACiD,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9D1N,GAAG,CAACsN,SAAS,IAAIA,SAAS,CAACK,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAACzM,IAAI,CAAC0M,WAAW,EAAE,CAACC,QAAQ,CAAClM,KAAK,CAACiM,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAAChN,IAAI,IAAIgN,QAAQ,CAAChN,IAAI,CAACiN,WAAW,EAAE,CAACC,QAAQ,CAAClM,KAAK,CAACiM,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAC1D,cAAc,CAACiD,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAOvN,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAoN,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAAChD,gBAAgB,GAAGgD,SAAS;IACnC,CAAC,CAAC;EACN;EAEAS,eAAeA,CAACH,QAA+B;IAC7C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAII,WAAW,GAAGJ,QAAQ,CAACzM,IAAI;IAC/B,IAAIyM,QAAQ,CAAChN,IAAI,EAAE;MACjBoN,WAAW,IAAI,KAAKJ,QAAQ,CAAChN,IAAI,GAAG;;IAEtC,IAAIgN,QAAQ,CAACxM,IAAI,KAAKjB,YAAY,CAAC8N,OAAO,IAAIL,QAAQ,CAAC/M,IAAI,EAAE;MAC3DmN,WAAW,IAAI,MAAMJ,QAAQ,CAAC/M,IAAI,EAAE;;IAEtC,OAAOmN,WAAW;EACpB;EAEA3L,QAAQA,CAAA;IACN,IAAI,IAAI,CAACgJ,UAAU,CAAC6C,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC9C,UAAU,CAAC;MAC1C;;IAGF,IAAI,CAAC1B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACpH,YAAY,GAAG,EAAE;IACtB,IAAI,CAACgI,WAAW,GAAG,IAAI;IAEvB,MAAM6D,SAAS,GAAG,IAAI,CAAC/C,UAAU,CAACzJ,KAAK;IAEvC;IACA,MAAMyM,OAAO,GAAuB;MAClCC,WAAW,EAAEF,SAAS,CAAC7C,WAAW;MAClCgD,YAAY,EAAEH,SAAS,CAAC3C,YAAY;MACpC+C,OAAO,EAAEJ,SAAS,CAACtC,aAAa;MAChC2C,kBAAkB,EAAE,CAClB;QACE3B,EAAE,EAAEsB,SAAS,CAAC1C,iBAAiB,EAAEoB,EAAE,IAAI,EAAE;QACzC1L,IAAI,EAAEgN,SAAS,CAACzC;OACjB,CACF;MACD+C,gBAAgB,EAAE,CAChB;QACE5B,EAAE,EAAEsB,SAAS,CAACxC,eAAe,EAAEkB,EAAE,IAAI,EAAE;QACvC1L,IAAI,EAAEgN,SAAS,CAACvC;OACjB,CACF;MACD8C,UAAU,EAAE,CACV;QACEvN,IAAI,EAAEgN,SAAS,CAACnJ,aAAa;QAC7B2J,KAAK,EAAER,SAAS,CAAClJ;OAClB,CACF;MACD2J,qBAAqB,EAAET,SAAS,CAACnC,OAAO;MACxC6C,oBAAoB,EAAE;QACpBC,oBAAoB,EAAE;UACpBxC,sBAAsB,EAAE6B,SAAS,CAAC7B;;OAErC;MACDJ,sBAAsB,EAAEiC,SAAS,CAACjC,sBAAsB;MACxDC,wBAAwB,EAAEgC,SAAS,CAAChC,wBAAwB;MAC5DC,6BAA6B,EAAE+B,SAAS,CAAC/B,6BAA6B;MACtEC,mBAAmB,EAAE8B,SAAS,CAAC9B,mBAAmB;MAClDzB,aAAa,EAAE,CAACuD,SAAS,CAACjL,WAAW,CAAC;MACtC6L,OAAO,EAAEZ,SAAS,CAAClC,OAAO;MAC1B+C,QAAQ,EAAEb,SAAS,CAAC9I;KACrB;IAED,IAAI,CAAC6E,cAAc,CAAC+E,WAAW,CAACb,OAAO,CAAC,CACrChB,SAAS,CAAC;MACT8B,IAAI,EAAGC,QAA6B,IAAI;QACtC,IAAI,CAACzF,SAAS,GAAG,KAAK;QACtB,IAAIyF,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;UAC3B,IAAI,CAAC7M,aAAa,GAAG2M,QAAQ,CAACG,IAAI,CAACC,OAAO;UAE1C;UACA7C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE6C,IAAI,CAACC,SAAS,CAACN,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEpE;UACA,IAAIA,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,IAAIJ,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC9M,MAAM,GAAG,CAAC,EAAE;YAC9EiK,OAAO,CAACrB,KAAK,CAAC,uBAAuB,CAAC;YACtCqB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEwC,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC9M,MAAM,CAAC;YAE3D;YACA,MAAMiN,iBAAiB,GAAGP,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC7B,MAAM,CAACiC,CAAC,IAAIA,CAAC,CAACtM,MAAM,IAAIsM,CAAC,CAACtM,MAAM,CAACZ,MAAM,GAAG,CAAC,CAAC;YAC5FiK,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+C,iBAAiB,CAACjN,MAAM,CAAC;YAE7D;YACA,MAAMmN,kBAAkB,GAAGF,iBAAiB,CAACG,OAAO,CAACF,CAAC,IAAIA,CAAC,CAACtM,MAAM,CAACtD,GAAG,CAAC+P,CAAC,IAAIA,CAAC,CAACxM,YAAY,CAAC,CAAC;YAC5FoJ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEiD,kBAAkB,CAAC;YAEvD;YACA,MAAMG,kBAAkB,GAAGH,kBAAkB,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAI;cAChED,GAAG,CAACC,GAAG,CAAC,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;cAC9B,OAAOD,GAAG;YACZ,CAAC,EAAE,EAA4B,CAAC;YAChCvD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoD,kBAAkB,CAAC;YAEvD;YACA,MAAMI,iBAAiB,GAAGT,iBAAiB,CAAChC,MAAM,CAACiC,CAAC,IAClDA,CAAC,CAACtM,MAAM,CAAC+M,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACtL,cAAc,IAAIsL,CAAC,CAACtL,cAAc,CAACC,UAAU,KAAK,IAAI,CAAC,CAC7E;YACDiI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwD,iBAAiB,CAAC1N,MAAM,CAAC;YAE5DiK,OAAO,CAACO,QAAQ,EAAE;;UAGpB;UACA,IAAIkC,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACe,QAAQ,EAAE;YAC3C,IAAI,CAAC9F,YAAY,GAAG4E,QAAQ,CAACG,IAAI,CAACe,QAAQ;YAC1C3D,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACpC,YAAY,CAAC;;UAErE;UAAA,KACK,IAAI4E,QAAQ,CAACC,MAAM,IAAID,QAAQ,CAACC,MAAM,CAACkB,SAAS,EAAE;YACrD,IAAI,CAAC/F,YAAY,GAAG4E,QAAQ,CAACC,MAAM,CAACkB,SAAS;YAC7C5D,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACpC,YAAY,CAAC;;UAExE;UAAA,KACK,IAAI4E,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,IAAIJ,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC9M,MAAM,GAAG,CAAC,IAAI0M,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC1C,EAAE,EAAE;YAClH,IAAI,CAACtC,YAAY,GAAG4E,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC1C,EAAE;YAC/CH,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACpC,YAAY,CAAC;WAChE,MAAM;YACLmC,OAAO,CAAC6D,KAAK,CAAC,qCAAqC,CAAC;YACpD7D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE6D,MAAM,CAACC,IAAI,CAACtB,QAAQ,CAAC,CAAC;YACzD,IAAIA,QAAQ,CAACG,IAAI,EAAE5C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE6D,MAAM,CAACC,IAAI,CAACtB,QAAQ,CAACG,IAAI,CAAC,CAAC;YAC7E,IAAIH,QAAQ,CAACC,MAAM,EAAE1C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE6D,MAAM,CAACC,IAAI,CAACtB,QAAQ,CAACC,MAAM,CAAC,CAAC;;SAEtF,MAAM;UACL,IAAI,CAAC9M,YAAY,GAAG,sDAAsD;UAC1E,IAAI6M,QAAQ,CAACC,MAAM,CAACsB,QAAQ,IAAIvB,QAAQ,CAACC,MAAM,CAACsB,QAAQ,CAACjO,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAACH,YAAY,GAAG6M,QAAQ,CAACC,MAAM,CAACsB,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACDJ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC7G,SAAS,GAAG,KAAK;QACtB,IAAI,CAACpH,YAAY,GAAG,wDAAwD;QAC5EoK,OAAO,CAAC6D,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEA;EACArC,oBAAoBA,CAAC0C,SAAoB;IACvCJ,MAAM,CAACK,MAAM,CAACD,SAAS,CAACE,QAAQ,CAAC,CAAChE,OAAO,CAACiE,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAYrR,SAAS,EAAE;QAChC,IAAI,CAACwO,oBAAoB,CAAC6C,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA;EACAvK,cAAcA,CAACyK,OAAe;IAC5B,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,KAAK,KAAKG,IAAI,KAAK;EAC/B;EAEA;EACAhL,UAAUA,CAACiL,UAAkB;IAC3B,MAAMhL,IAAI,GAAG,IAAI2E,IAAI,CAACqG,UAAU,CAAC;IACjC,OAAOhL,IAAI,CAACiL,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,OAAO;MAChBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACAzI,WAAWA,CAACyD,MAAc;IACxB,IAAI,CAACA,MAAM,CAACvJ,MAAM,IAAIuJ,MAAM,CAACvJ,MAAM,CAACZ,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd,MAAMoP,QAAQ,GAAGjF,MAAM,CAACvJ,MAAM,CAAC2M,MAAM,CAAC,CAAClE,GAAG,EAAEiB,KAAK,KAC/CA,KAAK,CAAC5H,KAAK,CAACC,MAAM,GAAG0G,GAAG,CAAC3G,KAAK,CAACC,MAAM,GAAG2H,KAAK,GAAGjB,GAAG,EAAEc,MAAM,CAACvJ,MAAM,CAAC,CAAC,CAAC,CAAC;IAExE,OAAOwO,QAAQ,CAAC1M,KAAK,CAAC2M,eAAe,IAAI,GAAGD,QAAQ,CAAC1M,KAAK,CAACC,MAAM,IAAIyM,QAAQ,CAAC1M,KAAK,CAACE,QAAQ,EAAE;EAChG;EAEA;EACA4D,iBAAiBA,CAAC2D,MAAc;IAC9B;IACA,IAAI,CAACA,MAAM,CAACvJ,MAAM,IAAIuJ,MAAM,CAACvJ,MAAM,CAACZ,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd;IACA,OAAOmK,MAAM,CAACvJ,MAAM,CAAC+M,IAAI,CAACrD,KAAK,IAAIA,KAAK,CAACzJ,YAAY,GAAG,CAAC,CAAC;EAC5D;EAEA;EACAqF,gBAAgBA,CAACiE,MAAc;IAC7BF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,MAAM,CAAC;IAErC,IAAIA,MAAM,IAAIA,MAAM,CAACvJ,MAAM,IAAIuJ,MAAM,CAACvJ,MAAM,CAACZ,MAAM,GAAG,CAAC,EAAE;MACvDiK,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,MAAM,CAACvJ,MAAM,CAAC;MAE5C;MACA,MAAM0O,UAAU,GAAGnF,MAAM,CAACvJ,MAAM,CAAC,CAAC,CAAC;MACnCqJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6D,MAAM,CAACC,IAAI,CAACsB,UAAU,CAAC,CAAC;MAC9DrF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEoF,UAAU,CAAC;MAE7C;MACA,IAAIzN,OAAO;MAEX;MACA,IAAIyN,UAAU,CAACzN,OAAO,EAAE;QACtBA,OAAO,GAAGyN,UAAU,CAACzN,OAAO;QAC5BoI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAErI,OAAO,CAAC;;MAE9C;MAAA,KACK,IAAIyN,UAAU,CAACC,QAAQ,IAAID,UAAU,CAACC,QAAQ,CAACvP,MAAM,GAAG,CAAC,IAAIsP,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC1N,OAAO,EAAE;QAChGA,OAAO,GAAGyN,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC1N,OAAO;QACxCoI,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAErI,OAAO,CAAC;;MAE1D;MAAA,KACK,IAAIyN,UAAU,CAAClF,EAAE,EAAE;QACtBvI,OAAO,GAAGyN,UAAU,CAAClF,EAAE;QACvBH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAErI,OAAO,CAAC;OACxC,MAAM,IAAIsI,MAAM,CAACC,EAAE,EAAE;QACpBvI,OAAO,GAAGsI,MAAM,CAACC,EAAE;QACnBH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAErI,OAAO,CAAC;OACrD,MAAM;QACL;QACAA,OAAO,GAAG,QAAQ,GAAG6M,IAAI,CAACc,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;QAChEzF,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAErI,OAAO,CAAC;;MAGlEoI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAErI,OAAO,CAAC;MAEzC;MACA,IAAI,CAAC,IAAI,CAACiG,YAAY,EAAE;QACtBmC,OAAO,CAAC6D,KAAK,CAAC,+BAA+B,CAAC;QAE9C;QACA,IAAI3D,MAAM,CAACC,EAAE,EAAE;UACbH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,MAAM,CAACC,EAAE,CAAC;UACtD,IAAI,CAACtC,YAAY,GAAGqC,MAAM,CAACC,EAAE;SAC9B,MAAM,IAAIvI,OAAO,EAAE;UAClBoI,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAErI,OAAO,CAAC;UAClD,IAAI,CAACiG,YAAY,GAAGjG,OAAO;SAC5B,MAAM;UACL;UACA,IAAI,CAACiG,YAAY,GAAG,SAAS,GAAG4G,IAAI,CAACc,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;UAC3EzF,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACpC,YAAY,CAAC;;;MAI/EmC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAACpC,YAAY,EAAE,cAAc,EAAEjG,OAAO,CAAC;MAEjG;MACA,IAAI,CAACA,OAAO,EAAE;QACZ8N,KAAK,CAAC,2CAA2C,CAAC;QAClD;;MAGF,IAAI,CAACjI,MAAM,CAACkI,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UACXjC,QAAQ,EAAE,IAAI,CAAC9F,YAAY;UAC3BjG,OAAO,EAAEA;;OAEZ,CAAC;KACH,MAAM;MACLoI,OAAO,CAAC6D,KAAK,CAAC,sCAAsC,EAAE3D,MAAM,CAAC;;EAEjE;EAEA;EACA2F,yBAAyBA,CAAA;IACvB,MAAMhF,YAAY,GAAG,IAAI,CAACnC,UAAU,CAAC8B,GAAG,CAAC,uBAAuB,CAAC,EAAEvL,KAAK,IAAI,CAAC;IAC7E,IAAI,CAACuI,cAAc,CAACiD,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACjD,kBAAkB,GAAGiD,SAAS;MACnC;MACA,MAAMmF,KAAK,GAAGC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAqB;MAC9E,IAAIF,KAAK,EAAE;QACTA,KAAK,CAACG,KAAK,EAAE;QACbH,KAAK,CAACI,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAC,uBAAuBA,CAAA;IACrB,MAAMvF,YAAY,GAAG,IAAI,CAACnC,UAAU,CAAC8B,GAAG,CAAC,qBAAqB,CAAC,EAAEvL,KAAK,IAAI,CAAC;IAC3E,IAAI,CAACuI,cAAc,CAACiD,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAAChD,gBAAgB,GAAGgD,SAAS;MACjC;MACA,MAAMmF,KAAK,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAqB;MAC5E,IAAIF,KAAK,EAAE;QACTA,KAAK,CAACG,KAAK,EAAE;QACbH,KAAK,CAACI,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAE,aAAaA,CAAA;IACX,MAAMtH,iBAAiB,GAAG,IAAI,CAACL,UAAU,CAAC8B,GAAG,CAAC,mBAAmB,CAAC,EAAEvL,KAAK;IACzE,MAAM+J,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAAC8B,GAAG,CAAC,uBAAuB,CAAC,EAAEvL,KAAK;IACjF,MAAMgK,eAAe,GAAG,IAAI,CAACP,UAAU,CAAC8B,GAAG,CAAC,iBAAiB,CAAC,EAAEvL,KAAK;IACrE,MAAMiK,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAAC8B,GAAG,CAAC,qBAAqB,CAAC,EAAEvL,KAAK;IAE7E,IAAI,CAACyJ,UAAU,CAAC4H,UAAU,CAAC;MACzBvH,iBAAiB,EAAEE,eAAe;MAClCD,qBAAqB,EAAEE,mBAAmB;MAC1CD,eAAe,EAAEF,iBAAiB;MAClCG,mBAAmB,EAAEF;KACtB,CAAC;EACJ;EAEA;EACAlI,oBAAoBA,CAAC8N,UAAkB;IACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B;IACA,MAAMhL,IAAI,GAAG,IAAI2E,IAAI,CAACqG,UAAU,CAAC;IACjC,OAAOhL,IAAI,CAAC2M,cAAc,EAAE;EAC9B;EAEA;EACArP,kBAAkBA,CAACE,WAAmB;IACpC,QAAQA,WAAW;MACjB,KAAK,CAAC;QAAE,OAAO,eAAe;MAC9B,KAAK,CAAC;QAAE,OAAO,iBAAiB;MAChC,KAAK,CAAC;QAAE,OAAO,cAAc;MAC7B;QAAS,OAAO,SAAS;;EAE7B;EAEA;EACAgB,oBAAoBA,CAACE,aAAqB;IACxC,QAAQA,aAAa;MACnB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,WAAW;;EAE/B;EAEA;EACAY,oBAAoBA,CAACsN,cAAmB,EAAEC,WAAgB;IACxD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAAClN,OAAO,IAAI,CAACkN,cAAc,CAAClN,OAAO,CAACM,IAAI,IAC1E,CAAC6M,WAAW,IAAI,CAACA,WAAW,CAAChN,SAAS,IAAI,CAACgN,WAAW,CAAChN,SAAS,CAACG,IAAI,EAAE;MACzE,OAAO,SAAS;;IAGlB,MAAM8M,WAAW,GAAG,IAAInI,IAAI,CAACiI,cAAc,CAAClN,OAAO,CAACM,IAAI,CAAC,CAAC+M,OAAO,EAAE;IACnE,MAAMC,aAAa,GAAG,IAAIrI,IAAI,CAACkI,WAAW,CAAChN,SAAS,CAACG,IAAI,CAAC,CAAC+M,OAAO,EAAE;IACpE,MAAME,MAAM,GAAGD,aAAa,GAAGF,WAAW;IAC1C,MAAMI,QAAQ,GAAGrC,IAAI,CAACC,KAAK,CAACmC,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIC,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,KAAK;KACxB,MAAM;MACL,MAAMtC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACoC,QAAQ,GAAG,EAAE,CAAC;MACvC,MAAMnC,IAAI,GAAGmC,QAAQ,GAAG,EAAE;MAC1B,OAAO,GAAGtC,KAAK,KAAKG,IAAI,KAAK;;EAEjC;;;uBA9iBWtH,oBAAoB,EAAA3J,EAAA,CAAAqT,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvT,EAAA,CAAAqT,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAzT,EAAA,CAAAqT,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApBhK,oBAAoB;MAAAiK,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjClU,EAAA,CAAAC,cAAA,aAAoC;UAGPD,EAAA,CAAAE,MAAA,+BAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpDH,EAAA,CAAAC,cAAA,WAAyB;UAAAD,EAAA,CAAAE,MAAA,2DAAoD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEnFH,EAAA,CAAAC,cAAA,aAAiC;UAC/BD,EAAA,CAAAY,SAAA,aAAuE;UACzEZ,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,aAA4B;UAKpBD,EAAA,CAAAY,SAAA,aAAgD;UAChDZ,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI7CH,EAAA,CAAAC,cAAA,eAAgC;UAC1BD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,uCAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGxCH,EAAA,CAAAC,cAAA,gBAA2E;UAA5CD,EAAA,CAAAyB,UAAA,sBAAA2S,wDAAA;YAAA,OAAYD,GAAA,CAAAnS,QAAA,EAAU;UAAA,EAAC;UAEpDhC,EAAA,CAAAC,cAAA,eAAyB;UAEvBD,EAAA,CAAAY,SAAA,iBAA6D;UAI7DZ,EAAA,CAAAC,cAAA,eAA8B;UAGKD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,aAAsC;UACtCZ,EAAA,CAAAC,cAAA,iBAQC;UADCD,EAAA,CAAAyB,UAAA,mBAAA4S,sDAAA;YAAA,OAASF,GAAA,CAAAhC,yBAAA,EAA2B;UAAA,EAAC;UAPvCnS,EAAA,CAAAG,YAAA,EAQC;UAEHH,EAAA,CAAAC,cAAA,gCAA8F;UAC5FD,EAAA,CAAAS,UAAA,KAAA6T,2CAAA,2BAuBa;UACftU,EAAA,CAAAG,YAAA,EAAmB;UACnBH,EAAA,CAAAS,UAAA,KAAA8T,oCAAA,kBAEM;UACRvU,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA+C;UAOvBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAK5CH,EAAA,CAAAC,cAAA,eAAmC;UACgBD,EAAA,CAAAyB,UAAA,mBAAA+S,uDAAA;YAAA,OAASL,GAAA,CAAAxB,aAAA,EAAe;UAAA,EAAC;UACxE3S,EAAA,CAAAY,SAAA,aAAmC;UACrCZ,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAAwB;UACOD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvCH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,aAAoC;UACpCZ,EAAA,CAAAC,cAAA,iBAQC;UADCD,EAAA,CAAAyB,UAAA,mBAAAgT,sDAAA;YAAA,OAASN,GAAA,CAAAzB,uBAAA,EAAyB;UAAA,EAAC;UAPrC1S,EAAA,CAAAG,YAAA,EAQC;UAEHH,EAAA,CAAAC,cAAA,gCAA4F;UAC1FD,EAAA,CAAAS,UAAA,KAAAiU,2CAAA,2BAuBa;UACf1U,EAAA,CAAAG,YAAA,EAAmB;UACnBH,EAAA,CAAAS,UAAA,KAAAkU,oCAAA,kBAEM;UACR3U,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA+C;UAOvBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAK5CH,EAAA,CAAAC,cAAA,eAAwB;UACKD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvCH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,aAAmC;UAQrCZ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAS,UAAA,KAAAmU,oCAAA,kBAEM;UACR5U,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAwB;UACMD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9CH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,aAAmC;UASrCZ,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAwB;UACGD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAY,SAAA,aAA4B;UAC5BZ,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAS,UAAA,KAAAoU,uCAAA,qBAA8G;UAChH7U,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,eAAqC;UAMjCD,EAAA,CAAAS,UAAA,KAAAqU,kCAAA,gBAAgD;UAChD9U,EAAA,CAAAS,UAAA,KAAAsU,qCAAA,mBAAsC;UACtC/U,EAAA,CAAAS,UAAA,KAAAuU,oCAAA,kBAEM;UACRhV,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAAwC;UAGlCD,EAAA,CAAAY,SAAA,cAA0B;UAACZ,EAAA,CAAAE,MAAA,2BAC7B;UAAAF,EAAA,CAAAG,YAAA,EAAU;UACVH,EAAA,CAAAC,cAAA,gBAA8B;UAIHD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrCH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAY,SAAA,cAA+B;UAC/BZ,EAAA,CAAAC,cAAA,mBAIC;UACuBD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,mBAAsB;UAAAD,EAAA,CAAAE,MAAA,sBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM7CH,EAAA,CAAAC,cAAA,gBAAwB;UACAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAY,SAAA,cAAsC;UACtCZ,EAAA,CAAAC,cAAA,mBAIC;UACqBD,EAAA,CAAAE,MAAA,sBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM7CH,EAAA,CAAAC,cAAA,gBAAwB;UACcD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3DH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAY,SAAA,cAA+B;UAC/BZ,EAAA,CAAAC,cAAA,mBAIC;UACqBD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClDH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOpDH,EAAA,CAAAC,cAAA,gBAAuC;UAGjCD,EAAA,CAAAY,SAAA,kBAKC;UACDZ,EAAA,CAAAC,cAAA,kBAAyD;UACvDD,EAAA,CAAAY,SAAA,iBAAkC;UAClCZ,EAAA,CAAAC,cAAA,iBAAkC;UAAAD,EAAA,CAAAE,MAAA,iCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKvEH,EAAA,CAAAC,cAAA,gBAAuC;UAEnCD,EAAA,CAAAY,SAAA,kBAKC;UACDZ,EAAA,CAAAC,cAAA,kBAA2D;UACzDD,EAAA,CAAAY,SAAA,iBAAkC;UAClCZ,EAAA,CAAAC,cAAA,iBAAkC;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK1EH,EAAA,CAAAC,cAAA,gBAAuC;UAEnCD,EAAA,CAAAY,SAAA,kBAKC;UACDZ,EAAA,CAAAC,cAAA,kBAAgE;UAC9DD,EAAA,CAAAY,SAAA,iBAAkC;UAClCZ,EAAA,CAAAC,cAAA,iBAAkC;UAAAD,EAAA,CAAAE,MAAA,0CAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKhFH,EAAA,CAAAC,cAAA,gBAAuC;UAEnCD,EAAA,CAAAY,SAAA,kBAKC;UACDZ,EAAA,CAAAC,cAAA,kBAAsD;UACpDD,EAAA,CAAAY,SAAA,iBAAkC;UAClCZ,EAAA,CAAAC,cAAA,iBAAkC;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAYhFH,EAAA,CAAAS,UAAA,MAAAwU,qCAAA,kBAoUM;UACRjV,EAAA,CAAAG,YAAA,EAAM;;;;;;;;UAlpBIH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAa,UAAA,cAAAsT,GAAA,CAAAnJ,UAAA,CAAwB;UAmBlBhL,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAAa,UAAA,oBAAAqU,GAAA,CAAiC;UAKclV,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAa,UAAA,gBAAAsT,GAAA,CAAAzG,eAAA,CAAAyH,IAAA,CAAAhB,GAAA,EAA0C;UAC1DnU,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAa,UAAA,YAAAsT,GAAA,CAAAnK,kBAAA,CAAqB;UAyBlDhK,EAAA,CAAAI,SAAA,GAAkG;UAAlGJ,EAAA,CAAAa,UAAA,WAAAuU,OAAA,GAAAjB,GAAA,CAAAnJ,UAAA,CAAA8B,GAAA,wCAAAsI,OAAA,CAAAvH,OAAA,OAAAuH,OAAA,GAAAjB,GAAA,CAAAnJ,UAAA,CAAA8B,GAAA,wCAAAsI,OAAA,CAAAC,OAAA,EAAkG;UAa9FrV,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UAqBjBb,EAAA,CAAAI,SAAA,IAA+B;UAA/BJ,EAAA,CAAAa,UAAA,oBAAAyU,GAAA,CAA+B;UAKctV,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAa,UAAA,gBAAAsT,GAAA,CAAAzG,eAAA,CAAAyH,IAAA,CAAAhB,GAAA,EAA0C;UACxDnU,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAa,UAAA,YAAAsT,GAAA,CAAAlK,gBAAA,CAAmB;UAyBhDjK,EAAA,CAAAI,SAAA,GAA8F;UAA9FJ,EAAA,CAAAa,UAAA,WAAA0U,QAAA,GAAApB,GAAA,CAAAnJ,UAAA,CAAA8B,GAAA,sCAAAyI,QAAA,CAAA1H,OAAA,OAAA0H,QAAA,GAAApB,GAAA,CAAAnJ,UAAA,CAAA8B,GAAA,sCAAAyI,QAAA,CAAAF,OAAA,EAA8F;UAa1FrV,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UAajBb,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAa,UAAA,QAAAsT,GAAA,CAAAvJ,OAAA,CAAe;UAIb5K,EAAA,CAAAI,SAAA,GAA0F;UAA1FJ,EAAA,CAAAa,UAAA,WAAA2U,QAAA,GAAArB,GAAA,CAAAnJ,UAAA,CAAA8B,GAAA,oCAAA0I,QAAA,CAAA3H,OAAA,OAAA2H,QAAA,GAAArB,GAAA,CAAAnJ,UAAA,CAAA8B,GAAA,oCAAA0I,QAAA,CAAAH,OAAA,EAA0F;UA+B5DrV,EAAA,CAAAI,SAAA,IAAgB;UAAhBJ,EAAA,CAAAa,UAAA,YAAAsT,GAAA,CAAA3J,aAAA,CAAgB;UAUlDxK,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAa,UAAA,aAAAsT,GAAA,CAAAnJ,UAAA,CAAA6C,OAAA,IAAAsG,GAAA,CAAA7K,SAAA,CAA4C;UAElBtJ,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAa,UAAA,UAAAsT,GAAA,CAAA7K,SAAA,CAAgB;UACnCtJ,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAa,UAAA,UAAAsT,GAAA,CAAA7K,SAAA,CAAgB;UACjBtJ,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAa,UAAA,SAAAsT,GAAA,CAAA7K,SAAA,CAAe;UA0DPtJ,EAAA,CAAAI,SAAA,IAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UACXb,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAa,UAAA,YAAW;UA2EEb,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAa,UAAA,SAAAsT,GAAA,CAAAjK,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}