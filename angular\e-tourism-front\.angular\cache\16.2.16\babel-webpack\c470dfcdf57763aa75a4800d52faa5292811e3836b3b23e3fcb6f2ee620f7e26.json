{"ast": null, "code": "/**\n * Énumérations basées sur la documentation de l'API Paximum\n */\n// Types de passagers\nexport var PassengerType = /*#__PURE__*/function (PassengerType) {\n  PassengerType[PassengerType[\"Adult\"] = 1] = \"Adult\";\n  PassengerType[PassengerType[\"Child\"] = 2] = \"Child\";\n  PassengerType[PassengerType[\"Infant\"] = 3] = \"Infant\";\n  PassengerType[PassengerType[\"Senior\"] = 4] = \"Senior\";\n  PassengerType[PassengerType[\"Student\"] = 5] = \"Student\";\n  PassengerType[PassengerType[\"Young\"] = 6] = \"Young\";\n  PassengerType[PassengerType[\"Military\"] = 7] = \"Military\";\n  PassengerType[PassengerType[\"Teacher\"] = 8] = \"Teacher\";\n  PassengerType[PassengerType[\"Medical\"] = 9] = \"Medical\";\n  return PassengerType;\n}(PassengerType || {});\n// Types de voyageurs\nexport var TravellerType = /*#__PURE__*/function (TravellerType) {\n  TravellerType[TravellerType[\"Adult\"] = 1] = \"Adult\";\n  TravellerType[TravellerType[\"Child\"] = 2] = \"Child\";\n  TravellerType[TravellerType[\"Infant\"] = 3] = \"Infant\";\n  return TravellerType;\n}(TravellerType || {});\n// Titres des voyageurs\nexport var TravellerTitle = /*#__PURE__*/function (TravellerTitle) {\n  TravellerTitle[TravellerTitle[\"Mr\"] = 1] = \"Mr\";\n  TravellerTitle[TravellerTitle[\"Ms\"] = 2] = \"Ms\";\n  TravellerTitle[TravellerTitle[\"Mrs\"] = 3] = \"Mrs\";\n  TravellerTitle[TravellerTitle[\"Miss\"] = 4] = \"Miss\";\n  TravellerTitle[TravellerTitle[\"Child\"] = 5] = \"Child\";\n  TravellerTitle[TravellerTitle[\"Infant\"] = 6] = \"Infant\";\n  return TravellerTitle;\n}(TravellerTitle || {});\n// Genre\nexport var Gender = /*#__PURE__*/function (Gender) {\n  Gender[Gender[\"Female\"] = 0] = \"Female\";\n  Gender[Gender[\"Male\"] = 1] = \"Male\";\n  return Gender;\n}(Gender || {});\n// Types de messages\nexport var MessageType = /*#__PURE__*/function (MessageType) {\n  MessageType[MessageType[\"Error\"] = 1] = \"Error\";\n  MessageType[MessageType[\"Success\"] = 2] = \"Success\";\n  MessageType[MessageType[\"Information\"] = 3] = \"Information\";\n  MessageType[MessageType[\"Warning\"] = 4] = \"Warning\";\n  return MessageType;\n}(MessageType || {});\n// Codes de messages\nexport var MessageCode = /*#__PURE__*/function (MessageCode) {\n  MessageCode[MessageCode[\"OperationSuccessful\"] = 1] = \"OperationSuccessful\";\n  return MessageCode;\n}(MessageCode || {});\n// Types de produits\nexport var ProductType = /*#__PURE__*/function (ProductType) {\n  ProductType[ProductType[\"HolidayPackage\"] = 1] = \"HolidayPackage\";\n  ProductType[ProductType[\"Hotel\"] = 2] = \"Hotel\";\n  ProductType[ProductType[\"Flight\"] = 3] = \"Flight\";\n  ProductType[ProductType[\"Excursion\"] = 4] = \"Excursion\";\n  ProductType[ProductType[\"Transfer\"] = 5] = \"Transfer\";\n  ProductType[ProductType[\"Tour\"] = 6] = \"Tour\";\n  ProductType[ProductType[\"Cruise\"] = 7] = \"Cruise\";\n  ProductType[ProductType[\"Transport\"] = 8] = \"Transport\";\n  ProductType[ProductType[\"Ferry\"] = 9] = \"Ferry\";\n  ProductType[ProductType[\"Visa\"] = 10] = \"Visa\";\n  ProductType[ProductType[\"AdditionalService\"] = 11] = \"AdditionalService\";\n  ProductType[ProductType[\"Insurance\"] = 12] = \"Insurance\";\n  ProductType[ProductType[\"Dynamic\"] = 13] = \"Dynamic\";\n  ProductType[ProductType[\"Renting\"] = 14] = \"Renting\";\n  return ProductType;\n}(ProductType || {});\n// Types de réponses d'autocomplétion\nexport var AutocompleteResponseType = /*#__PURE__*/function (AutocompleteResponseType) {\n  AutocompleteResponseType[AutocompleteResponseType[\"City\"] = 1] = \"City\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Hotel\"] = 2] = \"Hotel\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Airport\"] = 3] = \"Airport\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Town\"] = 4] = \"Town\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Village\"] = 5] = \"Village\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Excursion\"] = 6] = \"Excursion\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Category\"] = 7] = \"Category\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Country\"] = 8] = \"Country\";\n  AutocompleteResponseType[AutocompleteResponseType[\"Transfer\"] = 9] = \"Transfer\";\n  AutocompleteResponseType[AutocompleteResponseType[\"ExcursionPackage\"] = 10] = \"ExcursionPackage\";\n  return AutocompleteResponseType;\n}(AutocompleteResponseType || {});\n// Types de localisation\nexport var LocationType = /*#__PURE__*/function (LocationType) {\n  LocationType[LocationType[\"Country\"] = 1] = \"Country\";\n  LocationType[LocationType[\"City\"] = 2] = \"City\";\n  LocationType[LocationType[\"Town\"] = 3] = \"Town\";\n  LocationType[LocationType[\"Village\"] = 4] = \"Village\";\n  LocationType[LocationType[\"Airport\"] = 5] = \"Airport\";\n  return LocationType;\n}(LocationType || {});\n// Types de fichiers\nexport var FileType = /*#__PURE__*/function (FileType) {\n  FileType[FileType[\"Image\"] = 1] = \"Image\";\n  FileType[FileType[\"PDF\"] = 2] = \"PDF\";\n  return FileType;\n}(FileType || {});\n// Types de documents\nexport var DocumentType = /*#__PURE__*/function (DocumentType) {\n  DocumentType[DocumentType[\"Invoice\"] = 1] = \"Invoice\";\n  DocumentType[DocumentType[\"Voucher\"] = 2] = \"Voucher\";\n  DocumentType[DocumentType[\"Insurance\"] = 3] = \"Insurance\";\n  DocumentType[DocumentType[\"FlightTicket\"] = 4] = \"FlightTicket\";\n  DocumentType[DocumentType[\"Contract\"] = 5] = \"Contract\";\n  DocumentType[DocumentType[\"AgencyConfirmation\"] = 6] = \"AgencyConfirmation\";\n  DocumentType[DocumentType[\"ExcursionVoucher\"] = 7] = \"ExcursionVoucher\";\n  DocumentType[DocumentType[\"TransferVoucher\"] = 8] = \"TransferVoucher\";\n  DocumentType[DocumentType[\"Receipt\"] = 9] = \"Receipt\";\n  return DocumentType;\n}(DocumentType || {});\n// Options de paiement\nexport var PaymentOption = /*#__PURE__*/function (PaymentOption) {\n  PaymentOption[PaymentOption[\"Undefined\"] = -1] = \"Undefined\";\n  PaymentOption[PaymentOption[\"Cash\"] = 0] = \"Cash\";\n  PaymentOption[PaymentOption[\"OpenAccount\"] = 1] = \"OpenAccount\";\n  PaymentOption[PaymentOption[\"AgencyCredit\"] = 2] = \"AgencyCredit\";\n  PaymentOption[PaymentOption[\"CreditCard\"] = 3] = \"CreditCard\";\n  PaymentOption[PaymentOption[\"BankTransfer\"] = 4] = \"BankTransfer\";\n  PaymentOption[PaymentOption[\"ExternalCreditCard\"] = 5] = \"ExternalCreditCard\";\n  PaymentOption[PaymentOption[\"Optional\"] = 6] = \"Optional\";\n  PaymentOption[PaymentOption[\"Manuel\"] = 7] = \"Manuel\";\n  PaymentOption[PaymentOption[\"MolliePaymentGateway\"] = 8] = \"MolliePaymentGateway\";\n  PaymentOption[PaymentOption[\"AntePAYPaymentGateway\"] = 9] = \"AntePAYPaymentGateway\";\n  PaymentOption[PaymentOption[\"AllSecurePaymentGateway\"] = 10] = \"AllSecurePaymentGateway\";\n  PaymentOption[PaymentOption[\"SaferpayPaymentGateway\"] = 11] = \"SaferpayPaymentGateway\";\n  PaymentOption[PaymentOption[\"MultiCreditCard\"] = 12] = \"MultiCreditCard\";\n  PaymentOption[PaymentOption[\"SberBankPaymentIntegration\"] = 13] = \"SberBankPaymentIntegration\";\n  PaymentOption[PaymentOption[\"AlfaBankPaymentIntegration\"] = 14] = \"AlfaBankPaymentIntegration\";\n  PaymentOption[PaymentOption[\"KlarnaPaymentIntegration\"] = 15] = \"KlarnaPaymentIntegration\";\n  PaymentOption[PaymentOption[\"NovaPaymentIntegration\"] = 16] = \"NovaPaymentIntegration\";\n  PaymentOption[PaymentOption[\"RevoPaymentIntegration\"] = 17] = \"RevoPaymentIntegration\";\n  PaymentOption[PaymentOption[\"BankartPaymentIntegration\"] = 18] = \"BankartPaymentIntegration\";\n  PaymentOption[PaymentOption[\"CurrentAgencyHasLimitParentNot\"] = 19] = \"CurrentAgencyHasLimitParentNot\";\n  PaymentOption[PaymentOption[\"SafeChargePaymentGateway\"] = 20] = \"SafeChargePaymentGateway\";\n  PaymentOption[PaymentOption[\"BoricaPaymentIntegration\"] = 21] = \"BoricaPaymentIntegration\";\n  PaymentOption[PaymentOption[\"PayTabsPaymentGateway\"] = 22] = \"PayTabsPaymentGateway\";\n  PaymentOption[PaymentOption[\"Aaib\"] = 23] = \"Aaib\";\n  PaymentOption[PaymentOption[\"Stripe\"] = 24] = \"Stripe\";\n  PaymentOption[PaymentOption[\"PayTabs2PaymentGateway\"] = 25] = \"PayTabs2PaymentGateway\";\n  PaymentOption[PaymentOption[\"CibBank\"] = 26] = \"CibBank\";\n  PaymentOption[PaymentOption[\"VubBank\"] = 27] = \"VubBank\";\n  PaymentOption[PaymentOption[\"Moneta\"] = 28] = \"Moneta\";\n  PaymentOption[PaymentOption[\"GiroGate\"] = 29] = \"GiroGate\";\n  PaymentOption[PaymentOption[\"Monri\"] = 30] = \"Monri\";\n  PaymentOption[PaymentOption[\"Maib\"] = 31] = \"Maib\";\n  PaymentOption[PaymentOption[\"NGenius\"] = 32] = \"NGenius\";\n  PaymentOption[PaymentOption[\"EcommPay\"] = 33] = \"EcommPay\";\n  PaymentOption[PaymentOption[\"EcomCMIBankIntesa\"] = 34] = \"EcomCMIBankIntesa\";\n  PaymentOption[PaymentOption[\"Moka\"] = 35] = \"Moka\";\n  PaymentOption[PaymentOption[\"Adyen\"] = 36] = \"Adyen\";\n  PaymentOption[PaymentOption[\"Mellat\"] = 37] = \"Mellat\";\n  PaymentOption[PaymentOption[\"SigmaPay\"] = 39] = \"SigmaPay\";\n  PaymentOption[PaymentOption[\"FibBank\"] = 40] = \"FibBank\";\n  PaymentOption[PaymentOption[\"EMoney\"] = 42] = \"EMoney\";\n  PaymentOption[PaymentOption[\"FastPay\"] = 43] = \"FastPay\";\n  return PaymentOption;\n}(PaymentOption || {});\n// Fournisseurs de paiement\nexport var PaymentProvider = /*#__PURE__*/function (PaymentProvider) {\n  PaymentProvider[PaymentProvider[\"Iyzico\"] = 1000] = \"Iyzico\";\n  PaymentProvider[PaymentProvider[\"NestPay\"] = 1001] = \"NestPay\";\n  PaymentProvider[PaymentProvider[\"DPay\"] = 1002] = \"DPay\";\n  PaymentProvider[PaymentProvider[\"Mellay\"] = 1004] = \"Mellay\";\n  PaymentProvider[PaymentProvider[\"MIGS\"] = 1005] = \"MIGS\";\n  PaymentProvider[PaymentProvider[\"EST\"] = 1006] = \"EST\";\n  PaymentProvider[PaymentProvider[\"Garanti\"] = 1008] = \"Garanti\";\n  PaymentProvider[PaymentProvider[\"Denizbank\"] = 1009] = \"Denizbank\";\n  PaymentProvider[PaymentProvider[\"YapiKredi\"] = 1010] = \"YapiKredi\";\n  PaymentProvider[PaymentProvider[\"AlfaBank\"] = 1011] = \"AlfaBank\";\n  PaymentProvider[PaymentProvider[\"Mollie\"] = 1012] = \"Mollie\";\n  PaymentProvider[PaymentProvider[\"AllSecure\"] = 1014] = \"AllSecure\";\n  PaymentProvider[PaymentProvider[\"Saferpay\"] = 1016] = \"Saferpay\";\n  PaymentProvider[PaymentProvider[\"Sberbank\"] = 1017] = \"Sberbank\";\n  PaymentProvider[PaymentProvider[\"Klarna\"] = 1018] = \"Klarna\";\n  PaymentProvider[PaymentProvider[\"Finansbank\"] = 1019] = \"Finansbank\";\n  PaymentProvider[PaymentProvider[\"Revo\"] = 1021] = \"Revo\";\n  PaymentProvider[PaymentProvider[\"Bankart\"] = 1022] = \"Bankart\";\n  PaymentProvider[PaymentProvider[\"SafeCharge\"] = 1023] = \"SafeCharge\";\n  PaymentProvider[PaymentProvider[\"Borica\"] = 1024] = \"Borica\";\n  PaymentProvider[PaymentProvider[\"PayTabs\"] = 1025] = \"PayTabs\";\n  PaymentProvider[PaymentProvider[\"Aaib\"] = 1026] = \"Aaib\";\n  PaymentProvider[PaymentProvider[\"Stripe\"] = 1027] = \"Stripe\";\n  PaymentProvider[PaymentProvider[\"PayTabsV2\"] = 1028] = \"PayTabsV2\";\n  PaymentProvider[PaymentProvider[\"CIBBank\"] = 1029] = \"CIBBank\";\n  PaymentProvider[PaymentProvider[\"VUBBank\"] = 1030] = \"VUBBank\";\n  PaymentProvider[PaymentProvider[\"Vakifbank\"] = 1031] = \"Vakifbank\";\n  PaymentProvider[PaymentProvider[\"Moneta\"] = 1032] = \"Moneta\";\n  PaymentProvider[PaymentProvider[\"GiroGate\"] = 1033] = \"GiroGate\";\n  PaymentProvider[PaymentProvider[\"Param\"] = 1034] = \"Param\";\n  PaymentProvider[PaymentProvider[\"Monri\"] = 1035] = \"Monri\";\n  PaymentProvider[PaymentProvider[\"Maib\"] = 1036] = \"Maib\";\n  PaymentProvider[PaymentProvider[\"NGenius\"] = 1037] = \"NGenius\";\n  PaymentProvider[PaymentProvider[\"EcommPay\"] = 1038] = \"EcommPay\";\n  PaymentProvider[PaymentProvider[\"EcomCMIBankIntesa\"] = 1039] = \"EcomCMIBankIntesa\";\n  PaymentProvider[PaymentProvider[\"Moka\"] = 1040] = \"Moka\";\n  PaymentProvider[PaymentProvider[\"AdyenBank\"] = 1041] = \"AdyenBank\";\n  PaymentProvider[PaymentProvider[\"SigmaPay\"] = 1043] = \"SigmaPay\";\n  PaymentProvider[PaymentProvider[\"FibBank\"] = 1044] = \"FibBank\";\n  PaymentProvider[PaymentProvider[\"EMoney\"] = 1046] = \"EMoney\";\n  PaymentProvider[PaymentProvider[\"FastPay\"] = 1047] = \"FastPay\";\n  return PaymentProvider;\n}(PaymentProvider || {});\n// Type de transaction de paiement\nexport var PaymentTransactionType = /*#__PURE__*/function (PaymentTransactionType) {\n  PaymentTransactionType[PaymentTransactionType[\"Undefined\"] = 0] = \"Undefined\";\n  PaymentTransactionType[PaymentTransactionType[\"OpenModal\"] = 1] = \"OpenModal\";\n  PaymentTransactionType[PaymentTransactionType[\"PostForm\"] = 2] = \"PostForm\";\n  PaymentTransactionType[PaymentTransactionType[\"PostUrl\"] = 3] = \"PostUrl\";\n  PaymentTransactionType[PaymentTransactionType[\"Continue\"] = 4] = \"Continue\";\n  PaymentTransactionType[PaymentTransactionType[\"RedirectToPostUrl\"] = 5] = \"RedirectToPostUrl\";\n  PaymentTransactionType[PaymentTransactionType[\"RenderHtml\"] = 6] = \"RenderHtml\";\n  return PaymentTransactionType;\n}(PaymentTransactionType || {});\n// Type de réponse de transaction de paiement\nexport var PaymentTransactionResponseType = /*#__PURE__*/function (PaymentTransactionResponseType) {\n  PaymentTransactionResponseType[PaymentTransactionResponseType[\"WaitPaymentStatus\"] = 0] = \"WaitPaymentStatus\";\n  PaymentTransactionResponseType[PaymentTransactionResponseType[\"CheckPaymentStatus\"] = 1] = \"CheckPaymentStatus\";\n  PaymentTransactionResponseType[PaymentTransactionResponseType[\"CheckCommitTransactionResponse\"] = 2] = \"CheckCommitTransactionResponse\";\n  return PaymentTransactionResponseType;\n}(PaymentTransactionResponseType || {});\n// Statut de paiement\nexport var PaymentStatus = /*#__PURE__*/function (PaymentStatus) {\n  PaymentStatus[PaymentStatus[\"None\"] = 1] = \"None\";\n  PaymentStatus[PaymentStatus[\"UnPaid\"] = 2] = \"UnPaid\";\n  PaymentStatus[PaymentStatus[\"PartlyPaid\"] = 3] = \"PartlyPaid\";\n  PaymentStatus[PaymentStatus[\"Paid\"] = 4] = \"Paid\";\n  PaymentStatus[PaymentStatus[\"Over\"] = 5] = \"Over\";\n  return PaymentStatus;\n}(PaymentStatus || {});\n// Statut de transaction de paiement\nexport var PaymentTransactionStatus = /*#__PURE__*/function (PaymentTransactionStatus) {\n  PaymentTransactionStatus[PaymentTransactionStatus[\"InComplete\"] = 0] = \"InComplete\";\n  PaymentTransactionStatus[PaymentTransactionStatus[\"Complete\"] = 1] = \"Complete\";\n  PaymentTransactionStatus[PaymentTransactionStatus[\"Errored\"] = 2] = \"Errored\";\n  PaymentTransactionStatus[PaymentTransactionStatus[\"Voided\"] = 3] = \"Voided\";\n  PaymentTransactionStatus[PaymentTransactionStatus[\"Refunded\"] = 4] = \"Refunded\";\n  return PaymentTransactionStatus;\n}(PaymentTransactionStatus || {});\n// Statut de réservation\nexport var ReservationStatus = /*#__PURE__*/function (ReservationStatus) {\n  ReservationStatus[ReservationStatus[\"New\"] = 0] = \"New\";\n  ReservationStatus[ReservationStatus[\"Modified\"] = 1] = \"Modified\";\n  ReservationStatus[ReservationStatus[\"Cancel\"] = 2] = \"Cancel\";\n  ReservationStatus[ReservationStatus[\"CancelX\"] = 3] = \"CancelX\";\n  ReservationStatus[ReservationStatus[\"Draft\"] = 9] = \"Draft\";\n  return ReservationStatus;\n}(ReservationStatus || {});\n// Statut de confirmation\nexport var ConfirmationStatus = /*#__PURE__*/function (ConfirmationStatus) {\n  ConfirmationStatus[ConfirmationStatus[\"Request\"] = 0] = \"Request\";\n  ConfirmationStatus[ConfirmationStatus[\"Confirm\"] = 1] = \"Confirm\";\n  ConfirmationStatus[ConfirmationStatus[\"NoConfirm\"] = 2] = \"NoConfirm\";\n  ConfirmationStatus[ConfirmationStatus[\"NoShow\"] = 3] = \"NoShow\";\n  return ConfirmationStatus;\n}(ConfirmationStatus || {});\n// Statut du processus de transaction de réservation\nexport var BookingTransactionProcessStatus = /*#__PURE__*/function (BookingTransactionProcessStatus) {\n  BookingTransactionProcessStatus[BookingTransactionProcessStatus[\"Waiting\"] = 0] = \"Waiting\";\n  BookingTransactionProcessStatus[BookingTransactionProcessStatus[\"Completed\"] = 1] = \"Completed\";\n  BookingTransactionProcessStatus[BookingTransactionProcessStatus[\"Errored\"] = 2] = \"Errored\";\n  return BookingTransactionProcessStatus;\n}(BookingTransactionProcessStatus || {});\n// Statut de transaction de réservation\nexport var BookingTransactionStatus = /*#__PURE__*/function (BookingTransactionStatus) {\n  BookingTransactionStatus[BookingTransactionStatus[\"Errored\"] = 0] = \"Errored\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Completed\"] = 1] = \"Completed\";\n  BookingTransactionStatus[BookingTransactionStatus[\"ThirdPartyCompleted\"] = 2] = \"ThirdPartyCompleted\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Committing\"] = 3] = \"Committing\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Open\"] = 4] = \"Open\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Expired\"] = 5] = \"Expired\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Cancelled\"] = 6] = \"Cancelled\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Reserved\"] = 7] = \"Reserved\";\n  BookingTransactionStatus[BookingTransactionStatus[\"RollbackFailed\"] = 8] = \"RollbackFailed\";\n  BookingTransactionStatus[BookingTransactionStatus[\"PartialCancel\"] = 9] = \"PartialCancel\";\n  BookingTransactionStatus[BookingTransactionStatus[\"CancelX\"] = 10] = \"CancelX\";\n  BookingTransactionStatus[BookingTransactionStatus[\"Modified\"] = 11] = \"Modified\";\n  return BookingTransactionStatus;\n}(BookingTransactionStatus || {});\n// Disponibilité de l'offre\nexport var OfferAvailability = /*#__PURE__*/function (OfferAvailability) {\n  OfferAvailability[OfferAvailability[\"Available\"] = 1] = \"Available\";\n  OfferAvailability[OfferAvailability[\"Request\"] = 2] = \"Request\";\n  OfferAvailability[OfferAvailability[\"NotAvailable\"] = 3] = \"NotAvailable\";\n  return OfferAvailability;\n}(OfferAvailability || {});\n// Types de bagages\nexport var BaggageType = /*#__PURE__*/function (BaggageType) {\n  BaggageType[BaggageType[\"Checkin\"] = 1] = \"Checkin\";\n  BaggageType[BaggageType[\"Hand\"] = 2] = \"Hand\";\n  return BaggageType;\n}(BaggageType || {});\n// Direction de transfert\nexport var TransferDirection = /*#__PURE__*/function (TransferDirection) {\n  TransferDirection[TransferDirection[\"Forward\"] = 1] = \"Forward\";\n  TransferDirection[TransferDirection[\"Backward\"] = 2] = \"Backward\";\n  TransferDirection[TransferDirection[\"RoundTrip\"] = 3] = \"RoundTrip\";\n  return TransferDirection;\n}(TransferDirection || {});\n// Types de vols\nexport var FlightType = /*#__PURE__*/function (FlightType) {\n  FlightType[FlightType[\"Regular\"] = 0] = \"Regular\";\n  FlightType[FlightType[\"Charter\"] = 1] = \"Charter\";\n  return FlightType;\n}(FlightType || {});\n// Types d'itinéraires de vol\nexport var FlightRouteType = /*#__PURE__*/function (FlightRouteType) {\n  FlightRouteType[FlightRouteType[\"Outbound\"] = 1] = \"Outbound\";\n  FlightRouteType[FlightRouteType[\"Return\"] = 2] = \"Return\";\n  return FlightRouteType;\n}(FlightRouteType || {});\n// Types de classes de vol\nexport var FlightClassType = /*#__PURE__*/function (FlightClassType) {\n  FlightClassType[FlightClassType[\"PROMO\"] = 0] = \"PROMO\";\n  FlightClassType[FlightClassType[\"ECONOMY\"] = 1] = \"ECONOMY\";\n  FlightClassType[FlightClassType[\"BUSINESS\"] = 2] = \"BUSINESS\";\n  return FlightClassType;\n}(FlightClassType || {});\n// Groupe de service de fonctionnalité de marque de vol\nexport var FlightBrandFeatureServiceGroup = /*#__PURE__*/function (FlightBrandFeatureServiceGroup) {\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"BrandedFare\"] = 0] = \"BrandedFare\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"Baggage\"] = 1] = \"Baggage\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"FrequentFlyer\"] = 2] = \"FrequentFlyer\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"InFlightEntertainment\"] = 3] = \"InFlightEntertainment\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"LoungeAccess\"] = 4] = \"LoungeAccess\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"MealBeverage\"] = 5] = \"MealBeverage\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"Pets\"] = 6] = \"Pets\";\n  FlightBrandFeatureServiceGroup[FlightBrandFeatureServiceGroup[\"Upgrades\"] = 7] = \"Upgrades\";\n  return FlightBrandFeatureServiceGroup;\n}(FlightBrandFeatureServiceGroup || {});\n// Type de tarification de marque de vol\nexport var FlightBrandPricingType = /*#__PURE__*/function (FlightBrandPricingType) {\n  FlightBrandPricingType[FlightBrandPricingType[\"Free\"] = 0] = \"Free\";\n  FlightBrandPricingType[FlightBrandPricingType[\"Chargable\"] = 1] = \"Chargable\";\n  FlightBrandPricingType[FlightBrandPricingType[\"NotOffered\"] = 2] = \"NotOffered\";\n  return FlightBrandPricingType;\n}(FlightBrandPricingType || {});\n// Option de récupération de bagages de vol\nexport var FlightBaggageGetOption = /*#__PURE__*/function (FlightBaggageGetOption) {\n  FlightBaggageGetOption[FlightBaggageGetOption[\"All\"] = 0] = \"All\";\n  FlightBaggageGetOption[FlightBaggageGetOption[\"OnlyBaggage\"] = 1] = \"OnlyBaggage\";\n  FlightBaggageGetOption[FlightBaggageGetOption[\"OnlyNotBaggage\"] = 2] = \"OnlyNotBaggage\";\n  return FlightBaggageGetOption;\n}(FlightBaggageGetOption || {});\n// Type d'unité de bagage\nexport var BaggageUnitType = /*#__PURE__*/function (BaggageUnitType) {\n  BaggageUnitType[BaggageUnitType[\"KG\"] = 0] = \"KG\";\n  BaggageUnitType[BaggageUnitType[\"LB\"] = 1] = \"LB\";\n  BaggageUnitType[BaggageUnitType[\"Piece\"] = 2] = \"Piece\";\n  return BaggageUnitType;\n}(BaggageUnitType || {});\n// Types de critères de date\nexport var DateCriteriaType = /*#__PURE__*/function (DateCriteriaType) {\n  DateCriteriaType[DateCriteriaType[\"SERVICEDATE\"] = 0] = \"SERVICEDATE\";\n  DateCriteriaType[DateCriteriaType[\"REGISTER\"] = 1] = \"REGISTER\";\n  DateCriteriaType[DateCriteriaType[\"CANCELLATIONDEADLINE\"] = 2] = \"CANCELLATIONDEADLINE\";\n  DateCriteriaType[DateCriteriaType[\"OPTIONDATE\"] = 3] = \"OPTIONDATE\";\n  DateCriteriaType[DateCriteriaType[\"CHANGEDATE\"] = 4] = \"CHANGEDATE\";\n  return DateCriteriaType;\n}(DateCriteriaType || {});\n// Types de prix\nexport var PriceType = /*#__PURE__*/function (PriceType) {\n  PriceType[PriceType[\"UNDEFINED\"] = 0] = \"UNDEFINED\";\n  PriceType[PriceType[\"PERPAX\"] = 1] = \"PERPAX\";\n  PriceType[PriceType[\"PERSERVICE\"] = 2] = \"PERSERVICE\";\n  PriceType[PriceType[\"PERROOM\"] = 3] = \"PERROOM\";\n  return PriceType;\n}(PriceType || {});\n// Types de reçus\nexport var ReceiptType = /*#__PURE__*/function (ReceiptType) {\n  ReceiptType[ReceiptType[\"Payment\"] = 0] = \"Payment\";\n  ReceiptType[ReceiptType[\"Refund\"] = 1] = \"Refund\";\n  return ReceiptType;\n}(ReceiptType || {});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}