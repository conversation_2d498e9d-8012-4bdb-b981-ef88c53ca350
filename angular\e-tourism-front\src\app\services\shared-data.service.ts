import { Injectable } from '@angular/core';
import { PassengerType } from '../models/enums.model';

@Injectable({
  providedIn: 'root'
})
export class SharedDataService {
  // Stockage des informations de passagers
  private passengerCounts: { [key: number]: number } = {
    [PassengerType.Adult]: 1,
    [PassengerType.Child]: 0,
    [PassengerType.Infant]: 0
  };

  constructor() { }

  /**
   * Définir le nombre de passagers par type
   * @param passengerCounts Objet contenant le nombre de passagers par type
   */
  setPassengerCounts(passengerCounts: { [key: number]: number }): void {
    this.passengerCounts = { ...passengerCounts };
  }

  /**
   * Obtenir le nombre de passagers par type
   * @returns Objet contenant le nombre de passagers par type
   */
  getPassengerCounts(): { [key: number]: number } {
    return { ...this.passengerCounts };
  }

  /**
   * Obtenir le nombre total de passagers
   * @returns Nombre total de passagers
   */
  getTotalPassengers(): number {
    return Object.values(this.passengerCounts).reduce((sum, count) => sum + count, 0);
  }

  /**
   * Obtenir un tableau de passagers pour l'API
   * @returns Tableau de passagers pour l'API
   */
  getPassengersArray(): any[] {
    const passengers = [];

    // Ajouter chaque type de passager avec un nombre > 0
    Object.entries(this.passengerCounts).forEach(([type, count]) => {
      if (count > 0) {
        passengers.push({
          type: parseInt(type),
          count: count
        });
      }
    });

    // S'assurer qu'au moins un passager est inclus
    if (passengers.length === 0) {
      passengers.push({
        type: PassengerType.Adult,
        count: 1
      });
    }

    return passengers;
  }
}
