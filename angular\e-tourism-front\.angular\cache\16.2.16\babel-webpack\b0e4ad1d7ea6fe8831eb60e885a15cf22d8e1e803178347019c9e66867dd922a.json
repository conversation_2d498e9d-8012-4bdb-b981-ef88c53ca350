{"ast": null, "code": "import { catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class BookingService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8080/booking';\n  }\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request) {\n    // Déterminer l'endpoint en fonction de l'action\n    let endpoint = '';\n    let requestBody = {};\n    switch (request.action) {\n      case 'begin':\n        endpoint = '/begintransaction';\n        requestBody = request.beginRequest;\n        break;\n      case 'info':\n        endpoint = '/setreservationinfo';\n        requestBody = request.infoRequest;\n        break;\n      case 'commit':\n        endpoint = '/committransaction';\n        requestBody = request.commitRequest;\n        break;\n      default:\n        throw new Error(`Action non reconnue: ${request.action}`);\n    }\n    return this.http.post(`${this.apiUrl}${endpoint}`, requestBody).pipe(map(response => {\n      // Construire la réponse au format BookingTransactionResponse\n      const bookingResponse = {\n        action: request.action\n      };\n      switch (request.action) {\n        case 'begin':\n          bookingResponse.beginResponse = response;\n          break;\n        case 'info':\n          bookingResponse.infoResponse = response;\n          break;\n        case 'commit':\n          bookingResponse.commitResponse = response;\n          break;\n      }\n      return bookingResponse;\n    }), catchError(error => {\n      console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);\n      throw error;\n    }));\n  }\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds, currency = 'EUR', culture = 'fr-FR') {\n    const request = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n    return this.bookingTransaction(request);\n  }\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request) {\n    const bookingRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request) {\n    const bookingRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId) {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId) {\n    return {\n      transactionId,\n      paymentOption: 1,\n      paymentInformation: {\n        paymentTypeId: 1,\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n  static {\n    this.ɵfac = function BookingService_Factory(t) {\n      return new (t || BookingService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BookingService,\n      factory: BookingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["catchError", "BookingService", "constructor", "http", "apiUrl", "bookingTransaction", "request", "endpoint", "requestBody", "action", "beginRequest", "infoRequest", "commitRequest", "Error", "post", "pipe", "map", "response", "bookingResponse", "beginResponse", "infoResponse", "commitResponse", "error", "console", "beginTransaction", "offerIds", "currency", "culture", "setReservationInfo", "bookingRequest", "commitTransaction", "createDefaultReservationInfoRequest", "transactionId", "travellers", "reservationNote", "agencyReservationNumber", "createDefaultCommitTransactionRequest", "paymentOption", "paymentInformation", "paymentTypeId", "paymentPrice", "amount", "paymentDate", "Date", "toISOString", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\booking.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport {\n  BookingTransactionRequest,\n  BookingTransactionResponse,\n  BeginTransactionRequest,\n  BeginTransactionResponse,\n  SetReservationInfoRequest,\n  SetReservationInfoResponse,\n  CommitTransactionRequest,\n  CommitTransactionResponse\n} from '../models/booking';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class BookingService {\n  private apiUrl = 'http://localhost:8080/booking';\n\n  constructor(private http: HttpClient) { }\n\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request: BookingTransactionRequest): Observable<BookingTransactionResponse> {\n    // Déterminer l'endpoint en fonction de l'action\n    let endpoint = '';\n    let requestBody = {};\n\n    switch (request.action) {\n      case 'begin':\n        endpoint = '/begintransaction';\n        requestBody = request.beginRequest;\n        break;\n      case 'info':\n        endpoint = '/setreservationinfo';\n        requestBody = request.infoRequest;\n        break;\n      case 'commit':\n        endpoint = '/committransaction';\n        requestBody = request.commitRequest;\n        break;\n      default:\n        throw new Error(`Action non reconnue: ${request.action}`);\n    }\n\n    return this.http.post<any>(\n      `${this.apiUrl}${endpoint}`,\n      requestBody\n    ).pipe(\n      map(response => {\n        // Construire la réponse au format BookingTransactionResponse\n        const bookingResponse: BookingTransactionResponse = {\n          action: request.action\n        };\n\n        switch (request.action) {\n          case 'begin':\n            bookingResponse.beginResponse = response;\n            break;\n          case 'info':\n            bookingResponse.infoResponse = response;\n            break;\n          case 'commit':\n            bookingResponse.commitResponse = response;\n            break;\n        }\n\n        return bookingResponse;\n      }),\n      catchError(error => {\n        console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);\n        throw error;\n      })\n    );\n  }\n\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds: string[], currency: string = 'EUR', culture: string = 'fr-FR'): Observable<BookingTransactionResponse> {\n    const request: BookingTransactionRequest = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n\n    return this.bookingTransaction(request);\n  }\n\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request: SetReservationInfoRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request: CommitTransactionRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId: string): SetReservationInfoRequest {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId: string): CommitTransactionRequest {\n    return {\n      transactionId,\n      paymentOption: 1, // Option de paiement par défaut\n      paymentInformation: {\n        paymentTypeId: 1, // Type de paiement par défaut\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n}\n"], "mappings": "AAGA,SAASA,UAAU,QAAQ,gBAAgB;;;AAe3C,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,+BAA+B;EAER;EAExC;;;;;EAKAC,kBAAkBA,CAACC,OAAkC;IACnD;IACA,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAIC,WAAW,GAAG,EAAE;IAEpB,QAAQF,OAAO,CAACG,MAAM;MACpB,KAAK,OAAO;QACVF,QAAQ,GAAG,mBAAmB;QAC9BC,WAAW,GAAGF,OAAO,CAACI,YAAY;QAClC;MACF,KAAK,MAAM;QACTH,QAAQ,GAAG,qBAAqB;QAChCC,WAAW,GAAGF,OAAO,CAACK,WAAW;QACjC;MACF,KAAK,QAAQ;QACXJ,QAAQ,GAAG,oBAAoB;QAC/BC,WAAW,GAAGF,OAAO,CAACM,aAAa;QACnC;MACF;QACE,MAAM,IAAIC,KAAK,CAAC,wBAAwBP,OAAO,CAACG,MAAM,EAAE,CAAC;;IAG7D,OAAO,IAAI,CAACN,IAAI,CAACW,IAAI,CACnB,GAAG,IAAI,CAACV,MAAM,GAAGG,QAAQ,EAAE,EAC3BC,WAAW,CACZ,CAACO,IAAI,CACJC,GAAG,CAACC,QAAQ,IAAG;MACb;MACA,MAAMC,eAAe,GAA+B;QAClDT,MAAM,EAAEH,OAAO,CAACG;OACjB;MAED,QAAQH,OAAO,CAACG,MAAM;QACpB,KAAK,OAAO;UACVS,eAAe,CAACC,aAAa,GAAGF,QAAQ;UACxC;QACF,KAAK,MAAM;UACTC,eAAe,CAACE,YAAY,GAAGH,QAAQ;UACvC;QACF,KAAK,QAAQ;UACXC,eAAe,CAACG,cAAc,GAAGJ,QAAQ;UACzC;;MAGJ,OAAOC,eAAe;IACxB,CAAC,CAAC,EACFlB,UAAU,CAACsB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,iDAAiDhB,OAAO,CAACG,MAAM,IAAI,EAAEa,KAAK,CAAC;MACzF,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;EAOAE,gBAAgBA,CAACC,QAAkB,EAAEC,QAAA,GAAmB,KAAK,EAAEC,OAAA,GAAkB,OAAO;IACtF,MAAMrB,OAAO,GAA8B;MACzCG,MAAM,EAAE,OAAO;MACfC,YAAY,EAAE;QACZe,QAAQ;QACRC,QAAQ;QACRC;;KAEH;IAED,OAAO,IAAI,CAACtB,kBAAkB,CAACC,OAAO,CAAC;EACzC;EAEA;;;;;EAKAsB,kBAAkBA,CAACtB,OAAkC;IACnD,MAAMuB,cAAc,GAA8B;MAChDpB,MAAM,EAAE,MAAM;MACdE,WAAW,EAAEL;KACd;IAED,OAAO,IAAI,CAACD,kBAAkB,CAACwB,cAAc,CAAC;EAChD;EAEA;;;;;EAKAC,iBAAiBA,CAACxB,OAAiC;IACjD,MAAMuB,cAAc,GAA8B;MAChDpB,MAAM,EAAE,QAAQ;MAChBG,aAAa,EAAEN;KAChB;IAED,OAAO,IAAI,CAACD,kBAAkB,CAACwB,cAAc,CAAC;EAChD;EAEA;;;;;EAKAE,mCAAmCA,CAACC,aAAqB;IACvD,OAAO;MACLA,aAAa;MACbC,UAAU,EAAE,EAAE;MACdC,eAAe,EAAE,EAAE;MACnBC,uBAAuB,EAAE;KAC1B;EACH;EAEA;;;;;EAKAC,qCAAqCA,CAACJ,aAAqB;IACzD,OAAO;MACLA,aAAa;MACbK,aAAa,EAAE,CAAC;MAChBC,kBAAkB,EAAE;QAClBC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;UACZC,MAAM,EAAE,CAAC;UACTf,QAAQ,EAAE;SACX;QACDgB,WAAW,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;;KAEtC;EACH;;;uBA/IW3C,cAAc,EAAA4C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAd/C,cAAc;MAAAgD,OAAA,EAAdhD,cAAc,CAAAiD,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}