{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/progress-bar\";\nimport * as i6 from \"@angular/material/toolbar\";\nimport * as i7 from \"@angular/material/divider\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"@angular/material/badge\";\nfunction NavbarComponent_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleSidebar());\n    });\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavbarComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"a\", 15);\n    i0.ɵɵelement(2, \"i\", 16);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Accueil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"a\", 17);\n    i0.ɵɵelement(6, \"i\", 18);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Rechercher\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction NavbarComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"button\", 20);\n    i0.ɵɵelement(2, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 22)(4, \"button\", 23)(5, \"div\", 24);\n    i0.ɵɵelement(6, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 26);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-menu\", 28, 29)(12, \"div\", 30)(13, \"div\", 31);\n    i0.ɵɵelement(14, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 32)(16, \"h4\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(20, \"mat-divider\");\n    i0.ɵɵelementStart(21, \"button\", 33);\n    i0.ɵɵelement(22, \"i\", 34);\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24, \"Mon profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"button\", 33);\n    i0.ɵɵelement(26, \"i\", 35);\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"button\", 33);\n    i0.ɵɵelement(30, \"i\", 36);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \"Historique\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(33, \"mat-divider\");\n    i0.ɵɵelementStart(34, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_div_11_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.logout());\n    });\n    i0.ɵɵelement(35, \"i\", 38);\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(11);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matBadge\", 0)(\"matBadgeHidden\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r6);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.userName);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.userName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.userAgency);\n  }\n}\nfunction NavbarComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"mat-progress-bar\", 40);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class NavbarComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.toggleSidebarEvent = new EventEmitter();\n    this.isLoggedIn = false;\n    this.userName = '';\n    this.userAgency = '';\n    this.loading = false;\n    this.subscriptions = [];\n    this.scrolled = false;\n  }\n  ngOnInit() {\n    // Surveiller l'état d'authentification\n    const authSub = this.authService.isAuthenticated$.subscribe(isAuthenticated => {\n      this.isLoggedIn = isAuthenticated;\n      if (isAuthenticated) {\n        const userInfo = this.authService.getUserInfo();\n        this.userName = userInfo?.username || 'Utilisateur';\n        this.userAgency = userInfo?.agency || 'Agence';\n      }\n    });\n    this.subscriptions.push(authSub);\n    // Surveiller les événements de navigation pour afficher la barre de progression\n    const routerSub = this.router.events.pipe(filter(event => event instanceof NavigationStart || event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError)).subscribe(event => {\n      if (event instanceof NavigationStart) {\n        this.loading = true;\n      } else {\n        this.loading = false;\n      }\n    });\n    this.subscriptions.push(routerSub);\n    // Vérifier le défilement initial\n    this.checkScroll();\n  }\n  ngOnDestroy() {\n    // Nettoyer les abonnements\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  // Méthode pour basculer la sidebar\n  toggleSidebar() {\n    this.toggleSidebarEvent.emit();\n  }\n  // Méthode de déconnexion\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n  // Écouter l'événement de défilement pour changer l'apparence de la navbar\n  checkScroll() {\n    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n    if (scrollPosition > 20 && !this.scrolled) {\n      document.body.classList.add('scrolled');\n      this.scrolled = true;\n    } else if (scrollPosition <= 20 && this.scrolled) {\n      document.body.classList.remove('scrolled');\n      this.scrolled = false;\n    }\n  }\n  static {\n    this.ɵfac = function NavbarComponent_Factory(t) {\n      return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavbarComponent,\n      selectors: [[\"app-navbar\"]],\n      hostBindings: function NavbarComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"scroll\", function NavbarComponent_scroll_HostBindingHandler() {\n            return ctx.checkScroll();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      outputs: {\n        toggleSidebarEvent: \"toggleSidebarEvent\"\n      },\n      decls: 13,\n      vars: 4,\n      consts: [[1, \"navbar-wrapper\"], [1, \"navbar\"], [1, \"navbar-container\"], [\"mat-icon-button\", \"\", \"class\", \"menu-toggle\", 3, \"click\", 4, \"ngIf\"], [1, \"navbar-brand\"], [\"routerLink\", \"/accueil\", 1, \"brand-link\"], [1, \"brand-icon\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"brand-name\"], [\"class\", \"navbar-links\", 4, \"ngIf\"], [\"class\", \"navbar-actions\", 4, \"ngIf\"], [\"class\", \"progress-bar-container\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"menu-toggle\", 3, \"click\"], [1, \"fas\", \"fa-bars\"], [1, \"navbar-links\"], [\"routerLink\", \"/accueil\", \"routerLinkActive\", \"active-link\", 1, \"nav-link\"], [1, \"fas\", \"fa-home\"], [\"routerLink\", \"/search-price\", \"routerLinkActive\", \"active-link\", 1, \"nav-link\"], [1, \"fas\", \"fa-search\"], [1, \"navbar-actions\"], [\"mat-icon-button\", \"\", \"matBadgeColor\", \"accent\", \"aria-label\", \"Notifications\", 1, \"notification-button\", 3, \"matBadge\", \"matBadgeHidden\"], [1, \"fas\", \"fa-bell\"], [1, \"user-menu-container\"], [\"mat-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"user-avatar\"], [1, \"fas\", \"fa-user\"], [1, \"user-name\"], [1, \"fas\", \"fa-chevron-down\"], [\"xPosition\", \"before\", 1, \"user-menu\"], [\"userMenu\", \"matMenu\"], [1, \"user-menu-header\"], [1, \"user-avatar\", \"large\"], [1, \"user-info\"], [\"mat-menu-item\", \"\", \"disabled\", \"\"], [1, \"fas\", \"fa-user-circle\"], [1, \"fas\", \"fa-cog\"], [1, \"fas\", \"fa-history\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [1, \"progress-bar-container\"], [\"mode\", \"indeterminate\", \"color\", \"accent\"]],\n      template: function NavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-toolbar\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, NavbarComponent_button_3_Template, 2, 0, \"button\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"a\", 5)(6, \"div\", 6);\n          i0.ɵɵelement(7, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"span\", 8);\n          i0.ɵɵtext(9, \"E-Tourism\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(10, NavbarComponent_div_10_Template, 9, 0, \"div\", 9);\n          i0.ɵɵtemplate(11, NavbarComponent_div_11_Template, 38, 6, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, NavbarComponent_div_12_Template, 2, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [i3.NgIf, i2.RouterLink, i2.RouterLinkActive, i4.MatButton, i4.MatIconButton, i5.MatProgressBar, i6.MatToolbar, i7.MatDivider, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, i9.MatBadge],\n      styles: [\".navbar-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: var(--z-index-fixed);\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  height: 64px;\\n  padding: 0;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: var(--text-primary);\\n  box-shadow: var(--elevation-2);\\n  transition: all var(--transition-medium);\\n}\\n\\n.navbar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  height: 100%;\\n  padding: 0 var(--spacing-md);\\n  max-width: var(--container-xl);\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.menu-toggle[_ngcontent-%COMP%] {\\n  margin-right: var(--spacing-sm);\\n  color: var(--primary-color);\\n  display: none;\\n}\\n\\n.menu-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.brand-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  text-decoration: none;\\n  font-weight: 600;\\n  font-size: 1.25rem;\\n  color: var(--primary-dark);\\n  transition: all var(--transition-fast);\\n}\\n\\n.brand-link[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n\\n.brand-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  border-radius: 12px;\\n  color: white;\\n  font-size: 1.2rem;\\n  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.3);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);\\n  animation: shimmer 2s infinite;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.brand-name[_ngcontent-%COMP%] {\\n  letter-spacing: 0.5px;\\n  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  font-weight: 700;\\n}\\n\\n\\n\\n.navbar-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  margin-left: var(--spacing-xl);\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  position: relative;\\n  color: var(--text-secondary);\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n  padding: var(--spacing-sm) var(--spacing-md);\\n  border-radius: var(--border-radius-medium);\\n  transition: all var(--transition-fast);\\n  text-decoration: none;\\n}\\n\\n.nav-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  transition: all var(--transition-fast);\\n}\\n\\n.nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 50%;\\n  width: 0;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--primary-color), var(--accent-color));\\n  border-radius: 3px;\\n  transform: translateX(-50%);\\n  transition: width var(--transition-medium);\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateY(-2px);\\n}\\n\\n.nav-link.active-link[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-weight: 600;\\n}\\n\\n.nav-link.active-link[_ngcontent-%COMP%]::after {\\n  width: 30px;\\n}\\n\\n\\n\\n.navbar-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n}\\n\\n\\n\\n.notification-button[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  transition: all var(--transition-fast);\\n}\\n\\n.notification-button[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n  transform: translateY(-2px);\\n}\\n\\n.notification-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n\\n\\n\\n.user-menu-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  color: var(--text-primary);\\n  font-weight: 500;\\n  padding: var(--spacing-xs) var(--spacing-sm);\\n  border-radius: var(--border-radius-medium);\\n  transition: all var(--transition-fast);\\n  border: 1px solid transparent;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  border-color: rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));\\n  border-radius: 50%;\\n  color: white;\\n  font-size: 0.9rem;\\n  box-shadow: 0 2px 4px rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.user-avatar.large[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  font-size: 1.2rem;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  max-width: 150px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: var(--text-secondary);\\n  transition: transform var(--transition-fast);\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%]:hover   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  transform: translateY(2px);\\n}\\n\\n\\n\\n.user-menu-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  padding: var(--spacing-md) var(--spacing-md) var(--spacing-sm);\\n}\\n\\n.user-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n}\\n\\n.user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.8rem;\\n  color: var(--text-secondary);\\n}\\n\\n\\n\\n  .mat-menu-panel {\\n  border-radius: var(--border-radius-medium) !important;\\n  overflow: hidden;\\n  min-width: 220px !important;\\n  box-shadow: var(--elevation-3) !important;\\n}\\n\\n  .mat-menu-content {\\n  padding: 0 !important;\\n}\\n\\n  .mat-menu-item {\\n  font-family: var(--font-family);\\n  height: 48px;\\n  line-height: 48px;\\n}\\n\\n  .mat-menu-item i {\\n  margin-right: var(--spacing-sm);\\n  font-size: 1.1rem;\\n  width: 24px;\\n  height: 24px;\\n  line-height: 24px;\\n  text-align: center;\\n  color: var(--primary-color);\\n}\\n\\n  .mat-menu-item:hover:not([disabled]) {\\n  background-color: rgba(var(--primary-color-rgb), 0.05) !important;\\n}\\n\\n  .mat-menu-item[disabled] {\\n  color: var(--text-disabled) !important;\\n}\\n\\n  .mat-menu-item[disabled] i {\\n  color: var(--text-disabled) !important;\\n}\\n\\n\\n\\n.progress-bar-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 64px;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.scrolled[_nghost-%COMP%]   .navbar[_ngcontent-%COMP%], .scrolled   [_nghost-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  height: 56px;\\n  box-shadow: var(--elevation-3);\\n  background: rgba(255, 255, 255, 0.98);\\n}\\n\\n.scrolled[_nghost-%COMP%]   .brand-icon[_ngcontent-%COMP%], .scrolled   [_nghost-%COMP%]   .brand-icon[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  font-size: 1.1rem;\\n}\\n\\n\\n\\n@media (max-width: 992px) {\\n  .navbar-links[_ngcontent-%COMP%] {\\n    margin-left: var(--spacing-md);\\n    gap: var(--spacing-sm);\\n  }\\n\\n  .nav-link[_ngcontent-%COMP%] {\\n    padding: var(--spacing-sm) var(--spacing-sm);\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .menu-toggle[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n\\n  .navbar-links[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n\\n  .brand-link[_ngcontent-%COMP%] {\\n    margin-right: auto;\\n  }\\n}\\n\\n@media (max-width: 600px) {\\n  .brand-name[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n\\n  .user-name[_ngcontent-%COMP%] {\\n    max-width: 80px;\\n  }\\n\\n  .navbar-actions[_ngcontent-%COMP%] {\\n    gap: var(--spacing-sm);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "NavigationStart", "NavigationEnd", "NavigationCancel", "NavigationError", "filter", "i0", "ɵɵelementStart", "ɵɵlistener", "NavbarComponent_button_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "toggleSidebar", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "NavbarComponent_div_11_Template_button_click_34_listener", "_r8", "ctx_r7", "logout", "ɵɵadvance", "ɵɵproperty", "_r6", "ɵɵtextInterpolate", "ctx_r2", "userName", "userAgency", "NavbarComponent", "constructor", "authService", "router", "toggleSidebarEvent", "isLoggedIn", "loading", "subscriptions", "scrolled", "ngOnInit", "authSub", "isAuthenticated$", "subscribe", "isAuthenticated", "userInfo", "getUserInfo", "username", "agency", "push", "routerSub", "events", "pipe", "event", "checkScroll", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "emit", "navigate", "scrollPosition", "window", "pageYOffset", "document", "documentElement", "scrollTop", "body", "classList", "add", "remove", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "hostBindings", "NavbarComponent_HostBindings", "rf", "ctx", "ɵɵresolveWindow", "ɵɵtemplate", "NavbarComponent_button_3_Template", "NavbarComponent_div_10_Template", "NavbarComponent_div_11_Template", "NavbarComponent_div_12_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\navbar\\navbar.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\navbar\\navbar.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy, Output, EventEmitter, HostListener } from '@angular/core';\nimport { Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { Subscription } from 'rxjs';\nimport { filter } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-navbar',\n  templateUrl: './navbar.component.html',\n  styleUrls: ['./navbar.component.css']\n})\nexport class NavbarComponent implements OnInit, OnDestroy {\n  @Output() toggleSidebarEvent = new EventEmitter<void>();\n\n  isLoggedIn = false;\n  userName = '';\n  userAgency = '';\n  loading = false;\n\n  private subscriptions: Subscription[] = [];\n  private scrolled = false;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) { }\n\n  ngOnInit(): void {\n    // Surveiller l'état d'authentification\n    const authSub = this.authService.isAuthenticated$.subscribe(isAuthenticated => {\n      this.isLoggedIn = isAuthenticated;\n\n      if (isAuthenticated) {\n        const userInfo = this.authService.getUserInfo();\n        this.userName = userInfo?.username || 'Utilisateur';\n        this.userAgency = userInfo?.agency || 'Agence';\n      }\n    });\n    this.subscriptions.push(authSub);\n\n    // Surveiller les événements de navigation pour afficher la barre de progression\n    const routerSub = this.router.events\n      .pipe(\n        filter(event =>\n          event instanceof NavigationStart ||\n          event instanceof NavigationEnd ||\n          event instanceof NavigationCancel ||\n          event instanceof NavigationError\n        )\n      )\n      .subscribe(event => {\n        if (event instanceof NavigationStart) {\n          this.loading = true;\n        } else {\n          this.loading = false;\n        }\n      });\n    this.subscriptions.push(routerSub);\n\n    // Vérifier le défilement initial\n    this.checkScroll();\n  }\n\n  ngOnDestroy(): void {\n    // Nettoyer les abonnements\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  // Méthode pour basculer la sidebar\n  toggleSidebar(): void {\n    this.toggleSidebarEvent.emit();\n  }\n\n  // Méthode de déconnexion\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n\n  // Écouter l'événement de défilement pour changer l'apparence de la navbar\n  @HostListener('window:scroll')\n  checkScroll(): void {\n    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n\n    if (scrollPosition > 20 && !this.scrolled) {\n      document.body.classList.add('scrolled');\n      this.scrolled = true;\n    } else if (scrollPosition <= 20 && this.scrolled) {\n      document.body.classList.remove('scrolled');\n      this.scrolled = false;\n    }\n  }\n}\n", "<div class=\"navbar-wrapper\">\n  <mat-toolbar class=\"navbar\">\n    <div class=\"navbar-container\">\n      <!-- Mobile Menu Toggle -->\n      <button mat-icon-button class=\"menu-toggle\" *ngIf=\"isLoggedIn\" (click)=\"toggleSidebar()\">\n        <i class=\"fas fa-bars\"></i>\n      </button>\n\n      <!-- Brand Logo -->\n      <div class=\"navbar-brand\">\n        <a routerLink=\"/accueil\" class=\"brand-link\">\n          <div class=\"brand-icon\">\n            <i class=\"fas fa-plane-departure\"></i>\n          </div>\n          <span class=\"brand-name\">E-Tourism</span>\n        </a>\n      </div>\n\n      <!-- Navigation Links -->\n      <div class=\"navbar-links\" *ngIf=\"isLoggedIn\">\n        <a routerLink=\"/accueil\" routerLinkActive=\"active-link\" class=\"nav-link\">\n          <i class=\"fas fa-home\"></i>\n          <span>Accueil</span>\n        </a>\n        <a routerLink=\"/search-price\" routerLinkActive=\"active-link\" class=\"nav-link\">\n          <i class=\"fas fa-search\"></i>\n          <span>Rechercher</span>\n        </a>\n      </div>\n\n      <!-- Right Side Actions -->\n      <div class=\"navbar-actions\" *ngIf=\"isLoggedIn\">\n        <!-- Notification Icon (for future use) -->\n        <button mat-icon-button class=\"notification-button\" [matBadge]=\"0\" [matBadgeHidden]=\"true\" matBadgeColor=\"accent\" aria-label=\"Notifications\">\n          <i class=\"fas fa-bell\"></i>\n        </button>\n\n        <!-- User Menu -->\n        <div class=\"user-menu-container\">\n          <button mat-button [matMenuTriggerFor]=\"userMenu\" class=\"user-menu-button\">\n            <div class=\"user-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <span class=\"user-name\">{{ userName }}</span>\n            <i class=\"fas fa-chevron-down\"></i>\n          </button>\n\n          <mat-menu #userMenu=\"matMenu\" xPosition=\"before\" class=\"user-menu\">\n            <div class=\"user-menu-header\">\n              <div class=\"user-avatar large\">\n                <i class=\"fas fa-user\"></i>\n              </div>\n              <div class=\"user-info\">\n                <h4>{{ userName }}</h4>\n                <p>{{ userAgency }}</p>\n              </div>\n            </div>\n\n            <mat-divider></mat-divider>\n\n            <button mat-menu-item disabled>\n              <i class=\"fas fa-user-circle\"></i>\n              <span>Mon profil</span>\n            </button>\n            <button mat-menu-item disabled>\n              <i class=\"fas fa-cog\"></i>\n              <span>Paramètres</span>\n            </button>\n            <button mat-menu-item disabled>\n              <i class=\"fas fa-history\"></i>\n              <span>Historique</span>\n            </button>\n\n            <mat-divider></mat-divider>\n\n            <button mat-menu-item (click)=\"logout()\">\n              <i class=\"fas fa-sign-out-alt\"></i>\n              <span>Déconnexion</span>\n            </button>\n          </mat-menu>\n        </div>\n      </div>\n    </div>\n  </mat-toolbar>\n\n  <!-- Progress Bar for page loading -->\n  <div class=\"progress-bar-container\" *ngIf=\"loading\">\n    <mat-progress-bar mode=\"indeterminate\" color=\"accent\"></mat-progress-bar>\n  </div>\n</div>\n"], "mappings": "AAAA,SAA+CA,YAAY,QAAsB,eAAe;AAChG,SAAiBC,eAAe,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,iBAAiB;AAG3G,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;;ICAjCC,EAAA,CAAAC,cAAA,iBAAyF;IAA1BD,EAAA,CAAAE,UAAA,mBAAAC,0DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IACtFT,EAAA,CAAAU,SAAA,YAA2B;IAC7BV,EAAA,CAAAW,YAAA,EAAS;;;;;IAaTX,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAU,SAAA,YAA2B;IAC3BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,cAAO;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAEtBX,EAAA,CAAAC,cAAA,YAA8E;IAC5ED,EAAA,CAAAU,SAAA,YAA6B;IAC7BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,iBAAU;IAAAZ,EAAA,CAAAW,YAAA,EAAO;;;;;;IAK3BX,EAAA,CAAAC,cAAA,cAA+C;IAG3CD,EAAA,CAAAU,SAAA,YAA2B;IAC7BV,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,cAAiC;IAG3BD,EAAA,CAAAU,SAAA,YAA2B;IAC7BV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAY,MAAA,GAAc;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAC7CX,EAAA,CAAAU,SAAA,YAAmC;IACrCV,EAAA,CAAAW,YAAA,EAAS;IAETX,EAAA,CAAAC,cAAA,wBAAmE;IAG7DD,EAAA,CAAAU,SAAA,aAA2B;IAC7BV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAY,MAAA,IAAc;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACvBX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAY,MAAA,IAAgB;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IAI3BX,EAAA,CAAAU,SAAA,mBAA2B;IAE3BV,EAAA,CAAAC,cAAA,kBAA+B;IAC7BD,EAAA,CAAAU,SAAA,aAAkC;IAClCV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,kBAAU;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAEzBX,EAAA,CAAAC,cAAA,kBAA+B;IAC7BD,EAAA,CAAAU,SAAA,aAA0B;IAC1BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,uBAAU;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAEzBX,EAAA,CAAAC,cAAA,kBAA+B;IAC7BD,EAAA,CAAAU,SAAA,aAA8B;IAC9BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,kBAAU;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAGzBX,EAAA,CAAAU,SAAA,mBAA2B;IAE3BV,EAAA,CAAAC,cAAA,kBAAyC;IAAnBD,EAAA,CAAAE,UAAA,mBAAAW,yDAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAO,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IACtChB,EAAA,CAAAU,SAAA,aAAmC;IACnCV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,wBAAW;IAAAZ,EAAA,CAAAW,YAAA,EAAO;;;;;IA5CsBX,EAAA,CAAAiB,SAAA,GAAc;IAAdjB,EAAA,CAAAkB,UAAA,eAAc;IAM7ClB,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAkB,UAAA,sBAAAC,GAAA,CAA8B;IAIvBnB,EAAA,CAAAiB,SAAA,GAAc;IAAdjB,EAAA,CAAAoB,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAc;IAU9BtB,EAAA,CAAAiB,SAAA,GAAc;IAAdjB,EAAA,CAAAoB,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAc;IACftB,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAoB,iBAAA,CAAAC,MAAA,CAAAE,UAAA,CAAgB;;;;;IAgCjCvB,EAAA,CAAAC,cAAA,cAAoD;IAClDD,EAAA,CAAAU,SAAA,2BAAyE;IAC3EV,EAAA,CAAAW,YAAA,EAAM;;;AD7ER,OAAM,MAAOa,eAAe;EAW1BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAZN,KAAAC,kBAAkB,GAAG,IAAIlC,YAAY,EAAQ;IAEvD,KAAAmC,UAAU,GAAG,KAAK;IAClB,KAAAP,QAAQ,GAAG,EAAE;IACb,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAO,OAAO,GAAG,KAAK;IAEP,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,QAAQ,GAAG,KAAK;EAKpB;EAEJC,QAAQA,CAAA;IACN;IACA,MAAMC,OAAO,GAAG,IAAI,CAACR,WAAW,CAACS,gBAAgB,CAACC,SAAS,CAACC,eAAe,IAAG;MAC5E,IAAI,CAACR,UAAU,GAAGQ,eAAe;MAEjC,IAAIA,eAAe,EAAE;QACnB,MAAMC,QAAQ,GAAG,IAAI,CAACZ,WAAW,CAACa,WAAW,EAAE;QAC/C,IAAI,CAACjB,QAAQ,GAAGgB,QAAQ,EAAEE,QAAQ,IAAI,aAAa;QACnD,IAAI,CAACjB,UAAU,GAAGe,QAAQ,EAAEG,MAAM,IAAI,QAAQ;;IAElD,CAAC,CAAC;IACF,IAAI,CAACV,aAAa,CAACW,IAAI,CAACR,OAAO,CAAC;IAEhC;IACA,MAAMS,SAAS,GAAG,IAAI,CAAChB,MAAM,CAACiB,MAAM,CACjCC,IAAI,CACH9C,MAAM,CAAC+C,KAAK,IACVA,KAAK,YAAYnD,eAAe,IAChCmD,KAAK,YAAYlD,aAAa,IAC9BkD,KAAK,YAAYjD,gBAAgB,IACjCiD,KAAK,YAAYhD,eAAe,CACjC,CACF,CACAsC,SAAS,CAACU,KAAK,IAAG;MACjB,IAAIA,KAAK,YAAYnD,eAAe,EAAE;QACpC,IAAI,CAACmC,OAAO,GAAG,IAAI;OACpB,MAAM;QACL,IAAI,CAACA,OAAO,GAAG,KAAK;;IAExB,CAAC,CAAC;IACJ,IAAI,CAACC,aAAa,CAACW,IAAI,CAACC,SAAS,CAAC;IAElC;IACA,IAAI,CAACI,WAAW,EAAE;EACpB;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACjB,aAAa,CAACkB,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEA;EACA1C,aAAaA,CAAA;IACX,IAAI,CAACmB,kBAAkB,CAACwB,IAAI,EAAE;EAChC;EAEA;EACApC,MAAMA,CAAA;IACJ,IAAI,CAACU,WAAW,CAACV,MAAM,EAAE;IACzB,IAAI,CAACW,MAAM,CAAC0B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEA;EAEAN,WAAWA,CAAA;IACT,MAAMO,cAAc,GAAGC,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACC,SAAS,IAAIF,QAAQ,CAACG,IAAI,CAACD,SAAS,IAAI,CAAC;IAE/G,IAAIL,cAAc,GAAG,EAAE,IAAI,CAAC,IAAI,CAACtB,QAAQ,EAAE;MACzCyB,QAAQ,CAACG,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACvC,IAAI,CAAC9B,QAAQ,GAAG,IAAI;KACrB,MAAM,IAAIsB,cAAc,IAAI,EAAE,IAAI,IAAI,CAACtB,QAAQ,EAAE;MAChDyB,QAAQ,CAACG,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,UAAU,CAAC;MAC1C,IAAI,CAAC/B,QAAQ,GAAG,KAAK;;EAEzB;;;uBAhFWR,eAAe,EAAAxB,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlE,EAAA,CAAAgE,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAf5C,eAAe;MAAA6C,SAAA;MAAAC,YAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAAfC,GAAA,CAAA1B,WAAA,EAAa;UAAA,UAAA/C,EAAA,CAAA0E,eAAA;;;;;;;;;;;UCX1B1E,EAAA,CAAAC,cAAA,aAA4B;UAItBD,EAAA,CAAA2E,UAAA,IAAAC,iCAAA,oBAES;UAGT5E,EAAA,CAAAC,cAAA,aAA0B;UAGpBD,EAAA,CAAAU,SAAA,WAAsC;UACxCV,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAY,MAAA,gBAAS;UAAAZ,EAAA,CAAAW,YAAA,EAAO;UAK7CX,EAAA,CAAA2E,UAAA,KAAAE,+BAAA,iBASM;UAGN7E,EAAA,CAAA2E,UAAA,KAAAG,+BAAA,mBAkDM;UACR9E,EAAA,CAAAW,YAAA,EAAM;UAIRX,EAAA,CAAA2E,UAAA,KAAAI,+BAAA,kBAEM;UACR/E,EAAA,CAAAW,YAAA,EAAM;;;UArF6CX,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAAuD,GAAA,CAAA5C,UAAA,CAAgB;UAelC7B,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAAuD,GAAA,CAAA5C,UAAA,CAAgB;UAYd7B,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAAuD,GAAA,CAAA5C,UAAA,CAAgB;UAuDZ7B,EAAA,CAAAiB,SAAA,GAAa;UAAbjB,EAAA,CAAAkB,UAAA,SAAAuD,GAAA,CAAA3C,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}