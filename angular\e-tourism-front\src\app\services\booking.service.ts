import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { AuthService } from './auth.service';
import {
  BookingTransactionRequest,
  BookingTransactionResponse,
  BeginTransactionRequest,
  BeginTransactionResponse,
  SetReservationInfoRequest,
  SetReservationInfoResponse,
  CommitTransactionRequest,
  CommitTransactionResponse
} from '../models/booking';

@Injectable({
  providedIn: 'root'
})
export class BookingService {
  private apiUrl = 'http://localhost:8080/booking';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  /**
   * Méthode principale pour effectuer une transaction de réservation
   * @param request La requête de transaction de réservation
   * @returns Une observable de la réponse de transaction de réservation
   */
  bookingTransaction(request: BookingTransactionRequest): Observable<BookingTransactionResponse> {
    // Utiliser l'endpoint unifié pour toutes les actions de transaction
    const endpoint = '/booking-transaction';
    const requestBody = request; // Envoyer l'objet BookingTransactionRequest complet

    // Récupérer le token d'authentification
    const token = this.authService.getToken();
    if (!token) {
      throw new Error('Vous devez être connecté pour effectuer cette action.');
    }

    // Créer les en-têtes avec le token d'authentification
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });

    console.log(`Envoi de la requête ${request.action} à ${endpoint}:`, requestBody);

    return this.http.post<any>(
      `${this.apiUrl}${endpoint}`,
      requestBody,
      { headers }
    ).pipe(
      map(response => {
        console.log(`Réponse reçue de ${endpoint}:`, response);

        // Vérifier si la réponse est valide
        if (!response) {
          throw new Error('Réponse invalide du serveur');
        }

        // Vérifier si la réponse est au format BookingTransactionResponse
        // ou si c'est une réponse directe du type spécifique (BeginTransactionResponse, etc.)
        let bookingResponse: BookingTransactionResponse = {
          action: request.action
        };

        // Si la réponse a déjà le format BookingTransactionResponse
        if (response.action &&
            ((response.beginResponse && request.action === 'begin') ||
             (response.infoResponse && request.action === 'info') ||
             (response.commitResponse && request.action === 'commit'))) {

          bookingResponse = response as BookingTransactionResponse;
        }
        // Si la réponse est une réponse directe (non encapsulée)
        else {
          // Adapter la réponse selon l'action
          switch (request.action) {
            case 'begin':
              // Si c'est une réponse BeginTransactionResponse directe
              if (response.header && response.body && response.body.transactionId) {
                bookingResponse.beginResponse = response as BeginTransactionResponse;
              }
              // Si c'est un format simplifié ou personnalisé
              else if (response.transactionId) {
                bookingResponse.beginResponse = {
                  header: {
                    requestId: response.requestId || '',
                    success: true,
                    responseTime: response.responseTime || new Date().toISOString(),
                    messages: response.messages || []
                  },
                  body: {
                    transactionId: response.transactionId,
                    expiresOn: response.expiresOn || new Date(Date.now() + 3600000).toISOString(),
                    status: response.status || 1,
                    transactionType: response.transactionType || 1,
                    reservationData: response.reservationData || {
                      travellers: [],
                      reservationInfo: null,
                      services: [],
                      paymentDetail: null,
                      invoices: []
                    }
                  }
                };
              } else {
                throw new Error('Format de réponse non reconnu pour l\'action begin');
              }
              break;

            case 'info':
              // Si c'est une réponse SetReservationInfoResponse directe
              if (response.header && response.body && response.body.transactionId) {
                bookingResponse.infoResponse = response as SetReservationInfoResponse;
              } else {
                throw new Error('Format de réponse non reconnu pour l\'action info');
              }
              break;

            case 'commit':
              // Si c'est une réponse CommitTransactionResponse directe
              if (response.header && response.body &&
                 (response.body.reservationNumber || response.body.transactionId)) {
                bookingResponse.commitResponse = response as CommitTransactionResponse;
              } else {
                throw new Error('Format de réponse non reconnu pour l\'action commit');
              }
              break;
          }
        }

        return bookingResponse;
      }),
      catchError(error => {
        console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);

        // Améliorer le message d'erreur pour faciliter le débogage
        let errorMessage = 'Une erreur est survenue lors de la transaction de réservation';

        if (error.error && error.error.message) {
          // Erreur provenant du backend avec un message
          errorMessage = `Erreur: ${error.error.message}`;
        } else if (error.message) {
          // Erreur avec un message simple
          errorMessage = error.message;
        } else if (error.status) {
          // Erreur HTTP
          switch (error.status) {
            case 401:
              errorMessage = 'Vous n\'êtes pas autorisé à effectuer cette action. Veuillez vous reconnecter.';
              break;
            case 403:
              errorMessage = 'Accès refusé. Vous n\'avez pas les droits nécessaires pour effectuer cette action.';
              break;
            case 404:
              errorMessage = 'Le service de réservation est introuvable. Veuillez contacter l\'administrateur.';
              break;
            case 500:
              errorMessage = 'Erreur interne du serveur. Veuillez réessayer ultérieurement.';
              break;
            default:
              errorMessage = `Erreur HTTP ${error.status}: ${error.statusText}`;
          }
        }

        // Créer une nouvelle erreur avec un message plus descriptif
        const enhancedError = new Error(errorMessage);
        enhancedError.name = 'BookingTransactionError';

        // Conserver les détails de l'erreur originale
        (enhancedError as any).originalError = error;

        throw enhancedError;
      })
    );
  }

  /**
   * Méthode pour démarrer une transaction de réservation
   * @param offerIds Les IDs des offres à réserver
   * @param currency La devise
   * @param culture La culture
   * @returns Une observable de la réponse de début de transaction
   */
  beginTransaction(offerIds: string[], currency: string = 'EUR', culture: string = 'fr-FR'): Observable<BookingTransactionResponse> {
    const request: BookingTransactionRequest = {
      action: 'begin',
      beginRequest: {
        offerIds,
        currency,
        culture
      }
    };

    return this.bookingTransaction(request);
  }

  /**
   * Méthode pour définir les informations de réservation
   * @param request La requête d'informations de réservation
   * @returns Une observable de la réponse d'informations de réservation
   */
  setReservationInfo(request: SetReservationInfoRequest): Observable<BookingTransactionResponse> {
    // Vérifier si tous les passagers sont des enfants ou des bébés
    const hasAdult = request.travellers.some((traveller: any) =>
      traveller.passengerType === 1 // 1 = Adult
    );

    // Si tous les passagers sont des enfants ou des bébés, s'assurer que customerInfo n'est pas inclus
    if (!hasAdult && request.customerInfo) {
      console.log('Tous les passagers sont des enfants ou des bébés, customerInfo ne sera pas inclus dans la requête');
      // Créer une nouvelle requête sans customerInfo
      const { customerInfo, ...requestWithoutCustomerInfo } = request;
      request = requestWithoutCustomerInfo;
    }

    const bookingRequest: BookingTransactionRequest = {
      action: 'info',
      infoRequest: request
    };

    return this.bookingTransaction(bookingRequest);
  }

  /**
   * Méthode pour finaliser une transaction de réservation
   * @param request La requête de finalisation de transaction
   * @returns Une observable de la réponse de finalisation de transaction
   */
  commitTransaction(request: CommitTransactionRequest): Observable<BookingTransactionResponse> {
    const bookingRequest: BookingTransactionRequest = {
      action: 'commit',
      commitRequest: request
    };

    return this.bookingTransaction(bookingRequest);
  }

  /**
   * Crée un objet de requête d'informations de réservation par défaut
   * @param transactionId L'ID de transaction
   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut
   */
  createDefaultReservationInfoRequest(transactionId: string): SetReservationInfoRequest {
    return {
      transactionId,
      travellers: [],
      reservationNote: '',
      agencyReservationNumber: ''
    };
  }

  /**
   * Crée un objet de requête de finalisation de transaction par défaut
   * @param transactionId L'ID de transaction
   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut
   */
  createDefaultCommitTransactionRequest(transactionId: string): CommitTransactionRequest {
    return {
      transactionId,
      paymentOption: 1, // Option de paiement par défaut
      paymentInformation: {
        paymentTypeId: 1, // Type de paiement par défaut
        paymentPrice: {
          amount: 0,
          currency: 'EUR'
        },
        paymentDate: new Date().toISOString()
      }
    };
  }
}
