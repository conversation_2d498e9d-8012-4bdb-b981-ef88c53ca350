{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/divider\";\nimport * as i7 from \"@angular/material/menu\";\nimport * as i8 from \"@angular/material/badge\";\nfunction NavbarComponent_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleSidebar());\n    });\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavbarComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"a\", 15);\n    i0.ɵɵelement(2, \"i\", 16);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Accueil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"a\", 17);\n    i0.ɵɵelement(6, \"i\", 18);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Rechercher\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction NavbarComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"button\", 20);\n    i0.ɵɵelement(2, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 22)(4, \"button\", 23)(5, \"div\", 24);\n    i0.ɵɵelement(6, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 26);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-menu\", 28, 29)(12, \"div\", 30)(13, \"div\", 31);\n    i0.ɵɵelement(14, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 32)(16, \"h4\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(20, \"mat-divider\");\n    i0.ɵɵelementStart(21, \"button\", 33);\n    i0.ɵɵelement(22, \"i\", 34);\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24, \"Mon profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"button\", 33);\n    i0.ɵɵelement(26, \"i\", 35);\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"button\", 33);\n    i0.ɵɵelement(30, \"i\", 36);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \"Historique\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(33, \"mat-divider\");\n    i0.ɵɵelementStart(34, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_div_11_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.logout());\n    });\n    i0.ɵɵelement(35, \"i\", 38);\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(11);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matBadge\", 0)(\"matBadgeHidden\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r6);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.userName);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.userName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.userAgency);\n  }\n}\nfunction NavbarComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"mat-progress-bar\", 40);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class NavbarComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.isLoggedIn = false;\n    this.userName = '';\n  }\n  ngOnInit() {\n    this.authService.isAuthenticated$.subscribe(isAuthenticated => {\n      this.isLoggedIn = isAuthenticated;\n      if (isAuthenticated) {\n        const userInfo = this.authService.getUserInfo();\n        this.userName = userInfo?.username || 'User';\n      }\n    });\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function NavbarComponent_Factory(t) {\n      return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavbarComponent,\n      selectors: [[\"app-navbar\"]],\n      decls: 13,\n      vars: 4,\n      consts: [[1, \"navbar-wrapper\"], [1, \"navbar\"], [1, \"navbar-container\"], [\"mat-icon-button\", \"\", \"class\", \"menu-toggle\", 3, \"click\", 4, \"ngIf\"], [1, \"navbar-brand\"], [\"routerLink\", \"/accueil\", 1, \"brand-link\"], [1, \"brand-icon\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"brand-name\"], [\"class\", \"navbar-links\", 4, \"ngIf\"], [\"class\", \"navbar-actions\", 4, \"ngIf\"], [\"class\", \"progress-bar-container\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"menu-toggle\", 3, \"click\"], [1, \"fas\", \"fa-bars\"], [1, \"navbar-links\"], [\"routerLink\", \"/accueil\", \"routerLinkActive\", \"active-link\", 1, \"nav-link\"], [1, \"fas\", \"fa-home\"], [\"routerLink\", \"/search-price\", \"routerLinkActive\", \"active-link\", 1, \"nav-link\"], [1, \"fas\", \"fa-search\"], [1, \"navbar-actions\"], [\"mat-icon-button\", \"\", \"matBadgeColor\", \"accent\", \"aria-label\", \"Notifications\", 1, \"notification-button\", 3, \"matBadge\", \"matBadgeHidden\"], [1, \"fas\", \"fa-bell\"], [1, \"user-menu-container\"], [\"mat-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"user-avatar\"], [1, \"fas\", \"fa-user\"], [1, \"user-name\"], [1, \"fas\", \"fa-chevron-down\"], [\"xPosition\", \"before\", 1, \"user-menu\"], [\"userMenu\", \"matMenu\"], [1, \"user-menu-header\"], [1, \"user-avatar\", \"large\"], [1, \"user-info\"], [\"mat-menu-item\", \"\", \"disabled\", \"\"], [1, \"fas\", \"fa-user-circle\"], [1, \"fas\", \"fa-cog\"], [1, \"fas\", \"fa-history\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [1, \"progress-bar-container\"], [\"mode\", \"indeterminate\", \"color\", \"accent\"]],\n      template: function NavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-toolbar\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, NavbarComponent_button_3_Template, 2, 0, \"button\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"a\", 5)(6, \"div\", 6);\n          i0.ɵɵelement(7, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"span\", 8);\n          i0.ɵɵtext(9, \"E-Tourism\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(10, NavbarComponent_div_10_Template, 9, 0, \"div\", 9);\n          i0.ɵɵtemplate(11, NavbarComponent_div_11_Template, 38, 6, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, NavbarComponent_div_12_Template, 2, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [i3.NgIf, i2.RouterLink, i2.RouterLinkActive, i4.MatButton, i4.MatIconButton, i5.MatToolbar, i6.MatDivider, i7.MatMenu, i7.MatMenuItem, i7.MatMenuTrigger, i8.MatBadge],\n      styles: [\".navbar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  height: 64px;\\n  padding: 0;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  color: white;\\n  box-shadow: var(--elevation-2);\\n}\\n\\n.navbar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  height: 100%;\\n  padding: 0 var(--spacing-md);\\n}\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n}\\n\\n.brand-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  color: white;\\n  text-decoration: none;\\n  font-weight: 600;\\n  font-size: 1.25rem;\\n}\\n\\n.brand-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 1.5rem;\\n}\\n\\n.brand-name[_ngcontent-%COMP%] {\\n  letter-spacing: 0.5px;\\n}\\n\\n.navbar-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n}\\n\\n.navbar-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: white;\\n  opacity: 0.9;\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-xs);\\n  padding: 0 var(--spacing-sm);\\n  height: 36px;\\n  border-radius: var(--border-radius-small);\\n  transition: background-color var(--transition-fast);\\n}\\n\\n.navbar-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  opacity: 1;\\n}\\n\\n.navbar-links[_ngcontent-%COMP%]   a.active-link[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n  opacity: 1;\\n}\\n\\n.navbar-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n\\n.navbar-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  color: white;\\n  font-weight: 500;\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  max-width: 150px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n\\n\\n.mat-menu-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: var(--spacing-sm);\\n  font-size: 1.1rem;\\n  width: 24px;\\n  height: 24px;\\n  line-height: 24px;\\n  text-align: center;\\n  color: var(--primary-color);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .navbar-links[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n@media (max-width: 600px) {\\n  .brand-name[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n\\n  .user-name[_ngcontent-%COMP%] {\\n    max-width: 80px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "NavbarComponent_button_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "toggleSidebar", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "NavbarComponent_div_11_Template_button_click_34_listener", "_r8", "ctx_r7", "logout", "ɵɵadvance", "ɵɵproperty", "_r6", "ɵɵtextInterpolate", "ctx_r2", "userName", "userAgency", "NavbarComponent", "constructor", "authService", "router", "isLoggedIn", "ngOnInit", "isAuthenticated$", "subscribe", "isAuthenticated", "userInfo", "getUserInfo", "username", "navigate", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "NavbarComponent_Template", "rf", "ctx", "ɵɵtemplate", "NavbarComponent_button_3_Template", "NavbarComponent_div_10_Template", "NavbarComponent_div_11_Template", "NavbarComponent_div_12_Template", "loading"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\navbar\\navbar.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\navbar\\navbar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-navbar',\n  templateUrl: './navbar.component.html',\n  styleUrls: ['./navbar.component.css']\n})\nexport class NavbarComponent implements OnInit {\n  isLoggedIn = false;\n  userName = '';\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) { }\n\n  ngOnInit(): void {\n    this.authService.isAuthenticated$.subscribe(isAuthenticated => {\n      this.isLoggedIn = isAuthenticated;\n\n      if (isAuthenticated) {\n        const userInfo = this.authService.getUserInfo();\n        this.userName = userInfo?.username || 'User';\n      }\n    });\n  }\n\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n}\n", "<div class=\"navbar-wrapper\">\n  <mat-toolbar class=\"navbar\">\n    <div class=\"navbar-container\">\n      <!-- Mobile Menu Toggle -->\n      <button mat-icon-button class=\"menu-toggle\" *ngIf=\"isLoggedIn\" (click)=\"toggleSidebar()\">\n        <i class=\"fas fa-bars\"></i>\n      </button>\n\n      <!-- Brand Logo -->\n      <div class=\"navbar-brand\">\n        <a routerLink=\"/accueil\" class=\"brand-link\">\n          <div class=\"brand-icon\">\n            <i class=\"fas fa-plane-departure\"></i>\n          </div>\n          <span class=\"brand-name\">E-Tourism</span>\n        </a>\n      </div>\n\n      <!-- Navigation Links -->\n      <div class=\"navbar-links\" *ngIf=\"isLoggedIn\">\n        <a routerLink=\"/accueil\" routerLinkActive=\"active-link\" class=\"nav-link\">\n          <i class=\"fas fa-home\"></i>\n          <span>Accueil</span>\n        </a>\n        <a routerLink=\"/search-price\" routerLinkActive=\"active-link\" class=\"nav-link\">\n          <i class=\"fas fa-search\"></i>\n          <span>Rechercher</span>\n        </a>\n      </div>\n\n      <!-- Right Side Actions -->\n      <div class=\"navbar-actions\" *ngIf=\"isLoggedIn\">\n        <!-- Notification Icon (for future use) -->\n        <button mat-icon-button class=\"notification-button\" [matBadge]=\"0\" [matBadgeHidden]=\"true\" matBadgeColor=\"accent\" aria-label=\"Notifications\">\n          <i class=\"fas fa-bell\"></i>\n        </button>\n\n        <!-- User Menu -->\n        <div class=\"user-menu-container\">\n          <button mat-button [matMenuTriggerFor]=\"userMenu\" class=\"user-menu-button\">\n            <div class=\"user-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <span class=\"user-name\">{{ userName }}</span>\n            <i class=\"fas fa-chevron-down\"></i>\n          </button>\n\n          <mat-menu #userMenu=\"matMenu\" xPosition=\"before\" class=\"user-menu\">\n            <div class=\"user-menu-header\">\n              <div class=\"user-avatar large\">\n                <i class=\"fas fa-user\"></i>\n              </div>\n              <div class=\"user-info\">\n                <h4>{{ userName }}</h4>\n                <p>{{ userAgency }}</p>\n              </div>\n            </div>\n\n            <mat-divider></mat-divider>\n\n            <button mat-menu-item disabled>\n              <i class=\"fas fa-user-circle\"></i>\n              <span>Mon profil</span>\n            </button>\n            <button mat-menu-item disabled>\n              <i class=\"fas fa-cog\"></i>\n              <span>Paramètres</span>\n            </button>\n            <button mat-menu-item disabled>\n              <i class=\"fas fa-history\"></i>\n              <span>Historique</span>\n            </button>\n\n            <mat-divider></mat-divider>\n\n            <button mat-menu-item (click)=\"logout()\">\n              <i class=\"fas fa-sign-out-alt\"></i>\n              <span>Déconnexion</span>\n            </button>\n          </mat-menu>\n        </div>\n      </div>\n    </div>\n  </mat-toolbar>\n\n  <!-- Progress Bar for page loading -->\n  <div class=\"progress-bar-container\" *ngIf=\"loading\">\n    <mat-progress-bar mode=\"indeterminate\" color=\"accent\"></mat-progress-bar>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;ICIMA,EAAA,CAAAC,cAAA,iBAAyF;IAA1BD,EAAA,CAAAE,UAAA,mBAAAC,0DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IACtFT,EAAA,CAAAU,SAAA,YAA2B;IAC7BV,EAAA,CAAAW,YAAA,EAAS;;;;;IAaTX,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAU,SAAA,YAA2B;IAC3BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,cAAO;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAEtBX,EAAA,CAAAC,cAAA,YAA8E;IAC5ED,EAAA,CAAAU,SAAA,YAA6B;IAC7BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,iBAAU;IAAAZ,EAAA,CAAAW,YAAA,EAAO;;;;;;IAK3BX,EAAA,CAAAC,cAAA,cAA+C;IAG3CD,EAAA,CAAAU,SAAA,YAA2B;IAC7BV,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,cAAiC;IAG3BD,EAAA,CAAAU,SAAA,YAA2B;IAC7BV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAY,MAAA,GAAc;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAC7CX,EAAA,CAAAU,SAAA,YAAmC;IACrCV,EAAA,CAAAW,YAAA,EAAS;IAETX,EAAA,CAAAC,cAAA,wBAAmE;IAG7DD,EAAA,CAAAU,SAAA,aAA2B;IAC7BV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAY,MAAA,IAAc;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACvBX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAY,MAAA,IAAgB;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IAI3BX,EAAA,CAAAU,SAAA,mBAA2B;IAE3BV,EAAA,CAAAC,cAAA,kBAA+B;IAC7BD,EAAA,CAAAU,SAAA,aAAkC;IAClCV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,kBAAU;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAEzBX,EAAA,CAAAC,cAAA,kBAA+B;IAC7BD,EAAA,CAAAU,SAAA,aAA0B;IAC1BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,uBAAU;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAEzBX,EAAA,CAAAC,cAAA,kBAA+B;IAC7BD,EAAA,CAAAU,SAAA,aAA8B;IAC9BV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,kBAAU;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAGzBX,EAAA,CAAAU,SAAA,mBAA2B;IAE3BV,EAAA,CAAAC,cAAA,kBAAyC;IAAnBD,EAAA,CAAAE,UAAA,mBAAAW,yDAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAO,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IACtChB,EAAA,CAAAU,SAAA,aAAmC;IACnCV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAY,MAAA,wBAAW;IAAAZ,EAAA,CAAAW,YAAA,EAAO;;;;;IA5CsBX,EAAA,CAAAiB,SAAA,GAAc;IAAdjB,EAAA,CAAAkB,UAAA,eAAc;IAM7ClB,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAkB,UAAA,sBAAAC,GAAA,CAA8B;IAIvBnB,EAAA,CAAAiB,SAAA,GAAc;IAAdjB,EAAA,CAAAoB,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAc;IAU9BtB,EAAA,CAAAiB,SAAA,GAAc;IAAdjB,EAAA,CAAAoB,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAc;IACftB,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAoB,iBAAA,CAAAC,MAAA,CAAAE,UAAA,CAAgB;;;;;IAgCjCvB,EAAA,CAAAC,cAAA,cAAoD;IAClDD,EAAA,CAAAU,SAAA,2BAAyE;IAC3EV,EAAA,CAAAW,YAAA,EAAM;;;AD/ER,OAAM,MAAOa,eAAe;EAI1BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAN,QAAQ,GAAG,EAAE;EAKT;EAEJO,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACI,gBAAgB,CAACC,SAAS,CAACC,eAAe,IAAG;MAC5D,IAAI,CAACJ,UAAU,GAAGI,eAAe;MAEjC,IAAIA,eAAe,EAAE;QACnB,MAAMC,QAAQ,GAAG,IAAI,CAACP,WAAW,CAACQ,WAAW,EAAE;QAC/C,IAAI,CAACZ,QAAQ,GAAGW,QAAQ,EAAEE,QAAQ,IAAI,MAAM;;IAEhD,CAAC,CAAC;EACJ;EAEAnB,MAAMA,CAAA;IACJ,IAAI,CAACU,WAAW,CAACV,MAAM,EAAE;IACzB,IAAI,CAACW,MAAM,CAACS,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAvBWZ,eAAe,EAAAxB,EAAA,CAAAqC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvC,EAAA,CAAAqC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfjB,eAAe;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT5BhD,EAAA,CAAAC,cAAA,aAA4B;UAItBD,EAAA,CAAAkD,UAAA,IAAAC,iCAAA,oBAES;UAGTnD,EAAA,CAAAC,cAAA,aAA0B;UAGpBD,EAAA,CAAAU,SAAA,WAAsC;UACxCV,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAY,MAAA,gBAAS;UAAAZ,EAAA,CAAAW,YAAA,EAAO;UAK7CX,EAAA,CAAAkD,UAAA,KAAAE,+BAAA,iBASM;UAGNpD,EAAA,CAAAkD,UAAA,KAAAG,+BAAA,mBAkDM;UACRrD,EAAA,CAAAW,YAAA,EAAM;UAIRX,EAAA,CAAAkD,UAAA,KAAAI,+BAAA,kBAEM;UACRtD,EAAA,CAAAW,YAAA,EAAM;;;UArF6CX,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAA+B,GAAA,CAAArB,UAAA,CAAgB;UAelC5B,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAA+B,GAAA,CAAArB,UAAA,CAAgB;UAYd5B,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAA+B,GAAA,CAAArB,UAAA,CAAgB;UAuDZ5B,EAAA,CAAAiB,SAAA,GAAa;UAAbjB,EAAA,CAAAkB,UAAA,SAAA+B,GAAA,CAAAM,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}