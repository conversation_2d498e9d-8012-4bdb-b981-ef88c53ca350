{"ast": null, "code": "import { NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/progress-bar\";\nimport * as i6 from \"@angular/material/toolbar\";\nimport * as i7 from \"@angular/material/divider\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"@angular/material/badge\";\nfunction NavbarComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"a\", 12);\n    i0.ɵɵelement(2, \"i\", 13);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Rechercher\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction NavbarComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"button\", 15);\n    i0.ɵɵelement(2, \"i\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"button\", 18)(5, \"div\", 19);\n    i0.ɵɵelement(6, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 21);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-menu\", 23, 24)(12, \"div\", 25)(13, \"div\", 26);\n    i0.ɵɵelement(14, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 27)(16, \"h4\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(20, \"mat-divider\");\n    i0.ɵɵelementStart(21, \"button\", 28);\n    i0.ɵɵelement(22, \"i\", 29);\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24, \"Mon profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"button\", 28);\n    i0.ɵɵelement(26, \"i\", 30);\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Param\\u00E8tres\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"button\", 28);\n    i0.ɵɵelement(30, \"i\", 31);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \"Historique\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(33, \"mat-divider\");\n    i0.ɵɵelementStart(34, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_div_10_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.logout());\n    });\n    i0.ɵɵelement(35, \"i\", 33);\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r3 = i0.ɵɵreference(11);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matBadge\", 0)(\"matBadgeHidden\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.userName);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.userName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.userAgency);\n  }\n}\nfunction NavbarComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"mat-progress-bar\", 35);\n    i0.ɵɵelementEnd();\n  }\n}\nexport let NavbarComponent = /*#__PURE__*/(() => {\n  class NavbarComponent {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n      this.isLoggedIn = false;\n      this.userName = '';\n      this.userAgency = '';\n      this.loading = false;\n      this.subscriptions = [];\n      this.scrolled = false;\n    }\n    ngOnInit() {\n      // Surveiller l'état d'authentification\n      const authSub = this.authService.isAuthenticated$.subscribe(isAuthenticated => {\n        this.isLoggedIn = isAuthenticated;\n        if (isAuthenticated) {\n          const userInfo = this.authService.getUserInfo();\n          this.userName = userInfo?.username || 'Utilisateur';\n          this.userAgency = userInfo?.agency || 'Agence';\n        }\n      });\n      this.subscriptions.push(authSub);\n      // Surveiller les événements de navigation pour afficher la barre de progression\n      const routerSub = this.router.events.pipe(filter(event => event instanceof NavigationStart || event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError)).subscribe(event => {\n        if (event instanceof NavigationStart) {\n          this.loading = true;\n        } else {\n          this.loading = false;\n        }\n      });\n      this.subscriptions.push(routerSub);\n      // Vérifier le défilement initial\n      this.checkScroll();\n    }\n    ngOnDestroy() {\n      // Nettoyer les abonnements\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    // Méthode de déconnexion\n    logout() {\n      this.authService.logout();\n      this.router.navigate(['/login']);\n    }\n    // Écouter l'événement de défilement pour changer l'apparence de la navbar\n    checkScroll() {\n      const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n      if (scrollPosition > 20 && !this.scrolled) {\n        document.body.classList.add('scrolled');\n        this.scrolled = true;\n      } else if (scrollPosition <= 20 && this.scrolled) {\n        document.body.classList.remove('scrolled');\n        this.scrolled = false;\n      }\n    }\n    static {\n      this.ɵfac = function NavbarComponent_Factory(t) {\n        return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NavbarComponent,\n        selectors: [[\"app-navbar\"]],\n        hostBindings: function NavbarComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"scroll\", function NavbarComponent_scroll_HostBindingHandler() {\n              return ctx.checkScroll();\n            }, false, i0.ɵɵresolveWindow);\n          }\n        },\n        decls: 12,\n        vars: 3,\n        consts: [[1, \"navbar-wrapper\"], [1, \"navbar\"], [1, \"navbar-container\"], [1, \"navbar-brand\"], [\"routerLink\", \"/search-price\", 1, \"brand-link\"], [1, \"brand-icon\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"brand-name\"], [\"class\", \"navbar-links\", 4, \"ngIf\"], [\"class\", \"navbar-actions\", 4, \"ngIf\"], [\"class\", \"progress-bar-container\", 4, \"ngIf\"], [1, \"navbar-links\"], [\"routerLink\", \"/search-price\", \"routerLinkActive\", \"active-link\", 1, \"nav-link\"], [1, \"fas\", \"fa-search\"], [1, \"navbar-actions\"], [\"mat-icon-button\", \"\", \"matBadgeColor\", \"accent\", \"aria-label\", \"Notifications\", 1, \"notification-button\", 3, \"matBadge\", \"matBadgeHidden\"], [1, \"fas\", \"fa-bell\"], [1, \"user-menu-container\"], [\"mat-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"user-avatar\"], [1, \"fas\", \"fa-user\"], [1, \"user-name\"], [1, \"fas\", \"fa-chevron-down\"], [\"xPosition\", \"before\", 1, \"user-menu\"], [\"userMenu\", \"matMenu\"], [1, \"user-menu-header\"], [1, \"user-avatar\", \"large\"], [1, \"user-info\"], [\"mat-menu-item\", \"\", \"disabled\", \"\"], [1, \"fas\", \"fa-user-circle\"], [1, \"fas\", \"fa-cog\"], [1, \"fas\", \"fa-history\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [1, \"progress-bar-container\"], [\"mode\", \"indeterminate\", \"color\", \"accent\"]],\n        template: function NavbarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-toolbar\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"i\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"span\", 7);\n            i0.ɵɵtext(8, \"E-Tourism\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(9, NavbarComponent_div_9_Template, 5, 0, \"div\", 8);\n            i0.ɵɵtemplate(10, NavbarComponent_div_10_Template, 38, 6, \"div\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(11, NavbarComponent_div_11_Template, 2, 0, \"div\", 10);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          }\n        },\n        dependencies: [i3.NgIf, i2.RouterLink, i2.RouterLinkActive, i4.MatButton, i4.MatIconButton, i5.MatProgressBar, i6.MatToolbar, i7.MatDivider, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, i9.MatBadge],\n        styles: [\".navbar-wrapper[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;z-index:var(--z-index-fixed)}.navbar[_ngcontent-%COMP%]{height:64px;padding:0;background:rgba(255,255,255,.95);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);color:var(--text-primary);box-shadow:var(--elevation-2);transition:all var(--transition-medium)}.navbar-container[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%;height:100%;padding:0 var(--spacing-md);max-width:var(--container-xl);margin:0 auto}.navbar-brand[_ngcontent-%COMP%]{display:flex;align-items:center}.brand-link[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-sm);text-decoration:none;font-weight:600;font-size:1.25rem;color:var(--primary-dark);transition:all var(--transition-fast)}.brand-link[_ngcontent-%COMP%]:hover{transform:translateY(-1px)}.brand-icon[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:40px;height:40px;background:linear-gradient(135deg,var(--primary-color),var(--primary-dark));border-radius:12px;color:#fff;font-size:1.2rem;box-shadow:0 4px 8px rgba(var(--primary-color-rgb),.3);position:relative;overflow:hidden}.brand-icon[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(45deg,transparent 0%,rgba(255,255,255,.2) 50%,transparent 100%);animation:shimmer 2s infinite}.brand-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:relative;z-index:1}.brand-name[_ngcontent-%COMP%]{letter-spacing:.5px;background:linear-gradient(135deg,var(--primary-dark),var(--primary-color));-webkit-background-clip:text;-webkit-text-fill-color:transparent;font-weight:700}.navbar-links[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-md);margin-left:var(--spacing-xl)}.nav-link[_ngcontent-%COMP%]{position:relative;color:var(--text-secondary);font-weight:500;display:flex;align-items:center;gap:var(--spacing-xs);padding:var(--spacing-sm) var(--spacing-md);border-radius:var(--border-radius-medium);transition:all var(--transition-fast);text-decoration:none}.nav-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem;transition:all var(--transition-fast)}.nav-link[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:0;left:50%;width:0;height:3px;background:linear-gradient(to right,var(--primary-color),var(--accent-color));border-radius:3px;transform:translate(-50%);transition:width var(--transition-medium)}.nav-link[_ngcontent-%COMP%]:hover{color:var(--primary-color);background-color:rgba(var(--primary-color-rgb),.05)}.nav-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:translateY(-2px)}.nav-link.active-link[_ngcontent-%COMP%]{color:var(--primary-color);font-weight:600}.nav-link.active-link[_ngcontent-%COMP%]:after{width:30px}.navbar-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-md)}.notification-button[_ngcontent-%COMP%]{color:var(--text-secondary);transition:all var(--transition-fast)}.notification-button[_ngcontent-%COMP%]:hover{color:var(--primary-color);transform:translateY(-2px)}.notification-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem}.user-menu-container[_ngcontent-%COMP%]{position:relative}.user-menu-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-sm);color:var(--text-primary);font-weight:500;padding:var(--spacing-xs) var(--spacing-sm);border-radius:var(--border-radius-medium);transition:all var(--transition-fast);border:1px solid transparent}.user-menu-button[_ngcontent-%COMP%]:hover{background-color:rgba(var(--primary-color-rgb),.05);border-color:rgba(var(--primary-color-rgb),.1)}.user-avatar[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:32px;height:32px;background:linear-gradient(135deg,var(--primary-light),var(--primary-color));border-radius:50%;color:#fff;font-size:.9rem;box-shadow:0 2px 4px rgba(var(--primary-color-rgb),.3)}.user-avatar.large[_ngcontent-%COMP%]{width:48px;height:48px;font-size:1.2rem}.user-name[_ngcontent-%COMP%]{display:inline-block;max-width:150px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.user-menu-button[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%]{font-size:.8rem;color:var(--text-secondary);transition:transform var(--transition-fast)}.user-menu-button[_ngcontent-%COMP%]:hover   i.fa-chevron-down[_ngcontent-%COMP%]{transform:translateY(2px)}.user-menu-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-md);padding:var(--spacing-md) var(--spacing-md) var(--spacing-sm)}.user-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;font-size:1rem;font-weight:600;color:var(--text-primary)}.user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.8rem;color:var(--text-secondary)}  .mat-menu-panel{border-radius:var(--border-radius-medium)!important;overflow:hidden;min-width:220px!important;box-shadow:var(--elevation-3)!important}  .mat-menu-content{padding:0!important}  .mat-menu-item{font-family:var(--font-family);height:48px;line-height:48px}  .mat-menu-item i{margin-right:var(--spacing-sm);font-size:1.1rem;width:24px;height:24px;line-height:24px;text-align:center;color:var(--primary-color)}  .mat-menu-item:hover:not([disabled]){background-color:rgba(var(--primary-color-rgb),.05)!important}  .mat-menu-item[disabled]{color:var(--text-disabled)!important}  .mat-menu-item[disabled] i{color:var(--text-disabled)!important}.progress-bar-container[_ngcontent-%COMP%]{position:absolute;top:64px;left:0;right:0;height:4px;overflow:hidden}.scrolled[_nghost-%COMP%]   .navbar[_ngcontent-%COMP%], .scrolled   [_nghost-%COMP%]   .navbar[_ngcontent-%COMP%]{height:56px;box-shadow:var(--elevation-3);background:rgba(255,255,255,.98)}.scrolled[_nghost-%COMP%]   .brand-icon[_ngcontent-%COMP%], .scrolled   [_nghost-%COMP%]   .brand-icon[_ngcontent-%COMP%]{width:36px;height:36px;font-size:1.1rem}@media (max-width: 992px){.navbar-links[_ngcontent-%COMP%]{margin-left:var(--spacing-md);gap:var(--spacing-sm)}.nav-link[_ngcontent-%COMP%]{padding:var(--spacing-sm) var(--spacing-sm)}}@media (max-width: 768px){.navbar-links[_ngcontent-%COMP%]{display:none}.brand-link[_ngcontent-%COMP%]{margin-right:auto}}@media (max-width: 600px){.brand-name[_ngcontent-%COMP%]{display:none}.user-name[_ngcontent-%COMP%]{max-width:80px}.navbar-actions[_ngcontent-%COMP%]{gap:var(--spacing-sm)}}\"]\n      });\n    }\n  }\n  return NavbarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}