{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/button\";\nexport class SidebarComponent {\n  constructor() {\n    this.isOpen = false;\n    this.sidebarClose = new EventEmitter();\n  }\n  // Détermine si l'écran est en mode mobile\n  get isMobile() {\n    return window.innerWidth < 768;\n  }\n  // Ferme la sidebar et émet l'événement\n  closeSidebar() {\n    this.sidebarClose.emit();\n  }\n  // Ferme la sidebar uniquement en mode mobile\n  closeSidebarOnMobile() {\n    if (this.isMobile) {\n      this.closeSidebar();\n    }\n  }\n  // Ferme la sidebar si l'utilisateur appuie sur la touche Escape\n  onEscapePress() {\n    if (this.isOpen) {\n      this.closeSidebar();\n    }\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      hostBindings: function SidebarComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.escape\", function SidebarComponent_keydown_escape_HostBindingHandler() {\n            return ctx.onEscapePress();\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        isOpen: \"isOpen\"\n      },\n      outputs: {\n        sidebarClose: \"sidebarClose\"\n      },\n      decls: 53,\n      vars: 4,\n      consts: [[1, \"sidebar\"], [1, \"sidebar-header\"], [1, \"sidebar-brand\"], [1, \"brand-icon\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"brand-name\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"sidebar-nav\"], [1, \"nav-section\"], [1, \"nav-section-title\"], [\"routerLink\", \"/accueil\", \"routerLinkActive\", \"active-link\", 1, \"nav-item\", 3, \"click\"], [1, \"fas\", \"fa-home\"], [\"routerLink\", \"/search-price\", \"routerLinkActive\", \"active-link\", 1, \"nav-item\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"nav-item\", \"disabled\"], [1, \"fas\", \"fa-history\"], [1, \"fas\", \"fa-bookmark\"], [1, \"fas\", \"fa-question-circle\"], [1, \"fas\", \"fa-headset\"], [1, \"sidebar-footer\"], [1, \"app-info\"], [1, \"app-logo\"], [1, \"fas\", \"fa-globe-americas\"], [1, \"app-details\"], [1, \"app-name\"], [1, \"app-version\"], [1, \"sidebar-backdrop\", 3, \"click\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"i\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"h3\", 5);\n          i0.ɵɵtext(6, \"E-Tourism\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_button_click_7_listener() {\n            return ctx.closeSidebar();\n          });\n          i0.ɵɵelement(8, \"i\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9)(11, \"h4\", 10);\n          i0.ɵɵtext(12, \"Menu principal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"a\", 11);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_a_click_13_listener() {\n            return ctx.closeSidebarOnMobile();\n          });\n          i0.ɵɵelement(14, \"i\", 12);\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Accueil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"a\", 13);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_a_click_17_listener() {\n            return ctx.closeSidebarOnMobile();\n          });\n          i0.ɵɵelement(18, \"i\", 14);\n          i0.ɵɵelementStart(19, \"span\");\n          i0.ɵɵtext(20, \"Rechercher un vol\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 9)(22, \"h4\", 10);\n          i0.ɵɵtext(23, \"R\\u00E9servations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"a\", 15);\n          i0.ɵɵelement(25, \"i\", 16);\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"Historique\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"a\", 15);\n          i0.ɵɵelement(29, \"i\", 17);\n          i0.ɵɵelementStart(30, \"span\");\n          i0.ɵɵtext(31, \"Favoris\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 9)(33, \"h4\", 10);\n          i0.ɵɵtext(34, \"Aide & Support\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"a\", 15);\n          i0.ɵɵelement(36, \"i\", 18);\n          i0.ɵɵelementStart(37, \"span\");\n          i0.ɵɵtext(38, \"FAQ\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"a\", 15);\n          i0.ɵɵelement(40, \"i\", 19);\n          i0.ɵɵelementStart(41, \"span\");\n          i0.ɵɵtext(42, \"Nous contacter\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(43, \"div\", 20)(44, \"div\", 21)(45, \"div\", 22);\n          i0.ɵɵelement(46, \"i\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 24)(48, \"p\", 25);\n          i0.ɵɵtext(49, \"E-Tourism\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"p\", 26);\n          i0.ɵɵtext(51, \"Version 1.0.0\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(52, \"div\", 27);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_div_click_52_listener() {\n            return ctx.closeSidebar();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"sidebar-open\", ctx.isOpen);\n          i0.ɵɵadvance(52);\n          i0.ɵɵclassProp(\"sidebar-backdrop-visible\", ctx.isOpen);\n        }\n      },\n      dependencies: [i1.RouterLink, i1.RouterLinkActive, i2.MatIconButton],\n      styles: [\".sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 280px;\\n  height: 100vh;\\n  background-color: var(--surface-color);\\n  box-shadow: var(--elevation-4);\\n  z-index: 1000;\\n  display: flex;\\n  flex-direction: column;\\n  transform: translateX(-100%);\\n  transition: transform var(--transition-medium);\\n  padding-top: 64px; \\n\\n}\\n\\n.sidebar-open[_ngcontent-%COMP%] {\\n  transform: translateX(0);\\n}\\n\\n.sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: var(--spacing-md);\\n  border-bottom: 1px solid var(--divider-color);\\n}\\n\\n.sidebar-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n  color: var(--primary-color);\\n}\\n\\n.sidebar-nav[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: var(--spacing-sm) 0;\\n}\\n\\n.sidebar-nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n  padding: var(--spacing-sm) var(--spacing-md);\\n  color: var(--text-primary);\\n  text-decoration: none;\\n  transition: background-color var(--transition-fast);\\n}\\n\\n.sidebar-nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.04);\\n}\\n\\n.sidebar-nav[_ngcontent-%COMP%]   a.active-link[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n  font-weight: 500;\\n}\\n\\n.sidebar-nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n}\\n\\n.sidebar-footer[_ngcontent-%COMP%] {\\n  padding: var(--spacing-md);\\n  border-top: 1px solid var(--divider-color);\\n  text-align: center;\\n}\\n\\n.app-version[_ngcontent-%COMP%] {\\n  color: var(--text-hint);\\n  font-size: 0.75rem;\\n}\\n\\n.sidebar-backdrop[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  z-index: 999;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity var(--transition-medium), visibility var(--transition-medium);\\n}\\n\\n.sidebar-backdrop-visible[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n\\n\\n@media (min-width: 1200px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    transform: translateX(0);\\n  }\\n\\n  .sidebar-backdrop[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "SidebarComponent", "constructor", "isOpen", "sidebarClose", "isMobile", "window", "innerWidth", "closeSidebar", "emit", "closeSidebarOnMobile", "onEscapePress", "selectors", "hostBindings", "SidebarComponent_HostBindings", "rf", "ctx", "i0", "ɵɵresolveDocument", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "SidebarComponent_Template_button_click_7_listener", "SidebarComponent_Template_a_click_13_listener", "SidebarComponent_Template_a_click_17_listener", "SidebarComponent_Template_div_click_52_listener", "ɵɵclassProp", "ɵɵadvance"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\sidebar\\sidebar.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, HostListener } from '@angular/core';\n\n@Component({\n  selector: 'app-sidebar',\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.css']\n})\nexport class SidebarComponent {\n  @Input() isOpen = false;\n  @Output() sidebarClose = new EventEmitter<void>();\n\n  // Détermine si l'écran est en mode mobile\n  private get isMobile(): boolean {\n    return window.innerWidth < 768;\n  }\n\n  // Ferme la sidebar et émet l'événement\n  closeSidebar(): void {\n    this.sidebarClose.emit();\n  }\n\n  // Ferme la sidebar uniquement en mode mobile\n  closeSidebarOnMobile(): void {\n    if (this.isMobile) {\n      this.closeSidebar();\n    }\n  }\n\n  // Ferme la sidebar si l'utilisateur appuie sur la touche Escape\n  @HostListener('document:keydown.escape')\n  onEscapePress(): void {\n    if (this.isOpen) {\n      this.closeSidebar();\n    }\n  }\n}\n", "<div class=\"sidebar\" [class.sidebar-open]=\"isOpen\">\n  <div class=\"sidebar-header\">\n    <div class=\"sidebar-brand\">\n      <div class=\"brand-icon\">\n        <i class=\"fas fa-plane-departure\"></i>\n      </div>\n      <h3 class=\"brand-name\">E-Tourism</h3>\n    </div>\n    <button mat-icon-button class=\"close-button\" (click)=\"closeSidebar()\">\n      <i class=\"fas fa-times\"></i>\n    </button>\n  </div>\n\n  <div class=\"sidebar-nav\">\n    <div class=\"nav-section\">\n      <h4 class=\"nav-section-title\">Menu principal</h4>\n\n      <a class=\"nav-item\" routerLink=\"/accueil\" routerLinkActive=\"active-link\" (click)=\"closeSidebarOnMobile()\">\n        <i class=\"fas fa-home\"></i>\n        <span>Accueil</span>\n      </a>\n\n      <a class=\"nav-item\" routerLink=\"/search-price\" routerLinkActive=\"active-link\" (click)=\"closeSidebarOnMobile()\">\n        <i class=\"fas fa-search\"></i>\n        <span>Rechercher un vol</span>\n      </a>\n    </div>\n\n    <div class=\"nav-section\">\n      <h4 class=\"nav-section-title\">Réservations</h4>\n\n      <a class=\"nav-item disabled\">\n        <i class=\"fas fa-history\"></i>\n        <span>Historique</span>\n      </a>\n\n      <a class=\"nav-item disabled\">\n        <i class=\"fas fa-bookmark\"></i>\n        <span>Favoris</span>\n      </a>\n    </div>\n\n    <div class=\"nav-section\">\n      <h4 class=\"nav-section-title\">Aide & Support</h4>\n\n      <a class=\"nav-item disabled\">\n        <i class=\"fas fa-question-circle\"></i>\n        <span>FAQ</span>\n      </a>\n\n      <a class=\"nav-item disabled\">\n        <i class=\"fas fa-headset\"></i>\n        <span>Nous contacter</span>\n      </a>\n    </div>\n  </div>\n\n  <div class=\"sidebar-footer\">\n    <div class=\"app-info\">\n      <div class=\"app-logo\">\n        <i class=\"fas fa-globe-americas\"></i>\n      </div>\n      <div class=\"app-details\">\n        <p class=\"app-name\">E-Tourism</p>\n        <p class=\"app-version\">Version 1.0.0</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<div class=\"sidebar-backdrop\" [class.sidebar-backdrop-visible]=\"isOpen\" (click)=\"closeSidebar()\"></div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAsB,eAAe;;;;AAOpF,OAAM,MAAOC,gBAAgB;EAL7BC,YAAA;IAMW,KAAAC,MAAM,GAAG,KAAK;IACb,KAAAC,YAAY,GAAG,IAAIJ,YAAY,EAAQ;;EAEjD;EACA,IAAYK,QAAQA,CAAA;IAClB,OAAOC,MAAM,CAACC,UAAU,GAAG,GAAG;EAChC;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,CAACJ,YAAY,CAACK,IAAI,EAAE;EAC1B;EAEA;EACAC,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACL,QAAQ,EAAE;MACjB,IAAI,CAACG,YAAY,EAAE;;EAEvB;EAEA;EAEAG,aAAaA,CAAA;IACX,IAAI,IAAI,CAACR,MAAM,EAAE;MACf,IAAI,CAACK,YAAY,EAAE;;EAEvB;;;uBA3BWP,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAW,SAAA;MAAAC,YAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAAhBC,GAAA,CAAAL,aAAA,EAAe;UAAA,UAAAM,EAAA,CAAAC,iBAAA;;;;;;;;;;;;;;UCP5BD,EAAA,CAAAE,cAAA,aAAmD;UAI3CF,EAAA,CAAAG,SAAA,WAAsC;UACxCH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,cAAA,YAAuB;UAAAF,EAAA,CAAAK,MAAA,gBAAS;UAAAL,EAAA,CAAAI,YAAA,EAAK;UAEvCJ,EAAA,CAAAE,cAAA,gBAAsE;UAAzBF,EAAA,CAAAM,UAAA,mBAAAC,kDAAA;YAAA,OAASR,GAAA,CAAAR,YAAA,EAAc;UAAA,EAAC;UACnES,EAAA,CAAAG,SAAA,WAA4B;UAC9BH,EAAA,CAAAI,YAAA,EAAS;UAGXJ,EAAA,CAAAE,cAAA,aAAyB;UAESF,EAAA,CAAAK,MAAA,sBAAc;UAAAL,EAAA,CAAAI,YAAA,EAAK;UAEjDJ,EAAA,CAAAE,cAAA,aAA0G;UAAjCF,EAAA,CAAAM,UAAA,mBAAAE,8CAAA;YAAA,OAAST,GAAA,CAAAN,oBAAA,EAAsB;UAAA,EAAC;UACvGO,EAAA,CAAAG,SAAA,aAA2B;UAC3BH,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAK,MAAA,eAAO;UAAAL,EAAA,CAAAI,YAAA,EAAO;UAGtBJ,EAAA,CAAAE,cAAA,aAA+G;UAAjCF,EAAA,CAAAM,UAAA,mBAAAG,8CAAA;YAAA,OAASV,GAAA,CAAAN,oBAAA,EAAsB;UAAA,EAAC;UAC5GO,EAAA,CAAAG,SAAA,aAA6B;UAC7BH,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAK,MAAA,yBAAiB;UAAAL,EAAA,CAAAI,YAAA,EAAO;UAIlCJ,EAAA,CAAAE,cAAA,cAAyB;UACOF,EAAA,CAAAK,MAAA,yBAAY;UAAAL,EAAA,CAAAI,YAAA,EAAK;UAE/CJ,EAAA,CAAAE,cAAA,aAA6B;UAC3BF,EAAA,CAAAG,SAAA,aAA8B;UAC9BH,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAK,MAAA,kBAAU;UAAAL,EAAA,CAAAI,YAAA,EAAO;UAGzBJ,EAAA,CAAAE,cAAA,aAA6B;UAC3BF,EAAA,CAAAG,SAAA,aAA+B;UAC/BH,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAK,MAAA,eAAO;UAAAL,EAAA,CAAAI,YAAA,EAAO;UAIxBJ,EAAA,CAAAE,cAAA,cAAyB;UACOF,EAAA,CAAAK,MAAA,sBAAc;UAAAL,EAAA,CAAAI,YAAA,EAAK;UAEjDJ,EAAA,CAAAE,cAAA,aAA6B;UAC3BF,EAAA,CAAAG,SAAA,aAAsC;UACtCH,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAK,MAAA,WAAG;UAAAL,EAAA,CAAAI,YAAA,EAAO;UAGlBJ,EAAA,CAAAE,cAAA,aAA6B;UAC3BF,EAAA,CAAAG,SAAA,aAA8B;UAC9BH,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAK,MAAA,sBAAc;UAAAL,EAAA,CAAAI,YAAA,EAAO;UAKjCJ,EAAA,CAAAE,cAAA,eAA4B;UAGtBF,EAAA,CAAAG,SAAA,aAAqC;UACvCH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,cAAA,eAAyB;UACHF,EAAA,CAAAK,MAAA,iBAAS;UAAAL,EAAA,CAAAI,YAAA,EAAI;UACjCJ,EAAA,CAAAE,cAAA,aAAuB;UAAAF,EAAA,CAAAK,MAAA,qBAAa;UAAAL,EAAA,CAAAI,YAAA,EAAI;UAMhDJ,EAAA,CAAAE,cAAA,eAAiG;UAAzBF,EAAA,CAAAM,UAAA,mBAAAI,gDAAA;YAAA,OAASX,GAAA,CAAAR,YAAA,EAAc;UAAA,EAAC;UAACS,EAAA,CAAAI,YAAA,EAAM;;;UAtElFJ,EAAA,CAAAW,WAAA,iBAAAZ,GAAA,CAAAb,MAAA,CAA6B;UAsEpBc,EAAA,CAAAY,SAAA,IAAyC;UAAzCZ,EAAA,CAAAW,WAAA,6BAAAZ,GAAA,CAAAb,MAAA,CAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}