{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/divider\";\nimport * as i7 from \"@angular/material/menu\";\nfunction NavbarComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"a\", 10);\n    i0.ɵɵelement(2, \"i\", 11);\n    i0.ɵɵtext(3, \" Home \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 12);\n    i0.ɵɵelement(5, \"i\", 13);\n    i0.ɵɵtext(6, \" Search Flights \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NavbarComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"button\", 15)(2, \"span\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-menu\", 18, 19)(7, \"button\", 20);\n    i0.ɵɵelement(8, \"i\", 21);\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 20);\n    i0.ɵɵelement(12, \"i\", 22);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(15, \"mat-divider\");\n    i0.ɵɵelementStart(16, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_div_9_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.logout());\n    });\n    i0.ɵɵelement(17, \"i\", 24);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Logout\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r2 = i0.ɵɵreference(6);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.userName);\n  }\n}\nexport let NavbarComponent = /*#__PURE__*/(() => {\n  class NavbarComponent {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n      this.isLoggedIn = false;\n      this.userName = '';\n    }\n    ngOnInit() {\n      this.authService.isAuthenticated$.subscribe(isAuthenticated => {\n        this.isLoggedIn = isAuthenticated;\n        if (isAuthenticated) {\n          const userInfo = this.authService.getUserInfo();\n          this.userName = userInfo?.username || 'User';\n        }\n      });\n    }\n    logout() {\n      this.authService.logout();\n      this.router.navigate(['/login']);\n    }\n    static {\n      this.ɵfac = function NavbarComponent_Factory(t) {\n        return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NavbarComponent,\n        selectors: [[\"app-navbar\"]],\n        decls: 10,\n        vars: 2,\n        consts: [[\"color\", \"primary\", 1, \"navbar\"], [1, \"navbar-container\"], [1, \"navbar-brand\"], [\"routerLink\", \"/accueil\", 1, \"brand-link\"], [1, \"brand-icon\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"brand-name\"], [\"class\", \"navbar-links\", 4, \"ngIf\"], [\"class\", \"navbar-actions\", 4, \"ngIf\"], [1, \"navbar-links\"], [\"mat-button\", \"\", \"routerLink\", \"/accueil\", \"routerLinkActive\", \"active-link\"], [1, \"fas\", \"fa-home\"], [\"mat-button\", \"\", \"routerLink\", \"/search-price\", \"routerLinkActive\", \"active-link\"], [1, \"fas\", \"fa-search\"], [1, \"navbar-actions\"], [\"mat-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"user-name\"], [1, \"fas\", \"fa-user-circle\"], [\"xPosition\", \"before\", 1, \"user-menu\"], [\"userMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", \"disabled\", \"\"], [1, \"fas\", \"fa-user\"], [1, \"fas\", \"fa-cog\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"]],\n        template: function NavbarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"mat-toolbar\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"a\", 3)(4, \"span\", 4);\n            i0.ɵɵelement(5, \"i\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"span\", 6);\n            i0.ɵɵtext(7, \"E-Tourism\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(8, NavbarComponent_div_8_Template, 7, 0, \"div\", 7);\n            i0.ɵɵtemplate(9, NavbarComponent_div_9_Template, 20, 2, \"div\", 8);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n          }\n        },\n        dependencies: [i3.NgIf, i2.RouterLink, i2.RouterLinkActive, i4.MatAnchor, i4.MatButton, i5.MatToolbar, i6.MatDivider, i7.MatMenu, i7.MatMenuItem, i7.MatMenuTrigger],\n        styles: [\".navbar[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;z-index:1000;height:64px;padding:0;background:linear-gradient(135deg,var(--primary-color),var(--primary-dark));color:#fff;box-shadow:var(--elevation-2)}.navbar-container[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%;height:100%;padding:0 var(--spacing-md)}.navbar-brand[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-sm)}.brand-link[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-sm);color:#fff;text-decoration:none;font-weight:600;font-size:1.25rem}.brand-icon[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-size:1.5rem}.brand-name[_ngcontent-%COMP%]{letter-spacing:.5px}.navbar-links[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-sm)}.navbar-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff;opacity:.9;font-weight:500;display:flex;align-items:center;gap:var(--spacing-xs);padding:0 var(--spacing-sm);height:36px;border-radius:var(--border-radius-small);transition:background-color var(--transition-fast)}.navbar-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;opacity:1}.navbar-links[_ngcontent-%COMP%]   a.active-link[_ngcontent-%COMP%]{background-color:#fff3;opacity:1}.navbar-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.navbar-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-md)}.user-menu-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--spacing-sm);color:#fff;font-weight:500}.user-menu-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem}.user-name[_ngcontent-%COMP%]{display:inline-block;max-width:150px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-menu-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:var(--spacing-sm);font-size:1.1rem;width:24px;height:24px;line-height:24px;text-align:center;color:var(--primary-color)}@media (max-width: 768px){.navbar-links[_ngcontent-%COMP%]{display:none}}@media (max-width: 600px){.brand-name[_ngcontent-%COMP%]{display:none}.user-name[_ngcontent-%COMP%]{max-width:80px}}\"]\n      });\n    }\n  }\n  return NavbarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}