import { FlightBaggageGetOption, FlightClassType, LocationType, ProductType } from './enums.model';

export interface RoundTripRequest {
  ProductType: ProductType;
  ServiceTypes: string[];
  CheckIn: string;
  Night?: number; // Nombre de nuits (pour aller-retour)
  ReturnDate?: string; // Date de retour (alternative à Night)
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
  Passengers: Passenger[];
  showOnlyNonStopFlight: boolean;
  additionalParameters?: AdditionalParameters;
  acceptPendingProviders: boolean;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  calculateFlightFees: boolean;
  flightClasses: FlightClassType[];
  Culture: string;
  Currency: string;
}

export interface Location {
  id: string;
  type: LocationType;
}

export interface Passenger {
  type: number;
  count: number;
}

export interface AdditionalParameters {
  getOptionsParameters?: GetOptionsParameters;
  CorporateCodes?: CorporateCode[];
}

export interface GetOptionsParameters {
  flightBaggageGetOption: FlightBaggageGetOption;
}

export interface CorporateCode {
  Code: string;
  Rule: Rule;
}

export interface Rule {
  Airline: string;
  Supplier: string;
}
