import { BeginTransactionRequest } from './begin-transaction-request.model';
import { CommitTransactionRequest } from './commit-transaction-request.model';
import { SetReservationInfoRequest } from './set-reservation-info-request.model';

/**
 * Main wrapper model for all booking transaction requests
 * Used for the /booking-transaction endpoint with action parameter
 */
export interface BookingTransactionRequest {
    action: 'begin' | 'info' | 'commit';
    beginRequest?: BeginTransactionRequest;
    infoRequest?: SetReservationInfoRequest;
    commitRequest?: CommitTransactionRequest;
}
