{"ast": null, "code": "import { FlightClassType, PassengerType } from 'src/app/models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/services/product.service\";\nimport * as i3 from \"src/app/services/booking.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nfunction GetOfferComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading flight options...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GetOfferComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"p\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.errorMessage);\n  }\n}\nfunction GetOfferComponent_div_8_div_26_div_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"div\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const feature_r11 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r9.getFeatureIcon(feature_r11.serviceGroup));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r11.commercialName);\n  }\n}\nfunction GetOfferComponent_div_8_div_26_div_1_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const baggage_r13 = ctx.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r12.getBaggageIcon(baggage_r13.baggageType));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r12.getBaggageTypeName(baggage_r13.baggageType), \": \", baggage_r13.piece > 0 ? baggage_r13.piece + \" piece(s)\" : \"\", \" \", baggage_r13.weight > 0 ? baggage_r13.weight + \" \" + ctx_r12.getUnitTypeName(baggage_r13.unitType) : \"\", \" \");\n  }\n}\nfunction GetOfferComponent_div_8_div_26_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, GetOfferComponent_div_8_div_26_div_1_div_15_div_1_Template, 4, 5, \"div\", 45);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const offer_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, offer_r8.baggageInformations, 0, 2));\n  }\n}\nfunction GetOfferComponent_div_8_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_div_26_div_1_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const offer_r8 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r15.selectOffer(offer_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 30)(3, \"span\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 33);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 34);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 35);\n    i0.ɵɵtemplate(13, GetOfferComponent_div_8_div_26_div_1_div_13_Template, 6, 3, \"div\", 36);\n    i0.ɵɵpipe(14, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, GetOfferComponent_div_8_div_26_div_1_div_15_Template, 3, 5, \"div\", 37);\n    i0.ɵɵelementStart(16, \"div\", 38)(17, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_div_26_div_1_Template_button_click_17_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const offer_r8 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      ctx_r17.bookFlight(offer_r8);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(18, \" Select This Fare \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const offer_r8 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", ctx_r7.isSelected(offer_r8));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"business\", ctx_r7.isBusinessClass(offer_r8));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getOfferClassName(offer_r8), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.getOfferClassCode(offer_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 10, offer_r8.price.amount, offer_r8.price.currency), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", offer_r8.flightBrandInfo && offer_r8.flightBrandInfo.name || \"Standard\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(14, 13, ctx_r7.getImportantFeatures(offer_r8), 0, 3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", offer_r8.baggageInformations && offer_r8.baggageInformations.length > 0);\n  }\n}\nfunction GetOfferComponent_div_8_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, GetOfferComponent_div_8_div_26_div_1_Template, 19, 17, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getFilteredOffers());\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r25 = ctx.$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r20.getBaggageTypeName(baggage_r25.baggageType), \": \", baggage_r25.piece > 0 ? baggage_r25.piece + \" piece(s)\" : \"\", \" \");\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 55);\n    i0.ɵɵtext(2, \" WiFi\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵtext(2, \" Meals\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 57);\n    i0.ɵɵtext(2, \" Refundable\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵtext(2, \" Changeable\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_div_27_tr_17_Template_tr_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const offer_r19 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r26.selectOffer(offer_r19));\n    });\n    i0.ɵɵelementStart(1, \"td\", 50)(2, \"div\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 32);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtemplate(9, GetOfferComponent_div_8_div_27_tr_17_div_9_Template, 2, 2, \"div\", 51);\n    i0.ɵɵpipe(10, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"div\", 52);\n    i0.ɵɵtemplate(13, GetOfferComponent_div_8_div_27_tr_17_span_13_Template, 3, 0, \"span\", 53);\n    i0.ɵɵtemplate(14, GetOfferComponent_div_8_div_27_tr_17_span_14_Template, 3, 0, \"span\", 53);\n    i0.ɵɵtemplate(15, GetOfferComponent_div_8_div_27_tr_17_span_15_Template, 3, 0, \"span\", 53);\n    i0.ɵɵtemplate(16, GetOfferComponent_div_8_div_27_tr_17_span_16_Template, 3, 0, \"span\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"td\", 54);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\")(21, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_div_27_tr_17_Template_button_click_21_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const offer_r19 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      ctx_r28.bookFlight(offer_r19);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(22, \" Select \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const offer_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", ctx_r18.isSelected(offer_r19));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"business\", ctx_r18.isBusinessClass(offer_r19));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.getOfferClassName(offer_r19), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r18.getOfferClassCode(offer_r19));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(offer_r19.flightBrandInfo && offer_r19.flightBrandInfo.name || \"Standard\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(10, 13, offer_r19.baggageInformations, 0, 2));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.hasFeature(offer_r19, \"wifi\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.hasFeature(offer_r19, \"meal\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.hasFeature(offer_r19, \"refund\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.hasFeature(offer_r19, \"rebook\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(19, 17, offer_r19.price.amount, offer_r19.price.currency));\n  }\n}\nfunction GetOfferComponent_div_8_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"table\", 48)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Brand\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Features\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Price\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Action\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, GetOfferComponent_div_8_div_27_tr_17_Template, 23, 20, \"tr\", 49);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.getFilteredOffers());\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_37_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 90);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 91);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementStart(5, \"span\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const classInfo_r33 = ctx.$implicit;\n    const ctx_r32 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(classInfo_r33.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r32.getFlightClassTypeName(classInfo_r33.type), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", classInfo_r33.code, \")\");\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87);\n    i0.ɵɵelement(2, \"i\", 88);\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Class Details\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 89);\n    i0.ɵɵtemplate(6, GetOfferComponent_div_8_div_28_div_37_div_6_Template, 7, 3, \"div\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r29.selectedOffer.flightClassInformations);\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_38_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98);\n    i0.ɵɵelement(2, \"i\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 99)(4, \"div\", 100);\n    i0.ɵɵtext(5, \"Checked Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 101);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const baggage_r36 = ctx.$implicit;\n    const ctx_r34 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\" \", baggage_r36.piece > 0 ? baggage_r36.piece + \" piece(s)\" : \"\", \" \", baggage_r36.weight > 0 ? baggage_r36.weight + \" \" + ctx_r34.getUnitTypeName(baggage_r36.unitType) : \"\", \" \");\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_38_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"div\", 98);\n    i0.ɵɵelement(2, \"i\", 103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 99)(4, \"div\", 100);\n    i0.ɵɵtext(5, \"Cabin Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 101);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const baggage_r37 = ctx.$implicit;\n    const ctx_r35 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\" \", baggage_r37.piece > 0 ? baggage_r37.piece + \" piece(s)\" : \"\", \" \", baggage_r37.weight > 0 ? baggage_r37.weight + \" \" + ctx_r35.getUnitTypeName(baggage_r37.unitType) : \"\", \" \");\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 87);\n    i0.ɵɵelement(2, \"i\", 93);\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Baggage Allowance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 94);\n    i0.ɵɵtemplate(6, GetOfferComponent_div_8_div_28_div_38_div_6_Template, 8, 2, \"div\", 95);\n    i0.ɵɵpipe(7, \"slice\");\n    i0.ɵɵtemplate(8, GetOfferComponent_div_8_div_28_div_38_div_8_Template, 8, 2, \"div\", 96);\n    i0.ɵɵpipe(9, \"slice\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(7, 2, ctx_r30.selectedOffer.baggageInformations, 0, 1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(9, 6, ctx_r30.selectedOffer.baggageInformations, 1, 2));\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_39_div_6_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r39 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", feature_r39.explanations[0].text, \" \");\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_39_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"div\", 109);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 110)(4, \"div\", 111);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, GetOfferComponent_div_8_div_28_div_39_div_6_div_6_Template, 2, 1, \"div\", 112);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r39 = ctx.$implicit;\n    const ctx_r38 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r38.getFeatureIcon(feature_r39.serviceGroup));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r39.commercialName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", feature_r39.explanations && feature_r39.explanations.length > 0);\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104)(1, \"div\", 87);\n    i0.ɵɵelement(2, \"i\", 105);\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Included Services\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 106);\n    i0.ɵɵtemplate(6, GetOfferComponent_div_8_div_28_div_39_div_6_Template, 7, 4, \"div\", 107);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r31.getImportantFeatures(ctx_r31.selectedOffer));\n  }\n}\nfunction GetOfferComponent_div_8_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"div\", 61)(3, \"h3\");\n    i0.ɵɵtext(4, \"Selected Fare Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 62)(6, \"span\", 63);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"currency\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 64);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"div\", 66)(13, \"div\", 67)(14, \"div\", 68);\n    i0.ɵɵelement(15, \"i\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 70)(17, \"div\", 71);\n    i0.ɵɵtext(18, \"Flight\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 72);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 73)(22, \"div\", 68);\n    i0.ɵɵelement(23, \"i\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 70)(25, \"div\", 71);\n    i0.ɵɵtext(26, \"Brand\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 72);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 75)(30, \"div\", 68);\n    i0.ɵɵelement(31, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 70)(33, \"div\", 71);\n    i0.ɵɵtext(34, \"Passenger\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 72);\n    i0.ɵɵtext(36, \"1 Adult\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(37, GetOfferComponent_div_8_div_28_div_37_Template, 7, 1, \"div\", 77);\n    i0.ɵɵtemplate(38, GetOfferComponent_div_8_div_28_div_38_Template, 10, 10, \"div\", 78);\n    i0.ɵɵtemplate(39, GetOfferComponent_div_8_div_28_div_39_Template, 7, 1, \"div\", 79);\n    i0.ɵɵelementStart(40, \"div\", 80)(41, \"div\", 81)(42, \"div\", 82);\n    i0.ɵɵelement(43, \"span\", 83)(44, \"span\", 83)(45, \"span\", 83)(46, \"span\", 83)(47, \"span\", 83)(48, \"span\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_div_28_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r42.bookFlight(ctx_r42.selectedOffer));\n    });\n    i0.ɵɵelement(50, \"i\", 85);\n    i0.ɵɵtext(51, \" Book This Flight \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 9, ctx_r6.selectedOffer.price.amount, ctx_r6.selectedOffer.price.currency));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"business\", ctx_r6.isBusinessClass(ctx_r6.selectedOffer));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getOfferClassName(ctx_r6.selectedOffer), \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r6.selectedOffer.flightId);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r6.selectedOffer.flightBrandInfo && ctx_r6.selectedOffer.flightBrandInfo.name || \"Flexible\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectedOffer.flightClassInformations && ctx_r6.selectedOffer.flightClassInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectedOffer.baggageInformations && ctx_r6.selectedOffer.baggageInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectedOffer.flightBrandInfo && ctx_r6.selectedOffer.flightBrandInfo.features && ctx_r6.selectedOffer.flightBrandInfo.features.length > 0);\n  }\n}\nfunction GetOfferComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13)(3, \"div\", 14)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵtext(7, \"Istanbul (IST) \\u2192 Tunis (TUN)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 16)(9, \"div\", 17)(10, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.setViewMode(\"cards\"));\n    });\n    i0.ɵɵelement(11, \"i\", 19);\n    i0.ɵɵtext(12, \" Cards \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.setViewMode(\"table\"));\n    });\n    i0.ɵɵelement(14, \"i\", 20);\n    i0.ɵɵtext(15, \" Table \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 21)(17, \"span\", 22);\n    i0.ɵɵtext(18, \"Filter by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 23)(20, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.filterByClass(\"all\"));\n    });\n    i0.ɵɵtext(21, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.filterByClass(\"economy\"));\n    });\n    i0.ɵɵtext(23, \"Economy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.filterByClass(\"business\"));\n    });\n    i0.ɵɵtext(25, \"Business\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(26, GetOfferComponent_div_8_div_26_Template, 2, 1, \"div\", 24);\n    i0.ɵɵtemplate(27, GetOfferComponent_div_8_div_27_Template, 18, 1, \"div\", 25);\n    i0.ɵɵtemplate(28, GetOfferComponent_div_8_div_28_Template, 52, 12, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.offers[0].flightId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r2.viewMode === \"cards\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r2.viewMode === \"table\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"active\", ctx_r2.selectedClassFilter === \"all\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r2.selectedClassFilter === \"economy\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r2.selectedClassFilter === \"business\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.viewMode === \"cards\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.viewMode === \"table\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedOffer);\n  }\n}\nfunction GetOfferComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114)(1, \"p\");\n    i0.ɵɵtext(2, \"No flight options available for the selected criteria.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class GetOfferComponent {\n  constructor(route, router, productService, bookingService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.productService = productService;\n    this.bookingService = bookingService;\n    this.snackBar = snackBar;\n    // Paramètres de requête\n    this.searchId = '';\n    this.offerId = '';\n    // Données\n    this.offers = [];\n    this.selectedOffer = null;\n    // État de l'interface\n    this.isLoading = false;\n    this.errorMessage = '';\n    this.viewMode = 'cards';\n    this.selectedClassFilter = 'all';\n  }\n  ngOnInit() {\n    this.route.queryParams.subscribe(params => {\n      this.searchId = params['searchId'];\n      this.offerId = params['offerId'];\n      console.log('GetOfferComponent received params:', params);\n      console.log('SearchId:', this.searchId);\n      console.log('OfferId:', this.offerId);\n      // Vérifier si les deux paramètres sont présents\n      const missingParams = [];\n      if (!this.searchId) missingParams.push('searchId');\n      if (!this.offerId) missingParams.push('offerId');\n      if (missingParams.length === 0) {\n        // Tous les paramètres sont présents\n        this.loadOfferDetails();\n      } else {\n        // Paramètres manquants\n        this.errorMessage = `Missing parameters: ${missingParams.join(', ')}. Please go back and try again.`;\n        console.error('Missing parameters:', {\n          searchId: this.searchId,\n          offerId: this.offerId\n        });\n        // Essayer de récupérer les paramètres manquants de l'URL\n        const url = window.location.href;\n        console.log('Current URL:', url);\n        // Analyser l'URL pour trouver des paramètres potentiellement mal formatés\n        const urlParams = new URLSearchParams(window.location.search);\n        const paramsObj = {};\n        urlParams.forEach((value, key) => {\n          paramsObj[key] = value;\n        });\n        console.log('All URL params:', paramsObj);\n      }\n    });\n  }\n  loadOfferDetails() {\n    this.isLoading = true;\n    this.errorMessage = '';\n    const request = this.productService.createDefaultGetOffersRequest(this.searchId, [this.offerId]);\n    console.log('Sending GetOffersRequest:', request);\n    this.productService.getOffers(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        console.log('GetOffers response:', response);\n        if (response.header.success && response.body.offers && response.body.offers.length > 0) {\n          console.log('Offers found:', response.body.offers);\n          this.offers = response.body.offers;\n          // Sélectionner la première offre par défaut\n          if (this.offers.length > 0) {\n            this.selectedOffer = this.offers[0];\n          }\n          // Trier les offres par prix (du moins cher au plus cher)\n          this.sortOffersByPrice();\n        } else {\n          console.error('No offers found in response or response not successful');\n          console.log('Response header success:', response.header.success);\n          console.log('Response body offers:', response.body.offers);\n          this.errorMessage = 'No flight options found.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            console.log('Error message from API:', response.header.messages[0].message);\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred while loading flight options. Please try again.';\n        console.error('Error getting offers:', error);\n        // Afficher plus de détails sur l'erreur\n        if (error.error) {\n          console.log('Error details:', error.error);\n        }\n        if (error.message) {\n          console.log('Error message:', error.message);\n        }\n        if (error.status) {\n          console.log('Error status:', error.status);\n        }\n      }\n    });\n  }\n  // Méthodes de tri et de filtrage\n  sortOffersByPrice() {\n    this.offers.sort((a, b) => a.price.amount - b.price.amount);\n  }\n  getFilteredOffers() {\n    if (this.selectedClassFilter === 'all') {\n      return this.offers;\n    }\n    return this.offers.filter(offer => {\n      const isBusinessClass = this.isBusinessClass(offer);\n      return this.selectedClassFilter === 'business' && isBusinessClass || this.selectedClassFilter === 'economy' && !isBusinessClass;\n    });\n  }\n  filterByClass(classType) {\n    this.selectedClassFilter = classType;\n  }\n  setViewMode(mode) {\n    this.viewMode = mode;\n  }\n  // Méthodes de sélection\n  selectOffer(offer) {\n    this.selectedOffer = offer;\n  }\n  isSelected(offer) {\n    return this.selectedOffer === offer;\n  }\n  goBack() {\n    this.router.navigate(['/search-price']);\n  }\n  bookFlight(offer) {\n    console.log('Starting booking transaction with offer ID:', offer.offerId);\n    // Afficher un indicateur de chargement\n    this.isLoading = true;\n    this.errorMessage = '';\n    // Démarrer la transaction automatiquement\n    this.bookingService.beginTransaction([offer.offerId]).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response && response.beginResponse && response.beginResponse.body) {\n          const transactionId = response.beginResponse.body.transactionId;\n          console.log('Transaction started successfully with ID:', transactionId);\n          // Naviguer vers la page de réservation avec le transactionId\n          // Cela permettra au composant booking-transaction de sauter l'étape 1\n          this.router.navigate(['/booking-transaction'], {\n            queryParams: {\n              offerIds: offer.offerId,\n              transactionId: transactionId,\n              searchId: this.searchId,\n              autoStarted: 'true'\n            }\n          });\n          // Afficher un message de succès\n          this.snackBar.open('Réservation initiée avec succès', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = 'Erreur lors du démarrage de la transaction. Veuillez réessayer.';\n          console.error('Invalid response from beginTransaction:', response);\n          // Afficher un message d'erreur\n          this.snackBar.open(this.errorMessage, 'Fermer', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'Une erreur est survenue lors du démarrage de la réservation. Veuillez réessayer.';\n        console.error('Error starting transaction:', error);\n        // Afficher un message d'erreur\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n        // En cas d'erreur, rediriger vers la page de réservation traditionnelle\n        this.router.navigate(['/booking-transaction'], {\n          queryParams: {\n            offerIds: offer.offerId,\n            searchId: this.searchId\n          }\n        });\n      }\n    });\n  }\n  // Méthodes d'analyse des offres\n  isBusinessClass(offer) {\n    if (!offer.flightClassInformations || offer.flightClassInformations.length === 0) {\n      return false;\n    }\n    return offer.flightClassInformations.some(classInfo => classInfo.type === FlightClassType.BUSINESS);\n  }\n  getOfferClassName(offer) {\n    if (!offer.flightClassInformations || offer.flightClassInformations.length === 0) {\n      return 'Unknown';\n    }\n    return offer.flightClassInformations[0].name;\n  }\n  getOfferClassCode(offer) {\n    if (!offer.flightClassInformations || offer.flightClassInformations.length === 0) {\n      return '';\n    }\n    return offer.flightClassInformations[0].code;\n  }\n  // Méthodes pour les caractéristiques des offres\n  hasFeature(offer, featureName) {\n    if (!offer.flightBrandInfo || !offer.flightBrandInfo.features) {\n      return false;\n    }\n    return offer.flightBrandInfo.features.some(feature => feature.commercialName.toLowerCase().includes(featureName.toLowerCase()));\n  }\n  getImportantFeatures(offer) {\n    if (!offer || !offer.flightBrandInfo || !offer.flightBrandInfo.features) {\n      return [];\n    }\n    // Filtrer les caractéristiques pour n'afficher que les plus importantes\n    // et éviter les doublons\n    const uniqueFeatures = new Map();\n    offer.flightBrandInfo.features.forEach(feature => {\n      // Ne pas ajouter de doublons basés sur le nom commercial\n      if (!uniqueFeatures.has(feature.commercialName)) {\n        uniqueFeatures.set(feature.commercialName, feature);\n      }\n    });\n    // Convertir la Map en tableau et limiter à 6 caractéristiques maximum\n    return Array.from(uniqueFeatures.values()).slice(0, 6);\n  }\n  // Méthodes utilitaires pour afficher des valeurs lisibles\n  getProviderName(providerCode) {\n    const providers = {\n      1: 'Provider A',\n      2: 'Provider B',\n      3: 'Provider C'\n    };\n    return providers[providerCode] || `Provider ${providerCode}`;\n  }\n  getFlightClassTypeName(typeCode) {\n    switch (typeCode) {\n      case FlightClassType.PROMO:\n        return 'Promo';\n      case FlightClassType.ECONOMY:\n        return 'Economy';\n      case FlightClassType.BUSINESS:\n        return 'Business';\n      default:\n        return `Class Type ${typeCode}`;\n    }\n  }\n  getPassengerTypeName(typeCode) {\n    switch (typeCode) {\n      case PassengerType.Adult:\n        return 'Adult';\n      case PassengerType.Child:\n        return 'Child';\n      case PassengerType.Infant:\n        return 'Infant';\n      default:\n        return `Passenger Type ${typeCode}`;\n    }\n  }\n  getUnitTypeName(unitTypeCode) {\n    const unitTypes = {\n      1: 'kg',\n      2: 'lb'\n    };\n    return unitTypes[unitTypeCode] || '';\n  }\n  getBaggageTypeName(baggageTypeCode) {\n    const baggageTypes = {\n      1: 'Checked Baggage',\n      2: 'Cabin Baggage',\n      3: 'Hand Baggage'\n    };\n    return baggageTypes[baggageTypeCode] || `Baggage Type ${baggageTypeCode}`;\n  }\n  // Méthodes pour les icônes\n  getBaggageIcon(baggageTypeCode) {\n    const icons = {\n      1: 'fas fa-suitcase',\n      2: 'fas fa-briefcase',\n      3: 'fas fa-shopping-bag'\n    };\n    return icons[baggageTypeCode] || 'fas fa-luggage-cart';\n  }\n  getFeatureIcon(serviceGroupCode) {\n    const icons = {\n      0: 'fas fa-star',\n      1: 'fas fa-suitcase',\n      2: 'fas fa-utensils',\n      3: 'fas fa-wifi',\n      4: 'fas fa-couch',\n      5: 'fas fa-hamburger' // Meals\n    };\n\n    return icons[serviceGroupCode] || 'fas fa-star';\n  }\n  static {\n    this.ɵfac = function GetOfferComponent_Factory(t) {\n      return new (t || GetOfferComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.BookingService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GetOfferComponent,\n      selectors: [[\"app-get-offer\"]],\n      decls: 10,\n      vars: 4,\n      consts: [[1, \"get-offer-container\"], [1, \"offer-header\"], [1, \"back-button\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"offers-container\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"spinner\", \"large\"], [1, \"error-container\"], [1, \"error-message\"], [1, \"offers-container\"], [1, \"flight-summary-card\"], [1, \"flight-summary-header\"], [1, \"flight-info\"], [1, \"flight-route\"], [1, \"view-controls\"], [1, \"view-toggle\"], [3, \"click\"], [1, \"fas\", \"fa-th-large\"], [1, \"fas\", \"fa-table\"], [1, \"filter-controls\"], [1, \"filter-label\"], [1, \"filter-buttons\"], [\"class\", \"offers-grid\", 4, \"ngIf\"], [\"class\", \"offers-table-container\", 4, \"ngIf\"], [\"class\", \"selected-offer-details\", 4, \"ngIf\"], [1, \"offers-grid\"], [\"class\", \"offer-card\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"offer-card\", 3, \"click\"], [1, \"offer-class\"], [1, \"class-badge\"], [1, \"class-code\"], [1, \"offer-price\"], [1, \"offer-brand\"], [1, \"offer-features\"], [\"class\", \"feature-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"offer-baggage\", 4, \"ngIf\"], [1, \"offer-actions\"], [1, \"book-button\", 3, \"click\"], [1, \"feature-item\"], [1, \"feature-icon\"], [1, \"feature-content\"], [1, \"feature-name\"], [1, \"offer-baggage\"], [\"class\", \"baggage-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"baggage-item\"], [1, \"offers-table-container\"], [1, \"offers-table\"], [3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"class-cell\"], [4, \"ngFor\", \"ngForOf\"], [1, \"table-features\"], [4, \"ngIf\"], [1, \"price-cell\"], [1, \"fas\", \"fa-wifi\"], [1, \"fas\", \"fa-utensils\"], [1, \"fas\", \"fa-undo\"], [1, \"fas\", \"fa-exchange-alt\"], [1, \"selected-offer-details\"], [1, \"details-header\"], [1, \"details-header-content\"], [1, \"selected-price\"], [1, \"price-amount\"], [1, \"selected-class-badge\"], [1, \"details-content\"], [1, \"info-cards\"], [1, \"info-card\", \"flight-card\"], [1, \"info-card-icon\"], [1, \"fas\", \"fa-plane\"], [1, \"info-card-content\"], [1, \"info-card-label\"], [1, \"info-card-value\"], [1, \"info-card\", \"brand-card\"], [1, \"fas\", \"fa-tag\"], [1, \"info-card\", \"passenger-card\"], [1, \"fas\", \"fa-user\"], [\"class\", \"details-section class-details\", 4, \"ngIf\"], [\"class\", \"details-section baggage-section\", 4, \"ngIf\"], [\"class\", \"details-section services-section\", 4, \"ngIf\"], [1, \"booking-section\"], [1, \"button-container\"], [1, \"particles-container\"], [1, \"particle\"], [1, \"book-flight-button\", \"animate-pulse\", 3, \"click\"], [1, \"fas\", \"fa-check-circle\"], [1, \"details-section\", \"class-details\"], [1, \"section-header\"], [1, \"fas\", \"fa-chair\"], [1, \"class-details-content\"], [1, \"class-name\"], [1, \"class-type\"], [1, \"details-section\", \"baggage-section\"], [1, \"fas\", \"fa-suitcase\"], [1, \"baggage-cards\"], [\"class\", \"baggage-card checked\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"baggage-card cabin\", 4, \"ngFor\", \"ngForOf\"], [1, \"baggage-card\", \"checked\"], [1, \"baggage-icon\"], [1, \"baggage-details\"], [1, \"baggage-type\"], [1, \"baggage-value\"], [1, \"baggage-card\", \"cabin\"], [1, \"fas\", \"fa-briefcase\"], [1, \"details-section\", \"services-section\"], [1, \"fas\", \"fa-concierge-bell\"], [1, \"services-grid\"], [\"class\", \"service-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"service-item\"], [1, \"service-icon\"], [1, \"service-details\"], [1, \"service-name\"], [\"class\", \"service-description\", 4, \"ngIf\"], [1, \"service-description\"], [1, \"no-results\"]],\n      template: function GetOfferComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Flight Options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function GetOfferComponent_Template_button_click_4_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(5, \"Back to Search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, GetOfferComponent_div_6_Template, 4, 0, \"div\", 3);\n          i0.ɵɵtemplate(7, GetOfferComponent_div_7_Template, 3, 1, \"div\", 4);\n          i0.ɵɵtemplate(8, GetOfferComponent_div_8_Template, 29, 14, \"div\", 5);\n          i0.ɵɵtemplate(9, GetOfferComponent_div_9_Template, 3, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.offers && ctx.offers.length > 0 && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.offers && ctx.offers.length === 0 && !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i5.SlicePipe, i5.CurrencyPipe],\n      styles: [\".get-offer-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  font-family: 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\\n}\\n\\n.offer-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.offer-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #2989d8;\\n  margin: 0;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  background-color: #f0f0f0;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 8px 16px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n  color: #333;\\n  transition: background-color 0.3s;\\n}\\n\\n.back-button[_ngcontent-%COMP%]:hover {\\n  background-color: #e0e0e0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border: 3px solid rgba(41, 137, 216, 0.3);\\n  border-radius: 50%;\\n  border-top-color: #2989d8;\\n  animation: _ngcontent-%COMP%_spin 1s ease-in-out infinite;\\n}\\n\\n.spinner.large[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-width: 4px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background-color: #fdecea;\\n  border-radius: 5px;\\n  text-align: center;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n  margin: 0;\\n}\\n\\n\\n\\n.offers-container[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n\\n\\n.flight-summary-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 20px;\\n  overflow: hidden;\\n}\\n\\n.flight-summary-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #2989d8, #1e5799);\\n  color: white;\\n}\\n\\n.flight-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  margin: 0 0 5px 0;\\n  font-weight: 600;\\n}\\n\\n.flight-route[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  opacity: 0.9;\\n}\\n\\n.view-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-end;\\n  gap: 10px;\\n}\\n\\n.view-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  background-color: rgba(255, 255, 255, 0.2);\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n\\n.view-toggle[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: white;\\n  padding: 8px 12px;\\n  font-size: 14px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  transition: background-color 0.2s;\\n}\\n\\n.view-toggle[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.3);\\n  font-weight: 500;\\n}\\n\\n.view-toggle[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover:not(.active) {\\n  background-color: rgba(255, 255, 255, 0.15);\\n}\\n\\n.filter-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.filter-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n\\n.filter-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n}\\n\\n.filter-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n  border: none;\\n  border-radius: 4px;\\n  padding: 5px 10px;\\n  font-size: 13px;\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n\\n.filter-buttons[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%] {\\n  background-color: white;\\n  color: #2989d8;\\n  font-weight: 500;\\n}\\n\\n.filter-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover:not(.active) {\\n  background-color: rgba(255, 255, 255, 0.3);\\n}\\n\\n\\n\\n.offers-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.offer-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n  transition: transform 0.2s, box-shadow 0.2s;\\n  cursor: pointer;\\n  position: relative;\\n  border: 2px solid transparent;\\n}\\n\\n.offer-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n.offer-card.selected[_ngcontent-%COMP%] {\\n  border-color: #2989d8;\\n  box-shadow: 0 0 0 2px rgba(41, 137, 216, 0.2);\\n}\\n\\n.offer-section[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.offer-section[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.offer-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2989d8;\\n  margin-top: 0;\\n  margin-bottom: 15px;\\n  font-size: 18px;\\n}\\n\\n.offer-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #555;\\n  margin-top: 15px;\\n  margin-bottom: 10px;\\n  font-size: 16px;\\n}\\n\\n.info-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 8px;\\n}\\n\\n.info-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  flex: 0 0 150px;\\n  font-weight: 500;\\n  color: #666;\\n}\\n\\n.info-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: #333;\\n}\\n\\n.price-display[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.price-amount[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #2989d8;\\n}\\n\\n.class-info[_ngcontent-%COMP%], .baggage-info[_ngcontent-%COMP%], .feature[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-radius: 4px;\\n  padding: 12px;\\n  margin-bottom: 10px;\\n}\\n\\n.class-info[_ngcontent-%COMP%]:last-child, .baggage-info[_ngcontent-%COMP%]:last-child, .feature[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.offer-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.offer-class[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.class-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 4px 8px;\\n  background-color: #e8f4fd;\\n  color: #2989d8;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n\\n.class-badge.business[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #ffa000;\\n}\\n\\n.class-code[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n.offer-price[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: #2989d8;\\n}\\n\\n.offer-brand[_ngcontent-%COMP%] {\\n  padding: 10px 15px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #333;\\n  background-color: #f8f9fa;\\n}\\n\\n.offer-features[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n\\n.feature-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px 12px;\\n  background-color: #f1f3f4;\\n  border-radius: 16px;\\n  font-size: 13px;\\n  color: #555;\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  color: #2989d8;\\n  font-size: 14px;\\n}\\n\\n.offer-baggage[_ngcontent-%COMP%] {\\n  padding: 0 15px 15px;\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n.baggage-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 5px;\\n}\\n\\n.baggage-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #2989d8;\\n}\\n\\n.offer-actions[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  text-align: center;\\n  border-top: 1px solid #eee;\\n}\\n\\n.book-button[_ngcontent-%COMP%] {\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 10px 20px;\\n  font-size: 15px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.3s;\\n  width: 100%;\\n  display: inline-block;\\n  text-align: center;\\n  text-decoration: none;\\n  box-sizing: border-box;\\n}\\n\\n.book-button[_ngcontent-%COMP%]:hover {\\n  background-color: #1e5799;\\n  color: white;\\n  text-decoration: none;\\n}\\n\\n\\n\\n.offers-table-container[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n  margin-bottom: 20px;\\n}\\n\\n.offers-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.offers-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .offers-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  text-align: left;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.offers-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  font-weight: 600;\\n  color: #555;\\n}\\n\\n.offers-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: background-color 0.2s;\\n  cursor: pointer;\\n}\\n\\n.offers-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.offers-table[_ngcontent-%COMP%]   tr.selected[_ngcontent-%COMP%] {\\n  background-color: #e8f4fd;\\n}\\n\\n.class-cell[_ngcontent-%COMP%] {\\n  width: 120px;\\n}\\n\\n.price-cell[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #2989d8;\\n}\\n\\n.table-features[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 10px;\\n}\\n\\n.table-features[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n  font-size: 13px;\\n  color: #555;\\n}\\n\\n.table-features[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #2989d8;\\n}\\n\\n\\n\\n.selected-offer-details[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 16px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  margin-top: 40px;\\n  border: 1px solid rgba(0, 0, 0, 0.04);\\n  position: relative;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n}\\n\\n.selected-offer-details[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);\\n}\\n\\n\\n\\n.details-header[_ngcontent-%COMP%] {\\n  padding: 24px 30px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--ocean-blue));\\n  color: white;\\n  position: relative;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.details-header[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgzMCkiPjxwYXRoIGQ9Ik0gMCwxMCBMIDIwLDEwIiBzdHJva2U9InJnYmEoMjU1LDI1NSwyNTUsMC4xKSIgc3Ryb2tlLXdpZHRoPSIxIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+');\\n  opacity: 0.3;\\n  z-index: 0;\\n}\\n\\n.details-header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.details-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: white;\\n  font-size: 24px;\\n  font-weight: 700;\\n  letter-spacing: 0.5px;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.selected-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.price-amount[_ngcontent-%COMP%] {\\n  font-size: 26px;\\n  font-weight: 700;\\n  color: white;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n}\\n\\n.price-amount[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -4px;\\n  left: 0;\\n  width: 100%;\\n  height: 2px;\\n  background-color: rgba(255, 255, 255, 0.3);\\n  border-radius: 2px;\\n}\\n\\n.selected-class-badge[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.15);\\n  color: white;\\n  padding: 10px 20px;\\n  border-radius: 30px;\\n  font-weight: 600;\\n  font-size: 15px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n  z-index: 1;\\n  transition: transform 0.3s ease, background-color 0.3s ease;\\n}\\n\\n.selected-class-badge[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  background-color: rgba(255, 255, 255, 0.25);\\n}\\n\\n.selected-class-badge.business[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 193, 7, 0.25);\\n  color: white;\\n}\\n\\n.selected-class-badge.business[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 193, 7, 0.35);\\n}\\n\\n\\n\\n.details-content[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n\\n\\n\\n.info-cards[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n  margin-bottom: 35px;\\n}\\n\\n.info-card[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  background-color: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\\n  border: 1px solid rgba(0, 0, 0, 0.04);\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n}\\n\\n.info-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);\\n}\\n\\n.info-card-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 20px;\\n  flex-shrink: 0;\\n}\\n\\n.flight-card[_ngcontent-%COMP%]   .info-card-icon[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n}\\n\\n.brand-card[_ngcontent-%COMP%]   .info-card-icon[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--accent-color-rgb), 0.1);\\n  color: var(--accent-color);\\n}\\n\\n.passenger-card[_ngcontent-%COMP%]   .info-card-icon[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--secondary-color-rgb), 0.1);\\n  color: var(--secondary-color);\\n}\\n\\n.info-card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 5px;\\n}\\n\\n.info-card-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n.info-card-value[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n\\n\\n.details-section[_ngcontent-%COMP%] {\\n  margin-bottom: 35px;\\n  padding-bottom: 35px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.06);\\n}\\n\\n.details-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n  border-bottom: none;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 25px;\\n}\\n\\n.section-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 20px;\\n}\\n\\n.details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  letter-spacing: 0.3px;\\n}\\n\\n.class-details-content[_ngcontent-%COMP%] {\\n  background-color: #f9f9f9;\\n  border-radius: 12px;\\n  padding: 20px;\\n  border: 1px solid rgba(0, 0, 0, 0.04);\\n}\\n\\n.class-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 5px;\\n}\\n\\n.class-type[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  color: #666;\\n}\\n\\n.class-code[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #888;\\n  font-family: monospace;\\n}\\n\\n\\n\\n.baggage-cards[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n}\\n\\n.baggage-card[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  background-color: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\\n  border: 1px solid rgba(0, 0, 0, 0.04);\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n}\\n\\n.baggage-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);\\n}\\n\\n.baggage-card.checked[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--primary-color);\\n}\\n\\n.baggage-card.cabin[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--accent-color);\\n}\\n\\n.baggage-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 20px;\\n  flex-shrink: 0;\\n}\\n\\n.baggage-card.checked[_ngcontent-%COMP%]   .baggage-icon[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n}\\n\\n.baggage-card.cabin[_ngcontent-%COMP%]   .baggage-icon[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--accent-color-rgb), 0.1);\\n  color: var(--accent-color);\\n}\\n\\n.baggage-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 5px;\\n}\\n\\n.baggage-type[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.baggage-value[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n\\n\\n.services-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 20px;\\n}\\n\\n.service-item[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 15px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);\\n  border: 1px solid rgba(0, 0, 0, 0.04);\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n}\\n\\n.service-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);\\n}\\n\\n.service-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 10px;\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n  color: var(--primary-color);\\n  flex-shrink: 0;\\n}\\n\\n.service-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 5px;\\n}\\n\\n.service-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.service-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.booking-section[_ngcontent-%COMP%] {\\n  margin-top: 40px;\\n  text-align: center;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n\\n.button-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.particles-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.particle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 8px;\\n  height: 8px;\\n  background-color: rgba(var(--primary-color-rgb), 0.6);\\n  border-radius: 50%;\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n\\n.particle[_ngcontent-%COMP%]:nth-child(1) {\\n  top: 20%;\\n  left: 0;\\n  animation: _ngcontent-%COMP%_particle-animation-1 3s infinite ease-in-out;\\n}\\n\\n.particle[_ngcontent-%COMP%]:nth-child(2) {\\n  top: 60%;\\n  left: 0;\\n  animation: _ngcontent-%COMP%_particle-animation-2 4s infinite ease-in-out;\\n}\\n\\n.particle[_ngcontent-%COMP%]:nth-child(3) {\\n  top: 40%;\\n  right: 0;\\n  animation: _ngcontent-%COMP%_particle-animation-3 3.5s infinite ease-in-out;\\n}\\n\\n.particle[_ngcontent-%COMP%]:nth-child(4) {\\n  top: 80%;\\n  right: 0;\\n  animation: _ngcontent-%COMP%_particle-animation-4 4.5s infinite ease-in-out;\\n}\\n\\n.particle[_ngcontent-%COMP%]:nth-child(5) {\\n  top: 10%;\\n  left: 50%;\\n  animation: _ngcontent-%COMP%_particle-animation-5 5s infinite ease-in-out;\\n}\\n\\n.particle[_ngcontent-%COMP%]:nth-child(6) {\\n  top: 90%;\\n  left: 50%;\\n  animation: _ngcontent-%COMP%_particle-animation-6 5.5s infinite ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_particle-animation-1 {\\n  0%, 100% { opacity: 0; transform: translate(0, 0); }\\n  20%, 80% { opacity: 0.8; transform: translate(-20px, -10px); }\\n}\\n\\n@keyframes _ngcontent-%COMP%_particle-animation-2 {\\n  0%, 100% { opacity: 0; transform: translate(0, 0); }\\n  20%, 80% { opacity: 0.6; transform: translate(-15px, 10px); }\\n}\\n\\n@keyframes _ngcontent-%COMP%_particle-animation-3 {\\n  0%, 100% { opacity: 0; transform: translate(0, 0); }\\n  20%, 80% { opacity: 0.8; transform: translate(20px, -10px); }\\n}\\n\\n@keyframes _ngcontent-%COMP%_particle-animation-4 {\\n  0%, 100% { opacity: 0; transform: translate(0, 0); }\\n  20%, 80% { opacity: 0.6; transform: translate(15px, 10px); }\\n}\\n\\n@keyframes _ngcontent-%COMP%_particle-animation-5 {\\n  0%, 100% { opacity: 0; transform: translate(0, 0); }\\n  20%, 80% { opacity: 0.7; transform: translate(0, -20px); }\\n}\\n\\n@keyframes _ngcontent-%COMP%_particle-animation-6 {\\n  0%, 100% { opacity: 0; transform: translate(0, 0); }\\n  20%, 80% { opacity: 0.7; transform: translate(0, 20px); }\\n}\\n\\n.book-flight-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  color: white;\\n  border: none;\\n  padding: 16px 48px;\\n  font-size: 18px;\\n  font-weight: 600;\\n  border-radius: 50px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.3);\\n  position: relative;\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_button-float 3s ease-in-out infinite alternate;\\n}\\n\\n@keyframes _ngcontent-%COMP%_button-float {\\n  0% {\\n    transform: translateY(0);\\n  }\\n  100% {\\n    transform: translateY(-8px);\\n  }\\n}\\n\\n.book-flight-button[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.7s ease;\\n}\\n\\n.book-flight-button[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  top: -50%;\\n  left: -50%;\\n  width: 200%;\\n  height: 200%;\\n  background: radial-gradient(circle, rgba(var(--primary-color-rgb), 0.1) 0%, transparent 70%);\\n  opacity: 0;\\n  transition: opacity 0.5s ease;\\n  pointer-events: none;\\n}\\n\\n.book-flight-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px) scale(1.05);\\n  box-shadow: 0 15px 35px rgba(var(--primary-color-rgb), 0.5);\\n  animation: none;\\n}\\n\\n.book-flight-button[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n.book-flight-button[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n  animation: _ngcontent-%COMP%_glow 1.5s infinite alternate;\\n}\\n\\n@keyframes _ngcontent-%COMP%_glow {\\n  0% {\\n    opacity: 0.3;\\n  }\\n  100% {\\n    opacity: 0.6;\\n  }\\n}\\n\\n.book-flight-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(-2px) scale(0.98);\\n}\\n\\n.book-flight-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  animation: _ngcontent-%COMP%_icon-pulse 1.5s infinite alternate;\\n}\\n\\n@keyframes _ngcontent-%COMP%_icon-pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  100% {\\n    transform: scale(1.2);\\n  }\\n}\\n\\n\\n\\n\\n\\n.no-results[_ngcontent-%COMP%] {\\n  padding: 40px;\\n  text-align: center;\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n  margin: 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FlightClassType", "PassengerType", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "errorMessage", "ɵɵclassMap", "ctx_r9", "getFeatureIcon", "feature_r11", "serviceGroup", "commercialName", "ctx_r12", "getBaggageIcon", "baggage_r13", "baggageType", "ɵɵtextInterpolate3", "getBaggageTypeName", "piece", "weight", "getUnitTypeName", "unitType", "ɵɵtemplate", "GetOfferComponent_div_8_div_26_div_1_div_15_div_1_Template", "ɵɵproperty", "ɵɵpipeBind3", "offer_r8", "baggageInformations", "ɵɵlistener", "GetOfferComponent_div_8_div_26_div_1_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r16", "$implicit", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "selectOffer", "GetOfferComponent_div_8_div_26_div_1_div_13_Template", "GetOfferComponent_div_8_div_26_div_1_div_15_Template", "GetOfferComponent_div_8_div_26_div_1_Template_button_click_17_listener", "$event", "ctx_r17", "bookFlight", "stopPropagation", "ɵɵclassProp", "ctx_r7", "isSelected", "isBusinessClass", "ɵɵtextInterpolate1", "getOfferClassName", "getOfferClassCode", "ɵɵpipeBind2", "price", "amount", "currency", "flightBrandInfo", "name", "getImportantFeatures", "length", "GetOfferComponent_div_8_div_26_div_1_Template", "ctx_r4", "getFilteredOffers", "ɵɵtextInterpolate2", "ctx_r20", "baggage_r25", "GetOfferComponent_div_8_div_27_tr_17_Template_tr_click_0_listener", "_r27", "offer_r19", "ctx_r26", "GetOfferComponent_div_8_div_27_tr_17_div_9_Template", "GetOfferComponent_div_8_div_27_tr_17_span_13_Template", "GetOfferComponent_div_8_div_27_tr_17_span_14_Template", "GetOfferComponent_div_8_div_27_tr_17_span_15_Template", "GetOfferComponent_div_8_div_27_tr_17_span_16_Template", "GetOfferComponent_div_8_div_27_tr_17_Template_button_click_21_listener", "ctx_r28", "ctx_r18", "hasFeature", "GetOfferComponent_div_8_div_27_tr_17_Template", "ctx_r5", "classInfo_r33", "ctx_r32", "getFlightClassTypeName", "type", "code", "GetOfferComponent_div_8_div_28_div_37_div_6_Template", "ctx_r29", "<PERSON><PERSON><PERSON>", "flightClassInformations", "baggage_r36", "ctx_r34", "baggage_r37", "ctx_r35", "GetOfferComponent_div_8_div_28_div_38_div_6_Template", "GetOfferComponent_div_8_div_28_div_38_div_8_Template", "ctx_r30", "feature_r39", "explanations", "text", "GetOfferComponent_div_8_div_28_div_39_div_6_div_6_Template", "ctx_r38", "GetOfferComponent_div_8_div_28_div_39_div_6_Template", "ctx_r31", "GetOfferComponent_div_8_div_28_div_37_Template", "GetOfferComponent_div_8_div_28_div_38_Template", "GetOfferComponent_div_8_div_28_div_39_Template", "GetOfferComponent_div_8_div_28_Template_button_click_49_listener", "_r43", "ctx_r42", "ctx_r6", "flightId", "features", "GetOfferComponent_div_8_Template_button_click_10_listener", "_r45", "ctx_r44", "setViewMode", "GetOfferComponent_div_8_Template_button_click_13_listener", "ctx_r46", "GetOfferComponent_div_8_Template_button_click_20_listener", "ctx_r47", "filterByClass", "GetOfferComponent_div_8_Template_button_click_22_listener", "ctx_r48", "GetOfferComponent_div_8_Template_button_click_24_listener", "ctx_r49", "GetOfferComponent_div_8_div_26_Template", "GetOfferComponent_div_8_div_27_Template", "GetOfferComponent_div_8_div_28_Template", "ctx_r2", "offers", "viewMode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GetOfferComponent", "constructor", "route", "router", "productService", "bookingService", "snackBar", "searchId", "offerId", "isLoading", "ngOnInit", "queryParams", "subscribe", "params", "console", "log", "missingParams", "push", "loadOfferDetails", "join", "error", "url", "window", "location", "href", "urlParams", "URLSearchParams", "search", "paramsObj", "for<PERSON>ach", "value", "key", "request", "createDefaultGetOffersRequest", "getOffers", "next", "response", "header", "success", "body", "sortOffersByPrice", "messages", "message", "status", "sort", "a", "b", "filter", "offer", "classType", "mode", "goBack", "navigate", "beginTransaction", "beginResponse", "transactionId", "offerIds", "autoStarted", "open", "duration", "panelClass", "some", "classInfo", "BUSINESS", "featureName", "feature", "toLowerCase", "includes", "uniqueFeatures", "Map", "has", "set", "Array", "from", "values", "slice", "getProviderName", "providerCode", "providers", "typeCode", "PROMO", "ECONOMY", "getPassengerTypeName", "Adult", "Child", "Infant", "unitTypeCode", "unitTypes", "baggageTypeCode", "baggageTypes", "icons", "serviceGroupCode", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProductService", "i3", "BookingService", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "GetOfferComponent_Template", "rf", "ctx", "GetOfferComponent_Template_button_click_4_listener", "GetOfferComponent_div_6_Template", "GetOfferComponent_div_7_Template", "GetOfferComponent_div_8_Template", "GetOfferComponent_div_9_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\get-offer\\get-offer.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\get-offer\\get-offer.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ProductService } from 'src/app/services/product.service';\nimport { BookingService } from 'src/app/services/booking.service';\nimport { SharedDataService } from 'src/app/services/shared-data.service';\nimport { GetOffersRequest } from 'src/app/models/get-offers-request.model';\nimport { Offer, Feature } from 'src/app/models/get-offers-response.model';\nimport { FlightClassType, PassengerType } from 'src/app/models/enums.model';\nimport { MatSnackBar } from '@angular/material/snack-bar';\n\n@Component({\n  selector: 'app-get-offer',\n  templateUrl: './get-offer.component.html',\n  styleUrls: ['./get-offer.component.css']\n})\nexport class GetOfferComponent implements OnInit {\n  // Paramètres de requête\n  searchId: string = '';\n  offerId: string = '';\n\n  // Données\n  offers: Offer[] = [];\n  selectedOffer: Offer | null = null;\n\n  // État de l'interface\n  isLoading: boolean = false;\n  errorMessage: string = '';\n  viewMode: 'cards' | 'table' = 'cards';\n  selectedClassFilter: 'all' | 'economy' | 'business' = 'all';\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private productService: ProductService,\n    private bookingService: BookingService,\n    private snackBar: MatSnackBar\n  ) { }\n\n  ngOnInit(): void {\n    this.route.queryParams.subscribe(params => {\n      this.searchId = params['searchId'];\n      this.offerId = params['offerId'];\n\n      console.log('GetOfferComponent received params:', params);\n      console.log('SearchId:', this.searchId);\n      console.log('OfferId:', this.offerId);\n\n      // Vérifier si les deux paramètres sont présents\n      const missingParams = [];\n      if (!this.searchId) missingParams.push('searchId');\n      if (!this.offerId) missingParams.push('offerId');\n\n      if (missingParams.length === 0) {\n        // Tous les paramètres sont présents\n        this.loadOfferDetails();\n      } else {\n        // Paramètres manquants\n        this.errorMessage = `Missing parameters: ${missingParams.join(', ')}. Please go back and try again.`;\n        console.error('Missing parameters:', { searchId: this.searchId, offerId: this.offerId });\n\n        // Essayer de récupérer les paramètres manquants de l'URL\n        const url = window.location.href;\n        console.log('Current URL:', url);\n\n        // Analyser l'URL pour trouver des paramètres potentiellement mal formatés\n        const urlParams = new URLSearchParams(window.location.search);\n        const paramsObj: Record<string, string> = {};\n        urlParams.forEach((value, key) => {\n          paramsObj[key] = value;\n        });\n        console.log('All URL params:', paramsObj);\n      }\n    });\n  }\n\n  loadOfferDetails(): void {\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    const request: GetOffersRequest = this.productService.createDefaultGetOffersRequest(\n      this.searchId,\n      [this.offerId]\n    );\n\n    console.log('Sending GetOffersRequest:', request);\n\n    this.productService.getOffers(request).subscribe({\n      next: (response) => {\n        this.isLoading = false;\n        console.log('GetOffers response:', response);\n\n        if (response.header.success && response.body.offers && response.body.offers.length > 0) {\n          console.log('Offers found:', response.body.offers);\n          this.offers = response.body.offers;\n\n          // Sélectionner la première offre par défaut\n          if (this.offers.length > 0) {\n            this.selectedOffer = this.offers[0];\n          }\n\n          // Trier les offres par prix (du moins cher au plus cher)\n          this.sortOffersByPrice();\n        } else {\n          console.error('No offers found in response or response not successful');\n          console.log('Response header success:', response.header.success);\n          console.log('Response body offers:', response.body.offers);\n\n          this.errorMessage = 'No flight options found.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            console.log('Error message from API:', response.header.messages[0].message);\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred while loading flight options. Please try again.';\n        console.error('Error getting offers:', error);\n\n        // Afficher plus de détails sur l'erreur\n        if (error.error) {\n          console.log('Error details:', error.error);\n        }\n        if (error.message) {\n          console.log('Error message:', error.message);\n        }\n        if (error.status) {\n          console.log('Error status:', error.status);\n        }\n      }\n    });\n  }\n\n  // Méthodes de tri et de filtrage\n  sortOffersByPrice(): void {\n    this.offers.sort((a, b) => a.price.amount - b.price.amount);\n  }\n\n  getFilteredOffers(): Offer[] {\n    if (this.selectedClassFilter === 'all') {\n      return this.offers;\n    }\n\n    return this.offers.filter(offer => {\n      const isBusinessClass = this.isBusinessClass(offer);\n      return (this.selectedClassFilter === 'business' && isBusinessClass) ||\n             (this.selectedClassFilter === 'economy' && !isBusinessClass);\n    });\n  }\n\n  filterByClass(classType: 'all' | 'economy' | 'business'): void {\n    this.selectedClassFilter = classType;\n  }\n\n  setViewMode(mode: 'cards' | 'table'): void {\n    this.viewMode = mode;\n  }\n\n  // Méthodes de sélection\n  selectOffer(offer: Offer): void {\n    this.selectedOffer = offer;\n  }\n\n  isSelected(offer: Offer): boolean {\n    return this.selectedOffer === offer;\n  }\n\n  goBack(): void {\n    this.router.navigate(['/search-price']);\n  }\n\n  bookFlight(offer: Offer): void {\n    console.log('Starting booking transaction with offer ID:', offer.offerId);\n\n    // Afficher un indicateur de chargement\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    // Démarrer la transaction automatiquement\n    this.bookingService.beginTransaction([offer.offerId]).subscribe({\n      next: (response) => {\n        this.isLoading = false;\n\n        if (response && response.beginResponse && response.beginResponse.body) {\n          const transactionId = response.beginResponse.body.transactionId;\n          console.log('Transaction started successfully with ID:', transactionId);\n\n          // Naviguer vers la page de réservation avec le transactionId\n          // Cela permettra au composant booking-transaction de sauter l'étape 1\n          this.router.navigate(['/booking-transaction'], {\n            queryParams: {\n              offerIds: offer.offerId,\n              transactionId: transactionId,\n              searchId: this.searchId,\n              autoStarted: 'true'\n            }\n          });\n\n          // Afficher un message de succès\n          this.snackBar.open('Réservation initiée avec succès', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = 'Erreur lors du démarrage de la transaction. Veuillez réessayer.';\n          console.error('Invalid response from beginTransaction:', response);\n\n          // Afficher un message d'erreur\n          this.snackBar.open(this.errorMessage, 'Fermer', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.errorMessage = 'Une erreur est survenue lors du démarrage de la réservation. Veuillez réessayer.';\n        console.error('Error starting transaction:', error);\n\n        // Afficher un message d'erreur\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n\n        // En cas d'erreur, rediriger vers la page de réservation traditionnelle\n        this.router.navigate(['/booking-transaction'], {\n          queryParams: {\n            offerIds: offer.offerId,\n            searchId: this.searchId\n          }\n        });\n      }\n    });\n  }\n\n  // Méthodes d'analyse des offres\n  isBusinessClass(offer: Offer): boolean {\n    if (!offer.flightClassInformations || offer.flightClassInformations.length === 0) {\n      return false;\n    }\n\n    return offer.flightClassInformations.some(classInfo =>\n      classInfo.type === FlightClassType.BUSINESS\n    );\n  }\n\n  getOfferClassName(offer: Offer): string {\n    if (!offer.flightClassInformations || offer.flightClassInformations.length === 0) {\n      return 'Unknown';\n    }\n\n    return offer.flightClassInformations[0].name;\n  }\n\n  getOfferClassCode(offer: Offer): string {\n    if (!offer.flightClassInformations || offer.flightClassInformations.length === 0) {\n      return '';\n    }\n\n    return offer.flightClassInformations[0].code;\n  }\n\n  // Méthodes pour les caractéristiques des offres\n  hasFeature(offer: Offer, featureName: string): boolean {\n    if (!offer.flightBrandInfo || !offer.flightBrandInfo.features) {\n      return false;\n    }\n\n    return offer.flightBrandInfo.features.some(feature =>\n      feature.commercialName.toLowerCase().includes(featureName.toLowerCase())\n    );\n  }\n\n  getImportantFeatures(offer: Offer): Feature[] {\n    if (!offer || !offer.flightBrandInfo || !offer.flightBrandInfo.features) {\n      return [];\n    }\n\n    // Filtrer les caractéristiques pour n'afficher que les plus importantes\n    // et éviter les doublons\n    const uniqueFeatures = new Map<string, Feature>();\n\n    offer.flightBrandInfo.features.forEach(feature => {\n      // Ne pas ajouter de doublons basés sur le nom commercial\n      if (!uniqueFeatures.has(feature.commercialName)) {\n        uniqueFeatures.set(feature.commercialName, feature);\n      }\n    });\n\n    // Convertir la Map en tableau et limiter à 6 caractéristiques maximum\n    return Array.from(uniqueFeatures.values()).slice(0, 6);\n  }\n\n  // Méthodes utilitaires pour afficher des valeurs lisibles\n  getProviderName(providerCode: number): string {\n    const providers: { [key: number]: string } = {\n      1: 'Provider A',\n      2: 'Provider B',\n      3: 'Provider C'\n    };\n    return providers[providerCode] || `Provider ${providerCode}`;\n  }\n\n  getFlightClassTypeName(typeCode: number): string {\n    switch (typeCode) {\n      case FlightClassType.PROMO:\n        return 'Promo';\n      case FlightClassType.ECONOMY:\n        return 'Economy';\n      case FlightClassType.BUSINESS:\n        return 'Business';\n      default:\n        return `Class Type ${typeCode}`;\n    }\n  }\n\n  getPassengerTypeName(typeCode: number): string {\n    switch (typeCode) {\n      case PassengerType.Adult:\n        return 'Adult';\n      case PassengerType.Child:\n        return 'Child';\n      case PassengerType.Infant:\n        return 'Infant';\n      default:\n        return `Passenger Type ${typeCode}`;\n    }\n  }\n\n  getUnitTypeName(unitTypeCode: number): string {\n    const unitTypes: { [key: number]: string } = {\n      1: 'kg',\n      2: 'lb'\n    };\n    return unitTypes[unitTypeCode] || '';\n  }\n\n  getBaggageTypeName(baggageTypeCode: number): string {\n    const baggageTypes: { [key: number]: string } = {\n      1: 'Checked Baggage',\n      2: 'Cabin Baggage',\n      3: 'Hand Baggage'\n    };\n    return baggageTypes[baggageTypeCode] || `Baggage Type ${baggageTypeCode}`;\n  }\n\n  // Méthodes pour les icônes\n  getBaggageIcon(baggageTypeCode: number): string {\n    const icons: { [key: number]: string } = {\n      1: 'fas fa-suitcase',\n      2: 'fas fa-briefcase',\n      3: 'fas fa-shopping-bag'\n    };\n    return icons[baggageTypeCode] || 'fas fa-luggage-cart';\n  }\n\n  getFeatureIcon(serviceGroupCode: number): string {\n    const icons: { [key: number]: string } = {\n      0: 'fas fa-star', // Default/Other\n      1: 'fas fa-suitcase', // Baggage\n      2: 'fas fa-utensils', // Food\n      3: 'fas fa-wifi', // WiFi\n      4: 'fas fa-couch', // Lounge\n      5: 'fas fa-hamburger' // Meals\n    };\n    return icons[serviceGroupCode] || 'fas fa-star';\n  }\n}\n", "<div class=\"get-offer-container\">\n  <div class=\"offer-header\">\n    <h2>Flight Options</h2>\n    <button class=\"back-button\" (click)=\"goBack()\">Back to Search</button>\n  </div>\n\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"spinner large\"></div>\n    <p>Loading flight options...</p>\n  </div>\n\n  <div *ngIf=\"errorMessage && !isLoading\" class=\"error-container\">\n    <p class=\"error-message\">{{ errorMessage }}</p>\n  </div>\n\n  <div *ngIf=\"offers && offers.length > 0 && !isLoading\" class=\"offers-container\">\n    <!-- Flight Summary Section -->\n    <div class=\"flight-summary-card\">\n      <div class=\"flight-summary-header\">\n        <div class=\"flight-info\">\n          <h3>{{ offers[0].flightId }}</h3>\n          <div class=\"flight-route\">Istanbul (IST) → Tunis (TUN)</div>\n        </div>\n        <div class=\"view-controls\">\n          <div class=\"view-toggle\">\n            <button [class.active]=\"viewMode === 'cards'\" (click)=\"setViewMode('cards')\">\n              <i class=\"fas fa-th-large\"></i> Cards\n            </button>\n            <button [class.active]=\"viewMode === 'table'\" (click)=\"setViewMode('table')\">\n              <i class=\"fas fa-table\"></i> Table\n            </button>\n          </div>\n          <div class=\"filter-controls\">\n            <span class=\"filter-label\">Filter by:</span>\n            <div class=\"filter-buttons\">\n              <button [class.active]=\"selectedClassFilter === 'all'\" (click)=\"filterByClass('all')\">All</button>\n              <button [class.active]=\"selectedClassFilter === 'economy'\" (click)=\"filterByClass('economy')\">Economy</button>\n              <button [class.active]=\"selectedClassFilter === 'business'\" (click)=\"filterByClass('business')\">Business</button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Cards View -->\n    <div *ngIf=\"viewMode === 'cards'\" class=\"offers-grid\">\n      <div *ngFor=\"let offer of getFilteredOffers()\" class=\"offer-card\" [class.selected]=\"isSelected(offer)\" (click)=\"selectOffer(offer)\">\n        <div class=\"offer-header\">\n          <div class=\"offer-class\">\n            <span class=\"class-badge\" [class.business]=\"isBusinessClass(offer)\">\n              {{ getOfferClassName(offer) }}\n            </span>\n            <span class=\"class-code\">{{ getOfferClassCode(offer) }}</span>\n          </div>\n          <div class=\"offer-price\">\n            {{ offer.price.amount | currency:offer.price.currency }}\n          </div>\n        </div>\n\n        <div class=\"offer-brand\">\n          {{ offer.flightBrandInfo && offer.flightBrandInfo.name || 'Standard' }}\n        </div>\n\n        <div class=\"offer-features\">\n          <div class=\"feature-item\" *ngFor=\"let feature of getImportantFeatures(offer) | slice:0:3\">\n            <div class=\"feature-icon\">\n              <i [class]=\"getFeatureIcon(feature.serviceGroup)\"></i>\n            </div>\n            <div class=\"feature-content\">\n              <div class=\"feature-name\">{{ feature.commercialName }}</div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"offer-baggage\" *ngIf=\"offer.baggageInformations && offer.baggageInformations.length > 0\">\n          <div class=\"baggage-item\" *ngFor=\"let baggage of offer.baggageInformations | slice:0:2\">\n            <i [class]=\"getBaggageIcon(baggage.baggageType)\"></i>\n            <span>{{ getBaggageTypeName(baggage.baggageType) }}:\n              {{ baggage.piece > 0 ? baggage.piece + ' piece(s)' : '' }}\n              {{ baggage.weight > 0 ? baggage.weight + ' ' + getUnitTypeName(baggage.unitType) : '' }}\n            </span>\n          </div>\n        </div>\n\n        <div class=\"offer-actions\">\n          <button class=\"book-button\" (click)=\"bookFlight(offer); $event.stopPropagation()\">\n            Select This Fare\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Table View -->\n    <div *ngIf=\"viewMode === 'table'\" class=\"offers-table-container\">\n      <table class=\"offers-table\">\n        <thead>\n          <tr>\n            <th>Class</th>\n            <th>Brand</th>\n            <th>Baggage</th>\n            <th>Features</th>\n            <th>Price</th>\n            <th>Action</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr *ngFor=\"let offer of getFilteredOffers()\" [class.selected]=\"isSelected(offer)\" (click)=\"selectOffer(offer)\">\n            <td class=\"class-cell\">\n              <div class=\"class-badge\" [class.business]=\"isBusinessClass(offer)\">\n                {{ getOfferClassName(offer) }}\n              </div>\n              <div class=\"class-code\">{{ getOfferClassCode(offer) }}</div>\n            </td>\n            <td>{{ offer.flightBrandInfo && offer.flightBrandInfo.name || 'Standard' }}</td>\n            <td>\n              <div *ngFor=\"let baggage of offer.baggageInformations | slice:0:2\">\n                {{ getBaggageTypeName(baggage.baggageType) }}:\n                {{ baggage.piece > 0 ? baggage.piece + ' piece(s)' : '' }}\n              </div>\n            </td>\n            <td>\n              <div class=\"table-features\">\n                <span *ngIf=\"hasFeature(offer, 'wifi')\"><i class=\"fas fa-wifi\"></i> WiFi</span>\n                <span *ngIf=\"hasFeature(offer, 'meal')\"><i class=\"fas fa-utensils\"></i> Meals</span>\n                <span *ngIf=\"hasFeature(offer, 'refund')\"><i class=\"fas fa-undo\"></i> Refundable</span>\n                <span *ngIf=\"hasFeature(offer, 'rebook')\"><i class=\"fas fa-exchange-alt\"></i> Changeable</span>\n              </div>\n            </td>\n            <td class=\"price-cell\">{{ offer.price.amount | currency:offer.price.currency }}</td>\n            <td>\n              <button class=\"book-button\" (click)=\"bookFlight(offer); $event.stopPropagation()\">\n                Select\n              </button>\n            </td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n\n    <!-- Selected Offer Details - Modern Design -->\n    <div *ngIf=\"selectedOffer\" class=\"selected-offer-details\">\n      <!-- Modern Header with Gradient -->\n      <div class=\"details-header\">\n        <div class=\"details-header-content\">\n          <h3>Selected Fare Details</h3>\n          <div class=\"selected-price\">\n            <span class=\"price-amount\">{{ selectedOffer.price.amount | currency:selectedOffer.price.currency }}</span>\n          </div>\n        </div>\n        <div class=\"selected-class-badge\" [class.business]=\"isBusinessClass(selectedOffer)\">\n          {{ getOfferClassName(selectedOffer) }}\n        </div>\n      </div>\n\n      <!-- Main Content Area -->\n      <div class=\"details-content\">\n        <!-- Key Information Cards -->\n        <div class=\"info-cards\">\n          <div class=\"info-card flight-card\">\n            <div class=\"info-card-icon\">\n              <i class=\"fas fa-plane\"></i>\n            </div>\n            <div class=\"info-card-content\">\n              <div class=\"info-card-label\">Flight</div>\n              <div class=\"info-card-value\">{{ selectedOffer.flightId }}</div>\n            </div>\n          </div>\n\n          <div class=\"info-card brand-card\">\n            <div class=\"info-card-icon\">\n              <i class=\"fas fa-tag\"></i>\n            </div>\n            <div class=\"info-card-content\">\n              <div class=\"info-card-label\">Brand</div>\n              <div class=\"info-card-value\">{{ selectedOffer.flightBrandInfo && selectedOffer.flightBrandInfo.name || 'Flexible' }}</div>\n            </div>\n          </div>\n\n          <div class=\"info-card passenger-card\">\n            <div class=\"info-card-icon\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <div class=\"info-card-content\">\n              <div class=\"info-card-label\">Passenger</div>\n              <div class=\"info-card-value\">1 Adult</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Class Details Section -->\n        <div class=\"details-section class-details\" *ngIf=\"selectedOffer.flightClassInformations && selectedOffer.flightClassInformations.length > 0\">\n          <div class=\"section-header\">\n            <i class=\"fas fa-chair\"></i>\n            <h4>Class Details</h4>\n          </div>\n          <div class=\"class-details-content\">\n            <div *ngFor=\"let classInfo of selectedOffer.flightClassInformations\">\n              <div class=\"class-name\">{{ classInfo.name }}</div>\n              <div class=\"class-type\">{{ getFlightClassTypeName(classInfo.type) }} <span class=\"class-code\">({{ classInfo.code }})</span></div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Baggage Allowance Section -->\n        <div class=\"details-section baggage-section\" *ngIf=\"selectedOffer.baggageInformations && selectedOffer.baggageInformations.length > 0\">\n          <div class=\"section-header\">\n            <i class=\"fas fa-suitcase\"></i>\n            <h4>Baggage Allowance</h4>\n          </div>\n          <div class=\"baggage-cards\">\n            <!-- Checked Baggage -->\n            <div class=\"baggage-card checked\" *ngFor=\"let baggage of selectedOffer.baggageInformations | slice:0:1\">\n              <div class=\"baggage-icon\">\n                <i class=\"fas fa-suitcase\"></i>\n              </div>\n              <div class=\"baggage-details\">\n                <div class=\"baggage-type\">Checked Baggage</div>\n                <div class=\"baggage-value\">\n                  {{ baggage.piece > 0 ? baggage.piece + ' piece(s)' : '' }}\n                  {{ baggage.weight > 0 ? baggage.weight + ' ' + getUnitTypeName(baggage.unitType) : '' }}\n                </div>\n              </div>\n            </div>\n\n            <!-- Cabin Baggage -->\n            <div class=\"baggage-card cabin\" *ngFor=\"let baggage of selectedOffer.baggageInformations | slice:1:2\">\n              <div class=\"baggage-icon\">\n                <i class=\"fas fa-briefcase\"></i>\n              </div>\n              <div class=\"baggage-details\">\n                <div class=\"baggage-type\">Cabin Baggage</div>\n                <div class=\"baggage-value\">\n                  {{ baggage.piece > 0 ? baggage.piece + ' piece(s)' : '' }}\n                  {{ baggage.weight > 0 ? baggage.weight + ' ' + getUnitTypeName(baggage.unitType) : '' }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Included Services Section -->\n        <div class=\"details-section services-section\" *ngIf=\"selectedOffer.flightBrandInfo && selectedOffer.flightBrandInfo.features && selectedOffer.flightBrandInfo.features.length > 0\">\n          <div class=\"section-header\">\n            <i class=\"fas fa-concierge-bell\"></i>\n            <h4>Included Services</h4>\n          </div>\n          <div class=\"services-grid\">\n            <div class=\"service-item\" *ngFor=\"let feature of getImportantFeatures(selectedOffer)\">\n              <div class=\"service-icon\">\n                <i [class]=\"getFeatureIcon(feature.serviceGroup)\"></i>\n              </div>\n              <div class=\"service-details\">\n                <div class=\"service-name\">{{ feature.commercialName }}</div>\n                <div class=\"service-description\" *ngIf=\"feature.explanations && feature.explanations.length > 0\">\n                  {{ feature.explanations[0].text }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Booking Action Section -->\n        <div class=\"booking-section\">\n          <div class=\"button-container\">\n            <div class=\"particles-container\">\n              <span class=\"particle\"></span>\n              <span class=\"particle\"></span>\n              <span class=\"particle\"></span>\n              <span class=\"particle\"></span>\n              <span class=\"particle\"></span>\n              <span class=\"particle\"></span>\n            </div>\n            <button class=\"book-flight-button animate-pulse\" (click)=\"bookFlight(selectedOffer)\">\n              <i class=\"fas fa-check-circle\"></i>\n              Book This Flight\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- No Results -->\n  <div *ngIf=\"offers && offers.length === 0 && !isLoading\" class=\"no-results\">\n    <p>No flight options available for the selected criteria.</p>\n  </div>\n</div>\n"], "mappings": "AAOA,SAASA,eAAe,EAAEC,aAAa,QAAQ,4BAA4B;;;;;;;;;ICDzEC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGlCJ,EAAA,CAAAC,cAAA,aAAgE;IACrCD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAtBJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;IAoDrCR,EAAA,CAAAC,cAAA,cAA0F;IAEtFD,EAAA,CAAAE,SAAA,QAAsD;IACxDF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAA6B;IACDD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHzDJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAS,UAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAC,WAAA,CAAAC,YAAA,EAA8C;IAGvBb,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAM,WAAA,CAAAE,cAAA,CAA4B;;;;;IAM1Dd,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAE,SAAA,QAAqD;IACrDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAGN;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAJJJ,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAS,UAAA,CAAAM,OAAA,CAAAC,cAAA,CAAAC,WAAA,CAAAC,WAAA,EAA6C;IAC1ClB,EAAA,CAAAK,SAAA,GAGN;IAHML,EAAA,CAAAmB,kBAAA,KAAAJ,OAAA,CAAAK,kBAAA,CAAAH,WAAA,CAAAC,WAAA,SAAAD,WAAA,CAAAI,KAAA,OAAAJ,WAAA,CAAAI,KAAA,0BAAAJ,WAAA,CAAAK,MAAA,OAAAL,WAAA,CAAAK,MAAA,SAAAP,OAAA,CAAAQ,eAAA,CAAAN,WAAA,CAAAO,QAAA,YAGN;;;;;IANJxB,EAAA,CAAAC,cAAA,cAAqG;IACnGD,EAAA,CAAAyB,UAAA,IAAAC,0DAAA,kBAMM;;IACR1B,EAAA,CAAAI,YAAA,EAAM;;;;IAP0CJ,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAA2B,UAAA,YAAA3B,EAAA,CAAA4B,WAAA,OAAAC,QAAA,CAAAC,mBAAA,QAAwC;;;;;;IA7B1F9B,EAAA,CAAAC,cAAA,cAAoI;IAA7BD,EAAA,CAAA+B,UAAA,mBAAAC,mEAAA;MAAA,MAAAC,WAAA,GAAAjC,EAAA,CAAAkC,aAAA,CAAAC,IAAA;MAAA,MAAAN,QAAA,GAAAI,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAsC,aAAA;MAAA,OAAStC,EAAA,CAAAuC,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAAX,QAAA,CAAkB;IAAA,EAAC;IACjI7B,EAAA,CAAAC,cAAA,aAA0B;IAGpBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEhEJ,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGRJ,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAyB,UAAA,KAAAgB,oDAAA,kBAOM;;IACRzC,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAyB,UAAA,KAAAiB,oDAAA,kBAQM;IAEN1C,EAAA,CAAAC,cAAA,eAA2B;IACGD,EAAA,CAAA+B,UAAA,mBAAAY,uEAAAC,MAAA;MAAA,MAAAX,WAAA,GAAAjC,EAAA,CAAAkC,aAAA,CAAAC,IAAA;MAAA,MAAAN,QAAA,GAAAI,WAAA,CAAAG,SAAA;MAAA,MAAAS,OAAA,GAAA7C,EAAA,CAAAsC,aAAA;MAASO,OAAA,CAAAC,UAAA,CAAAjB,QAAA,CAAiB;MAAA,OAAE7B,EAAA,CAAAuC,WAAA,CAAAK,MAAA,CAAAG,eAAA,EAAwB;IAAA,EAAC;IAC/E/C,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAzCqDJ,EAAA,CAAAgD,WAAA,aAAAC,MAAA,CAAAC,UAAA,CAAArB,QAAA,EAAoC;IAGtE7B,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAgD,WAAA,aAAAC,MAAA,CAAAE,eAAA,CAAAtB,QAAA,EAAyC;IACjE7B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoD,kBAAA,MAAAH,MAAA,CAAAI,iBAAA,CAAAxB,QAAA,OACF;IACyB7B,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,iBAAA,CAAA2C,MAAA,CAAAK,iBAAA,CAAAzB,QAAA,EAA8B;IAGvD7B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoD,kBAAA,MAAApD,EAAA,CAAAuD,WAAA,QAAA1B,QAAA,CAAA2B,KAAA,CAAAC,MAAA,EAAA5B,QAAA,CAAA2B,KAAA,CAAAE,QAAA,OACF;IAIA1D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoD,kBAAA,MAAAvB,QAAA,CAAA8B,eAAA,IAAA9B,QAAA,CAAA8B,eAAA,CAAAC,IAAA,oBACF;IAGgD5D,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAA2B,UAAA,YAAA3B,EAAA,CAAA4B,WAAA,SAAAqB,MAAA,CAAAY,oBAAA,CAAAhC,QAAA,SAA0C;IAU9D7B,EAAA,CAAAK,SAAA,GAAuE;IAAvEL,EAAA,CAAA2B,UAAA,SAAAE,QAAA,CAAAC,mBAAA,IAAAD,QAAA,CAAAC,mBAAA,CAAAgC,MAAA,KAAuE;;;;;IA7BvG9D,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAyB,UAAA,IAAAsC,6CAAA,oBA2CM;IACR/D,EAAA,CAAAI,YAAA,EAAM;;;;IA5CmBJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA2B,UAAA,YAAAqC,MAAA,CAAAC,iBAAA,GAAsB;;;;;IAqErCjE,EAAA,CAAAC,cAAA,UAAmE;IACjED,EAAA,CAAAG,MAAA,GAEF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAFJJ,EAAA,CAAAK,SAAA,GAEF;IAFEL,EAAA,CAAAkE,kBAAA,MAAAC,OAAA,CAAA/C,kBAAA,CAAAgD,WAAA,CAAAlD,WAAA,SAAAkD,WAAA,CAAA/C,KAAA,OAAA+C,WAAA,CAAA/C,KAAA,yBAEF;;;;;IAIErB,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,SAAA,YAA2B;IAACF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAC/EJ,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,SAAA,YAA+B;IAACF,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACpFJ,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,SAAA,YAA2B;IAACF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACvFJ,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,SAAA,YAAmC;IAACF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAnBrGJ,EAAA,CAAAC,cAAA,aAAgH;IAA7BD,EAAA,CAAA+B,UAAA,mBAAAsC,kEAAA;MAAA,MAAApC,WAAA,GAAAjC,EAAA,CAAAkC,aAAA,CAAAoC,IAAA;MAAA,MAAAC,SAAA,GAAAtC,WAAA,CAAAG,SAAA;MAAA,MAAAoC,OAAA,GAAAxE,EAAA,CAAAsC,aAAA;MAAA,OAAStC,EAAA,CAAAuC,WAAA,CAAAiC,OAAA,CAAAhC,WAAA,CAAA+B,SAAA,CAAkB;IAAA,EAAC;IAC7GvE,EAAA,CAAAC,cAAA,aAAuB;IAEnBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAE9DJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAuE;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChFJ,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAyB,UAAA,IAAAgD,mDAAA,kBAGM;;IACRzE,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IAEAD,EAAA,CAAAyB,UAAA,KAAAiD,qDAAA,mBAA+E;IAC/E1E,EAAA,CAAAyB,UAAA,KAAAkD,qDAAA,mBAAoF;IACpF3E,EAAA,CAAAyB,UAAA,KAAAmD,qDAAA,mBAAuF;IACvF5E,EAAA,CAAAyB,UAAA,KAAAoD,qDAAA,mBAA+F;IACjG7E,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAG,MAAA,IAAwD;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpFJ,EAAA,CAAAC,cAAA,UAAI;IAC0BD,EAAA,CAAA+B,UAAA,mBAAA+C,uEAAAlC,MAAA;MAAA,MAAAX,WAAA,GAAAjC,EAAA,CAAAkC,aAAA,CAAAoC,IAAA;MAAA,MAAAC,SAAA,GAAAtC,WAAA,CAAAG,SAAA;MAAA,MAAA2C,OAAA,GAAA/E,EAAA,CAAAsC,aAAA;MAASyC,OAAA,CAAAjC,UAAA,CAAAyB,SAAA,CAAiB;MAAA,OAAEvE,EAAA,CAAAuC,WAAA,CAAAK,MAAA,CAAAG,eAAA,EAAwB;IAAA,EAAC;IAC/E/C,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IA1BiCJ,EAAA,CAAAgD,WAAA,aAAAgC,OAAA,CAAA9B,UAAA,CAAAqB,SAAA,EAAoC;IAErDvE,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAgD,WAAA,aAAAgC,OAAA,CAAA7B,eAAA,CAAAoB,SAAA,EAAyC;IAChEvE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoD,kBAAA,MAAA4B,OAAA,CAAA3B,iBAAA,CAAAkB,SAAA,OACF;IACwBvE,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,iBAAA,CAAA0E,OAAA,CAAA1B,iBAAA,CAAAiB,SAAA,EAA8B;IAEpDvE,EAAA,CAAAK,SAAA,GAAuE;IAAvEL,EAAA,CAAAM,iBAAA,CAAAiE,SAAA,CAAAZ,eAAA,IAAAY,SAAA,CAAAZ,eAAA,CAAAC,IAAA,eAAuE;IAEhD5D,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAA2B,UAAA,YAAA3B,EAAA,CAAA4B,WAAA,SAAA2C,SAAA,CAAAzC,mBAAA,QAAwC;IAOxD9B,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA2B,UAAA,SAAAqD,OAAA,CAAAC,UAAA,CAAAV,SAAA,UAA+B;IAC/BvE,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA2B,UAAA,SAAAqD,OAAA,CAAAC,UAAA,CAAAV,SAAA,UAA+B;IAC/BvE,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAA2B,UAAA,SAAAqD,OAAA,CAAAC,UAAA,CAAAV,SAAA,YAAiC;IACjCvE,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAA2B,UAAA,SAAAqD,OAAA,CAAAC,UAAA,CAAAV,SAAA,YAAiC;IAGrBvE,EAAA,CAAAK,SAAA,GAAwD;IAAxDL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAuD,WAAA,SAAAgB,SAAA,CAAAf,KAAA,CAAAC,MAAA,EAAAc,SAAA,CAAAf,KAAA,CAAAE,QAAA,EAAwD;;;;;IAnCvF1D,EAAA,CAAAC,cAAA,cAAiE;IAIrDD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACdJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACdJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChBJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjBJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACdJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAGnBJ,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAyB,UAAA,KAAAyD,6CAAA,mBA4BK;IACPlF,EAAA,CAAAI,YAAA,EAAQ;;;;IA7BgBJ,EAAA,CAAAK,SAAA,IAAsB;IAAtBL,EAAA,CAAA2B,UAAA,YAAAwD,MAAA,CAAAlB,iBAAA,GAAsB;;;;;IA0F1CjE,EAAA,CAAAC,cAAA,UAAqE;IAC3CD,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAClDJ,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAG,MAAA,GAA6C;IAAAH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IADnGJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,iBAAA,CAAA8E,aAAA,CAAAxB,IAAA,CAAoB;IACpB5D,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAoD,kBAAA,KAAAiC,OAAA,CAAAC,sBAAA,CAAAF,aAAA,CAAAG,IAAA,OAA6C;IAAyBvF,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAoD,kBAAA,MAAAgC,aAAA,CAAAI,IAAA,MAAsB;;;;;IAR1HxF,EAAA,CAAAC,cAAA,cAA6I;IAEzID,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAExBJ,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAyB,UAAA,IAAAgE,oDAAA,kBAGM;IACRzF,EAAA,CAAAI,YAAA,EAAM;;;;IAJuBJ,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAA2B,UAAA,YAAA+D,OAAA,CAAAC,aAAA,CAAAC,uBAAA,CAAwC;;;;;IAenE5F,EAAA,CAAAC,cAAA,cAAwG;IAEpGD,EAAA,CAAAE,SAAA,YAA+B;IACjCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAA6B;IACDD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC/CJ,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAG,MAAA,GAEF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAFJJ,EAAA,CAAAK,SAAA,GAEF;IAFEL,EAAA,CAAAkE,kBAAA,MAAA2B,WAAA,CAAAxE,KAAA,OAAAwE,WAAA,CAAAxE,KAAA,0BAAAwE,WAAA,CAAAvE,MAAA,OAAAuE,WAAA,CAAAvE,MAAA,SAAAwE,OAAA,CAAAvE,eAAA,CAAAsE,WAAA,CAAArE,QAAA,YAEF;;;;;IAKJxB,EAAA,CAAAC,cAAA,eAAsG;IAElGD,EAAA,CAAAE,SAAA,aAAgC;IAClCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAA6B;IACDD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC7CJ,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAG,MAAA,GAEF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAFJJ,EAAA,CAAAK,SAAA,GAEF;IAFEL,EAAA,CAAAkE,kBAAA,MAAA6B,WAAA,CAAA1E,KAAA,OAAA0E,WAAA,CAAA1E,KAAA,0BAAA0E,WAAA,CAAAzE,MAAA,OAAAyE,WAAA,CAAAzE,MAAA,SAAA0E,OAAA,CAAAzE,eAAA,CAAAwE,WAAA,CAAAvE,QAAA,YAEF;;;;;IA9BRxB,EAAA,CAAAC,cAAA,cAAuI;IAEnID,EAAA,CAAAE,SAAA,YAA+B;IAC/BF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE5BJ,EAAA,CAAAC,cAAA,cAA2B;IAEzBD,EAAA,CAAAyB,UAAA,IAAAwE,oDAAA,kBAWM;;IAGNjG,EAAA,CAAAyB,UAAA,IAAAyE,oDAAA,kBAWM;;IACRlG,EAAA,CAAAI,YAAA,EAAM;;;;IA1BkDJ,EAAA,CAAAK,SAAA,GAAgD;IAAhDL,EAAA,CAAA2B,UAAA,YAAA3B,EAAA,CAAA4B,WAAA,OAAAuE,OAAA,CAAAR,aAAA,CAAA7D,mBAAA,QAAgD;IAclD9B,EAAA,CAAAK,SAAA,GAAgD;IAAhDL,EAAA,CAAA2B,UAAA,YAAA3B,EAAA,CAAA4B,WAAA,OAAAuE,OAAA,CAAAR,aAAA,CAAA7D,mBAAA,QAAgD;;;;;IA4BhG9B,EAAA,CAAAC,cAAA,eAAiG;IAC/FD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoD,kBAAA,MAAAgD,WAAA,CAAAC,YAAA,IAAAC,IAAA,MACF;;;;;IARJtG,EAAA,CAAAC,cAAA,eAAsF;IAElFD,EAAA,CAAAE,SAAA,QAAsD;IACxDF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAA6B;IACDD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC5DJ,EAAA,CAAAyB,UAAA,IAAA8E,0DAAA,mBAEM;IACRvG,EAAA,CAAAI,YAAA,EAAM;;;;;IAPDJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAS,UAAA,CAAA+F,OAAA,CAAA7F,cAAA,CAAAyF,WAAA,CAAAvF,YAAA,EAA8C;IAGvBb,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAA8F,WAAA,CAAAtF,cAAA,CAA4B;IACpBd,EAAA,CAAAK,SAAA,GAA6D;IAA7DL,EAAA,CAAA2B,UAAA,SAAAyE,WAAA,CAAAC,YAAA,IAAAD,WAAA,CAAAC,YAAA,CAAAvC,MAAA,KAA6D;;;;;IAZvG9D,EAAA,CAAAC,cAAA,eAAmL;IAE/KD,EAAA,CAAAE,SAAA,aAAqC;IACrCF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE5BJ,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAyB,UAAA,IAAAgF,oDAAA,mBAUM;IACRzG,EAAA,CAAAI,YAAA,EAAM;;;;IAX0CJ,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAA2B,UAAA,YAAA+E,OAAA,CAAA7C,oBAAA,CAAA6C,OAAA,CAAAf,aAAA,EAAsC;;;;;;IA3G5F3F,EAAA,CAAAC,cAAA,cAA0D;IAIhDD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9BJ,EAAA,CAAAC,cAAA,cAA4B;IACCD,EAAA,CAAAG,MAAA,GAAwE;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAG9GJ,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAC,cAAA,eAA6B;IAKrBD,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAA+B;IACAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACzCJ,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAG,MAAA,IAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAInEJ,EAAA,CAAAC,cAAA,eAAkC;IAE9BD,EAAA,CAAAE,SAAA,aAA0B;IAC5BF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAA+B;IACAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACxCJ,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAG,MAAA,IAAuF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAI9HJ,EAAA,CAAAC,cAAA,eAAsC;IAElCD,EAAA,CAAAE,SAAA,aAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAA+B;IACAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC5CJ,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAMhDJ,EAAA,CAAAyB,UAAA,KAAAkF,8CAAA,kBAWM;IAGN3G,EAAA,CAAAyB,UAAA,KAAAmF,8CAAA,oBAkCM;IAGN5G,EAAA,CAAAyB,UAAA,KAAAoF,8CAAA,kBAkBM;IAGN7G,EAAA,CAAAC,cAAA,eAA6B;IAGvBD,EAAA,CAAAE,SAAA,gBAA8B;IAMhCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,kBAAqF;IAApCD,EAAA,CAAA+B,UAAA,mBAAA+E,iEAAA;MAAA9G,EAAA,CAAAkC,aAAA,CAAA6E,IAAA;MAAA,MAAAC,OAAA,GAAAhH,EAAA,CAAAsC,aAAA;MAAA,OAAStC,EAAA,CAAAuC,WAAA,CAAAyE,OAAA,CAAAlE,UAAA,CAAAkE,OAAA,CAAArB,aAAA,CAAyB;IAAA,EAAC;IAClF3F,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAjIkBJ,EAAA,CAAAK,SAAA,GAAwE;IAAxEL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAuD,WAAA,OAAA0D,MAAA,CAAAtB,aAAA,CAAAnC,KAAA,CAAAC,MAAA,EAAAwD,MAAA,CAAAtB,aAAA,CAAAnC,KAAA,CAAAE,QAAA,EAAwE;IAGrE1D,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAgD,WAAA,aAAAiE,MAAA,CAAA9D,eAAA,CAAA8D,MAAA,CAAAtB,aAAA,EAAiD;IACjF3F,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoD,kBAAA,MAAA6D,MAAA,CAAA5D,iBAAA,CAAA4D,MAAA,CAAAtB,aAAA,OACF;IAamC3F,EAAA,CAAAK,SAAA,IAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAA2G,MAAA,CAAAtB,aAAA,CAAAuB,QAAA,CAA4B;IAU5BlH,EAAA,CAAAK,SAAA,GAAuF;IAAvFL,EAAA,CAAAM,iBAAA,CAAA2G,MAAA,CAAAtB,aAAA,CAAAhC,eAAA,IAAAsD,MAAA,CAAAtB,aAAA,CAAAhC,eAAA,CAAAC,IAAA,eAAuF;IAgB9E5D,EAAA,CAAAK,SAAA,GAA+F;IAA/FL,EAAA,CAAA2B,UAAA,SAAAsF,MAAA,CAAAtB,aAAA,CAAAC,uBAAA,IAAAqB,MAAA,CAAAtB,aAAA,CAAAC,uBAAA,CAAA9B,MAAA,KAA+F;IAc7F9D,EAAA,CAAAK,SAAA,GAAuF;IAAvFL,EAAA,CAAA2B,UAAA,SAAAsF,MAAA,CAAAtB,aAAA,CAAA7D,mBAAA,IAAAmF,MAAA,CAAAtB,aAAA,CAAA7D,mBAAA,CAAAgC,MAAA,KAAuF;IAqCtF9D,EAAA,CAAAK,SAAA,GAAkI;IAAlIL,EAAA,CAAA2B,UAAA,SAAAsF,MAAA,CAAAtB,aAAA,CAAAhC,eAAA,IAAAsD,MAAA,CAAAtB,aAAA,CAAAhC,eAAA,CAAAwD,QAAA,IAAAF,MAAA,CAAAtB,aAAA,CAAAhC,eAAA,CAAAwD,QAAA,CAAArD,MAAA,KAAkI;;;;;;IAlOvL9D,EAAA,CAAAC,cAAA,cAAgF;IAKpED,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjCJ,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAG,MAAA,wCAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAE9DJ,EAAA,CAAAC,cAAA,cAA2B;IAEuBD,EAAA,CAAA+B,UAAA,mBAAAqF,0DAAA;MAAApH,EAAA,CAAAkC,aAAA,CAAAmF,IAAA;MAAA,MAAAC,OAAA,GAAAtH,EAAA,CAAAsC,aAAA;MAAA,OAAStC,EAAA,CAAAuC,WAAA,CAAA+E,OAAA,CAAAC,WAAA,CAAY,OAAO,CAAC;IAAA,EAAC;IAC1EvH,EAAA,CAAAE,SAAA,aAA+B;IAACF,EAAA,CAAAG,MAAA,eAClC;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA6E;IAA/BD,EAAA,CAAA+B,UAAA,mBAAAyF,0DAAA;MAAAxH,EAAA,CAAAkC,aAAA,CAAAmF,IAAA;MAAA,MAAAI,OAAA,GAAAzH,EAAA,CAAAsC,aAAA;MAAA,OAAStC,EAAA,CAAAuC,WAAA,CAAAkF,OAAA,CAAAF,WAAA,CAAY,OAAO,CAAC;IAAA,EAAC;IAC1EvH,EAAA,CAAAE,SAAA,aAA4B;IAACF,EAAA,CAAAG,MAAA,eAC/B;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAEXJ,EAAA,CAAAC,cAAA,eAA6B;IACAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC5CJ,EAAA,CAAAC,cAAA,eAA4B;IAC6BD,EAAA,CAAA+B,UAAA,mBAAA2F,0DAAA;MAAA1H,EAAA,CAAAkC,aAAA,CAAAmF,IAAA;MAAA,MAAAM,OAAA,GAAA3H,EAAA,CAAAsC,aAAA;MAAA,OAAStC,EAAA,CAAAuC,WAAA,CAAAoF,OAAA,CAAAC,aAAA,CAAc,KAAK,CAAC;IAAA,EAAC;IAAC5H,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAClGJ,EAAA,CAAAC,cAAA,kBAA8F;IAAnCD,EAAA,CAAA+B,UAAA,mBAAA8F,0DAAA;MAAA7H,EAAA,CAAAkC,aAAA,CAAAmF,IAAA;MAAA,MAAAS,OAAA,GAAA9H,EAAA,CAAAsC,aAAA;MAAA,OAAStC,EAAA,CAAAuC,WAAA,CAAAuF,OAAA,CAAAF,aAAA,CAAc,SAAS,CAAC;IAAA,EAAC;IAAC5H,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC9GJ,EAAA,CAAAC,cAAA,kBAAgG;IAApCD,EAAA,CAAA+B,UAAA,mBAAAgG,0DAAA;MAAA/H,EAAA,CAAAkC,aAAA,CAAAmF,IAAA;MAAA,MAAAW,OAAA,GAAAhI,EAAA,CAAAsC,aAAA;MAAA,OAAStC,EAAA,CAAAuC,WAAA,CAAAyF,OAAA,CAAAJ,aAAA,CAAc,UAAU,CAAC;IAAA,EAAC;IAAC5H,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAQ3HJ,EAAA,CAAAyB,UAAA,KAAAwG,uCAAA,kBA6CM;IAGNjI,EAAA,CAAAyB,UAAA,KAAAyG,uCAAA,mBA4CM;IAGNlI,EAAA,CAAAyB,UAAA,KAAA0G,uCAAA,oBA2IM;IACRnI,EAAA,CAAAI,YAAA,EAAM;;;;IApQMJ,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAA8H,MAAA,CAAAC,MAAA,IAAAnB,QAAA,CAAwB;IAKlBlH,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAgD,WAAA,WAAAoF,MAAA,CAAAE,QAAA,aAAqC;IAGrCtI,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAgD,WAAA,WAAAoF,MAAA,CAAAE,QAAA,aAAqC;IAOnCtI,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAgD,WAAA,WAAAoF,MAAA,CAAAG,mBAAA,WAA8C;IAC9CvI,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAgD,WAAA,WAAAoF,MAAA,CAAAG,mBAAA,eAAkD;IAClDvI,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAgD,WAAA,WAAAoF,MAAA,CAAAG,mBAAA,gBAAmD;IAQ/DvI,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA2B,UAAA,SAAAyG,MAAA,CAAAE,QAAA,aAA0B;IAgD1BtI,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA2B,UAAA,SAAAyG,MAAA,CAAAE,QAAA,aAA0B;IA+C1BtI,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAyG,MAAA,CAAAzC,aAAA,CAAmB;;;;;IA+I3B3F,EAAA,CAAAC,cAAA,eAA4E;IACvED,EAAA,CAAAG,MAAA,6DAAsD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;AD7QjE,OAAM,MAAOoI,iBAAiB;EAe5BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,cAA8B,EAC9BC,QAAqB;IAJrB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,QAAQ,GAARA,QAAQ;IAnBlB;IACA,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,OAAO,GAAW,EAAE;IAEpB;IACA,KAAAX,MAAM,GAAY,EAAE;IACpB,KAAA1C,aAAa,GAAiB,IAAI;IAElC;IACA,KAAAsD,SAAS,GAAY,KAAK;IAC1B,KAAAzI,YAAY,GAAW,EAAE;IACzB,KAAA8H,QAAQ,GAAsB,OAAO;IACrC,KAAAC,mBAAmB,GAAmC,KAAK;EAQvD;EAEJW,QAAQA,CAAA;IACN,IAAI,CAACR,KAAK,CAACS,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAI,CAACN,QAAQ,GAAGM,MAAM,CAAC,UAAU,CAAC;MAClC,IAAI,CAACL,OAAO,GAAGK,MAAM,CAAC,SAAS,CAAC;MAEhCC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEF,MAAM,CAAC;MACzDC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,IAAI,CAACR,QAAQ,CAAC;MACvCO,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACP,OAAO,CAAC;MAErC;MACA,MAAMQ,aAAa,GAAG,EAAE;MACxB,IAAI,CAAC,IAAI,CAACT,QAAQ,EAAES,aAAa,CAACC,IAAI,CAAC,UAAU,CAAC;MAClD,IAAI,CAAC,IAAI,CAACT,OAAO,EAAEQ,aAAa,CAACC,IAAI,CAAC,SAAS,CAAC;MAEhD,IAAID,aAAa,CAAC1F,MAAM,KAAK,CAAC,EAAE;QAC9B;QACA,IAAI,CAAC4F,gBAAgB,EAAE;OACxB,MAAM;QACL;QACA,IAAI,CAAClJ,YAAY,GAAG,uBAAuBgJ,aAAa,CAACG,IAAI,CAAC,IAAI,CAAC,iCAAiC;QACpGL,OAAO,CAACM,KAAK,CAAC,qBAAqB,EAAE;UAAEb,QAAQ,EAAE,IAAI,CAACA,QAAQ;UAAEC,OAAO,EAAE,IAAI,CAACA;QAAO,CAAE,CAAC;QAExF;QACA,MAAMa,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;QAChCV,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEM,GAAG,CAAC;QAEhC;QACA,MAAMI,SAAS,GAAG,IAAIC,eAAe,CAACJ,MAAM,CAACC,QAAQ,CAACI,MAAM,CAAC;QAC7D,MAAMC,SAAS,GAA2B,EAAE;QAC5CH,SAAS,CAACI,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;UAC/BH,SAAS,CAACG,GAAG,CAAC,GAAGD,KAAK;QACxB,CAAC,CAAC;QACFhB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEa,SAAS,CAAC;;IAE7C,CAAC,CAAC;EACJ;EAEAV,gBAAgBA,CAAA;IACd,IAAI,CAACT,SAAS,GAAG,IAAI;IACrB,IAAI,CAACzI,YAAY,GAAG,EAAE;IAEtB,MAAMgK,OAAO,GAAqB,IAAI,CAAC5B,cAAc,CAAC6B,6BAA6B,CACjF,IAAI,CAAC1B,QAAQ,EACb,CAAC,IAAI,CAACC,OAAO,CAAC,CACf;IAEDM,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEiB,OAAO,CAAC;IAEjD,IAAI,CAAC5B,cAAc,CAAC8B,SAAS,CAACF,OAAO,CAAC,CAACpB,SAAS,CAAC;MAC/CuB,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC3B,SAAS,GAAG,KAAK;QACtBK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEqB,QAAQ,CAAC;QAE5C,IAAIA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIF,QAAQ,CAACG,IAAI,CAAC1C,MAAM,IAAIuC,QAAQ,CAACG,IAAI,CAAC1C,MAAM,CAACvE,MAAM,GAAG,CAAC,EAAE;UACtFwF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEqB,QAAQ,CAACG,IAAI,CAAC1C,MAAM,CAAC;UAClD,IAAI,CAACA,MAAM,GAAGuC,QAAQ,CAACG,IAAI,CAAC1C,MAAM;UAElC;UACA,IAAI,IAAI,CAACA,MAAM,CAACvE,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC6B,aAAa,GAAG,IAAI,CAAC0C,MAAM,CAAC,CAAC,CAAC;;UAGrC;UACA,IAAI,CAAC2C,iBAAiB,EAAE;SACzB,MAAM;UACL1B,OAAO,CAACM,KAAK,CAAC,wDAAwD,CAAC;UACvEN,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEqB,QAAQ,CAACC,MAAM,CAACC,OAAO,CAAC;UAChExB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEqB,QAAQ,CAACG,IAAI,CAAC1C,MAAM,CAAC;UAE1D,IAAI,CAAC7H,YAAY,GAAG,0BAA0B;UAC9C,IAAIoK,QAAQ,CAACC,MAAM,CAACI,QAAQ,IAAIL,QAAQ,CAACC,MAAM,CAACI,QAAQ,CAACnH,MAAM,GAAG,CAAC,EAAE;YACnEwF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqB,QAAQ,CAACC,MAAM,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC;YAC3E,IAAI,CAAC1K,YAAY,GAAGoK,QAAQ,CAACC,MAAM,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACDtB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAI,CAACzI,YAAY,GAAG,mEAAmE;QACvF8I,OAAO,CAACM,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAE7C;QACA,IAAIA,KAAK,CAACA,KAAK,EAAE;UACfN,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEK,KAAK,CAACA,KAAK,CAAC;;QAE5C,IAAIA,KAAK,CAACsB,OAAO,EAAE;UACjB5B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEK,KAAK,CAACsB,OAAO,CAAC;;QAE9C,IAAItB,KAAK,CAACuB,MAAM,EAAE;UAChB7B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEK,KAAK,CAACuB,MAAM,CAAC;;MAE9C;KACD,CAAC;EACJ;EAEA;EACAH,iBAAiBA,CAAA;IACf,IAAI,CAAC3C,MAAM,CAAC+C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC7H,KAAK,CAACC,MAAM,GAAG6H,CAAC,CAAC9H,KAAK,CAACC,MAAM,CAAC;EAC7D;EAEAQ,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACsE,mBAAmB,KAAK,KAAK,EAAE;MACtC,OAAO,IAAI,CAACF,MAAM;;IAGpB,OAAO,IAAI,CAACA,MAAM,CAACkD,MAAM,CAACC,KAAK,IAAG;MAChC,MAAMrI,eAAe,GAAG,IAAI,CAACA,eAAe,CAACqI,KAAK,CAAC;MACnD,OAAQ,IAAI,CAACjD,mBAAmB,KAAK,UAAU,IAAIpF,eAAe,IAC1D,IAAI,CAACoF,mBAAmB,KAAK,SAAS,IAAI,CAACpF,eAAgB;IACrE,CAAC,CAAC;EACJ;EAEAyE,aAAaA,CAAC6D,SAAyC;IACrD,IAAI,CAAClD,mBAAmB,GAAGkD,SAAS;EACtC;EAEAlE,WAAWA,CAACmE,IAAuB;IACjC,IAAI,CAACpD,QAAQ,GAAGoD,IAAI;EACtB;EAEA;EACAlJ,WAAWA,CAACgJ,KAAY;IACtB,IAAI,CAAC7F,aAAa,GAAG6F,KAAK;EAC5B;EAEAtI,UAAUA,CAACsI,KAAY;IACrB,OAAO,IAAI,CAAC7F,aAAa,KAAK6F,KAAK;EACrC;EAEAG,MAAMA,CAAA;IACJ,IAAI,CAAChD,MAAM,CAACiD,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEA9I,UAAUA,CAAC0I,KAAY;IACrBlC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEiC,KAAK,CAACxC,OAAO,CAAC;IAEzE;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACzI,YAAY,GAAG,EAAE;IAEtB;IACA,IAAI,CAACqI,cAAc,CAACgD,gBAAgB,CAAC,CAACL,KAAK,CAACxC,OAAO,CAAC,CAAC,CAACI,SAAS,CAAC;MAC9DuB,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC3B,SAAS,GAAG,KAAK;QAEtB,IAAI2B,QAAQ,IAAIA,QAAQ,CAACkB,aAAa,IAAIlB,QAAQ,CAACkB,aAAa,CAACf,IAAI,EAAE;UACrE,MAAMgB,aAAa,GAAGnB,QAAQ,CAACkB,aAAa,CAACf,IAAI,CAACgB,aAAa;UAC/DzC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEwC,aAAa,CAAC;UAEvE;UACA;UACA,IAAI,CAACpD,MAAM,CAACiD,QAAQ,CAAC,CAAC,sBAAsB,CAAC,EAAE;YAC7CzC,WAAW,EAAE;cACX6C,QAAQ,EAAER,KAAK,CAACxC,OAAO;cACvB+C,aAAa,EAAEA,aAAa;cAC5BhD,QAAQ,EAAE,IAAI,CAACA,QAAQ;cACvBkD,WAAW,EAAE;;WAEhB,CAAC;UAEF;UACA,IAAI,CAACnD,QAAQ,CAACoD,IAAI,CAAC,iCAAiC,EAAE,QAAQ,EAAE;YAC9DC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;SACH,MAAM;UACL,IAAI,CAAC5L,YAAY,GAAG,iEAAiE;UACrF8I,OAAO,CAACM,KAAK,CAAC,yCAAyC,EAAEgB,QAAQ,CAAC;UAElE;UACA,IAAI,CAAC9B,QAAQ,CAACoD,IAAI,CAAC,IAAI,CAAC1L,YAAY,EAAE,QAAQ,EAAE;YAC9C2L,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,gBAAgB;WAC9B,CAAC;;MAEN,CAAC;MACDxC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAI,CAACzI,YAAY,GAAG,kFAAkF;QACtG8I,OAAO,CAACM,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QAEnD;QACA,IAAI,CAACd,QAAQ,CAACoD,IAAI,CAAC,IAAI,CAAC1L,YAAY,EAAE,QAAQ,EAAE;UAC9C2L,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;QAEF;QACA,IAAI,CAACzD,MAAM,CAACiD,QAAQ,CAAC,CAAC,sBAAsB,CAAC,EAAE;UAC7CzC,WAAW,EAAE;YACX6C,QAAQ,EAAER,KAAK,CAACxC,OAAO;YACvBD,QAAQ,EAAE,IAAI,CAACA;;SAElB,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EACA5F,eAAeA,CAACqI,KAAY;IAC1B,IAAI,CAACA,KAAK,CAAC5F,uBAAuB,IAAI4F,KAAK,CAAC5F,uBAAuB,CAAC9B,MAAM,KAAK,CAAC,EAAE;MAChF,OAAO,KAAK;;IAGd,OAAO0H,KAAK,CAAC5F,uBAAuB,CAACyG,IAAI,CAACC,SAAS,IACjDA,SAAS,CAAC/G,IAAI,KAAKzF,eAAe,CAACyM,QAAQ,CAC5C;EACH;EAEAlJ,iBAAiBA,CAACmI,KAAY;IAC5B,IAAI,CAACA,KAAK,CAAC5F,uBAAuB,IAAI4F,KAAK,CAAC5F,uBAAuB,CAAC9B,MAAM,KAAK,CAAC,EAAE;MAChF,OAAO,SAAS;;IAGlB,OAAO0H,KAAK,CAAC5F,uBAAuB,CAAC,CAAC,CAAC,CAAChC,IAAI;EAC9C;EAEAN,iBAAiBA,CAACkI,KAAY;IAC5B,IAAI,CAACA,KAAK,CAAC5F,uBAAuB,IAAI4F,KAAK,CAAC5F,uBAAuB,CAAC9B,MAAM,KAAK,CAAC,EAAE;MAChF,OAAO,EAAE;;IAGX,OAAO0H,KAAK,CAAC5F,uBAAuB,CAAC,CAAC,CAAC,CAACJ,IAAI;EAC9C;EAEA;EACAP,UAAUA,CAACuG,KAAY,EAAEgB,WAAmB;IAC1C,IAAI,CAAChB,KAAK,CAAC7H,eAAe,IAAI,CAAC6H,KAAK,CAAC7H,eAAe,CAACwD,QAAQ,EAAE;MAC7D,OAAO,KAAK;;IAGd,OAAOqE,KAAK,CAAC7H,eAAe,CAACwD,QAAQ,CAACkF,IAAI,CAACI,OAAO,IAChDA,OAAO,CAAC3L,cAAc,CAAC4L,WAAW,EAAE,CAACC,QAAQ,CAACH,WAAW,CAACE,WAAW,EAAE,CAAC,CACzE;EACH;EAEA7I,oBAAoBA,CAAC2H,KAAY;IAC/B,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAAC7H,eAAe,IAAI,CAAC6H,KAAK,CAAC7H,eAAe,CAACwD,QAAQ,EAAE;MACvE,OAAO,EAAE;;IAGX;IACA;IACA,MAAMyF,cAAc,GAAG,IAAIC,GAAG,EAAmB;IAEjDrB,KAAK,CAAC7H,eAAe,CAACwD,QAAQ,CAACkD,OAAO,CAACoC,OAAO,IAAG;MAC/C;MACA,IAAI,CAACG,cAAc,CAACE,GAAG,CAACL,OAAO,CAAC3L,cAAc,CAAC,EAAE;QAC/C8L,cAAc,CAACG,GAAG,CAACN,OAAO,CAAC3L,cAAc,EAAE2L,OAAO,CAAC;;IAEvD,CAAC,CAAC;IAEF;IACA,OAAOO,KAAK,CAACC,IAAI,CAACL,cAAc,CAACM,MAAM,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACxD;EAEA;EACAC,eAAeA,CAACC,YAAoB;IAClC,MAAMC,SAAS,GAA8B;MAC3C,CAAC,EAAE,YAAY;MACf,CAAC,EAAE,YAAY;MACf,CAAC,EAAE;KACJ;IACD,OAAOA,SAAS,CAACD,YAAY,CAAC,IAAI,YAAYA,YAAY,EAAE;EAC9D;EAEA/H,sBAAsBA,CAACiI,QAAgB;IACrC,QAAQA,QAAQ;MACd,KAAKzN,eAAe,CAAC0N,KAAK;QACxB,OAAO,OAAO;MAChB,KAAK1N,eAAe,CAAC2N,OAAO;QAC1B,OAAO,SAAS;MAClB,KAAK3N,eAAe,CAACyM,QAAQ;QAC3B,OAAO,UAAU;MACnB;QACE,OAAO,cAAcgB,QAAQ,EAAE;;EAErC;EAEAG,oBAAoBA,CAACH,QAAgB;IACnC,QAAQA,QAAQ;MACd,KAAKxN,aAAa,CAAC4N,KAAK;QACtB,OAAO,OAAO;MAChB,KAAK5N,aAAa,CAAC6N,KAAK;QACtB,OAAO,OAAO;MAChB,KAAK7N,aAAa,CAAC8N,MAAM;QACvB,OAAO,QAAQ;MACjB;QACE,OAAO,kBAAkBN,QAAQ,EAAE;;EAEzC;EAEAhM,eAAeA,CAACuM,YAAoB;IAClC,MAAMC,SAAS,GAA8B;MAC3C,CAAC,EAAE,IAAI;MACP,CAAC,EAAE;KACJ;IACD,OAAOA,SAAS,CAACD,YAAY,CAAC,IAAI,EAAE;EACtC;EAEA1M,kBAAkBA,CAAC4M,eAAuB;IACxC,MAAMC,YAAY,GAA8B;MAC9C,CAAC,EAAE,iBAAiB;MACpB,CAAC,EAAE,eAAe;MAClB,CAAC,EAAE;KACJ;IACD,OAAOA,YAAY,CAACD,eAAe,CAAC,IAAI,gBAAgBA,eAAe,EAAE;EAC3E;EAEA;EACAhN,cAAcA,CAACgN,eAAuB;IACpC,MAAME,KAAK,GAA8B;MACvC,CAAC,EAAE,iBAAiB;MACpB,CAAC,EAAE,kBAAkB;MACrB,CAAC,EAAE;KACJ;IACD,OAAOA,KAAK,CAACF,eAAe,CAAC,IAAI,qBAAqB;EACxD;EAEArN,cAAcA,CAACwN,gBAAwB;IACrC,MAAMD,KAAK,GAA8B;MACvC,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE,iBAAiB;MACpB,CAAC,EAAE,iBAAiB;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE,cAAc;MACjB,CAAC,EAAE,kBAAkB,CAAC;KACvB;;IACD,OAAOA,KAAK,CAACC,gBAAgB,CAAC,IAAI,aAAa;EACjD;;;uBAhWW3F,iBAAiB,EAAAxI,EAAA,CAAAoO,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtO,EAAA,CAAAoO,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAvO,EAAA,CAAAoO,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAzO,EAAA,CAAAoO,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA3O,EAAA,CAAAoO,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjBrG,iBAAiB;MAAAsG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCf9BpP,EAAA,CAAAC,cAAA,aAAiC;UAEzBD,EAAA,CAAAG,MAAA,qBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvBJ,EAAA,CAAAC,cAAA,gBAA+C;UAAnBD,EAAA,CAAA+B,UAAA,mBAAAuN,mDAAA;YAAA,OAASD,GAAA,CAAA1D,MAAA,EAAQ;UAAA,EAAC;UAAC3L,EAAA,CAAAG,MAAA,qBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAGxEJ,EAAA,CAAAyB,UAAA,IAAA8N,gCAAA,iBAGM;UAENvP,EAAA,CAAAyB,UAAA,IAAA+N,gCAAA,iBAEM;UAENxP,EAAA,CAAAyB,UAAA,IAAAgO,gCAAA,mBAyQM;UAGNzP,EAAA,CAAAyB,UAAA,IAAAiO,gCAAA,iBAEM;UACR1P,EAAA,CAAAI,YAAA,EAAM;;;UAxREJ,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAA2B,UAAA,SAAA0N,GAAA,CAAApG,SAAA,CAAe;UAKfjJ,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAA2B,UAAA,SAAA0N,GAAA,CAAA7O,YAAA,KAAA6O,GAAA,CAAApG,SAAA,CAAgC;UAIhCjJ,EAAA,CAAAK,SAAA,GAA+C;UAA/CL,EAAA,CAAA2B,UAAA,SAAA0N,GAAA,CAAAhH,MAAA,IAAAgH,GAAA,CAAAhH,MAAA,CAAAvE,MAAA,SAAAuL,GAAA,CAAApG,SAAA,CAA+C;UA4Q/CjJ,EAAA,CAAAK,SAAA,GAAiD;UAAjDL,EAAA,CAAA2B,UAAA,SAAA0N,GAAA,CAAAhH,MAAA,IAAAgH,GAAA,CAAAhH,MAAA,CAAAvE,MAAA,WAAAuL,GAAA,CAAApG,SAAA,CAAiD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}