{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { SharedModule } from './components/shared/shared.module';\n// Material Modules\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { SearchPriceComponent } from './components/product/search-price/search-price.component';\nimport { GetOfferComponent } from './components/product/get-offer/get-offer.component';\nimport { BookingTransactionComponent } from './components/booking/booking-transaction/booking-transaction.component';\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\nimport { MainLayoutComponent } from './layout/main-layout/main-layout.component';\nimport { NavbarComponent } from './layout/navbar/navbar.component';\nimport { SidebarComponent } from './layout/sidebar/sidebar.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: AuthInterceptor,\n        multi: true\n      }],\n      imports: [BrowserModule, AppRoutingModule, FormsModule, ReactiveFormsModule, HttpClientModule, BrowserAnimationsModule,\n      // Material Modules\n      MatAutocompleteModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatButtonModule, MatIconModule, MatCardModule, MatProgressSpinnerModule, MatProgressBarModule, MatToolbarModule, MatSidenavModule, MatListModule, MatMenuModule, MatDividerModule, MatTabsModule, MatChipsModule, MatBadgeModule, MatTooltipModule, MatSnackBarModule, MatExpansionModule, MatStepperModule,\n      // Modules personnalisés\n      SharedModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, LoginComponent, SearchPriceComponent, GetOfferComponent, BookingTransactionComponent, MainLayoutComponent, NavbarComponent, SidebarComponent],\n    imports: [BrowserModule, AppRoutingModule, FormsModule, ReactiveFormsModule, HttpClientModule, BrowserAnimationsModule,\n    // Material Modules\n    MatAutocompleteModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatButtonModule, MatIconModule, MatCardModule, MatProgressSpinnerModule, MatProgressBarModule, MatToolbarModule, MatSidenavModule, MatListModule, MatMenuModule, MatDividerModule, MatTabsModule, MatChipsModule, MatBadgeModule, MatTooltipModule, MatSnackBarModule, MatExpansionModule, MatStepperModule,\n    // Modules personnalisés\n    SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "ReactiveFormsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "BrowserAnimationsModule", "SharedModule", "MatAutocompleteModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatDatepickerModule", "MatNativeDateModule", "MatButtonModule", "MatIconModule", "MatCardModule", "MatProgressSpinnerModule", "MatProgressBarModule", "MatToolbarModule", "MatSidenavModule", "MatListModule", "MatMenuModule", "MatDividerModule", "MatTabsModule", "MatChipsModule", "MatBadgeModule", "MatTooltipModule", "MatSnackBarModule", "MatExpansionModule", "MatStepperModule", "AppRoutingModule", "AppComponent", "LoginComponent", "SearchPriceComponent", "GetOfferComponent", "BookingTransactionComponent", "AuthInterceptor", "MainLayoutComponent", "NavbarComponent", "SidebarComponent", "AppModule", "bootstrap", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { SharedModule } from './components/shared/shared.module';\n\n// Material Modules\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatStepperModule } from '@angular/material/stepper';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { SearchPriceComponent } from './components/product/search-price/search-price.component';\nimport { GetOfferComponent } from './components/product/get-offer/get-offer.component';\nimport { BookingTransactionComponent } from './components/booking/booking-transaction/booking-transaction.component';\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\nimport { MainLayoutComponent } from './layout/main-layout/main-layout.component';\nimport { NavbarComponent } from './layout/navbar/navbar.component';\nimport { SidebarComponent } from './layout/sidebar/sidebar.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    SearchPriceComponent,\n    GetOfferComponent,\n    BookingTransactionComponent,\n    MainLayoutComponent,\n    NavbarComponent,\n    SidebarComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    HttpClientModule,\n    BrowserAnimationsModule,\n    // Material Modules\n    MatAutocompleteModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCardModule,\n    MatProgressSpinnerModule,\n    MatProgressBarModule,\n    MatToolbarModule,\n    MatSidenavModule,\n    MatListModule,\n    MatMenuModule,\n    MatDividerModule,\n    MatTabsModule,\n    MatChipsModule,\n    MatBadgeModule,\n    MatTooltipModule,\n    MatSnackBarModule,\n    MatExpansionModule,\n    MatStepperModule,\n    // Modules personnalisés\n    SharedModule\n  ],\n  providers: [\n    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,YAAY,QAAQ,mCAAmC;AAEhE;AACA,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,oBAAoB,QAAQ,0DAA0D;AAC/F,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,2BAA2B,QAAQ,wEAAwE;AACpH,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,mBAAmB,QAAQ,4CAA4C;AAChF,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,gBAAgB,QAAQ,oCAAoC;;AAoDrE,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRV,YAAY;IAAA;EAAA;;;iBAHb,CACT;QAAEW,OAAO,EAAEtC,iBAAiB;QAAEuC,QAAQ,EAAEP,eAAe;QAAEQ,KAAK,EAAE;MAAI,CAAE,CACvE;MAAAC,OAAA,GAnCC7C,aAAa,EACb8B,gBAAgB,EAChB7B,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChBE,uBAAuB;MACvB;MACAE,qBAAqB,EACrBC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,wBAAwB,EACxBC,oBAAoB,EACpBC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,iBAAiB,EACjBC,kBAAkB,EAClBC,gBAAgB;MAChB;MACAvB,YAAY;IAAA;EAAA;;;2EAOHkC,SAAS;IAAAM,YAAA,GAhDlBf,YAAY,EACZC,cAAc,EACdC,oBAAoB,EACpBC,iBAAiB,EACjBC,2BAA2B,EAC3BE,mBAAmB,EACnBC,eAAe,EACfC,gBAAgB;IAAAM,OAAA,GAGhB7C,aAAa,EACb8B,gBAAgB,EAChB7B,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChBE,uBAAuB;IACvB;IACAE,qBAAqB,EACrBC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,wBAAwB,EACxBC,oBAAoB,EACpBC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,iBAAiB,EACjBC,kBAAkB,EAClBC,gBAAgB;IAChB;IACAvB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}