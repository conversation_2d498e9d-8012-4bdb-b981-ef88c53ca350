{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = 'http://localhost:8080/auth';\n      this.tokenKey = 'auth_token';\n      this.userKey = 'user_info';\n      this.isAuthenticatedSubject = new BehaviorSubject(this.hasToken());\n      this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    }\n    login(credentials) {\n      return this.http.post(`${this.apiUrl}/login`, credentials).pipe(tap(response => {\n        if (response && response.body && response.body.token) {\n          this.setToken(response.body.token);\n          this.setUserInfo(response.body.userInfo);\n          this.isAuthenticatedSubject.next(true);\n        }\n      }));\n    }\n    logout() {\n      localStorage.removeItem(this.tokenKey);\n      localStorage.removeItem(this.userKey);\n      this.isAuthenticatedSubject.next(false);\n    }\n    getToken() {\n      return localStorage.getItem(this.tokenKey);\n    }\n    setToken(token) {\n      localStorage.setItem(this.tokenKey, token);\n    }\n    setUserInfo(userInfo) {\n      localStorage.setItem(this.userKey, JSON.stringify(userInfo));\n    }\n    getUserInfo() {\n      const userInfo = localStorage.getItem(this.userKey);\n      return userInfo ? JSON.parse(userInfo) : null;\n    }\n    hasToken() {\n      return !!this.getToken();\n    }\n    isAuthenticated() {\n      return this.hasToken();\n    }\n    static {\n      this.ɵfac = function AuthService_Factory(t) {\n        return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthService,\n        factory: AuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}