{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, InjectionToken, EventEmitter, Directive, Output, Input, ViewChild, ContentChildren, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Attribute, NgModule } from '@angular/core';\nimport * as i3 from '@angular/material/core';\nimport { mixinDisableRipple, mixinTabIndex, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i2 from '@angular/cdk/collections';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n\n// Increasing integer for generating unique ids for radio components.\nconst _c0 = [\"input\"];\nconst _c1 = [\"*\"];\nlet nextUniqueId = 0;\n/** Change event object emitted by radio button and radio group. */\nclass MatRadioChange {\n  constructor( /** The radio button that emits the change event. */\n  source, /** The value of the radio button. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nconst MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatRadioGroup),\n  multi: true\n};\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_RADIO_GROUP = new InjectionToken('MatRadioGroup');\nconst MAT_RADIO_DEFAULT_OPTIONS = new InjectionToken('mat-radio-default-options', {\n  providedIn: 'root',\n  factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY\n});\nfunction MAT_RADIO_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent'\n  };\n}\n/**\n * Base class with all of the `MatRadioGroup` functionality.\n * @docs-private\n */\nclass _MatRadioGroupBase {\n  /** Name of the radio button group. All radio buttons inside this group will use this name. */\n  get name() {\n    return this._name;\n  }\n  set name(value) {\n    this._name = value;\n    this._updateRadioButtonNames();\n  }\n  /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n  get labelPosition() {\n    return this._labelPosition;\n  }\n  set labelPosition(v) {\n    this._labelPosition = v === 'before' ? 'before' : 'after';\n    this._markRadiosForCheck();\n  }\n  /**\n   * Value for the radio-group. Should equal the value of the selected radio button if there is\n   * a corresponding radio button with a matching value. If there is not such a corresponding\n   * radio button, this value persists to be applied in case a new radio button is added with a\n   * matching value.\n   */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    if (this._value !== newValue) {\n      // Set this before proceeding to ensure no circular loop occurs with selection.\n      this._value = newValue;\n      this._updateSelectedRadioFromValue();\n      this._checkSelectedRadioButton();\n    }\n  }\n  _checkSelectedRadioButton() {\n    if (this._selected && !this._selected.checked) {\n      this._selected.checked = true;\n    }\n  }\n  /**\n   * The currently selected radio button. If set to a new radio button, the radio group value\n   * will be updated to match the new selected button.\n   */\n  get selected() {\n    return this._selected;\n  }\n  set selected(selected) {\n    this._selected = selected;\n    this.value = selected ? selected.value : null;\n    this._checkSelectedRadioButton();\n  }\n  /** Whether the radio group is disabled */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    this._markRadiosForCheck();\n  }\n  /** Whether the radio group is required */\n  get required() {\n    return this._required;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n    this._markRadiosForCheck();\n  }\n  constructor(_changeDetector) {\n    this._changeDetector = _changeDetector;\n    /** Selected value for the radio group. */\n    this._value = null;\n    /** The HTML name attribute applied to radio buttons in this group. */\n    this._name = `mat-radio-group-${nextUniqueId++}`;\n    /** The currently selected radio button. Should match value. */\n    this._selected = null;\n    /** Whether the `value` has been set to its initial value. */\n    this._isInitialized = false;\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    this._labelPosition = 'after';\n    /** Whether the radio group is disabled. */\n    this._disabled = false;\n    /** Whether the radio group is required. */\n    this._required = false;\n    /** The method to be called in order to update ngModel */\n    this._controlValueAccessorChangeFn = () => {};\n    /**\n     * onTouch function registered via registerOnTouch (ControlValueAccessor).\n     * @docs-private\n     */\n    this.onTouched = () => {};\n    /**\n     * Event emitted when the group value changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * a radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    this.change = new EventEmitter();\n  }\n  /**\n   * Initialize properties once content children are available.\n   * This allows us to propagate relevant attributes to associated buttons.\n   */\n  ngAfterContentInit() {\n    // Mark this component as initialized in AfterContentInit because the initial value can\n    // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n    // NgModel occurs *after* the OnInit of the MatRadioGroup.\n    this._isInitialized = true;\n    // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n    // buttons depends on it. Note that we don't clear the `value`, because the radio button\n    // may be swapped out with a similar one and there are some internal apps that depend on\n    // that behavior.\n    this._buttonChanges = this._radios.changes.subscribe(() => {\n      if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n        this._selected = null;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._buttonChanges?.unsubscribe();\n  }\n  /**\n   * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n   * radio buttons upon their blur.\n   */\n  _touch() {\n    if (this.onTouched) {\n      this.onTouched();\n    }\n  }\n  _updateRadioButtonNames() {\n    if (this._radios) {\n      this._radios.forEach(radio => {\n        radio.name = this.name;\n        radio._markForCheck();\n      });\n    }\n  }\n  /** Updates the `selected` radio button from the internal _value state. */\n  _updateSelectedRadioFromValue() {\n    // If the value already matches the selected radio, do nothing.\n    const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n    if (this._radios && !isAlreadySelected) {\n      this._selected = null;\n      this._radios.forEach(radio => {\n        radio.checked = this.value === radio.value;\n        if (radio.checked) {\n          this._selected = radio;\n        }\n      });\n    }\n  }\n  /** Dispatch change event with current selection and group value. */\n  _emitChangeEvent() {\n    if (this._isInitialized) {\n      this.change.emit(new MatRadioChange(this._selected, this._value));\n    }\n  }\n  _markRadiosForCheck() {\n    if (this._radios) {\n      this._radios.forEach(radio => radio._markForCheck());\n    }\n  }\n  /**\n   * Sets the model value. Implemented as part of ControlValueAccessor.\n   * @param value\n   */\n  writeValue(value) {\n    this.value = value;\n    this._changeDetector.markForCheck();\n  }\n  /**\n   * Registers a callback to be triggered when the model value changes.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  /**\n   * Registers a callback to be triggered when the control is touched.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  /**\n   * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n   * @param isDisabled Whether the control should be disabled.\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetector.markForCheck();\n  }\n  static {\n    this.ɵfac = function _MatRadioGroupBase_Factory(t) {\n      return new (t || _MatRadioGroupBase)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatRadioGroupBase,\n      inputs: {\n        color: \"color\",\n        name: \"name\",\n        labelPosition: \"labelPosition\",\n        value: \"value\",\n        selected: \"selected\",\n        disabled: \"disabled\",\n        required: \"required\"\n      },\n      outputs: {\n        change: \"change\"\n      }\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatRadioGroupBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    change: [{\n      type: Output\n    }],\n    color: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }]\n  });\n})();\n// Boilerplate for applying mixins to MatRadioButton.\n/** @docs-private */\nclass MatRadioButtonBase {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n}\nconst _MatRadioButtonMixinBase = mixinDisableRipple(mixinTabIndex(MatRadioButtonBase));\n/**\n * Base class with all of the `MatRadioButton` functionality.\n * @docs-private\n */\nclass _MatRadioButtonBase extends _MatRadioButtonMixinBase {\n  /** Whether this radio button is checked. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    const newCheckedState = coerceBooleanProperty(value);\n    if (this._checked !== newCheckedState) {\n      this._checked = newCheckedState;\n      if (newCheckedState && this.radioGroup && this.radioGroup.value !== this.value) {\n        this.radioGroup.selected = this;\n      } else if (!newCheckedState && this.radioGroup && this.radioGroup.value === this.value) {\n        // When unchecking the selected radio button, update the selected radio\n        // property on the group.\n        this.radioGroup.selected = null;\n      }\n      if (newCheckedState) {\n        // Notify all radio buttons with the same name to un-check.\n        this._radioDispatcher.notify(this.id, this.name);\n      }\n      this._changeDetector.markForCheck();\n    }\n  }\n  /** The value of this radio button. */\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    if (this._value !== value) {\n      this._value = value;\n      if (this.radioGroup !== null) {\n        if (!this.checked) {\n          // Update checked when the value changed to match the radio group's value\n          this.checked = this.radioGroup.value === value;\n        }\n        if (this.checked) {\n          this.radioGroup.selected = this;\n        }\n      }\n    }\n  }\n  /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n  get labelPosition() {\n    return this._labelPosition || this.radioGroup && this.radioGroup.labelPosition || 'after';\n  }\n  set labelPosition(value) {\n    this._labelPosition = value;\n  }\n  /** Whether the radio button is disabled. */\n  get disabled() {\n    return this._disabled || this.radioGroup !== null && this.radioGroup.disabled;\n  }\n  set disabled(value) {\n    this._setDisabled(coerceBooleanProperty(value));\n  }\n  /** Whether the radio button is required. */\n  get required() {\n    return this._required || this.radioGroup && this.radioGroup.required;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n  }\n  /** Theme color of the radio button. */\n  get color() {\n    // As per Material design specifications the selection control radio should use the accent color\n    // palette by default. https://material.io/guidelines/components/selection-controls.html\n    return this._color || this.radioGroup && this.radioGroup.color || this._providerOverride && this._providerOverride.color || 'accent';\n  }\n  set color(newValue) {\n    this._color = newValue;\n  }\n  /** ID of the native input element inside `<mat-radio-button>` */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  constructor(radioGroup, elementRef, _changeDetector, _focusMonitor, _radioDispatcher, animationMode, _providerOverride, tabIndex) {\n    super(elementRef);\n    this._changeDetector = _changeDetector;\n    this._focusMonitor = _focusMonitor;\n    this._radioDispatcher = _radioDispatcher;\n    this._providerOverride = _providerOverride;\n    this._uniqueId = `mat-radio-${++nextUniqueId}`;\n    /** The unique ID for the radio button. */\n    this.id = this._uniqueId;\n    /**\n     * Event emitted when the checked state of this radio button changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * the radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    this.change = new EventEmitter();\n    /** Whether this radio is checked. */\n    this._checked = false;\n    /** Value assigned to this radio. */\n    this._value = null;\n    /** Unregister function for _radioDispatcher */\n    this._removeUniqueSelectionListener = () => {};\n    // Assertions. Ideally these should be stripped out by the compiler.\n    // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n    this.radioGroup = radioGroup;\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    if (tabIndex) {\n      this.tabIndex = coerceNumberProperty(tabIndex, 0);\n    }\n  }\n  /** Focuses the radio button. */\n  focus(options, origin) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._inputElement, origin, options);\n    } else {\n      this._inputElement.nativeElement.focus(options);\n    }\n  }\n  /**\n   * Marks the radio button as needing checking for change detection.\n   * This method is exposed because the parent radio group will directly\n   * update bound properties of the radio button.\n   */\n  _markForCheck() {\n    // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n    // update radio button's status\n    this._changeDetector.markForCheck();\n  }\n  ngOnInit() {\n    if (this.radioGroup) {\n      // If the radio is inside a radio group, determine if it should be checked\n      this.checked = this.radioGroup.value === this._value;\n      if (this.checked) {\n        this.radioGroup.selected = this;\n      }\n      // Copy name from parent radio group\n      this.name = this.radioGroup.name;\n    }\n    this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n      if (id !== this.id && name === this.name) {\n        this.checked = false;\n      }\n    });\n  }\n  ngDoCheck() {\n    this._updateTabIndex();\n  }\n  ngAfterViewInit() {\n    this._updateTabIndex();\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n      if (!focusOrigin && this.radioGroup) {\n        this.radioGroup._touch();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._removeUniqueSelectionListener();\n  }\n  /** Dispatch change event with current value. */\n  _emitChangeEvent() {\n    this.change.emit(new MatRadioChange(this, this._value));\n  }\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  _onInputClick(event) {\n    // We have to stop propagation for click events on the visual hidden input element.\n    // By default, when a user clicks on a label element, a generated click event will be\n    // dispatched on the associated input element. Since we are using a label element as our\n    // root container, the click event on the `radio-button` will be executed twice.\n    // The real click event will bubble up, and the generated click event also tries to bubble up.\n    // This will lead to multiple click events.\n    // Preventing bubbling for the second event will solve that issue.\n    event.stopPropagation();\n  }\n  /** Triggered when the radio button receives an interaction from the user. */\n  _onInputInteraction(event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n    if (!this.checked && !this.disabled) {\n      const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n      this.checked = true;\n      this._emitChangeEvent();\n      if (this.radioGroup) {\n        this.radioGroup._controlValueAccessorChangeFn(this.value);\n        if (groupValueChanged) {\n          this.radioGroup._emitChangeEvent();\n        }\n      }\n    }\n  }\n  /** Triggered when the user clicks on the touch target. */\n  _onTouchTargetClick(event) {\n    this._onInputInteraction(event);\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n  /** Sets the disabled state and marks for check if a change occurred. */\n  _setDisabled(value) {\n    if (this._disabled !== value) {\n      this._disabled = value;\n      this._changeDetector.markForCheck();\n    }\n  }\n  /** Gets the tabindex for the underlying input element. */\n  _updateTabIndex() {\n    const group = this.radioGroup;\n    let value;\n    // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n    // necessary, because the browser handles the tab order for inputs inside a group automatically,\n    // but we need an explicitly higher tabindex for the selected button in order for things like\n    // the focus trap to pick it up correctly.\n    if (!group || !group.selected || this.disabled) {\n      value = this.tabIndex;\n    } else {\n      value = group.selected === this ? this.tabIndex : -1;\n    }\n    if (value !== this._previousTabIndex) {\n      // We have to set the tabindex directly on the DOM node, because it depends on\n      // the selected state which is prone to \"changed after checked errors\".\n      const input = this._inputElement?.nativeElement;\n      if (input) {\n        input.setAttribute('tabindex', value + '');\n        this._previousTabIndex = value;\n      }\n    }\n  }\n  static {\n    this.ɵfac = function _MatRadioButtonBase_Factory(t) {\n      i0.ɵɵinvalidFactory();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: _MatRadioButtonBase,\n      viewQuery: function _MatRadioButtonBase_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n        }\n      },\n      inputs: {\n        id: \"id\",\n        name: \"name\",\n        ariaLabel: [\"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"],\n        checked: \"checked\",\n        value: \"value\",\n        labelPosition: \"labelPosition\",\n        disabled: \"disabled\",\n        required: \"required\",\n        color: \"color\"\n      },\n      outputs: {\n        change: \"change\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatRadioButtonBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: _MatRadioGroupBase\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.FocusMonitor\n    }, {\n      type: i2.UniqueSelectionDispatcher\n    }, {\n      type: undefined\n    }, {\n      type: undefined\n    }, {\n      type: undefined\n    }];\n  }, {\n    id: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    checked: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    _inputElement: [{\n      type: ViewChild,\n      args: ['input']\n    }]\n  });\n})();\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\nclass MatRadioGroup extends _MatRadioGroupBase {\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵMatRadioGroup_BaseFactory;\n      return function MatRadioGroup_Factory(t) {\n        return (ɵMatRadioGroup_BaseFactory || (ɵMatRadioGroup_BaseFactory = i0.ɵɵgetInheritedFactory(MatRadioGroup)))(t || MatRadioGroup);\n      };\n    }();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRadioGroup,\n      selectors: [[\"mat-radio-group\"]],\n      contentQueries: function MatRadioGroup_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatRadioButton, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._radios = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"radiogroup\", 1, \"mat-mdc-radio-group\"],\n      exportAs: [\"matRadioGroup\"],\n      features: [i0.ɵɵProvidersFeature([MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, {\n        provide: MAT_RADIO_GROUP,\n        useExisting: MatRadioGroup\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioGroup, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-radio-group',\n      exportAs: 'matRadioGroup',\n      providers: [MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, {\n        provide: MAT_RADIO_GROUP,\n        useExisting: MatRadioGroup\n      }],\n      host: {\n        'role': 'radiogroup',\n        'class': 'mat-mdc-radio-group'\n      }\n    }]\n  }], null, {\n    _radios: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatRadioButton), {\n        descendants: true\n      }]\n    }]\n  });\n})();\nclass MatRadioButton extends _MatRadioButtonBase {\n  constructor(radioGroup, elementRef, _changeDetector, _focusMonitor, _radioDispatcher, animationMode, _providerOverride, tabIndex) {\n    super(radioGroup, elementRef, _changeDetector, _focusMonitor, _radioDispatcher, animationMode, _providerOverride, tabIndex);\n  }\n  static {\n    this.ɵfac = function MatRadioButton_Factory(t) {\n      return new (t || MatRadioButton)(i0.ɵɵdirectiveInject(MAT_RADIO_GROUP, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i2.UniqueSelectionDispatcher), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_RADIO_DEFAULT_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatRadioButton,\n      selectors: [[\"mat-radio-button\"]],\n      hostAttrs: [1, \"mat-mdc-radio-button\"],\n      hostVars: 15,\n      hostBindings: function MatRadioButton_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatRadioButton_focus_HostBindingHandler() {\n            return ctx._inputElement.nativeElement.focus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n          i0.ɵɵclassProp(\"mat-primary\", ctx.color === \"primary\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"mat-mdc-radio-checked\", ctx.checked)(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        disableRipple: \"disableRipple\",\n        tabIndex: \"tabIndex\"\n      },\n      exportAs: [\"matRadioButton\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 13,\n      vars: 17,\n      consts: [[1, \"mdc-form-field\"], [\"formField\", \"\"], [1, \"mdc-radio\"], [1, \"mat-mdc-radio-touch-target\", 3, \"click\"], [\"type\", \"radio\", 1, \"mdc-radio__native-control\", 3, \"id\", \"checked\", \"disabled\", \"required\", \"change\"], [\"input\", \"\"], [1, \"mdc-radio__background\"], [1, \"mdc-radio__outer-circle\"], [1, \"mdc-radio__inner-circle\"], [\"mat-ripple\", \"\", 1, \"mat-radio-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mat-ripple-element\", \"mat-radio-persistent-ripple\"], [1, \"mdc-label\", 3, \"for\"]],\n      template: function MatRadioButton_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0, 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵlistener(\"click\", function MatRadioButton_Template_div_click_3_listener($event) {\n            return ctx._onTouchTargetClick($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"input\", 4, 5);\n          i0.ɵɵlistener(\"change\", function MatRadioButton_Template_input_change_4_listener($event) {\n            return ctx._onInputInteraction($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵelement(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 9);\n          i0.ɵɵelement(10, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"label\", 11);\n          i0.ɵɵprojection(12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(1);\n          i0.ɵɵclassProp(\"mdc-form-field--align-end\", ctx.labelPosition == \"before\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"mdc-radio--disabled\", ctx.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"id\", ctx.inputId)(\"checked\", ctx.checked)(\"disabled\", ctx.disabled)(\"required\", ctx.required);\n          i0.ɵɵattribute(\"name\", ctx.name)(\"value\", ctx.value)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"matRippleTrigger\", _r0)(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"for\", ctx.inputId);\n        }\n      },\n      dependencies: [i3.MatRipple],\n      styles: [\".mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mat-mdc-radio-button{--mdc-radio-disabled-selected-icon-opacity:0.38;--mdc-radio-disabled-unselected-icon-opacity:0.38;--mdc-radio-state-layer-size:40px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioButton, [{\n    type: Component,\n    args: [{\n      selector: 'mat-radio-button',\n      host: {\n        'class': 'mat-mdc-radio-button',\n        '[attr.id]': 'id',\n        '[class.mat-primary]': 'color === \"primary\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class.mat-mdc-radio-checked]': 'checked',\n        '[class._mat-animation-noopable]': '_noopAnimations',\n        // Needs to be removed since it causes some a11y issues (see #21266).\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.aria-describedby]': 'null',\n        // Note: under normal conditions focus shouldn't land on this element, however it may be\n        // programmatically set, for example inside of a focus trap, in this case we want to forward\n        // the focus to the native element.\n        '(focus)': '_inputElement.nativeElement.focus()'\n      },\n      inputs: ['disableRipple', 'tabIndex'],\n      exportAs: 'matRadioButton',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"mdc-form-field\\\" #formField\\n     [class.mdc-form-field--align-end]=\\\"labelPosition == 'before'\\\">\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-radio-touch-target\\\" (click)=\\\"_onTouchTargetClick($event)\\\"></div>\\n    <input #input class=\\\"mdc-radio__native-control\\\" type=\\\"radio\\\"\\n           [id]=\\\"inputId\\\"\\n           [checked]=\\\"checked\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [required]=\\\"required\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           (change)=\\\"_onInputInteraction($event)\\\">\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n    <div mat-ripple class=\\\"mat-radio-ripple mat-mdc-focus-indicator\\\"\\n         [matRippleTrigger]=\\\"formField\\\"\\n         [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n         [matRippleCentered]=\\\"true\\\">\\n      <div class=\\\"mat-ripple-element mat-radio-persistent-ripple\\\"></div>\\n    </div>\\n  </div>\\n  <label class=\\\"mdc-label\\\" [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mat-mdc-radio-button{--mdc-radio-disabled-selected-icon-opacity:0.38;--mdc-radio-disabled-unselected-icon-opacity:0.38;--mdc-radio-state-layer-size:40px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}\"]\n    }]\n  }], function () {\n    return [{\n      type: MatRadioGroup,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RADIO_GROUP]\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.FocusMonitor\n    }, {\n      type: i2.UniqueSelectionDispatcher\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RADIO_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }];\n  }, null);\n})();\nclass MatRadioModule {\n  static {\n    this.ɵfac = function MatRadioModule_Factory(t) {\n      return new (t || MatRadioModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatRadioModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CommonModule, MatRippleModule, MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CommonModule, MatRippleModule],\n      exports: [MatCommonModule, MatRadioGroup, MatRadioButton],\n      declarations: [MatRadioGroup, MatRadioButton]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_RADIO_DEFAULT_OPTIONS, MAT_RADIO_DEFAULT_OPTIONS_FACTORY, MAT_RADIO_GROUP, MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, MatRadioButton, MatRadioChange, MatRadioGroup, MatRadioModule, _MatRadioButtonBase, _MatRadioGroupBase };", "map": {"version": 3, "names": ["i0", "forwardRef", "InjectionToken", "EventEmitter", "Directive", "Output", "Input", "ViewChild", "ContentChildren", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "Attribute", "NgModule", "i3", "mixinDisableRipple", "mixinTabIndex", "MatCommonModule", "MatRippleModule", "i1", "coerceBooleanProperty", "coerceNumberProperty", "i2", "ANIMATION_MODULE_TYPE", "NG_VALUE_ACCESSOR", "CommonModule", "_c0", "_c1", "nextUniqueId", "MatRadioChange", "constructor", "source", "value", "MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR", "provide", "useExisting", "MatRadioGroup", "multi", "MAT_RADIO_GROUP", "MAT_RADIO_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_RADIO_DEFAULT_OPTIONS_FACTORY", "color", "_MatRadioGroupBase", "name", "_name", "_updateRadioButtonNames", "labelPosition", "_labelPosition", "v", "_markRadiosForCheck", "_value", "newValue", "_updateSelectedRadioFromValue", "_checkSelectedRadioButton", "_selected", "checked", "selected", "disabled", "_disabled", "required", "_required", "_changeDetector", "_isInitialized", "_controlValueAccessorChangeFn", "onTouched", "change", "ngAfterContentInit", "_buttonChanges", "_radios", "changes", "subscribe", "find", "radio", "ngOnDestroy", "unsubscribe", "_touch", "for<PERSON>ach", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "isAlreadySelected", "_emitChangeEvent", "emit", "writeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ɵfac", "_MatRadioGroupBase_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "ɵdir", "ɵɵdefineDirective", "type", "inputs", "outputs", "ngDevMode", "ɵsetClassMetadata", "MatRadioButtonBase", "_elementRef", "_MatRadioButtonMixinBase", "_MatRadioButtonBase", "_checked", "newCheckedState", "radioGroup", "_radioDispatcher", "notify", "id", "_setDisabled", "_color", "_providerOverride", "inputId", "_uniqueId", "elementRef", "_focusMonitor", "animationMode", "tabIndex", "_removeUniqueSelectionListener", "_noopAnimations", "focus", "options", "origin", "focusVia", "_inputElement", "nativeElement", "ngOnInit", "listen", "ngDoCheck", "_updateTabIndex", "ngAfterViewInit", "monitor", "<PERSON><PERSON><PERSON><PERSON>", "stopMonitoring", "_isRippleDisabled", "disable<PERSON><PERSON><PERSON>", "_onInputClick", "event", "stopPropagation", "_onInputInteraction", "groupValueChanged", "_onTouchTargetClick", "group", "_previousTabIndex", "input", "setAttribute", "_MatRadioButtonBase_Factory", "ɵɵinvalidFactory", "viewQuery", "_MatRadioButtonBase_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "features", "ɵɵInheritDefinitionFeature", "ElementRef", "FocusMonitor", "UniqueSelectionDispatcher", "undefined", "args", "ɵMatRadioGroup_BaseFactory", "MatRadioGroup_Factory", "ɵɵgetInheritedFactory", "selectors", "contentQueries", "MatRadioGroup_ContentQueries", "dirIndex", "ɵɵcontentQuery", "MatRadioButton", "hostAttrs", "exportAs", "ɵɵProvidersFeature", "selector", "providers", "host", "descendants", "MatRadioButton_Factory", "ɵɵinjectAttribute", "ɵcmp", "ɵɵdefineComponent", "hostVars", "hostBindings", "MatRadioButton_HostBindings", "ɵɵlistener", "MatRadioButton_focus_HostBindingHandler", "ɵɵattribute", "ɵɵclassProp", "ngContentSelectors", "decls", "vars", "consts", "template", "MatRadioButton_Template", "ɵɵprojectionDef", "ɵɵelementStart", "MatRadio<PERSON><PERSON><PERSON>_Template_div_click_3_listener", "$event", "ɵɵelementEnd", "MatRadio<PERSON><PERSON>on_Template_input_change_4_listener", "ɵɵelement", "ɵɵprojection", "_r0", "ɵɵreference", "ɵɵadvance", "ɵɵproperty", "dependencies", "<PERSON><PERSON><PERSON><PERSON>", "styles", "encapsulation", "changeDetection", "None", "OnPush", "decorators", "MatRadioModule", "MatRadioModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Desktop/e-tourism/angular/e-tourism-front/node_modules/@angular/material/fesm2022/radio.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { forwardRef, InjectionToken, EventEmitter, Directive, Output, Input, ViewChild, ContentChildren, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Attribute, NgModule } from '@angular/core';\nimport * as i3 from '@angular/material/core';\nimport { mixinDisableRipple, mixinTabIndex, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i2 from '@angular/cdk/collections';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n\n// Increasing integer for generating unique ids for radio components.\nlet nextUniqueId = 0;\n/** Change event object emitted by radio button and radio group. */\nclass MatRadioChange {\n    constructor(\n    /** The radio button that emits the change event. */\n    source, \n    /** The value of the radio button. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nconst MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatRadioGroup),\n    multi: true,\n};\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_RADIO_GROUP = new InjectionToken('MatRadioGroup');\nconst MAT_RADIO_DEFAULT_OPTIONS = new InjectionToken('mat-radio-default-options', {\n    providedIn: 'root',\n    factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY,\n});\nfunction MAT_RADIO_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        color: 'accent',\n    };\n}\n/**\n * Base class with all of the `MatRadioGroup` functionality.\n * @docs-private\n */\nclass _MatRadioGroupBase {\n    /** Name of the radio button group. All radio buttons inside this group will use this name. */\n    get name() {\n        return this._name;\n    }\n    set name(value) {\n        this._name = value;\n        this._updateRadioButtonNames();\n    }\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    get labelPosition() {\n        return this._labelPosition;\n    }\n    set labelPosition(v) {\n        this._labelPosition = v === 'before' ? 'before' : 'after';\n        this._markRadiosForCheck();\n    }\n    /**\n     * Value for the radio-group. Should equal the value of the selected radio button if there is\n     * a corresponding radio button with a matching value. If there is not such a corresponding\n     * radio button, this value persists to be applied in case a new radio button is added with a\n     * matching value.\n     */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        if (this._value !== newValue) {\n            // Set this before proceeding to ensure no circular loop occurs with selection.\n            this._value = newValue;\n            this._updateSelectedRadioFromValue();\n            this._checkSelectedRadioButton();\n        }\n    }\n    _checkSelectedRadioButton() {\n        if (this._selected && !this._selected.checked) {\n            this._selected.checked = true;\n        }\n    }\n    /**\n     * The currently selected radio button. If set to a new radio button, the radio group value\n     * will be updated to match the new selected button.\n     */\n    get selected() {\n        return this._selected;\n    }\n    set selected(selected) {\n        this._selected = selected;\n        this.value = selected ? selected.value : null;\n        this._checkSelectedRadioButton();\n    }\n    /** Whether the radio group is disabled */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._markRadiosForCheck();\n    }\n    /** Whether the radio group is required */\n    get required() {\n        return this._required;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n        this._markRadiosForCheck();\n    }\n    constructor(_changeDetector) {\n        this._changeDetector = _changeDetector;\n        /** Selected value for the radio group. */\n        this._value = null;\n        /** The HTML name attribute applied to radio buttons in this group. */\n        this._name = `mat-radio-group-${nextUniqueId++}`;\n        /** The currently selected radio button. Should match value. */\n        this._selected = null;\n        /** Whether the `value` has been set to its initial value. */\n        this._isInitialized = false;\n        /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n        this._labelPosition = 'after';\n        /** Whether the radio group is disabled. */\n        this._disabled = false;\n        /** Whether the radio group is required. */\n        this._required = false;\n        /** The method to be called in order to update ngModel */\n        this._controlValueAccessorChangeFn = () => { };\n        /**\n         * onTouch function registered via registerOnTouch (ControlValueAccessor).\n         * @docs-private\n         */\n        this.onTouched = () => { };\n        /**\n         * Event emitted when the group value changes.\n         * Change events are only emitted when the value changes due to user interaction with\n         * a radio button (the same behavior as `<input type-\"radio\">`).\n         */\n        this.change = new EventEmitter();\n    }\n    /**\n     * Initialize properties once content children are available.\n     * This allows us to propagate relevant attributes to associated buttons.\n     */\n    ngAfterContentInit() {\n        // Mark this component as initialized in AfterContentInit because the initial value can\n        // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n        // NgModel occurs *after* the OnInit of the MatRadioGroup.\n        this._isInitialized = true;\n        // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n        // buttons depends on it. Note that we don't clear the `value`, because the radio button\n        // may be swapped out with a similar one and there are some internal apps that depend on\n        // that behavior.\n        this._buttonChanges = this._radios.changes.subscribe(() => {\n            if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n                this._selected = null;\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._buttonChanges?.unsubscribe();\n    }\n    /**\n     * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n     * radio buttons upon their blur.\n     */\n    _touch() {\n        if (this.onTouched) {\n            this.onTouched();\n        }\n    }\n    _updateRadioButtonNames() {\n        if (this._radios) {\n            this._radios.forEach(radio => {\n                radio.name = this.name;\n                radio._markForCheck();\n            });\n        }\n    }\n    /** Updates the `selected` radio button from the internal _value state. */\n    _updateSelectedRadioFromValue() {\n        // If the value already matches the selected radio, do nothing.\n        const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n        if (this._radios && !isAlreadySelected) {\n            this._selected = null;\n            this._radios.forEach(radio => {\n                radio.checked = this.value === radio.value;\n                if (radio.checked) {\n                    this._selected = radio;\n                }\n            });\n        }\n    }\n    /** Dispatch change event with current selection and group value. */\n    _emitChangeEvent() {\n        if (this._isInitialized) {\n            this.change.emit(new MatRadioChange(this._selected, this._value));\n        }\n    }\n    _markRadiosForCheck() {\n        if (this._radios) {\n            this._radios.forEach(radio => radio._markForCheck());\n        }\n    }\n    /**\n     * Sets the model value. Implemented as part of ControlValueAccessor.\n     * @param value\n     */\n    writeValue(value) {\n        this.value = value;\n        this._changeDetector.markForCheck();\n    }\n    /**\n     * Registers a callback to be triggered when the model value changes.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnChange(fn) {\n        this._controlValueAccessorChangeFn = fn;\n    }\n    /**\n     * Registers a callback to be triggered when the control is touched.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    /**\n     * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n     * @param isDisabled Whether the control should be disabled.\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetector.markForCheck();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatRadioGroupBase, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatRadioGroupBase, inputs: { color: \"color\", name: \"name\", labelPosition: \"labelPosition\", value: \"value\", selected: \"selected\", disabled: \"disabled\", required: \"required\" }, outputs: { change: \"change\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatRadioGroupBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }]; }, propDecorators: { change: [{\n                type: Output\n            }], color: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], labelPosition: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], selected: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }] } });\n// Boilerplate for applying mixins to MatRadioButton.\n/** @docs-private */\nclass MatRadioButtonBase {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}\nconst _MatRadioButtonMixinBase = mixinDisableRipple(mixinTabIndex(MatRadioButtonBase));\n/**\n * Base class with all of the `MatRadioButton` functionality.\n * @docs-private\n */\nclass _MatRadioButtonBase extends _MatRadioButtonMixinBase {\n    /** Whether this radio button is checked. */\n    get checked() {\n        return this._checked;\n    }\n    set checked(value) {\n        const newCheckedState = coerceBooleanProperty(value);\n        if (this._checked !== newCheckedState) {\n            this._checked = newCheckedState;\n            if (newCheckedState && this.radioGroup && this.radioGroup.value !== this.value) {\n                this.radioGroup.selected = this;\n            }\n            else if (!newCheckedState && this.radioGroup && this.radioGroup.value === this.value) {\n                // When unchecking the selected radio button, update the selected radio\n                // property on the group.\n                this.radioGroup.selected = null;\n            }\n            if (newCheckedState) {\n                // Notify all radio buttons with the same name to un-check.\n                this._radioDispatcher.notify(this.id, this.name);\n            }\n            this._changeDetector.markForCheck();\n        }\n    }\n    /** The value of this radio button. */\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        if (this._value !== value) {\n            this._value = value;\n            if (this.radioGroup !== null) {\n                if (!this.checked) {\n                    // Update checked when the value changed to match the radio group's value\n                    this.checked = this.radioGroup.value === value;\n                }\n                if (this.checked) {\n                    this.radioGroup.selected = this;\n                }\n            }\n        }\n    }\n    /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n    get labelPosition() {\n        return this._labelPosition || (this.radioGroup && this.radioGroup.labelPosition) || 'after';\n    }\n    set labelPosition(value) {\n        this._labelPosition = value;\n    }\n    /** Whether the radio button is disabled. */\n    get disabled() {\n        return this._disabled || (this.radioGroup !== null && this.radioGroup.disabled);\n    }\n    set disabled(value) {\n        this._setDisabled(coerceBooleanProperty(value));\n    }\n    /** Whether the radio button is required. */\n    get required() {\n        return this._required || (this.radioGroup && this.radioGroup.required);\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n    }\n    /** Theme color of the radio button. */\n    get color() {\n        // As per Material design specifications the selection control radio should use the accent color\n        // palette by default. https://material.io/guidelines/components/selection-controls.html\n        return (this._color ||\n            (this.radioGroup && this.radioGroup.color) ||\n            (this._providerOverride && this._providerOverride.color) ||\n            'accent');\n    }\n    set color(newValue) {\n        this._color = newValue;\n    }\n    /** ID of the native input element inside `<mat-radio-button>` */\n    get inputId() {\n        return `${this.id || this._uniqueId}-input`;\n    }\n    constructor(radioGroup, elementRef, _changeDetector, _focusMonitor, _radioDispatcher, animationMode, _providerOverride, tabIndex) {\n        super(elementRef);\n        this._changeDetector = _changeDetector;\n        this._focusMonitor = _focusMonitor;\n        this._radioDispatcher = _radioDispatcher;\n        this._providerOverride = _providerOverride;\n        this._uniqueId = `mat-radio-${++nextUniqueId}`;\n        /** The unique ID for the radio button. */\n        this.id = this._uniqueId;\n        /**\n         * Event emitted when the checked state of this radio button changes.\n         * Change events are only emitted when the value changes due to user interaction with\n         * the radio button (the same behavior as `<input type-\"radio\">`).\n         */\n        this.change = new EventEmitter();\n        /** Whether this radio is checked. */\n        this._checked = false;\n        /** Value assigned to this radio. */\n        this._value = null;\n        /** Unregister function for _radioDispatcher */\n        this._removeUniqueSelectionListener = () => { };\n        // Assertions. Ideally these should be stripped out by the compiler.\n        // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n        this.radioGroup = radioGroup;\n        this._noopAnimations = animationMode === 'NoopAnimations';\n        if (tabIndex) {\n            this.tabIndex = coerceNumberProperty(tabIndex, 0);\n        }\n    }\n    /** Focuses the radio button. */\n    focus(options, origin) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._inputElement, origin, options);\n        }\n        else {\n            this._inputElement.nativeElement.focus(options);\n        }\n    }\n    /**\n     * Marks the radio button as needing checking for change detection.\n     * This method is exposed because the parent radio group will directly\n     * update bound properties of the radio button.\n     */\n    _markForCheck() {\n        // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n        // update radio button's status\n        this._changeDetector.markForCheck();\n    }\n    ngOnInit() {\n        if (this.radioGroup) {\n            // If the radio is inside a radio group, determine if it should be checked\n            this.checked = this.radioGroup.value === this._value;\n            if (this.checked) {\n                this.radioGroup.selected = this;\n            }\n            // Copy name from parent radio group\n            this.name = this.radioGroup.name;\n        }\n        this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n            if (id !== this.id && name === this.name) {\n                this.checked = false;\n            }\n        });\n    }\n    ngDoCheck() {\n        this._updateTabIndex();\n    }\n    ngAfterViewInit() {\n        this._updateTabIndex();\n        this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n            if (!focusOrigin && this.radioGroup) {\n                this.radioGroup._touch();\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._removeUniqueSelectionListener();\n    }\n    /** Dispatch change event with current value. */\n    _emitChangeEvent() {\n        this.change.emit(new MatRadioChange(this, this._value));\n    }\n    _isRippleDisabled() {\n        return this.disableRipple || this.disabled;\n    }\n    _onInputClick(event) {\n        // We have to stop propagation for click events on the visual hidden input element.\n        // By default, when a user clicks on a label element, a generated click event will be\n        // dispatched on the associated input element. Since we are using a label element as our\n        // root container, the click event on the `radio-button` will be executed twice.\n        // The real click event will bubble up, and the generated click event also tries to bubble up.\n        // This will lead to multiple click events.\n        // Preventing bubbling for the second event will solve that issue.\n        event.stopPropagation();\n    }\n    /** Triggered when the radio button receives an interaction from the user. */\n    _onInputInteraction(event) {\n        // We always have to stop propagation on the change event.\n        // Otherwise the change event, from the input element, will bubble up and\n        // emit its event object to the `change` output.\n        event.stopPropagation();\n        if (!this.checked && !this.disabled) {\n            const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n            this.checked = true;\n            this._emitChangeEvent();\n            if (this.radioGroup) {\n                this.radioGroup._controlValueAccessorChangeFn(this.value);\n                if (groupValueChanged) {\n                    this.radioGroup._emitChangeEvent();\n                }\n            }\n        }\n    }\n    /** Triggered when the user clicks on the touch target. */\n    _onTouchTargetClick(event) {\n        this._onInputInteraction(event);\n        if (!this.disabled) {\n            // Normally the input should be focused already, but if the click\n            // comes from the touch target, then we might have to focus it ourselves.\n            this._inputElement.nativeElement.focus();\n        }\n    }\n    /** Sets the disabled state and marks for check if a change occurred. */\n    _setDisabled(value) {\n        if (this._disabled !== value) {\n            this._disabled = value;\n            this._changeDetector.markForCheck();\n        }\n    }\n    /** Gets the tabindex for the underlying input element. */\n    _updateTabIndex() {\n        const group = this.radioGroup;\n        let value;\n        // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n        // necessary, because the browser handles the tab order for inputs inside a group automatically,\n        // but we need an explicitly higher tabindex for the selected button in order for things like\n        // the focus trap to pick it up correctly.\n        if (!group || !group.selected || this.disabled) {\n            value = this.tabIndex;\n        }\n        else {\n            value = group.selected === this ? this.tabIndex : -1;\n        }\n        if (value !== this._previousTabIndex) {\n            // We have to set the tabindex directly on the DOM node, because it depends on\n            // the selected state which is prone to \"changed after checked errors\".\n            const input = this._inputElement?.nativeElement;\n            if (input) {\n                input.setAttribute('tabindex', value + '');\n                this._previousTabIndex = value;\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatRadioButtonBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatRadioButtonBase, inputs: { id: \"id\", name: \"name\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], checked: \"checked\", value: \"value\", labelPosition: \"labelPosition\", disabled: \"disabled\", required: \"required\", color: \"color\" }, outputs: { change: \"change\" }, viewQueries: [{ propertyName: \"_inputElement\", first: true, predicate: [\"input\"], descendants: true }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatRadioButtonBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: _MatRadioGroupBase }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.FocusMonitor }, { type: i2.UniqueSelectionDispatcher }, { type: undefined }, { type: undefined }, { type: undefined }]; }, propDecorators: { id: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], checked: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], labelPosition: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], _inputElement: [{\n                type: ViewChild,\n                args: ['input']\n            }] } });\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\nclass MatRadioGroup extends _MatRadioGroupBase {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRadioGroup, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatRadioGroup, selector: \"mat-radio-group\", host: { attributes: { \"role\": \"radiogroup\" }, classAttribute: \"mat-mdc-radio-group\" }, providers: [\n            MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR,\n            { provide: MAT_RADIO_GROUP, useExisting: MatRadioGroup },\n        ], queries: [{ propertyName: \"_radios\", predicate: i0.forwardRef(function () { return MatRadioButton; }), descendants: true }], exportAs: [\"matRadioGroup\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRadioGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-radio-group',\n                    exportAs: 'matRadioGroup',\n                    providers: [\n                        MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR,\n                        { provide: MAT_RADIO_GROUP, useExisting: MatRadioGroup },\n                    ],\n                    host: {\n                        'role': 'radiogroup',\n                        'class': 'mat-mdc-radio-group',\n                    },\n                }]\n        }], propDecorators: { _radios: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatRadioButton), { descendants: true }]\n            }] } });\nclass MatRadioButton extends _MatRadioButtonBase {\n    constructor(radioGroup, elementRef, _changeDetector, _focusMonitor, _radioDispatcher, animationMode, _providerOverride, tabIndex) {\n        super(radioGroup, elementRef, _changeDetector, _focusMonitor, _radioDispatcher, animationMode, _providerOverride, tabIndex);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRadioButton, deps: [{ token: MAT_RADIO_GROUP, optional: true }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.FocusMonitor }, { token: i2.UniqueSelectionDispatcher }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_RADIO_DEFAULT_OPTIONS, optional: true }, { token: 'tabindex', attribute: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatRadioButton, selector: \"mat-radio-button\", inputs: { disableRipple: \"disableRipple\", tabIndex: \"tabIndex\" }, host: { listeners: { \"focus\": \"_inputElement.nativeElement.focus()\" }, properties: { \"attr.id\": \"id\", \"class.mat-primary\": \"color === \\\"primary\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class.mat-mdc-radio-checked\": \"checked\", \"class._mat-animation-noopable\": \"_noopAnimations\", \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.aria-describedby\": \"null\" }, classAttribute: \"mat-mdc-radio-button\" }, exportAs: [\"matRadioButton\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-form-field\\\" #formField\\n     [class.mdc-form-field--align-end]=\\\"labelPosition == 'before'\\\">\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-radio-touch-target\\\" (click)=\\\"_onTouchTargetClick($event)\\\"></div>\\n    <input #input class=\\\"mdc-radio__native-control\\\" type=\\\"radio\\\"\\n           [id]=\\\"inputId\\\"\\n           [checked]=\\\"checked\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [required]=\\\"required\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           (change)=\\\"_onInputInteraction($event)\\\">\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n    <div mat-ripple class=\\\"mat-radio-ripple mat-mdc-focus-indicator\\\"\\n         [matRippleTrigger]=\\\"formField\\\"\\n         [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n         [matRippleCentered]=\\\"true\\\">\\n      <div class=\\\"mat-ripple-element mat-radio-persistent-ripple\\\"></div>\\n    </div>\\n  </div>\\n  <label class=\\\"mdc-label\\\" [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mat-mdc-radio-button{--mdc-radio-disabled-selected-icon-opacity:0.38;--mdc-radio-disabled-unselected-icon-opacity:0.38;--mdc-radio-state-layer-size:40px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}\"], dependencies: [{ kind: \"directive\", type: i3.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRadioButton, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-radio-button', host: {\n                        'class': 'mat-mdc-radio-button',\n                        '[attr.id]': 'id',\n                        '[class.mat-primary]': 'color === \"primary\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class.mat-mdc-radio-checked]': 'checked',\n                        '[class._mat-animation-noopable]': '_noopAnimations',\n                        // Needs to be removed since it causes some a11y issues (see #21266).\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.aria-describedby]': 'null',\n                        // Note: under normal conditions focus shouldn't land on this element, however it may be\n                        // programmatically set, for example inside of a focus trap, in this case we want to forward\n                        // the focus to the native element.\n                        '(focus)': '_inputElement.nativeElement.focus()',\n                    }, inputs: ['disableRipple', 'tabIndex'], exportAs: 'matRadioButton', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"mdc-form-field\\\" #formField\\n     [class.mdc-form-field--align-end]=\\\"labelPosition == 'before'\\\">\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-radio-touch-target\\\" (click)=\\\"_onTouchTargetClick($event)\\\"></div>\\n    <input #input class=\\\"mdc-radio__native-control\\\" type=\\\"radio\\\"\\n           [id]=\\\"inputId\\\"\\n           [checked]=\\\"checked\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [required]=\\\"required\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           (change)=\\\"_onInputInteraction($event)\\\">\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n    <div mat-ripple class=\\\"mat-radio-ripple mat-mdc-focus-indicator\\\"\\n         [matRippleTrigger]=\\\"formField\\\"\\n         [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n         [matRippleCentered]=\\\"true\\\">\\n      <div class=\\\"mat-ripple-element mat-radio-persistent-ripple\\\"></div>\\n    </div>\\n  </div>\\n  <label class=\\\"mdc-label\\\" [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mat-mdc-radio-button{--mdc-radio-disabled-selected-icon-opacity:0.38;--mdc-radio-disabled-unselected-icon-opacity:0.38;--mdc-radio-state-layer-size:40px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}\"] }]\n        }], ctorParameters: function () { return [{ type: MatRadioGroup, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RADIO_GROUP]\n                }] }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.FocusMonitor }, { type: i2.UniqueSelectionDispatcher }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RADIO_DEFAULT_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }]; } });\n\nclass MatRadioModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRadioModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRadioModule, declarations: [MatRadioGroup, MatRadioButton], imports: [MatCommonModule, CommonModule, MatRippleModule], exports: [MatCommonModule, MatRadioGroup, MatRadioButton] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRadioModule, imports: [MatCommonModule, CommonModule, MatRippleModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatRadioModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, CommonModule, MatRippleModule],\n                    exports: [MatCommonModule, MatRadioGroup, MatRadioButton],\n                    declarations: [MatRadioGroup, MatRadioButton],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_RADIO_DEFAULT_OPTIONS, MAT_RADIO_DEFAULT_OPTIONS_FACTORY, MAT_RADIO_GROUP, MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, MatRadioButton, MatRadioChange, MatRadioGroup, MatRadioModule, _MatRadioButtonBase, _MatRadioGroupBase };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,cAAc,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC5N,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,kBAAkB,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC5G,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,OAAO,KAAKC,EAAE,MAAM,0BAA0B;AAC9C,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,YAAY,QAAQ,iBAAiB;;AAE9C;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,IAAIC,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAAA,CACX;EACAC,MAAM,EACN;EACAC,KAAK,EAAE;IACH,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sCAAsC,GAAG;EAC3CC,OAAO,EAAEV,iBAAiB;EAC1BW,WAAW,EAAEpC,UAAU,CAAC,MAAMqC,aAAa,CAAC;EAC5CC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,IAAItC,cAAc,CAAC,eAAe,CAAC;AAC3D,MAAMuC,yBAAyB,GAAG,IAAIvC,cAAc,CAAC,2BAA2B,EAAE;EAC9EwC,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF,SAASA,iCAAiCA,CAAA,EAAG;EACzC,OAAO;IACHC,KAAK,EAAE;EACX,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrB;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACb,KAAK,EAAE;IACZ,IAAI,CAACc,KAAK,GAAGd,KAAK;IAClB,IAAI,CAACe,uBAAuB,CAAC,CAAC;EAClC;EACA;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAaA,CAACE,CAAC,EAAE;IACjB,IAAI,CAACD,cAAc,GAAGC,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;IACzD,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAInB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACoB,MAAM;EACtB;EACA,IAAIpB,KAAKA,CAACqB,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACD,MAAM,KAAKC,QAAQ,EAAE;MAC1B;MACA,IAAI,CAACD,MAAM,GAAGC,QAAQ;MACtB,IAAI,CAACC,6BAA6B,CAAC,CAAC;MACpC,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpC;EACJ;EACAA,yBAAyBA,CAAA,EAAG;IACxB,IAAI,IAAI,CAACC,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACC,OAAO,EAAE;MAC3C,IAAI,CAACD,SAAS,CAACC,OAAO,GAAG,IAAI;IACjC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACF,SAAS;EACzB;EACA,IAAIE,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACF,SAAS,GAAGE,QAAQ;IACzB,IAAI,CAAC1B,KAAK,GAAG0B,QAAQ,GAAGA,QAAQ,CAAC1B,KAAK,GAAG,IAAI;IAC7C,IAAI,CAACuB,yBAAyB,CAAC,CAAC;EACpC;EACA;EACA,IAAII,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAC3B,KAAK,EAAE;IAChB,IAAI,CAAC4B,SAAS,GAAGxC,qBAAqB,CAACY,KAAK,CAAC;IAC7C,IAAI,CAACmB,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACA,IAAIU,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAC7B,KAAK,EAAE;IAChB,IAAI,CAAC8B,SAAS,GAAG1C,qBAAqB,CAACY,KAAK,CAAC;IAC7C,IAAI,CAACmB,mBAAmB,CAAC,CAAC;EAC9B;EACArB,WAAWA,CAACiC,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC;IACA,IAAI,CAACX,MAAM,GAAG,IAAI;IAClB;IACA,IAAI,CAACN,KAAK,GAAI,mBAAkBlB,YAAY,EAAG,EAAC;IAChD;IACA,IAAI,CAAC4B,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACQ,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACf,cAAc,GAAG,OAAO;IAC7B;IACA,IAAI,CAACW,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACG,6BAA6B,GAAG,MAAM,CAAE,CAAC;IAC9C;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,MAAM,CAAE,CAAC;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAIlE,YAAY,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACImE,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA;IACA,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC1B;IACA;IACA;IACA;IACA,IAAI,CAACK,cAAc,GAAG,IAAI,CAACC,OAAO,CAACC,OAAO,CAACC,SAAS,CAAC,MAAM;MACvD,IAAI,IAAI,CAACd,QAAQ,IAAI,CAAC,IAAI,CAACY,OAAO,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAChB,QAAQ,CAAC,EAAE;QACvE,IAAI,CAACF,SAAS,GAAG,IAAI;MACzB;IACJ,CAAC,CAAC;EACN;EACAmB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,cAAc,EAAEO,WAAW,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACX,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAC,CAAC;IACpB;EACJ;EACAnB,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACuB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACQ,OAAO,CAACJ,KAAK,IAAI;QAC1BA,KAAK,CAAC7B,IAAI,GAAG,IAAI,CAACA,IAAI;QACtB6B,KAAK,CAACK,aAAa,CAAC,CAAC;MACzB,CAAC,CAAC;IACN;EACJ;EACA;EACAzB,6BAA6BA,CAAA,EAAG;IAC5B;IACA,MAAM0B,iBAAiB,GAAG,IAAI,CAACxB,SAAS,KAAK,IAAI,IAAI,IAAI,CAACA,SAAS,CAACxB,KAAK,KAAK,IAAI,CAACoB,MAAM;IACzF,IAAI,IAAI,CAACkB,OAAO,IAAI,CAACU,iBAAiB,EAAE;MACpC,IAAI,CAACxB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACc,OAAO,CAACQ,OAAO,CAACJ,KAAK,IAAI;QAC1BA,KAAK,CAACjB,OAAO,GAAG,IAAI,CAACzB,KAAK,KAAK0C,KAAK,CAAC1C,KAAK;QAC1C,IAAI0C,KAAK,CAACjB,OAAO,EAAE;UACf,IAAI,CAACD,SAAS,GAAGkB,KAAK;QAC1B;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACAO,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACjB,cAAc,EAAE;MACrB,IAAI,CAACG,MAAM,CAACe,IAAI,CAAC,IAAIrD,cAAc,CAAC,IAAI,CAAC2B,SAAS,EAAE,IAAI,CAACJ,MAAM,CAAC,CAAC;IACrE;EACJ;EACAD,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACmB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACQ,OAAO,CAACJ,KAAK,IAAIA,KAAK,CAACK,aAAa,CAAC,CAAC,CAAC;IACxD;EACJ;EACA;AACJ;AACA;AACA;EACII,UAAUA,CAACnD,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC+B,eAAe,CAACqB,YAAY,CAAC,CAAC;EACvC;EACA;AACJ;AACA;AACA;AACA;EACIC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACrB,6BAA6B,GAAGqB,EAAE;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACIC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACpB,SAAS,GAAGoB,EAAE;EACvB;EACA;AACJ;AACA;AACA;EACIE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAC9B,QAAQ,GAAG8B,UAAU;IAC1B,IAAI,CAAC1B,eAAe,CAACqB,YAAY,CAAC,CAAC;EACvC;EACA;IAAS,IAAI,CAACM,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFhD,kBAAkB,EAA5B9C,EAAE,CAAA+F,iBAAA,CAA4C/F,EAAE,CAACgG,iBAAiB;IAAA,CAA4C;EAAE;EAChN;IAAS,IAAI,CAACC,IAAI,kBAD8EjG,EAAE,CAAAkG,iBAAA;MAAAC,IAAA,EACJrD,kBAAkB;MAAAsD,MAAA;QAAAvD,KAAA;QAAAE,IAAA;QAAAG,aAAA;QAAAhB,KAAA;QAAA0B,QAAA;QAAAC,QAAA;QAAAE,QAAA;MAAA;MAAAsC,OAAA;QAAAhC,MAAA;MAAA;IAAA,EAA4M;EAAE;AAClU;AACA;EAAA,QAAAiC,SAAA,oBAAAA,SAAA,KAHoGtG,EAAE,CAAAuG,iBAAA,CAGXzD,kBAAkB,EAAc,CAAC;IAChHqD,IAAI,EAAE/F;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE+F,IAAI,EAAEnG,EAAE,CAACgG;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE3B,MAAM,EAAE,CAAC;MACjG8B,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEwC,KAAK,EAAE,CAAC;MACRsD,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEyC,IAAI,EAAE,CAAC;MACPoD,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE4C,aAAa,EAAE,CAAC;MAChBiD,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE4B,KAAK,EAAE,CAAC;MACRiE,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEsD,QAAQ,EAAE,CAAC;MACXuC,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEuD,QAAQ,EAAE,CAAC;MACXsC,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEyD,QAAQ,EAAE,CAAC;MACXoC,IAAI,EAAE7F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA,MAAMkG,kBAAkB,CAAC;EACrBxE,WAAWA,CAACyE,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;AACJ;AACA,MAAMC,wBAAwB,GAAGzF,kBAAkB,CAACC,aAAa,CAACsF,kBAAkB,CAAC,CAAC;AACtF;AACA;AACA;AACA;AACA,MAAMG,mBAAmB,SAASD,wBAAwB,CAAC;EACvD;EACA,IAAI/C,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACiD,QAAQ;EACxB;EACA,IAAIjD,OAAOA,CAACzB,KAAK,EAAE;IACf,MAAM2E,eAAe,GAAGvF,qBAAqB,CAACY,KAAK,CAAC;IACpD,IAAI,IAAI,CAAC0E,QAAQ,KAAKC,eAAe,EAAE;MACnC,IAAI,CAACD,QAAQ,GAAGC,eAAe;MAC/B,IAAIA,eAAe,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC5E,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;QAC5E,IAAI,CAAC4E,UAAU,CAAClD,QAAQ,GAAG,IAAI;MACnC,CAAC,MACI,IAAI,CAACiD,eAAe,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC5E,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;QAClF;QACA;QACA,IAAI,CAAC4E,UAAU,CAAClD,QAAQ,GAAG,IAAI;MACnC;MACA,IAAIiD,eAAe,EAAE;QACjB;QACA,IAAI,CAACE,gBAAgB,CAACC,MAAM,CAAC,IAAI,CAACC,EAAE,EAAE,IAAI,CAAClE,IAAI,CAAC;MACpD;MACA,IAAI,CAACkB,eAAe,CAACqB,YAAY,CAAC,CAAC;IACvC;EACJ;EACA;EACA,IAAIpD,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACoB,MAAM;EACtB;EACA,IAAIpB,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,IAAI,CAACoB,MAAM,KAAKpB,KAAK,EAAE;MACvB,IAAI,CAACoB,MAAM,GAAGpB,KAAK;MACnB,IAAI,IAAI,CAAC4E,UAAU,KAAK,IAAI,EAAE;QAC1B,IAAI,CAAC,IAAI,CAACnD,OAAO,EAAE;UACf;UACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAACmD,UAAU,CAAC5E,KAAK,KAAKA,KAAK;QAClD;QACA,IAAI,IAAI,CAACyB,OAAO,EAAE;UACd,IAAI,CAACmD,UAAU,CAAClD,QAAQ,GAAG,IAAI;QACnC;MACJ;IACJ;EACJ;EACA;EACA,IAAIV,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc,IAAK,IAAI,CAAC2D,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC5D,aAAc,IAAI,OAAO;EAC/F;EACA,IAAIA,aAAaA,CAAChB,KAAK,EAAE;IACrB,IAAI,CAACiB,cAAc,GAAGjB,KAAK;EAC/B;EACA;EACA,IAAI2B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAK,IAAI,CAACgD,UAAU,KAAK,IAAI,IAAI,IAAI,CAACA,UAAU,CAACjD,QAAS;EACnF;EACA,IAAIA,QAAQA,CAAC3B,KAAK,EAAE;IAChB,IAAI,CAACgF,YAAY,CAAC5F,qBAAqB,CAACY,KAAK,CAAC,CAAC;EACnD;EACA;EACA,IAAI6B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAK,IAAI,CAAC8C,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC/C,QAAS;EAC1E;EACA,IAAIA,QAAQA,CAAC7B,KAAK,EAAE;IAChB,IAAI,CAAC8B,SAAS,GAAG1C,qBAAqB,CAACY,KAAK,CAAC;EACjD;EACA;EACA,IAAIW,KAAKA,CAAA,EAAG;IACR;IACA;IACA,OAAQ,IAAI,CAACsE,MAAM,IACd,IAAI,CAACL,UAAU,IAAI,IAAI,CAACA,UAAU,CAACjE,KAAM,IACzC,IAAI,CAACuE,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACvE,KAAM,IACxD,QAAQ;EAChB;EACA,IAAIA,KAAKA,CAACU,QAAQ,EAAE;IAChB,IAAI,CAAC4D,MAAM,GAAG5D,QAAQ;EAC1B;EACA;EACA,IAAI8D,OAAOA,CAAA,EAAG;IACV,OAAQ,GAAE,IAAI,CAACJ,EAAE,IAAI,IAAI,CAACK,SAAU,QAAO;EAC/C;EACAtF,WAAWA,CAAC8E,UAAU,EAAES,UAAU,EAAEtD,eAAe,EAAEuD,aAAa,EAAET,gBAAgB,EAAEU,aAAa,EAAEL,iBAAiB,EAAEM,QAAQ,EAAE;IAC9H,KAAK,CAACH,UAAU,CAAC;IACjB,IAAI,CAACtD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACuD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACT,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACK,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACE,SAAS,GAAI,aAAY,EAAExF,YAAa,EAAC;IAC9C;IACA,IAAI,CAACmF,EAAE,GAAG,IAAI,CAACK,SAAS;IACxB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACjD,MAAM,GAAG,IAAIlE,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAACyG,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACtD,MAAM,GAAG,IAAI;IAClB;IACA,IAAI,CAACqE,8BAA8B,GAAG,MAAM,CAAE,CAAC;IAC/C;IACA;IACA,IAAI,CAACb,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACc,eAAe,GAAGH,aAAa,KAAK,gBAAgB;IACzD,IAAIC,QAAQ,EAAE;MACV,IAAI,CAACA,QAAQ,GAAGnG,oBAAoB,CAACmG,QAAQ,EAAE,CAAC,CAAC;IACrD;EACJ;EACA;EACAG,KAAKA,CAACC,OAAO,EAAEC,MAAM,EAAE;IACnB,IAAIA,MAAM,EAAE;MACR,IAAI,CAACP,aAAa,CAACQ,QAAQ,CAAC,IAAI,CAACC,aAAa,EAAEF,MAAM,EAAED,OAAO,CAAC;IACpE,CAAC,MACI;MACD,IAAI,CAACG,aAAa,CAACC,aAAa,CAACL,KAAK,CAACC,OAAO,CAAC;IACnD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI7C,aAAaA,CAAA,EAAG;IACZ;IACA;IACA,IAAI,CAAChB,eAAe,CAACqB,YAAY,CAAC,CAAC;EACvC;EACA6C,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACrB,UAAU,EAAE;MACjB;MACA,IAAI,CAACnD,OAAO,GAAG,IAAI,CAACmD,UAAU,CAAC5E,KAAK,KAAK,IAAI,CAACoB,MAAM;MACpD,IAAI,IAAI,CAACK,OAAO,EAAE;QACd,IAAI,CAACmD,UAAU,CAAClD,QAAQ,GAAG,IAAI;MACnC;MACA;MACA,IAAI,CAACb,IAAI,GAAG,IAAI,CAAC+D,UAAU,CAAC/D,IAAI;IACpC;IACA,IAAI,CAAC4E,8BAA8B,GAAG,IAAI,CAACZ,gBAAgB,CAACqB,MAAM,CAAC,CAACnB,EAAE,EAAElE,IAAI,KAAK;MAC7E,IAAIkE,EAAE,KAAK,IAAI,CAACA,EAAE,IAAIlE,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;QACtC,IAAI,CAACY,OAAO,GAAG,KAAK;MACxB;IACJ,CAAC,CAAC;EACN;EACA0E,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACD,eAAe,CAAC,CAAC;IACtB,IAAI,CAACd,aAAa,CAACgB,OAAO,CAAC,IAAI,CAAC/B,WAAW,EAAE,IAAI,CAAC,CAAC/B,SAAS,CAAC+D,WAAW,IAAI;MACxE,IAAI,CAACA,WAAW,IAAI,IAAI,CAAC3B,UAAU,EAAE;QACjC,IAAI,CAACA,UAAU,CAAC/B,MAAM,CAAC,CAAC;MAC5B;IACJ,CAAC,CAAC;EACN;EACAF,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2C,aAAa,CAACkB,cAAc,CAAC,IAAI,CAACjC,WAAW,CAAC;IACnD,IAAI,CAACkB,8BAA8B,CAAC,CAAC;EACzC;EACA;EACAxC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACd,MAAM,CAACe,IAAI,CAAC,IAAIrD,cAAc,CAAC,IAAI,EAAE,IAAI,CAACuB,MAAM,CAAC,CAAC;EAC3D;EACAqF,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,aAAa,IAAI,IAAI,CAAC/E,QAAQ;EAC9C;EACAgF,aAAaA,CAACC,KAAK,EAAE;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACAA,KAAK,CAACC,eAAe,CAAC,CAAC;EAC3B;EACA;EACAC,mBAAmBA,CAACF,KAAK,EAAE;IACvB;IACA;IACA;IACAA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,CAAC,IAAI,CAACpF,OAAO,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE;MACjC,MAAMoF,iBAAiB,GAAG,IAAI,CAACnC,UAAU,IAAI,IAAI,CAAC5E,KAAK,KAAK,IAAI,CAAC4E,UAAU,CAAC5E,KAAK;MACjF,IAAI,CAACyB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACwB,gBAAgB,CAAC,CAAC;MACvB,IAAI,IAAI,CAAC2B,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAAC3C,6BAA6B,CAAC,IAAI,CAACjC,KAAK,CAAC;QACzD,IAAI+G,iBAAiB,EAAE;UACnB,IAAI,CAACnC,UAAU,CAAC3B,gBAAgB,CAAC,CAAC;QACtC;MACJ;IACJ;EACJ;EACA;EACA+D,mBAAmBA,CAACJ,KAAK,EAAE;IACvB,IAAI,CAACE,mBAAmB,CAACF,KAAK,CAAC;IAC/B,IAAI,CAAC,IAAI,CAACjF,QAAQ,EAAE;MAChB;MACA;MACA,IAAI,CAACoE,aAAa,CAACC,aAAa,CAACL,KAAK,CAAC,CAAC;IAC5C;EACJ;EACA;EACAX,YAAYA,CAAChF,KAAK,EAAE;IAChB,IAAI,IAAI,CAAC4B,SAAS,KAAK5B,KAAK,EAAE;MAC1B,IAAI,CAAC4B,SAAS,GAAG5B,KAAK;MACtB,IAAI,CAAC+B,eAAe,CAACqB,YAAY,CAAC,CAAC;IACvC;EACJ;EACA;EACAgD,eAAeA,CAAA,EAAG;IACd,MAAMa,KAAK,GAAG,IAAI,CAACrC,UAAU;IAC7B,IAAI5E,KAAK;IACT;IACA;IACA;IACA;IACA,IAAI,CAACiH,KAAK,IAAI,CAACA,KAAK,CAACvF,QAAQ,IAAI,IAAI,CAACC,QAAQ,EAAE;MAC5C3B,KAAK,GAAG,IAAI,CAACwF,QAAQ;IACzB,CAAC,MACI;MACDxF,KAAK,GAAGiH,KAAK,CAACvF,QAAQ,KAAK,IAAI,GAAG,IAAI,CAAC8D,QAAQ,GAAG,CAAC,CAAC;IACxD;IACA,IAAIxF,KAAK,KAAK,IAAI,CAACkH,iBAAiB,EAAE;MAClC;MACA;MACA,MAAMC,KAAK,GAAG,IAAI,CAACpB,aAAa,EAAEC,aAAa;MAC/C,IAAImB,KAAK,EAAE;QACPA,KAAK,CAACC,YAAY,CAAC,UAAU,EAAEpH,KAAK,GAAG,EAAE,CAAC;QAC1C,IAAI,CAACkH,iBAAiB,GAAGlH,KAAK;MAClC;IACJ;EACJ;EACA;IAAS,IAAI,CAAC0D,IAAI,YAAA2D,4BAAAzD,CAAA;MA3Q8E9F,EAAE,CAAAwJ,gBAAA;IAAA,CA2QqF;EAAE;EACzL;IAAS,IAAI,CAACvD,IAAI,kBA5Q8EjG,EAAE,CAAAkG,iBAAA;MAAAC,IAAA,EA4QJQ,mBAAmB;MAAA8C,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5QjB3J,EAAE,CAAA6J,WAAA,CAAAjI,GAAA;QAAA;QAAA,IAAA+H,EAAA;UAAA,IAAAG,EAAA;UAAF9J,EAAE,CAAA+J,cAAA,CAAAD,EAAA,GAAF9J,EAAE,CAAAgK,WAAA,QAAAJ,GAAA,CAAA3B,aAAA,GAAA6B,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAA7D,MAAA;QAAAa,EAAA;QAAAlE,IAAA;QAAAmH,SAAA;QAAAC,cAAA;QAAAC,eAAA;QAAAzG,OAAA;QAAAzB,KAAA;QAAAgB,aAAA;QAAAW,QAAA;QAAAE,QAAA;QAAAlB,KAAA;MAAA;MAAAwD,OAAA;QAAAhC,MAAA;MAAA;MAAAgG,QAAA,GAAFrK,EAAE,CAAAsK,0BAAA;IAAA,EA4Q2f;EAAE;AACnmB;AACA;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KA9QoGtG,EAAE,CAAAuG,iBAAA,CA8QXI,mBAAmB,EAAc,CAAC;IACjHR,IAAI,EAAE/F;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE+F,IAAI,EAAErD;IAAmB,CAAC,EAAE;MAAEqD,IAAI,EAAEnG,EAAE,CAACuK;IAAW,CAAC,EAAE;MAAEpE,IAAI,EAAEnG,EAAE,CAACgG;IAAkB,CAAC,EAAE;MAAEG,IAAI,EAAE9E,EAAE,CAACmJ;IAAa,CAAC,EAAE;MAAErE,IAAI,EAAE3E,EAAE,CAACiJ;IAA0B,CAAC,EAAE;MAAEtE,IAAI,EAAEuE;IAAU,CAAC,EAAE;MAAEvE,IAAI,EAAEuE;IAAU,CAAC,EAAE;MAAEvE,IAAI,EAAEuE;IAAU,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEzD,EAAE,EAAE,CAAC;MACtRd,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEyC,IAAI,EAAE,CAAC;MACPoD,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE4J,SAAS,EAAE,CAAC;MACZ/D,IAAI,EAAE7F,KAAK;MACXqK,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAER,cAAc,EAAE,CAAC;MACjBhE,IAAI,EAAE7F,KAAK;MACXqK,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEP,eAAe,EAAE,CAAC;MAClBjE,IAAI,EAAE7F,KAAK;MACXqK,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEhH,OAAO,EAAE,CAAC;MACVwC,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE4B,KAAK,EAAE,CAAC;MACRiE,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE4C,aAAa,EAAE,CAAC;MAChBiD,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEuD,QAAQ,EAAE,CAAC;MACXsC,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEyD,QAAQ,EAAE,CAAC;MACXoC,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEuC,KAAK,EAAE,CAAC;MACRsD,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE+D,MAAM,EAAE,CAAC;MACT8B,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAE4H,aAAa,EAAE,CAAC;MAChB9B,IAAI,EAAE5F,SAAS;MACfoK,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMrI,aAAa,SAASQ,kBAAkB,CAAC;EAC3C;IAAS,IAAI,CAAC8C,IAAI;MAAA,IAAAgF,0BAAA;MAAA,gBAAAC,sBAAA/E,CAAA;QAAA,QAAA8E,0BAAA,KAAAA,0BAAA,GAnT8E5K,EAAE,CAAA8K,qBAAA,CAmTQxI,aAAa,IAAAwD,CAAA,IAAbxD,aAAa;MAAA;IAAA,GAAqD;EAAE;EAC9K;IAAS,IAAI,CAAC2D,IAAI,kBApT8EjG,EAAE,CAAAkG,iBAAA;MAAAC,IAAA,EAoTJ7D,aAAa;MAAAyI,SAAA;MAAAC,cAAA,WAAAC,6BAAAtB,EAAA,EAAAC,GAAA,EAAAsB,QAAA;QAAA,IAAAvB,EAAA;UApTX3J,EAAE,CAAAmL,cAAA,CAAAD,QAAA,EAuTRE,cAAc;QAAA;QAAA,IAAAzB,EAAA;UAAA,IAAAG,EAAA;UAvTR9J,EAAE,CAAA+J,cAAA,CAAAD,EAAA,GAAF9J,EAAE,CAAAgK,WAAA,QAAAJ,GAAA,CAAApF,OAAA,GAAAsF,EAAA;QAAA;MAAA;MAAAuB,SAAA,WAoTsE,YAAY;MAAAC,QAAA;MAAAjB,QAAA,GApTpFrK,EAAE,CAAAuL,kBAAA,CAoT0I,CACpOpJ,sCAAsC,EACtC;QAAEC,OAAO,EAAEI,eAAe;QAAEH,WAAW,EAAEC;MAAc,CAAC,CAC3D,GAvT2FtC,EAAE,CAAAsK,0BAAA;IAAA,EAuTqG;EAAE;AAC7M;AACA;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KAzToGtG,EAAE,CAAAuG,iBAAA,CAyTXjE,aAAa,EAAc,CAAC;IAC3G6D,IAAI,EAAE/F,SAAS;IACfuK,IAAI,EAAE,CAAC;MACCa,QAAQ,EAAE,iBAAiB;MAC3BF,QAAQ,EAAE,eAAe;MACzBG,SAAS,EAAE,CACPtJ,sCAAsC,EACtC;QAAEC,OAAO,EAAEI,eAAe;QAAEH,WAAW,EAAEC;MAAc,CAAC,CAC3D;MACDoJ,IAAI,EAAE;QACF,MAAM,EAAE,YAAY;QACpB,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAElH,OAAO,EAAE,CAAC;MACxB2B,IAAI,EAAE3F,eAAe;MACrBmK,IAAI,EAAE,CAAC1K,UAAU,CAAC,MAAMmL,cAAc,CAAC,EAAE;QAAEO,WAAW,EAAE;MAAK,CAAC;IAClE,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMP,cAAc,SAASzE,mBAAmB,CAAC;EAC7C3E,WAAWA,CAAC8E,UAAU,EAAES,UAAU,EAAEtD,eAAe,EAAEuD,aAAa,EAAET,gBAAgB,EAAEU,aAAa,EAAEL,iBAAiB,EAAEM,QAAQ,EAAE;IAC9H,KAAK,CAACZ,UAAU,EAAES,UAAU,EAAEtD,eAAe,EAAEuD,aAAa,EAAET,gBAAgB,EAAEU,aAAa,EAAEL,iBAAiB,EAAEM,QAAQ,CAAC;EAC/H;EACA;IAAS,IAAI,CAAC9B,IAAI,YAAAgG,uBAAA9F,CAAA;MAAA,YAAAA,CAAA,IAAwFsF,cAAc,EA/UxBpL,EAAE,CAAA+F,iBAAA,CA+UwCvD,eAAe,MA/UzDxC,EAAE,CAAA+F,iBAAA,CA+UoF/F,EAAE,CAACuK,UAAU,GA/UnGvK,EAAE,CAAA+F,iBAAA,CA+U8G/F,EAAE,CAACgG,iBAAiB,GA/UpIhG,EAAE,CAAA+F,iBAAA,CA+U+I1E,EAAE,CAACmJ,YAAY,GA/UhKxK,EAAE,CAAA+F,iBAAA,CA+U2KvE,EAAE,CAACiJ,yBAAyB,GA/UzMzK,EAAE,CAAA+F,iBAAA,CA+UoNtE,qBAAqB,MA/U3OzB,EAAE,CAAA+F,iBAAA,CA+UsQtD,yBAAyB,MA/UjSzC,EAAE,CAAA6L,iBAAA,CA+U4T,UAAU;IAAA,CAA6D;EAAE;EACve;IAAS,IAAI,CAACC,IAAI,kBAhV8E9L,EAAE,CAAA+L,iBAAA;MAAA5F,IAAA,EAgVJiF,cAAc;MAAAL,SAAA;MAAAM,SAAA;MAAAW,QAAA;MAAAC,YAAA,WAAAC,4BAAAvC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhVZ3J,EAAE,CAAAmM,UAAA,mBAAAC,wCAAA;YAAA,OAgVJxC,GAAA,CAAA3B,aAAA,CAAAC,aAAA,CAAAL,KAAA,CAAkC,CAAC;UAAA;QAAA;QAAA,IAAA8B,EAAA;UAhVjC3J,EAAE,CAAAqM,WAAA,OAAAzC,GAAA,CAAA3C,EAAA;UAAFjH,EAAE,CAAAsM,WAAA,gBAAA1C,GAAA,CAAA/G,KAAA,8BAAA+G,GAAA,CAAA/G,KAAA,2BAAA+G,GAAA,CAAA/G,KAAA,sCAAA+G,GAAA,CAAAjG,OAAA,6BAAAiG,GAAA,CAAAhC,eAAA;QAAA;MAAA;MAAAxB,MAAA;QAAAwC,aAAA;QAAAlB,QAAA;MAAA;MAAA4D,QAAA;MAAAjB,QAAA,GAAFrK,EAAE,CAAAsK,0BAAA;MAAAiC,kBAAA,EAAA1K,GAAA;MAAA2K,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAjD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3J,EAAE,CAAA6M,eAAA;UAAF7M,EAAE,CAAA8M,cAAA,eAgV+wB,CAAC,YAAD,CAAC,YAAD,CAAC;UAhVlxB9M,EAAE,CAAAmM,UAAA,mBAAAY,6CAAAC,MAAA;YAAA,OAgVg9BpD,GAAA,CAAAV,mBAAA,CAAA8D,MAA0B,CAAC;UAAA,CAAC,CAAC;UAhV/+BhN,EAAE,CAAAiN,YAAA,CAgVm/B,CAAC;UAhVt/BjN,EAAE,CAAA8M,cAAA,iBAgVo9C,CAAC;UAhVv9C9M,EAAE,CAAAmM,UAAA,oBAAAe,gDAAAF,MAAA;YAAA,OAgVu7CpD,GAAA,CAAAZ,mBAAA,CAAAgE,MAA0B,CAAC;UAAA,CAAC,CAAC;UAhVt9ChN,EAAE,CAAAiN,YAAA,CAgVo9C,CAAC;UAhVv9CjN,EAAE,CAAA8M,cAAA,YAgV+/C,CAAC;UAhVlgD9M,EAAE,CAAAmN,SAAA,YAgVojD,CAAC,YAAD,CAAC;UAhVvjDnN,EAAE,CAAAiN,YAAA,CAgVqnD,CAAC;UAhVxnDjN,EAAE,CAAA8M,cAAA,YAgVs0D,CAAC;UAhVz0D9M,EAAE,CAAAmN,SAAA,cAgVk5D,CAAC;UAhVr5DnN,EAAE,CAAAiN,YAAA,CAgV85D,CAAC,CAAD,CAAC;UAhVj6DjN,EAAE,CAAA8M,cAAA,gBAgVy9D,CAAC;UAhV59D9M,EAAE,CAAAoN,YAAA,GAgVw/D,CAAC;UAhV3/DpN,EAAE,CAAAiN,YAAA,CAgVogE,CAAC,CAAD,CAAC;QAAA;QAAA,IAAAtD,EAAA;UAAA,MAAA0D,GAAA,GAhVvgErN,EAAE,CAAAsN,WAAA;UAAFtN,EAAE,CAAAsM,WAAA,8BAAA1C,GAAA,CAAA1G,aAAA,YAgV8wB,CAAC;UAhVjxBlD,EAAE,CAAAuN,SAAA,EAgVo1B,CAAC;UAhVv1BvN,EAAE,CAAAsM,WAAA,wBAAA1C,GAAA,CAAA/F,QAgVo1B,CAAC;UAhVv1B7D,EAAE,CAAAuN,SAAA,EAgVslC,CAAC;UAhVzlCvN,EAAE,CAAAwN,UAAA,OAAA5D,GAAA,CAAAvC,OAgVslC,CAAC,YAAAuC,GAAA,CAAAjG,OAAD,CAAC,aAAAiG,GAAA,CAAA/F,QAAD,CAAC,aAAA+F,GAAA,CAAA7F,QAAD,CAAC;UAhVzlC/D,EAAE,CAAAqM,WAAA,SAAAzC,GAAA,CAAA7G,IAgV6rC,CAAC,UAAA6G,GAAA,CAAA1H,KAAD,CAAC,eAAA0H,GAAA,CAAAM,SAAD,CAAC,oBAAAN,GAAA,CAAAO,cAAD,CAAC,qBAAAP,GAAA,CAAAQ,eAAD,CAAC;UAhVhsCpK,EAAE,CAAAuN,SAAA,EAgVwuD,CAAC;UAhV3uDvN,EAAE,CAAAwN,UAAA,qBAAAH,GAgVwuD,CAAC,sBAAAzD,GAAA,CAAAjB,iBAAA,EAAD,CAAC,0BAAD,CAAC;UAhV3uD3I,EAAE,CAAAuN,SAAA,EAgVw9D,CAAC;UAhV39DvN,EAAE,CAAAwN,UAAA,QAAA5D,GAAA,CAAAvC,OAgVw9D,CAAC;QAAA;MAAA;MAAAoG,YAAA,GAA6mXzM,EAAE,CAAC0M,SAAS;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAA6T;EAAE;AACv/b;AACA;EAAA,QAAAvH,SAAA,oBAAAA,SAAA,KAlVoGtG,EAAE,CAAAuG,iBAAA,CAkVX6E,cAAc,EAAc,CAAC;IAC5GjF,IAAI,EAAE1F,SAAS;IACfkK,IAAI,EAAE,CAAC;MAAEa,QAAQ,EAAE,kBAAkB;MAAEE,IAAI,EAAE;QACjC,OAAO,EAAE,sBAAsB;QAC/B,WAAW,EAAE,IAAI;QACjB,qBAAqB,EAAE,qBAAqB;QAC5C,oBAAoB,EAAE,oBAAoB;QAC1C,kBAAkB,EAAE,kBAAkB;QACtC,+BAA+B,EAAE,SAAS;QAC1C,iCAAiC,EAAE,iBAAiB;QACpD;QACA,iBAAiB,EAAE,MAAM;QACzB,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,yBAAyB,EAAE,MAAM;QACjC;QACA;QACA;QACA,SAAS,EAAE;MACf,CAAC;MAAEtF,MAAM,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC;MAAEkF,QAAQ,EAAE,gBAAgB;MAAEsC,aAAa,EAAElN,iBAAiB,CAACoN,IAAI;MAAED,eAAe,EAAElN,uBAAuB,CAACoN,MAAM;MAAEpB,QAAQ,EAAE,g3CAAg3C;MAAEgB,MAAM,EAAE,CAAC,8/WAA8/W;IAAE,CAAC;EAChja,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExH,IAAI,EAAE7D,aAAa;MAAE0L,UAAU,EAAE,CAAC;QAClE7H,IAAI,EAAEvF;MACV,CAAC,EAAE;QACCuF,IAAI,EAAEtF,MAAM;QACZ8J,IAAI,EAAE,CAACnI,eAAe;MAC1B,CAAC;IAAE,CAAC,EAAE;MAAE2D,IAAI,EAAEnG,EAAE,CAACuK;IAAW,CAAC,EAAE;MAAEpE,IAAI,EAAEnG,EAAE,CAACgG;IAAkB,CAAC,EAAE;MAAEG,IAAI,EAAE9E,EAAE,CAACmJ;IAAa,CAAC,EAAE;MAAErE,IAAI,EAAE3E,EAAE,CAACiJ;IAA0B,CAAC,EAAE;MAAEtE,IAAI,EAAEuE,SAAS;MAAEsD,UAAU,EAAE,CAAC;QAC9J7H,IAAI,EAAEvF;MACV,CAAC,EAAE;QACCuF,IAAI,EAAEtF,MAAM;QACZ8J,IAAI,EAAE,CAAClJ,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAE0E,IAAI,EAAEuE,SAAS;MAAEsD,UAAU,EAAE,CAAC;QAClC7H,IAAI,EAAEvF;MACV,CAAC,EAAE;QACCuF,IAAI,EAAEtF,MAAM;QACZ8J,IAAI,EAAE,CAAClI,yBAAyB;MACpC,CAAC;IAAE,CAAC,EAAE;MAAE0D,IAAI,EAAEuE,SAAS;MAAEsD,UAAU,EAAE,CAAC;QAClC7H,IAAI,EAAErF,SAAS;QACf6J,IAAI,EAAE,CAAC,UAAU;MACrB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMsD,cAAc,CAAC;EACjB;IAAS,IAAI,CAACrI,IAAI,YAAAsI,uBAAApI,CAAA;MAAA,YAAAA,CAAA,IAAwFmI,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBA5X8EnO,EAAE,CAAAoO,gBAAA;MAAAjI,IAAA,EA4XS8H;IAAc,EAAwK;EAAE;EACnS;IAAS,IAAI,CAACI,IAAI,kBA7X8ErO,EAAE,CAAAsO,gBAAA;MAAAC,OAAA,GA6XmCpN,eAAe,EAAEQ,YAAY,EAAEP,eAAe,EAAED,eAAe;IAAA,EAAI;EAAE;AAC9M;AACA;EAAA,QAAAmF,SAAA,oBAAAA,SAAA,KA/XoGtG,EAAE,CAAAuG,iBAAA,CA+XX0H,cAAc,EAAc,CAAC;IAC5G9H,IAAI,EAAEpF,QAAQ;IACd4J,IAAI,EAAE,CAAC;MACC4D,OAAO,EAAE,CAACpN,eAAe,EAAEQ,YAAY,EAAEP,eAAe,CAAC;MACzDoN,OAAO,EAAE,CAACrN,eAAe,EAAEmB,aAAa,EAAE8I,cAAc,CAAC;MACzDqD,YAAY,EAAE,CAACnM,aAAa,EAAE8I,cAAc;IAChD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS3I,yBAAyB,EAAEG,iCAAiC,EAAEJ,eAAe,EAAEL,sCAAsC,EAAEiJ,cAAc,EAAErJ,cAAc,EAAEO,aAAa,EAAE2L,cAAc,EAAEtH,mBAAmB,EAAE7D,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}