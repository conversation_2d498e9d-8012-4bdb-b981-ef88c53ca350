/**
 * Model for Commit Transaction Response
 * Response from finalizing a booking transaction
 */
export interface CommitTransactionResponse {
    header: Header;
    body: Body;
}

export interface Header {
    requestId: string;
    success: boolean;
    responseTime: string;
    messages: Message[];
}

export interface Message {
    id: number;
    code: string;
    messageType: number;
    message: string;
}

export interface Body {
    reservationNumber: string;
    encryptedReservationNumber: string;
    transactionId: string;
}
