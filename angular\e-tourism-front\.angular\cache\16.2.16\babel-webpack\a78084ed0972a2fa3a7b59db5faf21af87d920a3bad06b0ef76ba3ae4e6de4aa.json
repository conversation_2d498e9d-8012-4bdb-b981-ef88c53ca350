{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewEncapsulation, HostListener } from '@angular/core';\nimport { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nexport let SearchPriceComponent = class SearchPriceComponent {\n  constructor(fb, productService, router, sharedDataService) {\n    this.fb = fb;\n    this.productService = productService;\n    this.router = router;\n    this.sharedDataService = sharedDataService;\n    this.departureLocations = [];\n    this.arrivalLocations = [];\n    this.isLoading = false;\n    this.searchResults = [];\n    this.filteredResults = [];\n    this.hasSearched = false;\n    this.errorMessage = '';\n    this.lastSearchId = '';\n    // Types de recherche de vol\n    this.SEARCH_TYPE_ONE_WAY = 1;\n    this.SEARCH_TYPE_ROUND_TRIP = 2;\n    this.SEARCH_TYPE_MULTI_CITY = 3;\n    // Type de recherche actuel (par défaut: aller simple)\n    this.currentSearchType = this.SEARCH_TYPE_ONE_WAY;\n    // Passenger selector properties\n    this.showPassengerDropdown = false;\n    this.passengerCounts = {\n      [PassengerType.Adult]: 1,\n      [PassengerType.Child]: 0,\n      [PassengerType.Infant]: 0\n    };\n    // Filter options\n    this.currentFilter = 'recommended';\n    this.filterOptions = [{\n      value: 'recommended',\n      label: 'Recommended',\n      icon: 'fa-star'\n    }, {\n      value: 'cheapest',\n      label: 'Cheapest',\n      icon: 'fa-dollar-sign'\n    }, {\n      value: 'shortest',\n      label: 'Shortest',\n      icon: 'fa-clock'\n    }];\n    // Sidebar filter options\n    this.sidebarFilters = {\n      stops: {\n        direct: false,\n        oneStop: false,\n        multiStop: false\n      },\n      departureTime: {\n        earlyMorning: false,\n        morning: false,\n        afternoon: false,\n        evening: false // 18:00 - 00:00\n      },\n\n      arrivalTime: {\n        earlyMorning: false,\n        morning: false,\n        afternoon: false,\n        evening: false // 18:00 - 00:00\n      },\n\n      airlines: {} // Will be populated dynamically based on search results\n    };\n    // Expanded sections in sidebar\n    this.expandedSections = {\n      stops: true,\n      departureTime: true,\n      arrivalTime: true,\n      airlines: true\n    };\n    // Price ranges for each filter option (will be calculated from results)\n    this.filterPrices = {\n      stops: {\n        direct: 0,\n        oneStop: 0,\n        multiStop: 0\n      },\n      departureTime: {\n        earlyMorning: 0,\n        morning: 0,\n        afternoon: 0,\n        evening: 0\n      },\n      arrivalTime: {\n        earlyMorning: 0,\n        morning: 0,\n        afternoon: 0,\n        evening: 0\n      },\n      airlines: {}\n    };\n    // Passenger type options\n    this.passengerTypes = [{\n      value: PassengerType.Adult,\n      label: 'Adult'\n    }, {\n      value: PassengerType.Child,\n      label: 'Child'\n    }, {\n      value: PassengerType.Infant,\n      label: 'Infant'\n    }];\n    // Flight class options\n    this.flightClasses = [{\n      value: FlightClassType.PROMO,\n      label: 'Promo'\n    }, {\n      value: FlightClassType.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClassType.BUSINESS,\n      label: 'Business'\n    }];\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required],\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      arrivalLocation: ['', Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      returnDate: [''],\n      // Options de vol\n      flightClass: [1, Validators.required],\n      nonStop: [false],\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n    // Initialiser les compteurs de passagers dans le service partagé\n    this.sharedDataService.setPassengerCounts(this.passengerCounts);\n  }\n  // Close dropdown when clicking outside\n  onDocumentClick(event) {\n    // Check if click is outside the passenger dropdown\n    const clickedElement = event.target;\n    const passengerSelector = document.querySelector('.passengers-selector');\n    if (passengerSelector && !passengerSelector.contains(clickedElement)) {\n      this.showPassengerDropdown = false;\n    }\n  }\n  // Toggle passenger dropdown\n  togglePassengerDropdown(event) {\n    event.stopPropagation();\n    this.showPassengerDropdown = !this.showPassengerDropdown;\n  }\n  // Close passenger dropdown\n  closePassengerDropdown() {\n    this.showPassengerDropdown = false;\n  }\n  // Get passenger count for a specific type\n  getPassengerCount(type) {\n    return this.passengerCounts[type] || 0;\n  }\n  // Increase passenger count\n  increasePassengerCount(type) {\n    if (this.getTotalPassengers() < 9) {\n      this.passengerCounts[type] = (this.passengerCounts[type] || 0) + 1;\n      // Mettre à jour le service partagé\n      this.sharedDataService.setPassengerCounts(this.passengerCounts);\n    }\n  }\n  // Decrease passenger count\n  decreasePassengerCount(type) {\n    // Pour les adultes, ne pas permettre de descendre en dessous de 1\n    if (type === PassengerType.Adult) {\n      if (this.passengerCounts[type] > 1) {\n        this.passengerCounts[type] -= 1;\n      }\n    } else if (this.passengerCounts[type] > 0) {\n      this.passengerCounts[type] -= 1;\n    }\n    // Mettre à jour le service partagé\n    this.sharedDataService.setPassengerCounts(this.passengerCounts);\n  }\n  // Get total number of passengers\n  getTotalPassengers() {\n    return Object.values(this.passengerCounts).reduce((sum, count) => sum + count, 0);\n  }\n  // Get passengers array for API request\n  getPassengersArray() {\n    // Utiliser le service partagé pour obtenir les passagers\n    return this.sharedDataService.getPassengersArray();\n  }\n  ngOnInit() {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n  // Méthode pour appliquer le filtre sélectionné\n  applyFilter(filterValue) {\n    this.currentFilter = filterValue;\n    this.applyAllFilters();\n  }\n  // Méthode pour vérifier si un vol correspond à la classe sélectionnée\n  isFlightMatchingClass(flight, selectedClass) {\n    // Vérifier si le vol a des items\n    if (!flight.items || flight.items.length === 0) {\n      console.log(`Flight ${flight.id} has no items`);\n      return false;\n    }\n    console.log(`Checking flight ${flight.id} for class ${selectedClass}`);\n    // Vérifier la classe de vol dans les items\n    for (const item of flight.items) {\n      if (item.flightClass) {\n        console.log(`Item class: ${item.flightClass.name} (type: ${item.flightClass.type})`);\n        if (item.flightClass.type === selectedClass) {\n          console.log(`Match found in item flightClass`);\n          return true;\n        }\n      }\n      // Vérifier également dans les segments si disponibles\n      if (item.segments) {\n        console.log(`Checking ${item.segments.length} segments`);\n        for (const segment of item.segments) {\n          if (segment.flightClass) {\n            console.log(`Segment class: ${segment.flightClass.name} (type: ${segment.flightClass.type})`);\n            if (segment.flightClass.type === selectedClass) {\n              console.log(`Match found in segment flightClass`);\n              return true;\n            }\n          }\n        }\n      }\n    }\n    // Vérifier également dans les offres si disponibles\n    if (flight.offers && flight.offers.length > 0) {\n      console.log(`Checking ${flight.offers.length} offers`);\n      for (const offer of flight.offers) {\n        if (offer.flightClassInformations && offer.flightClassInformations.length > 0) {\n          console.log(`Offer has ${offer.flightClassInformations.length} class infos`);\n          for (const classInfo of offer.flightClassInformations) {\n            console.log(`Offer class info: ${classInfo.name} (type: ${classInfo.type})`);\n            if (classInfo.type === selectedClass) {\n              console.log(`Match found in offer flightClassInformations`);\n              return true;\n            }\n          }\n        } else {\n          console.log(`Offer has no flightClassInformations`);\n        }\n      }\n    }\n    console.log(`No match found for flight ${flight.id}`);\n    return false;\n  }\n  // Filtrer les résultats par date\n  filterResultsByDate(results) {\n    if (!results || results.length === 0) {\n      return [];\n    }\n    const formValue = this.searchForm.value;\n    const departureDate = new Date(formValue.departureDate);\n    const returnDate = formValue.returnDate ? new Date(formValue.returnDate) : null;\n    // Réinitialiser les heures pour comparer uniquement les dates\n    departureDate.setHours(0, 0, 0, 0);\n    if (returnDate) returnDate.setHours(0, 0, 0, 0);\n    console.log('Filtering flights by dates:');\n    console.log('Requested departure date:', departureDate.toISOString().split('T')[0]);\n    if (returnDate) console.log('Requested return date:', returnDate.toISOString().split('T')[0]);\n    return results.filter(flight => {\n      if (!flight.items || flight.items.length === 0) {\n        return false;\n      }\n      // Pour les vols aller simple\n      if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n        if (!flight.items[0].departure || !flight.items[0].departure.date) {\n          return false;\n        }\n        const flightDepartureDate = new Date(flight.items[0].departure.date);\n        flightDepartureDate.setHours(0, 0, 0, 0);\n        // Comparer uniquement les dates (jour/mois/année)\n        return flightDepartureDate.getTime() === departureDate.getTime();\n      }\n      // Pour les vols aller-retour\n      else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP && returnDate) {\n        // Cas 1: Vol avec plusieurs items (aller et retour séparés)\n        if (flight.items.length > 1) {\n          if (!flight.items[0].departure || !flight.items[0].departure.date || !flight.items[1].departure || !flight.items[1].departure.date) {\n            return false;\n          }\n          const outboundDepartureDate = new Date(flight.items[0].departure.date);\n          outboundDepartureDate.setHours(0, 0, 0, 0);\n          const inboundDepartureDate = new Date(flight.items[1].departure.date);\n          inboundDepartureDate.setHours(0, 0, 0, 0);\n          console.log('Flight', flight.id, 'outbound:', outboundDepartureDate.toISOString().split('T')[0], 'inbound:', inboundDepartureDate.toISOString().split('T')[0]);\n          return outboundDepartureDate.getTime() === departureDate.getTime() && inboundDepartureDate.getTime() === returnDate.getTime();\n        }\n        // Cas 2: Vol avec un seul item mais plusieurs segments\n        else if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n          const segments = flight.items[0].segments;\n          const halfLength = Math.ceil(segments.length / 2);\n          // Premier segment (aller)\n          if (!segments[0].departure || !segments[0].departure.date) {\n            return false;\n          }\n          // Dernier segment de la première moitié (fin de l'aller)\n          const outboundDepartureDate = new Date(segments[0].departure.date);\n          outboundDepartureDate.setHours(0, 0, 0, 0);\n          // Premier segment de la seconde moitié (début du retour)\n          if (segments.length <= halfLength) {\n            return false;\n          }\n          if (!segments[halfLength].departure || !segments[halfLength].departure.date) {\n            return false;\n          }\n          const inboundDepartureDate = new Date(segments[halfLength].departure.date);\n          inboundDepartureDate.setHours(0, 0, 0, 0);\n          console.log('Flight', flight.id, 'outbound (segments):', outboundDepartureDate.toISOString().split('T')[0], 'inbound (segments):', inboundDepartureDate.toISOString().split('T')[0]);\n          return outboundDepartureDate.getTime() === departureDate.getTime() && inboundDepartureDate.getTime() === returnDate.getTime();\n        }\n      }\n      return false;\n    });\n  }\n  // Méthode pour appliquer tous les filtres (top et sidebar)\n  applyAllFilters() {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      this.filteredResults = [];\n      return;\n    }\n    // Étape 1: Appliquer les filtres de la sidebar\n    let results = [...this.searchResults];\n    // Filtrer par date\n    results = this.filterResultsByDate(results);\n    // Récupérer la classe de vol sélectionnée\n    const selectedClass = this.searchForm.get('flightClass')?.value;\n    // Filtrer par classe de vol\n    if (selectedClass !== undefined) {\n      console.log('Filtering by flight class:', selectedClass);\n      // Afficher le nombre total de vols avant filtrage\n      console.log('Total flights before class filtering:', results.length);\n      // Vérifier combien de vols correspondent à chaque classe\n      const classCounts = {\n        [FlightClassType.PROMO]: 0,\n        [FlightClassType.ECONOMY]: 0,\n        [FlightClassType.BUSINESS]: 0\n      };\n      results.forEach(flight => {\n        if (this.isFlightMatchingClass(flight, FlightClassType.PROMO)) classCounts[FlightClassType.PROMO]++;\n        if (this.isFlightMatchingClass(flight, FlightClassType.ECONOMY)) classCounts[FlightClassType.ECONOMY]++;\n        if (this.isFlightMatchingClass(flight, FlightClassType.BUSINESS)) classCounts[FlightClassType.BUSINESS]++;\n      });\n      console.log('Flights by class (before filtering):', classCounts);\n      // Option pour désactiver temporairement le filtrage par classe (pour débogage)\n      // Mettre à true pour voir tous les vols disponibles sans filtrage par classe\n      const disableClassFiltering = true;\n      if (!disableClassFiltering) {\n        results = results.filter(flight => this.isFlightMatchingClass(flight, selectedClass));\n        console.log('Flights after class filtering:', results.length);\n      } else {\n        console.log('Class filtering disabled for debugging');\n      }\n    }\n    // Filtrer par nombre d'escales\n    if (this.sidebarFilters.stops.direct || this.sidebarFilters.stops.oneStop || this.sidebarFilters.stops.multiStop) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0) return false;\n        const stopCount = flight.items[0].stopCount || 0;\n        return this.sidebarFilters.stops.direct && stopCount === 0 || this.sidebarFilters.stops.oneStop && stopCount === 1 || this.sidebarFilters.stops.multiStop && stopCount >= 2;\n      });\n    }\n    // Filtrer par horaire de départ\n    if (this.sidebarFilters.departureTime.earlyMorning || this.sidebarFilters.departureTime.morning || this.sidebarFilters.departureTime.afternoon || this.sidebarFilters.departureTime.evening) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].departure || !flight.items[0].departure.date) {\n          return false;\n        }\n        const departureDate = new Date(flight.items[0].departure.date);\n        const hours = departureDate.getHours();\n        return this.sidebarFilters.departureTime.earlyMorning && hours >= 0 && hours < 8 || this.sidebarFilters.departureTime.morning && hours >= 8 && hours < 12 || this.sidebarFilters.departureTime.afternoon && hours >= 12 && hours < 18 || this.sidebarFilters.departureTime.evening && hours >= 18 && hours < 24;\n      });\n    }\n    // Filtrer par horaire d'arrivée\n    if (this.sidebarFilters.arrivalTime.earlyMorning || this.sidebarFilters.arrivalTime.morning || this.sidebarFilters.arrivalTime.afternoon || this.sidebarFilters.arrivalTime.evening) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].arrival || !flight.items[0].arrival.date) {\n          return false;\n        }\n        const arrivalDate = new Date(flight.items[0].arrival.date);\n        const hours = arrivalDate.getHours();\n        return this.sidebarFilters.arrivalTime.earlyMorning && hours >= 0 && hours < 8 || this.sidebarFilters.arrivalTime.morning && hours >= 8 && hours < 12 || this.sidebarFilters.arrivalTime.afternoon && hours >= 12 && hours < 18 || this.sidebarFilters.arrivalTime.evening && hours >= 18 && hours < 24;\n      });\n    }\n    // Filtrer par compagnie aérienne\n    const selectedAirlines = Object.entries(this.sidebarFilters.airlines).filter(([_, selected]) => selected).map(([airline, _]) => airline);\n    if (selectedAirlines.length > 0) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].airline || !flight.items[0].airline.name) {\n          return false;\n        }\n        return selectedAirlines.includes(flight.items[0].airline.name);\n      });\n    }\n    // Étape 2: Appliquer le tri selon le filtre sélectionné en haut\n    switch (this.currentFilter) {\n      case 'cheapest':\n        this.filteredResults = this.sortByPrice(results);\n        break;\n      case 'shortest':\n        this.filteredResults = this.sortByDuration(results);\n        break;\n      case 'recommended':\n      default:\n        this.filteredResults = this.sortByRecommendation(results);\n        break;\n    }\n  }\n  // Trier les vols par prix (du moins cher au plus cher)\n  sortByPrice(flights) {\n    return flights.sort((a, b) => {\n      const priceA = this.getMinPriceAmount(a);\n      const priceB = this.getMinPriceAmount(b);\n      return priceA - priceB;\n    });\n  }\n  // Trier les vols par durée (du plus court au plus long)\n  sortByDuration(flights) {\n    return flights.sort((a, b) => {\n      const durationA = a.items && a.items[0] ? a.items[0].duration : Number.MAX_VALUE;\n      const durationB = b.items && b.items[0] ? b.items[0].duration : Number.MAX_VALUE;\n      return durationA - durationB;\n    });\n  }\n  // Trier les vols par recommandation (combinaison de prix, durée et autres facteurs)\n  sortByRecommendation(flights) {\n    return flights.sort((a, b) => {\n      // Calculer un score pour chaque vol basé sur plusieurs facteurs\n      const scoreA = this.calculateRecommendationScore(a);\n      const scoreB = this.calculateRecommendationScore(b);\n      return scoreB - scoreA; // Ordre décroissant (score plus élevé = meilleur)\n    });\n  }\n  // Calculer un score de recommandation pour un vol\n  calculateRecommendationScore(flight) {\n    if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n      return 0;\n    }\n    const item = flight.items[0];\n    const offer = flight.offers[0];\n    // Facteurs à considérer pour le score\n    const price = this.getMinPriceAmount(flight);\n    const duration = item.duration;\n    const stopCount = item.stopCount || 0;\n    const availability = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    // Normaliser les valeurs (plus le score est élevé, meilleur est le vol)\n    const priceScore = 1000 / (price + 100); // Inverse du prix (moins cher = meilleur score)\n    const durationScore = 1000 / (duration + 100); // Inverse de la durée (plus court = meilleur score)\n    const stopScore = 1 / (stopCount + 1); // Moins d'escales = meilleur score\n    const availabilityScore = Math.min(availability, 10) / 10; // Plus de disponibilité = meilleur score\n    // Pondération des facteurs (ajustable selon les préférences)\n    const weights = {\n      price: 0.4,\n      duration: 0.3,\n      stops: 0.2,\n      availability: 0.1 // 10% importance pour la disponibilité\n    };\n    // Calculer le score final pondéré\n    return priceScore * weights.price + durationScore * weights.duration + stopScore * weights.stops + availabilityScore * weights.availability;\n  }\n  // Obtenir le montant du prix minimum pour un vol\n  getMinPriceAmount(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return Number.MAX_VALUE;\n    }\n    return flight.offers.reduce((min, offer) => offer.price && offer.price.amount < min ? offer.price.amount : min, flight.offers[0].price ? flight.offers[0].price.amount : Number.MAX_VALUE);\n  }\n  // Calculer les prix minimums pour chaque option de filtre\n  calculateFilterPrices() {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      return;\n    }\n    // Réinitialiser les prix\n    this.resetFilterPrices();\n    // Collecter toutes les compagnies aériennes\n    const airlines = new Set();\n    // Parcourir tous les vols pour calculer les prix minimums\n    this.searchResults.forEach(flight => {\n      if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n        return;\n      }\n      const item = flight.items[0];\n      const price = this.getMinPriceAmount(flight);\n      // Ajouter la compagnie aérienne à la liste\n      if (item.airline && item.airline.name) {\n        airlines.add(item.airline.name);\n        // Initialiser le prix pour cette compagnie si nécessaire\n        if (!(item.airline.name in this.filterPrices.airlines)) {\n          this.filterPrices.airlines[item.airline.name] = Number.MAX_VALUE;\n        }\n        // Mettre à jour le prix minimum pour cette compagnie\n        this.filterPrices.airlines[item.airline.name] = Math.min(this.filterPrices.airlines[item.airline.name], price);\n      }\n      // Mettre à jour les prix par nombre d'escales\n      const stopCount = item.stopCount || 0;\n      if (stopCount === 0) {\n        this.filterPrices.stops.direct = Math.min(this.filterPrices.stops.direct, price);\n      } else if (stopCount === 1) {\n        this.filterPrices.stops.oneStop = Math.min(this.filterPrices.stops.oneStop, price);\n      } else {\n        this.filterPrices.stops.multiStop = Math.min(this.filterPrices.stops.multiStop, price);\n      }\n      // Mettre à jour les prix par horaire de départ\n      if (item.departure && item.departure.date) {\n        const departureDate = new Date(item.departure.date);\n        const departureHours = departureDate.getHours();\n        if (departureHours >= 0 && departureHours < 8) {\n          this.filterPrices.departureTime.earlyMorning = Math.min(this.filterPrices.departureTime.earlyMorning, price);\n        } else if (departureHours >= 8 && departureHours < 12) {\n          this.filterPrices.departureTime.morning = Math.min(this.filterPrices.departureTime.morning, price);\n        } else if (departureHours >= 12 && departureHours < 18) {\n          this.filterPrices.departureTime.afternoon = Math.min(this.filterPrices.departureTime.afternoon, price);\n        } else {\n          this.filterPrices.departureTime.evening = Math.min(this.filterPrices.departureTime.evening, price);\n        }\n      }\n      // Mettre à jour les prix par horaire d'arrivée\n      if (item.arrival && item.arrival.date) {\n        const arrivalDate = new Date(item.arrival.date);\n        const arrivalHours = arrivalDate.getHours();\n        if (arrivalHours >= 0 && arrivalHours < 8) {\n          this.filterPrices.arrivalTime.earlyMorning = Math.min(this.filterPrices.arrivalTime.earlyMorning, price);\n        } else if (arrivalHours >= 8 && arrivalHours < 12) {\n          this.filterPrices.arrivalTime.morning = Math.min(this.filterPrices.arrivalTime.morning, price);\n        } else if (arrivalHours >= 12 && arrivalHours < 18) {\n          this.filterPrices.arrivalTime.afternoon = Math.min(this.filterPrices.arrivalTime.afternoon, price);\n        } else {\n          this.filterPrices.arrivalTime.evening = Math.min(this.filterPrices.arrivalTime.evening, price);\n        }\n      }\n    });\n    // Initialiser les filtres de compagnies aériennes\n    airlines.forEach(airline => {\n      if (!(airline in this.sidebarFilters.airlines)) {\n        this.sidebarFilters.airlines[airline] = false;\n      }\n    });\n    // Remplacer les valeurs MAX_VALUE par 0 pour les options sans vols\n    this.cleanupFilterPrices();\n  }\n  // Réinitialiser les prix des filtres\n  resetFilterPrices() {\n    this.filterPrices = {\n      stops: {\n        direct: Number.MAX_VALUE,\n        oneStop: Number.MAX_VALUE,\n        multiStop: Number.MAX_VALUE\n      },\n      departureTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      arrivalTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      airlines: {}\n    };\n  }\n  // Nettoyer les prix des filtres (remplacer MAX_VALUE par 0)\n  cleanupFilterPrices() {\n    // Escales\n    if (this.filterPrices.stops.direct === Number.MAX_VALUE) this.filterPrices.stops.direct = 0;\n    if (this.filterPrices.stops.oneStop === Number.MAX_VALUE) this.filterPrices.stops.oneStop = 0;\n    if (this.filterPrices.stops.multiStop === Number.MAX_VALUE) this.filterPrices.stops.multiStop = 0;\n    // Horaires de départ\n    if (this.filterPrices.departureTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.departureTime.earlyMorning = 0;\n    if (this.filterPrices.departureTime.morning === Number.MAX_VALUE) this.filterPrices.departureTime.morning = 0;\n    if (this.filterPrices.departureTime.afternoon === Number.MAX_VALUE) this.filterPrices.departureTime.afternoon = 0;\n    if (this.filterPrices.departureTime.evening === Number.MAX_VALUE) this.filterPrices.departureTime.evening = 0;\n    // Horaires d'arrivée\n    if (this.filterPrices.arrivalTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.arrivalTime.earlyMorning = 0;\n    if (this.filterPrices.arrivalTime.morning === Number.MAX_VALUE) this.filterPrices.arrivalTime.morning = 0;\n    if (this.filterPrices.arrivalTime.afternoon === Number.MAX_VALUE) this.filterPrices.arrivalTime.afternoon = 0;\n    if (this.filterPrices.arrivalTime.evening === Number.MAX_VALUE) this.filterPrices.arrivalTime.evening = 0;\n    // Compagnies aériennes\n    Object.keys(this.filterPrices.airlines).forEach(airline => {\n      if (this.filterPrices.airlines[airline] === Number.MAX_VALUE) {\n        this.filterPrices.airlines[airline] = 0;\n      }\n    });\n  }\n  // Basculer l'état d'expansion d'une section\n  toggleSection(section) {\n    this.expandedSections[section] = !this.expandedSections[section];\n  }\n  // Basculer un filtre d'escale\n  toggleStopFilter(filter) {\n    this.sidebarFilters.stops[filter] = !this.sidebarFilters.stops[filter];\n    this.applyAllFilters();\n  }\n  // Basculer un filtre d'horaire de départ\n  toggleDepartureTimeFilter(filter) {\n    this.sidebarFilters.departureTime[filter] = !this.sidebarFilters.departureTime[filter];\n    this.applyAllFilters();\n  }\n  // Basculer un filtre d'horaire d'arrivée\n  toggleArrivalTimeFilter(filter) {\n    this.sidebarFilters.arrivalTime[filter] = !this.sidebarFilters.arrivalTime[filter];\n    this.applyAllFilters();\n  }\n  // Basculer un filtre de compagnie aérienne\n  toggleAirlineFilter(airline) {\n    this.sidebarFilters.airlines[airline] = !this.sidebarFilters.airlines[airline];\n    this.applyAllFilters();\n  }\n  // Effacer tous les filtres\n  clearAllFilters() {\n    // Réinitialiser les filtres d'escales\n    this.sidebarFilters.stops.direct = false;\n    this.sidebarFilters.stops.oneStop = false;\n    this.sidebarFilters.stops.multiStop = false;\n    // Réinitialiser les filtres d'horaires de départ\n    this.sidebarFilters.departureTime.earlyMorning = false;\n    this.sidebarFilters.departureTime.morning = false;\n    this.sidebarFilters.departureTime.afternoon = false;\n    this.sidebarFilters.departureTime.evening = false;\n    // Réinitialiser les filtres d'horaires d'arrivée\n    this.sidebarFilters.arrivalTime.earlyMorning = false;\n    this.sidebarFilters.arrivalTime.morning = false;\n    this.sidebarFilters.arrivalTime.afternoon = false;\n    this.sidebarFilters.arrivalTime.evening = false;\n    // Réinitialiser les filtres de compagnies aériennes\n    Object.keys(this.sidebarFilters.airlines).forEach(airline => {\n      this.sidebarFilters.airlines[airline] = false;\n    });\n    // Appliquer les filtres (qui seront tous désactivés)\n    this.applyAllFilters();\n  }\n  // Formater le prix pour l'affichage\n  formatPrice(price) {\n    if (price === 0) return '-';\n    return price.toFixed(0) + ' €';\n  }\n  // Obtenir les clés des compagnies aériennes\n  getAirlineKeys() {\n    return Object.keys(this.sidebarFilters.airlines);\n  }\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight) {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n    header.appendChild(logo);\n    header.appendChild(title);\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n        generalInfo.appendChild(airlineInfo);\n      }\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n      routeSection.appendChild(routeVisual);\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n          segmentsList.appendChild(segmentItem);\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n            segmentsList.appendChild(layover);\n          }\n        });\n        routeSection.appendChild(segmentsList);\n      }\n    }\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n          const baggageContainer = document.createElement('div');\n          baggageContainer.style.display = 'flex';\n          baggageContainer.style.flexDirection = 'column';\n          baggageContainer.style.gap = '10px';\n          baggageContainer.style.marginBottom = '15px';\n          // Filtrer et regrouper les bagages par type\n          const checkedBaggage = offer.baggageInformations.filter(b => b.baggageType === 2);\n          const cabinBaggage = offer.baggageInformations.filter(b => b.baggageType === 1);\n          const handBaggage = offer.baggageInformations.filter(b => b.baggageType === 3);\n          // Bagages en soute\n          if (checkedBaggage.length > 0) {\n            checkedBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#e7f5ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #c5e1f9';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-suitcase\" style=\"color: #4a6fa5; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Checked Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n          // Bagages cabine\n          if (cabinBaggage.length > 0) {\n            cabinBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#f3f0ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #e5dbff';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Cabin Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          } else {\n            // Ajouter un bagage cabine par défaut si aucun n'est spécifié\n            const baggageItem = document.createElement('div');\n            baggageItem.style.display = 'flex';\n            baggageItem.style.alignItems = 'center';\n            baggageItem.style.padding = '10px 15px';\n            baggageItem.style.backgroundColor = '#f3f0ff';\n            baggageItem.style.borderRadius = '6px';\n            baggageItem.style.border = '1px solid #e5dbff';\n            const baggageIcon = document.createElement('div');\n            baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n            const baggageInfo = document.createElement('div');\n            baggageInfo.style.display = 'flex';\n            baggageInfo.style.flexDirection = 'column';\n            const baggageType = document.createElement('div');\n            baggageType.textContent = 'Cabin Baggage';\n            baggageType.style.fontWeight = '600';\n            baggageType.style.fontSize = '14px';\n            const baggageDetails = document.createElement('div');\n            baggageDetails.style.fontSize = '12px';\n            baggageDetails.style.color = '#666';\n            baggageDetails.textContent = 'Included';\n            baggageInfo.appendChild(baggageType);\n            baggageInfo.appendChild(baggageDetails);\n            baggageItem.appendChild(baggageIcon);\n            baggageItem.appendChild(baggageInfo);\n            baggageContainer.appendChild(baggageItem);\n          }\n          // Bagages à main\n          if (handBaggage.length > 0) {\n            handBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#fff4e6';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #ffe8cc';\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-shopping-bag\" style=\"color: #e8590c; font-size: 16px; margin-right: 10px;\"></i>';\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Hand Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n          offerItem.appendChild(baggageContainer);\n        }\n        offerItem.appendChild(offerDetails);\n        offersList.appendChild(offerItem);\n      });\n      offersSection.appendChild(offersList);\n    }\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n  // Méthode utilitaire pour créer une section\n  createSection(title, iconClass) {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n    return section;\n  }\n  // Méthode utilitaire pour créer une ligne d'information\n  createInfoRow(label, value) {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n    return row;\n  }\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight) {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n      // Obtenir les informations de passagers\n      const passengerCounts = this.sharedDataService.getPassengerCounts();\n      // Sérialiser les informations de passagers pour les passer dans l'URL\n      const passengerInfo = JSON.stringify(passengerCounts);\n      console.log('Navigating to get-offer with searchId:', searchId, 'offerId:', offerId, 'and passengers:', passengerInfo);\n      // Rediriger vers la page get-offer avec les informations de passagers\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId,\n          passengers: passengerInfo\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n  preloadLocations() {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n  setupAutocomplete() {\n    // Charger les locations par type par défaut\n    const departureLocationType = 2; // Type 2 (City) par défaut\n    const arrivalLocationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(departureLocationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(departureLocationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(arrivalLocationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(arrivalLocationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  displayLocation(location) {\n    if (!location) return '';\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n  // Méthode pour changer le type de recherche (aller simple, aller-retour)\n  setSearchType(type) {\n    this.currentSearchType = type;\n    // Mettre à jour le type de service dans le formulaire\n    if (type === this.SEARCH_TYPE_ONE_WAY) {\n      this.searchForm.get('serviceTypes')?.setValue(['1']);\n      // Rendre le champ de date de retour optionnel\n      this.searchForm.get('returnDate')?.clearValidators();\n    } else if (type === this.SEARCH_TYPE_ROUND_TRIP) {\n      this.searchForm.get('serviceTypes')?.setValue(['2']);\n      // Rendre le champ de date de retour obligatoire\n      this.searchForm.get('returnDate')?.setValidators([Validators.required]);\n    }\n    // Mettre à jour les validateurs\n    this.searchForm.get('returnDate')?.updateValueAndValidity();\n  }\n  onSearch() {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n    const formValue = this.searchForm.value;\n    // Vérifier la valeur de la classe de vol et le type de recherche\n    console.log('Form values:', formValue);\n    console.log('Selected flight class:', formValue.flightClass);\n    console.log('Current search type:', this.currentSearchType);\n    // Créer la requête en fonction du type de recherche (aller simple ou aller-retour)\n    let request;\n    if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n      // Requête pour vol aller simple\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['1'],\n        CheckIn: formValue.departureDate,\n        DepartureLocations: [{\n          id: formValue.departureLocation?.id || '',\n          type: 2 // Type 2 (City) par défaut\n        }],\n\n        ArrivalLocations: [{\n          id: formValue.arrivalLocation?.id || '',\n          type: 5 // Type 5 (Airport) par défaut\n        }],\n\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n      console.log('Sending one-way search request with ServiceTypes:', request.ServiceTypes);\n    } else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      // Requête pour vol aller-retour\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['2'],\n        CheckIn: formValue.departureDate,\n        ReturnDate: formValue.returnDate,\n        DepartureLocations: [{\n          id: formValue.departureLocation?.id || '',\n          type: 2 // Type 2 (City) par défaut\n        }],\n\n        ArrivalLocations: [{\n          id: formValue.arrivalLocation?.id || '',\n          type: 5 // Type 5 (Airport) par défaut\n        }],\n\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n      console.log('Sending round-trip search request with ServiceTypes:', request.ServiceTypes);\n    }\n    // Créer la requête appropriée selon le type de recherche\n    if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n      // Requête pour vol aller simple\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['1'],\n        CheckIn: formValue.departureDate,\n        DepartureLocations: [{\n          id: formValue.departureLocation?.id || '',\n          type: 2 // Type 2 (City) par défaut\n        }],\n\n        ArrivalLocations: [{\n          id: formValue.arrivalLocation?.id || '',\n          type: 5 // Type 5 (Airport) par défaut\n        }],\n\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n      console.log('Sending one-way search request with ServiceTypes:', request.ServiceTypes);\n    } else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      // Requête pour vol aller-retour\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['2'],\n        CheckIn: formValue.departureDate,\n        ReturnDate: formValue.returnDate,\n        DepartureLocations: [{\n          id: formValue.departureLocation?.id || '',\n          type: 2 // Type 2 (City) par défaut\n        }],\n\n        ArrivalLocations: [{\n          id: formValue.arrivalLocation?.id || '',\n          type: 5 // Type 5 (Airport) par défaut\n        }],\n\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n      console.log('Sending round-trip search request with ServiceTypes:', request.ServiceTypes);\n    }\n    // Ajouter des logs pour déboguer les dates\n    console.log('Search request details:');\n    console.log('Search type:', this.currentSearchType === this.SEARCH_TYPE_ONE_WAY ? 'One-way' : 'Round-trip');\n    console.log('Departure date:', formValue.departureDate);\n    if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      console.log('Return date:', formValue.returnDate);\n    }\n    // Utiliser la méthode searchPrice pour envoyer la requête\n    this.productService.searchPrice(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.header.success) {\n          this.searchResults = response.body.flights;\n          // Ajouter des logs pour analyser les dates dans la réponse\n          console.log('API response analysis:');\n          console.log('Total flights received:', this.searchResults.length);\n          // Analyser les dates des vols reçus\n          if (this.searchResults.length > 0) {\n            console.group('Flight dates analysis');\n            this.searchResults.slice(0, 5).forEach(flight => {\n              if (flight.items && flight.items.length > 0) {\n                console.log('Flight ID:', flight.id);\n                // Analyser les dates du premier item (aller)\n                if (flight.items[0].departure && flight.items[0].departure.date) {\n                  const depDate = new Date(flight.items[0].departure.date);\n                  console.log('Outbound departure:', depDate.toISOString().split('T')[0], depDate.toLocaleTimeString());\n                }\n                // Analyser les dates du second item (retour) si présent\n                if (flight.items.length > 1 && flight.items[1].departure && flight.items[1].departure.date) {\n                  const retDate = new Date(flight.items[1].departure.date);\n                  console.log('Inbound departure:', retDate.toISOString().split('T')[0], retDate.toLocaleTimeString());\n                }\n                // Analyser les segments si présents\n                if (flight.items[0].segments && flight.items[0].segments.length > 1) {\n                  console.log('Segments count:', flight.items[0].segments.length);\n                  const firstSegment = flight.items[0].segments[0];\n                  const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n                  if (firstSegment.departure && firstSegment.departure.date) {\n                    const firstDepDate = new Date(firstSegment.departure.date);\n                    console.log('First segment departure:', firstDepDate.toISOString().split('T')[0], firstDepDate.toLocaleTimeString());\n                  }\n                  if (lastSegment.arrival && lastSegment.arrival.date) {\n                    const lastArrDate = new Date(lastSegment.arrival.date);\n                    console.log('Last segment arrival:', lastArrDate.toISOString().split('T')[0], lastArrDate.toLocaleTimeString());\n                  }\n                }\n              }\n            });\n            console.groupEnd();\n          }\n          // Appliquer le filtre par date\n          const filteredByDate = this.filterResultsByDate(this.searchResults);\n          console.log('Flights matching requested dates:', filteredByDate.length, 'out of', this.searchResults.length);\n          // Analyser les classes de vol dans les résultats\n          console.group('Flight Class Analysis');\n          console.log('Selected flight class:', formValue.flightClass);\n          console.log('Total flights received:', response.body.flights.length);\n          // Compter les vols par classe\n          const flightsByClass = {\n            [FlightClassType.PROMO.toString()]: 0,\n            [FlightClassType.ECONOMY.toString()]: 0,\n            [FlightClassType.BUSINESS.toString()]: 0,\n            'unknown': 0\n          };\n          response.body.flights.forEach((flight, index) => {\n            if (flight.items && flight.items.length > 0 && flight.items[0].flightClass) {\n              const classType = flight.items[0].flightClass.type.toString();\n              if (flightsByClass[classType] !== undefined) {\n                flightsByClass[classType]++;\n              } else {\n                flightsByClass['unknown']++;\n              }\n              // Afficher les détails de classe pour chaque vol\n              console.log(`Flight ${flight.id} class:`, flight.items[0].flightClass ? `${flight.items[0].flightClass.name} (type: ${flight.items[0].flightClass.type})` : 'No class info');\n              // Afficher la structure complète du premier vol pour analyse\n              if (index === 0) {\n                console.group('First Flight Structure');\n                console.log('Flight ID:', flight.id);\n                console.log('Flight Items:', flight.items);\n                if (flight.items && flight.items.length > 0) {\n                  console.log('First Item FlightClass:', flight.items[0].flightClass);\n                  if (flight.items[0].segments) {\n                    console.log('Segments:', flight.items[0].segments);\n                    flight.items[0].segments.forEach((segment, segIndex) => {\n                      console.log(`Segment ${segIndex} FlightClass:`, segment.flightClass);\n                    });\n                  }\n                }\n                if (flight.offers && flight.offers.length > 0) {\n                  console.log('First Offer:', flight.offers[0]);\n                  console.log('FlightClassInformations:', flight.offers[0].flightClassInformations);\n                }\n                console.groupEnd();\n              }\n            } else {\n              flightsByClass['unknown']++;\n            }\n          });\n          console.log('Flights by class:', flightsByClass);\n          console.groupEnd();\n          // Calculer les prix minimums pour chaque option de filtre\n          this.calculateFilterPrices();\n          // Appliquer le filtre actuel aux résultats\n          this.applyAllFilters();\n          // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n          // console.log('Full API Response:', JSON.stringify(response, null, 2));\n          // Analyser les données de disponibilité\n          if (response.body && response.body.flights && response.body.flights.length > 0) {\n            console.group('Availability Analysis');\n            console.log('Total flights:', response.body.flights.length);\n            // Compter les vols avec des offres\n            const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n            console.log('Flights with offers:', flightsWithOffers.length);\n            // Analyser les valeurs de disponibilité\n            const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability !== undefined ? o.availability : o.seatInfo ? o.seatInfo.availableSeatCount : 0));\n            console.log('Availability values:', availabilityValues);\n            // Compter les différentes valeurs de disponibilité\n            const availabilityCounts = availabilityValues.reduce((acc, val) => {\n              if (val !== undefined) {\n                acc[val] = (acc[val] || 0) + 1;\n              }\n              return acc;\n            }, {});\n            console.log('Availability counts:', availabilityCounts);\n            // Vérifier les vols réservables\n            const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true));\n            console.log('Reservable flights:', reservableFlights.length);\n            console.groupEnd();\n          }\n          // Vérifier si searchId existe dans le corps de la réponse\n          if (response.body && response.body.searchId) {\n            this.lastSearchId = response.body.searchId;\n            console.log('Search ID found in body.searchId:', this.lastSearchId);\n          }\n          // Vérifier si searchId existe dans l'en-tête de la réponse\n          else if (response.header && response.header.requestId) {\n            this.lastSearchId = response.header.requestId;\n            console.log('Search ID found in header.requestId:', this.lastSearchId);\n          }\n          // Vérifier d'autres emplacements possibles\n          else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n            this.lastSearchId = response.body.flights[0].id;\n            console.log('Using flight ID as search ID:', this.lastSearchId);\n          } else {\n            console.error('No search ID found in the response!');\n            console.log('Response structure:', Object.keys(response));\n            if (response.body) console.log('Body structure:', Object.keys(response.body));\n            if (response.header) console.log('Header structure:', Object.keys(response.header));\n          }\n        } else {\n          this.errorMessage = 'The search could not be completed. Please try again.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred during the search. Please try again.';\n        console.error('Error searching flights:', error);\n      }\n    });\n  }\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  // Formater la durée en heures et minutes\n  formatDuration(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n  // Formater la date pour l'affichage\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n    const minOffer = flight.offers.reduce((min, offer) => offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight) {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    return availabilityValue > 0;\n  }\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations() {\n    const locationType = 2; // Type 2 (City) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations() {\n    const locationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations() {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      arrivalLocation: departureLocation\n    });\n  }\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString) {\n    if (!dateString) return 'N/A';\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType) {\n    switch (baggageType) {\n      case 1:\n        return 'Cabin Baggage';\n      case 2:\n        return 'Checked Baggage';\n      case 3:\n        return 'Hand Baggage';\n      default:\n        return 'Baggage';\n    }\n  }\n  // Filtrer les bagages par type\n  filterBaggageByType(baggageInformations, type) {\n    if (!baggageInformations || !Array.isArray(baggageInformations)) {\n      return [];\n    }\n    return baggageInformations.filter(baggage => baggage.baggageType === type);\n  }\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType) {\n    switch (passengerType) {\n      case 1:\n        return 'Adult';\n      case 2:\n        return 'Child';\n      case 3:\n        return 'Infant';\n      default:\n        return 'Passenger';\n    }\n  }\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment, nextSegment) {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date || !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n  // Détecter si un vol est un aller-retour\n  isRoundTripFlight(flight) {\n    // Vérifier si le vol a plusieurs items (aller et retour)\n    if (flight.items && flight.items.length > 1) {\n      return true;\n    }\n    // Vérifier si le premier item a des segments qui forment un aller-retour\n    // (généralement, les segments d'un aller-retour forment une boucle)\n    if (flight.items && flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      const firstSegment = flight.items[0].segments[0];\n      const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n      // Vérifier si le premier segment part de A vers B et le dernier segment part de B vers A\n      if (firstSegment && firstSegment.departure && firstSegment.arrival && lastSegment && lastSegment.departure && lastSegment.arrival) {\n        const firstDeparture = firstSegment.departure.airport?.code || firstSegment.departure.city?.name;\n        const firstArrival = firstSegment.arrival.airport?.code || firstSegment.arrival.city?.name;\n        const lastDeparture = lastSegment.departure.airport?.code || lastSegment.departure.city?.name;\n        const lastArrival = lastSegment.arrival.airport?.code || lastSegment.arrival.city?.name;\n        // Si le dernier segment revient au point de départ du premier segment\n        if (firstDeparture && lastArrival && firstDeparture === lastArrival) {\n          return true;\n        }\n      }\n    }\n    // Vérifier si le vol a été recherché avec ServiceTypes = 2 (aller-retour)\n    return this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP;\n  }\n  // Obtenir les segments aller d'un vol aller-retour\n  getOutboundSegments(flight) {\n    if (!flight.items) return [];\n    // Si le vol a plusieurs items, le premier est généralement l'aller\n    if (flight.items.length > 1) {\n      return flight.items[0].segments || [flight.items[0]];\n    }\n    // Si le vol a un seul item avec plusieurs segments\n    if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      // Pour un aller-retour, on considère la première moitié des segments comme l'aller\n      const halfLength = Math.ceil(flight.items[0].segments.length / 2);\n      return flight.items[0].segments.slice(0, halfLength);\n    }\n    return flight.items[0].segments || [flight.items[0]];\n  }\n  // Obtenir les segments retour d'un vol aller-retour\n  getInboundSegments(flight) {\n    if (!flight.items) return [];\n    // Si le vol a plusieurs items, le second est généralement le retour\n    if (flight.items.length > 1) {\n      return flight.items[1].segments || [flight.items[1]];\n    }\n    // Si le vol a un seul item avec plusieurs segments\n    if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      // Pour un aller-retour, on considère la seconde moitié des segments comme le retour\n      const halfLength = Math.ceil(flight.items[0].segments.length / 2);\n      return flight.items[0].segments.slice(halfLength);\n    }\n    return [];\n  }\n};\n__decorate([HostListener('document:click', ['$event'])], SearchPriceComponent.prototype, \"onDocumentClick\", null);\nSearchPriceComponent = __decorate([Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css', './flight-type-tabs.css', './round-trip-styles.css'],\n  encapsulation: ViewEncapsulation.None\n})], SearchPriceComponent);", "map": {"version": 3, "names": ["Component", "ViewEncapsulation", "HostListener", "FormGroup", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "of", "FlightClassType", "LocationType", "PassengerType", "SearchPriceComponent", "constructor", "fb", "productService", "router", "sharedDataService", "departureLocations", "arrivalLocations", "isLoading", "searchResults", "filteredResults", "hasSearched", "errorMessage", "lastSearchId", "SEARCH_TYPE_ONE_WAY", "SEARCH_TYPE_ROUND_TRIP", "SEARCH_TYPE_MULTI_CITY", "currentSearchType", "showPassengerDropdown", "passengerCounts", "Adult", "Child", "Infant", "currentFilter", "filterOptions", "value", "label", "icon", "sidebarFilters", "stops", "direct", "oneStop", "multiStop", "departureTime", "earlyMorning", "morning", "afternoon", "evening", "arrivalTime", "airlines", "expandedSections", "filterPrices", "passengerTypes", "flightClasses", "PROMO", "ECONOMY", "BUSINESS", "minDate", "Date", "toISOString", "split", "searchForm", "group", "productType", "required", "serviceTypes", "departureLocation", "arrivalLocation", "departureDate", "returnDate", "flightClass", "nonStop", "culture", "currency", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightBaggageGetOption", "set<PERSON>assengerCounts", "onDocumentClick", "event", "clickedElement", "target", "passengerSelector", "document", "querySelector", "contains", "togglePassengerDropdown", "stopPropagation", "closePassengerDropdown", "get<PERSON>assengerCount", "type", "increasePassengerCount", "getTotalPassengers", "decreasePassengerCount", "Object", "values", "reduce", "sum", "count", "getPassengersArray", "ngOnInit", "setupAutocomplete", "preloadLocations", "console", "log", "applyFilter", "filterValue", "applyAllFilters", "isFlightMatchingClass", "flight", "selectedClass", "items", "length", "id", "item", "name", "segments", "segment", "offers", "offer", "flightClassInformations", "classInfo", "filterResultsByDate", "results", "formValue", "setHours", "filter", "departure", "date", "flightDepartureDate", "getTime", "outboundDepartureDate", "inboundDepartureDate", "<PERSON><PERSON><PERSON><PERSON>", "Math", "ceil", "get", "undefined", "classCounts", "for<PERSON>ach", "disableClassFiltering", "stopCount", "hours", "getHours", "arrival", "arrivalDate", "selectedAirlines", "entries", "_", "selected", "airline", "includes", "sortByPrice", "sortByDuration", "sortByRecommendation", "flights", "sort", "a", "b", "priceA", "getMinPriceAmount", "priceB", "durationA", "duration", "Number", "MAX_VALUE", "durationB", "scoreA", "calculateRecommendationScore", "scoreB", "price", "availability", "seatInfo", "availableSeatCount", "priceScore", "durationScore", "stopScore", "availabilityScore", "min", "weights", "amount", "calculateFilterPrices", "resetFilterPrices", "Set", "add", "departureHours", "arrivalHours", "cleanupFilterPrices", "keys", "toggleSection", "section", "toggleStopFilter", "toggleDepartureTimeFilter", "toggleArrivalTimeFilter", "toggleAirlineFilter", "clearAllFilters", "formatPrice", "toFixed", "getAirlineKeys", "showAllDetails", "modalDiv", "createElement", "style", "position", "top", "left", "width", "height", "backgroundColor", "zIndex", "display", "justifyContent", "alignItems", "modalContent", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "color", "fontFamily", "closeButton", "innerHTML", "right", "border", "background", "fontSize", "cursor", "transition", "on<PERSON><PERSON>ver", "onmouseout", "onclick", "body", "<PERSON><PERSON><PERSON><PERSON>", "header", "marginBottom", "paddingBottom", "borderBottom", "logo", "title", "textContent", "margin", "fontWeight", "append<PERSON><PERSON><PERSON>", "detailsContainer", "flexDirection", "gap", "generalInfo", "createSection", "airlineInfo", "thumbnailFull", "airlineLogo", "src", "alt", "marginRight", "airlineIcon", "airlineName", "internationalCode", "flightNumberRow", "createInfoRow", "flightNo", "flightDateRow", "flightDate", "toLocaleDateString", "durationRow", "formatDuration", "classRow", "code", "stopsRow", "routeSection", "routeVisual", "textAlign", "flex", "toLocaleTimeString", "hour", "minute", "departureAirport", "marginTop", "airport", "departureCity", "city", "connectionLine", "line", "plane", "marginLeft", "arrivalAirport", "arrivalCity", "segmentsTitle", "segmentsList", "index", "segmentItem", "segmentHeader", "segmentTitle", "segmentDuration", "segmentRoute", "segmentDeparture", "depTime", "depAirport", "arrow", "segmentArrival", "arrTime", "arrAirport", "layover", "currentArrival", "nextDeparture", "layoverTime", "floor", "offersSection", "offersList", "offerItem", "offerHeader", "offerTitle", "offerPrice", "offerDetails", "gridTemplateColumns", "availabilityValue", "expiresOn", "expires", "toLocaleString", "brandedFare", "reservableInfo", "reservable", "baggageInformations", "baggageTitle", "baggageContainer", "checkedBaggage", "baggageType", "cabinBaggage", "handBaggage", "baggage", "baggageItem", "baggageIcon", "baggageInfo", "baggageDetails", "detailsText", "weight", "piece", "services", "servicesSection", "servicesList", "listStyle", "service", "serviceItem", "iconClass", "section<PERSON><PERSON><PERSON>", "className", "sectionTitle", "row", "labelElement", "valueElement", "selectThisFlight", "offerId", "searchId", "getPassengerCounts", "passengerInfo", "JSON", "stringify", "navigate", "queryParams", "passengers", "error", "departureLocationType", "arrivalLocationType", "getLocationsByType", "subscribe", "locations", "valueChanges", "pipe", "location", "toLowerCase", "displayLocation", "displayText", "Airport", "setSearchType", "setValue", "clearValidators", "setValidators", "updateValueAndValidity", "onSearch", "invalid", "markFormGroupTouched", "request", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "ArrivalLocations", "Passengers", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "ReturnDate", "searchPrice", "next", "response", "success", "slice", "depDate", "retDate", "firstSegment", "lastSegment", "firstDepDate", "lastArrDate", "groupEnd", "filteredByDate", "flightsByClass", "toString", "classType", "segIndex", "flightsWithOffers", "f", "availabilityValues", "flatMap", "o", "availabilityCounts", "acc", "val", "reservableFlights", "some", "requestId", "messages", "message", "formGroup", "controls", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "minutes", "mins", "formatDate", "dateString", "weekday", "day", "month", "getMinPrice", "min<PERSON>ffer", "formattedAmount", "isFlightAvailable", "showAllDepartureLocations", "locationType", "input", "getElementById", "focus", "dispatchEvent", "Event", "showAllArrivalLocations", "swapLocations", "patchValue", "formatExpirationDate", "getBaggageTypeName", "filterBaggageByType", "Array", "isArray", "getPassengerTypeName", "passengerType", "calculateLayoverTime", "currentSegment", "nextSegment", "diffMs", "diffMins", "isRoundTripFlight", "firstDeparture", "firstArrival", "lastDeparture", "lastArrival", "getOutboundSegments", "getInboundSegments", "__decorate", "selector", "templateUrl", "styleUrls", "encapsulation", "None"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation, HostListener } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { Observable, of } from 'rxjs';\nimport { ProductService } from '../../../services/product.service';\nimport { SharedDataService } from '../../../services/shared-data.service';\nimport { LocationOption } from '../../../models/one-way-request.model';\nimport { OneWayRequest, Passenger } from '../../../models/one-way-request.model';\nimport { RoundTripRequest } from '../../../models/round-trip-request.model';\nimport { OneWayResponse, Flight } from '../../../models/one-way-response.model';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css', './flight-type-tabs.css', './round-trip-styles.css'],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchPriceComponent implements OnInit {\n  searchForm: FormGroup;\n  departureLocations: LocationOption[] = [];\n  arrivalLocations: LocationOption[] = [];\n  isLoading = false;\n  searchResults: Flight[] = [];\n  filteredResults: Flight[] = [];\n  hasSearched = false;\n  errorMessage = '';\n  lastSearchId = '';\n\n  // Types de recherche de vol\n  readonly SEARCH_TYPE_ONE_WAY = 1;\n  readonly SEARCH_TYPE_ROUND_TRIP = 2;\n  readonly SEARCH_TYPE_MULTI_CITY = 3;\n\n  // Type de recherche actuel (par défaut: aller simple)\n  currentSearchType: number = this.SEARCH_TYPE_ONE_WAY;\n\n  // Passenger selector properties\n  showPassengerDropdown = false;\n  passengerCounts: { [key: number]: number } = {\n    [PassengerType.Adult]: 1,\n    [PassengerType.Child]: 0,\n    [PassengerType.Infant]: 0\n  };\n\n  // Filter options\n  currentFilter: string = 'recommended';\n  filterOptions = [\n    { value: 'recommended', label: 'Recommended', icon: 'fa-star' },\n    { value: 'cheapest', label: 'Cheapest', icon: 'fa-dollar-sign' },\n    { value: 'shortest', label: 'Shortest', icon: 'fa-clock' }\n  ];\n\n  // Sidebar filter options\n  sidebarFilters = {\n    stops: {\n      direct: false,\n      oneStop: false,\n      multiStop: false\n    },\n    departureTime: {\n      earlyMorning: false, // 00:00 - 08:00\n      morning: false,      // 08:00 - 12:00\n      afternoon: false,    // 12:00 - 18:00\n      evening: false       // 18:00 - 00:00\n    },\n    arrivalTime: {\n      earlyMorning: false, // 00:00 - 08:00\n      morning: false,      // 08:00 - 12:00\n      afternoon: false,    // 12:00 - 18:00\n      evening: false       // 18:00 - 00:00\n    },\n    airlines: {} as { [key: string]: boolean }  // Will be populated dynamically based on search results\n  };\n\n  // Expanded sections in sidebar\n  expandedSections: { [key: string]: boolean } = {\n    stops: true,\n    departureTime: true,\n    arrivalTime: true,\n    airlines: true\n  };\n\n  // Price ranges for each filter option (will be calculated from results)\n  filterPrices = {\n    stops: {\n      direct: 0,\n      oneStop: 0,\n      multiStop: 0\n    },\n    departureTime: {\n      earlyMorning: 0,\n      morning: 0,\n      afternoon: 0,\n      evening: 0\n    },\n    arrivalTime: {\n      earlyMorning: 0,\n      morning: 0,\n      afternoon: 0,\n      evening: 0\n    },\n    airlines: {} as { [key: string]: number }\n  };\n\n  // Passenger type options\n  passengerTypes = [\n    { value: PassengerType.Adult, label: 'Adult' },\n    { value: PassengerType.Child, label: 'Child' },\n    { value: PassengerType.Infant, label: 'Infant' }\n  ];\n\n  // Flight class options\n  flightClasses = [\n    { value: FlightClassType.PROMO, label: 'Promo' },\n    { value: FlightClassType.ECONOMY, label: 'Economy' },\n    { value: FlightClassType.BUSINESS, label: 'Business' }\n  ];\n\n  // Date minimale (aujourd'hui)\n  minDate: string;\n\n  constructor(\n    private fb: FormBuilder,\n    private productService: ProductService,\n    private router: Router,\n    private sharedDataService: SharedDataService\n  ) {\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required], // Flight par défaut\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      arrivalLocation: ['', Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      returnDate: [''],  // Date de retour pour les vols aller-retour\n\n      // Options de vol\n      flightClass: [1, Validators.required], // ECONOMY par défaut\n      nonStop: [false],\n\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n\n    // Initialiser les compteurs de passagers dans le service partagé\n    this.sharedDataService.setPassengerCounts(this.passengerCounts);\n  }\n\n  // Close dropdown when clicking outside\n  @HostListener('document:click', ['$event'])\n  onDocumentClick(event: MouseEvent): void {\n    // Check if click is outside the passenger dropdown\n    const clickedElement = event.target as HTMLElement;\n    const passengerSelector = document.querySelector('.passengers-selector');\n\n    if (passengerSelector && !passengerSelector.contains(clickedElement)) {\n      this.showPassengerDropdown = false;\n    }\n  }\n\n  // Toggle passenger dropdown\n  togglePassengerDropdown(event: Event): void {\n    event.stopPropagation();\n    this.showPassengerDropdown = !this.showPassengerDropdown;\n  }\n\n  // Close passenger dropdown\n  closePassengerDropdown(): void {\n    this.showPassengerDropdown = false;\n  }\n\n  // Get passenger count for a specific type\n  getPassengerCount(type: number): number {\n    return this.passengerCounts[type] || 0;\n  }\n\n  // Increase passenger count\n  increasePassengerCount(type: number): void {\n    if (this.getTotalPassengers() < 9) {\n      this.passengerCounts[type] = (this.passengerCounts[type] || 0) + 1;\n      // Mettre à jour le service partagé\n      this.sharedDataService.setPassengerCounts(this.passengerCounts);\n    }\n  }\n\n  // Decrease passenger count\n  decreasePassengerCount(type: number): void {\n    // Pour les adultes, ne pas permettre de descendre en dessous de 1\n    if (type === PassengerType.Adult) {\n      if (this.passengerCounts[type] > 1) {\n        this.passengerCounts[type] -= 1;\n      }\n    } else if (this.passengerCounts[type] > 0) {\n      this.passengerCounts[type] -= 1;\n    }\n    // Mettre à jour le service partagé\n    this.sharedDataService.setPassengerCounts(this.passengerCounts);\n  }\n\n  // Get total number of passengers\n  getTotalPassengers(): number {\n    return Object.values(this.passengerCounts).reduce((sum, count) => sum + count, 0);\n  }\n\n  // Get passengers array for API request\n  getPassengersArray(): any[] {\n    // Utiliser le service partagé pour obtenir les passagers\n    return this.sharedDataService.getPassengersArray();\n  }\n\n  ngOnInit(): void {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n\n  // Méthode pour appliquer le filtre sélectionné\n  applyFilter(filterValue: string): void {\n    this.currentFilter = filterValue;\n    this.applyAllFilters();\n  }\n\n  // Méthode pour vérifier si un vol correspond à la classe sélectionnée\n  private isFlightMatchingClass(flight: Flight, selectedClass: FlightClassType): boolean {\n    // Vérifier si le vol a des items\n    if (!flight.items || flight.items.length === 0) {\n      console.log(`Flight ${flight.id} has no items`);\n      return false;\n    }\n\n    console.log(`Checking flight ${flight.id} for class ${selectedClass}`);\n\n    // Vérifier la classe de vol dans les items\n    for (const item of flight.items) {\n      if (item.flightClass) {\n        console.log(`Item class: ${item.flightClass.name} (type: ${item.flightClass.type})`);\n        if (item.flightClass.type === selectedClass) {\n          console.log(`Match found in item flightClass`);\n          return true;\n        }\n      }\n\n      // Vérifier également dans les segments si disponibles\n      if (item.segments) {\n        console.log(`Checking ${item.segments.length} segments`);\n        for (const segment of item.segments) {\n          if (segment.flightClass) {\n            console.log(`Segment class: ${segment.flightClass.name} (type: ${segment.flightClass.type})`);\n            if (segment.flightClass.type === selectedClass) {\n              console.log(`Match found in segment flightClass`);\n              return true;\n            }\n          }\n        }\n      }\n    }\n\n    // Vérifier également dans les offres si disponibles\n    if (flight.offers && flight.offers.length > 0) {\n      console.log(`Checking ${flight.offers.length} offers`);\n      for (const offer of flight.offers) {\n        if (offer.flightClassInformations && offer.flightClassInformations.length > 0) {\n          console.log(`Offer has ${offer.flightClassInformations.length} class infos`);\n          for (const classInfo of offer.flightClassInformations) {\n            console.log(`Offer class info: ${classInfo.name} (type: ${classInfo.type})`);\n            if (classInfo.type === selectedClass) {\n              console.log(`Match found in offer flightClassInformations`);\n              return true;\n            }\n          }\n        } else {\n          console.log(`Offer has no flightClassInformations`);\n        }\n      }\n    }\n\n    console.log(`No match found for flight ${flight.id}`);\n    return false;\n  }\n\n  // Filtrer les résultats par date\n  filterResultsByDate(results: any[]): any[] {\n    if (!results || results.length === 0) {\n      return [];\n    }\n\n    const formValue = this.searchForm.value;\n    const departureDate = new Date(formValue.departureDate);\n    const returnDate = formValue.returnDate ? new Date(formValue.returnDate) : null;\n\n    // Réinitialiser les heures pour comparer uniquement les dates\n    departureDate.setHours(0, 0, 0, 0);\n    if (returnDate) returnDate.setHours(0, 0, 0, 0);\n\n    console.log('Filtering flights by dates:');\n    console.log('Requested departure date:', departureDate.toISOString().split('T')[0]);\n    if (returnDate) console.log('Requested return date:', returnDate.toISOString().split('T')[0]);\n\n    return results.filter(flight => {\n      if (!flight.items || flight.items.length === 0) {\n        return false;\n      }\n\n      // Pour les vols aller simple\n      if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n        if (!flight.items[0].departure || !flight.items[0].departure.date) {\n          return false;\n        }\n\n        const flightDepartureDate = new Date(flight.items[0].departure.date);\n        flightDepartureDate.setHours(0, 0, 0, 0);\n\n        // Comparer uniquement les dates (jour/mois/année)\n        return flightDepartureDate.getTime() === departureDate.getTime();\n      }\n\n      // Pour les vols aller-retour\n      else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP && returnDate) {\n        // Cas 1: Vol avec plusieurs items (aller et retour séparés)\n        if (flight.items.length > 1) {\n          if (!flight.items[0].departure || !flight.items[0].departure.date ||\n              !flight.items[1].departure || !flight.items[1].departure.date) {\n            return false;\n          }\n\n          const outboundDepartureDate = new Date(flight.items[0].departure.date);\n          outboundDepartureDate.setHours(0, 0, 0, 0);\n\n          const inboundDepartureDate = new Date(flight.items[1].departure.date);\n          inboundDepartureDate.setHours(0, 0, 0, 0);\n\n          console.log('Flight', flight.id, 'outbound:', outboundDepartureDate.toISOString().split('T')[0],\n                      'inbound:', inboundDepartureDate.toISOString().split('T')[0]);\n\n          return outboundDepartureDate.getTime() === departureDate.getTime() &&\n                 inboundDepartureDate.getTime() === returnDate.getTime();\n        }\n\n        // Cas 2: Vol avec un seul item mais plusieurs segments\n        else if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n          const segments = flight.items[0].segments;\n          const halfLength = Math.ceil(segments.length / 2);\n\n          // Premier segment (aller)\n          if (!segments[0].departure || !segments[0].departure.date) {\n            return false;\n          }\n\n          // Dernier segment de la première moitié (fin de l'aller)\n          const outboundDepartureDate = new Date(segments[0].departure.date);\n          outboundDepartureDate.setHours(0, 0, 0, 0);\n\n          // Premier segment de la seconde moitié (début du retour)\n          if (segments.length <= halfLength) {\n            return false;\n          }\n\n          if (!segments[halfLength].departure || !segments[halfLength].departure.date) {\n            return false;\n          }\n\n          const inboundDepartureDate = new Date(segments[halfLength].departure.date);\n          inboundDepartureDate.setHours(0, 0, 0, 0);\n\n          console.log('Flight', flight.id, 'outbound (segments):', outboundDepartureDate.toISOString().split('T')[0],\n                      'inbound (segments):', inboundDepartureDate.toISOString().split('T')[0]);\n\n          return outboundDepartureDate.getTime() === departureDate.getTime() &&\n                 inboundDepartureDate.getTime() === returnDate.getTime();\n        }\n      }\n\n      return false;\n    });\n  }\n\n  // Méthode pour appliquer tous les filtres (top et sidebar)\n  applyAllFilters(): void {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      this.filteredResults = [];\n      return;\n    }\n\n    // Étape 1: Appliquer les filtres de la sidebar\n    let results = [...this.searchResults];\n\n    // Filtrer par date\n    results = this.filterResultsByDate(results);\n\n    // Récupérer la classe de vol sélectionnée\n    const selectedClass = this.searchForm.get('flightClass')?.value;\n\n    // Filtrer par classe de vol\n    if (selectedClass !== undefined) {\n      console.log('Filtering by flight class:', selectedClass);\n\n      // Afficher le nombre total de vols avant filtrage\n      console.log('Total flights before class filtering:', results.length);\n\n      // Vérifier combien de vols correspondent à chaque classe\n      const classCounts = {\n        [FlightClassType.PROMO]: 0,\n        [FlightClassType.ECONOMY]: 0,\n        [FlightClassType.BUSINESS]: 0\n      };\n\n      results.forEach(flight => {\n        if (this.isFlightMatchingClass(flight, FlightClassType.PROMO)) classCounts[FlightClassType.PROMO]++;\n        if (this.isFlightMatchingClass(flight, FlightClassType.ECONOMY)) classCounts[FlightClassType.ECONOMY]++;\n        if (this.isFlightMatchingClass(flight, FlightClassType.BUSINESS)) classCounts[FlightClassType.BUSINESS]++;\n      });\n\n      console.log('Flights by class (before filtering):', classCounts);\n\n      // Option pour désactiver temporairement le filtrage par classe (pour débogage)\n      // Mettre à true pour voir tous les vols disponibles sans filtrage par classe\n      const disableClassFiltering = true;\n\n      if (!disableClassFiltering) {\n        results = results.filter(flight => this.isFlightMatchingClass(flight, selectedClass));\n        console.log('Flights after class filtering:', results.length);\n      } else {\n        console.log('Class filtering disabled for debugging');\n      }\n    }\n\n    // Filtrer par nombre d'escales\n    if (this.sidebarFilters.stops.direct || this.sidebarFilters.stops.oneStop || this.sidebarFilters.stops.multiStop) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0) return false;\n\n        const stopCount = flight.items[0].stopCount || 0;\n\n        return (this.sidebarFilters.stops.direct && stopCount === 0) ||\n               (this.sidebarFilters.stops.oneStop && stopCount === 1) ||\n               (this.sidebarFilters.stops.multiStop && stopCount >= 2);\n      });\n    }\n\n    // Filtrer par horaire de départ\n    if (this.sidebarFilters.departureTime.earlyMorning ||\n        this.sidebarFilters.departureTime.morning ||\n        this.sidebarFilters.departureTime.afternoon ||\n        this.sidebarFilters.departureTime.evening) {\n\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].departure || !flight.items[0].departure.date) {\n          return false;\n        }\n\n        const departureDate = new Date(flight.items[0].departure.date);\n        const hours = departureDate.getHours();\n\n        return (this.sidebarFilters.departureTime.earlyMorning && hours >= 0 && hours < 8) ||\n               (this.sidebarFilters.departureTime.morning && hours >= 8 && hours < 12) ||\n               (this.sidebarFilters.departureTime.afternoon && hours >= 12 && hours < 18) ||\n               (this.sidebarFilters.departureTime.evening && hours >= 18 && hours < 24);\n      });\n    }\n\n    // Filtrer par horaire d'arrivée\n    if (this.sidebarFilters.arrivalTime.earlyMorning ||\n        this.sidebarFilters.arrivalTime.morning ||\n        this.sidebarFilters.arrivalTime.afternoon ||\n        this.sidebarFilters.arrivalTime.evening) {\n\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].arrival || !flight.items[0].arrival.date) {\n          return false;\n        }\n\n        const arrivalDate = new Date(flight.items[0].arrival.date);\n        const hours = arrivalDate.getHours();\n\n        return (this.sidebarFilters.arrivalTime.earlyMorning && hours >= 0 && hours < 8) ||\n               (this.sidebarFilters.arrivalTime.morning && hours >= 8 && hours < 12) ||\n               (this.sidebarFilters.arrivalTime.afternoon && hours >= 12 && hours < 18) ||\n               (this.sidebarFilters.arrivalTime.evening && hours >= 18 && hours < 24);\n      });\n    }\n\n    // Filtrer par compagnie aérienne\n    const selectedAirlines = Object.entries(this.sidebarFilters.airlines)\n      .filter(([_, selected]) => selected)\n      .map(([airline, _]) => airline);\n\n    if (selectedAirlines.length > 0) {\n      results = results.filter(flight => {\n        if (!flight.items || flight.items.length === 0 || !flight.items[0].airline || !flight.items[0].airline.name) {\n          return false;\n        }\n\n        return selectedAirlines.includes(flight.items[0].airline.name);\n      });\n    }\n\n    // Étape 2: Appliquer le tri selon le filtre sélectionné en haut\n    switch (this.currentFilter) {\n      case 'cheapest':\n        this.filteredResults = this.sortByPrice(results);\n        break;\n      case 'shortest':\n        this.filteredResults = this.sortByDuration(results);\n        break;\n      case 'recommended':\n      default:\n        this.filteredResults = this.sortByRecommendation(results);\n        break;\n    }\n  }\n\n  // Trier les vols par prix (du moins cher au plus cher)\n  private sortByPrice(flights: Flight[]): Flight[] {\n    return flights.sort((a, b) => {\n      const priceA = this.getMinPriceAmount(a);\n      const priceB = this.getMinPriceAmount(b);\n      return priceA - priceB;\n    });\n  }\n\n  // Trier les vols par durée (du plus court au plus long)\n  private sortByDuration(flights: Flight[]): Flight[] {\n    return flights.sort((a, b) => {\n      const durationA = a.items && a.items[0] ? a.items[0].duration : Number.MAX_VALUE;\n      const durationB = b.items && b.items[0] ? b.items[0].duration : Number.MAX_VALUE;\n      return durationA - durationB;\n    });\n  }\n\n  // Trier les vols par recommandation (combinaison de prix, durée et autres facteurs)\n  private sortByRecommendation(flights: Flight[]): Flight[] {\n    return flights.sort((a, b) => {\n      // Calculer un score pour chaque vol basé sur plusieurs facteurs\n      const scoreA = this.calculateRecommendationScore(a);\n      const scoreB = this.calculateRecommendationScore(b);\n      return scoreB - scoreA; // Ordre décroissant (score plus élevé = meilleur)\n    });\n  }\n\n  // Calculer un score de recommandation pour un vol\n  private calculateRecommendationScore(flight: Flight): number {\n    if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n      return 0;\n    }\n\n    const item = flight.items[0];\n    const offer = flight.offers[0];\n\n    // Facteurs à considérer pour le score\n    const price = this.getMinPriceAmount(flight);\n    const duration = item.duration;\n    const stopCount = item.stopCount || 0;\n    const availability = offer.availability !== undefined ? offer.availability :\n                        (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n\n    // Normaliser les valeurs (plus le score est élevé, meilleur est le vol)\n    const priceScore = 1000 / (price + 100); // Inverse du prix (moins cher = meilleur score)\n    const durationScore = 1000 / (duration + 100); // Inverse de la durée (plus court = meilleur score)\n    const stopScore = 1 / (stopCount + 1); // Moins d'escales = meilleur score\n    const availabilityScore = Math.min(availability, 10) / 10; // Plus de disponibilité = meilleur score\n\n    // Pondération des facteurs (ajustable selon les préférences)\n    const weights = {\n      price: 0.4,      // 40% importance pour le prix\n      duration: 0.3,   // 30% importance pour la durée\n      stops: 0.2,      // 20% importance pour les escales\n      availability: 0.1 // 10% importance pour la disponibilité\n    };\n\n    // Calculer le score final pondéré\n    return (\n      priceScore * weights.price +\n      durationScore * weights.duration +\n      stopScore * weights.stops +\n      availabilityScore * weights.availability\n    );\n  }\n\n  // Obtenir le montant du prix minimum pour un vol\n  private getMinPriceAmount(flight: Flight): number {\n    if (!flight.offers || flight.offers.length === 0) {\n      return Number.MAX_VALUE;\n    }\n\n    return flight.offers.reduce((min, offer) =>\n      offer.price && offer.price.amount < min ? offer.price.amount : min,\n      flight.offers[0].price ? flight.offers[0].price.amount : Number.MAX_VALUE\n    );\n  }\n\n  // Calculer les prix minimums pour chaque option de filtre\n  calculateFilterPrices(): void {\n    if (!this.searchResults || this.searchResults.length === 0) {\n      return;\n    }\n\n    // Réinitialiser les prix\n    this.resetFilterPrices();\n\n    // Collecter toutes les compagnies aériennes\n    const airlines = new Set<string>();\n\n    // Parcourir tous les vols pour calculer les prix minimums\n    this.searchResults.forEach(flight => {\n      if (!flight.items || flight.items.length === 0 || !flight.offers || flight.offers.length === 0) {\n        return;\n      }\n\n      const item = flight.items[0];\n      const price = this.getMinPriceAmount(flight);\n\n      // Ajouter la compagnie aérienne à la liste\n      if (item.airline && item.airline.name) {\n        airlines.add(item.airline.name);\n\n        // Initialiser le prix pour cette compagnie si nécessaire\n        if (!(item.airline.name in this.filterPrices.airlines)) {\n          this.filterPrices.airlines[item.airline.name] = Number.MAX_VALUE;\n        }\n\n        // Mettre à jour le prix minimum pour cette compagnie\n        this.filterPrices.airlines[item.airline.name] = Math.min(\n          this.filterPrices.airlines[item.airline.name],\n          price\n        );\n      }\n\n      // Mettre à jour les prix par nombre d'escales\n      const stopCount = item.stopCount || 0;\n      if (stopCount === 0) {\n        this.filterPrices.stops.direct = Math.min(this.filterPrices.stops.direct, price);\n      } else if (stopCount === 1) {\n        this.filterPrices.stops.oneStop = Math.min(this.filterPrices.stops.oneStop, price);\n      } else {\n        this.filterPrices.stops.multiStop = Math.min(this.filterPrices.stops.multiStop, price);\n      }\n\n      // Mettre à jour les prix par horaire de départ\n      if (item.departure && item.departure.date) {\n        const departureDate = new Date(item.departure.date);\n        const departureHours = departureDate.getHours();\n\n        if (departureHours >= 0 && departureHours < 8) {\n          this.filterPrices.departureTime.earlyMorning = Math.min(this.filterPrices.departureTime.earlyMorning, price);\n        } else if (departureHours >= 8 && departureHours < 12) {\n          this.filterPrices.departureTime.morning = Math.min(this.filterPrices.departureTime.morning, price);\n        } else if (departureHours >= 12 && departureHours < 18) {\n          this.filterPrices.departureTime.afternoon = Math.min(this.filterPrices.departureTime.afternoon, price);\n        } else {\n          this.filterPrices.departureTime.evening = Math.min(this.filterPrices.departureTime.evening, price);\n        }\n      }\n\n      // Mettre à jour les prix par horaire d'arrivée\n      if (item.arrival && item.arrival.date) {\n        const arrivalDate = new Date(item.arrival.date);\n        const arrivalHours = arrivalDate.getHours();\n\n        if (arrivalHours >= 0 && arrivalHours < 8) {\n          this.filterPrices.arrivalTime.earlyMorning = Math.min(this.filterPrices.arrivalTime.earlyMorning, price);\n        } else if (arrivalHours >= 8 && arrivalHours < 12) {\n          this.filterPrices.arrivalTime.morning = Math.min(this.filterPrices.arrivalTime.morning, price);\n        } else if (arrivalHours >= 12 && arrivalHours < 18) {\n          this.filterPrices.arrivalTime.afternoon = Math.min(this.filterPrices.arrivalTime.afternoon, price);\n        } else {\n          this.filterPrices.arrivalTime.evening = Math.min(this.filterPrices.arrivalTime.evening, price);\n        }\n      }\n    });\n\n    // Initialiser les filtres de compagnies aériennes\n    airlines.forEach(airline => {\n      if (!(airline in this.sidebarFilters.airlines)) {\n        this.sidebarFilters.airlines[airline] = false;\n      }\n    });\n\n    // Remplacer les valeurs MAX_VALUE par 0 pour les options sans vols\n    this.cleanupFilterPrices();\n  }\n\n  // Réinitialiser les prix des filtres\n  private resetFilterPrices(): void {\n    this.filterPrices = {\n      stops: {\n        direct: Number.MAX_VALUE,\n        oneStop: Number.MAX_VALUE,\n        multiStop: Number.MAX_VALUE\n      },\n      departureTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      arrivalTime: {\n        earlyMorning: Number.MAX_VALUE,\n        morning: Number.MAX_VALUE,\n        afternoon: Number.MAX_VALUE,\n        evening: Number.MAX_VALUE\n      },\n      airlines: {}\n    };\n  }\n\n  // Nettoyer les prix des filtres (remplacer MAX_VALUE par 0)\n  private cleanupFilterPrices(): void {\n    // Escales\n    if (this.filterPrices.stops.direct === Number.MAX_VALUE) this.filterPrices.stops.direct = 0;\n    if (this.filterPrices.stops.oneStop === Number.MAX_VALUE) this.filterPrices.stops.oneStop = 0;\n    if (this.filterPrices.stops.multiStop === Number.MAX_VALUE) this.filterPrices.stops.multiStop = 0;\n\n    // Horaires de départ\n    if (this.filterPrices.departureTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.departureTime.earlyMorning = 0;\n    if (this.filterPrices.departureTime.morning === Number.MAX_VALUE) this.filterPrices.departureTime.morning = 0;\n    if (this.filterPrices.departureTime.afternoon === Number.MAX_VALUE) this.filterPrices.departureTime.afternoon = 0;\n    if (this.filterPrices.departureTime.evening === Number.MAX_VALUE) this.filterPrices.departureTime.evening = 0;\n\n    // Horaires d'arrivée\n    if (this.filterPrices.arrivalTime.earlyMorning === Number.MAX_VALUE) this.filterPrices.arrivalTime.earlyMorning = 0;\n    if (this.filterPrices.arrivalTime.morning === Number.MAX_VALUE) this.filterPrices.arrivalTime.morning = 0;\n    if (this.filterPrices.arrivalTime.afternoon === Number.MAX_VALUE) this.filterPrices.arrivalTime.afternoon = 0;\n    if (this.filterPrices.arrivalTime.evening === Number.MAX_VALUE) this.filterPrices.arrivalTime.evening = 0;\n\n    // Compagnies aériennes\n    Object.keys(this.filterPrices.airlines).forEach(airline => {\n      if (this.filterPrices.airlines[airline] === Number.MAX_VALUE) {\n        this.filterPrices.airlines[airline] = 0;\n      }\n    });\n  }\n\n  // Basculer l'état d'expansion d'une section\n  toggleSection(section: string): void {\n    this.expandedSections[section] = !this.expandedSections[section];\n  }\n\n  // Basculer un filtre d'escale\n  toggleStopFilter(filter: 'direct' | 'oneStop' | 'multiStop'): void {\n    this.sidebarFilters.stops[filter] = !this.sidebarFilters.stops[filter];\n    this.applyAllFilters();\n  }\n\n  // Basculer un filtre d'horaire de départ\n  toggleDepartureTimeFilter(filter: 'earlyMorning' | 'morning' | 'afternoon' | 'evening'): void {\n    this.sidebarFilters.departureTime[filter] = !this.sidebarFilters.departureTime[filter];\n    this.applyAllFilters();\n  }\n\n  // Basculer un filtre d'horaire d'arrivée\n  toggleArrivalTimeFilter(filter: 'earlyMorning' | 'morning' | 'afternoon' | 'evening'): void {\n    this.sidebarFilters.arrivalTime[filter] = !this.sidebarFilters.arrivalTime[filter];\n    this.applyAllFilters();\n  }\n\n  // Basculer un filtre de compagnie aérienne\n  toggleAirlineFilter(airline: string): void {\n    this.sidebarFilters.airlines[airline] = !this.sidebarFilters.airlines[airline];\n    this.applyAllFilters();\n  }\n\n  // Effacer tous les filtres\n  clearAllFilters(): void {\n    // Réinitialiser les filtres d'escales\n    this.sidebarFilters.stops.direct = false;\n    this.sidebarFilters.stops.oneStop = false;\n    this.sidebarFilters.stops.multiStop = false;\n\n    // Réinitialiser les filtres d'horaires de départ\n    this.sidebarFilters.departureTime.earlyMorning = false;\n    this.sidebarFilters.departureTime.morning = false;\n    this.sidebarFilters.departureTime.afternoon = false;\n    this.sidebarFilters.departureTime.evening = false;\n\n    // Réinitialiser les filtres d'horaires d'arrivée\n    this.sidebarFilters.arrivalTime.earlyMorning = false;\n    this.sidebarFilters.arrivalTime.morning = false;\n    this.sidebarFilters.arrivalTime.afternoon = false;\n    this.sidebarFilters.arrivalTime.evening = false;\n\n    // Réinitialiser les filtres de compagnies aériennes\n    Object.keys(this.sidebarFilters.airlines).forEach(airline => {\n      this.sidebarFilters.airlines[airline] = false;\n    });\n\n    // Appliquer les filtres (qui seront tous désactivés)\n    this.applyAllFilters();\n  }\n\n  // Formater le prix pour l'affichage\n  formatPrice(price: number): string {\n    if (price === 0) return '-';\n    return price.toFixed(0) + ' €';\n  }\n\n  // Obtenir les clés des compagnies aériennes\n  getAirlineKeys(): string[] {\n    return Object.keys(this.sidebarFilters.airlines);\n  }\n\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight: Flight): void {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n\n    header.appendChild(logo);\n    header.appendChild(title);\n\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n\n        generalInfo.appendChild(airlineInfo);\n      }\n\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n\n      routeSection.appendChild(routeVisual);\n\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n\n          segmentsList.appendChild(segmentItem);\n\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n\n            segmentsList.appendChild(layover);\n          }\n        });\n\n        routeSection.appendChild(segmentsList);\n      }\n    }\n\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability :\n                                 (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n\n          const baggageContainer = document.createElement('div');\n          baggageContainer.style.display = 'flex';\n          baggageContainer.style.flexDirection = 'column';\n          baggageContainer.style.gap = '10px';\n          baggageContainer.style.marginBottom = '15px';\n\n          // Filtrer et regrouper les bagages par type\n          const checkedBaggage = offer.baggageInformations.filter(b => b.baggageType === 2);\n          const cabinBaggage = offer.baggageInformations.filter(b => b.baggageType === 1);\n          const handBaggage = offer.baggageInformations.filter(b => b.baggageType === 3);\n\n          // Bagages en soute\n          if (checkedBaggage.length > 0) {\n            checkedBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#e7f5ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #c5e1f9';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-suitcase\" style=\"color: #4a6fa5; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Checked Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n\n          // Bagages cabine\n          if (cabinBaggage.length > 0) {\n            cabinBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#f3f0ff';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #e5dbff';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Cabin Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          } else {\n            // Ajouter un bagage cabine par défaut si aucun n'est spécifié\n            const baggageItem = document.createElement('div');\n            baggageItem.style.display = 'flex';\n            baggageItem.style.alignItems = 'center';\n            baggageItem.style.padding = '10px 15px';\n            baggageItem.style.backgroundColor = '#f3f0ff';\n            baggageItem.style.borderRadius = '6px';\n            baggageItem.style.border = '1px solid #e5dbff';\n\n            const baggageIcon = document.createElement('div');\n            baggageIcon.innerHTML = '<i class=\"fas fa-briefcase\" style=\"color: #6741d9; font-size: 16px; margin-right: 10px;\"></i>';\n\n            const baggageInfo = document.createElement('div');\n            baggageInfo.style.display = 'flex';\n            baggageInfo.style.flexDirection = 'column';\n\n            const baggageType = document.createElement('div');\n            baggageType.textContent = 'Cabin Baggage';\n            baggageType.style.fontWeight = '600';\n            baggageType.style.fontSize = '14px';\n\n            const baggageDetails = document.createElement('div');\n            baggageDetails.style.fontSize = '12px';\n            baggageDetails.style.color = '#666';\n            baggageDetails.textContent = 'Included';\n\n            baggageInfo.appendChild(baggageType);\n            baggageInfo.appendChild(baggageDetails);\n\n            baggageItem.appendChild(baggageIcon);\n            baggageItem.appendChild(baggageInfo);\n            baggageContainer.appendChild(baggageItem);\n          }\n\n          // Bagages à main\n          if (handBaggage.length > 0) {\n            handBaggage.forEach(baggage => {\n              const baggageItem = document.createElement('div');\n              baggageItem.style.display = 'flex';\n              baggageItem.style.alignItems = 'center';\n              baggageItem.style.padding = '10px 15px';\n              baggageItem.style.backgroundColor = '#fff4e6';\n              baggageItem.style.borderRadius = '6px';\n              baggageItem.style.border = '1px solid #ffe8cc';\n\n              const baggageIcon = document.createElement('div');\n              baggageIcon.innerHTML = '<i class=\"fas fa-shopping-bag\" style=\"color: #e8590c; font-size: 16px; margin-right: 10px;\"></i>';\n\n              const baggageInfo = document.createElement('div');\n              baggageInfo.style.display = 'flex';\n              baggageInfo.style.flexDirection = 'column';\n\n              const baggageType = document.createElement('div');\n              baggageType.textContent = 'Hand Baggage';\n              baggageType.style.fontWeight = '600';\n              baggageType.style.fontSize = '14px';\n\n              const baggageDetails = document.createElement('div');\n              baggageDetails.style.fontSize = '12px';\n              baggageDetails.style.color = '#666';\n\n              let detailsText = '';\n              if (baggage.weight > 0) detailsText += `${baggage.weight} kg`;\n              if (baggage.piece > 0) {\n                if (detailsText) detailsText += ' - ';\n                detailsText += `${baggage.piece} piece(s)`;\n              }\n              baggageDetails.textContent = detailsText || 'Included';\n\n              baggageInfo.appendChild(baggageType);\n              baggageInfo.appendChild(baggageDetails);\n\n              baggageItem.appendChild(baggageIcon);\n              baggageItem.appendChild(baggageInfo);\n              baggageContainer.appendChild(baggageItem);\n            });\n          }\n\n          offerItem.appendChild(baggageContainer);\n        }\n\n        offerItem.appendChild(offerDetails);\n\n        offersList.appendChild(offerItem);\n      });\n\n      offersSection.appendChild(offersList);\n    }\n\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n\n  // Méthode utilitaire pour créer une section\n  private createSection(title: string, iconClass: string): HTMLElement {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n\n    return section;\n  }\n\n  // Méthode utilitaire pour créer une ligne d'information\n  private createInfoRow(label: string, value: string): HTMLElement {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n\n    return row;\n  }\n\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight: Flight): void {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n\n      // Obtenir les informations de passagers\n      const passengerCounts = this.sharedDataService.getPassengerCounts();\n\n      // Sérialiser les informations de passagers pour les passer dans l'URL\n      const passengerInfo = JSON.stringify(passengerCounts);\n\n      console.log('Navigating to get-offer with searchId:', searchId, 'offerId:', offerId, 'and passengers:', passengerInfo);\n\n      // Rediriger vers la page get-offer avec les informations de passagers\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId,\n          passengers: passengerInfo\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n\n  preloadLocations(): void {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n\n  setupAutocomplete(): void {\n    // Charger les locations par type par défaut\n    const departureLocationType = 2; // Type 2 (City) par défaut\n    const arrivalLocationType = 5;   // Type 5 (Airport) par défaut\n\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(departureLocationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(departureLocationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.departureLocations = locations;\n      });\n\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(arrivalLocationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(arrivalLocationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n  }\n\n  displayLocation(location: LocationOption | null): string {\n    if (!location) return '';\n\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n\n  // Méthode pour changer le type de recherche (aller simple, aller-retour)\n  setSearchType(type: number): void {\n    this.currentSearchType = type;\n\n    // Mettre à jour le type de service dans le formulaire\n    if (type === this.SEARCH_TYPE_ONE_WAY) {\n      this.searchForm.get('serviceTypes')?.setValue(['1']);\n      // Rendre le champ de date de retour optionnel\n      this.searchForm.get('returnDate')?.clearValidators();\n    } else if (type === this.SEARCH_TYPE_ROUND_TRIP) {\n      this.searchForm.get('serviceTypes')?.setValue(['2']);\n      // Rendre le champ de date de retour obligatoire\n      this.searchForm.get('returnDate')?.setValidators([Validators.required]);\n    }\n\n    // Mettre à jour les validateurs\n    this.searchForm.get('returnDate')?.updateValueAndValidity();\n  }\n\n  onSearch(): void {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n\n    const formValue = this.searchForm.value;\n\n    // Vérifier la valeur de la classe de vol et le type de recherche\n    console.log('Form values:', formValue);\n    console.log('Selected flight class:', formValue.flightClass);\n    console.log('Current search type:', this.currentSearchType);\n\n    // Créer la requête en fonction du type de recherche (aller simple ou aller-retour)\n    let request: any;\n\n    if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n      // Requête pour vol aller simple\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['1'], // Important: \"1\" pour aller simple\n        CheckIn: formValue.departureDate,\n        DepartureLocations: [\n          {\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }\n        ],\n        ArrivalLocations: [\n          {\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }\n        ],\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n\n      console.log('Sending one-way search request with ServiceTypes:', request.ServiceTypes);\n    } else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      // Requête pour vol aller-retour\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['2'], // Important: \"2\" pour aller-retour\n        CheckIn: formValue.departureDate,\n        ReturnDate: formValue.returnDate,\n        DepartureLocations: [\n          {\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }\n        ],\n        ArrivalLocations: [\n          {\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }\n        ],\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n\n      console.log('Sending round-trip search request with ServiceTypes:', request.ServiceTypes);\n    }\n\n    // Créer la requête appropriée selon le type de recherche\n    if (this.currentSearchType === this.SEARCH_TYPE_ONE_WAY) {\n      // Requête pour vol aller simple\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['1'], // Important: \"1\" pour aller simple\n        CheckIn: formValue.departureDate,\n        DepartureLocations: [\n          {\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }\n        ],\n        ArrivalLocations: [\n          {\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }\n        ],\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n\n      console.log('Sending one-way search request with ServiceTypes:', request.ServiceTypes);\n    } else if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      // Requête pour vol aller-retour\n      request = {\n        ProductType: formValue.productType,\n        ServiceTypes: ['2'], // Important: \"2\" pour aller-retour\n        CheckIn: formValue.departureDate,\n        ReturnDate: formValue.returnDate,\n        DepartureLocations: [\n          {\n            id: formValue.departureLocation?.id || '',\n            type: 2 // Type 2 (City) par défaut\n          }\n        ],\n        ArrivalLocations: [\n          {\n            id: formValue.arrivalLocation?.id || '',\n            type: 5 // Type 5 (Airport) par défaut\n          }\n        ],\n        Passengers: this.getPassengersArray(),\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n\n      console.log('Sending round-trip search request with ServiceTypes:', request.ServiceTypes);\n    }\n\n    // Ajouter des logs pour déboguer les dates\n    console.log('Search request details:');\n    console.log('Search type:', this.currentSearchType === this.SEARCH_TYPE_ONE_WAY ? 'One-way' : 'Round-trip');\n    console.log('Departure date:', formValue.departureDate);\n    if (this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP) {\n      console.log('Return date:', formValue.returnDate);\n    }\n\n    // Utiliser la méthode searchPrice pour envoyer la requête\n    this.productService.searchPrice(request)\n        .subscribe({\n          next: (response: OneWayResponse) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n\n            // Ajouter des logs pour analyser les dates dans la réponse\n            console.log('API response analysis:');\n            console.log('Total flights received:', this.searchResults.length);\n\n            // Analyser les dates des vols reçus\n            if (this.searchResults.length > 0) {\n              console.group('Flight dates analysis');\n              this.searchResults.slice(0, 5).forEach(flight => {\n                if (flight.items && flight.items.length > 0) {\n                  console.log('Flight ID:', flight.id);\n\n                  // Analyser les dates du premier item (aller)\n                  if (flight.items[0].departure && flight.items[0].departure.date) {\n                    const depDate = new Date(flight.items[0].departure.date);\n                    console.log('Outbound departure:', depDate.toISOString().split('T')[0], depDate.toLocaleTimeString());\n                  }\n\n                  // Analyser les dates du second item (retour) si présent\n                  if (flight.items.length > 1 && flight.items[1].departure && flight.items[1].departure.date) {\n                    const retDate = new Date(flight.items[1].departure.date);\n                    console.log('Inbound departure:', retDate.toISOString().split('T')[0], retDate.toLocaleTimeString());\n                  }\n\n                  // Analyser les segments si présents\n                  if (flight.items[0].segments && flight.items[0].segments.length > 1) {\n                    console.log('Segments count:', flight.items[0].segments.length);\n                    const firstSegment = flight.items[0].segments[0];\n                    const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n\n                    if (firstSegment.departure && firstSegment.departure.date) {\n                      const firstDepDate = new Date(firstSegment.departure.date);\n                      console.log('First segment departure:', firstDepDate.toISOString().split('T')[0], firstDepDate.toLocaleTimeString());\n                    }\n\n                    if (lastSegment.arrival && lastSegment.arrival.date) {\n                      const lastArrDate = new Date(lastSegment.arrival.date);\n                      console.log('Last segment arrival:', lastArrDate.toISOString().split('T')[0], lastArrDate.toLocaleTimeString());\n                    }\n                  }\n                }\n              });\n              console.groupEnd();\n            }\n\n            // Appliquer le filtre par date\n            const filteredByDate = this.filterResultsByDate(this.searchResults);\n            console.log('Flights matching requested dates:', filteredByDate.length, 'out of', this.searchResults.length);\n\n            // Analyser les classes de vol dans les résultats\n            console.group('Flight Class Analysis');\n            console.log('Selected flight class:', formValue.flightClass);\n            console.log('Total flights received:', response.body.flights.length);\n\n            // Compter les vols par classe\n            const flightsByClass: Record<string, number> = {\n              [FlightClassType.PROMO.toString()]: 0,\n              [FlightClassType.ECONOMY.toString()]: 0,\n              [FlightClassType.BUSINESS.toString()]: 0,\n              'unknown': 0\n            };\n\n            response.body.flights.forEach((flight, index) => {\n              if (flight.items && flight.items.length > 0 && flight.items[0].flightClass) {\n                const classType = flight.items[0].flightClass.type.toString();\n                if (flightsByClass[classType] !== undefined) {\n                  flightsByClass[classType]++;\n                } else {\n                  flightsByClass['unknown']++;\n                }\n\n                // Afficher les détails de classe pour chaque vol\n                console.log(`Flight ${flight.id} class:`,\n                  flight.items[0].flightClass ?\n                  `${flight.items[0].flightClass.name} (type: ${flight.items[0].flightClass.type})` :\n                  'No class info');\n\n                // Afficher la structure complète du premier vol pour analyse\n                if (index === 0) {\n                  console.group('First Flight Structure');\n                  console.log('Flight ID:', flight.id);\n                  console.log('Flight Items:', flight.items);\n\n                  if (flight.items && flight.items.length > 0) {\n                    console.log('First Item FlightClass:', flight.items[0].flightClass);\n\n                    if (flight.items[0].segments) {\n                      console.log('Segments:', flight.items[0].segments);\n                      flight.items[0].segments.forEach((segment, segIndex) => {\n                        console.log(`Segment ${segIndex} FlightClass:`, segment.flightClass);\n                      });\n                    }\n                  }\n\n                  if (flight.offers && flight.offers.length > 0) {\n                    console.log('First Offer:', flight.offers[0]);\n                    console.log('FlightClassInformations:', flight.offers[0].flightClassInformations);\n                  }\n\n                  console.groupEnd();\n                }\n              } else {\n                flightsByClass['unknown']++;\n              }\n            });\n\n            console.log('Flights by class:', flightsByClass);\n            console.groupEnd();\n\n            // Calculer les prix minimums pour chaque option de filtre\n            this.calculateFilterPrices();\n\n            // Appliquer le filtre actuel aux résultats\n            this.applyAllFilters();\n\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            // console.log('Full API Response:', JSON.stringify(response, null, 2));\n\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o =>\n                o.availability !== undefined ? o.availability : (o.seatInfo ? o.seatInfo.availableSeatCount : 0)\n              ));\n              console.log('Availability values:', availabilityValues);\n\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc, val) => {\n                if (val !== undefined) {\n                  acc[val] = (acc[val] || 0) + 1;\n                }\n                return acc;\n              }, {} as Record<number, number>);\n              console.log('Availability counts:', availabilityCounts);\n\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f =>\n                f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true)\n              );\n              console.log('Reservable flights:', reservableFlights.length);\n\n              console.groupEnd();\n            }\n\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n  }\n\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup: FormGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n\n  // Formater la durée en heures et minutes\n  formatDuration(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n\n  // Formater la date pour l'affichage\n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight: Flight): string {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n\n    const minOffer = flight.offers.reduce((min, offer) =>\n      offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight: Flight): boolean {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability :\n                             (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n    return availabilityValue > 0;\n  }\n\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations(): void {\n    const locationType = 2; // Type 2 (City) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations(): void {\n    const locationType = 5; // Type 5 (Airport) par défaut\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations(): void {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      arrivalLocation: departureLocation\n    });\n  }\n\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType: number): string {\n    switch (baggageType) {\n      case 1: return 'Cabin Baggage';\n      case 2: return 'Checked Baggage';\n      case 3: return 'Hand Baggage';\n      default: return 'Baggage';\n    }\n  }\n\n  // Filtrer les bagages par type\n  filterBaggageByType(baggageInformations: any[], type: number): any[] {\n    if (!baggageInformations || !Array.isArray(baggageInformations)) {\n      return [];\n    }\n    return baggageInformations.filter(baggage => baggage.baggageType === type);\n  }\n\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType: number): string {\n    switch (passengerType) {\n      case 1: return 'Adult';\n      case 2: return 'Child';\n      case 3: return 'Infant';\n      default: return 'Passenger';\n    }\n  }\n\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment: any, nextSegment: any): string {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date ||\n        !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n\n  // Détecter si un vol est un aller-retour\n  isRoundTripFlight(flight: any): boolean {\n    // Vérifier si le vol a plusieurs items (aller et retour)\n    if (flight.items && flight.items.length > 1) {\n      return true;\n    }\n\n    // Vérifier si le premier item a des segments qui forment un aller-retour\n    // (généralement, les segments d'un aller-retour forment une boucle)\n    if (flight.items && flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      const firstSegment = flight.items[0].segments[0];\n      const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n\n      // Vérifier si le premier segment part de A vers B et le dernier segment part de B vers A\n      if (firstSegment && firstSegment.departure && firstSegment.arrival &&\n          lastSegment && lastSegment.departure && lastSegment.arrival) {\n\n        const firstDeparture = firstSegment.departure.airport?.code || firstSegment.departure.city?.name;\n        const firstArrival = firstSegment.arrival.airport?.code || firstSegment.arrival.city?.name;\n        const lastDeparture = lastSegment.departure.airport?.code || lastSegment.departure.city?.name;\n        const lastArrival = lastSegment.arrival.airport?.code || lastSegment.arrival.city?.name;\n\n        // Si le dernier segment revient au point de départ du premier segment\n        if (firstDeparture && lastArrival && firstDeparture === lastArrival) {\n          return true;\n        }\n      }\n    }\n\n    // Vérifier si le vol a été recherché avec ServiceTypes = 2 (aller-retour)\n    return this.currentSearchType === this.SEARCH_TYPE_ROUND_TRIP;\n  }\n\n  // Obtenir les segments aller d'un vol aller-retour\n  getOutboundSegments(flight: any): any[] {\n    if (!flight.items) return [];\n\n    // Si le vol a plusieurs items, le premier est généralement l'aller\n    if (flight.items.length > 1) {\n      return flight.items[0].segments || [flight.items[0]];\n    }\n\n    // Si le vol a un seul item avec plusieurs segments\n    if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      // Pour un aller-retour, on considère la première moitié des segments comme l'aller\n      const halfLength = Math.ceil(flight.items[0].segments.length / 2);\n      return flight.items[0].segments.slice(0, halfLength);\n    }\n\n    return flight.items[0].segments || [flight.items[0]];\n  }\n\n  // Obtenir les segments retour d'un vol aller-retour\n  getInboundSegments(flight: any): any[] {\n    if (!flight.items) return [];\n\n    // Si le vol a plusieurs items, le second est généralement le retour\n    if (flight.items.length > 1) {\n      return flight.items[1].segments || [flight.items[1]];\n    }\n\n    // Si le vol a un seul item avec plusieurs segments\n    if (flight.items.length === 1 && flight.items[0].segments && flight.items[0].segments.length > 1) {\n      // Pour un aller-retour, on considère la seconde moitié des segments comme le retour\n      const halfLength = Math.ceil(flight.items[0].segments.length / 2);\n      return flight.items[0].segments.slice(halfLength);\n    }\n\n    return [];\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,iBAAiB,EAAEC,YAAY,QAAQ,eAAe;AAClF,SAAsBC,SAAS,EAAEC,UAAU,QAAmB,gBAAgB;AAE9E,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnF,SAAqBC,EAAE,QAAQ,MAAM;AAOrC,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;AAQnF,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAwG/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,MAAc,EACdC,iBAAoC;IAHpC,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IA1G3B,KAAAC,kBAAkB,GAAqB,EAAE;IACzC,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAa,EAAE;IAC5B,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,YAAY,GAAG,EAAE;IAEjB;IACS,KAAAC,mBAAmB,GAAG,CAAC;IACvB,KAAAC,sBAAsB,GAAG,CAAC;IAC1B,KAAAC,sBAAsB,GAAG,CAAC;IAEnC;IACA,KAAAC,iBAAiB,GAAW,IAAI,CAACH,mBAAmB;IAEpD;IACA,KAAAI,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,eAAe,GAA8B;MAC3C,CAACpB,aAAa,CAACqB,KAAK,GAAG,CAAC;MACxB,CAACrB,aAAa,CAACsB,KAAK,GAAG,CAAC;MACxB,CAACtB,aAAa,CAACuB,MAAM,GAAG;KACzB;IAED;IACA,KAAAC,aAAa,GAAW,aAAa;IACrC,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAS,CAAE,EAC/D;MAAEF,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAgB,CAAE,EAChE;MAAEF,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAU,CAAE,CAC3D;IAED;IACA,KAAAC,cAAc,GAAG;MACfC,KAAK,EAAE;QACLC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE;OACZ;MACDC,aAAa,EAAE;QACbC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,KAAK,CAAO;OACtB;;MACDC,WAAW,EAAE;QACXJ,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,KAAK,CAAO;OACtB;;MACDE,QAAQ,EAAE,EAAgC,CAAE;KAC7C;IAED;IACA,KAAAC,gBAAgB,GAA+B;MAC7CX,KAAK,EAAE,IAAI;MACXI,aAAa,EAAE,IAAI;MACnBK,WAAW,EAAE,IAAI;MACjBC,QAAQ,EAAE;KACX;IAED;IACA,KAAAE,YAAY,GAAG;MACbZ,KAAK,EAAE;QACLC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE;OACZ;MACDC,aAAa,EAAE;QACbC,YAAY,EAAE,CAAC;QACfC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE;OACV;MACDC,WAAW,EAAE;QACXJ,YAAY,EAAE,CAAC;QACfC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE;OACV;MACDE,QAAQ,EAAE;KACX;IAED;IACA,KAAAG,cAAc,GAAG,CACf;MAAEjB,KAAK,EAAE1B,aAAa,CAACqB,KAAK;MAAEM,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAE1B,aAAa,CAACsB,KAAK;MAAEK,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAE1B,aAAa,CAACuB,MAAM;MAAEI,KAAK,EAAE;IAAQ,CAAE,CACjD;IAED;IACA,KAAAiB,aAAa,GAAG,CACd;MAAElB,KAAK,EAAE5B,eAAe,CAAC+C,KAAK;MAAElB,KAAK,EAAE;IAAO,CAAE,EAChD;MAAED,KAAK,EAAE5B,eAAe,CAACgD,OAAO;MAAEnB,KAAK,EAAE;IAAS,CAAE,EACpD;MAAED,KAAK,EAAE5B,eAAe,CAACiD,QAAQ;MAAEpB,KAAK,EAAE;IAAU,CAAE,CACvD;IAWC;IACA,IAAI,CAACqB,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAErD;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACjD,EAAE,CAACkD,KAAK,CAAC;MAC9B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAE9D,UAAU,CAAC+D,QAAQ,CAAC;MACrCC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,EAAEhE,UAAU,CAAC+D,QAAQ,CAAC;MAC1CE,iBAAiB,EAAE,CAAC,EAAE,EAAEjE,UAAU,CAAC+D,QAAQ,CAAC;MAC5CG,eAAe,EAAE,CAAC,EAAE,EAAElE,UAAU,CAAC+D,QAAQ,CAAC;MAC1CI,aAAa,EAAE,CAAC,IAAI,CAACX,OAAO,EAAExD,UAAU,CAAC+D,QAAQ,CAAC;MAClDK,UAAU,EAAE,CAAC,EAAE,CAAC;MAEhB;MACAC,WAAW,EAAE,CAAC,CAAC,EAAErE,UAAU,CAAC+D,QAAQ,CAAC;MACrCO,OAAO,EAAE,CAAC,KAAK,CAAC;MAEhB;MACAC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBC,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,wBAAwB,EAAE,CAAC,KAAK,CAAC;MACjCC,6BAA6B,EAAE,CAAC,IAAI,CAAC;MACrCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAE5B;MACAC,sBAAsB,EAAE,CAAC,CAAC;KAC3B,CAAC;IAEF;IACA,IAAI,CAAC/D,iBAAiB,CAACgE,kBAAkB,CAAC,IAAI,CAAClD,eAAe,CAAC;EACjE;EAEA;EAEAmD,eAAeA,CAACC,KAAiB;IAC/B;IACA,MAAMC,cAAc,GAAGD,KAAK,CAACE,MAAqB;IAClD,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC;IAExE,IAAIF,iBAAiB,IAAI,CAACA,iBAAiB,CAACG,QAAQ,CAACL,cAAc,CAAC,EAAE;MACpE,IAAI,CAACtD,qBAAqB,GAAG,KAAK;;EAEtC;EAEA;EACA4D,uBAAuBA,CAACP,KAAY;IAClCA,KAAK,CAACQ,eAAe,EAAE;IACvB,IAAI,CAAC7D,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEA;EACA8D,sBAAsBA,CAAA;IACpB,IAAI,CAAC9D,qBAAqB,GAAG,KAAK;EACpC;EAEA;EACA+D,iBAAiBA,CAACC,IAAY;IAC5B,OAAO,IAAI,CAAC/D,eAAe,CAAC+D,IAAI,CAAC,IAAI,CAAC;EACxC;EAEA;EACAC,sBAAsBA,CAACD,IAAY;IACjC,IAAI,IAAI,CAACE,kBAAkB,EAAE,GAAG,CAAC,EAAE;MACjC,IAAI,CAACjE,eAAe,CAAC+D,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC/D,eAAe,CAAC+D,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;MAClE;MACA,IAAI,CAAC7E,iBAAiB,CAACgE,kBAAkB,CAAC,IAAI,CAAClD,eAAe,CAAC;;EAEnE;EAEA;EACAkE,sBAAsBA,CAACH,IAAY;IACjC;IACA,IAAIA,IAAI,KAAKnF,aAAa,CAACqB,KAAK,EAAE;MAChC,IAAI,IAAI,CAACD,eAAe,CAAC+D,IAAI,CAAC,GAAG,CAAC,EAAE;QAClC,IAAI,CAAC/D,eAAe,CAAC+D,IAAI,CAAC,IAAI,CAAC;;KAElC,MAAM,IAAI,IAAI,CAAC/D,eAAe,CAAC+D,IAAI,CAAC,GAAG,CAAC,EAAE;MACzC,IAAI,CAAC/D,eAAe,CAAC+D,IAAI,CAAC,IAAI,CAAC;;IAEjC;IACA,IAAI,CAAC7E,iBAAiB,CAACgE,kBAAkB,CAAC,IAAI,CAAClD,eAAe,CAAC;EACjE;EAEA;EACAiE,kBAAkBA,CAAA;IAChB,OAAOE,MAAM,CAACC,MAAM,CAAC,IAAI,CAACpE,eAAe,CAAC,CAACqE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;EACnF;EAEA;EACAC,kBAAkBA,CAAA;IAChB;IACA,OAAO,IAAI,CAACtF,iBAAiB,CAACsF,kBAAkB,EAAE;EACpD;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACAC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EACjF;EAEA;EACAC,WAAWA,CAACC,WAAmB;IAC7B,IAAI,CAAC3E,aAAa,GAAG2E,WAAW;IAChC,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;EACQC,qBAAqBA,CAACC,MAAc,EAAEC,aAA8B;IAC1E;IACA,IAAI,CAACD,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9CT,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,eAAe,CAAC;MAC/C,OAAO,KAAK;;IAGdV,OAAO,CAACC,GAAG,CAAC,mBAAmBK,MAAM,CAACI,EAAE,cAAcH,aAAa,EAAE,CAAC;IAEtE;IACA,KAAK,MAAMI,IAAI,IAAIL,MAAM,CAACE,KAAK,EAAE;MAC/B,IAAIG,IAAI,CAAC9C,WAAW,EAAE;QACpBmC,OAAO,CAACC,GAAG,CAAC,eAAeU,IAAI,CAAC9C,WAAW,CAAC+C,IAAI,WAAWD,IAAI,CAAC9C,WAAW,CAACsB,IAAI,GAAG,CAAC;QACpF,IAAIwB,IAAI,CAAC9C,WAAW,CAACsB,IAAI,KAAKoB,aAAa,EAAE;UAC3CP,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C,OAAO,IAAI;;;MAIf;MACA,IAAIU,IAAI,CAACE,QAAQ,EAAE;QACjBb,OAAO,CAACC,GAAG,CAAC,YAAYU,IAAI,CAACE,QAAQ,CAACJ,MAAM,WAAW,CAAC;QACxD,KAAK,MAAMK,OAAO,IAAIH,IAAI,CAACE,QAAQ,EAAE;UACnC,IAAIC,OAAO,CAACjD,WAAW,EAAE;YACvBmC,OAAO,CAACC,GAAG,CAAC,kBAAkBa,OAAO,CAACjD,WAAW,CAAC+C,IAAI,WAAWE,OAAO,CAACjD,WAAW,CAACsB,IAAI,GAAG,CAAC;YAC7F,IAAI2B,OAAO,CAACjD,WAAW,CAACsB,IAAI,KAAKoB,aAAa,EAAE;cAC9CP,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;cACjD,OAAO,IAAI;;;;;;IAOrB;IACA,IAAIK,MAAM,CAACS,MAAM,IAAIT,MAAM,CAACS,MAAM,CAACN,MAAM,GAAG,CAAC,EAAE;MAC7CT,OAAO,CAACC,GAAG,CAAC,YAAYK,MAAM,CAACS,MAAM,CAACN,MAAM,SAAS,CAAC;MACtD,KAAK,MAAMO,KAAK,IAAIV,MAAM,CAACS,MAAM,EAAE;QACjC,IAAIC,KAAK,CAACC,uBAAuB,IAAID,KAAK,CAACC,uBAAuB,CAACR,MAAM,GAAG,CAAC,EAAE;UAC7ET,OAAO,CAACC,GAAG,CAAC,aAAae,KAAK,CAACC,uBAAuB,CAACR,MAAM,cAAc,CAAC;UAC5E,KAAK,MAAMS,SAAS,IAAIF,KAAK,CAACC,uBAAuB,EAAE;YACrDjB,OAAO,CAACC,GAAG,CAAC,qBAAqBiB,SAAS,CAACN,IAAI,WAAWM,SAAS,CAAC/B,IAAI,GAAG,CAAC;YAC5E,IAAI+B,SAAS,CAAC/B,IAAI,KAAKoB,aAAa,EAAE;cACpCP,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;cAC3D,OAAO,IAAI;;;SAGhB,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;;;IAKzDD,OAAO,CAACC,GAAG,CAAC,6BAA6BK,MAAM,CAACI,EAAE,EAAE,CAAC;IACrD,OAAO,KAAK;EACd;EAEA;EACAS,mBAAmBA,CAACC,OAAc;IAChC,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACX,MAAM,KAAK,CAAC,EAAE;MACpC,OAAO,EAAE;;IAGX,MAAMY,SAAS,GAAG,IAAI,CAACjE,UAAU,CAAC1B,KAAK;IACvC,MAAMiC,aAAa,GAAG,IAAIV,IAAI,CAACoE,SAAS,CAAC1D,aAAa,CAAC;IACvD,MAAMC,UAAU,GAAGyD,SAAS,CAACzD,UAAU,GAAG,IAAIX,IAAI,CAACoE,SAAS,CAACzD,UAAU,CAAC,GAAG,IAAI;IAE/E;IACAD,aAAa,CAAC2D,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAClC,IAAI1D,UAAU,EAAEA,UAAU,CAAC0D,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE/CtB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1CD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEtC,aAAa,CAACT,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnF,IAAIS,UAAU,EAAEoC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAErC,UAAU,CAACV,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE7F,OAAOiE,OAAO,CAACG,MAAM,CAACjB,MAAM,IAAG;MAC7B,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QAC9C,OAAO,KAAK;;MAGd;MACA,IAAI,IAAI,CAACvF,iBAAiB,KAAK,IAAI,CAACH,mBAAmB,EAAE;QACvD,IAAI,CAACuF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,IAAI,CAAClB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,EAAE;UACjE,OAAO,KAAK;;QAGd,MAAMC,mBAAmB,GAAG,IAAIzE,IAAI,CAACqD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,CAAC;QACpEC,mBAAmB,CAACJ,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAExC;QACA,OAAOI,mBAAmB,CAACC,OAAO,EAAE,KAAKhE,aAAa,CAACgE,OAAO,EAAE;;MAGlE;MAAA,KACK,IAAI,IAAI,CAACzG,iBAAiB,KAAK,IAAI,CAACF,sBAAsB,IAAI4C,UAAU,EAAE;QAC7E;QACA,IAAI0C,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,IAAI,CAAClB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,IAC7D,CAACnB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,IAAI,CAAClB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,EAAE;YACjE,OAAO,KAAK;;UAGd,MAAMG,qBAAqB,GAAG,IAAI3E,IAAI,CAACqD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,CAAC;UACtEG,qBAAqB,CAACN,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAE1C,MAAMO,oBAAoB,GAAG,IAAI5E,IAAI,CAACqD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,CAAC;UACrEI,oBAAoB,CAACP,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAEzCtB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEK,MAAM,CAACI,EAAE,EAAE,WAAW,EAAEkB,qBAAqB,CAAC1E,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EACnF,UAAU,EAAE0E,oBAAoB,CAAC3E,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UAEzE,OAAOyE,qBAAqB,CAACD,OAAO,EAAE,KAAKhE,aAAa,CAACgE,OAAO,EAAE,IAC3DE,oBAAoB,CAACF,OAAO,EAAE,KAAK/D,UAAU,CAAC+D,OAAO,EAAE;;QAGhE;QAAA,KACK,IAAIrB,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAIH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,IAAIP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACJ,MAAM,GAAG,CAAC,EAAE;UACrG,MAAMI,QAAQ,GAAGP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ;UACzC,MAAMiB,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACnB,QAAQ,CAACJ,MAAM,GAAG,CAAC,CAAC;UAEjD;UACA,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACW,SAAS,IAAI,CAACX,QAAQ,CAAC,CAAC,CAAC,CAACW,SAAS,CAACC,IAAI,EAAE;YACzD,OAAO,KAAK;;UAGd;UACA,MAAMG,qBAAqB,GAAG,IAAI3E,IAAI,CAAC4D,QAAQ,CAAC,CAAC,CAAC,CAACW,SAAS,CAACC,IAAI,CAAC;UAClEG,qBAAqB,CAACN,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAE1C;UACA,IAAIT,QAAQ,CAACJ,MAAM,IAAIqB,UAAU,EAAE;YACjC,OAAO,KAAK;;UAGd,IAAI,CAACjB,QAAQ,CAACiB,UAAU,CAAC,CAACN,SAAS,IAAI,CAACX,QAAQ,CAACiB,UAAU,CAAC,CAACN,SAAS,CAACC,IAAI,EAAE;YAC3E,OAAO,KAAK;;UAGd,MAAMI,oBAAoB,GAAG,IAAI5E,IAAI,CAAC4D,QAAQ,CAACiB,UAAU,CAAC,CAACN,SAAS,CAACC,IAAI,CAAC;UAC1EI,oBAAoB,CAACP,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAEzCtB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEK,MAAM,CAACI,EAAE,EAAE,sBAAsB,EAAEkB,qBAAqB,CAAC1E,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAC9F,qBAAqB,EAAE0E,oBAAoB,CAAC3E,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UAEpF,OAAOyE,qBAAqB,CAACD,OAAO,EAAE,KAAKhE,aAAa,CAACgE,OAAO,EAAE,IAC3DE,oBAAoB,CAACF,OAAO,EAAE,KAAK/D,UAAU,CAAC+D,OAAO,EAAE;;;MAIlE,OAAO,KAAK;IACd,CAAC,CAAC;EACJ;EAEA;EACAvB,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC1F,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC+F,MAAM,KAAK,CAAC,EAAE;MAC1D,IAAI,CAAC9F,eAAe,GAAG,EAAE;MACzB;;IAGF;IACA,IAAIyG,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC1G,aAAa,CAAC;IAErC;IACA0G,OAAO,GAAG,IAAI,CAACD,mBAAmB,CAACC,OAAO,CAAC;IAE3C;IACA,MAAMb,aAAa,GAAG,IAAI,CAACnD,UAAU,CAAC6E,GAAG,CAAC,aAAa,CAAC,EAAEvG,KAAK;IAE/D;IACA,IAAI6E,aAAa,KAAK2B,SAAS,EAAE;MAC/BlC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEM,aAAa,CAAC;MAExD;MACAP,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEmB,OAAO,CAACX,MAAM,CAAC;MAEpE;MACA,MAAM0B,WAAW,GAAG;QAClB,CAACrI,eAAe,CAAC+C,KAAK,GAAG,CAAC;QAC1B,CAAC/C,eAAe,CAACgD,OAAO,GAAG,CAAC;QAC5B,CAAChD,eAAe,CAACiD,QAAQ,GAAG;OAC7B;MAEDqE,OAAO,CAACgB,OAAO,CAAC9B,MAAM,IAAG;QACvB,IAAI,IAAI,CAACD,qBAAqB,CAACC,MAAM,EAAExG,eAAe,CAAC+C,KAAK,CAAC,EAAEsF,WAAW,CAACrI,eAAe,CAAC+C,KAAK,CAAC,EAAE;QACnG,IAAI,IAAI,CAACwD,qBAAqB,CAACC,MAAM,EAAExG,eAAe,CAACgD,OAAO,CAAC,EAAEqF,WAAW,CAACrI,eAAe,CAACgD,OAAO,CAAC,EAAE;QACvG,IAAI,IAAI,CAACuD,qBAAqB,CAACC,MAAM,EAAExG,eAAe,CAACiD,QAAQ,CAAC,EAAEoF,WAAW,CAACrI,eAAe,CAACiD,QAAQ,CAAC,EAAE;MAC3G,CAAC,CAAC;MAEFiD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEkC,WAAW,CAAC;MAEhE;MACA;MACA,MAAME,qBAAqB,GAAG,IAAI;MAElC,IAAI,CAACA,qBAAqB,EAAE;QAC1BjB,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACjB,MAAM,IAAI,IAAI,CAACD,qBAAqB,CAACC,MAAM,EAAEC,aAAa,CAAC,CAAC;QACrFP,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmB,OAAO,CAACX,MAAM,CAAC;OAC9D,MAAM;QACLT,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;;IAIzD;IACA,IAAI,IAAI,CAACpE,cAAc,CAACC,KAAK,CAACC,MAAM,IAAI,IAAI,CAACF,cAAc,CAACC,KAAK,CAACE,OAAO,IAAI,IAAI,CAACH,cAAc,CAACC,KAAK,CAACG,SAAS,EAAE;MAChHmF,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACjB,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;QAE5D,MAAM6B,SAAS,GAAGhC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC8B,SAAS,IAAI,CAAC;QAEhD,OAAQ,IAAI,CAACzG,cAAc,CAACC,KAAK,CAACC,MAAM,IAAIuG,SAAS,KAAK,CAAC,IACnD,IAAI,CAACzG,cAAc,CAACC,KAAK,CAACE,OAAO,IAAIsG,SAAS,KAAK,CAAE,IACrD,IAAI,CAACzG,cAAc,CAACC,KAAK,CAACG,SAAS,IAAIqG,SAAS,IAAI,CAAE;MAChE,CAAC,CAAC;;IAGJ;IACA,IAAI,IAAI,CAACzG,cAAc,CAACK,aAAa,CAACC,YAAY,IAC9C,IAAI,CAACN,cAAc,CAACK,aAAa,CAACE,OAAO,IACzC,IAAI,CAACP,cAAc,CAACK,aAAa,CAACG,SAAS,IAC3C,IAAI,CAACR,cAAc,CAACK,aAAa,CAACI,OAAO,EAAE;MAE7C8E,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACjB,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,IAAI,CAAClB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,EAAE;UAC/G,OAAO,KAAK;;QAGd,MAAM9D,aAAa,GAAG,IAAIV,IAAI,CAACqD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,CAAC;QAC9D,MAAMc,KAAK,GAAG5E,aAAa,CAAC6E,QAAQ,EAAE;QAEtC,OAAQ,IAAI,CAAC3G,cAAc,CAACK,aAAa,CAACC,YAAY,IAAIoG,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,CAAC,IACzE,IAAI,CAAC1G,cAAc,CAACK,aAAa,CAACE,OAAO,IAAImG,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,EAAG,IACtE,IAAI,CAAC1G,cAAc,CAACK,aAAa,CAACG,SAAS,IAAIkG,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG,IACzE,IAAI,CAAC1G,cAAc,CAACK,aAAa,CAACI,OAAO,IAAIiG,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG;MACjF,CAAC,CAAC;;IAGJ;IACA,IAAI,IAAI,CAAC1G,cAAc,CAACU,WAAW,CAACJ,YAAY,IAC5C,IAAI,CAACN,cAAc,CAACU,WAAW,CAACH,OAAO,IACvC,IAAI,CAACP,cAAc,CAACU,WAAW,CAACF,SAAS,IACzC,IAAI,CAACR,cAAc,CAACU,WAAW,CAACD,OAAO,EAAE;MAE3C8E,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACjB,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACiC,OAAO,IAAI,CAACnC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACiC,OAAO,CAAChB,IAAI,EAAE;UAC3G,OAAO,KAAK;;QAGd,MAAMiB,WAAW,GAAG,IAAIzF,IAAI,CAACqD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACiC,OAAO,CAAChB,IAAI,CAAC;QAC1D,MAAMc,KAAK,GAAGG,WAAW,CAACF,QAAQ,EAAE;QAEpC,OAAQ,IAAI,CAAC3G,cAAc,CAACU,WAAW,CAACJ,YAAY,IAAIoG,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,CAAC,IACvE,IAAI,CAAC1G,cAAc,CAACU,WAAW,CAACH,OAAO,IAAImG,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,EAAG,IACpE,IAAI,CAAC1G,cAAc,CAACU,WAAW,CAACF,SAAS,IAAIkG,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG,IACvE,IAAI,CAAC1G,cAAc,CAACU,WAAW,CAACD,OAAO,IAAIiG,KAAK,IAAI,EAAE,IAAIA,KAAK,GAAG,EAAG;MAC/E,CAAC,CAAC;;IAGJ;IACA,MAAMI,gBAAgB,GAAGpD,MAAM,CAACqD,OAAO,CAAC,IAAI,CAAC/G,cAAc,CAACW,QAAQ,CAAC,CAClE+E,MAAM,CAAC,CAAC,CAACsB,CAAC,EAAEC,QAAQ,CAAC,KAAKA,QAAQ,CAAC,CACnClJ,GAAG,CAAC,CAAC,CAACmJ,OAAO,EAAEF,CAAC,CAAC,KAAKE,OAAO,CAAC;IAEjC,IAAIJ,gBAAgB,CAAClC,MAAM,GAAG,CAAC,EAAE;MAC/BW,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACjB,MAAM,IAAG;QAChC,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACuC,OAAO,IAAI,CAACzC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACuC,OAAO,CAACnC,IAAI,EAAE;UAC3G,OAAO,KAAK;;QAGd,OAAO+B,gBAAgB,CAACK,QAAQ,CAAC1C,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACuC,OAAO,CAACnC,IAAI,CAAC;MAChE,CAAC,CAAC;;IAGJ;IACA,QAAQ,IAAI,CAACpF,aAAa;MACxB,KAAK,UAAU;QACb,IAAI,CAACb,eAAe,GAAG,IAAI,CAACsI,WAAW,CAAC7B,OAAO,CAAC;QAChD;MACF,KAAK,UAAU;QACb,IAAI,CAACzG,eAAe,GAAG,IAAI,CAACuI,cAAc,CAAC9B,OAAO,CAAC;QACnD;MACF,KAAK,aAAa;MAClB;QACE,IAAI,CAACzG,eAAe,GAAG,IAAI,CAACwI,oBAAoB,CAAC/B,OAAO,CAAC;QACzD;;EAEN;EAEA;EACQ6B,WAAWA,CAACG,OAAiB;IACnC,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,MAAMC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACH,CAAC,CAAC;MACxC,MAAMI,MAAM,GAAG,IAAI,CAACD,iBAAiB,CAACF,CAAC,CAAC;MACxC,OAAOC,MAAM,GAAGE,MAAM;IACxB,CAAC,CAAC;EACJ;EAEA;EACQR,cAAcA,CAACE,OAAiB;IACtC,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B,MAAMI,SAAS,GAAGL,CAAC,CAAC9C,KAAK,IAAI8C,CAAC,CAAC9C,KAAK,CAAC,CAAC,CAAC,GAAG8C,CAAC,CAAC9C,KAAK,CAAC,CAAC,CAAC,CAACoD,QAAQ,GAAGC,MAAM,CAACC,SAAS;MAChF,MAAMC,SAAS,GAAGR,CAAC,CAAC/C,KAAK,IAAI+C,CAAC,CAAC/C,KAAK,CAAC,CAAC,CAAC,GAAG+C,CAAC,CAAC/C,KAAK,CAAC,CAAC,CAAC,CAACoD,QAAQ,GAAGC,MAAM,CAACC,SAAS;MAChF,OAAOH,SAAS,GAAGI,SAAS;IAC9B,CAAC,CAAC;EACJ;EAEA;EACQZ,oBAAoBA,CAACC,OAAiB;IAC5C,OAAOA,OAAO,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC3B;MACA,MAAMS,MAAM,GAAG,IAAI,CAACC,4BAA4B,CAACX,CAAC,CAAC;MACnD,MAAMY,MAAM,GAAG,IAAI,CAACD,4BAA4B,CAACV,CAAC,CAAC;MACnD,OAAOW,MAAM,GAAGF,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ;EAEA;EACQC,4BAA4BA,CAAC3D,MAAc;IACjD,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,MAAM,CAACS,MAAM,IAAIT,MAAM,CAACS,MAAM,CAACN,MAAM,KAAK,CAAC,EAAE;MAC9F,OAAO,CAAC;;IAGV,MAAME,IAAI,GAAGL,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IAC5B,MAAMQ,KAAK,GAAGV,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC;IAE9B;IACA,MAAMoD,KAAK,GAAG,IAAI,CAACV,iBAAiB,CAACnD,MAAM,CAAC;IAC5C,MAAMsD,QAAQ,GAAGjD,IAAI,CAACiD,QAAQ;IAC9B,MAAMtB,SAAS,GAAG3B,IAAI,CAAC2B,SAAS,IAAI,CAAC;IACrC,MAAM8B,YAAY,GAAGpD,KAAK,CAACoD,YAAY,KAAKlC,SAAS,GAAGlB,KAAK,CAACoD,YAAY,GACrDpD,KAAK,CAACqD,QAAQ,GAAGrD,KAAK,CAACqD,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IAE5E;IACA,MAAMC,UAAU,GAAG,IAAI,IAAIJ,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;IACzC,MAAMK,aAAa,GAAG,IAAI,IAAIZ,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC;IAC/C,MAAMa,SAAS,GAAG,CAAC,IAAInC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;IACvC,MAAMoC,iBAAiB,GAAG3C,IAAI,CAAC4C,GAAG,CAACP,YAAY,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAE3D;IACA,MAAMQ,OAAO,GAAG;MACdT,KAAK,EAAE,GAAG;MACVP,QAAQ,EAAE,GAAG;MACb9H,KAAK,EAAE,GAAG;MACVsI,YAAY,EAAE,GAAG,CAAC;KACnB;IAED;IACA,OACEG,UAAU,GAAGK,OAAO,CAACT,KAAK,GAC1BK,aAAa,GAAGI,OAAO,CAAChB,QAAQ,GAChCa,SAAS,GAAGG,OAAO,CAAC9I,KAAK,GACzB4I,iBAAiB,GAAGE,OAAO,CAACR,YAAY;EAE5C;EAEA;EACQX,iBAAiBA,CAACnD,MAAc;IACtC,IAAI,CAACA,MAAM,CAACS,MAAM,IAAIT,MAAM,CAACS,MAAM,CAACN,MAAM,KAAK,CAAC,EAAE;MAChD,OAAOoD,MAAM,CAACC,SAAS;;IAGzB,OAAOxD,MAAM,CAACS,MAAM,CAACtB,MAAM,CAAC,CAACkF,GAAG,EAAE3D,KAAK,KACrCA,KAAK,CAACmD,KAAK,IAAInD,KAAK,CAACmD,KAAK,CAACU,MAAM,GAAGF,GAAG,GAAG3D,KAAK,CAACmD,KAAK,CAACU,MAAM,GAAGF,GAAG,EAClErE,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,CAACoD,KAAK,GAAG7D,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,CAACoD,KAAK,CAACU,MAAM,GAAGhB,MAAM,CAACC,SAAS,CAC1E;EACH;EAEA;EACAgB,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACpK,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC+F,MAAM,KAAK,CAAC,EAAE;MAC1D;;IAGF;IACA,IAAI,CAACsE,iBAAiB,EAAE;IAExB;IACA,MAAMvI,QAAQ,GAAG,IAAIwI,GAAG,EAAU;IAElC;IACA,IAAI,CAACtK,aAAa,CAAC0H,OAAO,CAAC9B,MAAM,IAAG;MAClC,IAAI,CAACA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,MAAM,CAACS,MAAM,IAAIT,MAAM,CAACS,MAAM,CAACN,MAAM,KAAK,CAAC,EAAE;QAC9F;;MAGF,MAAME,IAAI,GAAGL,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;MAC5B,MAAM2D,KAAK,GAAG,IAAI,CAACV,iBAAiB,CAACnD,MAAM,CAAC;MAE5C;MACA,IAAIK,IAAI,CAACoC,OAAO,IAAIpC,IAAI,CAACoC,OAAO,CAACnC,IAAI,EAAE;QACrCpE,QAAQ,CAACyI,GAAG,CAACtE,IAAI,CAACoC,OAAO,CAACnC,IAAI,CAAC;QAE/B;QACA,IAAI,EAAED,IAAI,CAACoC,OAAO,CAACnC,IAAI,IAAI,IAAI,CAAClE,YAAY,CAACF,QAAQ,CAAC,EAAE;UACtD,IAAI,CAACE,YAAY,CAACF,QAAQ,CAACmE,IAAI,CAACoC,OAAO,CAACnC,IAAI,CAAC,GAAGiD,MAAM,CAACC,SAAS;;QAGlE;QACA,IAAI,CAACpH,YAAY,CAACF,QAAQ,CAACmE,IAAI,CAACoC,OAAO,CAACnC,IAAI,CAAC,GAAGmB,IAAI,CAAC4C,GAAG,CACtD,IAAI,CAACjI,YAAY,CAACF,QAAQ,CAACmE,IAAI,CAACoC,OAAO,CAACnC,IAAI,CAAC,EAC7CuD,KAAK,CACN;;MAGH;MACA,MAAM7B,SAAS,GAAG3B,IAAI,CAAC2B,SAAS,IAAI,CAAC;MACrC,IAAIA,SAAS,KAAK,CAAC,EAAE;QACnB,IAAI,CAAC5F,YAAY,CAACZ,KAAK,CAACC,MAAM,GAAGgG,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACjI,YAAY,CAACZ,KAAK,CAACC,MAAM,EAAEoI,KAAK,CAAC;OACjF,MAAM,IAAI7B,SAAS,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAC5F,YAAY,CAACZ,KAAK,CAACE,OAAO,GAAG+F,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACjI,YAAY,CAACZ,KAAK,CAACE,OAAO,EAAEmI,KAAK,CAAC;OACnF,MAAM;QACL,IAAI,CAACzH,YAAY,CAACZ,KAAK,CAACG,SAAS,GAAG8F,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACjI,YAAY,CAACZ,KAAK,CAACG,SAAS,EAAEkI,KAAK,CAAC;;MAGxF;MACA,IAAIxD,IAAI,CAACa,SAAS,IAAIb,IAAI,CAACa,SAAS,CAACC,IAAI,EAAE;QACzC,MAAM9D,aAAa,GAAG,IAAIV,IAAI,CAAC0D,IAAI,CAACa,SAAS,CAACC,IAAI,CAAC;QACnD,MAAMyD,cAAc,GAAGvH,aAAa,CAAC6E,QAAQ,EAAE;QAE/C,IAAI0C,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAG,CAAC,EAAE;UAC7C,IAAI,CAACxI,YAAY,CAACR,aAAa,CAACC,YAAY,GAAG4F,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACjI,YAAY,CAACR,aAAa,CAACC,YAAY,EAAEgI,KAAK,CAAC;SAC7G,MAAM,IAAIe,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAG,EAAE,EAAE;UACrD,IAAI,CAACxI,YAAY,CAACR,aAAa,CAACE,OAAO,GAAG2F,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACjI,YAAY,CAACR,aAAa,CAACE,OAAO,EAAE+H,KAAK,CAAC;SACnG,MAAM,IAAIe,cAAc,IAAI,EAAE,IAAIA,cAAc,GAAG,EAAE,EAAE;UACtD,IAAI,CAACxI,YAAY,CAACR,aAAa,CAACG,SAAS,GAAG0F,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACjI,YAAY,CAACR,aAAa,CAACG,SAAS,EAAE8H,KAAK,CAAC;SACvG,MAAM;UACL,IAAI,CAACzH,YAAY,CAACR,aAAa,CAACI,OAAO,GAAGyF,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACjI,YAAY,CAACR,aAAa,CAACI,OAAO,EAAE6H,KAAK,CAAC;;;MAItG;MACA,IAAIxD,IAAI,CAAC8B,OAAO,IAAI9B,IAAI,CAAC8B,OAAO,CAAChB,IAAI,EAAE;QACrC,MAAMiB,WAAW,GAAG,IAAIzF,IAAI,CAAC0D,IAAI,CAAC8B,OAAO,CAAChB,IAAI,CAAC;QAC/C,MAAM0D,YAAY,GAAGzC,WAAW,CAACF,QAAQ,EAAE;QAE3C,IAAI2C,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,CAAC,EAAE;UACzC,IAAI,CAACzI,YAAY,CAACH,WAAW,CAACJ,YAAY,GAAG4F,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACjI,YAAY,CAACH,WAAW,CAACJ,YAAY,EAAEgI,KAAK,CAAC;SACzG,MAAM,IAAIgB,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,EAAE,EAAE;UACjD,IAAI,CAACzI,YAAY,CAACH,WAAW,CAACH,OAAO,GAAG2F,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACjI,YAAY,CAACH,WAAW,CAACH,OAAO,EAAE+H,KAAK,CAAC;SAC/F,MAAM,IAAIgB,YAAY,IAAI,EAAE,IAAIA,YAAY,GAAG,EAAE,EAAE;UAClD,IAAI,CAACzI,YAAY,CAACH,WAAW,CAACF,SAAS,GAAG0F,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACjI,YAAY,CAACH,WAAW,CAACF,SAAS,EAAE8H,KAAK,CAAC;SACnG,MAAM;UACL,IAAI,CAACzH,YAAY,CAACH,WAAW,CAACD,OAAO,GAAGyF,IAAI,CAAC4C,GAAG,CAAC,IAAI,CAACjI,YAAY,CAACH,WAAW,CAACD,OAAO,EAAE6H,KAAK,CAAC;;;IAGpG,CAAC,CAAC;IAEF;IACA3H,QAAQ,CAAC4F,OAAO,CAACW,OAAO,IAAG;MACzB,IAAI,EAAEA,OAAO,IAAI,IAAI,CAAClH,cAAc,CAACW,QAAQ,CAAC,EAAE;QAC9C,IAAI,CAACX,cAAc,CAACW,QAAQ,CAACuG,OAAO,CAAC,GAAG,KAAK;;IAEjD,CAAC,CAAC;IAEF;IACA,IAAI,CAACqC,mBAAmB,EAAE;EAC5B;EAEA;EACQL,iBAAiBA,CAAA;IACvB,IAAI,CAACrI,YAAY,GAAG;MAClBZ,KAAK,EAAE;QACLC,MAAM,EAAE8H,MAAM,CAACC,SAAS;QACxB9H,OAAO,EAAE6H,MAAM,CAACC,SAAS;QACzB7H,SAAS,EAAE4H,MAAM,CAACC;OACnB;MACD5H,aAAa,EAAE;QACbC,YAAY,EAAE0H,MAAM,CAACC,SAAS;QAC9B1H,OAAO,EAAEyH,MAAM,CAACC,SAAS;QACzBzH,SAAS,EAAEwH,MAAM,CAACC,SAAS;QAC3BxH,OAAO,EAAEuH,MAAM,CAACC;OACjB;MACDvH,WAAW,EAAE;QACXJ,YAAY,EAAE0H,MAAM,CAACC,SAAS;QAC9B1H,OAAO,EAAEyH,MAAM,CAACC,SAAS;QACzBzH,SAAS,EAAEwH,MAAM,CAACC,SAAS;QAC3BxH,OAAO,EAAEuH,MAAM,CAACC;OACjB;MACDtH,QAAQ,EAAE;KACX;EACH;EAEA;EACQ4I,mBAAmBA,CAAA;IACzB;IACA,IAAI,IAAI,CAAC1I,YAAY,CAACZ,KAAK,CAACC,MAAM,KAAK8H,MAAM,CAACC,SAAS,EAAE,IAAI,CAACpH,YAAY,CAACZ,KAAK,CAACC,MAAM,GAAG,CAAC;IAC3F,IAAI,IAAI,CAACW,YAAY,CAACZ,KAAK,CAACE,OAAO,KAAK6H,MAAM,CAACC,SAAS,EAAE,IAAI,CAACpH,YAAY,CAACZ,KAAK,CAACE,OAAO,GAAG,CAAC;IAC7F,IAAI,IAAI,CAACU,YAAY,CAACZ,KAAK,CAACG,SAAS,KAAK4H,MAAM,CAACC,SAAS,EAAE,IAAI,CAACpH,YAAY,CAACZ,KAAK,CAACG,SAAS,GAAG,CAAC;IAEjG;IACA,IAAI,IAAI,CAACS,YAAY,CAACR,aAAa,CAACC,YAAY,KAAK0H,MAAM,CAACC,SAAS,EAAE,IAAI,CAACpH,YAAY,CAACR,aAAa,CAACC,YAAY,GAAG,CAAC;IACvH,IAAI,IAAI,CAACO,YAAY,CAACR,aAAa,CAACE,OAAO,KAAKyH,MAAM,CAACC,SAAS,EAAE,IAAI,CAACpH,YAAY,CAACR,aAAa,CAACE,OAAO,GAAG,CAAC;IAC7G,IAAI,IAAI,CAACM,YAAY,CAACR,aAAa,CAACG,SAAS,KAAKwH,MAAM,CAACC,SAAS,EAAE,IAAI,CAACpH,YAAY,CAACR,aAAa,CAACG,SAAS,GAAG,CAAC;IACjH,IAAI,IAAI,CAACK,YAAY,CAACR,aAAa,CAACI,OAAO,KAAKuH,MAAM,CAACC,SAAS,EAAE,IAAI,CAACpH,YAAY,CAACR,aAAa,CAACI,OAAO,GAAG,CAAC;IAE7G;IACA,IAAI,IAAI,CAACI,YAAY,CAACH,WAAW,CAACJ,YAAY,KAAK0H,MAAM,CAACC,SAAS,EAAE,IAAI,CAACpH,YAAY,CAACH,WAAW,CAACJ,YAAY,GAAG,CAAC;IACnH,IAAI,IAAI,CAACO,YAAY,CAACH,WAAW,CAACH,OAAO,KAAKyH,MAAM,CAACC,SAAS,EAAE,IAAI,CAACpH,YAAY,CAACH,WAAW,CAACH,OAAO,GAAG,CAAC;IACzG,IAAI,IAAI,CAACM,YAAY,CAACH,WAAW,CAACF,SAAS,KAAKwH,MAAM,CAACC,SAAS,EAAE,IAAI,CAACpH,YAAY,CAACH,WAAW,CAACF,SAAS,GAAG,CAAC;IAC7G,IAAI,IAAI,CAACK,YAAY,CAACH,WAAW,CAACD,OAAO,KAAKuH,MAAM,CAACC,SAAS,EAAE,IAAI,CAACpH,YAAY,CAACH,WAAW,CAACD,OAAO,GAAG,CAAC;IAEzG;IACAiD,MAAM,CAAC8F,IAAI,CAAC,IAAI,CAAC3I,YAAY,CAACF,QAAQ,CAAC,CAAC4F,OAAO,CAACW,OAAO,IAAG;MACxD,IAAI,IAAI,CAACrG,YAAY,CAACF,QAAQ,CAACuG,OAAO,CAAC,KAAKc,MAAM,CAACC,SAAS,EAAE;QAC5D,IAAI,CAACpH,YAAY,CAACF,QAAQ,CAACuG,OAAO,CAAC,GAAG,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAuC,aAAaA,CAACC,OAAe;IAC3B,IAAI,CAAC9I,gBAAgB,CAAC8I,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC9I,gBAAgB,CAAC8I,OAAO,CAAC;EAClE;EAEA;EACAC,gBAAgBA,CAACjE,MAA0C;IACzD,IAAI,CAAC1F,cAAc,CAACC,KAAK,CAACyF,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC1F,cAAc,CAACC,KAAK,CAACyF,MAAM,CAAC;IACtE,IAAI,CAACnB,eAAe,EAAE;EACxB;EAEA;EACAqF,yBAAyBA,CAAClE,MAA4D;IACpF,IAAI,CAAC1F,cAAc,CAACK,aAAa,CAACqF,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC1F,cAAc,CAACK,aAAa,CAACqF,MAAM,CAAC;IACtF,IAAI,CAACnB,eAAe,EAAE;EACxB;EAEA;EACAsF,uBAAuBA,CAACnE,MAA4D;IAClF,IAAI,CAAC1F,cAAc,CAACU,WAAW,CAACgF,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC1F,cAAc,CAACU,WAAW,CAACgF,MAAM,CAAC;IAClF,IAAI,CAACnB,eAAe,EAAE;EACxB;EAEA;EACAuF,mBAAmBA,CAAC5C,OAAe;IACjC,IAAI,CAAClH,cAAc,CAACW,QAAQ,CAACuG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAClH,cAAc,CAACW,QAAQ,CAACuG,OAAO,CAAC;IAC9E,IAAI,CAAC3C,eAAe,EAAE;EACxB;EAEA;EACAwF,eAAeA,CAAA;IACb;IACA,IAAI,CAAC/J,cAAc,CAACC,KAAK,CAACC,MAAM,GAAG,KAAK;IACxC,IAAI,CAACF,cAAc,CAACC,KAAK,CAACE,OAAO,GAAG,KAAK;IACzC,IAAI,CAACH,cAAc,CAACC,KAAK,CAACG,SAAS,GAAG,KAAK;IAE3C;IACA,IAAI,CAACJ,cAAc,CAACK,aAAa,CAACC,YAAY,GAAG,KAAK;IACtD,IAAI,CAACN,cAAc,CAACK,aAAa,CAACE,OAAO,GAAG,KAAK;IACjD,IAAI,CAACP,cAAc,CAACK,aAAa,CAACG,SAAS,GAAG,KAAK;IACnD,IAAI,CAACR,cAAc,CAACK,aAAa,CAACI,OAAO,GAAG,KAAK;IAEjD;IACA,IAAI,CAACT,cAAc,CAACU,WAAW,CAACJ,YAAY,GAAG,KAAK;IACpD,IAAI,CAACN,cAAc,CAACU,WAAW,CAACH,OAAO,GAAG,KAAK;IAC/C,IAAI,CAACP,cAAc,CAACU,WAAW,CAACF,SAAS,GAAG,KAAK;IACjD,IAAI,CAACR,cAAc,CAACU,WAAW,CAACD,OAAO,GAAG,KAAK;IAE/C;IACAiD,MAAM,CAAC8F,IAAI,CAAC,IAAI,CAACxJ,cAAc,CAACW,QAAQ,CAAC,CAAC4F,OAAO,CAACW,OAAO,IAAG;MAC1D,IAAI,CAAClH,cAAc,CAACW,QAAQ,CAACuG,OAAO,CAAC,GAAG,KAAK;IAC/C,CAAC,CAAC;IAEF;IACA,IAAI,CAAC3C,eAAe,EAAE;EACxB;EAEA;EACAyF,WAAWA,CAAC1B,KAAa;IACvB,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,GAAG;IAC3B,OAAOA,KAAK,CAAC2B,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;EAChC;EAEA;EACAC,cAAcA,CAAA;IACZ,OAAOxG,MAAM,CAAC8F,IAAI,CAAC,IAAI,CAACxJ,cAAc,CAACW,QAAQ,CAAC;EAClD;EAEA;EACAwJ,cAAcA,CAAC1F,MAAc;IAC3B;IACA,MAAM2F,QAAQ,GAAGrH,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;IAC9CD,QAAQ,CAACE,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCH,QAAQ,CAACE,KAAK,CAACE,GAAG,GAAG,GAAG;IACxBJ,QAAQ,CAACE,KAAK,CAACG,IAAI,GAAG,GAAG;IACzBL,QAAQ,CAACE,KAAK,CAACI,KAAK,GAAG,MAAM;IAC7BN,QAAQ,CAACE,KAAK,CAACK,MAAM,GAAG,MAAM;IAC9BP,QAAQ,CAACE,KAAK,CAACM,eAAe,GAAG,oBAAoB;IACrDR,QAAQ,CAACE,KAAK,CAACO,MAAM,GAAG,MAAM;IAC9BT,QAAQ,CAACE,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC/BV,QAAQ,CAACE,KAAK,CAACS,cAAc,GAAG,QAAQ;IACxCX,QAAQ,CAACE,KAAK,CAACU,UAAU,GAAG,QAAQ;IAEpC;IACA,MAAMC,YAAY,GAAGlI,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;IAClDY,YAAY,CAACX,KAAK,CAACM,eAAe,GAAG,OAAO;IAC5CK,YAAY,CAACX,KAAK,CAACY,OAAO,GAAG,MAAM;IACnCD,YAAY,CAACX,KAAK,CAACa,YAAY,GAAG,MAAM;IACxCF,YAAY,CAACX,KAAK,CAACc,QAAQ,GAAG,KAAK;IACnCH,YAAY,CAACX,KAAK,CAACe,SAAS,GAAG,KAAK;IACpCJ,YAAY,CAACX,KAAK,CAACgB,QAAQ,GAAG,MAAM;IACpCL,YAAY,CAACX,KAAK,CAACC,QAAQ,GAAG,UAAU;IACxCU,YAAY,CAACX,KAAK,CAACiB,SAAS,GAAG,gCAAgC;IAC/DN,YAAY,CAACX,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCP,YAAY,CAACX,KAAK,CAACmB,UAAU,GAAG,mBAAmB;IAEnD;IACA,MAAMC,WAAW,GAAG3I,QAAQ,CAACsH,aAAa,CAAC,QAAQ,CAAC;IACpDqB,WAAW,CAACC,SAAS,GAAG,SAAS;IACjCD,WAAW,CAACpB,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvCmB,WAAW,CAACpB,KAAK,CAACE,GAAG,GAAG,MAAM;IAC9BkB,WAAW,CAACpB,KAAK,CAACsB,KAAK,GAAG,MAAM;IAChCF,WAAW,CAACpB,KAAK,CAACuB,MAAM,GAAG,MAAM;IACjCH,WAAW,CAACpB,KAAK,CAACwB,UAAU,GAAG,MAAM;IACrCJ,WAAW,CAACpB,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACnCL,WAAW,CAACpB,KAAK,CAAC0B,MAAM,GAAG,SAAS;IACpCN,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnCE,WAAW,CAACpB,KAAK,CAAC2B,UAAU,GAAG,YAAY;IAC3CP,WAAW,CAACQ,WAAW,GAAG,MAAMR,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnEE,WAAW,CAACS,UAAU,GAAG,MAAMT,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IAClEE,WAAW,CAACU,OAAO,GAAG,MAAMrJ,QAAQ,CAACsJ,IAAI,CAACC,WAAW,CAAClC,QAAQ,CAAC;IAE/D;IACA,MAAMmC,MAAM,GAAGxJ,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;IAC5CkC,MAAM,CAACjC,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC7ByB,MAAM,CAACjC,KAAK,CAACU,UAAU,GAAG,QAAQ;IAClCuB,MAAM,CAACjC,KAAK,CAACkC,YAAY,GAAG,MAAM;IAClCD,MAAM,CAACjC,KAAK,CAACmC,aAAa,GAAG,MAAM;IACnCF,MAAM,CAACjC,KAAK,CAACoC,YAAY,GAAG,gBAAgB;IAE5C,MAAMC,IAAI,GAAG5J,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;IAC1CsC,IAAI,CAAChB,SAAS,GAAG,2FAA2F;IAE5G,MAAMiB,KAAK,GAAG7J,QAAQ,CAACsH,aAAa,CAAC,IAAI,CAAC;IAC1CuC,KAAK,CAACC,WAAW,GAAG,yBAAyB;IAC7CD,KAAK,CAACtC,KAAK,CAACwC,MAAM,GAAG,GAAG;IACxBF,KAAK,CAACtC,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC7Ba,KAAK,CAACtC,KAAK,CAACyC,UAAU,GAAG,KAAK;IAC9BH,KAAK,CAACtC,KAAK,CAACkB,KAAK,GAAG,SAAS;IAE7Be,MAAM,CAACS,WAAW,CAACL,IAAI,CAAC;IACxBJ,MAAM,CAACS,WAAW,CAACJ,KAAK,CAAC;IAEzB;IACA,MAAMK,gBAAgB,GAAGlK,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;IACtD4C,gBAAgB,CAAC3C,KAAK,CAACQ,OAAO,GAAG,MAAM;IACvCmC,gBAAgB,CAAC3C,KAAK,CAAC4C,aAAa,GAAG,QAAQ;IAC/CD,gBAAgB,CAAC3C,KAAK,CAAC6C,GAAG,GAAG,MAAM;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9E;IACA,IAAI5I,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAME,IAAI,GAAGL,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,IAAIG,IAAI,CAACoC,OAAO,EAAE;QAChB,MAAMoG,WAAW,GAAGvK,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QACjDiD,WAAW,CAAChD,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCwC,WAAW,CAAChD,KAAK,CAACU,UAAU,GAAG,QAAQ;QACvCsC,WAAW,CAAChD,KAAK,CAACkC,YAAY,GAAG,MAAM;QAEvC;QACA,IAAI1H,IAAI,CAACoC,OAAO,CAACqG,aAAa,EAAE;UAC9B,MAAMC,WAAW,GAAGzK,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;UACjDmD,WAAW,CAACC,GAAG,GAAG3I,IAAI,CAACoC,OAAO,CAACqG,aAAa;UAC5CC,WAAW,CAACE,GAAG,GAAG5I,IAAI,CAACoC,OAAO,CAACnC,IAAI;UACnCyI,WAAW,CAAClD,KAAK,CAACK,MAAM,GAAG,MAAM;UACjC6C,WAAW,CAAClD,KAAK,CAACqD,WAAW,GAAG,MAAM;UACtCL,WAAW,CAACN,WAAW,CAACQ,WAAW,CAAC;SACrC,MAAM;UACL,MAAMI,WAAW,GAAG7K,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;UACjDuD,WAAW,CAACjC,SAAS,GAAG,wFAAwF;UAChH2B,WAAW,CAACN,WAAW,CAACY,WAAW,CAAC;;QAGtC,MAAMC,WAAW,GAAG9K,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QACjDwD,WAAW,CAAClC,SAAS,GAAG,WAAW7G,IAAI,CAACoC,OAAO,CAACnC,IAAI,cAAcD,IAAI,CAACoC,OAAO,CAAC4G,iBAAiB,GAAG;QACnGD,WAAW,CAACvD,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCuB,WAAW,CAACN,WAAW,CAACa,WAAW,CAAC;QAEpCT,WAAW,CAACJ,WAAW,CAACM,WAAW,CAAC;;MAGtC;MACA,MAAMS,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC,eAAe,EAAElJ,IAAI,CAACmJ,QAAQ,IAAI,KAAK,CAAC;MACnFb,WAAW,CAACJ,WAAW,CAACe,eAAe,CAAC;MAExC;MACA,MAAMG,aAAa,GAAG,IAAI,CAACF,aAAa,CAAC,aAAa,EAAE,IAAI5M,IAAI,CAAC0D,IAAI,CAACqJ,UAAU,CAAC,CAACC,kBAAkB,EAAE,CAAC;MACvGhB,WAAW,CAACJ,WAAW,CAACkB,aAAa,CAAC;MAEtC;MACA,MAAMG,WAAW,GAAG,IAAI,CAACL,aAAa,CAAC,UAAU,EAAE,IAAI,CAACM,cAAc,CAACxJ,IAAI,CAACiD,QAAQ,CAAC,CAAC;MACtFqF,WAAW,CAACJ,WAAW,CAACqB,WAAW,CAAC;MAEpC;MACA,IAAIvJ,IAAI,CAAC9C,WAAW,EAAE;QACpB,MAAMuM,QAAQ,GAAG,IAAI,CAACP,aAAa,CAAC,OAAO,EAAE,GAAGlJ,IAAI,CAAC9C,WAAW,CAAC+C,IAAI,KAAKD,IAAI,CAAC9C,WAAW,CAACwM,IAAI,GAAG,CAAC;QACnGpB,WAAW,CAACJ,WAAW,CAACuB,QAAQ,CAAC;;MAGnC;MACA,MAAME,QAAQ,GAAG,IAAI,CAACT,aAAa,CAAC,OAAO,EAAElJ,IAAI,CAAC2B,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,GAAG3B,IAAI,CAAC2B,SAAS,UAAU,CAAC;MAClH2G,WAAW,CAACJ,WAAW,CAACyB,QAAQ,CAAC;;IAGnC;IACA,MAAMC,YAAY,GAAG,IAAI,CAACrB,aAAa,CAAC,eAAe,EAAE,UAAU,CAAC;IAEpE,IAAI5I,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAME,IAAI,GAAGL,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,MAAMgK,WAAW,GAAG5L,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;MACjDsE,WAAW,CAACrE,KAAK,CAACQ,OAAO,GAAG,MAAM;MAClC6D,WAAW,CAACrE,KAAK,CAACU,UAAU,GAAG,QAAQ;MACvC2D,WAAW,CAACrE,KAAK,CAACS,cAAc,GAAG,eAAe;MAClD4D,WAAW,CAACrE,KAAK,CAACwC,MAAM,GAAG,QAAQ;MACnC6B,WAAW,CAACrE,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEvC;MACA,MAAM5E,SAAS,GAAG5C,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;MAC/C1E,SAAS,CAAC2E,KAAK,CAACsE,SAAS,GAAG,QAAQ;MACpCjJ,SAAS,CAAC2E,KAAK,CAACuE,IAAI,GAAG,GAAG;MAE1B,IAAI/J,IAAI,CAACa,SAAS,EAAE;QAClB,MAAMtF,aAAa,GAAG0C,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QACnDhK,aAAa,CAACiK,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC1L,aAAa,CAACiK,KAAK,CAACyC,UAAU,GAAG,MAAM;QACvC1M,aAAa,CAACwM,WAAW,GAAG,IAAIzL,IAAI,CAAC0D,IAAI,CAACa,SAAS,CAACC,IAAI,CAAC,CAACkJ,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAErH,MAAMC,gBAAgB,GAAGlM,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QACtD4E,gBAAgB,CAAC3E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACxCkD,gBAAgB,CAAC3E,KAAK,CAAC4E,SAAS,GAAG,KAAK;QACxCD,gBAAgB,CAACtD,SAAS,GAAG,WAAW7G,IAAI,CAACa,SAAS,CAACwJ,OAAO,EAAEX,IAAI,IAAI,KAAK,WAAW;QAExF,MAAMY,aAAa,GAAGrM,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QACnD+E,aAAa,CAAC9E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCqD,aAAa,CAAC9E,KAAK,CAACkB,KAAK,GAAG,MAAM;QAClC4D,aAAa,CAACvC,WAAW,GAAG/H,IAAI,CAACa,SAAS,CAAC0J,IAAI,EAAEtK,IAAI,IAAI,KAAK;QAE9DY,SAAS,CAACqH,WAAW,CAAC3M,aAAa,CAAC;QACpCsF,SAAS,CAACqH,WAAW,CAACiC,gBAAgB,CAAC;QACvCtJ,SAAS,CAACqH,WAAW,CAACoC,aAAa,CAAC;;MAGtC;MACA,MAAME,cAAc,GAAGvM,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;MACpDiF,cAAc,CAAChF,KAAK,CAACuE,IAAI,GAAG,GAAG;MAC/BS,cAAc,CAAChF,KAAK,CAACQ,OAAO,GAAG,MAAM;MACrCwE,cAAc,CAAChF,KAAK,CAACU,UAAU,GAAG,QAAQ;MAC1CsE,cAAc,CAAChF,KAAK,CAACS,cAAc,GAAG,QAAQ;MAC9CuE,cAAc,CAAChF,KAAK,CAACY,OAAO,GAAG,QAAQ;MAEvC,MAAMqE,IAAI,GAAGxM,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;MAC1CkF,IAAI,CAACjF,KAAK,CAACK,MAAM,GAAG,KAAK;MACzB4E,IAAI,CAACjF,KAAK,CAACM,eAAe,GAAG,MAAM;MACnC2E,IAAI,CAACjF,KAAK,CAACI,KAAK,GAAG,MAAM;MACzB6E,IAAI,CAACjF,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEhC,MAAMiF,KAAK,GAAGzM,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;MAC3CmF,KAAK,CAAC7D,SAAS,GAAG,iGAAiG;MACnH6D,KAAK,CAAClF,KAAK,CAACC,QAAQ,GAAG,UAAU;MACjCiF,KAAK,CAAClF,KAAK,CAACE,GAAG,GAAG,MAAM;MACxBgF,KAAK,CAAClF,KAAK,CAACG,IAAI,GAAG,KAAK;MACxB+E,KAAK,CAAClF,KAAK,CAACmF,UAAU,GAAG,MAAM;MAC/BD,KAAK,CAAClF,KAAK,CAACM,eAAe,GAAG,OAAO;MACrC4E,KAAK,CAAClF,KAAK,CAACY,OAAO,GAAG,OAAO;MAE7BqE,IAAI,CAACvC,WAAW,CAACwC,KAAK,CAAC;MACvBF,cAAc,CAACtC,WAAW,CAACuC,IAAI,CAAC;MAEhC;MACA,MAAM3I,OAAO,GAAG7D,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;MAC7CzD,OAAO,CAAC0D,KAAK,CAACsE,SAAS,GAAG,QAAQ;MAClChI,OAAO,CAAC0D,KAAK,CAACuE,IAAI,GAAG,GAAG;MAExB,IAAI/J,IAAI,CAAC8B,OAAO,EAAE;QAChB,MAAMlG,WAAW,GAAGqC,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QACjD3J,WAAW,CAAC4J,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCrL,WAAW,CAAC4J,KAAK,CAACyC,UAAU,GAAG,MAAM;QACrCrM,WAAW,CAACmM,WAAW,GAAG,IAAIzL,IAAI,CAAC0D,IAAI,CAAC8B,OAAO,CAAChB,IAAI,CAAC,CAACkJ,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAEjH,MAAMU,cAAc,GAAG3M,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QACpDqF,cAAc,CAACpF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACtC2D,cAAc,CAACpF,KAAK,CAAC4E,SAAS,GAAG,KAAK;QACtCQ,cAAc,CAAC/D,SAAS,GAAG,WAAW7G,IAAI,CAAC8B,OAAO,CAACuI,OAAO,EAAEX,IAAI,IAAI,KAAK,WAAW;QAEpF,MAAMmB,WAAW,GAAG5M,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QACjDsF,WAAW,CAACrF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnC4D,WAAW,CAACrF,KAAK,CAACkB,KAAK,GAAG,MAAM;QAChCmE,WAAW,CAAC9C,WAAW,GAAG/H,IAAI,CAAC8B,OAAO,CAACyI,IAAI,EAAEtK,IAAI,IAAI,KAAK;QAE1D6B,OAAO,CAACoG,WAAW,CAACtM,WAAW,CAAC;QAChCkG,OAAO,CAACoG,WAAW,CAAC0C,cAAc,CAAC;QACnC9I,OAAO,CAACoG,WAAW,CAAC2C,WAAW,CAAC;;MAGlChB,WAAW,CAAC3B,WAAW,CAACrH,SAAS,CAAC;MAClCgJ,WAAW,CAAC3B,WAAW,CAACsC,cAAc,CAAC;MACvCX,WAAW,CAAC3B,WAAW,CAACpG,OAAO,CAAC;MAEhC8H,YAAY,CAAC1B,WAAW,CAAC2B,WAAW,CAAC;MAErC;MACA,IAAI7J,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACE,QAAQ,CAACJ,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMgL,aAAa,GAAG7M,QAAQ,CAACsH,aAAa,CAAC,IAAI,CAAC;QAClDuF,aAAa,CAAC/C,WAAW,GAAG,iBAAiB;QAC7C+C,aAAa,CAACtF,KAAK,CAAC4E,SAAS,GAAG,MAAM;QACtCU,aAAa,CAACtF,KAAK,CAACkC,YAAY,GAAG,MAAM;QACzCoD,aAAa,CAACtF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC6D,aAAa,CAACtF,KAAK,CAACyC,UAAU,GAAG,KAAK;QACtC2B,YAAY,CAAC1B,WAAW,CAAC4C,aAAa,CAAC;QAEvC,MAAMC,YAAY,GAAG9M,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QAClDwF,YAAY,CAACvF,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnC+E,YAAY,CAACvF,KAAK,CAAC4C,aAAa,GAAG,QAAQ;QAC3C2C,YAAY,CAACvF,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/BrI,IAAI,CAACE,QAAQ,CAACuB,OAAO,CAAC,CAACtB,OAAO,EAAE6K,KAAK,KAAI;UACvC,MAAMC,WAAW,GAAGhN,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;UACjD0F,WAAW,CAACzF,KAAK,CAACY,OAAO,GAAG,MAAM;UAClC6E,WAAW,CAACzF,KAAK,CAACM,eAAe,GAAG,SAAS;UAC7CmF,WAAW,CAACzF,KAAK,CAACa,YAAY,GAAG,KAAK;UACtC4E,WAAW,CAACzF,KAAK,CAACuB,MAAM,GAAG,gBAAgB;UAE3C,MAAMmE,aAAa,GAAGjN,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;UACnD2F,aAAa,CAAC1F,KAAK,CAACQ,OAAO,GAAG,MAAM;UACpCkF,aAAa,CAAC1F,KAAK,CAACS,cAAc,GAAG,eAAe;UACpDiF,aAAa,CAAC1F,KAAK,CAACkC,YAAY,GAAG,MAAM;UACzCwD,aAAa,CAAC1F,KAAK,CAACmC,aAAa,GAAG,MAAM;UAC1CuD,aAAa,CAAC1F,KAAK,CAACoC,YAAY,GAAG,gBAAgB;UAEnD,MAAMuD,YAAY,GAAGlN,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;UAClD4F,YAAY,CAACtE,SAAS,GAAG,mBAAmBmE,KAAK,GAAG,CAAC,cAAc7K,OAAO,CAACiC,OAAO,EAAEnC,IAAI,IAAI,SAAS,IAAIE,OAAO,CAACgJ,QAAQ,EAAE;UAE3H,MAAMiC,eAAe,GAAGnN,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;UACrD6F,eAAe,CAACrD,WAAW,GAAG,IAAI,CAACyB,cAAc,CAACrJ,OAAO,CAAC8C,QAAQ,CAAC;UACnEmI,eAAe,CAAC5F,KAAK,CAACkB,KAAK,GAAG,MAAM;UAEpCwE,aAAa,CAAChD,WAAW,CAACiD,YAAY,CAAC;UACvCD,aAAa,CAAChD,WAAW,CAACkD,eAAe,CAAC;UAC1CH,WAAW,CAAC/C,WAAW,CAACgD,aAAa,CAAC;UAEtC;UACA,MAAMG,YAAY,GAAGpN,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;UAClD8F,YAAY,CAAC7F,KAAK,CAACQ,OAAO,GAAG,MAAM;UACnCqF,YAAY,CAAC7F,KAAK,CAACU,UAAU,GAAG,QAAQ;UACxCmF,YAAY,CAAC7F,KAAK,CAACS,cAAc,GAAG,eAAe;UAEnD;UACA,MAAMqF,gBAAgB,GAAGrN,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;UACtD+F,gBAAgB,CAAC9F,KAAK,CAACuE,IAAI,GAAG,GAAG;UAEjC,IAAI5J,OAAO,CAACU,SAAS,EAAE;YACrB,MAAM0K,OAAO,GAAGtN,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;YAC7CgG,OAAO,CAAC/F,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCsD,OAAO,CAACxD,WAAW,GAAG,IAAIzL,IAAI,CAAC6D,OAAO,CAACU,SAAS,CAACC,IAAI,CAAC,CAACkJ,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAElH,MAAMsB,UAAU,GAAGvN,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;YAChDiG,UAAU,CAAChG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClCuE,UAAU,CAACzD,WAAW,GAAG,GAAG5H,OAAO,CAACU,SAAS,CAACwJ,OAAO,EAAEX,IAAI,IAAI,KAAK,KAAKvJ,OAAO,CAACU,SAAS,CAAC0J,IAAI,EAAEtK,IAAI,IAAI,KAAK,GAAG;YAEjHqL,gBAAgB,CAACpD,WAAW,CAACqD,OAAO,CAAC;YACrCD,gBAAgB,CAACpD,WAAW,CAACsD,UAAU,CAAC;;UAG1C;UACA,MAAMC,KAAK,GAAGxN,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;UAC3CkG,KAAK,CAAC5E,SAAS,GAAG,qFAAqF;UAEvG;UACA,MAAM6E,cAAc,GAAGzN,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;UACpDmG,cAAc,CAAClG,KAAK,CAACuE,IAAI,GAAG,GAAG;UAC/B2B,cAAc,CAAClG,KAAK,CAACsE,SAAS,GAAG,OAAO;UAExC,IAAI3J,OAAO,CAAC2B,OAAO,EAAE;YACnB,MAAM6J,OAAO,GAAG1N,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;YAC7CoG,OAAO,CAACnG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjC0D,OAAO,CAAC5D,WAAW,GAAG,IAAIzL,IAAI,CAAC6D,OAAO,CAAC2B,OAAO,CAAChB,IAAI,CAAC,CAACkJ,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAEhH,MAAM0B,UAAU,GAAG3N,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;YAChDqG,UAAU,CAACpG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClC2E,UAAU,CAAC7D,WAAW,GAAG,GAAG5H,OAAO,CAAC2B,OAAO,CAACuI,OAAO,EAAEX,IAAI,IAAI,KAAK,KAAKvJ,OAAO,CAAC2B,OAAO,CAACyI,IAAI,EAAEtK,IAAI,IAAI,KAAK,GAAG;YAE7GyL,cAAc,CAACxD,WAAW,CAACyD,OAAO,CAAC;YACnCD,cAAc,CAACxD,WAAW,CAAC0D,UAAU,CAAC;;UAGxCP,YAAY,CAACnD,WAAW,CAACoD,gBAAgB,CAAC;UAC1CD,YAAY,CAACnD,WAAW,CAACuD,KAAK,CAAC;UAC/BJ,YAAY,CAACnD,WAAW,CAACwD,cAAc,CAAC;UACxCT,WAAW,CAAC/C,WAAW,CAACmD,YAAY,CAAC;UAErCN,YAAY,CAAC7C,WAAW,CAAC+C,WAAW,CAAC;UAErC;UACA,IAAID,KAAK,GAAGhL,IAAI,CAACE,QAAQ,CAACJ,MAAM,GAAG,CAAC,EAAE;YACpC,MAAM+L,OAAO,GAAG5N,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;YAC7CsG,OAAO,CAACrG,KAAK,CAACsE,SAAS,GAAG,QAAQ;YAClC+B,OAAO,CAACrG,KAAK,CAACY,OAAO,GAAG,MAAM;YAC9ByF,OAAO,CAACrG,KAAK,CAACkB,KAAK,GAAG,SAAS;YAC/BmF,OAAO,CAACrG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAE/B;YACA,MAAM6E,cAAc,GAAG,IAAIxP,IAAI,CAAC6D,OAAO,CAAC2B,OAAO,EAAEhB,IAAI,IAAI,CAAC,CAAC,CAACE,OAAO,EAAE;YACrE,MAAM+K,aAAa,GAAG,IAAIzP,IAAI,CAAC0D,IAAI,CAACE,QAAQ,CAAC8K,KAAK,GAAG,CAAC,CAAC,CAACnK,SAAS,EAAEC,IAAI,IAAI,CAAC,CAAC,CAACE,OAAO,EAAE;YACvF,MAAMgL,WAAW,GAAG5K,IAAI,CAAC6K,KAAK,CAAC,CAACF,aAAa,GAAGD,cAAc,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;YAE9ED,OAAO,CAAChF,SAAS,GAAG,yCAAyC,IAAI,CAAC2C,cAAc,CAACwC,WAAW,CAAC,eAAe7L,OAAO,CAAC2B,OAAO,EAAEyI,IAAI,EAAEtK,IAAI,IAAI,iBAAiB,EAAE;YAE9J8K,YAAY,CAAC7C,WAAW,CAAC2D,OAAO,CAAC;;QAErC,CAAC,CAAC;QAEFjC,YAAY,CAAC1B,WAAW,CAAC6C,YAAY,CAAC;;;IAI1C;IACA,MAAMmB,aAAa,GAAG,IAAI,CAAC3D,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAE5D,IAAI5I,MAAM,CAACS,MAAM,IAAIT,MAAM,CAACS,MAAM,CAACN,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAMqM,UAAU,GAAGlO,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;MAChD4G,UAAU,CAAC3G,KAAK,CAACQ,OAAO,GAAG,MAAM;MACjCmG,UAAU,CAAC3G,KAAK,CAAC4C,aAAa,GAAG,QAAQ;MACzC+D,UAAU,CAAC3G,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE7B1I,MAAM,CAACS,MAAM,CAACqB,OAAO,CAAC,CAACpB,KAAK,EAAE2K,KAAK,KAAI;QACrC,MAAMoB,SAAS,GAAGnO,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QAC/C6G,SAAS,CAAC5G,KAAK,CAACY,OAAO,GAAG,MAAM;QAChCgG,SAAS,CAAC5G,KAAK,CAACM,eAAe,GAAG,SAAS;QAC3CsG,SAAS,CAAC5G,KAAK,CAACa,YAAY,GAAG,KAAK;QACpC+F,SAAS,CAAC5G,KAAK,CAACuB,MAAM,GAAG,gBAAgB;QAEzC;QACA,MAAMsF,WAAW,GAAGpO,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QACjD8G,WAAW,CAAC7G,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCqG,WAAW,CAAC7G,KAAK,CAACS,cAAc,GAAG,eAAe;QAClDoG,WAAW,CAAC7G,KAAK,CAACkC,YAAY,GAAG,MAAM;QACvC2E,WAAW,CAAC7G,KAAK,CAACmC,aAAa,GAAG,MAAM;QACxC0E,WAAW,CAAC7G,KAAK,CAACoC,YAAY,GAAG,gBAAgB;QAEjD,MAAM0E,UAAU,GAAGrO,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QAChD+G,UAAU,CAACzF,SAAS,GAAG,iBAAiBmE,KAAK,GAAG,CAAC,WAAW;QAC5DsB,UAAU,CAAC9G,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAElC,MAAMsF,UAAU,GAAGtO,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QAChDgH,UAAU,CAAC/G,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAClCsF,UAAU,CAAC/G,KAAK,CAACyC,UAAU,GAAG,MAAM;QACpCsE,UAAU,CAAC/G,KAAK,CAACkB,KAAK,GAAG,SAAS;QAClC6F,UAAU,CAACxE,WAAW,GAAG,GAAG1H,KAAK,CAACmD,KAAK,CAACU,MAAM,IAAI7D,KAAK,CAACmD,KAAK,CAACnG,QAAQ,EAAE;QAExEgP,WAAW,CAACnE,WAAW,CAACoE,UAAU,CAAC;QACnCD,WAAW,CAACnE,WAAW,CAACqE,UAAU,CAAC;QACnCH,SAAS,CAAClE,WAAW,CAACmE,WAAW,CAAC;QAElC;QACA,MAAMG,YAAY,GAAGvO,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;QAClDiH,YAAY,CAAChH,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnCwG,YAAY,CAAChH,KAAK,CAACiH,mBAAmB,GAAG,uCAAuC;QAChFD,YAAY,CAAChH,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B;QACA,MAAMqE,iBAAiB,GAAGrM,KAAK,CAACoD,YAAY,KAAKlC,SAAS,GAAGlB,KAAK,CAACoD,YAAY,GACrDpD,KAAK,CAACqD,QAAQ,GAAGrD,KAAK,CAACqD,QAAQ,CAACC,kBAAkB,GAAG,CAAE;QACjF,MAAMF,YAAY,GAAG,IAAI,CAACyF,aAAa,CAAC,cAAc,EAAEwD,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,eAAe,CAAC;QAC9GF,YAAY,CAACtE,WAAW,CAACzE,YAAY,CAAC;QAEtC;QACA,IAAIpD,KAAK,CAACsM,SAAS,EAAE;UACnB,MAAMC,OAAO,GAAG,IAAI,CAAC1D,aAAa,CAAC,YAAY,EAAE,IAAI5M,IAAI,CAAC+D,KAAK,CAACsM,SAAS,CAAC,CAACE,cAAc,EAAE,CAAC;UAC5FL,YAAY,CAACtE,WAAW,CAAC0E,OAAO,CAAC;;QAGnC;QACA,IAAIvM,KAAK,CAACyM,WAAW,EAAE;UACrB,MAAMA,WAAW,GAAG,IAAI,CAAC5D,aAAa,CAAC,cAAc,EAAE7I,KAAK,CAACyM,WAAW,CAAC7M,IAAI,CAAC;UAC9EuM,YAAY,CAACtE,WAAW,CAAC4E,WAAW,CAAC;;QAGvC;QACA,IAAIzM,KAAK,CAAC0M,cAAc,EAAE;UACxB,MAAMC,UAAU,GAAG,IAAI,CAAC9D,aAAa,CAAC,YAAY,EAAE7I,KAAK,CAAC0M,cAAc,CAACC,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC;UACnGR,YAAY,CAACtE,WAAW,CAAC8E,UAAU,CAAC;;QAGtC;QACA,IAAI3M,KAAK,CAAC4M,mBAAmB,IAAI5M,KAAK,CAAC4M,mBAAmB,CAACnN,MAAM,GAAG,CAAC,EAAE;UACrE,MAAMoN,YAAY,GAAGjP,QAAQ,CAACsH,aAAa,CAAC,IAAI,CAAC;UACjD2H,YAAY,CAACnF,WAAW,GAAG,qBAAqB;UAChDmF,YAAY,CAAC1H,KAAK,CAAC4E,SAAS,GAAG,MAAM;UACrC8C,YAAY,CAAC1H,KAAK,CAACkC,YAAY,GAAG,MAAM;UACxCwF,YAAY,CAAC1H,KAAK,CAACyB,QAAQ,GAAG,MAAM;UACpCiG,YAAY,CAAC1H,KAAK,CAACyC,UAAU,GAAG,KAAK;UACrCmE,SAAS,CAAClE,WAAW,CAACgF,YAAY,CAAC;UAEnC,MAAMC,gBAAgB,GAAGlP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;UACtD4H,gBAAgB,CAAC3H,KAAK,CAACQ,OAAO,GAAG,MAAM;UACvCmH,gBAAgB,CAAC3H,KAAK,CAAC4C,aAAa,GAAG,QAAQ;UAC/C+E,gBAAgB,CAAC3H,KAAK,CAAC6C,GAAG,GAAG,MAAM;UACnC8E,gBAAgB,CAAC3H,KAAK,CAACkC,YAAY,GAAG,MAAM;UAE5C;UACA,MAAM0F,cAAc,GAAG/M,KAAK,CAAC4M,mBAAmB,CAACrM,MAAM,CAACgC,CAAC,IAAIA,CAAC,CAACyK,WAAW,KAAK,CAAC,CAAC;UACjF,MAAMC,YAAY,GAAGjN,KAAK,CAAC4M,mBAAmB,CAACrM,MAAM,CAACgC,CAAC,IAAIA,CAAC,CAACyK,WAAW,KAAK,CAAC,CAAC;UAC/E,MAAME,WAAW,GAAGlN,KAAK,CAAC4M,mBAAmB,CAACrM,MAAM,CAACgC,CAAC,IAAIA,CAAC,CAACyK,WAAW,KAAK,CAAC,CAAC;UAE9E;UACA,IAAID,cAAc,CAACtN,MAAM,GAAG,CAAC,EAAE;YAC7BsN,cAAc,CAAC3L,OAAO,CAAC+L,OAAO,IAAG;cAC/B,MAAMC,WAAW,GAAGxP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjDkI,WAAW,CAACjI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClCyH,WAAW,CAACjI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvCuH,WAAW,CAACjI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCqH,WAAW,CAACjI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C2H,WAAW,CAACjI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCoH,WAAW,CAACjI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM2G,WAAW,GAAGzP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjDmI,WAAW,CAAC7G,SAAS,GAAG,8FAA8F;cAEtH,MAAM8G,WAAW,GAAG1P,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjDoI,WAAW,CAACnI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC2H,WAAW,CAACnI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMiF,WAAW,GAAGpP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjD8H,WAAW,CAACtF,WAAW,GAAG,iBAAiB;cAC3CsF,WAAW,CAAC7H,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCoF,WAAW,CAAC7H,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM2G,cAAc,GAAG3P,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACpDqI,cAAc,CAACpI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC2G,cAAc,CAACpI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAImH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACM,MAAM,GAAG,CAAC,EAAED,WAAW,IAAI,GAAGL,OAAO,CAACM,MAAM,KAAK;cAC7D,IAAIN,OAAO,CAACO,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIF,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACO,KAAK,WAAW;;cAE5CH,cAAc,CAAC7F,WAAW,GAAG8F,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAACzF,WAAW,CAACmF,WAAW,CAAC;cACpCM,WAAW,CAACzF,WAAW,CAAC0F,cAAc,CAAC;cAEvCH,WAAW,CAACvF,WAAW,CAACwF,WAAW,CAAC;cACpCD,WAAW,CAACvF,WAAW,CAACyF,WAAW,CAAC;cACpCR,gBAAgB,CAACjF,WAAW,CAACuF,WAAW,CAAC;YAC3C,CAAC,CAAC;;UAGJ;UACA,IAAIH,YAAY,CAACxN,MAAM,GAAG,CAAC,EAAE;YAC3BwN,YAAY,CAAC7L,OAAO,CAAC+L,OAAO,IAAG;cAC7B,MAAMC,WAAW,GAAGxP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjDkI,WAAW,CAACjI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClCyH,WAAW,CAACjI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvCuH,WAAW,CAACjI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCqH,WAAW,CAACjI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C2H,WAAW,CAACjI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCoH,WAAW,CAACjI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM2G,WAAW,GAAGzP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjDmI,WAAW,CAAC7G,SAAS,GAAG,+FAA+F;cAEvH,MAAM8G,WAAW,GAAG1P,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjDoI,WAAW,CAACnI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC2H,WAAW,CAACnI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMiF,WAAW,GAAGpP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjD8H,WAAW,CAACtF,WAAW,GAAG,eAAe;cACzCsF,WAAW,CAAC7H,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCoF,WAAW,CAAC7H,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM2G,cAAc,GAAG3P,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACpDqI,cAAc,CAACpI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC2G,cAAc,CAACpI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAImH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACM,MAAM,GAAG,CAAC,EAAED,WAAW,IAAI,GAAGL,OAAO,CAACM,MAAM,KAAK;cAC7D,IAAIN,OAAO,CAACO,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIF,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACO,KAAK,WAAW;;cAE5CH,cAAc,CAAC7F,WAAW,GAAG8F,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAACzF,WAAW,CAACmF,WAAW,CAAC;cACpCM,WAAW,CAACzF,WAAW,CAAC0F,cAAc,CAAC;cAEvCH,WAAW,CAACvF,WAAW,CAACwF,WAAW,CAAC;cACpCD,WAAW,CAACvF,WAAW,CAACyF,WAAW,CAAC;cACpCR,gBAAgB,CAACjF,WAAW,CAACuF,WAAW,CAAC;YAC3C,CAAC,CAAC;WACH,MAAM;YACL;YACA,MAAMA,WAAW,GAAGxP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;YACjDkI,WAAW,CAACjI,KAAK,CAACQ,OAAO,GAAG,MAAM;YAClCyH,WAAW,CAACjI,KAAK,CAACU,UAAU,GAAG,QAAQ;YACvCuH,WAAW,CAACjI,KAAK,CAACY,OAAO,GAAG,WAAW;YACvCqH,WAAW,CAACjI,KAAK,CAACM,eAAe,GAAG,SAAS;YAC7C2H,WAAW,CAACjI,KAAK,CAACa,YAAY,GAAG,KAAK;YACtCoH,WAAW,CAACjI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;YAE9C,MAAM2G,WAAW,GAAGzP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;YACjDmI,WAAW,CAAC7G,SAAS,GAAG,+FAA+F;YAEvH,MAAM8G,WAAW,GAAG1P,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;YACjDoI,WAAW,CAACnI,KAAK,CAACQ,OAAO,GAAG,MAAM;YAClC2H,WAAW,CAACnI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;YAE1C,MAAMiF,WAAW,GAAGpP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;YACjD8H,WAAW,CAACtF,WAAW,GAAG,eAAe;YACzCsF,WAAW,CAAC7H,KAAK,CAACyC,UAAU,GAAG,KAAK;YACpCoF,WAAW,CAAC7H,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAEnC,MAAM2G,cAAc,GAAG3P,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;YACpDqI,cAAc,CAACpI,KAAK,CAACyB,QAAQ,GAAG,MAAM;YACtC2G,cAAc,CAACpI,KAAK,CAACkB,KAAK,GAAG,MAAM;YACnCkH,cAAc,CAAC7F,WAAW,GAAG,UAAU;YAEvC4F,WAAW,CAACzF,WAAW,CAACmF,WAAW,CAAC;YACpCM,WAAW,CAACzF,WAAW,CAAC0F,cAAc,CAAC;YAEvCH,WAAW,CAACvF,WAAW,CAACwF,WAAW,CAAC;YACpCD,WAAW,CAACvF,WAAW,CAACyF,WAAW,CAAC;YACpCR,gBAAgB,CAACjF,WAAW,CAACuF,WAAW,CAAC;;UAG3C;UACA,IAAIF,WAAW,CAACzN,MAAM,GAAG,CAAC,EAAE;YAC1ByN,WAAW,CAAC9L,OAAO,CAAC+L,OAAO,IAAG;cAC5B,MAAMC,WAAW,GAAGxP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjDkI,WAAW,CAACjI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClCyH,WAAW,CAACjI,KAAK,CAACU,UAAU,GAAG,QAAQ;cACvCuH,WAAW,CAACjI,KAAK,CAACY,OAAO,GAAG,WAAW;cACvCqH,WAAW,CAACjI,KAAK,CAACM,eAAe,GAAG,SAAS;cAC7C2H,WAAW,CAACjI,KAAK,CAACa,YAAY,GAAG,KAAK;cACtCoH,WAAW,CAACjI,KAAK,CAACuB,MAAM,GAAG,mBAAmB;cAE9C,MAAM2G,WAAW,GAAGzP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjDmI,WAAW,CAAC7G,SAAS,GAAG,kGAAkG;cAE1H,MAAM8G,WAAW,GAAG1P,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjDoI,WAAW,CAACnI,KAAK,CAACQ,OAAO,GAAG,MAAM;cAClC2H,WAAW,CAACnI,KAAK,CAAC4C,aAAa,GAAG,QAAQ;cAE1C,MAAMiF,WAAW,GAAGpP,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACjD8H,WAAW,CAACtF,WAAW,GAAG,cAAc;cACxCsF,WAAW,CAAC7H,KAAK,CAACyC,UAAU,GAAG,KAAK;cACpCoF,WAAW,CAAC7H,KAAK,CAACyB,QAAQ,GAAG,MAAM;cAEnC,MAAM2G,cAAc,GAAG3P,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;cACpDqI,cAAc,CAACpI,KAAK,CAACyB,QAAQ,GAAG,MAAM;cACtC2G,cAAc,CAACpI,KAAK,CAACkB,KAAK,GAAG,MAAM;cAEnC,IAAImH,WAAW,GAAG,EAAE;cACpB,IAAIL,OAAO,CAACM,MAAM,GAAG,CAAC,EAAED,WAAW,IAAI,GAAGL,OAAO,CAACM,MAAM,KAAK;cAC7D,IAAIN,OAAO,CAACO,KAAK,GAAG,CAAC,EAAE;gBACrB,IAAIF,WAAW,EAAEA,WAAW,IAAI,KAAK;gBACrCA,WAAW,IAAI,GAAGL,OAAO,CAACO,KAAK,WAAW;;cAE5CH,cAAc,CAAC7F,WAAW,GAAG8F,WAAW,IAAI,UAAU;cAEtDF,WAAW,CAACzF,WAAW,CAACmF,WAAW,CAAC;cACpCM,WAAW,CAACzF,WAAW,CAAC0F,cAAc,CAAC;cAEvCH,WAAW,CAACvF,WAAW,CAACwF,WAAW,CAAC;cACpCD,WAAW,CAACvF,WAAW,CAACyF,WAAW,CAAC;cACpCR,gBAAgB,CAACjF,WAAW,CAACuF,WAAW,CAAC;YAC3C,CAAC,CAAC;;UAGJrB,SAAS,CAAClE,WAAW,CAACiF,gBAAgB,CAAC;;QAGzCf,SAAS,CAAClE,WAAW,CAACsE,YAAY,CAAC;QAEnCL,UAAU,CAACjE,WAAW,CAACkE,SAAS,CAAC;MACnC,CAAC,CAAC;MAEFF,aAAa,CAAChE,WAAW,CAACiE,UAAU,CAAC;;IAGvC;IACA,IAAIxM,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,IAAIF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACmO,QAAQ,IAAIrO,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACmO,QAAQ,CAAClO,MAAM,GAAG,CAAC,EAAE;MACtG,MAAMmO,eAAe,GAAG,IAAI,CAAC1F,aAAa,CAAC,UAAU,EAAE,mBAAmB,CAAC;MAE3E,MAAM2F,YAAY,GAAGjQ,QAAQ,CAACsH,aAAa,CAAC,IAAI,CAAC;MACjD2I,YAAY,CAAC1I,KAAK,CAAC2I,SAAS,GAAG,MAAM;MACrCD,YAAY,CAAC1I,KAAK,CAACY,OAAO,GAAG,GAAG;MAChC8H,YAAY,CAAC1I,KAAK,CAACwC,MAAM,GAAG,GAAG;MAC/BkG,YAAY,CAAC1I,KAAK,CAACQ,OAAO,GAAG,MAAM;MACnCkI,YAAY,CAAC1I,KAAK,CAACiH,mBAAmB,GAAG,uCAAuC;MAChFyB,YAAY,CAAC1I,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE/B1I,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACmO,QAAQ,CAACvM,OAAO,CAAC2M,OAAO,IAAG;QACzC,MAAMC,WAAW,GAAGpQ,QAAQ,CAACsH,aAAa,CAAC,IAAI,CAAC;QAChD8I,WAAW,CAAC7I,KAAK,CAACY,OAAO,GAAG,MAAM;QAClCiI,WAAW,CAAC7I,KAAK,CAACM,eAAe,GAAG,SAAS;QAC7CuI,WAAW,CAAC7I,KAAK,CAACa,YAAY,GAAG,KAAK;QACtCgI,WAAW,CAACxH,SAAS,GAAG,2EAA2EuH,OAAO,CAACnO,IAAI,IAAI,SAAS,EAAE;QAC9HiO,YAAY,CAAChG,WAAW,CAACmG,WAAW,CAAC;MACvC,CAAC,CAAC;MAEFJ,eAAe,CAAC/F,WAAW,CAACgG,YAAY,CAAC;MACzC/F,gBAAgB,CAACD,WAAW,CAAC+F,eAAe,CAAC;;IAG/C;IACA9F,gBAAgB,CAACD,WAAW,CAACI,WAAW,CAAC;IACzCH,gBAAgB,CAACD,WAAW,CAAC0B,YAAY,CAAC;IAC1CzB,gBAAgB,CAACD,WAAW,CAACgE,aAAa,CAAC;IAE3C;IACA/F,YAAY,CAAC+B,WAAW,CAACtB,WAAW,CAAC;IACrCT,YAAY,CAAC+B,WAAW,CAACT,MAAM,CAAC;IAChCtB,YAAY,CAAC+B,WAAW,CAACC,gBAAgB,CAAC;IAC1C7C,QAAQ,CAAC4C,WAAW,CAAC/B,YAAY,CAAC;IAElC;IACAlI,QAAQ,CAACsJ,IAAI,CAACW,WAAW,CAAC5C,QAAQ,CAAC;EACrC;EAEA;EACQiD,aAAaA,CAACT,KAAa,EAAEwG,SAAiB;IACpD,MAAM1J,OAAO,GAAG3G,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;IAC7CX,OAAO,CAACY,KAAK,CAACM,eAAe,GAAG,SAAS;IACzClB,OAAO,CAACY,KAAK,CAACa,YAAY,GAAG,KAAK;IAClCzB,OAAO,CAACY,KAAK,CAACY,OAAO,GAAG,MAAM;IAC9BxB,OAAO,CAACY,KAAK,CAACiB,SAAS,GAAG,+BAA+B;IAEzD,MAAM8H,aAAa,GAAGtQ,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;IACnDgJ,aAAa,CAAC/I,KAAK,CAACQ,OAAO,GAAG,MAAM;IACpCuI,aAAa,CAAC/I,KAAK,CAACU,UAAU,GAAG,QAAQ;IACzCqI,aAAa,CAAC/I,KAAK,CAACkC,YAAY,GAAG,MAAM;IAEzC,MAAMzM,IAAI,GAAGgD,QAAQ,CAACsH,aAAa,CAAC,GAAG,CAAC;IACxCtK,IAAI,CAACuT,SAAS,GAAG,OAAOF,SAAS,EAAE;IACnCrT,IAAI,CAACuK,KAAK,CAACkB,KAAK,GAAG,SAAS;IAC5BzL,IAAI,CAACuK,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC5BhM,IAAI,CAACuK,KAAK,CAACqD,WAAW,GAAG,MAAM;IAE/B,MAAM4F,YAAY,GAAGxQ,QAAQ,CAACsH,aAAa,CAAC,IAAI,CAAC;IACjDkJ,YAAY,CAAC1G,WAAW,GAAGD,KAAK;IAChC2G,YAAY,CAACjJ,KAAK,CAACwC,MAAM,GAAG,GAAG;IAC/ByG,YAAY,CAACjJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCwH,YAAY,CAACjJ,KAAK,CAACyC,UAAU,GAAG,KAAK;IAErCsG,aAAa,CAACrG,WAAW,CAACjN,IAAI,CAAC;IAC/BsT,aAAa,CAACrG,WAAW,CAACuG,YAAY,CAAC;IACvC7J,OAAO,CAACsD,WAAW,CAACqG,aAAa,CAAC;IAElC,OAAO3J,OAAO;EAChB;EAEA;EACQsE,aAAaA,CAAClO,KAAa,EAAED,KAAa;IAChD,MAAM2T,GAAG,GAAGzQ,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;IACzCmJ,GAAG,CAAClJ,KAAK,CAACkC,YAAY,GAAG,MAAM;IAE/B,MAAMiH,YAAY,GAAG1Q,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;IAClDoJ,YAAY,CAAC5G,WAAW,GAAG/M,KAAK;IAChC2T,YAAY,CAACnJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpC0H,YAAY,CAACnJ,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCiI,YAAY,CAACnJ,KAAK,CAACkC,YAAY,GAAG,KAAK;IAEvC,MAAMkH,YAAY,GAAG3Q,QAAQ,CAACsH,aAAa,CAAC,KAAK,CAAC;IAClDqJ,YAAY,CAAC7G,WAAW,GAAGhN,KAAK;IAChC6T,YAAY,CAACpJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAEpCyH,GAAG,CAACxG,WAAW,CAACyG,YAAY,CAAC;IAC7BD,GAAG,CAACxG,WAAW,CAAC0G,YAAY,CAAC;IAE7B,OAAOF,GAAG;EACZ;EAEA;EACAG,gBAAgBA,CAAClP,MAAc;IAC7B,IAAIA,MAAM,IAAIA,MAAM,CAACS,MAAM,IAAIT,MAAM,CAACS,MAAM,CAACN,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAIgP,OAAO,GAAGnP,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,CAAC0O,OAAO,IAAInP,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,CAACL,EAAE;MAE7D;MACA,IAAIgP,QAAQ,GAAG,IAAI,CAAC5U,YAAY;MAEhC;MACA,IAAI,CAAC4U,QAAQ,EAAE;QACbA,QAAQ,GAAGpP,MAAM,CAACI,EAAE;;MAGtB;MACA,MAAMtF,eAAe,GAAG,IAAI,CAACd,iBAAiB,CAACqV,kBAAkB,EAAE;MAEnE;MACA,MAAMC,aAAa,GAAGC,IAAI,CAACC,SAAS,CAAC1U,eAAe,CAAC;MAErD4E,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEyP,QAAQ,EAAE,UAAU,EAAED,OAAO,EAAE,iBAAiB,EAAEG,aAAa,CAAC;MAEtH;MACA,IAAI,CAACvV,MAAM,CAAC0V,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UACXN,QAAQ,EAAEA,QAAQ;UAClBD,OAAO,EAAEA,OAAO;UAChBQ,UAAU,EAAEL;;OAEf,CAAC;KACH,MAAM;MACL5P,OAAO,CAACkQ,KAAK,CAAC,sCAAsC,EAAE5P,MAAM,CAAC;;EAEjE;EAEAP,gBAAgBA,CAAA;IACd;IACA;IACA;EAAA;EAGFD,iBAAiBA,CAAA;IACf;IACA,MAAMqQ,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACjC,MAAMC,mBAAmB,GAAG,CAAC,CAAC,CAAG;IAEjC,IAAI,CAAChW,cAAc,CAACiW,kBAAkB,CAACF,qBAAqB,CAAC,CAACG,SAAS,CAACC,SAAS,IAAG;MAClF,IAAI,CAAChW,kBAAkB,GAAGgW,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAACnW,cAAc,CAACiW,kBAAkB,CAACD,mBAAmB,CAAC,CAACE,SAAS,CAACC,SAAS,IAAG;MAChF,IAAI,CAAC/V,gBAAgB,GAAG+V,SAAS;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAACnT,UAAU,CAAC6E,GAAG,CAAC,mBAAmB,CAAC,EAAEuO,YAAY,CACnDC,IAAI,CACHhX,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC+B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B;QACA,IAAIA,KAAK,CAAC+E,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACrG,cAAc,CAACiW,kBAAkB,CAACF,qBAAqB,CAAC,CAACM,IAAI,CACvE7W,GAAG,CAAC2W,SAAS,IAAIA,SAAS,CAAChP,MAAM,CAACmP,QAAQ,IACxCA,QAAQ,CAAC9P,IAAI,CAAC+P,WAAW,EAAE,CAAC3N,QAAQ,CAACtH,KAAK,CAACiV,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACrG,IAAI,IAAIqG,QAAQ,CAACrG,IAAI,CAACsG,WAAW,EAAE,CAAC3N,QAAQ,CAACtH,KAAK,CAACiV,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAACvW,cAAc,CAACiW,kBAAkB,CAACF,qBAAqB,CAAC;;;MAGxE,OAAOtW,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAyW,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAAChW,kBAAkB,GAAGgW,SAAS;IACrC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACnT,UAAU,CAAC6E,GAAG,CAAC,iBAAiB,CAAC,EAAEuO,YAAY,CACjDC,IAAI,CACHhX,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC+B,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B;QACA,IAAIA,KAAK,CAAC+E,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACrG,cAAc,CAACiW,kBAAkB,CAACD,mBAAmB,CAAC,CAACK,IAAI,CACrE7W,GAAG,CAAC2W,SAAS,IAAIA,SAAS,CAAChP,MAAM,CAACmP,QAAQ,IACxCA,QAAQ,CAAC9P,IAAI,CAAC+P,WAAW,EAAE,CAAC3N,QAAQ,CAACtH,KAAK,CAACiV,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACrG,IAAI,IAAIqG,QAAQ,CAACrG,IAAI,CAACsG,WAAW,EAAE,CAAC3N,QAAQ,CAACtH,KAAK,CAACiV,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAACvW,cAAc,CAACiW,kBAAkB,CAACD,mBAAmB,CAAC;;;MAGtE,OAAOvW,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAyW,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAAC/V,gBAAgB,GAAG+V,SAAS;IACnC,CAAC,CAAC;EACN;EAEAK,eAAeA,CAACF,QAA+B;IAC7C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAIG,WAAW,GAAGH,QAAQ,CAAC9P,IAAI;IAC/B,IAAI8P,QAAQ,CAACrG,IAAI,EAAE;MACjBwG,WAAW,IAAI,KAAKH,QAAQ,CAACrG,IAAI,GAAG;;IAEtC,IAAIqG,QAAQ,CAACvR,IAAI,KAAKpF,YAAY,CAAC+W,OAAO,IAAIJ,QAAQ,CAACxF,IAAI,EAAE;MAC3D2F,WAAW,IAAI,MAAMH,QAAQ,CAACxF,IAAI,EAAE;;IAEtC,OAAO2F,WAAW;EACpB;EAEA;EACAE,aAAaA,CAAC5R,IAAY;IACxB,IAAI,CAACjE,iBAAiB,GAAGiE,IAAI;IAE7B;IACA,IAAIA,IAAI,KAAK,IAAI,CAACpE,mBAAmB,EAAE;MACrC,IAAI,CAACqC,UAAU,CAAC6E,GAAG,CAAC,cAAc,CAAC,EAAE+O,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MACpD;MACA,IAAI,CAAC5T,UAAU,CAAC6E,GAAG,CAAC,YAAY,CAAC,EAAEgP,eAAe,EAAE;KACrD,MAAM,IAAI9R,IAAI,KAAK,IAAI,CAACnE,sBAAsB,EAAE;MAC/C,IAAI,CAACoC,UAAU,CAAC6E,GAAG,CAAC,cAAc,CAAC,EAAE+O,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MACpD;MACA,IAAI,CAAC5T,UAAU,CAAC6E,GAAG,CAAC,YAAY,CAAC,EAAEiP,aAAa,CAAC,CAAC1X,UAAU,CAAC+D,QAAQ,CAAC,CAAC;;IAGzE;IACA,IAAI,CAACH,UAAU,CAAC6E,GAAG,CAAC,YAAY,CAAC,EAAEkP,sBAAsB,EAAE;EAC7D;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAChU,UAAU,CAACiU,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAClU,UAAU,CAAC;MAC1C;;IAGF,IAAI,CAAC3C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACI,YAAY,GAAG,EAAE;IACtB,IAAI,CAACD,WAAW,GAAG,IAAI;IAEvB,MAAMyG,SAAS,GAAG,IAAI,CAACjE,UAAU,CAAC1B,KAAK;IAEvC;IACAsE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEoB,SAAS,CAAC;IACtCrB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoB,SAAS,CAACxD,WAAW,CAAC;IAC5DmC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC/E,iBAAiB,CAAC;IAE3D;IACA,IAAIqW,OAAY;IAEhB,IAAI,IAAI,CAACrW,iBAAiB,KAAK,IAAI,CAACH,mBAAmB,EAAE;MACvD;MACAwW,OAAO,GAAG;QACRC,WAAW,EAAEnQ,SAAS,CAAC/D,WAAW;QAClCmU,YAAY,EAAE,CAAC,GAAG,CAAC;QACnBC,OAAO,EAAErQ,SAAS,CAAC1D,aAAa;QAChCgU,kBAAkB,EAAE,CAClB;UACEjR,EAAE,EAAEW,SAAS,CAAC5D,iBAAiB,EAAEiD,EAAE,IAAI,EAAE;UACzCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACDyS,gBAAgB,EAAE,CAChB;UACElR,EAAE,EAAEW,SAAS,CAAC3D,eAAe,EAAEgD,EAAE,IAAI,EAAE;UACvCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD0S,UAAU,EAAE,IAAI,CAACjS,kBAAkB,EAAE;QACrCkS,qBAAqB,EAAEzQ,SAAS,CAACvD,OAAO;QACxCiU,oBAAoB,EAAE;UACpBC,oBAAoB,EAAE;YACpB3T,sBAAsB,EAAEgD,SAAS,CAAChD;;SAErC;QACDJ,sBAAsB,EAAEoD,SAAS,CAACpD,sBAAsB;QACxDC,wBAAwB,EAAEmD,SAAS,CAACnD,wBAAwB;QAC5DC,6BAA6B,EAAEkD,SAAS,CAAClD,6BAA6B;QACtEC,mBAAmB,EAAEiD,SAAS,CAACjD,mBAAmB;QAClDxB,aAAa,EAAE,CAACyE,SAAS,CAACxD,WAAW,CAAC;QACtCoU,OAAO,EAAE5Q,SAAS,CAACtD,OAAO;QAC1BmU,QAAQ,EAAE7Q,SAAS,CAACrD;OACrB;MAEDgC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEsR,OAAO,CAACE,YAAY,CAAC;KACvF,MAAM,IAAI,IAAI,CAACvW,iBAAiB,KAAK,IAAI,CAACF,sBAAsB,EAAE;MACjE;MACAuW,OAAO,GAAG;QACRC,WAAW,EAAEnQ,SAAS,CAAC/D,WAAW;QAClCmU,YAAY,EAAE,CAAC,GAAG,CAAC;QACnBC,OAAO,EAAErQ,SAAS,CAAC1D,aAAa;QAChCwU,UAAU,EAAE9Q,SAAS,CAACzD,UAAU;QAChC+T,kBAAkB,EAAE,CAClB;UACEjR,EAAE,EAAEW,SAAS,CAAC5D,iBAAiB,EAAEiD,EAAE,IAAI,EAAE;UACzCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACDyS,gBAAgB,EAAE,CAChB;UACElR,EAAE,EAAEW,SAAS,CAAC3D,eAAe,EAAEgD,EAAE,IAAI,EAAE;UACvCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD0S,UAAU,EAAE,IAAI,CAACjS,kBAAkB,EAAE;QACrCkS,qBAAqB,EAAEzQ,SAAS,CAACvD,OAAO;QACxCiU,oBAAoB,EAAE;UACpBC,oBAAoB,EAAE;YACpB3T,sBAAsB,EAAEgD,SAAS,CAAChD;;SAErC;QACDJ,sBAAsB,EAAEoD,SAAS,CAACpD,sBAAsB;QACxDC,wBAAwB,EAAEmD,SAAS,CAACnD,wBAAwB;QAC5DC,6BAA6B,EAAEkD,SAAS,CAAClD,6BAA6B;QACtEC,mBAAmB,EAAEiD,SAAS,CAACjD,mBAAmB;QAClDxB,aAAa,EAAE,CAACyE,SAAS,CAACxD,WAAW,CAAC;QACtCoU,OAAO,EAAE5Q,SAAS,CAACtD,OAAO;QAC1BmU,QAAQ,EAAE7Q,SAAS,CAACrD;OACrB;MAEDgC,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEsR,OAAO,CAACE,YAAY,CAAC;;IAG3F;IACA,IAAI,IAAI,CAACvW,iBAAiB,KAAK,IAAI,CAACH,mBAAmB,EAAE;MACvD;MACAwW,OAAO,GAAG;QACRC,WAAW,EAAEnQ,SAAS,CAAC/D,WAAW;QAClCmU,YAAY,EAAE,CAAC,GAAG,CAAC;QACnBC,OAAO,EAAErQ,SAAS,CAAC1D,aAAa;QAChCgU,kBAAkB,EAAE,CAClB;UACEjR,EAAE,EAAEW,SAAS,CAAC5D,iBAAiB,EAAEiD,EAAE,IAAI,EAAE;UACzCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACDyS,gBAAgB,EAAE,CAChB;UACElR,EAAE,EAAEW,SAAS,CAAC3D,eAAe,EAAEgD,EAAE,IAAI,EAAE;UACvCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD0S,UAAU,EAAE,IAAI,CAACjS,kBAAkB,EAAE;QACrCkS,qBAAqB,EAAEzQ,SAAS,CAACvD,OAAO;QACxCiU,oBAAoB,EAAE;UACpBC,oBAAoB,EAAE;YACpB3T,sBAAsB,EAAEgD,SAAS,CAAChD;;SAErC;QACDJ,sBAAsB,EAAEoD,SAAS,CAACpD,sBAAsB;QACxDC,wBAAwB,EAAEmD,SAAS,CAACnD,wBAAwB;QAC5DC,6BAA6B,EAAEkD,SAAS,CAAClD,6BAA6B;QACtEC,mBAAmB,EAAEiD,SAAS,CAACjD,mBAAmB;QAClDxB,aAAa,EAAE,CAACyE,SAAS,CAACxD,WAAW,CAAC;QACtCoU,OAAO,EAAE5Q,SAAS,CAACtD,OAAO;QAC1BmU,QAAQ,EAAE7Q,SAAS,CAACrD;OACrB;MAEDgC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEsR,OAAO,CAACE,YAAY,CAAC;KACvF,MAAM,IAAI,IAAI,CAACvW,iBAAiB,KAAK,IAAI,CAACF,sBAAsB,EAAE;MACjE;MACAuW,OAAO,GAAG;QACRC,WAAW,EAAEnQ,SAAS,CAAC/D,WAAW;QAClCmU,YAAY,EAAE,CAAC,GAAG,CAAC;QACnBC,OAAO,EAAErQ,SAAS,CAAC1D,aAAa;QAChCwU,UAAU,EAAE9Q,SAAS,CAACzD,UAAU;QAChC+T,kBAAkB,EAAE,CAClB;UACEjR,EAAE,EAAEW,SAAS,CAAC5D,iBAAiB,EAAEiD,EAAE,IAAI,EAAE;UACzCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACDyS,gBAAgB,EAAE,CAChB;UACElR,EAAE,EAAEW,SAAS,CAAC3D,eAAe,EAAEgD,EAAE,IAAI,EAAE;UACvCvB,IAAI,EAAE,CAAC,CAAC;SACT,CACF;;QACD0S,UAAU,EAAE,IAAI,CAACjS,kBAAkB,EAAE;QACrCkS,qBAAqB,EAAEzQ,SAAS,CAACvD,OAAO;QACxCiU,oBAAoB,EAAE;UACpBC,oBAAoB,EAAE;YACpB3T,sBAAsB,EAAEgD,SAAS,CAAChD;;SAErC;QACDJ,sBAAsB,EAAEoD,SAAS,CAACpD,sBAAsB;QACxDC,wBAAwB,EAAEmD,SAAS,CAACnD,wBAAwB;QAC5DC,6BAA6B,EAAEkD,SAAS,CAAClD,6BAA6B;QACtEC,mBAAmB,EAAEiD,SAAS,CAACjD,mBAAmB;QAClDxB,aAAa,EAAE,CAACyE,SAAS,CAACxD,WAAW,CAAC;QACtCoU,OAAO,EAAE5Q,SAAS,CAACtD,OAAO;QAC1BmU,QAAQ,EAAE7Q,SAAS,CAACrD;OACrB;MAEDgC,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEsR,OAAO,CAACE,YAAY,CAAC;;IAG3F;IACAzR,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtCD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC/E,iBAAiB,KAAK,IAAI,CAACH,mBAAmB,GAAG,SAAS,GAAG,YAAY,CAAC;IAC3GiF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoB,SAAS,CAAC1D,aAAa,CAAC;IACvD,IAAI,IAAI,CAACzC,iBAAiB,KAAK,IAAI,CAACF,sBAAsB,EAAE;MAC1DgF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEoB,SAAS,CAACzD,UAAU,CAAC;;IAGnD;IACA,IAAI,CAACxD,cAAc,CAACgY,WAAW,CAACb,OAAO,CAAC,CACnCjB,SAAS,CAAC;MACT+B,IAAI,EAAGC,QAAwB,IAAI;QACnC,IAAI,CAAC7X,SAAS,GAAG,KAAK;QACtB,IAAI6X,QAAQ,CAAClK,MAAM,CAACmK,OAAO,EAAE;UAC3B,IAAI,CAAC7X,aAAa,GAAG4X,QAAQ,CAACpK,IAAI,CAAC9E,OAAO;UAE1C;UACApD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;UACrCD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACvF,aAAa,CAAC+F,MAAM,CAAC;UAEjE;UACA,IAAI,IAAI,CAAC/F,aAAa,CAAC+F,MAAM,GAAG,CAAC,EAAE;YACjCT,OAAO,CAAC3C,KAAK,CAAC,uBAAuB,CAAC;YACtC,IAAI,CAAC3C,aAAa,CAAC8X,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACpQ,OAAO,CAAC9B,MAAM,IAAG;cAC9C,IAAIA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;gBAC3CT,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEK,MAAM,CAACI,EAAE,CAAC;gBAEpC;gBACA,IAAIJ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,IAAIlB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,EAAE;kBAC/D,MAAMgR,OAAO,GAAG,IAAIxV,IAAI,CAACqD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,CAAC;kBACxDzB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwS,OAAO,CAACvV,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEsV,OAAO,CAAC9H,kBAAkB,EAAE,CAAC;;gBAGvG;gBACA,IAAIrK,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,IAAIH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,IAAIlB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,EAAE;kBAC1F,MAAMiR,OAAO,GAAG,IAAIzV,IAAI,CAACqD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACgB,SAAS,CAACC,IAAI,CAAC;kBACxDzB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyS,OAAO,CAACxV,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEuV,OAAO,CAAC/H,kBAAkB,EAAE,CAAC;;gBAGtG;gBACA,IAAIrK,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,IAAIP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACJ,MAAM,GAAG,CAAC,EAAE;kBACnET,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEK,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACJ,MAAM,CAAC;kBAC/D,MAAMkS,YAAY,GAAGrS,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;kBAChD,MAAM+R,WAAW,GAAGtS,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACJ,MAAM,GAAG,CAAC,CAAC;kBAEjF,IAAIkS,YAAY,CAACnR,SAAS,IAAImR,YAAY,CAACnR,SAAS,CAACC,IAAI,EAAE;oBACzD,MAAMoR,YAAY,GAAG,IAAI5V,IAAI,CAAC0V,YAAY,CAACnR,SAAS,CAACC,IAAI,CAAC;oBAC1DzB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE4S,YAAY,CAAC3V,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE0V,YAAY,CAAClI,kBAAkB,EAAE,CAAC;;kBAGtH,IAAIiI,WAAW,CAACnQ,OAAO,IAAImQ,WAAW,CAACnQ,OAAO,CAAChB,IAAI,EAAE;oBACnD,MAAMqR,WAAW,GAAG,IAAI7V,IAAI,CAAC2V,WAAW,CAACnQ,OAAO,CAAChB,IAAI,CAAC;oBACtDzB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE6S,WAAW,CAAC5V,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE2V,WAAW,CAACnI,kBAAkB,EAAE,CAAC;;;;YAIvH,CAAC,CAAC;YACF3K,OAAO,CAAC+S,QAAQ,EAAE;;UAGpB;UACA,MAAMC,cAAc,GAAG,IAAI,CAAC7R,mBAAmB,CAAC,IAAI,CAACzG,aAAa,CAAC;UACnEsF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE+S,cAAc,CAACvS,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC/F,aAAa,CAAC+F,MAAM,CAAC;UAE5G;UACAT,OAAO,CAAC3C,KAAK,CAAC,uBAAuB,CAAC;UACtC2C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoB,SAAS,CAACxD,WAAW,CAAC;UAC5DmC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqS,QAAQ,CAACpK,IAAI,CAAC9E,OAAO,CAAC3C,MAAM,CAAC;UAEpE;UACA,MAAMwS,cAAc,GAA2B;YAC7C,CAACnZ,eAAe,CAAC+C,KAAK,CAACqW,QAAQ,EAAE,GAAG,CAAC;YACrC,CAACpZ,eAAe,CAACgD,OAAO,CAACoW,QAAQ,EAAE,GAAG,CAAC;YACvC,CAACpZ,eAAe,CAACiD,QAAQ,CAACmW,QAAQ,EAAE,GAAG,CAAC;YACxC,SAAS,EAAE;WACZ;UAEDZ,QAAQ,CAACpK,IAAI,CAAC9E,OAAO,CAAChB,OAAO,CAAC,CAAC9B,MAAM,EAAEqL,KAAK,KAAI;YAC9C,IAAIrL,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,IAAIH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,EAAE;cAC1E,MAAMsV,SAAS,GAAG7S,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,CAACsB,IAAI,CAAC+T,QAAQ,EAAE;cAC7D,IAAID,cAAc,CAACE,SAAS,CAAC,KAAKjR,SAAS,EAAE;gBAC3C+Q,cAAc,CAACE,SAAS,CAAC,EAAE;eAC5B,MAAM;gBACLF,cAAc,CAAC,SAAS,CAAC,EAAE;;cAG7B;cACAjT,OAAO,CAACC,GAAG,CAAC,UAAUK,MAAM,CAACI,EAAE,SAAS,EACtCJ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,GAC3B,GAAGyC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,CAAC+C,IAAI,WAAWN,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,CAACsB,IAAI,GAAG,GACjF,eAAe,CAAC;cAElB;cACA,IAAIwM,KAAK,KAAK,CAAC,EAAE;gBACf3L,OAAO,CAAC3C,KAAK,CAAC,wBAAwB,CAAC;gBACvC2C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEK,MAAM,CAACI,EAAE,CAAC;gBACpCV,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEK,MAAM,CAACE,KAAK,CAAC;gBAE1C,IAAIF,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;kBAC3CT,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEK,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC3C,WAAW,CAAC;kBAEnE,IAAIyC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,EAAE;oBAC5Bb,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAAC;oBAClDP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACuB,OAAO,CAAC,CAACtB,OAAO,EAAEsS,QAAQ,KAAI;sBACrDpT,OAAO,CAACC,GAAG,CAAC,WAAWmT,QAAQ,eAAe,EAAEtS,OAAO,CAACjD,WAAW,CAAC;oBACtE,CAAC,CAAC;;;gBAIN,IAAIyC,MAAM,CAACS,MAAM,IAAIT,MAAM,CAACS,MAAM,CAACN,MAAM,GAAG,CAAC,EAAE;kBAC7CT,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEK,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,CAAC;kBAC7Cf,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,CAACE,uBAAuB,CAAC;;gBAGnFjB,OAAO,CAAC+S,QAAQ,EAAE;;aAErB,MAAM;cACLE,cAAc,CAAC,SAAS,CAAC,EAAE;;UAE/B,CAAC,CAAC;UAEFjT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgT,cAAc,CAAC;UAChDjT,OAAO,CAAC+S,QAAQ,EAAE;UAElB;UACA,IAAI,CAACjO,qBAAqB,EAAE;UAE5B;UACA,IAAI,CAAC1E,eAAe,EAAE;UAEtB;UACA;UAEA;UACA,IAAIkS,QAAQ,CAACpK,IAAI,IAAIoK,QAAQ,CAACpK,IAAI,CAAC9E,OAAO,IAAIkP,QAAQ,CAACpK,IAAI,CAAC9E,OAAO,CAAC3C,MAAM,GAAG,CAAC,EAAE;YAC9ET,OAAO,CAAC3C,KAAK,CAAC,uBAAuB,CAAC;YACtC2C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqS,QAAQ,CAACpK,IAAI,CAAC9E,OAAO,CAAC3C,MAAM,CAAC;YAE3D;YACA,MAAM4S,iBAAiB,GAAGf,QAAQ,CAACpK,IAAI,CAAC9E,OAAO,CAAC7B,MAAM,CAAC+R,CAAC,IAAIA,CAAC,CAACvS,MAAM,IAAIuS,CAAC,CAACvS,MAAM,CAACN,MAAM,GAAG,CAAC,CAAC;YAC5FT,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoT,iBAAiB,CAAC5S,MAAM,CAAC;YAE7D;YACA,MAAM8S,kBAAkB,GAAGF,iBAAiB,CAACG,OAAO,CAACF,CAAC,IAAIA,CAAC,CAACvS,MAAM,CAACnH,GAAG,CAAC6Z,CAAC,IACtEA,CAAC,CAACrP,YAAY,KAAKlC,SAAS,GAAGuR,CAAC,CAACrP,YAAY,GAAIqP,CAAC,CAACpP,QAAQ,GAAGoP,CAAC,CAACpP,QAAQ,CAACC,kBAAkB,GAAG,CAAE,CACjG,CAAC;YACFtE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsT,kBAAkB,CAAC;YAEvD;YACA,MAAMG,kBAAkB,GAAGH,kBAAkB,CAAC9T,MAAM,CAAC,CAACkU,GAAG,EAAEC,GAAG,KAAI;cAChE,IAAIA,GAAG,KAAK1R,SAAS,EAAE;gBACrByR,GAAG,CAACC,GAAG,CAAC,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEhC,OAAOD,GAAG;YACZ,CAAC,EAAE,EAA4B,CAAC;YAChC3T,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEyT,kBAAkB,CAAC;YAEvD;YACA,MAAMG,iBAAiB,GAAGR,iBAAiB,CAAC9R,MAAM,CAAC+R,CAAC,IAClDA,CAAC,CAACvS,MAAM,CAAC+S,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC/F,cAAc,IAAI+F,CAAC,CAAC/F,cAAc,CAACC,UAAU,KAAK,IAAI,CAAC,CAC7E;YACD3N,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE4T,iBAAiB,CAACpT,MAAM,CAAC;YAE5DT,OAAO,CAAC+S,QAAQ,EAAE;;UAGpB;UACA,IAAIT,QAAQ,CAACpK,IAAI,IAAIoK,QAAQ,CAACpK,IAAI,CAACwH,QAAQ,EAAE;YAC3C,IAAI,CAAC5U,YAAY,GAAGwX,QAAQ,CAACpK,IAAI,CAACwH,QAAQ;YAC1C1P,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACnF,YAAY,CAAC;;UAErE;UAAA,KACK,IAAIwX,QAAQ,CAAClK,MAAM,IAAIkK,QAAQ,CAAClK,MAAM,CAAC2L,SAAS,EAAE;YACrD,IAAI,CAACjZ,YAAY,GAAGwX,QAAQ,CAAClK,MAAM,CAAC2L,SAAS;YAC7C/T,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACnF,YAAY,CAAC;;UAExE;UAAA,KACK,IAAIwX,QAAQ,CAACpK,IAAI,IAAIoK,QAAQ,CAACpK,IAAI,CAAC9E,OAAO,IAAIkP,QAAQ,CAACpK,IAAI,CAAC9E,OAAO,CAAC3C,MAAM,GAAG,CAAC,IAAI6R,QAAQ,CAACpK,IAAI,CAAC9E,OAAO,CAAC,CAAC,CAAC,CAAC1C,EAAE,EAAE;YAClH,IAAI,CAAC5F,YAAY,GAAGwX,QAAQ,CAACpK,IAAI,CAAC9E,OAAO,CAAC,CAAC,CAAC,CAAC1C,EAAE;YAC/CV,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACnF,YAAY,CAAC;WAChE,MAAM;YACLkF,OAAO,CAACkQ,KAAK,CAAC,qCAAqC,CAAC;YACpDlQ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEV,MAAM,CAAC8F,IAAI,CAACiN,QAAQ,CAAC,CAAC;YACzD,IAAIA,QAAQ,CAACpK,IAAI,EAAElI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEV,MAAM,CAAC8F,IAAI,CAACiN,QAAQ,CAACpK,IAAI,CAAC,CAAC;YAC7E,IAAIoK,QAAQ,CAAClK,MAAM,EAAEpI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,MAAM,CAAC8F,IAAI,CAACiN,QAAQ,CAAClK,MAAM,CAAC,CAAC;;SAEtF,MAAM;UACL,IAAI,CAACvN,YAAY,GAAG,sDAAsD;UAC1E,IAAIyX,QAAQ,CAAClK,MAAM,CAAC4L,QAAQ,IAAI1B,QAAQ,CAAClK,MAAM,CAAC4L,QAAQ,CAACvT,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAAC5F,YAAY,GAAGyX,QAAQ,CAAClK,MAAM,CAAC4L,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACD/D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzV,SAAS,GAAG,KAAK;QACtB,IAAI,CAACI,YAAY,GAAG,wDAAwD;QAC5EmF,OAAO,CAACkQ,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEA;EACAoB,oBAAoBA,CAAC4C,SAAoB;IACvC3U,MAAM,CAACC,MAAM,CAAC0U,SAAS,CAACC,QAAQ,CAAC,CAAC/R,OAAO,CAACgS,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAY7a,SAAS,EAAE;QAChC,IAAI,CAAC+X,oBAAoB,CAAC8C,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA;EACAjK,cAAcA,CAACmK,OAAe;IAC5B,MAAM/R,KAAK,GAAGR,IAAI,CAAC6K,KAAK,CAAC0H,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMC,IAAI,GAAGD,OAAO,GAAG,EAAE;IACzB,OAAO,GAAG/R,KAAK,KAAKgS,IAAI,KAAK;EAC/B;EAEA;EACAC,UAAUA,CAACC,UAAkB;IAC3B,MAAMhT,IAAI,GAAG,IAAIxE,IAAI,CAACwX,UAAU,CAAC;IACjC,OAAOhT,IAAI,CAACwI,kBAAkB,CAAC,OAAO,EAAE;MACtCyK,OAAO,EAAE,OAAO;MAChBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACdhK,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACAgK,WAAWA,CAACvU,MAAc;IACxB,IAAI,CAACA,MAAM,CAACS,MAAM,IAAIT,MAAM,CAACS,MAAM,CAACN,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd,MAAMqU,QAAQ,GAAGxU,MAAM,CAACS,MAAM,CAACtB,MAAM,CAAC,CAACkF,GAAG,EAAE3D,KAAK,KAC/CA,KAAK,CAACmD,KAAK,CAACU,MAAM,GAAGF,GAAG,CAACR,KAAK,CAACU,MAAM,GAAG7D,KAAK,GAAG2D,GAAG,EAAErE,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,CAAC;IAExE,OAAO+T,QAAQ,CAAC3Q,KAAK,CAAC4Q,eAAe,IAAI,GAAGD,QAAQ,CAAC3Q,KAAK,CAACU,MAAM,IAAIiQ,QAAQ,CAAC3Q,KAAK,CAACnG,QAAQ,EAAE;EAChG;EAEA;EACAgX,iBAAiBA,CAAC1U,MAAc;IAC9B;IACA,IAAI,CAACA,MAAM,CAACS,MAAM,IAAIT,MAAM,CAACS,MAAM,CAACN,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd;IACA,MAAMO,KAAK,GAAGV,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC;IAC9B,MAAMsM,iBAAiB,GAAGrM,KAAK,CAACoD,YAAY,KAAKlC,SAAS,GAAGlB,KAAK,CAACoD,YAAY,GACrDpD,KAAK,CAACqD,QAAQ,GAAGrD,KAAK,CAACqD,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IACjF,OAAO+I,iBAAiB,GAAG,CAAC;EAC9B;EAEA;EACA4H,yBAAyBA,CAAA;IACvB,MAAMC,YAAY,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,CAAC9a,cAAc,CAACiW,kBAAkB,CAAC6E,YAAY,CAAC,CAAC5E,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAAChW,kBAAkB,GAAGgW,SAAS;MACnC;MACA,MAAM4E,KAAK,GAAGvW,QAAQ,CAACwW,cAAc,CAAC,mBAAmB,CAAqB;MAC9E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAC,uBAAuBA,CAAA;IACrB,MAAMN,YAAY,GAAG,CAAC,CAAC,CAAC;IACxB,IAAI,CAAC9a,cAAc,CAACiW,kBAAkB,CAAC6E,YAAY,CAAC,CAAC5E,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAAC/V,gBAAgB,GAAG+V,SAAS;MACjC;MACA,MAAM4E,KAAK,GAAGvW,QAAQ,CAACwW,cAAc,CAAC,iBAAiB,CAAqB;MAC5E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAE,aAAaA,CAAA;IACX,MAAMhY,iBAAiB,GAAG,IAAI,CAACL,UAAU,CAAC6E,GAAG,CAAC,mBAAmB,CAAC,EAAEvG,KAAK;IACzE,MAAMgC,eAAe,GAAG,IAAI,CAACN,UAAU,CAAC6E,GAAG,CAAC,iBAAiB,CAAC,EAAEvG,KAAK;IAErE,IAAI,CAAC0B,UAAU,CAACsY,UAAU,CAAC;MACzBjY,iBAAiB,EAAEC,eAAe;MAClCA,eAAe,EAAED;KAClB,CAAC;EACJ;EAEA;EACAkY,oBAAoBA,CAAClB,UAAkB;IACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B;IACA,MAAMhT,IAAI,GAAG,IAAIxE,IAAI,CAACwX,UAAU,CAAC;IACjC,OAAOhT,IAAI,CAAC+L,cAAc,EAAE;EAC9B;EAEA;EACAoI,kBAAkBA,CAAC5H,WAAmB;IACpC,QAAQA,WAAW;MACjB,KAAK,CAAC;QAAE,OAAO,eAAe;MAC9B,KAAK,CAAC;QAAE,OAAO,iBAAiB;MAChC,KAAK,CAAC;QAAE,OAAO,cAAc;MAC7B;QAAS,OAAO,SAAS;;EAE7B;EAEA;EACA6H,mBAAmBA,CAACjI,mBAA0B,EAAEzO,IAAY;IAC1D,IAAI,CAACyO,mBAAmB,IAAI,CAACkI,KAAK,CAACC,OAAO,CAACnI,mBAAmB,CAAC,EAAE;MAC/D,OAAO,EAAE;;IAEX,OAAOA,mBAAmB,CAACrM,MAAM,CAAC4M,OAAO,IAAIA,OAAO,CAACH,WAAW,KAAK7O,IAAI,CAAC;EAC5E;EAEA;EACA6W,oBAAoBA,CAACC,aAAqB;IACxC,QAAQA,aAAa;MACnB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,WAAW;;EAE/B;EAEA;EACAC,oBAAoBA,CAACC,cAAmB,EAAEC,WAAgB;IACxD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAAC1T,OAAO,IAAI,CAAC0T,cAAc,CAAC1T,OAAO,CAAChB,IAAI,IAC1E,CAAC2U,WAAW,IAAI,CAACA,WAAW,CAAC5U,SAAS,IAAI,CAAC4U,WAAW,CAAC5U,SAAS,CAACC,IAAI,EAAE;MACzE,OAAO,SAAS;;IAGlB,MAAMlF,WAAW,GAAG,IAAIU,IAAI,CAACkZ,cAAc,CAAC1T,OAAO,CAAChB,IAAI,CAAC,CAACE,OAAO,EAAE;IACnE,MAAMzF,aAAa,GAAG,IAAIe,IAAI,CAACmZ,WAAW,CAAC5U,SAAS,CAACC,IAAI,CAAC,CAACE,OAAO,EAAE;IACpE,MAAM0U,MAAM,GAAGna,aAAa,GAAGK,WAAW;IAC1C,MAAM+Z,QAAQ,GAAGvU,IAAI,CAAC6K,KAAK,CAACyJ,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIC,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,KAAK;KACxB,MAAM;MACL,MAAM/T,KAAK,GAAGR,IAAI,CAAC6K,KAAK,CAAC0J,QAAQ,GAAG,EAAE,CAAC;MACvC,MAAM/B,IAAI,GAAG+B,QAAQ,GAAG,EAAE;MAC1B,OAAO,GAAG/T,KAAK,KAAKgS,IAAI,KAAK;;EAEjC;EAEA;EACAgC,iBAAiBA,CAACjW,MAAW;IAC3B;IACA,IAAIA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO,IAAI;;IAGb;IACA;IACA,IAAIH,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAIH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,IAAIP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACJ,MAAM,GAAG,CAAC,EAAE;MAChH,MAAMkS,YAAY,GAAGrS,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;MAChD,MAAM+R,WAAW,GAAGtS,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACJ,MAAM,GAAG,CAAC,CAAC;MAEjF;MACA,IAAIkS,YAAY,IAAIA,YAAY,CAACnR,SAAS,IAAImR,YAAY,CAAClQ,OAAO,IAC9DmQ,WAAW,IAAIA,WAAW,CAACpR,SAAS,IAAIoR,WAAW,CAACnQ,OAAO,EAAE;QAE/D,MAAM+T,cAAc,GAAG7D,YAAY,CAACnR,SAAS,CAACwJ,OAAO,EAAEX,IAAI,IAAIsI,YAAY,CAACnR,SAAS,CAAC0J,IAAI,EAAEtK,IAAI;QAChG,MAAM6V,YAAY,GAAG9D,YAAY,CAAClQ,OAAO,CAACuI,OAAO,EAAEX,IAAI,IAAIsI,YAAY,CAAClQ,OAAO,CAACyI,IAAI,EAAEtK,IAAI;QAC1F,MAAM8V,aAAa,GAAG9D,WAAW,CAACpR,SAAS,CAACwJ,OAAO,EAAEX,IAAI,IAAIuI,WAAW,CAACpR,SAAS,CAAC0J,IAAI,EAAEtK,IAAI;QAC7F,MAAM+V,WAAW,GAAG/D,WAAW,CAACnQ,OAAO,CAACuI,OAAO,EAAEX,IAAI,IAAIuI,WAAW,CAACnQ,OAAO,CAACyI,IAAI,EAAEtK,IAAI;QAEvF;QACA,IAAI4V,cAAc,IAAIG,WAAW,IAAIH,cAAc,KAAKG,WAAW,EAAE;UACnE,OAAO,IAAI;;;;IAKjB;IACA,OAAO,IAAI,CAACzb,iBAAiB,KAAK,IAAI,CAACF,sBAAsB;EAC/D;EAEA;EACA4b,mBAAmBA,CAACtW,MAAW;IAC7B,IAAI,CAACA,MAAM,CAACE,KAAK,EAAE,OAAO,EAAE;IAE5B;IACA,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3B,OAAOH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,IAAI,CAACP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;;IAGtD;IACA,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAIH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,IAAIP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACJ,MAAM,GAAG,CAAC,EAAE;MAChG;MACA,MAAMqB,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC1B,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACJ,MAAM,GAAG,CAAC,CAAC;MACjE,OAAOH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAAC2R,KAAK,CAAC,CAAC,EAAE1Q,UAAU,CAAC;;IAGtD,OAAOxB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,IAAI,CAACP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;EACtD;EAEA;EACAqW,kBAAkBA,CAACvW,MAAW;IAC5B,IAAI,CAACA,MAAM,CAACE,KAAK,EAAE,OAAO,EAAE;IAE5B;IACA,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3B,OAAOH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,IAAI,CAACP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;;IAGtD;IACA,IAAIF,MAAM,CAACE,KAAK,CAACC,MAAM,KAAK,CAAC,IAAIH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,IAAIP,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACJ,MAAM,GAAG,CAAC,EAAE;MAChG;MACA,MAAMqB,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC1B,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAACJ,MAAM,GAAG,CAAC,CAAC;MACjE,OAAOH,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACK,QAAQ,CAAC2R,KAAK,CAAC1Q,UAAU,CAAC;;IAGnD,OAAO,EAAE;EACX;CACD;AAziECgV,UAAA,EADCxd,YAAY,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,0DAS1C;AAzJUW,oBAAoB,GAAA6c,UAAA,EANhC1d,SAAS,CAAC;EACT2d,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,8BAA8B,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,yBAAyB,CAAC;EACrHC,aAAa,EAAE7d,iBAAiB,CAAC8d;CAClC,CAAC,C,EACWld,oBAAoB,CA0rEhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}