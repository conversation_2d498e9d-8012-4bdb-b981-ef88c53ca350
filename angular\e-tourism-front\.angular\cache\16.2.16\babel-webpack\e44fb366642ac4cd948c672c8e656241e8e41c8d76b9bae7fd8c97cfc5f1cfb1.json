{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../navbar/navbar.component\";\nexport class MainLayoutComponent {\n  constructor() {}\n  static {\n    this.ɵfac = function MainLayoutComponent_Factory(t) {\n      return new (t || MainLayoutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MainLayoutComponent,\n      selectors: [[\"app-main-layout\"]],\n      decls: 5,\n      vars: 0,\n      consts: [[1, \"main-layout\"], [1, \"main-content\"], [1, \"content-container\"]],\n      template: function MainLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-navbar\");\n          i0.ɵɵelementStart(2, \"main\", 1)(3, \"div\", 2);\n          i0.ɵɵelement(4, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i2.NavbarComponent],\n      styles: [\".main-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 100vh;\\n  width: 100%;\\n  position: relative;\\n  overflow-x: hidden;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-top: 64px; \\n\\n  transition: transform var(--transition-medium), padding-left var(--transition-medium);\\n}\\n\\n.content-container[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xl) var(--spacing-lg);\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  width: 100%;\\n}\\n\\n\\n\\n.sidebar-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  z-index: 999;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity var(--transition-medium), visibility var(--transition-medium);\\n  -webkit-backdrop-filter: blur(3px);\\n          backdrop-filter: blur(3px);\\n}\\n\\n.sidebar-overlay.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n\\n\\nbody.sidebar-open-mobile[_nghost-%COMP%], body.sidebar-open-mobile   [_nghost-%COMP%] {\\n  overflow: hidden;\\n  height: 100vh;\\n}\\n\\n\\n\\nbody.scrolled[_nghost-%COMP%]   .main-content[_ngcontent-%COMP%], body.scrolled   [_nghost-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n  padding-top: 56px; \\n\\n}\\n\\n\\n\\n@media (min-width: 768px) {\\n  \\n\\n  .sidebar-open[_nghost-%COMP%]   .main-content[_ngcontent-%COMP%], .sidebar-open   [_nghost-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n    padding-left: 280px; \\n\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .content-container[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg) var(--spacing-md);\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .content-container[_ngcontent-%COMP%] {\\n    padding: var(--spacing-md) var(--spacing-sm);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MainLayoutComponent", "constructor", "selectors", "decls", "vars", "consts", "template", "MainLayoutComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\main-layout\\main-layout.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\layout\\main-layout\\main-layout.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-main-layout',\n  templateUrl: './main-layout.component.html',\n  styleUrls: ['./main-layout.component.css']\n})\nexport class MainLayoutComponent {\n  constructor() { }\n}\n", "<div class=\"main-layout\">\n  <!-- Navbar sans événement pour la sidebar -->\n  <app-navbar></app-navbar>\n\n  <!-- Contenu principal -->\n  <main class=\"main-content\">\n    <div class=\"content-container\">\n      <router-outlet></router-outlet>\n    </div>\n  </main>\n</div>\n"], "mappings": ";;;AAOA,OAAM,MAAOA,mBAAmB;EAC9BC,YAAA,GAAgB;;;uBADLD,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPhCE,EAAA,CAAAC,cAAA,aAAyB;UAEvBD,EAAA,CAAAE,SAAA,iBAAyB;UAGzBF,EAAA,CAAAC,cAAA,cAA2B;UAEvBD,EAAA,CAAAE,SAAA,oBAA+B;UACjCF,EAAA,CAAAG,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}