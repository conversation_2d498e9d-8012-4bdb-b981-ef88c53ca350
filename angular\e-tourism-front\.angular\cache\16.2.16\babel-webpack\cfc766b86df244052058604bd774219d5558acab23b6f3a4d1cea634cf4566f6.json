{"ast": null, "code": "import { catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class BookingService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8080/booking';\n  }\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request) {\n    // Ajouter le paramètre action à l'URL\n    return this.http.post(`${this.apiUrl}/booking-transaction?action=${request.action}`, request).pipe(catchError(error => {\n      console.error('Erreur lors de la transaction de réservation:', error);\n      throw error;\n    }));\n  }\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds, currency = 'EUR', culture = 'fr-FR') {\n    const request = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n    return this.bookingTransaction(request);\n  }\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request) {\n    const bookingRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request) {\n    const bookingRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId) {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId) {\n    return {\n      transactionId,\n      paymentOption: 1,\n      paymentInformation: {\n        paymentTypeId: 1,\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n  static {\n    this.ɵfac = function BookingService_Factory(t) {\n      return new (t || BookingService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BookingService,\n      factory: BookingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["catchError", "BookingService", "constructor", "http", "apiUrl", "bookingTransaction", "request", "post", "action", "pipe", "error", "console", "beginTransaction", "offerIds", "currency", "culture", "beginRequest", "setReservationInfo", "bookingRequest", "infoRequest", "commitTransaction", "commitRequest", "createDefaultReservationInfoRequest", "transactionId", "travellers", "reservationNote", "agencyReservationNumber", "createDefaultCommitTransactionRequest", "paymentOption", "paymentInformation", "paymentTypeId", "paymentPrice", "amount", "paymentDate", "Date", "toISOString", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\booking.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport {\n  BookingTransactionRequest,\n  BookingTransactionResponse,\n  BeginTransactionRequest,\n  BeginTransactionResponse,\n  SetReservationInfoRequest,\n  SetReservationInfoResponse,\n  CommitTransactionRequest,\n  CommitTransactionResponse\n} from '../models/booking';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class BookingService {\n  private apiUrl = 'http://localhost:8080/booking';\n\n  constructor(private http: HttpClient) { }\n\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request: BookingTransactionRequest): Observable<BookingTransactionResponse> {\n    // Ajouter le paramètre action à l'URL\n    return this.http.post<BookingTransactionResponse>(\n      `${this.apiUrl}/booking-transaction?action=${request.action}`,\n      request\n    ).pipe(\n      catchError(error => {\n        console.error('Erreur lors de la transaction de réservation:', error);\n        throw error;\n      })\n    );\n  }\n\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds: string[], currency: string = 'EUR', culture: string = 'fr-FR'): Observable<BookingTransactionResponse> {\n    const request: BookingTransactionRequest = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n\n    return this.bookingTransaction(request);\n  }\n\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request: SetReservationInfoRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request: CommitTransactionRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId: string): SetReservationInfoRequest {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId: string): CommitTransactionRequest {\n    return {\n      transactionId,\n      paymentOption: 1, // Option de paiement par défaut\n      paymentInformation: {\n        paymentTypeId: 1, // Type de paiement par défaut\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n}\n"], "mappings": "AAGA,SAASA,UAAU,QAAQ,gBAAgB;;;AAe3C,OAAM,MAAOC,cAAc;EAGzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,+BAA+B;EAER;EAExC;;;;;EAKAC,kBAAkBA,CAACC,OAAkC;IACnD;IACA,OAAO,IAAI,CAACH,IAAI,CAACI,IAAI,CACnB,GAAG,IAAI,CAACH,MAAM,+BAA+BE,OAAO,CAACE,MAAM,EAAE,EAC7DF,OAAO,CACR,CAACG,IAAI,CACJT,UAAU,CAACU,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;EAOAE,gBAAgBA,CAACC,QAAkB,EAAEC,QAAA,GAAmB,KAAK,EAAEC,OAAA,GAAkB,OAAO;IACtF,MAAMT,OAAO,GAA8B;MACzCE,MAAM,EAAE,OAAO;MACfQ,YAAY,EAAE;QACZH,QAAQ;QACRC,QAAQ;QACRC;;KAEH;IAED,OAAO,IAAI,CAACV,kBAAkB,CAACC,OAAO,CAAC;EACzC;EAEA;;;;;EAKAW,kBAAkBA,CAACX,OAAkC;IACnD,MAAMY,cAAc,GAA8B;MAChDV,MAAM,EAAE,MAAM;MACdW,WAAW,EAAEb;KACd;IAED,OAAO,IAAI,CAACD,kBAAkB,CAACa,cAAc,CAAC;EAChD;EAEA;;;;;EAKAE,iBAAiBA,CAACd,OAAiC;IACjD,MAAMY,cAAc,GAA8B;MAChDV,MAAM,EAAE,QAAQ;MAChBa,aAAa,EAAEf;KAChB;IAED,OAAO,IAAI,CAACD,kBAAkB,CAACa,cAAc,CAAC;EAChD;EAEA;;;;;EAKAI,mCAAmCA,CAACC,aAAqB;IACvD,OAAO;MACLA,aAAa;MACbC,UAAU,EAAE,EAAE;MACdC,eAAe,EAAE,EAAE;MACnBC,uBAAuB,EAAE;KAC1B;EACH;EAEA;;;;;EAKAC,qCAAqCA,CAACJ,aAAqB;IACzD,OAAO;MACLA,aAAa;MACbK,aAAa,EAAE,CAAC;MAChBC,kBAAkB,EAAE;QAClBC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;UACZC,MAAM,EAAE,CAAC;UACTlB,QAAQ,EAAE;SACX;QACDmB,WAAW,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;;KAEtC;EACH;;;uBAvGWlC,cAAc,EAAAmC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdtC,cAAc;MAAAuC,OAAA,EAAdvC,cAAc,CAAAwC,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}