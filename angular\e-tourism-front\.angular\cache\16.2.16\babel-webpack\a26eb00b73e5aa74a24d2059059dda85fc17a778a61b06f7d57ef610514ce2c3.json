{"ast": null, "code": "import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction LoginComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction LoginComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"div\", 43);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(authService, router, snackBar) {\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.credentials = {\n      Agency: '',\n      User: '',\n      Password: ''\n    };\n    this.loading = false;\n    this.error = '';\n    this.hidePassword = true;\n  }\n  ngOnInit() {\n    // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n    this.authService.logout();\n    // Animation d'entrée\n    document.body.classList.add('login-page-active');\n  }\n  ngOnDestroy() {\n    // Nettoyage de la classe lors de la destruction du composant\n    document.body.classList.remove('login-page-active');\n  }\n  onSubmit() {\n    if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n      this.error = 'Veuillez remplir tous les champs requis.';\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    this.authService.login(this.credentials).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.header.success) {\n          this.snackBar.open('Connexion réussie! Bienvenue.', 'Fermer', {\n            duration: 3000,\n            panelClass: 'success-snackbar',\n            verticalPosition: 'top'\n          });\n          this.router.navigate(['/search-price']);\n        } else {\n          this.error = 'Échec de l\\'authentification. Veuillez vérifier vos identifiants.';\n        }\n      },\n      error: err => {\n        this.loading = false;\n        this.error = 'Une erreur s\\'est produite lors de la connexion. Veuillez réessayer.';\n        console.error('Login error:', err);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 58,\n      vars: 10,\n      consts: [[1, \"login-page\"], [1, \"login-background\"], [1, \"login-shapes\"], [1, \"shape\", \"shape-1\"], [1, \"shape\", \"shape-2\"], [1, \"shape\", \"shape-3\"], [1, \"shape\", \"shape-4\"], [1, \"shape\", \"shape-5\"], [1, \"login-illustration\"], [\"src\", \"assets/images/login-illustration.svg\", \"alt\", \"Travel Illustration\"], [1, \"login-container\"], [1, \"login-card\", \"animate-slide-up\"], [1, \"login-logo\"], [1, \"logo-icon\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"login-header\"], [1, \"login-form\", 3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"agency\"], [1, \"input-with-icon\"], [1, \"fas\", \"fa-building\"], [\"type\", \"text\", \"id\", \"agency\", \"name\", \"agency\", \"required\", \"\", \"placeholder\", \"Entrez le nom de votre agence\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"input-focus-border\"], [\"for\", \"username\"], [1, \"fas\", \"fa-user\"], [\"type\", \"text\", \"id\", \"username\", \"name\", \"username\", \"required\", \"\", \"placeholder\", \"Entrez votre nom d'utilisateur\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [1, \"fas\", \"fa-lock\"], [\"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"placeholder\", \"Entrez votre mot de passe\", 1, \"form-control\", 3, \"type\", \"ngModel\", \"ngModelChange\"], [\"type\", \"button\", \"aria-label\", \"Afficher/masquer le mot de passe\", 1, \"password-toggle\", 3, \"title\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"login-footer\"], [1, \"footer-decoration\"], [1, \"decoration-line\"], [1, \"fas\", \"fa-globe-americas\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"spinner-container\"], [1, \"spinner\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8);\n          i0.ɵɵelement(9, \"img\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13);\n          i0.ɵɵelement(14, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"h1\");\n          i0.ɵɵtext(16, \"E-Tourism\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 15)(18, \"h2\");\n          i0.ɵɵtext(19, \"Bienvenue\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\");\n          i0.ɵɵtext(21, \"Connectez-vous pour acc\\u00E9der \\u00E0 votre compte\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"form\", 16, 17);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_22_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(24, \"div\", 18)(25, \"label\", 19);\n          i0.ɵɵtext(26, \"Agence\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 20);\n          i0.ɵɵelement(28, \"i\", 21);\n          i0.ɵɵelementStart(29, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_29_listener($event) {\n            return ctx.credentials.Agency = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"div\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 18)(32, \"label\", 24);\n          i0.ɵɵtext(33, \"Nom d'utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 20);\n          i0.ɵɵelement(35, \"i\", 25);\n          i0.ɵɵelementStart(36, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_36_listener($event) {\n            return ctx.credentials.User = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"div\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 18)(39, \"label\", 27);\n          i0.ɵɵtext(40, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 20);\n          i0.ɵɵelement(42, \"i\", 28);\n          i0.ɵɵelementStart(43, \"input\", 29);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_43_listener($event) {\n            return ctx.credentials.Password = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_44_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelement(45, \"i\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"div\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(47, LoginComponent_div_47_Template, 4, 1, \"div\", 32);\n          i0.ɵɵelementStart(48, \"button\", 33);\n          i0.ɵɵtemplate(49, LoginComponent_div_49_Template, 2, 0, \"div\", 34);\n          i0.ɵɵtemplate(50, LoginComponent_span_50_Template, 2, 0, \"span\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 36)(52, \"div\", 37);\n          i0.ɵɵelement(53, \"div\", 38)(54, \"i\", 39)(55, \"div\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\");\n          i0.ɵɵtext(57, \"\\u00A9 2023 E-Tourism. Tous droits r\\u00E9serv\\u00E9s.\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(23);\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngModel\", ctx.credentials.Agency);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.credentials.User);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\")(\"ngModel\", ctx.credentials.Password);\n          i0.ɵɵadvance(1);\n          i0.ɵɵpropertyInterpolate(\"title\", ctx.hidePassword ? \"Afficher le mot de passe\" : \"Masquer le mot de passe\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", ctx.hidePassword ? \"fa-eye\" : \"fa-eye-slash\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !_r0.form.valid || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm],\n      styles: [\".login-page[_ngcontent-%COMP%] {\\n  position: relative;\\n  min-height: 100vh;\\n  width: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.login-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, var(--ocean-blue) 0%, var(--primary-color) 60%, var(--sky-blue) 100%);\\n  z-index: -1;\\n}\\n\\n.login-shapes[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.shape[_ngcontent-%COMP%] {\\n  position: absolute;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 50%;\\n}\\n\\n.shape-1[_ngcontent-%COMP%] {\\n  width: 600px;\\n  height: 600px;\\n  top: -300px;\\n  right: -200px;\\n  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 70%, transparent 100%);\\n  animation: _ngcontent-%COMP%_float 20s infinite alternate ease-in-out;\\n}\\n\\n.shape-2[_ngcontent-%COMP%] {\\n  width: 400px;\\n  height: 400px;\\n  bottom: -200px;\\n  left: -100px;\\n  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 70%, transparent 100%);\\n  animation: _ngcontent-%COMP%_float 25s infinite alternate-reverse ease-in-out;\\n}\\n\\n.shape-3[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: 300px;\\n  top: 20%;\\n  right: 10%;\\n  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 70%, transparent 100%);\\n  animation: _ngcontent-%COMP%_pulse 15s infinite alternate ease-in-out;\\n}\\n\\n.shape-4[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  bottom: 15%;\\n  right: 20%;\\n  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 70%, transparent 100%);\\n  animation: _ngcontent-%COMP%_float 18s infinite alternate-reverse ease-in-out;\\n}\\n\\n.shape-5[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  top: 30%;\\n  left: 15%;\\n  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 70%, transparent 100%);\\n  animation: _ngcontent-%COMP%_pulse 12s infinite alternate ease-in-out;\\n}\\n\\n.login-illustration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 5%;\\n  right: 5%;\\n  width: 40%;\\n  max-width: 500px;\\n  opacity: 0.9;\\n  animation: _ngcontent-%COMP%_float 6s infinite alternate ease-in-out;\\n  display: none; \\n\\n}\\n\\n.login-illustration[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0% {\\n    transform: translateY(0) rotate(0deg);\\n  }\\n  100% {\\n    transform: translateY(20px) rotate(3deg);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n    opacity: 0.5;\\n  }\\n  100% {\\n    transform: scale(1.2);\\n    opacity: 0.2;\\n  }\\n}\\n\\n.login-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  width: 100%;\\n  padding: var(--spacing-md);\\n  z-index: 1;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 450px;\\n  background-color: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: var(--border-radius-large);\\n  box-shadow: var(--elevation-4), 0 10px 30px rgba(0, 0, 0, 0.1);\\n  padding: var(--spacing-xl);\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_cardAppear 0.8s var(--transition-bounce) forwards;\\n}\\n\\n@keyframes _ngcontent-%COMP%_cardAppear {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(30px) scale(0.95);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n\\n.login-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin-bottom: var(--spacing-lg);\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 80px;\\n  height: 80px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\\n  border-radius: 50%;\\n  margin-bottom: var(--spacing-md);\\n  box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.3);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  color: white;\\n}\\n\\n.login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.75rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, var(--primary-dark), var(--accent-color));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  margin: 0;\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: var(--spacing-lg);\\n}\\n\\n.login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: var(--text-primary);\\n  margin-bottom: var(--spacing-xs);\\n  font-size: 1.75rem;\\n  font-weight: 600;\\n}\\n\\n.login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-md);\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-md);\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: var(--spacing-xs);\\n  font-weight: 500;\\n  color: var(--text-primary);\\n  font-size: 0.9rem;\\n  transition: color var(--transition-fast);\\n}\\n\\n.input-with-icon[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.input-with-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 16px;\\n  color: var(--primary-color);\\n  font-size: 1rem;\\n  transition: all var(--transition-fast);\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px 14px 14px 45px;\\n  border: 1px solid var(--divider-color);\\n  border-radius: var(--border-radius-medium);\\n  font-size: 1rem;\\n  transition: all var(--transition-fast);\\n  background-color: var(--surface-color);\\n  color: var(--text-primary);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);\\n}\\n\\ninput[type=\\\"password\\\"].form-control[_ngcontent-%COMP%], input[type=\\\"text\\\"].form-control[name=\\\"password\\\"][_ngcontent-%COMP%] {\\n  padding-right: 45px; \\n\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus    + .input-focus-border[_ngcontent-%COMP%] {\\n  transform: scaleX(1);\\n}\\n\\n.input-focus-border[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 2px;\\n  background: linear-gradient(to right, var(--primary-color), var(--accent-color));\\n  transform: scaleX(0);\\n  transform-origin: left;\\n  transition: transform var(--transition-medium);\\n}\\n\\n.form-control[_ngcontent-%COMP%]::placeholder {\\n  color: var(--text-hint);\\n}\\n\\n.input-with-icon[_ngcontent-%COMP%]:focus-within   i[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n}\\n\\n.password-toggle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 12px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  background: none;\\n  border: none;\\n  color: var(--text-secondary);\\n  cursor: pointer;\\n  padding: 8px;\\n  font-size: 1rem;\\n  transition: all var(--transition-fast);\\n  border-radius: 50%;\\n  z-index: 2;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 36px;\\n  width: 36px;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  color: var(--error-color);\\n  background-color: rgba(244, 67, 54, 0.1);\\n  padding: var(--spacing-sm) var(--spacing-md);\\n  border-radius: var(--border-radius-medium);\\n  margin-bottom: var(--spacing-sm);\\n  animation: _ngcontent-%COMP%_shake 0.5s cubic-bezier(.36,.07,.19,.97) both;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shake {\\n  10%, 90% { transform: translate3d(-1px, 0, 0); }\\n  20%, 80% { transform: translate3d(2px, 0, 0); }\\n  30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }\\n  40%, 60% { transform: translate3d(3px, 0, 0); }\\n}\\n\\n.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 50px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  margin-top: var(--spacing-md);\\n  position: relative;\\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\\n  color: white;\\n  border: none;\\n  border-radius: var(--border-radius-medium);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  overflow: hidden;\\n  box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.login-button[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: all 0.6s;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled)::before {\\n  left: 100%;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(var(--primary-color-rgb), 0.4);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(1px);\\n  box-shadow: 0 2px 10px rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  background: linear-gradient(135deg, #ccc, #ddd);\\n  cursor: not-allowed;\\n  box-shadow: none;\\n}\\n\\n.spinner-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  border-top-color: white;\\n  animation: _ngcontent-%COMP%_spin 0.8s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n.login-footer[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-xl);\\n  text-align: center;\\n}\\n\\n.footer-decoration[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.decoration-line[_ngcontent-%COMP%] {\\n  height: 1px;\\n  width: 60px;\\n  background: linear-gradient(to right, transparent, var(--divider-color));\\n}\\n\\n.decoration-line[_ngcontent-%COMP%]:last-child {\\n  background: linear-gradient(to left, transparent, var(--divider-color));\\n}\\n\\n.footer-decoration[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 1.2rem;\\n  margin: 0 var(--spacing-sm);\\n}\\n\\n.login-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-hint);\\n  font-size: 0.75rem;\\n  margin: 0;\\n}\\n\\n\\n\\n@media (min-width: 768px) {\\n  .login-illustration[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n\\n  .login-container[_ngcontent-%COMP%] {\\n    margin-left: -10%;\\n  }\\n}\\n\\n@media (max-width: 767px) {\\n  .login-card[_ngcontent-%COMP%] {\\n    max-width: 90%;\\n  }\\n}\\n\\n@media (max-width: 500px) {\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg);\\n  }\\n\\n  .logo-icon[_ngcontent-%COMP%] {\\n    width: 70px;\\n    height: 70px;\\n  }\\n\\n  .logo-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .form-control[_ngcontent-%COMP%] {\\n    padding: 12px 12px 12px 40px;\\n  }\\n}\\n\\n\\n\\nbody.login-page-active[_nghost-%COMP%], body.login-page-active   [_nghost-%COMP%] {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      data: {\n        animation: [trigger('fadeInOut', [transition(':enter', [style({\n          opacity: 0\n        }), animate('0.5s ease-in-out', style({\n          opacity: 1\n        }))]), transition(':leave', [animate('0.5s ease-in-out', style({\n          opacity: 0\n        }))])])]\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["trigger", "transition", "style", "animate", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "LoginComponent", "constructor", "authService", "router", "snackBar", "credentials", "Agency", "User", "Password", "loading", "hidePassword", "ngOnInit", "logout", "document", "body", "classList", "add", "ngOnDestroy", "remove", "onSubmit", "login", "subscribe", "next", "response", "header", "success", "open", "duration", "panelClass", "verticalPosition", "navigate", "err", "console", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_22_listener", "LoginComponent_Template_input_ngModelChange_29_listener", "$event", "LoginComponent_Template_input_ngModelChange_36_listener", "LoginComponent_Template_input_ngModelChange_43_listener", "LoginComponent_Template_button_click_44_listener", "ɵɵtemplate", "LoginComponent_div_47_Template", "LoginComponent_div_49_Template", "LoginComponent_span_50_Template", "ɵɵproperty", "ɵɵpropertyInterpolate", "_r0", "form", "valid", "opacity"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { AuthRequest } from '../../models/auth-request.model';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { trigger, transition, style, animate } from '@angular/animations';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css'],\n  animations: [\n    trigger('fadeInOut', [\n      transition(':enter', [\n        style({ opacity: 0 }),\n        animate('0.5s ease-in-out', style({ opacity: 1 }))\n      ]),\n      transition(':leave', [\n        animate('0.5s ease-in-out', style({ opacity: 0 }))\n      ])\n    ])\n  ]\n})\nexport class LoginComponent implements OnInit, OnDestroy {\n  credentials: AuthRequest = {\n    Agency: '',\n    User: '',\n    Password: ''\n  };\n\n  loading = false;\n  error = '';\n  hidePassword = true;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) { }\n\n  ngOnInit(): void {\n    // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n    this.authService.logout();\n\n    // Animation d'entrée\n    document.body.classList.add('login-page-active');\n  }\n\n  ngOnDestroy(): void {\n    // Nettoyage de la classe lors de la destruction du composant\n    document.body.classList.remove('login-page-active');\n  }\n\n  onSubmit(): void {\n    if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n      this.error = 'Veuillez remplir tous les champs requis.';\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    this.authService.login(this.credentials)\n      .subscribe({\n        next: (response) => {\n          this.loading = false;\n          if (response.header.success) {\n            this.snackBar.open('Connexion réussie! Bienvenue.', 'Fermer', {\n              duration: 3000,\n              panelClass: 'success-snackbar',\n              verticalPosition: 'top'\n            });\n            this.router.navigate(['/search-price']);\n          } else {\n            this.error = 'Échec de l\\'authentification. Veuillez vérifier vos identifiants.';\n          }\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = 'Une erreur s\\'est produite lors de la connexion. Veuillez réessayer.';\n          console.error('Login error:', err);\n        }\n      });\n  }\n}\n", "<div class=\"login-page\">\n  <div class=\"login-background\">\n    <div class=\"login-shapes\">\n      <div class=\"shape shape-1\"></div>\n      <div class=\"shape shape-2\"></div>\n      <div class=\"shape shape-3\"></div>\n      <div class=\"shape shape-4\"></div>\n      <div class=\"shape shape-5\"></div>\n    </div>\n    <div class=\"login-illustration\">\n      <img src=\"assets/images/login-illustration.svg\" alt=\"Travel Illustration\">\n    </div>\n  </div>\n\n  <div class=\"login-container\">\n    <div class=\"login-card animate-slide-up\">\n      <div class=\"login-logo\">\n        <div class=\"logo-icon\">\n          <i class=\"fas fa-plane-departure\"></i>\n        </div>\n        <h1>E-Tourism</h1>\n      </div>\n\n      <div class=\"login-header\">\n        <h2>Bienvenue</h2>\n        <p>Connectez-vous pour accéder à votre compte</p>\n      </div>\n\n      <form (ngSubmit)=\"onSubmit()\" #loginForm=\"ngForm\" class=\"login-form\">\n        <div class=\"form-group\">\n          <label for=\"agency\">Agence</label>\n          <div class=\"input-with-icon\">\n            <i class=\"fas fa-building\"></i>\n            <input\n              type=\"text\"\n              id=\"agency\"\n              name=\"agency\"\n              [(ngModel)]=\"credentials.Agency\"\n              required\n              class=\"form-control\"\n              placeholder=\"Entrez le nom de votre agence\">\n            <div class=\"input-focus-border\"></div>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"username\">Nom d'utilisateur</label>\n          <div class=\"input-with-icon\">\n            <i class=\"fas fa-user\"></i>\n            <input\n              type=\"text\"\n              id=\"username\"\n              name=\"username\"\n              [(ngModel)]=\"credentials.User\"\n              required\n              class=\"form-control\"\n              placeholder=\"Entrez votre nom d'utilisateur\">\n            <div class=\"input-focus-border\"></div>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"password\">Mot de passe</label>\n          <div class=\"input-with-icon\">\n            <i class=\"fas fa-lock\"></i>\n            <input\n              [type]=\"hidePassword ? 'password' : 'text'\"\n              id=\"password\"\n              name=\"password\"\n              [(ngModel)]=\"credentials.Password\"\n              required\n              class=\"form-control\"\n              placeholder=\"Entrez votre mot de passe\">\n            <button\n              type=\"button\"\n              class=\"password-toggle\"\n              (click)=\"hidePassword = !hidePassword\"\n              aria-label=\"Afficher/masquer le mot de passe\"\n              title=\"{{ hidePassword ? 'Afficher le mot de passe' : 'Masquer le mot de passe' }}\">\n              <i class=\"fas\" [ngClass]=\"hidePassword ? 'fa-eye' : 'fa-eye-slash'\"></i>\n            </button>\n            <div class=\"input-focus-border\"></div>\n          </div>\n        </div>\n\n        <div *ngIf=\"error\" class=\"error-message\">\n          <i class=\"fas fa-exclamation-circle\"></i>\n          <span>{{ error }}</span>\n        </div>\n\n        <button\n          type=\"submit\"\n          [disabled]=\"!loginForm.form.valid || loading\"\n          class=\"login-button\">\n          <div *ngIf=\"loading\" class=\"spinner-container\">\n            <div class=\"spinner\"></div>\n          </div>\n          <span *ngIf=\"!loading\">Se connecter</span>\n        </button>\n      </form>\n\n      <div class=\"login-footer\">\n        <div class=\"footer-decoration\">\n          <div class=\"decoration-line\"></div>\n          <i class=\"fas fa-globe-americas\"></i>\n          <div class=\"decoration-line\"></div>\n        </div>\n        <p>© 2023 E-Tourism. Tous droits réservés.</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAKA,SAASA,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;;;;;;;;;ICgFjEC,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAAE,SAAA,YAAyC;IACzCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAlBJ,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAOjBR,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;AD1EpD,OAAM,MAAOK,cAAc;EAWzBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAblB,KAAAC,WAAW,GAAgB;MACzBC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE;KACX;IAED,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAV,KAAK,GAAG,EAAE;IACV,KAAAW,YAAY,GAAG,IAAI;EAMf;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACT,WAAW,CAACU,MAAM,EAAE;IAEzB;IACAC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAClD;EAEAC,WAAWA,CAAA;IACT;IACAJ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACG,MAAM,CAAC,mBAAmB,CAAC;EACrD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACd,WAAW,CAACC,MAAM,IAAI,CAAC,IAAI,CAACD,WAAW,CAACE,IAAI,IAAI,CAAC,IAAI,CAACF,WAAW,CAACG,QAAQ,EAAE;MACpF,IAAI,CAACT,KAAK,GAAG,0CAA0C;MACvD;;IAGF,IAAI,CAACU,OAAO,GAAG,IAAI;IACnB,IAAI,CAACV,KAAK,GAAG,EAAE;IAEf,IAAI,CAACG,WAAW,CAACkB,KAAK,CAAC,IAAI,CAACf,WAAW,CAAC,CACrCgB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACd,OAAO,GAAG,KAAK;QACpB,IAAIc,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;UAC3B,IAAI,CAACrB,QAAQ,CAACsB,IAAI,CAAC,+BAA+B,EAAE,QAAQ,EAAE;YAC5DC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,kBAAkB;YAC9BC,gBAAgB,EAAE;WACnB,CAAC;UACF,IAAI,CAAC1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;SACxC,MAAM;UACL,IAAI,CAAC/B,KAAK,GAAG,mEAAmE;;MAEpF,CAAC;MACDA,KAAK,EAAGgC,GAAG,IAAI;QACb,IAAI,CAACtB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACV,KAAK,GAAG,sEAAsE;QACnFiC,OAAO,CAACjC,KAAK,CAAC,cAAc,EAAEgC,GAAG,CAAC;MACpC;KACD,CAAC;EACN;;;uBA5DW/B,cAAc,EAAAT,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAA0C,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAA0C,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAdvC,cAAc;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvB3BvD,EAAA,CAAAC,cAAA,aAAwB;UAGlBD,EAAA,CAAAE,SAAA,aAAiC;UAKnCF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,aAAgC;UAC9BD,EAAA,CAAAE,SAAA,aAA0E;UAC5EF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAC,cAAA,eAA6B;UAIrBD,EAAA,CAAAE,SAAA,aAAsC;UACxCF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGpBJ,EAAA,CAAAC,cAAA,eAA0B;UACpBD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,4DAA0C;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAGnDJ,EAAA,CAAAC,cAAA,oBAAqE;UAA/DD,EAAA,CAAAyD,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAC3B5B,EAAA,CAAAC,cAAA,eAAwB;UACFD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAClCJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,SAAA,aAA+B;UAC/BF,EAAA,CAAAC,cAAA,iBAO8C;UAH5CD,EAAA,CAAAyD,UAAA,2BAAAE,wDAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAC,MAAA,GAAA6C,MAAA;UAAA,EAAgC;UAJlC5D,EAAA,CAAAI,YAAA,EAO8C;UAC9CJ,EAAA,CAAAE,SAAA,eAAsC;UACxCF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAG,MAAA,yBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC/CJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,SAAA,aAA2B;UAC3BF,EAAA,CAAAC,cAAA,iBAO+C;UAH7CD,EAAA,CAAAyD,UAAA,2BAAAI,wDAAAD,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAE,IAAA,GAAA4C,MAAA;UAAA,EAA8B;UAJhC5D,EAAA,CAAAI,YAAA,EAO+C;UAC/CJ,EAAA,CAAAE,SAAA,eAAsC;UACxCF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC1CJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,SAAA,aAA2B;UAC3BF,EAAA,CAAAC,cAAA,iBAO0C;UAHxCD,EAAA,CAAAyD,UAAA,2BAAAK,wDAAAF,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAG,QAAA,GAAA2C,MAAA;UAAA,EAAkC;UAJpC5D,EAAA,CAAAI,YAAA,EAO0C;UAC1CJ,EAAA,CAAAC,cAAA,kBAKsF;UAFpFD,EAAA,CAAAyD,UAAA,mBAAAM,iDAAA;YAAA,OAAAP,GAAA,CAAArC,YAAA,IAAAqC,GAAA,CAAArC,YAAA;UAAA,EAAsC;UAGtCnB,EAAA,CAAAE,SAAA,aAAwE;UAC1EF,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,SAAA,eAAsC;UACxCF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAgE,UAAA,KAAAC,8BAAA,kBAGM;UAENjE,EAAA,CAAAC,cAAA,kBAGuB;UACrBD,EAAA,CAAAgE,UAAA,KAAAE,8BAAA,kBAEM;UACNlE,EAAA,CAAAgE,UAAA,KAAAG,+BAAA,mBAA0C;UAC5CnE,EAAA,CAAAI,YAAA,EAAS;UAGXJ,EAAA,CAAAC,cAAA,eAA0B;UAEtBD,EAAA,CAAAE,SAAA,eAAmC;UAGrCF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,8DAAuC;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;;UAtExCJ,EAAA,CAAAK,SAAA,IAAgC;UAAhCL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAA1C,WAAA,CAAAC,MAAA,CAAgC;UAgBhCf,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAA1C,WAAA,CAAAE,IAAA,CAA8B;UAa9BhB,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAArC,YAAA,uBAA2C,YAAAqC,GAAA,CAAA1C,WAAA,CAAAG,QAAA;UAY3CjB,EAAA,CAAAK,SAAA,GAAmF;UAAnFL,EAAA,CAAAqE,qBAAA,UAAAb,GAAA,CAAArC,YAAA,0DAAmF;UACpEnB,EAAA,CAAAK,SAAA,GAAoD;UAApDL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAArC,YAAA,6BAAoD;UAMnEnB,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAAhD,KAAA,CAAW;UAOfR,EAAA,CAAAK,SAAA,GAA6C;UAA7CL,EAAA,CAAAoE,UAAA,cAAAE,GAAA,CAAAC,IAAA,CAAAC,KAAA,IAAAhB,GAAA,CAAAtC,OAAA,CAA6C;UAEvClB,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAAtC,OAAA,CAAa;UAGZlB,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAoE,UAAA,UAAAZ,GAAA,CAAAtC,OAAA,CAAc;;;;;;mBDtFjB,CACVtB,OAAO,CAAC,WAAW,EAAE,CACnBC,UAAU,CAAC,QAAQ,EAAE,CACnBC,KAAK,CAAC;UAAE2E,OAAO,EAAE;QAAC,CAAE,CAAC,EACrB1E,OAAO,CAAC,kBAAkB,EAAED,KAAK,CAAC;UAAE2E,OAAO,EAAE;QAAC,CAAE,CAAC,CAAC,CACnD,CAAC,EACF5E,UAAU,CAAC,QAAQ,EAAE,CACnBE,OAAO,CAAC,kBAAkB,EAAED,KAAK,CAAC;UAAE2E,OAAO,EAAE;QAAC,CAAE,CAAC,CAAC,CACnD,CAAC,CACH,CAAC;MACH;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}