<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200px" height="600px" viewBox="0 0 1200 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Features Background</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F8F9FA" offset="0%"></stop>
            <stop stop-color="#E0F2FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#0071C2" stop-opacity="0.05" offset="0%"></stop>
            <stop stop-color="#0071C2" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Features-Background" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="Background" fill="url(#linearGradient-1)" x="0" y="0" width="1200" height="600"></rect>
        
        <!-- Decorative Patterns -->
        <g id="Patterns" opacity="0.5">
            <path d="M0,300 C100,250 200,280 300,230 C400,180 500,210 600,160 C700,110 800,140 900,90 C1000,40 1100,70 1200,20 L1200,600 L0,600 L0,300 Z" id="Wave-1" fill="url(#linearGradient-2)"></path>
            
            <g id="Dots" fill="#0071C2" opacity="0.1">
                <circle cx="100" cy="100" r="3"></circle>
                <circle cx="150" cy="150" r="3"></circle>
                <circle cx="200" cy="100" r="3"></circle>
                <circle cx="250" cy="150" r="3"></circle>
                <circle cx="300" cy="100" r="3"></circle>
                <circle cx="350" cy="150" r="3"></circle>
                <circle cx="400" cy="100" r="3"></circle>
                <circle cx="450" cy="150" r="3"></circle>
                <circle cx="500" cy="100" r="3"></circle>
                <circle cx="550" cy="150" r="3"></circle>
                <circle cx="600" cy="100" r="3"></circle>
                <circle cx="650" cy="150" r="3"></circle>
                <circle cx="700" cy="100" r="3"></circle>
                <circle cx="750" cy="150" r="3"></circle>
                <circle cx="800" cy="100" r="3"></circle>
                <circle cx="850" cy="150" r="3"></circle>
                <circle cx="900" cy="100" r="3"></circle>
                <circle cx="950" cy="150" r="3"></circle>
                <circle cx="1000" cy="100" r="3"></circle>
                <circle cx="1050" cy="150" r="3"></circle>
                <circle cx="1100" cy="100" r="3"></circle>
                <circle cx="1150" cy="150" r="3"></circle>
                
                <circle cx="100" cy="200" r="3"></circle>
                <circle cx="150" cy="250" r="3"></circle>
                <circle cx="200" cy="200" r="3"></circle>
                <circle cx="250" cy="250" r="3"></circle>
                <circle cx="300" cy="200" r="3"></circle>
                <circle cx="350" cy="250" r="3"></circle>
                <circle cx="400" cy="200" r="3"></circle>
                <circle cx="450" cy="250" r="3"></circle>
                <circle cx="500" cy="200" r="3"></circle>
                <circle cx="550" cy="250" r="3"></circle>
                <circle cx="600" cy="200" r="3"></circle>
                <circle cx="650" cy="250" r="3"></circle>
                <circle cx="700" cy="200" r="3"></circle>
                <circle cx="750" cy="250" r="3"></circle>
                <circle cx="800" cy="200" r="3"></circle>
                <circle cx="850" cy="250" r="3"></circle>
                <circle cx="900" cy="200" r="3"></circle>
                <circle cx="950" cy="250" r="3"></circle>
                <circle cx="1000" cy="200" r="3"></circle>
                <circle cx="1050" cy="250" r="3"></circle>
                <circle cx="1100" cy="200" r="3"></circle>
                <circle cx="1150" cy="250" r="3"></circle>
                
                <circle cx="100" cy="300" r="3"></circle>
                <circle cx="150" cy="350" r="3"></circle>
                <circle cx="200" cy="300" r="3"></circle>
                <circle cx="250" cy="350" r="3"></circle>
                <circle cx="300" cy="300" r="3"></circle>
                <circle cx="350" cy="350" r="3"></circle>
                <circle cx="400" cy="300" r="3"></circle>
                <circle cx="450" cy="350" r="3"></circle>
                <circle cx="500" cy="300" r="3"></circle>
                <circle cx="550" cy="350" r="3"></circle>
                <circle cx="600" cy="300" r="3"></circle>
                <circle cx="650" cy="350" r="3"></circle>
                <circle cx="700" cy="300" r="3"></circle>
                <circle cx="750" cy="350" r="3"></circle>
                <circle cx="800" cy="300" r="3"></circle>
                <circle cx="850" cy="350" r="3"></circle>
                <circle cx="900" cy="300" r="3"></circle>
                <circle cx="950" cy="350" r="3"></circle>
                <circle cx="1000" cy="300" r="3"></circle>
                <circle cx="1050" cy="350" r="3"></circle>
                <circle cx="1100" cy="300" r="3"></circle>
                <circle cx="1150" cy="350" r="3"></circle>
                
                <circle cx="100" cy="400" r="3"></circle>
                <circle cx="150" cy="450" r="3"></circle>
                <circle cx="200" cy="400" r="3"></circle>
                <circle cx="250" cy="450" r="3"></circle>
                <circle cx="300" cy="400" r="3"></circle>
                <circle cx="350" cy="450" r="3"></circle>
                <circle cx="400" cy="400" r="3"></circle>
                <circle cx="450" cy="450" r="3"></circle>
                <circle cx="500" cy="400" r="3"></circle>
                <circle cx="550" cy="450" r="3"></circle>
                <circle cx="600" cy="400" r="3"></circle>
                <circle cx="650" cy="450" r="3"></circle>
                <circle cx="700" cy="400" r="3"></circle>
                <circle cx="750" cy="450" r="3"></circle>
                <circle cx="800" cy="400" r="3"></circle>
                <circle cx="850" cy="450" r="3"></circle>
                <circle cx="900" cy="400" r="3"></circle>
                <circle cx="950" cy="450" r="3"></circle>
                <circle cx="1000" cy="400" r="3"></circle>
                <circle cx="1050" cy="450" r="3"></circle>
                <circle cx="1100" cy="400" r="3"></circle>
                <circle cx="1150" cy="450" r="3"></circle>
                
                <circle cx="100" cy="500" r="3"></circle>
                <circle cx="150" cy="550" r="3"></circle>
                <circle cx="200" cy="500" r="3"></circle>
                <circle cx="250" cy="550" r="3"></circle>
                <circle cx="300" cy="500" r="3"></circle>
                <circle cx="350" cy="550" r="3"></circle>
                <circle cx="400" cy="500" r="3"></circle>
                <circle cx="450" cy="550" r="3"></circle>
                <circle cx="500" cy="500" r="3"></circle>
                <circle cx="550" cy="550" r="3"></circle>
                <circle cx="600" cy="500" r="3"></circle>
                <circle cx="650" cy="550" r="3"></circle>
                <circle cx="700" cy="500" r="3"></circle>
                <circle cx="750" cy="550" r="3"></circle>
                <circle cx="800" cy="500" r="3"></circle>
                <circle cx="850" cy="550" r="3"></circle>
                <circle cx="900" cy="500" r="3"></circle>
                <circle cx="950" cy="550" r="3"></circle>
                <circle cx="1000" cy="500" r="3"></circle>
                <circle cx="1050" cy="550" r="3"></circle>
                <circle cx="1100" cy="500" r="3"></circle>
                <circle cx="1150" cy="550" r="3"></circle>
            </g>
        </g>
    </g>
</svg>
