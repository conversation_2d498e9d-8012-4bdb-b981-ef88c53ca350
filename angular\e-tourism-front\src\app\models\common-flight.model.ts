// Interface commune pour les vols (one-way, round-trip, multi-city)

// Interface pour les segments
export interface Segment {
  id?: string;
  flightNo: string;
  pnlName?: string;
  flightDate?: string;
  airline: {
    internationalCode: string;
    thumbnail: string;
    thumbnailFull: string;
    logo: string;
    logoFull: string;
    id: string;
    name: string;
  };
  marketingAirline?: {
    internationalCode: string;
    thumbnail: string;
    thumbnailFull: string;
    logo: string;
    logoFull: string;
    id: string;
    name: string;
  };
  duration: number;
  dayChange?: number;
  departure: {
    country?: {
      name: string;
      provider: number;
      isTopRegion: boolean;
      id: string;
    };
    city?: {
      name: string;
      provider: number;
      isTopRegion: boolean;
      id: string;
    };
    airport?: {
      name: string;
      code: string;
      provider: number;
      id: string;
      geolocation?: {
        latitude: string | number;
        longitude: string | number;
      };
    };
    date: string;
    geoLocation?: {
      latitude: string | number;
      longitude: string | number;
    };
  };
  arrival: {
    country?: {
      name: string;
      provider: number;
      isTopRegion: boolean;
      id: string;
    };
    city?: {
      name: string;
      provider: number;
      isTopRegion: boolean;
      id: string;
    };
    airport?: {
      name: string;
      code: string;
      provider: number;
      id: string;
      geolocation?: {
        latitude: string | number;
        longitude: string | number;
      };
    };
    date: string;
    geoLocation?: {
      latitude: string | number;
      longitude: string | number;
    };
  };
  flightClass?: {
    type: number;
    name: string;
    id: string;
    code: string;
  };
  route?: number;
  stopCount?: number;
  flightProvider?: {
    displayName: string;
    id: string;
    name: string;
  };
  baggageInformations?: any[];
  passengers?: {
    type: number;
    count: number;
    ages?: number[];
  }[];
  flightType?: number;
  services?: any[];
  aircraft?: string;
  segmentNumber?: number;
}

// Interface pour les services
export interface Service {
  id?: string;
  name: string;
  description?: string;
  type?: number;
  code?: string;
  price?: {
    amount: number;
    currency: string;
    currencyId?: string;
    formattedAmount?: string;
  };
  isIncluded?: boolean;
  isOptional?: boolean;
}

// Interface pour les éléments communs des vols
export interface FlightItem {
  id?: string;
  flightNo: string;
  pnlName: string;
  flightDate: string;
  airline: {
    internationalCode: string;
    thumbnail: string;
    thumbnailFull: string;
    logo: string;
    logoFull: string;
    id: string;
    name: string;
  };
  marketingAirline?: {
    internationalCode: string;
    thumbnail: string;
    thumbnailFull: string;
    logo: string;
    logoFull: string;
    id: string;
    name: string;
  };
  duration: number;
  dayChange: number;
  departure: {
    country: {
      name: string;
      provider: number;
      isTopRegion: boolean;
      id: string;
    };
    city: {
      name: string;
      provider: number;
      isTopRegion: boolean;
      id: string;
    };
    airport: {
      name: string;
      code: string;
      provider: number;
      id: string;
      geolocation?: {
        latitude: string | number;
        longitude: string | number;
      };
    };
    date: string;
    geoLocation: {
      latitude: string | number;
      longitude: string | number;
    };
  };
  arrival: {
    country: {
      name: string;
      provider: number;
      isTopRegion: boolean;
      id: string;
    };
    city: {
      name: string;
      provider: number;
      isTopRegion: boolean;
      id: string;
    };
    airport: {
      name: string;
      code: string;
      provider: number;
      id: string;
      geolocation?: {
        latitude: string | number;
        longitude: string | number;
      };
    };
    date: string;
    geoLocation: {
      latitude: string | number;
      longitude: string | number;
    };
  };
  flightClass: {
    type: number;
    name: string;
    id: string;
    code: string;
  };
  route: number;
  segments: Segment[];
  stopCount: number;
  flightProvider?: {
    displayName: string;
    id: string;
    name: string;
  };
  baggageInformations: any[];
  passengers: {
    type: number;
    count: number;
    ages?: number[];
  }[];
  flightType: number;
  services: Service[];
  aircraft?: string;
  segmentNumber?: number;
}

// Interface pour les offres communes
export interface Offer {
  segmentNumber?: number;
  singleAdultPrice: {
    amount: number;
    currency: string;
    currencyId?: string;
    formattedAmount?: string;
  };
  priceBreakDown: {
    items: {
      passengerType: number;
      passengerCount: number;
      price: {
        amount: number;
        currency: string;
        currencyId?: string;
        formattedAmount?: string;
      };
      airportTax?: {
        amount: number;
        currency: string;
        currencyId?: string;
        formattedAmount?: string;
      };
    }[];
  };
  serviceFee: {
    amount: number;
    currency: string;
    currencyId?: string;
    formattedAmount?: string;
  };
  seatInfo: {
    availableSeatCount: number;
    availableSeatCountType: number;
  };
  flightClassInformations: any[];
  baggageInformations: any[];
  services: Service[];
  reservableInfo: {
    reservable: boolean;
  };
  groupKeys: string[];
  fees?: any;
  isPackageOffer: boolean;
  hasBrand: boolean;
  route: number;
  flightProvider: {
    displayName: string;
    id: string;
    name: string;
  };
  flightBrandInfo: {
    id: string;
    name: string;
    features?: any[];
  };
  expiresOn: string;
  offerId?: string;
  price: {
    amount: number;
    currency: string;
    currencyId?: string;
    formattedAmount?: string;
  };
  provider: number;
  id?: string;
  offerIds?: any[];
  availability?: number;
  brandedFare?: any;
}

// Type union pour les vols
export interface CommonFlight {
  id?: string;
  items: FlightItem[];
  offers: Offer[];
  provider?: number;
  route?: number;
}

// Interface commune pour les réponses
export interface CommonResponse {
  header: {
    requestId: string;
    success: boolean;
    responseTime: string;
    messages: {
      id: number;
      code: string;
      messageType: number;
      message: string;
    }[];
  };
  body: {
    searchId: string;
    expiresOn: string;
    flights: CommonFlight[];
    details?: {
      flightResponseListType: number;
      enablePaging: boolean;
    };
  };
}
