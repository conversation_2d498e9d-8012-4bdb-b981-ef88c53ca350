{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction LoginComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction LoginComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(authService, router, snackBar) {\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.credentials = {\n      Agency: '',\n      User: '',\n      Password: ''\n    };\n    this.loading = false;\n    this.error = '';\n    this.hidePassword = true;\n  }\n  ngOnInit() {\n    // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n    this.authService.logout();\n    // Animation d'entrée\n    document.body.classList.add('login-page-active');\n  }\n  onSubmit() {\n    if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n      this.error = 'Please fill in all required fields.';\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    this.authService.login(this.credentials).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.header.success) {\n          this.snackBar.open('Login successful! Welcome back.', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar',\n            verticalPosition: 'top'\n          });\n          this.router.navigate(['/accueil']);\n        } else {\n          this.error = 'Authentication failed. Please check your credentials.';\n        }\n      },\n      error: err => {\n        this.loading = false;\n        this.error = 'An error occurred during login. Please try again.';\n        console.error('Login error:', err);\n      }\n    });\n  }\n  ngOnDestroy() {\n    document.body.classList.remove('login-page-active');\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 46,\n      vars: 9,\n      consts: [[1, \"login-page\"], [1, \"login-background\"], [1, \"login-shapes\"], [1, \"shape\", \"shape-1\"], [1, \"shape\", \"shape-2\"], [1, \"shape\", \"shape-3\"], [1, \"login-container\"], [1, \"login-card\", \"animate-slide-up\"], [1, \"login-logo\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"login-header\"], [1, \"login-form\", 3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"agency\"], [1, \"input-with-icon\"], [1, \"fas\", \"fa-building\"], [\"type\", \"text\", \"id\", \"agency\", \"name\", \"agency\", \"required\", \"\", \"placeholder\", \"Enter your agency name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"username\"], [1, \"fas\", \"fa-user\"], [\"type\", \"text\", \"id\", \"username\", \"name\", \"username\", \"required\", \"\", \"placeholder\", \"Enter your username\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [1, \"fas\", \"fa-lock\"], [\"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"type\", \"ngModel\", \"ngModelChange\"], [\"type\", \"button\", 1, \"password-toggle\", 3, \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"login-footer\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"spinner-container\"], [1, \"spinner\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelement(9, \"i\", 9);\n          i0.ɵɵelementStart(10, \"h1\");\n          i0.ɵɵtext(11, \"E-Tourism\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"h2\");\n          i0.ɵɵtext(14, \"Welcome Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p\");\n          i0.ɵɵtext(16, \"Sign in to continue to your account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"form\", 11, 12);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_17_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(19, \"div\", 13)(20, \"label\", 14);\n          i0.ɵɵtext(21, \"Agency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 15);\n          i0.ɵɵelement(23, \"i\", 16);\n          i0.ɵɵelementStart(24, \"input\", 17);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_24_listener($event) {\n            return ctx.credentials.Agency = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 13)(26, \"label\", 18);\n          i0.ɵɵtext(27, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 15);\n          i0.ɵɵelement(29, \"i\", 19);\n          i0.ɵɵelementStart(30, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_30_listener($event) {\n            return ctx.credentials.User = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"label\", 21);\n          i0.ɵɵtext(33, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 15);\n          i0.ɵɵelement(35, \"i\", 22);\n          i0.ɵɵelementStart(36, \"input\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_36_listener($event) {\n            return ctx.credentials.Password = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_37_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelement(38, \"i\", 25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(39, LoginComponent_div_39_Template, 4, 1, \"div\", 26);\n          i0.ɵɵelementStart(40, \"button\", 27);\n          i0.ɵɵtemplate(41, LoginComponent_div_41_Template, 2, 0, \"div\", 28);\n          i0.ɵɵtemplate(42, LoginComponent_span_42_Template, 2, 0, \"span\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 30)(44, \"p\");\n          i0.ɵɵtext(45, \"\\u00A9 2023 E-Tourism. All rights reserved.\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(18);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngModel\", ctx.credentials.Agency);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.credentials.User);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\")(\"ngModel\", ctx.credentials.Password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.hidePassword ? \"fa-eye\" : \"fa-eye-slash\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !_r0.form.valid || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm],\n      styles: [\".login-page[_ngcontent-%COMP%] {\\n  position: relative;\\n  min-height: 100vh;\\n  width: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.login-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 50%, var(--primary-light) 100%);\\n  z-index: -1;\\n}\\n\\n.login-shapes[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.shape[_ngcontent-%COMP%] {\\n  position: absolute;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 50%;\\n}\\n\\n.shape-1[_ngcontent-%COMP%] {\\n  width: 500px;\\n  height: 500px;\\n  top: -250px;\\n  right: -100px;\\n  animation: _ngcontent-%COMP%_float 15s infinite alternate ease-in-out;\\n}\\n\\n.shape-2[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: 300px;\\n  bottom: -150px;\\n  left: -50px;\\n  animation: _ngcontent-%COMP%_float 20s infinite alternate-reverse ease-in-out;\\n}\\n\\n.shape-3[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  animation: _ngcontent-%COMP%_pulse 10s infinite alternate ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0% {\\n    transform: translateY(0) rotate(0deg);\\n  }\\n  100% {\\n    transform: translateY(30px) rotate(10deg);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: translate(-50%, -50%) scale(1);\\n    opacity: 0.5;\\n  }\\n  100% {\\n    transform: translate(-50%, -50%) scale(1.5);\\n    opacity: 0.2;\\n  }\\n}\\n\\n.login-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  width: 100%;\\n  padding: var(--spacing-md);\\n  z-index: 1;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 450px;\\n  background-color: var(--surface-color);\\n  border-radius: var(--border-radius-medium);\\n  box-shadow: var(--elevation-4);\\n  padding: var(--spacing-xl);\\n  overflow: hidden;\\n}\\n\\n.login-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin-bottom: var(--spacing-lg);\\n}\\n\\n.login-logo[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: var(--primary-color);\\n  margin-bottom: var(--spacing-sm);\\n}\\n\\n.login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.75rem;\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  margin: 0;\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: var(--spacing-lg);\\n}\\n\\n.login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: var(--text-primary);\\n  margin-bottom: var(--spacing-xs);\\n  font-size: 1.5rem;\\n  font-weight: 500;\\n}\\n\\n.login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: var(--spacing-md);\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-md);\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: var(--spacing-xs);\\n  font-weight: 500;\\n  color: var(--text-primary);\\n  font-size: 0.9rem;\\n}\\n\\n.input-with-icon[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.input-with-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 12px;\\n  color: var(--primary-color);\\n  font-size: 1rem;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 12px 12px 40px;\\n  border: 1px solid var(--divider-color);\\n  border-radius: var(--border-radius-small);\\n  font-size: 1rem;\\n  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);\\n  background-color: var(--surface-color);\\n  color: var(--text-primary);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.form-control[_ngcontent-%COMP%]::placeholder {\\n  color: var(--text-hint);\\n}\\n\\n.password-toggle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 12px;\\n  background: none;\\n  border: none;\\n  color: var(--text-secondary);\\n  cursor: pointer;\\n  padding: 0;\\n  font-size: 1rem;\\n  transition: color var(--transition-fast);\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  color: var(--error-color);\\n  background-color: rgba(244, 67, 54, 0.1);\\n  padding: var(--spacing-sm) var(--spacing-md);\\n  border-radius: var(--border-radius-small);\\n  margin-bottom: var(--spacing-sm);\\n}\\n\\n.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 48px;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  margin-top: var(--spacing-sm);\\n  position: relative;\\n  background-color: var(--primary-color);\\n  color: white;\\n  border: none;\\n  border-radius: var(--border-radius-small);\\n  cursor: pointer;\\n  transition: background-color var(--transition-fast);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: var(--primary-dark);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  background-color: var(--divider-color);\\n  cursor: not-allowed;\\n}\\n\\n.spinner-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  border-top-color: white;\\n  animation: _ngcontent-%COMP%_spin 0.8s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n.login-footer[_ngcontent-%COMP%] {\\n  margin-top: var(--spacing-xl);\\n  text-align: center;\\n}\\n\\n.login-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-hint);\\n  font-size: 0.75rem;\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 500px) {\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: var(--spacing-lg);\\n  }\\n\\n  .login-logo[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n\\n  .login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n}\\n\\n\\n\\nbody.login-page-active[_nghost-%COMP%], body.login-page-active   [_nghost-%COMP%] {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "LoginComponent", "constructor", "authService", "router", "snackBar", "credentials", "Agency", "User", "Password", "loading", "hidePassword", "ngOnInit", "logout", "document", "body", "classList", "add", "onSubmit", "login", "subscribe", "next", "response", "header", "success", "open", "duration", "panelClass", "verticalPosition", "navigate", "err", "console", "ngOnDestroy", "remove", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_17_listener", "LoginComponent_Template_input_ngModelChange_24_listener", "$event", "LoginComponent_Template_input_ngModelChange_30_listener", "LoginComponent_Template_input_ngModelChange_36_listener", "LoginComponent_Template_button_click_37_listener", "ɵɵtemplate", "LoginComponent_div_39_Template", "LoginComponent_div_41_Template", "LoginComponent_span_42_Template", "ɵɵproperty", "_r0", "form", "valid"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { AuthRequest } from '../../models/auth-request.model';\nimport { MatSnackBar } from '@angular/material/snack-bar';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  credentials: AuthRequest = {\n    Agency: '',\n    User: '',\n    Password: ''\n  };\n\n  loading = false;\n  error = '';\n  hidePassword = true;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) { }\n\n  ngOnInit(): void {\n    // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n    this.authService.logout();\n\n    // Animation d'entrée\n    document.body.classList.add('login-page-active');\n  }\n\n  onSubmit(): void {\n    if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n      this.error = 'Please fill in all required fields.';\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    this.authService.login(this.credentials)\n      .subscribe({\n        next: (response) => {\n          this.loading = false;\n          if (response.header.success) {\n            this.snackBar.open('Login successful! Welcome back.', 'Close', {\n              duration: 3000,\n              panelClass: 'success-snackbar',\n              verticalPosition: 'top'\n            });\n            this.router.navigate(['/accueil']);\n          } else {\n            this.error = 'Authentication failed. Please check your credentials.';\n          }\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = 'An error occurred during login. Please try again.';\n          console.error('Login error:', err);\n        }\n      });\n  }\n\n  ngOnDestroy(): void {\n    document.body.classList.remove('login-page-active');\n  }\n}\n", "<div class=\"login-page\">\n  <div class=\"login-background\">\n    <div class=\"login-shapes\">\n      <div class=\"shape shape-1\"></div>\n      <div class=\"shape shape-2\"></div>\n      <div class=\"shape shape-3\"></div>\n    </div>\n  </div>\n\n  <div class=\"login-container\">\n    <div class=\"login-card animate-slide-up\">\n      <div class=\"login-logo\">\n        <i class=\"fas fa-plane-departure\"></i>\n        <h1>E-Tourism</h1>\n      </div>\n\n      <div class=\"login-header\">\n        <h2>Welcome Back</h2>\n        <p>Sign in to continue to your account</p>\n      </div>\n\n      <form (ngSubmit)=\"onSubmit()\" #loginForm=\"ngForm\" class=\"login-form\">\n        <div class=\"form-group\">\n          <label for=\"agency\">Agency</label>\n          <div class=\"input-with-icon\">\n            <i class=\"fas fa-building\"></i>\n            <input\n              type=\"text\"\n              id=\"agency\"\n              name=\"agency\"\n              [(ngModel)]=\"credentials.Agency\"\n              required\n              class=\"form-control\"\n              placeholder=\"Enter your agency name\">\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"username\">Username</label>\n          <div class=\"input-with-icon\">\n            <i class=\"fas fa-user\"></i>\n            <input\n              type=\"text\"\n              id=\"username\"\n              name=\"username\"\n              [(ngModel)]=\"credentials.User\"\n              required\n              class=\"form-control\"\n              placeholder=\"Enter your username\">\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"password\">Password</label>\n          <div class=\"input-with-icon\">\n            <i class=\"fas fa-lock\"></i>\n            <input\n              [type]=\"hidePassword ? 'password' : 'text'\"\n              id=\"password\"\n              name=\"password\"\n              [(ngModel)]=\"credentials.Password\"\n              required\n              class=\"form-control\"\n              placeholder=\"Enter your password\">\n            <button\n              type=\"button\"\n              class=\"password-toggle\"\n              (click)=\"hidePassword = !hidePassword\">\n              <i class=\"fas\" [ngClass]=\"hidePassword ? 'fa-eye' : 'fa-eye-slash'\"></i>\n            </button>\n          </div>\n        </div>\n\n        <div *ngIf=\"error\" class=\"error-message\">\n          <i class=\"fas fa-exclamation-circle\"></i>\n          <span>{{ error }}</span>\n        </div>\n\n        <button\n          type=\"submit\"\n          [disabled]=\"!loginForm.form.valid || loading\"\n          class=\"login-button\">\n          <div *ngIf=\"loading\" class=\"spinner-container\">\n            <div class=\"spinner\"></div>\n          </div>\n          <span *ngIf=\"!loading\">Sign In</span>\n        </button>\n      </form>\n\n      <div class=\"login-footer\">\n        <p>© 2023 E-Tourism. All rights reserved.</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICyEQA,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAAE,SAAA,YAAyC;IACzCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAlBJ,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAOjBR,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;AD1E/C,OAAM,MAAOK,cAAc;EAWzBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAblB,KAAAC,WAAW,GAAgB;MACzBC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE;KACX;IAED,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAV,KAAK,GAAG,EAAE;IACV,KAAAW,YAAY,GAAG,IAAI;EAMf;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACT,WAAW,CAACU,MAAM,EAAE;IAEzB;IACAC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAClD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACZ,WAAW,CAACC,MAAM,IAAI,CAAC,IAAI,CAACD,WAAW,CAACE,IAAI,IAAI,CAAC,IAAI,CAACF,WAAW,CAACG,QAAQ,EAAE;MACpF,IAAI,CAACT,KAAK,GAAG,qCAAqC;MAClD;;IAGF,IAAI,CAACU,OAAO,GAAG,IAAI;IACnB,IAAI,CAACV,KAAK,GAAG,EAAE;IAEf,IAAI,CAACG,WAAW,CAACgB,KAAK,CAAC,IAAI,CAACb,WAAW,CAAC,CACrCc,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACZ,OAAO,GAAG,KAAK;QACpB,IAAIY,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;UAC3B,IAAI,CAACnB,QAAQ,CAACoB,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;YAC7DC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,kBAAkB;YAC9BC,gBAAgB,EAAE;WACnB,CAAC;UACF,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;SACnC,MAAM;UACL,IAAI,CAAC7B,KAAK,GAAG,uDAAuD;;MAExE,CAAC;MACDA,KAAK,EAAG8B,GAAG,IAAI;QACb,IAAI,CAACpB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACV,KAAK,GAAG,mDAAmD;QAChE+B,OAAO,CAAC/B,KAAK,CAAC,cAAc,EAAE8B,GAAG,CAAC;MACpC;KACD,CAAC;EACN;EAEAE,WAAWA,CAAA;IACTlB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACiB,MAAM,CAAC,mBAAmB,CAAC;EACrD;;;uBA3DWhC,cAAc,EAAAT,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAA0C,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAA0C,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAdvC,cAAc;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX3BvD,EAAA,CAAAC,cAAA,aAAwB;UAGlBD,EAAA,CAAAE,SAAA,aAAiC;UAGnCF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAC,cAAA,aAA6B;UAGvBD,EAAA,CAAAE,SAAA,WAAsC;UACtCF,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGpBJ,EAAA,CAAAC,cAAA,eAA0B;UACpBD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACrBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,2CAAmC;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAG5CJ,EAAA,CAAAC,cAAA,oBAAqE;UAA/DD,EAAA,CAAAyD,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAA9B,QAAA,EAAU;UAAA,EAAC;UAC3B1B,EAAA,CAAAC,cAAA,eAAwB;UACFD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAClCJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,SAAA,aAA+B;UAC/BF,EAAA,CAAAC,cAAA,iBAOuC;UAHrCD,EAAA,CAAAyD,UAAA,2BAAAE,wDAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAC,MAAA,GAAA6C,MAAA;UAAA,EAAgC;UAJlC5D,EAAA,CAAAI,YAAA,EAOuC;UAI3CJ,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACtCJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,SAAA,aAA2B;UAC3BF,EAAA,CAAAC,cAAA,iBAOoC;UAHlCD,EAAA,CAAAyD,UAAA,2BAAAI,wDAAAD,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAE,IAAA,GAAA4C,MAAA;UAAA,EAA8B;UAJhC5D,EAAA,CAAAI,YAAA,EAOoC;UAIxCJ,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACtCJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,SAAA,aAA2B;UAC3BF,EAAA,CAAAC,cAAA,iBAOoC;UAHlCD,EAAA,CAAAyD,UAAA,2BAAAK,wDAAAF,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAG,QAAA,GAAA2C,MAAA;UAAA,EAAkC;UAJpC5D,EAAA,CAAAI,YAAA,EAOoC;UACpCJ,EAAA,CAAAC,cAAA,kBAGyC;UAAvCD,EAAA,CAAAyD,UAAA,mBAAAM,iDAAA;YAAA,OAAAP,GAAA,CAAArC,YAAA,IAAAqC,GAAA,CAAArC,YAAA;UAAA,EAAsC;UACtCnB,EAAA,CAAAE,SAAA,aAAwE;UAC1EF,EAAA,CAAAI,YAAA,EAAS;UAIbJ,EAAA,CAAAgE,UAAA,KAAAC,8BAAA,kBAGM;UAENjE,EAAA,CAAAC,cAAA,kBAGuB;UACrBD,EAAA,CAAAgE,UAAA,KAAAE,8BAAA,kBAEM;UACNlE,EAAA,CAAAgE,UAAA,KAAAG,+BAAA,mBAAqC;UACvCnE,EAAA,CAAAI,YAAA,EAAS;UAGXJ,EAAA,CAAAC,cAAA,eAA0B;UACrBD,EAAA,CAAAG,MAAA,mDAAsC;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;;UA5DvCJ,EAAA,CAAAK,SAAA,IAAgC;UAAhCL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAA1C,WAAA,CAAAC,MAAA,CAAgC;UAehCf,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAA1C,WAAA,CAAAE,IAAA,CAA8B;UAY9BhB,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAArC,YAAA,uBAA2C,YAAAqC,GAAA,CAAA1C,WAAA,CAAAG,QAAA;UAW5BjB,EAAA,CAAAK,SAAA,GAAoD;UAApDL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAArC,YAAA,6BAAoD;UAKnEnB,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAAhD,KAAA,CAAW;UAOfR,EAAA,CAAAK,SAAA,GAA6C;UAA7CL,EAAA,CAAAoE,UAAA,cAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,IAAAf,GAAA,CAAAtC,OAAA,CAA6C;UAEvClB,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAAtC,OAAA,CAAa;UAGZlB,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAoE,UAAA,UAAAZ,GAAA,CAAAtC,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}