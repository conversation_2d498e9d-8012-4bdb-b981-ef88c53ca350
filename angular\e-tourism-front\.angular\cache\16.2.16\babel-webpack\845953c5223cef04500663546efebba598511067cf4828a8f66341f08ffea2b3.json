{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { FormControl } from '@angular/forms';\nimport { map, startWith } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/country.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/autocomplete\";\nimport * as i5 from \"@angular/material/core\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/icon\";\nfunction CountrySelectorComponent_mat_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 6)(1, \"div\", 7);\n    i0.ɵɵelement(2, \"img\", 8);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 9);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const country_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", country_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", country_r2.flag, i0.ɵɵsanitizeUrl)(\"alt\", country_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(country_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", country_r2.code, \")\");\n  }\n}\nexport let CountrySelectorComponent = /*#__PURE__*/(() => {\n  class CountrySelectorComponent {\n    constructor(countryService) {\n      this.countryService = countryService;\n      this.label = 'Pays';\n      this.placeholder = 'Sélectionnez un pays';\n      this.required = false;\n      this.countrySelected = new EventEmitter();\n      this.countries = [];\n      this.countryControl = new FormControl();\n      this.selectedCountry = null;\n      // Fonctions pour ControlValueAccessor\n      this.onChange = () => {};\n      this.onTouched = () => {};\n      this.filteredCountries = this.countryControl.valueChanges.pipe(startWith(''), map(value => this._filter(value || '')));\n    }\n    ngOnInit() {\n      this.countryService.getCountries().subscribe(countries => {\n        this.countries = countries;\n      });\n      this.countryControl.valueChanges.subscribe(value => {\n        if (typeof value === 'string') {\n          // L'utilisateur a saisi du texte, chercher le pays correspondant\n          const country = this.countries.find(c => c.name.toLowerCase().includes(value.toLowerCase()) || c.code.toLowerCase() === value.toLowerCase());\n          if (country) {\n            this.selectCountry(country);\n          }\n        } else if (value && value.code) {\n          // L'utilisateur a sélectionné un pays dans la liste\n          this.selectCountry(value);\n        }\n      });\n    }\n    // Méthode pour filtrer les pays en fonction de la saisie\n    _filter(value) {\n      const filterValue = value.toLowerCase();\n      return this.countries.filter(country => country.name.toLowerCase().includes(filterValue) || country.code.toLowerCase().includes(filterValue));\n    }\n    // Méthode pour afficher le nom du pays dans l'autocomplete\n    displayFn(country) {\n      return country && country.name ? country.name : '';\n    }\n    // Méthode pour sélectionner un pays\n    selectCountry(country) {\n      this.selectedCountry = country;\n      this.onChange(country.code);\n      this.onTouched();\n      this.countrySelected.emit(country);\n    }\n    // Méthodes pour ControlValueAccessor\n    writeValue(value) {\n      if (value && typeof value === 'string') {\n        // Si la valeur est un code de pays, chercher le pays correspondant\n        const country = this.countryService.getCountryByCode(value);\n        if (country) {\n          this.selectedCountry = country;\n          this.countryControl.setValue(country);\n        } else {\n          this.selectedCountry = null;\n          this.countryControl.setValue('');\n        }\n      } else {\n        this.selectedCountry = null;\n        this.countryControl.setValue('');\n      }\n    }\n    registerOnChange(fn) {\n      this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onTouched = fn;\n    }\n    setDisabledState(isDisabled) {\n      if (isDisabled) {\n        this.countryControl.disable();\n      } else {\n        this.countryControl.enable();\n      }\n    }\n    static {\n      this.ɵfac = function CountrySelectorComponent_Factory(t) {\n        return new (t || CountrySelectorComponent)(i0.ɵɵdirectiveInject(i1.CountryService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CountrySelectorComponent,\n        selectors: [[\"app-country-selector\"]],\n        inputs: {\n          label: \"label\",\n          placeholder: \"placeholder\",\n          required: \"required\"\n        },\n        outputs: {\n          countrySelected: \"countrySelected\"\n        },\n        features: [i0.ɵɵProvidersFeature([{\n          provide: NG_VALUE_ACCESSOR,\n          useExisting: forwardRef(() => CountrySelectorComponent),\n          multi: true\n        }])],\n        decls: 10,\n        vars: 9,\n        consts: [[\"appearance\", \"outline\", 1, \"country-selector\"], [\"type\", \"text\", \"matInput\", \"\", 3, \"formControl\", \"matAutocomplete\", \"placeholder\", \"required\"], [3, \"displayWith\"], [\"auto\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matSuffix\", \"\"], [3, \"value\"], [1, \"country-option\"], [1, \"country-flag\", 3, \"src\", \"alt\"], [1, \"country-code\"]],\n        template: function CountrySelectorComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"mat-form-field\", 0)(1, \"mat-label\");\n            i0.ɵɵtext(2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(3, \"input\", 1);\n            i0.ɵɵelementStart(4, \"mat-autocomplete\", 2, 3);\n            i0.ɵɵtemplate(6, CountrySelectorComponent_mat_option_6_Template, 7, 5, \"mat-option\", 4);\n            i0.ɵɵpipe(7, \"async\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"mat-icon\", 5);\n            i0.ɵɵtext(9, \"public\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            const _r0 = i0.ɵɵreference(5);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.label);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formControl\", ctx.countryControl)(\"matAutocomplete\", _r0)(\"placeholder\", ctx.placeholder)(\"required\", ctx.required);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"displayWith\", ctx.displayFn);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(7, 7, ctx.filteredCountries));\n          }\n        },\n        dependencies: [i2.NgForOf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.RequiredValidator, i3.FormControlDirective, i4.MatAutocomplete, i5.MatOption, i4.MatAutocompleteTrigger, i6.MatFormField, i6.MatLabel, i6.MatSuffix, i7.MatInput, i8.MatIcon, i2.AsyncPipe],\n        styles: [\".country-selector[_ngcontent-%COMP%]{width:100%}.country-option[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.country-flag[_ngcontent-%COMP%]{width:24px;height:16px;object-fit:cover;border-radius:2px;box-shadow:0 1px 2px #0003}.country-code[_ngcontent-%COMP%]{color:#0000008a;margin-left:auto;font-size:.85em}\"]\n      });\n    }\n  }\n  return CountrySelectorComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}