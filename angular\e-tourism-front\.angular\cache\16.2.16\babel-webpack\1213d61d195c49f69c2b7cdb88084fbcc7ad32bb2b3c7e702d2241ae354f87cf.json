{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/product.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/autocomplete\";\nimport * as i6 from \"@angular/material/core\";\nfunction SearchPriceComponent_mat_option_34_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r12.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_34_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 92);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r12.city);\n  }\n}\nconst _c0 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"fa-flag\": a0,\n    \"fa-city\": a1,\n    \"fa-building\": a2,\n    \"fa-home\": a3,\n    \"fa-plane\": a4\n  };\n};\nfunction SearchPriceComponent_mat_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 30)(1, \"div\", 84)(2, \"div\", 85);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 86);\n    i0.ɵɵtemplate(5, SearchPriceComponent_mat_option_34_span_5_Template, 2, 1, \"span\", 87);\n    i0.ɵɵtemplate(6, SearchPriceComponent_mat_option_34_span_6_Template, 2, 1, \"span\", 88);\n    i0.ɵɵelementStart(7, \"span\", 89);\n    i0.ɵɵelement(8, \"i\", 90);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r12);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r12.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r12.type === 5 && location_r12.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r12.type === 1, location_r12.type === 2, location_r12.type === 3, location_r12.type === 4, location_r12.type === 5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r12.type === 1 ? \"Country\" : location_r12.type === 2 ? \"City\" : location_r12.type === 3 ? \"Town\" : location_r12.type === 4 ? \"Village\" : location_r12.type === 5 ? \"Airport\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"i\", 94);\n    i0.ɵɵtext(2, \" Please select a departure location \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_mat_option_59_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r17.code);\n  }\n}\nfunction SearchPriceComponent_mat_option_59_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 92);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(location_r17.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 30)(1, \"div\", 84)(2, \"div\", 85);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 86);\n    i0.ɵɵtemplate(5, SearchPriceComponent_mat_option_59_span_5_Template, 2, 1, \"span\", 87);\n    i0.ɵɵtemplate(6, SearchPriceComponent_mat_option_59_span_6_Template, 2, 1, \"span\", 88);\n    i0.ɵɵelementStart(7, \"span\", 89);\n    i0.ɵɵelement(8, \"i\", 90);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const location_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r17);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(location_r17.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", location_r17.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r17.type === 5 && location_r17.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c0, location_r17.type === 1, location_r17.type === 2, location_r17.type === 3, location_r17.type === 4, location_r17.type === 5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", location_r17.type === 1 ? \"Country\" : location_r17.type === 2 ? \"City\" : location_r17.type === 3 ? \"Town\" : location_r17.type === 4 ? \"Village\" : location_r17.type === 5 ? \"Airport\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"i\", 94);\n    i0.ɵɵtext(2, \" Please select an arrival location \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"i\", 94);\n    i0.ɵɵtext(2, \" Please select a date \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_option_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", flightClass_r22.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(flightClass_r22.label);\n  }\n}\nfunction SearchPriceComponent_i_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 95);\n  }\n}\nfunction SearchPriceComponent_span_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Search\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"div\", 97);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_166_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"div\", 103)(2, \"div\", 104);\n    i0.ɵɵelement(3, \"i\", 105)(4, \"div\", 106)(5, \"div\", 106)(6, \"div\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Searching for the best flights...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SearchPriceComponent_div_166_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"div\", 108);\n    i0.ɵɵelement(2, \"i\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Oops! Something went wrong\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 93);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_166_div_2_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.onSearch());\n    });\n    i0.ɵɵelement(8, \"i\", 110);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r24.errorMessage);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No flights found for your search. Please modify your criteria.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"span\", 118);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" flights found \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r29.searchResults.length);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"div\", 120);\n    i0.ɵɵelement(2, \"i\", 121);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Sort by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"select\", 122)(6, \"option\", 123);\n    i0.ɵɵtext(7, \"Price (lowest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"option\", 124);\n    i0.ɵɵtext(9, \"Duration (shortest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 125);\n    i0.ɵɵtext(11, \"Departure (earliest first)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 126);\n    i0.ɵɵtext(13, \"Arrival (earliest first)\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 170);\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", flight_r33.items[0].airline.thumbnailFull, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 171);\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 172);\n    i0.ɵɵelement(1, \"i\", 173);\n    i0.ɵɵtext(2, \" Direct \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 172);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", flight_r33.items[0].flightClass.name, \" \");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 174);\n    i0.ɵɵelement(1, \"i\", 175);\n    i0.ɵɵtext(2, \" Not available \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 176)(1, \"span\", 177);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r33.items[0].stopCount);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" stop\", flight_r33.items[0].stopCount > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 178);\n    i0.ɵɵtext(1, \" Direct flight \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 127)(1, \"div\", 128)(2, \"div\", 129)(3, \"div\", 130);\n    i0.ɵɵtemplate(4, SearchPriceComponent_div_166_div_3_div_9_img_4_Template, 1, 1, \"img\", 131);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_166_div_3_div_9_i_5_Template, 1, 0, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 133)(7, \"span\", 134);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 135);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 136);\n    i0.ɵɵtemplate(12, SearchPriceComponent_div_166_div_3_div_9_span_12_Template, 3, 0, \"span\", 137);\n    i0.ɵɵtemplate(13, SearchPriceComponent_div_166_div_3_div_9_span_13_Template, 3, 1, \"span\", 137);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 138)(15, \"span\", 139);\n    i0.ɵɵtext(16, \"Price per person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 140);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, SearchPriceComponent_div_166_div_3_div_9_span_19_Template, 3, 0, \"span\", 141);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 142)(21, \"div\", 143)(22, \"div\", 144)(23, \"div\", 145);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 146)(26, \"span\", 147);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 148);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 149)(31, \"div\", 150);\n    i0.ɵɵelement(32, \"span\", 151);\n    i0.ɵɵelementStart(33, \"div\", 152);\n    i0.ɵɵelement(34, \"span\", 153);\n    i0.ɵɵelementStart(35, \"span\", 154);\n    i0.ɵɵelement(36, \"i\", 105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(37, \"span\", 155);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 156);\n    i0.ɵɵelement(39, \"i\", 157);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(41, SearchPriceComponent_div_166_div_3_div_9_div_41_Template, 4, 2, \"div\", 158);\n    i0.ɵɵtemplate(42, SearchPriceComponent_div_166_div_3_div_9_div_42_Template, 2, 0, \"div\", 159);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 160)(44, \"div\", 145);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 146)(47, \"span\", 147);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"span\", 148);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(51, \"div\", 161)(52, \"div\", 162);\n    i0.ɵɵelement(53, \"i\", 68);\n    i0.ɵɵelementStart(54, \"span\");\n    i0.ɵɵtext(55, \"Baggage included\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 162);\n    i0.ɵɵelement(57, \"i\", 163);\n    i0.ɵɵelementStart(58, \"span\");\n    i0.ɵɵtext(59, \"Meal service\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 162);\n    i0.ɵɵelement(61, \"i\", 164);\n    i0.ɵɵelementStart(62, \"span\");\n    i0.ɵɵtext(63, \"WiFi available\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(64, \"div\", 165)(65, \"button\", 166);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_166_div_3_div_9_Template_button_click_65_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r45);\n      const flight_r33 = restoredCtx.$implicit;\n      const ctx_r44 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r44.viewOfferDetails(flight_r33));\n    });\n    i0.ɵɵelement(66, \"i\", 167);\n    i0.ɵɵtext(67, \" View Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"button\", 168);\n    i0.ɵɵelement(69, \"i\", 169);\n    i0.ɵɵtext(70, \" Select This Flight \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r33 = ctx.$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"unavailable\", !ctx_r31.isFlightAvailable(flight_r33));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].airline && flight_r33.items[0].airline.thumbnailFull);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].airline && flight_r33.items[0].airline.thumbnailFull));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].airline ? flight_r33.items[0].airline.name : \"Airline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] ? flight_r33.items[0].flightNo : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].stopCount === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].flightClass);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r31.getMinPrice(flight_r33));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r31.isFlightAvailable(flight_r33));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].departure ? ctx_r31.formatDate(flight_r33.items[0].departure.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].departure && flight_r33.items[0].departure.airport ? flight_r33.items[0].departure.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].departure && flight_r33.items[0].departure.city ? flight_r33.items[0].departure.city.name : \"N/A\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", flight_r33.items && flight_r33.items[0] ? ctx_r31.formatDuration(flight_r33.items[0].duration) : \"N/A\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].stopCount > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r33.items && flight_r33.items[0] && flight_r33.items[0].stopCount === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].arrival ? ctx_r31.formatDate(flight_r33.items[0].arrival.date) : \"N/A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].arrival && flight_r33.items[0].arrival.airport ? flight_r33.items[0].arrival.airport.code : \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r33.items && flight_r33.items[0] && flight_r33.items[0].arrival && flight_r33.items[0].arrival.city ? flight_r33.items[0].arrival.city.name : \"N/A\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"disabled\", !ctx_r31.isFlightAvailable(flight_r33));\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 179)(1, \"div\", 180);\n    i0.ɵɵelement(2, \"i\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No flights found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"We couldn't find any flights matching your search criteria. Try adjusting your search parameters.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 181)(8, \"div\", 182);\n    i0.ɵɵelement(9, \"i\", 40);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Try different dates\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 182);\n    i0.ɵɵelement(13, \"i\", 183);\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Try nearby airports\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 182);\n    i0.ɵɵelement(17, \"i\", 105);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Include flights with stops\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SearchPriceComponent_div_166_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"div\", 112)(2, \"div\", 113)(3, \"h3\");\n    i0.ɵɵtext(4, \"Flight Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_166_div_3_p_5_Template, 2, 0, \"p\", 51);\n    i0.ɵɵtemplate(6, SearchPriceComponent_div_166_div_3_p_6_Template, 4, 1, \"p\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SearchPriceComponent_div_166_div_3_div_7_Template, 14, 0, \"div\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 115);\n    i0.ɵɵtemplate(9, SearchPriceComponent_div_166_div_3_div_9_Template, 71, 20, \"div\", 116);\n    i0.ɵɵtemplate(10, SearchPriceComponent_div_166_div_3_div_10_Template, 20, 0, \"div\", 117);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.searchResults.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r25.searchResults);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r25.isLoading && !ctx_r25.errorMessage && ctx_r25.searchResults.length === 0);\n  }\n}\nfunction SearchPriceComponent_div_166_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtemplate(1, SearchPriceComponent_div_166_div_1_Template, 9, 0, \"div\", 99);\n    i0.ɵɵtemplate(2, SearchPriceComponent_div_166_div_2_Template, 10, 1, \"div\", 100);\n    i0.ɵɵtemplate(3, SearchPriceComponent_div_166_div_3_Template, 11, 5, \"div\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.isLoading && ctx_r11.errorMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.isLoading && !ctx_r11.errorMessage);\n  }\n}\nexport let SearchPriceComponent = /*#__PURE__*/(() => {\n  class SearchPriceComponent {\n    constructor(fb, productService, router) {\n      this.fb = fb;\n      this.productService = productService;\n      this.router = router;\n      this.departureLocations = [];\n      this.arrivalLocations = [];\n      this.isLoading = false;\n      this.searchResults = [];\n      this.hasSearched = false;\n      this.errorMessage = '';\n      this.lastSearchId = '';\n      // Passenger type options\n      this.passengerTypes = [{\n        value: PassengerType.Adult,\n        label: 'Adult'\n      }, {\n        value: PassengerType.Child,\n        label: 'Child'\n      }, {\n        value: PassengerType.Infant,\n        label: 'Infant'\n      }];\n      // Flight class options\n      this.flightClasses = [{\n        value: FlightClassType.PROMO,\n        label: 'Promo'\n      }, {\n        value: FlightClassType.ECONOMY,\n        label: 'Economy'\n      }, {\n        value: FlightClassType.BUSINESS,\n        label: 'Business'\n      }];\n      // Initialiser la date minimale à aujourd'hui\n      this.minDate = new Date().toISOString().split('T')[0];\n      // Initialiser le formulaire avec tous les champs dynamiques\n      this.searchForm = this.fb.group({\n        // Champs principaux\n        productType: [3, Validators.required],\n        serviceTypes: [['1'], Validators.required],\n        departureLocation: ['', Validators.required],\n        departureLocationType: [2, Validators.required],\n        arrivalLocation: ['', Validators.required],\n        arrivalLocationType: [5, Validators.required],\n        departureDate: [this.minDate, Validators.required],\n        passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n        passengerType: [1, Validators.required],\n        // Options de vol\n        flightClass: [0, Validators.required],\n        nonStop: [false],\n        // Options avancées\n        culture: ['en-US'],\n        currency: ['EUR'],\n        acceptPendingProviders: [false],\n        forceFlightBundlePackage: [false],\n        disablePackageOfferTotalPrice: [true],\n        calculateFlightFees: [false],\n        // Options de bagages\n        flightBaggageGetOption: [0]\n      });\n    }\n    ngOnInit() {\n      // Configurer les observables pour l'autocomplétion\n      this.setupAutocomplete();\n      // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n      this.preloadLocations();\n    }\n    preloadLocations() {\n      // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n      // toutes les valeurs sans valeurs par défaut\n      // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n    }\n    setupAutocomplete() {\n      // Charger les locations par type par défaut\n      const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n      const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n      this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n        this.departureLocations = locations;\n      });\n      this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n      // Écouter les changements de type de localisation de départ\n      this.searchForm.get('departureLocationType')?.valueChanges.subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.departureLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('departureLocation')?.setValue('');\n        });\n      });\n      // Écouter les changements de type de localisation d'arrivée\n      this.searchForm.get('arrivalLocationType')?.valueChanges.subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.arrivalLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('arrivalLocation')?.setValue('');\n        });\n      });\n      // Autocomplétion pour le lieu de départ\n      this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n        if (typeof value === 'string') {\n          const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n          // Si l'utilisateur tape quelque chose, filtrer les résultats\n          if (value.length > 0) {\n            // Filtrer les résultats par type et par texte\n            return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n          } else {\n            // Si le champ est vide, afficher toutes les options du type sélectionné\n            return this.productService.getLocationsByType(locationType);\n          }\n        }\n        return of([]);\n      })).subscribe(locations => {\n        this.departureLocations = locations;\n      });\n      // Autocomplétion pour le lieu d'arrivée\n      this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n        if (typeof value === 'string') {\n          const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n          // Si l'utilisateur tape quelque chose, filtrer les résultats\n          if (value.length > 0) {\n            // Filtrer les résultats par type et par texte\n            return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n          } else {\n            // Si le champ est vide, afficher toutes les options du type sélectionné\n            return this.productService.getLocationsByType(locationType);\n          }\n        }\n        return of([]);\n      })).subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n    }\n    displayLocation(location) {\n      if (!location) return '';\n      let displayText = location.name;\n      if (location.code) {\n        displayText += ` (${location.code})`;\n      }\n      if (location.type === LocationType.Airport && location.city) {\n        displayText += ` - ${location.city}`;\n      }\n      return displayText;\n    }\n    onSearch() {\n      if (this.searchForm.invalid) {\n        this.markFormGroupTouched(this.searchForm);\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      this.hasSearched = true;\n      const formValue = this.searchForm.value;\n      // Créer la requête de recherche entièrement dynamique\n      const request = {\n        ProductType: formValue.productType,\n        ServiceTypes: formValue.serviceTypes,\n        CheckIn: formValue.departureDate,\n        DepartureLocations: [{\n          id: formValue.departureLocation?.id || '',\n          type: formValue.departureLocationType\n        }],\n        ArrivalLocations: [{\n          id: formValue.arrivalLocation?.id || '',\n          type: formValue.arrivalLocationType\n        }],\n        Passengers: [{\n          type: formValue.passengerType,\n          count: formValue.passengerCount\n        }],\n        showOnlyNonStopFlight: formValue.nonStop,\n        additionalParameters: {\n          getOptionsParameters: {\n            flightBaggageGetOption: formValue.flightBaggageGetOption\n          }\n        },\n        acceptPendingProviders: formValue.acceptPendingProviders,\n        forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n        disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n        calculateFlightFees: formValue.calculateFlightFees,\n        flightClasses: [formValue.flightClass],\n        Culture: formValue.culture,\n        Currency: formValue.currency\n      };\n      this.productService.searchPrice(request).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            console.log('Full API Response:', JSON.stringify(response, null, 2));\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n    }\n    // Utilitaire pour marquer tous les champs comme touchés\n    markFormGroupTouched(formGroup) {\n      Object.values(formGroup.controls).forEach(control => {\n        control.markAsTouched();\n        if (control instanceof FormGroup) {\n          this.markFormGroupTouched(control);\n        }\n      });\n    }\n    // Formater la durée en heures et minutes\n    formatDuration(minutes) {\n      const hours = Math.floor(minutes / 60);\n      const mins = minutes % 60;\n      return `${hours}h ${mins}min`;\n    }\n    // Formater la date pour l'affichage\n    formatDate(dateString) {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('fr-FR', {\n        weekday: 'short',\n        day: '2-digit',\n        month: 'short',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    // Obtenir le prix minimum pour un vol\n    getMinPrice(flight) {\n      if (!flight.offers || flight.offers.length === 0) {\n        return 'N/A';\n      }\n      const minOffer = flight.offers.reduce((min, offer) => offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n      return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n    }\n    // Vérifier si un vol est disponible\n    isFlightAvailable(flight) {\n      return flight.offers && flight.offers.some(offer => offer.availability === 1);\n    }\n    // Naviguer vers la page de détails de l'offre\n    viewOfferDetails(flight) {\n      console.log('Flight object:', flight);\n      if (flight && flight.offers && flight.offers.length > 0) {\n        console.log('Flight offers:', flight.offers);\n        // Vérifier la structure exacte de l'offre\n        const firstOffer = flight.offers[0];\n        console.log('First offer structure:', Object.keys(firstOffer));\n        console.log('Full offer object:', firstOffer);\n        // Essayer de trouver l'ID de l'offre dans différents champs possibles\n        let offerId;\n        // Vérifier d'abord le champ offerId direct\n        if (firstOffer.offerId) {\n          offerId = firstOffer.offerId;\n          console.log('Using offer.offerId:', offerId);\n        }\n        // Vérifier ensuite le tableau offerIds\n        else if (firstOffer.offerIds && firstOffer.offerIds.length > 0 && firstOffer.offerIds[0].offerId) {\n          offerId = firstOffer.offerIds[0].offerId;\n          console.log('Using offer.offerIds[0].offerId:', offerId);\n        }\n        // Vérifier ensuite le champ id\n        else if (firstOffer.id) {\n          offerId = firstOffer.id;\n          console.log('Using offer.id:', offerId);\n        } else if (flight.id) {\n          offerId = flight.id;\n          console.log('Using flight.id as fallback:', offerId);\n        } else {\n          // Générer un ID aléatoire comme dernier recours\n          offerId = 'offer-' + Math.random().toString(36).substring(2, 15);\n          console.log('Generated random offerId as last resort:', offerId);\n        }\n        console.log('Selected offerId:', offerId);\n        // Vérifier si l'ID de recherche est disponible\n        if (!this.lastSearchId) {\n          console.error('SearchId is missing or empty!');\n          // Essayer de trouver un ID alternatif\n          if (flight.id) {\n            console.log('Using flight.id as searchId:', flight.id);\n            this.lastSearchId = flight.id;\n          } else if (offerId) {\n            console.log('Using offerId as searchId:', offerId);\n            this.lastSearchId = offerId;\n          } else {\n            // Générer un ID de recherche aléatoire comme dernier recours\n            this.lastSearchId = 'search-' + Math.random().toString(36).substring(2, 15);\n            console.log('Generated random searchId as last resort:', this.lastSearchId);\n          }\n        }\n        console.log('Navigating to get-offer with searchId:', this.lastSearchId, 'and offerId:', offerId);\n        // S'assurer que les deux IDs sont définis\n        if (!offerId) {\n          alert('Offer ID is missing. Cannot view details.');\n          return;\n        }\n        this.router.navigate(['/get-offer'], {\n          queryParams: {\n            searchId: this.lastSearchId,\n            offerId: offerId\n          }\n        });\n      } else {\n        console.error('No offers available for this flight:', flight);\n      }\n    }\n    // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n    showAllDepartureLocations() {\n      const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.departureLocations = locations;\n        // Forcer l'ouverture du menu d'autocomplétion\n        const input = document.getElementById('departureLocation');\n        if (input) {\n          input.focus();\n          input.dispatchEvent(new Event('input'));\n        }\n      });\n    }\n    // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n    showAllArrivalLocations() {\n      const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.arrivalLocations = locations;\n        // Forcer l'ouverture du menu d'autocomplétion\n        const input = document.getElementById('arrivalLocation');\n        if (input) {\n          input.focus();\n          input.dispatchEvent(new Event('input'));\n        }\n      });\n    }\n    // Échanger les emplacements de départ et d'arrivée\n    swapLocations() {\n      const departureLocation = this.searchForm.get('departureLocation')?.value;\n      const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n      const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n      const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n      this.searchForm.patchValue({\n        departureLocation: arrivalLocation,\n        departureLocationType: arrivalLocationType,\n        arrivalLocation: departureLocation,\n        arrivalLocationType: departureLocationType\n      });\n    }\n    static {\n      this.ɵfac = function SearchPriceComponent_Factory(t) {\n        return new (t || SearchPriceComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SearchPriceComponent,\n        selectors: [[\"app-search-price\"]],\n        decls: 167,\n        vars: 30,\n        consts: [[1, \"search-price-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-illustration\"], [\"src\", \"assets/images/airplane-banner.jpg\", \"alt\", \"Airplane in the sky\"], [1, \"search-content\"], [1, \"search-form-container\"], [1, \"sidebar-logo\"], [1, \"logo-container\"], [1, \"fas\", \"fa-plane-departure\", \"logo-icon\"], [1, \"logo-text\"], [1, \"search-form-header\"], [1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"search-card\"], [\"type\", \"hidden\", \"formControlName\", \"productType\", \"value\", \"3\"], [\"type\", \"hidden\", \"formControlName\", \"serviceTypes\", \"value\", \"['1']\"], [1, \"single-line-form\"], [1, \"form-group\"], [\"for\", \"departureLocation\"], [1, \"input-with-icon\"], [1, \"fas\", \"fa-plane-departure\"], [\"type\", \"text\", \"id\", \"departureLocation\", \"formControlName\", \"departureLocation\", \"placeholder\", \"City or airport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [3, \"displayWith\"], [\"departureAuto\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"form-group\", \"location-type-selector\"], [\"id\", \"departureLocationType\", \"formControlName\", \"departureLocationType\", \"aria-label\", \"Departure Location Type\", 1, \"form-control\"], [3, \"value\"], [1, \"swap-button-container\"], [\"type\", \"button\", 1, \"swap-locations-btn\", 3, \"click\"], [1, \"fas\", \"fa-exchange-alt\"], [\"for\", \"arrivalLocation\"], [1, \"fas\", \"fa-plane-arrival\"], [\"type\", \"text\", \"id\", \"arrivalLocation\", \"formControlName\", \"arrivalLocation\", \"placeholder\", \"City or airport\", 1, \"form-control\", 3, \"matAutocomplete\", \"click\"], [\"arrivalAuto\", \"matAutocomplete\"], [\"id\", \"arrivalLocationType\", \"formControlName\", \"arrivalLocationType\", \"aria-label\", \"Arrival Location Type\", 1, \"form-control\"], [\"for\", \"departureDate\"], [1, \"fas\", \"fa-calendar-alt\"], [\"type\", \"date\", \"id\", \"departureDate\", \"formControlName\", \"departureDate\", 1, \"form-control\", 3, \"min\"], [\"for\", \"passengerCount\"], [1, \"fas\", \"fa-user-friends\"], [\"type\", \"number\", \"id\", \"passengerCount\", \"formControlName\", \"passengerCount\", \"min\", \"1\", \"max\", \"9\", 1, \"form-control\"], [\"for\", \"flightClass\"], [1, \"fas\", \"fa-chair\"], [\"id\", \"flightClass\", \"formControlName\", \"flightClass\", 1, \"form-control\"], [1, \"search-button-container\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [\"class\", \"fas fa-search\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [1, \"advanced-options-container\"], [1, \"fas\", \"fa-cog\"], [1, \"advanced-options\"], [1, \"form-row\"], [\"for\", \"culture\"], [1, \"fas\", \"fa-language\"], [\"id\", \"culture\", \"formControlName\", \"culture\", 1, \"form-control\"], [\"value\", \"en-US\"], [\"value\", \"fr-FR\"], [\"for\", \"currency\"], [1, \"fas\", \"fa-money-bill-wave\"], [\"id\", \"currency\", \"formControlName\", \"currency\", 1, \"form-control\"], [\"value\", \"EUR\"], [\"value\", \"USD\"], [\"for\", \"flightBaggageGetOption\"], [1, \"fas\", \"fa-suitcase\"], [\"id\", \"flightBaggageGetOption\", \"formControlName\", \"flightBaggageGetOption\", 1, \"form-control\"], [1, \"form-row\", \"checkbox-options\"], [1, \"form-group\", \"checkbox-group\"], [1, \"toggle-switch\", \"small\"], [\"type\", \"checkbox\", \"id\", \"acceptPendingProviders\", \"formControlName\", \"acceptPendingProviders\", 1, \"toggle-input\"], [\"for\", \"acceptPendingProviders\", 1, \"toggle-label\"], [1, \"toggle-inner\"], [1, \"toggle-switch-label\"], [\"type\", \"checkbox\", \"id\", \"forceFlightBundlePackage\", \"formControlName\", \"forceFlightBundlePackage\", 1, \"toggle-input\"], [\"for\", \"forceFlightBundlePackage\", 1, \"toggle-label\"], [\"type\", \"checkbox\", \"id\", \"disablePackageOfferTotalPrice\", \"formControlName\", \"disablePackageOfferTotalPrice\", 1, \"toggle-input\"], [\"for\", \"disablePackageOfferTotalPrice\", 1, \"toggle-label\"], [\"type\", \"checkbox\", \"id\", \"calculateFlightFees\", \"formControlName\", \"calculateFlightFees\", 1, \"toggle-input\"], [\"for\", \"calculateFlightFees\", 1, \"toggle-label\"], [\"class\", \"search-results-container\", 4, \"ngIf\"], [1, \"location-option\"], [1, \"location-name\"], [1, \"location-details\"], [\"class\", \"location-code\", 4, \"ngIf\"], [\"class\", \"location-city\", 4, \"ngIf\"], [1, \"location-type\"], [1, \"fas\", 3, \"ngClass\"], [1, \"location-code\"], [1, \"location-city\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"fas\", \"fa-search\"], [1, \"spinner-container\"], [1, \"spinner\"], [1, \"search-results-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"search-results-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-animation\"], [1, \"plane-loader\"], [1, \"fas\", \"fa-plane\"], [1, \"cloud\"], [1, \"error-container\"], [1, \"error-icon\"], [1, \"retry-button\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"search-results-content\"], [1, \"results-header\"], [1, \"results-title\"], [\"class\", \"results-filters\", 4, \"ngIf\"], [1, \"flight-list\"], [\"class\", \"flight-card\", 3, \"unavailable\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"results-count\"], [1, \"results-filters\"], [1, \"filter-option\"], [1, \"fas\", \"fa-sort-amount-down\"], [1, \"filter-select\"], [\"value\", \"price\"], [\"value\", \"duration\"], [\"value\", \"departure\"], [\"value\", \"arrival\"], [1, \"flight-card\"], [1, \"flight-header\"], [1, \"airline-info\"], [1, \"airline-logo-container\"], [\"alt\", \"Airline logo\", \"class\", \"airline-logo\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"fas fa-plane airline-icon\", 4, \"ngIf\"], [1, \"airline-details\"], [1, \"airline-name\"], [1, \"flight-number\"], [1, \"flight-badges\"], [\"class\", \"flight-badge\", 4, \"ngIf\"], [1, \"flight-price\"], [1, \"price-label\"], [1, \"price\"], [\"class\", \"availability\", 4, \"ngIf\"], [1, \"flight-details\"], [1, \"flight-route\"], [1, \"departure\"], [1, \"time\"], [1, \"location\"], [1, \"airport-code\"], [1, \"city-name\"], [1, \"flight-duration\"], [1, \"duration-line\"], [1, \"dot\", \"departure-dot\"], [1, \"line-container\"], [1, \"line\"], [1, \"plane-icon\"], [1, \"dot\", \"arrival-dot\"], [1, \"duration-text\"], [1, \"fas\", \"fa-clock\"], [\"class\", \"stops\", 4, \"ngIf\"], [\"class\", \"stops direct\", 4, \"ngIf\"], [1, \"arrival\"], [1, \"flight-features\"], [1, \"feature\"], [1, \"fas\", \"fa-utensils\"], [1, \"fas\", \"fa-wifi\"], [1, \"flight-actions\"], [1, \"view-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [1, \"select-button\", 3, \"disabled\"], [1, \"fas\", \"fa-check-circle\"], [\"alt\", \"Airline logo\", 1, \"airline-logo\", 3, \"src\"], [1, \"fas\", \"fa-plane\", \"airline-icon\"], [1, \"flight-badge\"], [1, \"fas\", \"fa-bolt\"], [1, \"availability\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"stops\"], [1, \"stop-count\"], [1, \"stops\", \"direct\"], [1, \"no-results\"], [1, \"no-results-icon\"], [1, \"no-results-suggestions\"], [1, \"suggestion\"], [1, \"fas\", \"fa-map-marker-alt\"]],\n        template: function SearchPriceComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n            i0.ɵɵtext(4, \"Find Your Perfect Flight\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\", 4);\n            i0.ɵɵtext(6, \"Search and compare flights to destinations worldwide\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 5);\n            i0.ɵɵelement(8, \"img\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10);\n            i0.ɵɵelement(13, \"i\", 11);\n            i0.ɵɵelementStart(14, \"span\", 12);\n            i0.ɵɵtext(15, \"TravelEase\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"div\", 13)(17, \"h2\");\n            i0.ɵɵtext(18, \"Search Flights\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"p\");\n            i0.ɵɵtext(20, \"Enter your travel details below\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"form\", 14);\n            i0.ɵɵlistener(\"ngSubmit\", function SearchPriceComponent_Template_form_ngSubmit_21_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵelementStart(22, \"div\", 15);\n            i0.ɵɵelement(23, \"input\", 16)(24, \"input\", 17);\n            i0.ɵɵelementStart(25, \"div\", 18)(26, \"div\", 19)(27, \"label\", 20);\n            i0.ɵɵtext(28, \"From\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 21);\n            i0.ɵɵelement(30, \"i\", 22);\n            i0.ɵɵelementStart(31, \"input\", 23);\n            i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_31_listener() {\n              return ctx.showAllDepartureLocations();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"mat-autocomplete\", 24, 25);\n            i0.ɵɵtemplate(34, SearchPriceComponent_mat_option_34_Template, 10, 12, \"mat-option\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(35, SearchPriceComponent_div_35_Template, 3, 0, \"div\", 27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"div\", 28)(37, \"select\", 29)(38, \"option\", 30);\n            i0.ɵɵtext(39, \"Country (1)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"option\", 30);\n            i0.ɵɵtext(41, \"City (2)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"option\", 30);\n            i0.ɵɵtext(43, \"Town (3)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"option\", 30);\n            i0.ɵɵtext(45, \"Village (4)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"option\", 30);\n            i0.ɵɵtext(47, \"Airport (5)\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(48, \"div\", 31)(49, \"button\", 32);\n            i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_49_listener() {\n              return ctx.swapLocations();\n            });\n            i0.ɵɵelement(50, \"i\", 33);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(51, \"div\", 19)(52, \"label\", 34);\n            i0.ɵɵtext(53, \"To\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"div\", 21);\n            i0.ɵɵelement(55, \"i\", 35);\n            i0.ɵɵelementStart(56, \"input\", 36);\n            i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_56_listener() {\n              return ctx.showAllArrivalLocations();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(57, \"mat-autocomplete\", 24, 37);\n            i0.ɵɵtemplate(59, SearchPriceComponent_mat_option_59_Template, 10, 12, \"mat-option\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(60, SearchPriceComponent_div_60_Template, 3, 0, \"div\", 27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"div\", 28)(62, \"select\", 38)(63, \"option\", 30);\n            i0.ɵɵtext(64, \"Country (1)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"option\", 30);\n            i0.ɵɵtext(66, \"City (2)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"option\", 30);\n            i0.ɵɵtext(68, \"Town (3)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"option\", 30);\n            i0.ɵɵtext(70, \"Village (4)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"option\", 30);\n            i0.ɵɵtext(72, \"Airport (5)\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(73, \"div\", 19)(74, \"label\", 39);\n            i0.ɵɵtext(75, \"Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(76, \"div\", 21);\n            i0.ɵɵelement(77, \"i\", 40)(78, \"input\", 41);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(79, SearchPriceComponent_div_79_Template, 3, 0, \"div\", 27);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"div\", 19)(81, \"label\", 42);\n            i0.ɵɵtext(82, \"Passengers\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(83, \"div\", 21);\n            i0.ɵɵelement(84, \"i\", 43)(85, \"input\", 44);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(86, \"div\", 19)(87, \"label\", 45);\n            i0.ɵɵtext(88, \"Class\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(89, \"div\", 21);\n            i0.ɵɵelement(90, \"i\", 46);\n            i0.ɵɵelementStart(91, \"select\", 47);\n            i0.ɵɵtemplate(92, SearchPriceComponent_option_92_Template, 2, 2, \"option\", 26);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(93, \"div\", 48)(94, \"button\", 49);\n            i0.ɵɵtemplate(95, SearchPriceComponent_i_95_Template, 1, 0, \"i\", 50);\n            i0.ɵɵtemplate(96, SearchPriceComponent_span_96_Template, 2, 0, \"span\", 51);\n            i0.ɵɵtemplate(97, SearchPriceComponent_div_97_Template, 2, 0, \"div\", 52);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(98, \"div\", 53)(99, \"details\")(100, \"summary\");\n            i0.ɵɵelement(101, \"i\", 54);\n            i0.ɵɵtext(102, \" Advanced Options \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(103, \"div\", 55)(104, \"div\", 56)(105, \"div\", 19)(106, \"label\", 57);\n            i0.ɵɵtext(107, \"Language\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(108, \"div\", 21);\n            i0.ɵɵelement(109, \"i\", 58);\n            i0.ɵɵelementStart(110, \"select\", 59)(111, \"option\", 60);\n            i0.ɵɵtext(112, \"English (US)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(113, \"option\", 61);\n            i0.ɵɵtext(114, \"Fran\\u00E7ais\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(115, \"div\", 19)(116, \"label\", 62);\n            i0.ɵɵtext(117, \"Currency\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(118, \"div\", 21);\n            i0.ɵɵelement(119, \"i\", 63);\n            i0.ɵɵelementStart(120, \"select\", 64)(121, \"option\", 65);\n            i0.ɵɵtext(122, \"Euro (\\u20AC)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(123, \"option\", 66);\n            i0.ɵɵtext(124, \"Dollar ($)\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(125, \"div\", 19)(126, \"label\", 67);\n            i0.ɵɵtext(127, \"Baggage Options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(128, \"div\", 21);\n            i0.ɵɵelement(129, \"i\", 68);\n            i0.ɵɵelementStart(130, \"select\", 69)(131, \"option\", 30);\n            i0.ɵɵtext(132, \"All options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(133, \"option\", 30);\n            i0.ɵɵtext(134, \"Baggage included only\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(135, \"option\", 30);\n            i0.ɵɵtext(136, \"No baggage only\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(137, \"div\", 70)(138, \"div\", 71)(139, \"div\", 72);\n            i0.ɵɵelement(140, \"input\", 73);\n            i0.ɵɵelementStart(141, \"label\", 74);\n            i0.ɵɵelement(142, \"span\", 75);\n            i0.ɵɵelementStart(143, \"span\", 76);\n            i0.ɵɵtext(144, \"Accept pending providers\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(145, \"div\", 71)(146, \"div\", 72);\n            i0.ɵɵelement(147, \"input\", 77);\n            i0.ɵɵelementStart(148, \"label\", 78);\n            i0.ɵɵelement(149, \"span\", 75);\n            i0.ɵɵelementStart(150, \"span\", 76);\n            i0.ɵɵtext(151, \"Force flight bundle package\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(152, \"div\", 71)(153, \"div\", 72);\n            i0.ɵɵelement(154, \"input\", 79);\n            i0.ɵɵelementStart(155, \"label\", 80);\n            i0.ɵɵelement(156, \"span\", 75);\n            i0.ɵɵelementStart(157, \"span\", 76);\n            i0.ɵɵtext(158, \"Disable package offer total price\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(159, \"div\", 71)(160, \"div\", 72);\n            i0.ɵɵelement(161, \"input\", 81);\n            i0.ɵɵelementStart(162, \"label\", 82);\n            i0.ɵɵelement(163, \"span\", 75);\n            i0.ɵɵelementStart(164, \"span\", 76);\n            i0.ɵɵtext(165, \"Calculate flight fees\");\n            i0.ɵɵelementEnd()()()()()()()()()();\n            i0.ɵɵtemplate(166, SearchPriceComponent_div_166_Template, 4, 3, \"div\", 83);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            const _r0 = i0.ɵɵreference(33);\n            const _r3 = i0.ɵɵreference(58);\n            let tmp_4_0;\n            let tmp_13_0;\n            let tmp_20_0;\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"matAutocomplete\", _r0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.departureLocations);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.searchForm.get(\"departureLocation\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.searchForm.get(\"departureLocation\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 2);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 3);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 4);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 5);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"matAutocomplete\", _r3);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"displayWith\", ctx.displayLocation.bind(ctx));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.arrivalLocations);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.searchForm.get(\"arrivalLocation\")) == null ? null : tmp_13_0.touched));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 2);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 3);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 4);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 5);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"min\", ctx.minDate);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = ctx.searchForm.get(\"departureDate\")) == null ? null : tmp_20_0.invalid) && ((tmp_20_0 = ctx.searchForm.get(\"departureDate\")) == null ? null : tmp_20_0.touched));\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.searchForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(34);\n            i0.ɵɵproperty(\"value\", 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 2);\n            i0.ɵɵadvance(31);\n            i0.ɵɵproperty(\"ngIf\", ctx.hasSearched);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i5.MatAutocomplete, i6.MatOption, i5.MatAutocompleteTrigger],\n        styles: [\"*{box-sizing:border-box}.search-price-container{display:flex;flex-direction:column;padding:10px;width:100%;max-width:100%;margin:0;position:relative;z-index:1;box-sizing:border-box;overflow-x:hidden}@media (min-width: 992px){.search-price-container{padding:0;margin:0}}.search-price-container:before{content:\\\"\\\";position:fixed;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(var(--primary-color-rgb),.03) 0%,rgba(var(--primary-color-rgb),0) 50%),radial-gradient(circle at top right,rgba(var(--secondary-color-rgb),.03) 0%,rgba(var(--secondary-color-rgb),0) 70%);z-index:-1;pointer-events:none}.page-header{display:flex;flex-direction:column;align-items:center;text-align:center;margin-bottom:30px;position:relative;overflow:hidden;border-radius:20px;box-shadow:0 10px 30px #0000001a}.page-header:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(to bottom,rgba(0,0,0,.4),rgba(0,0,0,.1));z-index:1}.header-content{max-width:800px;padding:40px 20px;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:2;color:#fff;text-shadow:0 2px 4px rgba(0,0,0,.3)}.page-title{font-size:36px;font-weight:700;color:#fff;margin-bottom:15px;position:relative;display:inline-block}.page-title:after{content:\\\"\\\";position:absolute;bottom:-10px;left:50%;transform:translate(-50%);width:60px;height:3px;background:white;border-radius:3px}.page-subtitle{font-size:18px;color:#ffffffe6;line-height:1.5;max-width:600px;margin:20px auto 0}.header-illustration{width:100%;height:300px;overflow:hidden}.header-illustration img{width:100%;height:100%;object-fit:cover;object-position:center;transition:transform 10s ease}.page-header:hover .header-illustration img{transform:scale(1.1)}@media (min-width: 992px){.page-header{display:none}.search-price-container{flex-direction:column;align-items:stretch;padding:0;margin:0;width:100%}.search-content{display:flex;flex-direction:column;width:100%;gap:0}.search-form-container{position:sticky;top:0;width:100%;max-height:none;overflow:visible;margin-bottom:20px;padding:0;border-radius:0;box-shadow:0 2px 10px #0000001a;z-index:100;background-color:#fff}.search-results-container{width:100%;padding:20px;max-width:1200px;margin:0 auto}}.search-form-container{background-color:#fff;border-radius:10px;box-shadow:0 5px 15px #0000001a;padding:25px;margin-bottom:30px}.sidebar-logo{display:none}@media (min-width: 992px){.search-form-container{padding:0;margin-bottom:20px;border-radius:0}.sidebar-logo{display:block;background-color:var(--primary-color);color:#fff;padding:15px 20px;text-align:left}.logo-container{display:flex;align-items:center;gap:10px;max-width:1200px;margin:0 auto;width:100%}.logo-icon{font-size:22px}.logo-text{font-size:22px;font-weight:700;letter-spacing:.5px}}.search-form-header{margin-bottom:25px;text-align:center;padding:20px 20px 0}.search-form-header h2{color:var(--primary-color);margin-bottom:8px;font-size:26px;font-weight:600}.search-form-header p{color:#666;font-size:15px}@media (min-width: 992px){.search-form-header{max-width:1200px;margin:0 auto 15px;text-align:left;padding:20px 20px 0}}.search-form{display:flex;flex-direction:column;gap:15px}.form-group{margin-bottom:15px}.form-row{display:flex;gap:15px}.half-width{flex:1}label{display:block;margin-bottom:5px;font-weight:500;color:#333;font-size:14px}.form-control{width:100%;padding:10px 12px;border:1px solid #ddd;border-radius:5px;font-size:14px;transition:border-color .3s}.form-control:focus{border-color:#2989d8;outline:none;box-shadow:0 0 0 2px #2989d833}.checkbox-group{display:flex;align-items:center}.checkbox-group input{margin-right:8px}.search-button{padding:12px;background-color:#2989d8;color:#fff;border:none;border-radius:5px;font-size:16px;font-weight:600;cursor:pointer;transition:background-color .3s;display:flex;justify-content:center;align-items:center;margin-top:10px}.search-button:hover{background-color:#1e5799}.search-button:disabled{background-color:#b3d4f0;cursor:not-allowed}.error-message{color:#e74c3c;font-size:12px;margin-top:5px}details{margin-top:10px;margin-bottom:15px}summary{cursor:pointer;color:#2989d8;font-weight:500;padding:5px 0}summary:hover{text-decoration:underline}.advanced-options{margin-top:10px;padding:15px;background-color:#f8f9fa;border-radius:5px;border:1px solid #eee}::ng-deep .mat-autocomplete-panel{max-height:300px!important}::ng-deep .mat-option{height:auto!important;line-height:1.2!important;padding:10px 16px!important}::ng-deep .mat-option small{color:#666;display:block;margin-top:2px}.search-results-container{background-color:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:12px;box-shadow:0 5px 15px #00000014,0 0 0 1px rgba(var(--primary-color-rgb),.05);padding:20px;position:relative;overflow-x:hidden;border:1px solid rgba(var(--primary-color-rgb),.08);box-sizing:border-box;width:100%}.search-results-container:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:6px;background:linear-gradient(90deg,var(--primary-color),var(--secondary-color));z-index:1}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:60px;text-align:center}.loading-container p{margin-top:20px;color:var(--primary-color);font-weight:500;animation:pulse 1.5s infinite}.spinner{width:30px;height:30px;border:3px solid rgba(var(--primary-color-rgb),.2);border-radius:50%;border-top-color:var(--primary-color);animation:spin 1s cubic-bezier(.6,.2,.4,.8) infinite;box-shadow:0 0 10px rgba(var(--primary-color-rgb),.1)}.spinner.large{width:50px;height:50px;border-width:4px}@keyframes spin{to{transform:rotate(360deg)}}.error-container{padding:30px;background-color:#e74c3c0d;border-radius:16px;text-align:center;border:1px solid rgba(231,76,60,.1);box-shadow:0 5px 15px #e74c3c0d;animation:scaleIn .4s cubic-bezier(.165,.84,.44,1)}.error-container h4{color:#e74c3c;margin-bottom:10px;font-size:18px}.error-container p{color:#0009}.results-header{margin-bottom:30px;position:relative;padding-bottom:15px}.results-header:after{content:\\\"\\\";position:absolute;bottom:0;left:0;width:100%;height:1px;background:linear-gradient(90deg,rgba(var(--primary-color-rgb),.1) 0%,rgba(var(--primary-color-rgb),.05) 50%,rgba(var(--primary-color-rgb),0) 100%)}.results-header h3{color:var(--primary-dark);margin-bottom:8px;font-size:24px;font-weight:600}.results-header p{color:#0009;font-size:14px}.flight-list{display:flex;flex-direction:column;gap:25px}.flight-card{background-color:#fff;border-radius:8px;overflow:hidden;transition:all .3s ease;box-shadow:0 2px 8px #0000000d,0 0 0 1px #00000008;position:relative;width:100%;box-sizing:border-box}.flight-card:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(var(--primary-color-rgb),.02) 0%,rgba(var(--primary-color-rgb),0) 100%);pointer-events:none}.flight-card:hover{transform:translateY(-8px) scale(1.01);box-shadow:0 15px 30px #00000014,0 0 0 1px rgba(var(--primary-color-rgb),.05)}.flight-card.unavailable{opacity:.7;transform:none!important;box-shadow:0 5px 15px #00000008!important}.flight-header{display:flex;justify-content:space-between;align-items:center;padding:15px;background-color:#f9f9f9;border-bottom:1px solid #eaeaea;position:relative;flex-wrap:wrap}.flight-header:after{content:\\\"\\\";position:absolute;bottom:-1px;left:0;width:100%;height:1px;background:linear-gradient(90deg,rgba(var(--primary-color-rgb),0) 0%,rgba(var(--primary-color-rgb),.1) 50%,rgba(var(--primary-color-rgb),0) 100%)}.airline-info{display:flex;align-items:center;gap:15px}.airline-logo{width:40px;height:40px;object-fit:contain;padding:5px;background-color:#fff;border-radius:50%;box-shadow:0 3px 10px #0000000d;transition:transform .3s ease}.flight-card:hover .airline-logo{transform:scale(1.1)}.airline-name{font-weight:600;font-size:16px;color:var(--primary-dark);transition:color .3s ease}.flight-card:hover .airline-name{color:var(--primary-color)}.flight-price{text-align:right;position:relative}.price{font-size:24px;font-weight:700;color:var(--primary-color);display:block;transition:all .3s ease;position:relative}.flight-card:hover .price{color:var(--secondary-color);transform:scale(1.05)}.price:before{content:\\\"\\\";position:absolute;bottom:-3px;left:0;width:0;height:2px;background-color:var(--secondary-color);transition:width .3s ease}.flight-card:hover .price:before{width:100%}.availability{font-size:13px;color:#e74c3c;font-weight:500;margin-top:5px}.flight-details{padding:15px;position:relative;width:100%;box-sizing:border-box}.flight-route{display:flex;flex-wrap:wrap;justify-content:space-between;align-items:center;margin-bottom:15px;position:relative;width:100%}@media (max-width: 768px){.flight-route{flex-direction:column;align-items:flex-start;gap:15px}}.departure,.arrival{flex:1;position:relative;transition:transform .3s ease}.flight-card:hover .departure{transform:translate(-5px)}.flight-card:hover .arrival{transform:translate(5px)}.time{font-size:22px;font-weight:700;margin-bottom:8px;color:var(--primary-dark);display:flex;align-items:center;gap:8px}.time i{color:var(--primary-color);font-size:18px;opacity:0;transform:translateY(5px);transition:all .3s ease}.flight-card:hover .departure .time i,.flight-card:hover .arrival .time i{opacity:1;transform:translateY(0)}.location{font-size:15px;color:#0009;font-weight:500;transition:color .3s ease}.flight-card:hover .location{color:var(--primary-color)}.location small{display:block;font-size:13px;color:#0006;margin-top:3px}.flight-duration{flex:1;text-align:center;padding:0 20px;position:relative;transition:transform .3s ease}.flight-card:hover .flight-duration{transform:translateY(-5px)}.duration-line{display:flex;align-items:center;justify-content:center;margin-bottom:10px;position:relative}.dot{width:10px;height:10px;background-color:var(--primary-color);border-radius:50%;position:relative;z-index:1;transition:all .3s ease;box-shadow:0 0 0 4px rgba(var(--primary-color-rgb),.1)}.flight-card:hover .dot{background-color:var(--secondary-color);transform:scale(1.2);box-shadow:0 0 0 6px rgba(var(--secondary-color-rgb),.15)}.line{flex:1;height:2px;background:linear-gradient(90deg,var(--primary-color),var(--primary-light));margin:0 8px;position:relative;transition:height .3s ease,background .3s ease}.flight-card:hover .line{height:3px;background:linear-gradient(90deg,var(--primary-color),var(--secondary-color))}.duration-text{font-size:16px;font-weight:600;margin-bottom:8px;color:var(--primary-dark);transition:color .3s ease}.flight-card:hover .duration-text{color:var(--primary-color)}.stops{font-size:13px;color:#00000080;font-weight:500;padding:4px 12px;background-color:rgba(var(--primary-color-rgb),.05);border-radius:50px;display:inline-block;transition:all .3s ease}.flight-card:hover .stops{background-color:rgba(var(--primary-color-rgb),.1);color:var(--primary-color)}.flight-info{display:flex;justify-content:space-between;font-size:14px;color:#00000080;margin-top:20px;padding-top:15px;border-top:1px dashed rgba(var(--primary-color-rgb),.1)}.flight-actions{padding:20px 25px;border-top:1px solid rgba(var(--primary-color-rgb),.08);display:flex;gap:15px;justify-content:flex-end;align-items:center;position:relative}.flight-actions:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:1px;background:linear-gradient(90deg,rgba(var(--primary-color-rgb),0) 0%,rgba(var(--primary-color-rgb),.1) 50%,rgba(var(--primary-color-rgb),0) 100%)}.view-details-button{padding:10px 18px;background-color:rgba(var(--primary-color-rgb),.05);color:var(--primary-dark);border:1px solid rgba(var(--primary-color-rgb),.1);border-radius:50px;font-size:14px;font-weight:600;cursor:pointer;transition:all .3s cubic-bezier(.175,.885,.32,1.275);display:flex;align-items:center;gap:8px;position:relative;overflow:hidden}.view-details-button:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(var(--primary-color-rgb),.1) 0%,rgba(var(--primary-color-rgb),0) 100%);opacity:0;transition:opacity .3s ease}.view-details-button i{font-size:16px;transition:transform .3s ease}.view-details-button:hover{background-color:rgba(var(--primary-color-rgb),.1);color:var(--primary-color);transform:translateY(-2px);box-shadow:0 4px 10px rgba(var(--primary-color-rgb),.1)}.view-details-button:hover:before{opacity:1}.view-details-button:hover i{transform:translate(3px)}.select-button{padding:10px 24px;background:linear-gradient(135deg,var(--primary-color),var(--primary-dark));color:#fff;border:none;border-radius:50px;font-size:15px;font-weight:600;cursor:pointer;transition:all .3s cubic-bezier(.175,.885,.32,1.275);display:flex;align-items:center;gap:10px;box-shadow:0 4px 12px rgba(var(--primary-color-rgb),.2);position:relative;overflow:hidden}.select-button:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(255,255,255,.2) 0%,rgba(255,255,255,0) 50%);z-index:1}.select-button:after{content:\\\"\\\";position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle,rgba(255,255,255,.3) 0%,rgba(255,255,255,0) 60%);opacity:0;transform:scale(.5);transition:transform .8s ease,opacity .8s ease;z-index:1}.select-button i{font-size:16px;transition:transform .3s ease;position:relative;z-index:2}.select-button span{position:relative;z-index:2}.select-button:hover{background:linear-gradient(135deg,var(--secondary-color),var(--primary-color));transform:translateY(-3px) scale(1.02);box-shadow:0 8px 20px rgba(var(--primary-color-rgb),.3)}.select-button:hover:after{opacity:1;transform:scale(1)}.select-button:hover i{transform:translate(3px);animation:pulse 1s infinite}.select-button:active{transform:translateY(-1px) scale(1);box-shadow:0 4px 12px rgba(var(--primary-color-rgb),.2)}.select-button:disabled{background:linear-gradient(135deg,#b0b0b0,#d0d0d0);cursor:not-allowed;transform:none;box-shadow:0 2px 8px #0000001a;opacity:.7}\\n\", \".search-form *{box-sizing:border-box;max-width:100%}.search-form input,.search-form select,.search-form button{max-width:100%;overflow:hidden;text-overflow:ellipsis}.search-card{background-color:var(--surface-color, white);border-radius:8px;box-shadow:0 2px 8px #00000026;padding:20px;position:relative;margin-bottom:20px;border:none;display:flex;flex-direction:column;width:100%;box-sizing:border-box;overflow:hidden}.search-card:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:4px;background-color:var(--primary-color);z-index:1}.search-card:hover{box-shadow:0 4px 12px #0003}.single-line-form{display:flex;flex-direction:column;gap:15px;width:100%}@media (min-width: 992px){.search-card{flex-direction:row;flex-wrap:nowrap;align-items:flex-end;padding:15px;height:auto;border-radius:0;box-shadow:none;max-width:1200px;margin:0 auto}.single-line-form{flex-direction:row;align-items:flex-end;flex-wrap:nowrap;gap:10px}.single-line-form .form-group:nth-child(1),.single-line-form .form-group:nth-child(5){flex:1.5;z-index:4}.single-line-form .form-group:nth-child(7),.single-line-form .form-group:nth-child(8),.single-line-form .form-group:nth-child(9){flex:.8}.single-line-form .swap-button-container{flex:0 0 auto;margin:0;padding:0 5px;z-index:6}.form-group{min-width:0;margin-bottom:0;flex:1;position:relative;z-index:3;padding:0 5px}.form-group.checkbox-group{flex:0 0 auto}.search-button-container{flex:0 0 auto;margin-top:0;margin-left:10px;text-align:center;padding-left:5px}.search-button{width:auto;white-space:nowrap;padding:10px 20px;height:40px}}.trip-type-selector{display:flex;margin-bottom:20px;position:relative;border-bottom:1px solid #e7e7e7;padding-bottom:0}.trip-type-option{display:flex;align-items:center;gap:8px;padding:12px 20px;cursor:pointer;transition:all .2s ease;font-weight:500;color:#333;background-color:transparent;border:none;border-bottom:3px solid transparent;position:relative;min-width:100px;justify-content:center}.trip-type-option i{color:#666;font-size:16px;transition:color .2s ease}.trip-type-option span{transition:color .2s ease}.trip-type-option.selected{color:var(--primary-color);font-weight:600;border-bottom:3px solid var(--primary-color);background-color:transparent}.trip-type-option.selected i{color:var(--primary-color)}.trip-type-option:not(.selected):hover{color:var(--primary-color);border-bottom-color:rgba(var(--primary-color-rgb),.3)}.trip-type-option:not(.selected):hover i{color:var(--primary-color)}.trip-type-option.disabled{opacity:.5;cursor:not-allowed}.form-row{display:flex;gap:16px;margin-bottom:20px;position:relative}.locations-row{position:relative}.form-group{flex:1;position:relative}.location-type-selector{display:none}label{display:block;margin-bottom:6px;font-weight:500;color:#333;font-size:14px}.input-with-icon{position:relative;display:flex;align-items:center}.input-with-icon i{position:absolute;left:12px;color:#666;font-size:16px;z-index:2}.form-control{width:100%;padding:12px 12px 12px 40px;border:1px solid #e7e7e7;border-radius:4px;font-size:15px;transition:border-color .2s ease,box-shadow .2s ease;background-color:#fff;color:#333;height:40px}.form-control:focus{outline:none;border-color:var(--primary-color);box-shadow:0 0 0 2px rgba(var(--primary-color-rgb),.2)}.form-control::placeholder{color:#999}.form-group:focus-within label{color:var(--primary-color)}.form-group:focus-within .input-with-icon i{color:var(--primary-color)}@media (min-width: 992px){.single-line-form .form-group{margin-bottom:0}.single-line-form label{font-size:12px;margin-bottom:4px}.single-line-form .form-control,.single-line-form .input-with-icon i{font-size:14px}.single-line-form .error-message{position:absolute;font-size:11px;bottom:-18px;left:0;white-space:nowrap}}.swap-button-container{display:flex;align-items:flex-end;justify-content:center;padding-bottom:10px;position:relative;z-index:5}.swap-locations-btn{width:32px;height:32px;border-radius:50%;background-color:var(--primary-color);color:#fff;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;box-shadow:0 2px 5px #0003;transition:all .3s ease;position:relative}.swap-locations-btn:hover{background-color:var(--primary-dark);transform:rotate(180deg)}.swap-locations-btn:active{transform:scale(.95) rotate(180deg)}.swap-locations-btn i{font-size:16px}@media (min-width: 992px){.swap-button-container{padding-bottom:10px}}.location-option{display:flex;flex-direction:column;gap:4px}.location-name{font-weight:500}.location-details{display:flex;gap:12px;font-size:13px;color:#0009}.location-code{font-weight:500}.location-type{display:flex;align-items:center;gap:4px}.location-type i{font-size:12px;color:#2989d8}.toggle-switch{position:relative;display:inline-flex;align-items:center;cursor:pointer}.toggle-input{opacity:0;width:0;height:0;position:absolute}.toggle-label{display:flex;align-items:center;gap:12px;cursor:pointer}.toggle-inner{position:relative;display:inline-block;width:50px;height:24px;background-color:#0000001f;border-radius:12px;transition:background-color .2s ease}.toggle-inner:before{content:\\\"\\\";position:absolute;left:2px;top:2px;width:20px;height:20px;background-color:#fff;border-radius:50%;transition:transform .2s ease;box-shadow:0 1px 3px #0003}.toggle-input:checked+.toggle-label .toggle-inner{background-color:#2989d8}.toggle-input:checked+.toggle-label .toggle-inner:before{transform:translate(26px)}.toggle-switch-label{font-size:14px;color:#000c}.toggle-switch.small .toggle-inner{width:40px;height:20px}.toggle-switch.small .toggle-inner:before{width:16px;height:16px}.toggle-switch.small .toggle-input:checked+.toggle-label .toggle-inner:before{transform:translate(20px)}.search-button-container{margin-top:20px;display:flex;justify-content:center;position:relative}@media (min-width: 992px){.search-button-container{margin-top:0}}.search-button{background-color:var(--primary-color);color:#fff;border:none;border-radius:4px;padding:12px 24px;font-size:16px;font-weight:600;cursor:pointer;transition:background-color .2s ease;display:flex;align-items:center;gap:8px;min-width:140px;justify-content:center;box-shadow:0 1px 3px #0000001a}@media (min-width: 992px){.search-button{padding:0 20px;font-size:15px;min-width:100px;height:40px}.single-line-form .search-button-container{margin-top:21px}}.search-button:hover:not(:disabled){background-color:var(--primary-dark)}.search-button:active:not(:disabled){transform:translateY(1px)}.search-button:disabled{background-color:#ccc;cursor:not-allowed}.search-button i{font-size:16px}.advanced-options-container{margin-top:25px;position:relative}.advanced-options-container:before{content:\\\"\\\";position:absolute;top:-10px;left:0;width:100%;height:1px;background:linear-gradient(90deg,rgba(var(--primary-color-rgb),0) 0%,rgba(var(--primary-color-rgb),.1) 50%,rgba(var(--primary-color-rgb),0) 100%)}.advanced-options-container summary{cursor:pointer;color:var(--primary-color);font-weight:600;display:flex;align-items:center;justify-content:center;gap:10px;padding:12px 20px;transition:all .3s ease;outline:none;border-radius:50px;background-color:rgba(var(--primary-color-rgb),.05);box-shadow:0 2px 8px rgba(var(--primary-color-rgb),.05);width:-moz-fit-content;width:fit-content;margin:0 auto;position:relative;overflow:hidden}.advanced-options-container summary:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,rgba(var(--primary-color-rgb),.1) 0%,rgba(var(--primary-color-rgb),0) 100%);opacity:0;transition:opacity .3s ease}.advanced-options-container summary:hover{color:var(--secondary-color);background-color:rgba(var(--primary-color-rgb),.08);transform:translateY(-2px);box-shadow:0 4px 12px rgba(var(--primary-color-rgb),.1)}.advanced-options-container summary:hover:before{opacity:1}.advanced-options-container summary i{font-size:18px;transition:transform .3s ease}.advanced-options-container[open] summary i{transform:rotate(180deg)}.advanced-options{margin-top:20px;padding:25px;background-color:rgba(var(--primary-color-rgb),.03);border-radius:16px;border:1px solid rgba(var(--primary-color-rgb),.08);box-shadow:inset 0 1px 8px rgba(var(--primary-color-rgb),.05),0 5px 15px #00000008;position:relative;animation:scaleIn .4s cubic-bezier(.165,.84,.44,1);overflow:hidden}.advanced-options:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:radial-gradient(circle at top right,rgba(var(--primary-color-rgb),.05) 0%,rgba(var(--primary-color-rgb),0) 70%),radial-gradient(circle at bottom left,rgba(var(--secondary-color-rgb),.05) 0%,rgba(var(--secondary-color-rgb),0) 70%);pointer-events:none}.checkbox-options{display:flex;flex-wrap:wrap;gap:20px}.checkbox-options .form-group{flex:1 0 45%;transition:transform .3s ease}.checkbox-options .form-group:hover{transform:translateY(-2px)}.error-message{color:#e74c3c;font-size:13px;margin-top:6px;display:flex;align-items:center;gap:6px}.error-message i{font-size:14px}.spinner-container{display:flex;align-items:center;justify-content:center}.spinner{width:20px;height:20px;border:2px solid rgba(255,255,255,.3);border-radius:50%;border-top-color:#fff;animation:spin .8s linear infinite}@keyframes spin{to{transform:rotate(360deg)}}.search-results-container{background-color:#fff;border-radius:16px;box-shadow:0 8px 24px #0000001f;padding:24px;margin-top:24px}.loading-animation{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px}.plane-loader{position:relative;width:200px;height:100px;margin-bottom:24px}.plane-loader i{position:absolute;font-size:32px;color:#2989d8;animation:fly 3s infinite linear;top:40%;left:0}.cloud{position:absolute;width:50px;height:20px;background-color:#0000000d;border-radius:20px}.cloud:nth-child(2){top:20%;left:20%;animation:cloud 8s infinite linear}.cloud:nth-child(3){top:60%;left:40%;animation:cloud 6s infinite linear}.cloud:nth-child(4){top:40%;left:60%;animation:cloud 10s infinite linear}@keyframes fly{0%{transform:translate(0) rotate(0)}to{transform:translate(200px) rotate(0)}}@keyframes cloud{0%{transform:translate(0)}to{transform:translate(-200px)}}.loading-animation p{color:#0009;font-size:16px;font-weight:500}.results-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid rgba(0,0,0,.08)}.results-title h3{font-size:20px;font-weight:600;color:#000c;margin-bottom:4px}.results-count{font-weight:600;color:#2989d8}.filter-option{display:flex;align-items:center;gap:8px;color:#0009}.filter-select{padding:8px 12px;border:1px solid rgba(0,0,0,.12);border-radius:6px;font-size:14px;color:#000c;background-color:#fff}.flight-card{background-color:#fff;border-radius:12px;box-shadow:0 4px 16px #00000014;overflow:hidden;transition:transform .3s ease,box-shadow .3s ease;margin-bottom:20px;border:1px solid rgba(0,0,0,.05)}.flight-card:hover{transform:translateY(-4px);box-shadow:0 8px 24px #0000001f}.flight-header{display:flex;justify-content:space-between;align-items:center;padding:16px 20px;background-color:#00000005;border-bottom:1px solid rgba(0,0,0,.05)}.airline-info{display:flex;align-items:center;gap:12px}.airline-logo-container{width:40px;height:40px;display:flex;align-items:center;justify-content:center;background-color:#fff;border-radius:8px;box-shadow:0 2px 6px #0000001a}.airline-logo{max-width:32px;max-height:32px;object-fit:contain}.airline-icon{font-size:20px;color:#2989d8}.airline-details{display:flex;flex-direction:column}.airline-name{font-weight:600;color:#000c;font-size:15px}.flight-number{font-size:13px;color:#0009}.flight-badges{display:flex;gap:8px}.flight-badge{display:flex;align-items:center;gap:4px;padding:4px 8px;background-color:#2989d81a;color:#2989d8;border-radius:4px;font-size:12px;font-weight:500}.flight-price{text-align:right}.price-label{display:block;font-size:12px;color:#0009;margin-bottom:4px}.price{font-size:22px;font-weight:700;color:#2989d8}.availability{display:flex;align-items:center;gap:4px;color:#e74c3c;font-size:12px;margin-top:4px}.flight-details{padding:20px}.flight-route{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.departure,.arrival{flex:1}.time{font-size:20px;font-weight:700;color:#000c;margin-bottom:6px}.location{display:flex;flex-direction:column}.airport-code{font-weight:600;color:#000000b3;font-size:15px}.city-name{color:#0009;font-size:13px}.flight-duration{flex:1;text-align:center;padding:0 20px}.duration-line{position:relative;display:flex;align-items:center;justify-content:center;margin-bottom:10px}.dot{width:10px;height:10px;background-color:#2989d8;border-radius:50%;z-index:1}.departure-dot{background-color:#4caf50}.arrival-dot{background-color:#f44336}.line-container{flex:1;position:relative;height:20px;display:flex;align-items:center}.line{width:100%;height:2px;background-color:#2989d8}.plane-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#2989d8;font-size:14px}.duration-text{display:flex;align-items:center;justify-content:center;gap:6px;font-size:14px;font-weight:600;color:#000000b3;margin-bottom:6px}.stops{font-size:13px;color:#0009}.stop-count{font-weight:600;color:#f44336}.stops.direct{color:#4caf50;font-weight:500}.flight-features{display:flex;gap:16px;margin-top:16px;padding-top:16px;border-top:1px dashed rgba(0,0,0,.1)}.feature{display:flex;align-items:center;gap:6px;font-size:13px;color:#000000b3}.feature i{color:#2989d8;font-size:14px}.flight-actions{display:flex;gap:12px;justify-content:flex-end;padding:16px 20px;background-color:#00000005;border-top:1px solid rgba(0,0,0,.05)}.view-details-button{display:flex;align-items:center;gap:6px;padding:10px 16px;background-color:#0000000d;color:#000000b3;border:none;border-radius:6px;font-size:14px;font-weight:500;cursor:pointer;transition:all .2s ease}.view-details-button:hover{background-color:#0000001a}.select-button{display:flex;align-items:center;gap:6px;padding:10px 16px;background-color:#2989d8;color:#fff;border:none;border-radius:6px;font-size:14px;font-weight:500;cursor:pointer;transition:all .2s ease;box-shadow:0 2px 8px #2989d84d}.select-button:hover{background-color:#1e5799;transform:translateY(-2px);box-shadow:0 4px 12px #2989d866}.select-button:disabled{background-color:#0000001a;color:#0006;cursor:not-allowed;transform:none;box-shadow:none}.no-results{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 20px;text-align:center}.no-results-icon{width:80px;height:80px;background-color:#0000000d;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:20px}.no-results-icon i{font-size:32px;color:#0000004d}.no-results h3{font-size:20px;font-weight:600;color:#000c;margin-bottom:8px}.no-results p{color:#0009;margin-bottom:24px;max-width:500px}.no-results-suggestions{display:flex;flex-wrap:wrap;gap:16px;justify-content:center}.suggestion{display:flex;align-items:center;gap:8px;padding:10px 16px;background-color:#0000000d;border-radius:8px;color:#000000b3;font-size:14px}.suggestion i{color:#2989d8}.error-container{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 20px;text-align:center}.error-icon{width:80px;height:80px;background-color:#e74c3c1a;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:20px}.error-icon i{font-size:32px;color:#e74c3c}.error-container h3{font-size:20px;font-weight:600;color:#000c;margin-bottom:8px}.error-container .error-message{color:#0009;margin-bottom:24px;font-size:15px;justify-content:center}.retry-button{display:flex;align-items:center;gap:8px;padding:12px 20px;background-color:#2989d8;color:#fff;border:none;border-radius:8px;font-size:15px;font-weight:500;cursor:pointer;transition:all .2s ease;box-shadow:0 2px 8px #2989d84d}.retry-button:hover{background-color:#1e5799;transform:translateY(-2px);box-shadow:0 4px 12px #2989d866}@media (max-width: 768px){.form-row{flex-direction:column;gap:16px}.swap-locations-btn{display:none}.flight-route{flex-direction:column;gap:20px}.departure,.arrival{text-align:center}.flight-duration{margin:16px 0}.flight-header{flex-direction:column;gap:16px}.airline-info{width:100%;justify-content:center}.flight-badges{justify-content:center}.flight-price{text-align:center;width:100%}.flight-features{flex-direction:column;gap:12px;align-items:center}.flight-actions{flex-direction:column}.view-details-button,.select-button{width:100%;justify-content:center}}@media (max-width: 576px){.page-header{flex-direction:column;gap:24px}.header-content{text-align:center}.page-title{font-size:1.75rem}.checkbox-options .form-group{flex:1 0 100%}}\\n\"],\n        encapsulation: 2\n      });\n    }\n  }\n  return SearchPriceComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}