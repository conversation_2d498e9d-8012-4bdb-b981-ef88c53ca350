{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8080/auth';\n    this.tokenKey = 'auth_token';\n    this.userKey = 'user_info';\n    this.isAuthenticatedSubject = new BehaviorSubject(this.hasToken());\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n  }\n  login(credentials) {\n    return this.http.post(`${this.apiUrl}/login`, credentials).pipe(tap(response => {\n      if (response && response.body && response.body.token) {\n        this.setToken(response.body.token);\n        this.setUserInfo(response.body.userInfo);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }));\n  }\n  logout() {\n    localStorage.removeItem(this.tokenKey);\n    localStorage.removeItem(this.userKey);\n    this.isAuthenticatedSubject.next(false);\n  }\n  getToken() {\n    return localStorage.getItem(this.tokenKey);\n  }\n  setToken(token) {\n    localStorage.setItem(this.tokenKey, token);\n  }\n  setUserInfo(userInfo) {\n    localStorage.setItem(this.userKey, JSON.stringify(userInfo));\n  }\n  getUserInfo() {\n    const userInfo = localStorage.getItem(this.userKey);\n    return userInfo ? JSON.parse(userInfo) : null;\n  }\n  hasToken() {\n    return !!this.getToken();\n  }\n  isAuthenticated() {\n    return this.hasToken();\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "AuthService", "constructor", "http", "apiUrl", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isAuthenticatedSubject", "hasToken", "isAuthenticated$", "asObservable", "login", "credentials", "post", "pipe", "response", "body", "token", "setToken", "setUserInfo", "userInfo", "next", "logout", "localStorage", "removeItem", "getToken", "getItem", "setItem", "JSON", "stringify", "getUserInfo", "parse", "isAuthenticated", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap } from 'rxjs';\nimport { AuthRequest } from '../models/auth-request.model';\nimport { AuthResponse } from '../models/auth-response.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private apiUrl = 'http://localhost:8080/auth';\n  private tokenKey = 'auth_token';\n  private userKey = 'user_info';\n\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasToken());\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  constructor(private http: HttpClient) { }\n\n  login(credentials: AuthRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/login`, credentials)\n      .pipe(\n        tap(response => {\n          if (response && response.body && response.body.token) {\n            this.setToken(response.body.token);\n            this.setUserInfo(response.body.userInfo);\n            this.isAuthenticatedSubject.next(true);\n          }\n        })\n      );\n  }\n\n  logout(): void {\n    localStorage.removeItem(this.tokenKey);\n    localStorage.removeItem(this.userKey);\n    this.isAuthenticatedSubject.next(false);\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem(this.tokenKey);\n  }\n\n  private setToken(token: string): void {\n    localStorage.setItem(this.tokenKey, token);\n  }\n\n  private setUserInfo(userInfo: any): void {\n    localStorage.setItem(this.userKey, JSON.stringify(userInfo));\n  }\n\n  getUserInfo(): any {\n    const userInfo = localStorage.getItem(this.userKey);\n    return userInfo ? JSON.parse(userInfo) : null;\n  }\n\n  private hasToken(): boolean {\n    return !!this.getToken();\n  }\n\n  isAuthenticated(): boolean {\n    return this.hasToken();\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,QAAQ,MAAM;;;AAOvD,OAAM,MAAOC,WAAW;EAQtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAPhB,KAAAC,MAAM,GAAG,4BAA4B;IACrC,KAAAC,QAAQ,GAAG,YAAY;IACvB,KAAAC,OAAO,GAAG,WAAW;IAErB,KAAAC,sBAAsB,GAAG,IAAIR,eAAe,CAAU,IAAI,CAACS,QAAQ,EAAE,CAAC;IACvE,KAAAC,gBAAgB,GAAG,IAAI,CAACF,sBAAsB,CAACG,YAAY,EAAE;EAE5B;EAExCC,KAAKA,CAACC,WAAwB;IAC5B,OAAO,IAAI,CAACT,IAAI,CAACU,IAAI,CAAe,GAAG,IAAI,CAACT,MAAM,QAAQ,EAAEQ,WAAW,CAAC,CACrEE,IAAI,CACHd,GAAG,CAACe,QAAQ,IAAG;MACb,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACC,KAAK,EAAE;QACpD,IAAI,CAACC,QAAQ,CAACH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC;QAClC,IAAI,CAACE,WAAW,CAACJ,QAAQ,CAACC,IAAI,CAACI,QAAQ,CAAC;QACxC,IAAI,CAACb,sBAAsB,CAACc,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,CACH;EACL;EAEAC,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,IAAI,CAACnB,QAAQ,CAAC;IACtCkB,YAAY,CAACC,UAAU,CAAC,IAAI,CAAClB,OAAO,CAAC;IACrC,IAAI,CAACC,sBAAsB,CAACc,IAAI,CAAC,KAAK,CAAC;EACzC;EAEAI,QAAQA,CAAA;IACN,OAAOF,YAAY,CAACG,OAAO,CAAC,IAAI,CAACrB,QAAQ,CAAC;EAC5C;EAEQa,QAAQA,CAACD,KAAa;IAC5BM,YAAY,CAACI,OAAO,CAAC,IAAI,CAACtB,QAAQ,EAAEY,KAAK,CAAC;EAC5C;EAEQE,WAAWA,CAACC,QAAa;IAC/BG,YAAY,CAACI,OAAO,CAAC,IAAI,CAACrB,OAAO,EAAEsB,IAAI,CAACC,SAAS,CAACT,QAAQ,CAAC,CAAC;EAC9D;EAEAU,WAAWA,CAAA;IACT,MAAMV,QAAQ,GAAGG,YAAY,CAACG,OAAO,CAAC,IAAI,CAACpB,OAAO,CAAC;IACnD,OAAOc,QAAQ,GAAGQ,IAAI,CAACG,KAAK,CAACX,QAAQ,CAAC,GAAG,IAAI;EAC/C;EAEQZ,QAAQA,CAAA;IACd,OAAO,CAAC,CAAC,IAAI,CAACiB,QAAQ,EAAE;EAC1B;EAEAO,eAAeA,CAAA;IACb,OAAO,IAAI,CAACxB,QAAQ,EAAE;EACxB;;;uBApDWP,WAAW,EAAAgC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXnC,WAAW;MAAAoC,OAAA,EAAXpC,WAAW,CAAAqC,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}