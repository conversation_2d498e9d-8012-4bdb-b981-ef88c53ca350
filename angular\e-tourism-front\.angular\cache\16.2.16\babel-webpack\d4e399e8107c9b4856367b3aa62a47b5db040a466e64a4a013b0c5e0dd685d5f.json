{"ast": null, "code": "import { of } from 'rxjs';\nimport { map, shareReplay, catchError, tap, retry } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CountryService {\n  constructor(http) {\n    this.http = http;\n    this.countriesCache = [];\n    this.API_URL = 'https://restcountries.com/v3.1/all';\n    // Initialiser la liste des pays\n    this.countries$ = this.http.get(`${this.API_URL}?fields=name,cca2,flags,translations`).pipe(\n    // Réessayer 3 fois en cas d'échec\n    retry(3),\n    // Transformer les données de l'API en format Country\n    map(countries => countries.map(country => {\n      // Utiliser la traduction française si disponible\n      const name = country.translations?.fra?.common || country.name.common;\n      return {\n        name: name,\n        code: country.cca2,\n        // Utiliser SVG si disponible, sinon PNG, sinon une URL par défaut\n        flag: country.flags?.svg || country.flags?.png || `https://flagcdn.com/${country.cca2.toLowerCase()}.svg`\n      };\n    })),\n    // Trier les pays par nom\n    map(countries => countries.sort((a, b) => a.name.localeCompare(b.name))),\n    // Journaliser le nombre de pays récupérés\n    tap(countries => console.log(`${countries.length} pays récupérés depuis l'API`)),\n    // Mettre en cache le résultat\n    shareReplay(1),\n    // Gérer les erreurs\n    catchError(error => {\n      console.error('Erreur lors de la récupération des pays:', error);\n      // Utiliser la liste complète des pays par défaut\n      const defaultCountries = this.getAllCountries();\n      console.log(`Utilisation de la liste par défaut de ${defaultCountries.length} pays`);\n      return of(defaultCountries);\n    }));\n    // Mettre en cache la liste des pays\n    this.countries$.subscribe(countries => {\n      this.countriesCache = countries;\n    });\n  }\n  /**\n   * Récupérer la liste des pays\n   * @returns Observable de la liste des pays\n   */\n  getCountries() {\n    return this.countries$;\n  }\n  /**\n   * Récupérer un pays par son code\n   * @param code Code du pays (2 lettres)\n   * @returns Le pays correspondant ou undefined\n   */\n  getCountryByCode(code) {\n    return this.countriesCache.find(country => country.code === code);\n  }\n  /**\n   * Récupérer un pays par son nom\n   * @param name Nom du pays\n   * @returns Le pays correspondant ou undefined\n   */\n  getCountryByName(name) {\n    return this.countriesCache.find(country => country.name.toLowerCase() === name.toLowerCase());\n  }\n  /**\n   * Liste par défaut des pays en cas d'erreur de l'API\n   * @returns Liste des pays les plus courants\n   */\n  getDefaultCountries() {\n    return [{\n      name: 'France',\n      code: 'FR',\n      flag: 'https://flagcdn.com/fr.svg'\n    }, {\n      name: 'Germany',\n      code: 'DE',\n      flag: 'https://flagcdn.com/de.svg'\n    }, {\n      name: 'United Kingdom',\n      code: 'GB',\n      flag: 'https://flagcdn.com/gb.svg'\n    }, {\n      name: 'United States',\n      code: 'US',\n      flag: 'https://flagcdn.com/us.svg'\n    }, {\n      name: 'Spain',\n      code: 'ES',\n      flag: 'https://flagcdn.com/es.svg'\n    }, {\n      name: 'Italy',\n      code: 'IT',\n      flag: 'https://flagcdn.com/it.svg'\n    }, {\n      name: 'Canada',\n      code: 'CA',\n      flag: 'https://flagcdn.com/ca.svg'\n    }, {\n      name: 'Australia',\n      code: 'AU',\n      flag: 'https://flagcdn.com/au.svg'\n    }, {\n      name: 'Japan',\n      code: 'JP',\n      flag: 'https://flagcdn.com/jp.svg'\n    }, {\n      name: 'China',\n      code: 'CN',\n      flag: 'https://flagcdn.com/cn.svg'\n    }, {\n      name: 'Brazil',\n      code: 'BR',\n      flag: 'https://flagcdn.com/br.svg'\n    }, {\n      name: 'India',\n      code: 'IN',\n      flag: 'https://flagcdn.com/in.svg'\n    }, {\n      name: 'Russia',\n      code: 'RU',\n      flag: 'https://flagcdn.com/ru.svg'\n    }, {\n      name: 'South Africa',\n      code: 'ZA',\n      flag: 'https://flagcdn.com/za.svg'\n    }, {\n      name: 'Mexico',\n      code: 'MX',\n      flag: 'https://flagcdn.com/mx.svg'\n    }, {\n      name: 'Argentina',\n      code: 'AR',\n      flag: 'https://flagcdn.com/ar.svg'\n    }, {\n      name: 'Turkey',\n      code: 'TR',\n      flag: 'https://flagcdn.com/tr.svg'\n    }, {\n      name: 'Egypt',\n      code: 'EG',\n      flag: 'https://flagcdn.com/eg.svg'\n    }, {\n      name: 'Morocco',\n      code: 'MA',\n      flag: 'https://flagcdn.com/ma.svg'\n    }, {\n      name: 'Tunisia',\n      code: 'TN',\n      flag: 'https://flagcdn.com/tn.svg'\n    }];\n  }\n  static {\n    this.ɵfac = function CountryService_Factory(t) {\n      return new (t || CountryService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CountryService,\n      factory: CountryService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "map", "shareReplay", "catchError", "tap", "retry", "CountryService", "constructor", "http", "countriesCache", "API_URL", "countries$", "get", "pipe", "countries", "country", "name", "translations", "fra", "common", "code", "cca2", "flag", "flags", "svg", "png", "toLowerCase", "sort", "a", "b", "localeCompare", "console", "log", "length", "error", "defaultCountries", "getAllCountries", "subscribe", "getCountries", "getCountryByCode", "find", "getCountryByName", "getDefaultCountries", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\country.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, of } from 'rxjs';\nimport { map, shareReplay, catchError, tap, retry } from 'rxjs/operators';\n\nexport interface Country {\n  name: string;\n  code: string;\n  flag: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class CountryService {\n  private countries$: Observable<Country[]>;\n  private countriesCache: Country[] = [];\n  private readonly API_URL = 'https://restcountries.com/v3.1/all';\n\n  constructor(private http: HttpClient) {\n    // Initialiser la liste des pays\n    this.countries$ = this.http.get<any[]>(`${this.API_URL}?fields=name,cca2,flags,translations`)\n      .pipe(\n        // Réessayer 3 fois en cas d'échec\n        retry(3),\n        // Transformer les données de l'API en format Country\n        map(countries => countries.map(country => {\n          // Utiliser la traduction française si disponible\n          const name = country.translations?.fra?.common || country.name.common;\n          return {\n            name: name,\n            code: country.cca2,\n            // Utiliser SVG si disponible, sinon PNG, sinon une URL par défaut\n            flag: country.flags?.svg || country.flags?.png || `https://flagcdn.com/${country.cca2.toLowerCase()}.svg`\n          };\n        })),\n        // Trier les pays par nom\n        map(countries => countries.sort((a, b) => a.name.localeCompare(b.name))),\n        // Journaliser le nombre de pays récupérés\n        tap(countries => console.log(`${countries.length} pays récupérés depuis l'API`)),\n        // Mettre en cache le résultat\n        shareReplay(1),\n        // Gérer les erreurs\n        catchError(error => {\n          console.error('Erreur lors de la récupération des pays:', error);\n          // Utiliser la liste complète des pays par défaut\n          const defaultCountries = this.getAllCountries();\n          console.log(`Utilisation de la liste par défaut de ${defaultCountries.length} pays`);\n          return of(defaultCountries);\n        })\n      );\n\n    // Mettre en cache la liste des pays\n    this.countries$.subscribe(countries => {\n      this.countriesCache = countries;\n    });\n  }\n\n  /**\n   * Récupérer la liste des pays\n   * @returns Observable de la liste des pays\n   */\n  getCountries(): Observable<Country[]> {\n    return this.countries$;\n  }\n\n  /**\n   * Récupérer un pays par son code\n   * @param code Code du pays (2 lettres)\n   * @returns Le pays correspondant ou undefined\n   */\n  getCountryByCode(code: string): Country | undefined {\n    return this.countriesCache.find(country => country.code === code);\n  }\n\n  /**\n   * Récupérer un pays par son nom\n   * @param name Nom du pays\n   * @returns Le pays correspondant ou undefined\n   */\n  getCountryByName(name: string): Country | undefined {\n    return this.countriesCache.find(country =>\n      country.name.toLowerCase() === name.toLowerCase()\n    );\n  }\n\n  /**\n   * Liste par défaut des pays en cas d'erreur de l'API\n   * @returns Liste des pays les plus courants\n   */\n  private getDefaultCountries(): Country[] {\n    return [\n      { name: 'France', code: 'FR', flag: 'https://flagcdn.com/fr.svg' },\n      { name: 'Germany', code: 'DE', flag: 'https://flagcdn.com/de.svg' },\n      { name: 'United Kingdom', code: 'GB', flag: 'https://flagcdn.com/gb.svg' },\n      { name: 'United States', code: 'US', flag: 'https://flagcdn.com/us.svg' },\n      { name: 'Spain', code: 'ES', flag: 'https://flagcdn.com/es.svg' },\n      { name: 'Italy', code: 'IT', flag: 'https://flagcdn.com/it.svg' },\n      { name: 'Canada', code: 'CA', flag: 'https://flagcdn.com/ca.svg' },\n      { name: 'Australia', code: 'AU', flag: 'https://flagcdn.com/au.svg' },\n      { name: 'Japan', code: 'JP', flag: 'https://flagcdn.com/jp.svg' },\n      { name: 'China', code: 'CN', flag: 'https://flagcdn.com/cn.svg' },\n      { name: 'Brazil', code: 'BR', flag: 'https://flagcdn.com/br.svg' },\n      { name: 'India', code: 'IN', flag: 'https://flagcdn.com/in.svg' },\n      { name: 'Russia', code: 'RU', flag: 'https://flagcdn.com/ru.svg' },\n      { name: 'South Africa', code: 'ZA', flag: 'https://flagcdn.com/za.svg' },\n      { name: 'Mexico', code: 'MX', flag: 'https://flagcdn.com/mx.svg' },\n      { name: 'Argentina', code: 'AR', flag: 'https://flagcdn.com/ar.svg' },\n      { name: 'Turkey', code: 'TR', flag: 'https://flagcdn.com/tr.svg' },\n      { name: 'Egypt', code: 'EG', flag: 'https://flagcdn.com/eg.svg' },\n      { name: 'Morocco', code: 'MA', flag: 'https://flagcdn.com/ma.svg' },\n      { name: 'Tunisia', code: 'TN', flag: 'https://flagcdn.com/tn.svg' }\n    ];\n  }\n}\n"], "mappings": "AAEA,SAAqBA,EAAE,QAAQ,MAAM;AACrC,SAASC,GAAG,EAAEC,WAAW,EAAEC,UAAU,EAAEC,GAAG,EAAEC,KAAK,QAAQ,gBAAgB;;;AAWzE,OAAM,MAAOC,cAAc;EAKzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHhB,KAAAC,cAAc,GAAc,EAAE;IACrB,KAAAC,OAAO,GAAG,oCAAoC;IAG7D;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACH,IAAI,CAACI,GAAG,CAAQ,GAAG,IAAI,CAACF,OAAO,sCAAsC,CAAC,CAC1FG,IAAI;IACH;IACAR,KAAK,CAAC,CAAC,CAAC;IACR;IACAJ,GAAG,CAACa,SAAS,IAAIA,SAAS,CAACb,GAAG,CAACc,OAAO,IAAG;MACvC;MACA,MAAMC,IAAI,GAAGD,OAAO,CAACE,YAAY,EAAEC,GAAG,EAAEC,MAAM,IAAIJ,OAAO,CAACC,IAAI,CAACG,MAAM;MACrE,OAAO;QACLH,IAAI,EAAEA,IAAI;QACVI,IAAI,EAAEL,OAAO,CAACM,IAAI;QAClB;QACAC,IAAI,EAAEP,OAAO,CAACQ,KAAK,EAAEC,GAAG,IAAIT,OAAO,CAACQ,KAAK,EAAEE,GAAG,IAAI,uBAAuBV,OAAO,CAACM,IAAI,CAACK,WAAW,EAAE;OACpG;IACH,CAAC,CAAC,CAAC;IACH;IACAzB,GAAG,CAACa,SAAS,IAAIA,SAAS,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACZ,IAAI,CAACc,aAAa,CAACD,CAAC,CAACb,IAAI,CAAC,CAAC,CAAC;IACxE;IACAZ,GAAG,CAACU,SAAS,IAAIiB,OAAO,CAACC,GAAG,CAAC,GAAGlB,SAAS,CAACmB,MAAM,8BAA8B,CAAC,CAAC;IAChF;IACA/B,WAAW,CAAC,CAAC,CAAC;IACd;IACAC,UAAU,CAAC+B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE;MACA,MAAMC,gBAAgB,GAAG,IAAI,CAACC,eAAe,EAAE;MAC/CL,OAAO,CAACC,GAAG,CAAC,yCAAyCG,gBAAgB,CAACF,MAAM,OAAO,CAAC;MACpF,OAAOjC,EAAE,CAACmC,gBAAgB,CAAC;IAC7B,CAAC,CAAC,CACH;IAEH;IACA,IAAI,CAACxB,UAAU,CAAC0B,SAAS,CAACvB,SAAS,IAAG;MACpC,IAAI,CAACL,cAAc,GAAGK,SAAS;IACjC,CAAC,CAAC;EACJ;EAEA;;;;EAIAwB,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC3B,UAAU;EACxB;EAEA;;;;;EAKA4B,gBAAgBA,CAACnB,IAAY;IAC3B,OAAO,IAAI,CAACX,cAAc,CAAC+B,IAAI,CAACzB,OAAO,IAAIA,OAAO,CAACK,IAAI,KAAKA,IAAI,CAAC;EACnE;EAEA;;;;;EAKAqB,gBAAgBA,CAACzB,IAAY;IAC3B,OAAO,IAAI,CAACP,cAAc,CAAC+B,IAAI,CAACzB,OAAO,IACrCA,OAAO,CAACC,IAAI,CAACU,WAAW,EAAE,KAAKV,IAAI,CAACU,WAAW,EAAE,CAClD;EACH;EAEA;;;;EAIQgB,mBAAmBA,CAAA;IACzB,OAAO,CACL;MAAE1B,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEN,IAAI,EAAE,SAAS;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACnE;MAAEN,IAAI,EAAE,gBAAgB;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAC1E;MAAEN,IAAI,EAAE,eAAe;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACzE;MAAEN,IAAI,EAAE,OAAO;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEN,IAAI,EAAE,OAAO;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEN,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEN,IAAI,EAAE,WAAW;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACrE;MAAEN,IAAI,EAAE,OAAO;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEN,IAAI,EAAE,OAAO;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEN,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEN,IAAI,EAAE,OAAO;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEN,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEN,IAAI,EAAE,cAAc;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACxE;MAAEN,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEN,IAAI,EAAE,WAAW;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACrE;MAAEN,IAAI,EAAE,QAAQ;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEN,IAAI,EAAE,OAAO;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEN,IAAI,EAAE,SAAS;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACnE;MAAEN,IAAI,EAAE,SAAS;MAAEI,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,CACpE;EACH;;;uBAnGWhB,cAAc,EAAAqC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdxC,cAAc;MAAAyC,OAAA,EAAdzC,cAAc,CAAA0C,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}