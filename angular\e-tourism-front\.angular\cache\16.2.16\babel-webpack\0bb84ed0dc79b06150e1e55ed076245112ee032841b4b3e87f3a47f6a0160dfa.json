{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nexport class LoginComponent {\n  constructor(authService, router, snackBar) {\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.credentials = {\n      Agency: '',\n      User: '',\n      Password: ''\n    };\n    this.loading = false;\n    this.error = '';\n    this.hidePassword = true;\n  }\n  ngOnInit() {\n    // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n    this.authService.logout();\n    // Animation d'entrée\n    document.body.classList.add('login-page-active');\n  }\n  onSubmit() {\n    if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n      this.error = 'Please fill in all required fields.';\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    this.authService.login(this.credentials).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.header.success) {\n          this.snackBar.open('Login successful! Welcome back.', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar',\n            verticalPosition: 'top'\n          });\n          this.router.navigate(['/accueil']);\n        } else {\n          this.error = 'Authentication failed. Please check your credentials.';\n        }\n      },\n      error: err => {\n        this.loading = false;\n        this.error = 'An error occurred during login. Please try again.';\n        console.error('Login error:', err);\n      }\n    });\n  }\n  ngOnDestroy() {\n    document.body.classList.remove('login-page-active');\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 1,\n      vars: 0,\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtext(0, \"\\uFFFD\\uFFFD\\n\");\n        }\n      },\n      styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 38:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\\\\n\\\\nSyntaxError\\\\n\\\\n(1:1) C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Desktop\\\\\\\\e-tourism\\\\\\\\angular\\\\\\\\e-tourism-front\\\\\\\\src\\\\\\\\app\\\\\\\\components\\\\\\\\login\\\\\\\\login.component.css Unknown word\\\\n\\\\n\\\\u001b[1m\\\\u001b[31m>\\\\u001b[39m\\\\u001b[22m\\\\u001b[90m 1 | \\\\u001b[39m\\uFFFD\\uFFFD\\\\r\\\\u0000\\\\n \\\\u001b[90m   | \\\\u001b[39m\\\\u001b[1m\\\\u001b[31m^\\\\u001b[39m\\\\u001b[22m\\\\n \\\\u001b[90m 2 | \\\\u001b[39m\\\\u0000\\\\n\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[38]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\\n/*# sourceMappingURL=login.component.css.map*/\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["LoginComponent", "constructor", "authService", "router", "snackBar", "credentials", "Agency", "User", "Password", "loading", "error", "hidePassword", "ngOnInit", "logout", "document", "body", "classList", "add", "onSubmit", "login", "subscribe", "next", "response", "header", "success", "open", "duration", "panelClass", "verticalPosition", "navigate", "err", "console", "ngOnDestroy", "remove", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "MatSnackBar", "selectors", "decls", "vars", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵtext"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { AuthRequest } from '../../models/auth-request.model';\nimport { MatSnackBar } from '@angular/material/snack-bar';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  credentials: AuthRequest = {\n    Agency: '',\n    User: '',\n    Password: ''\n  };\n\n  loading = false;\n  error = '';\n  hidePassword = true;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) { }\n\n  ngOnInit(): void {\n    // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n    this.authService.logout();\n\n    // Animation d'entrée\n    document.body.classList.add('login-page-active');\n  }\n\n  onSubmit(): void {\n    if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n      this.error = 'Please fill in all required fields.';\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    this.authService.login(this.credentials)\n      .subscribe({\n        next: (response) => {\n          this.loading = false;\n          if (response.header.success) {\n            this.snackBar.open('Login successful! Welcome back.', 'Close', {\n              duration: 3000,\n              panelClass: 'success-snackbar',\n              verticalPosition: 'top'\n            });\n            this.router.navigate(['/accueil']);\n          } else {\n            this.error = 'Authentication failed. Please check your credentials.';\n          }\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = 'An error occurred during login. Please try again.';\n          console.error('Login error:', err);\n        }\n      });\n  }\n\n  ngOnDestroy(): void {\n    document.body.classList.remove('login-page-active');\n  }\n}\n", "��\r\u0000\n\u0000"], "mappings": ";;;;AAWA,OAAM,MAAOA,cAAc;EAWzBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAblB,KAAAC,WAAW,GAAgB;MACzBC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE;KACX;IAED,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,YAAY,GAAG,IAAI;EAMf;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACV,WAAW,CAACW,MAAM,EAAE;IAEzB;IACAC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAClD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACb,WAAW,CAACC,MAAM,IAAI,CAAC,IAAI,CAACD,WAAW,CAACE,IAAI,IAAI,CAAC,IAAI,CAACF,WAAW,CAACG,QAAQ,EAAE;MACpF,IAAI,CAACE,KAAK,GAAG,qCAAqC;MAClD;;IAGF,IAAI,CAACD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,EAAE;IAEf,IAAI,CAACR,WAAW,CAACiB,KAAK,CAAC,IAAI,CAACd,WAAW,CAAC,CACrCe,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACb,OAAO,GAAG,KAAK;QACpB,IAAIa,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;UAC3B,IAAI,CAACpB,QAAQ,CAACqB,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;YAC7DC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,kBAAkB;YAC9BC,gBAAgB,EAAE;WACnB,CAAC;UACF,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;SACnC,MAAM;UACL,IAAI,CAACnB,KAAK,GAAG,uDAAuD;;MAExE,CAAC;MACDA,KAAK,EAAGoB,GAAG,IAAI;QACb,IAAI,CAACrB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAG,mDAAmD;QAChEqB,OAAO,CAACrB,KAAK,CAAC,cAAc,EAAEoB,GAAG,CAAC;MACpC;KACD,CAAC;EACN;EAEAE,WAAWA,CAAA;IACTlB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACiB,MAAM,CAAC,mBAAmB,CAAC;EACrD;;;uBA3DWjC,cAAc,EAAAkC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAdzC,cAAc;MAAA0C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX3Bb,EAAA,CAAAe,MAAA,qBACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}