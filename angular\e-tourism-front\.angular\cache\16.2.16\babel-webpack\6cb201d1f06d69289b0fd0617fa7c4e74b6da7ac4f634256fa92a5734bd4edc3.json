{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Gender, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../services/booking.service\";\nimport * as i4 from \"../../../services/auth.service\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/core\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/select\";\nimport * as i11 from \"@angular/material/datepicker\";\nimport * as i12 from \"@angular/material/button\";\nimport * as i13 from \"@angular/material/icon\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nimport * as i15 from \"@angular/material/expansion\";\nimport * as i16 from \"@angular/material/stepper\";\nfunction BookingTransactionComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"D\\u00E9marrer la r\\u00E9servation\");\n  }\n}\nfunction BookingTransactionComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.errorMessage);\n  }\n}\nfunction BookingTransactionComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Aucun vol n'a \\u00E9t\\u00E9 s\\u00E9lectionn\\u00E9. Veuillez retourner \\u00E0 la page de recherche et s\\u00E9lectionner un vol.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 51);\n    i0.ɵɵtext(6, \" Retour \\u00E0 la recherche \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BookingTransactionComponent_div_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62);\n    i0.ɵɵelement(2, \"i\", 63);\n    i0.ɵɵelementStart(3, \"span\", 64);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 65)(6, \"p\", 66);\n    i0.ɵɵtext(7, \"Votre vol est pr\\u00EAt \\u00E0 \\u00EAtre r\\u00E9serv\\u00E9\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const offerId_r21 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r18.formatOfferId(offerId_r21));\n  }\n}\nfunction BookingTransactionComponent_div_21_mat_spinner_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 67);\n  }\n}\nfunction BookingTransactionComponent_div_21_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"D\\u00E9marrer la r\\u00E9servation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 52)(2, \"h3\");\n    i0.ɵɵtext(3, \"R\\u00E9sum\\u00E9 de votre s\\u00E9lection\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 53)(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 54);\n    i0.ɵɵtemplate(8, BookingTransactionComponent_div_21_div_8_Template, 8, 1, \"div\", 55);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"form\", 16);\n    i0.ɵɵlistener(\"ngSubmit\", function BookingTransactionComponent_div_21_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.beginTransaction());\n    });\n    i0.ɵɵelementStart(10, \"div\", 18)(11, \"mat-form-field\", 22)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"Devise\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-select\", 39)(15, \"mat-option\", 40);\n    i0.ɵɵtext(16, \"Euro (EUR)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-option\", 41);\n    i0.ɵɵtext(18, \"Dollar am\\u00E9ricain (USD)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-option\", 42);\n    i0.ɵɵtext(20, \"Livre sterling (GBP)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"mat-form-field\", 22)(22, \"mat-label\");\n    i0.ɵɵtext(23, \"Culture\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-select\", 56)(25, \"mat-option\", 57);\n    i0.ɵɵtext(26, \"Fran\\u00E7ais (FR)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"mat-option\", 58);\n    i0.ɵɵtext(28, \"Anglais (US)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"mat-option\", 59);\n    i0.ɵɵtext(30, \"Anglais (GB)\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 28)(32, \"button\", 29);\n    i0.ɵɵtemplate(33, BookingTransactionComponent_div_21_mat_spinner_33_Template, 1, 0, \"mat-spinner\", 30);\n    i0.ɵɵtemplate(34, BookingTransactionComponent_div_21_span_34_Template, 2, 0, \"span\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 60);\n    i0.ɵɵtext(36, \"Annuler\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Offres s\\u00E9lectionn\\u00E9es: \", ctx_r4.offerIds.length, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.offerIds);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.beginTransactionForm);\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isLoading);\n  }\n}\nfunction BookingTransactionComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"h3\");\n    i0.ɵɵtext(2, \"D\\u00E9tails de la transaction\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 69)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"ID de transaction:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"strong\");\n    i0.ɵɵtext(10, \"Expire le:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\")(13, \"strong\");\n    i0.ɵɵtext(14, \"Statut:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.beginResponse.body.transactionId || \"N/A\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.formatDate(ctx_r5.beginResponse.body.expiresOn), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.beginResponse.body.status || \"N/A\", \"\");\n  }\n}\nfunction BookingTransactionComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Informations voyageurs\");\n  }\n}\nfunction BookingTransactionComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r7.errorMessage);\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_60_mat_panel_description_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-panel-description\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const traveller_r24 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", (tmp_0_0 = traveller_r24.get(\"name\")) == null ? null : tmp_0_0.value, \" \", (tmp_0_0 = traveller_r24.get(\"surname\")) == null ? null : tmp_0_0.value, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_60_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r40 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r40.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r40.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_60_mat_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r41 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", title_r41.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", title_r41.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_60_mat_option_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r42 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r42.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r42.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_60_mat_option_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const gender_r43 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", gender_r43.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", gender_r43.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_60_mat_error_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code pays invalide (2 lettres requis) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_60_mat_error_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Num\\u00E9ro de passeport invalide (minimum 5 caract\\u00E8res) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_60_mat_error_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" La date d'expiration doit \\u00EAtre dans le futur \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_60_mat_error_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" La date d'expiration est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_60_button_155_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function BookingTransactionComponent_mat_expansion_panel_60_button_155_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const i_r25 = i0.ɵɵnextContext().index;\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.removeTraveller(i_r25));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Supprimer ce voyageur \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 70)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, BookingTransactionComponent_mat_expansion_panel_60_mat_panel_description_4_Template, 2, 2, \"mat-panel-description\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 71)(6, \"div\", 18)(7, \"mat-form-field\", 22)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Type de voyageur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-select\", 72);\n    i0.ɵɵtemplate(11, BookingTransactionComponent_mat_expansion_panel_60_mat_option_11_Template, 2, 2, \"mat-option\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"mat-form-field\", 22)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Titre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-select\", 74);\n    i0.ɵɵtemplate(16, BookingTransactionComponent_mat_expansion_panel_60_mat_option_16_Template, 2, 2, \"mat-option\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"mat-form-field\", 22)(18, \"mat-label\");\n    i0.ɵɵtext(19, \"Type de passager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"mat-select\", 75);\n    i0.ɵɵtemplate(21, BookingTransactionComponent_mat_expansion_panel_60_mat_option_21_Template, 2, 2, \"mat-option\", 73);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 18)(23, \"mat-form-field\", 22)(24, \"mat-label\");\n    i0.ɵɵtext(25, \"Pr\\u00E9nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"mat-form-field\", 22)(28, \"mat-label\");\n    i0.ɵɵtext(29, \"Nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"input\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"mat-form-field\", 22)(32, \"mat-label\");\n    i0.ɵɵtext(33, \"Date de naissance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 78)(35, \"mat-datepicker-toggle\", 45)(36, \"mat-datepicker\", null, 79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 18)(39, \"mat-form-field\", 22)(40, \"mat-label\");\n    i0.ɵɵtext(41, \"Genre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"mat-select\", 80);\n    i0.ɵɵtemplate(43, BookingTransactionComponent_mat_expansion_panel_60_mat_option_43_Template, 2, 2, \"mat-option\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 81)(45, \"mat-form-field\", 22)(46, \"mat-label\");\n    i0.ɵɵtext(47, \"Nationalit\\u00E9 (code pays)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(48, \"input\", 82);\n    i0.ɵɵelementStart(49, \"mat-hint\");\n    i0.ɵɵtext(50, \"Code pays \\u00E0 2 lettres (ex: FR, US, GB)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(51, BookingTransactionComponent_mat_expansion_panel_60_mat_error_51_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"mat-form-field\", 22)(53, \"mat-label\");\n    i0.ɵɵtext(54, \"Num\\u00E9ro d'identit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(55, \"input\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 17)(57, \"h4\");\n    i0.ɵɵtext(58, \"Informations de passeport\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"div\", 84)(60, \"div\", 18)(61, \"mat-form-field\", 22)(62, \"mat-label\");\n    i0.ɵɵtext(63, \"Num\\u00E9ro de s\\u00E9rie\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(64, \"input\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"mat-form-field\", 22)(66, \"mat-label\");\n    i0.ɵɵtext(67, \"Num\\u00E9ro de passeport\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(68, \"input\", 86);\n    i0.ɵɵelementStart(69, \"mat-hint\");\n    i0.ɵɵtext(70, \"Minimum 5 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(71, BookingTransactionComponent_mat_expansion_panel_60_mat_error_71_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(72, \"div\", 18)(73, \"mat-form-field\", 22)(74, \"mat-label\");\n    i0.ɵɵtext(75, \"Date d'expiration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(76, \"input\", 87)(77, \"mat-datepicker-toggle\", 45)(78, \"mat-datepicker\", null, 88);\n    i0.ɵɵelementStart(80, \"mat-hint\");\n    i0.ɵɵtext(81, \"Doit \\u00EAtre dans le futur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(82, BookingTransactionComponent_mat_expansion_panel_60_mat_error_82_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵtemplate(83, BookingTransactionComponent_mat_expansion_panel_60_mat_error_83_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"mat-form-field\", 22)(85, \"mat-label\");\n    i0.ɵɵtext(86, \"Date d'\\u00E9mission\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(87, \"input\", 89)(88, \"mat-datepicker-toggle\", 45)(89, \"mat-datepicker\", null, 90);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(91, \"div\", 18)(92, \"mat-form-field\", 22)(93, \"mat-label\");\n    i0.ɵɵtext(94, \"Code pays de citoyennet\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(95, \"input\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"mat-form-field\", 22)(97, \"mat-label\");\n    i0.ɵɵtext(98, \"Code pays d'\\u00E9mission\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(99, \"input\", 92);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(100, \"div\", 17)(101, \"h4\");\n    i0.ɵɵtext(102, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(103, \"div\", 93)(104, \"div\", 18)(105, \"div\", 94)(106, \"mat-form-field\", 22)(107, \"mat-label\");\n    i0.ɵɵtext(108, \"Code pays\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(109, \"input\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(110, \"mat-form-field\", 22)(111, \"mat-label\");\n    i0.ɵɵtext(112, \"Indicatif r\\u00E9gional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(113, \"input\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"mat-form-field\", 22)(115, \"mat-label\");\n    i0.ɵɵtext(116, \"Num\\u00E9ro de t\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(117, \"input\", 97);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(118, \"div\", 18)(119, \"mat-form-field\", 19)(120, \"mat-label\");\n    i0.ɵɵtext(121, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(122, \"input\", 98);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(123, \"div\", 18)(124, \"mat-form-field\", 19)(125, \"mat-label\");\n    i0.ɵɵtext(126, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(127, \"input\", 99);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(128, \"div\", 18)(129, \"mat-form-field\", 22)(130, \"mat-label\");\n    i0.ɵɵtext(131, \"Code postal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(132, \"input\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(133, \"div\", 101)(134, \"mat-form-field\", 22)(135, \"mat-label\");\n    i0.ɵɵtext(136, \"ID de ville\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(137, \"input\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(138, \"mat-form-field\", 22)(139, \"mat-label\");\n    i0.ɵɵtext(140, \"Nom de ville\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(141, \"input\", 76);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(142, \"div\", 18)(143, \"div\", 103)(144, \"mat-form-field\", 22)(145, \"mat-label\");\n    i0.ɵɵtext(146, \"ID de pays\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(147, \"input\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(148, \"mat-form-field\", 22)(149, \"mat-label\");\n    i0.ɵɵtext(150, \"Nom de pays\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(151, \"input\", 76);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(152, \"div\", 104)(153, \"mat-checkbox\", 105);\n    i0.ɵɵtext(154, \"Chef de groupe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(155, BookingTransactionComponent_mat_expansion_panel_60_button_155_Template, 4, 0, \"button\", 106);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const traveller_r24 = ctx.$implicit;\n    const i_r25 = ctx.index;\n    const _r30 = i0.ɵɵreference(37);\n    const _r34 = i0.ɵɵreference(79);\n    const _r37 = i0.ɵɵreference(90);\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_14_0;\n    let tmp_15_0;\n    i0.ɵɵproperty(\"expanded\", i_r25 === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Voyageur \", i_r25 + 1, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = traveller_r24.get(\"name\")) == null ? null : tmp_2_0.value) || ((tmp_2_0 = traveller_r24.get(\"surname\")) == null ? null : tmp_2_0.value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroupName\", i_r25);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.passengerTypes);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.travellerTitles);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.passengerTypes);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matDatepicker\", _r30);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r30);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.genderOptions);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_10_0 = traveller_r24.get(\"nationality.twoLetterCode\")) == null ? null : tmp_10_0.invalid);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngIf\", (tmp_11_0 = traveller_r24.get(\"passportInfo.number\")) == null ? null : tmp_11_0.invalid);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"matDatepicker\", _r34);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r34);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_14_0 = traveller_r24.get(\"passportInfo.expireDate\")) == null ? null : tmp_14_0.errors == null ? null : tmp_14_0.errors[\"expireDateInvalid\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_15_0 = traveller_r24.get(\"passportInfo.expireDate\")) == null ? null : tmp_15_0.errors == null ? null : tmp_15_0.errors[\"required\"]);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"matDatepicker\", _r37);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r37);\n    i0.ɵɵadvance(67);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.travellers.length > 1);\n  }\n}\nfunction BookingTransactionComponent_mat_spinner_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 67);\n  }\n}\nfunction BookingTransactionComponent_span_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Enregistrer les informations\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"h3\");\n    i0.ɵɵtext(2, \"Informations de r\\u00E9servation enregistr\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 108)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"ID de transaction:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"strong\");\n    i0.ɵɵtext(10, \"Nombre de voyageurs:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.infoResponse.body.transactionId || \"N/A\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.infoResponse.body.reservationData && ctx_r11.infoResponse.body.reservationData.travellers ? ctx_r11.infoResponse.body.reservationData.travellers.length : 0, \"\");\n  }\n}\nfunction BookingTransactionComponent_ng_template_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Paiement et confirmation\");\n  }\n}\nfunction BookingTransactionComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r13.errorMessage);\n  }\n}\nfunction BookingTransactionComponent_mat_spinner_152_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 67);\n  }\n}\nfunction BookingTransactionComponent_span_153_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Finaliser la r\\u00E9servation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_div_156_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"h3\");\n    i0.ɵɵtext(2, \"R\\u00E9servation confirm\\u00E9e!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 110)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Num\\u00E9ro de r\\u00E9servation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"strong\");\n    i0.ɵɵtext(10, \"ID de transaction:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 111)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\");\n    i0.ɵɵtext(16, \"Votre r\\u00E9servation a \\u00E9t\\u00E9 confirm\\u00E9e avec succ\\u00E8s. Vous recevrez bient\\u00F4t un email de confirmation avec tous les d\\u00E9tails de votre voyage.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 112)(18, \"button\", 51);\n    i0.ɵɵtext(19, \" Retour \\u00E0 la recherche \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.commitResponse.body.reservationNumber || \"N/A\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.commitResponse.body.transactionId || \"N/A\", \"\");\n  }\n}\nexport class BookingTransactionComponent {\n  constructor(fb, route, router, bookingService, authService, snackBar) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.bookingService = bookingService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    // Étape actuelle du processus de réservation\n    this.currentStep = 1;\n    // Données de transaction\n    this.transactionId = '';\n    this.offerIds = [];\n    // Réponses des différentes étapes\n    this.beginResponse = null;\n    this.infoResponse = null;\n    this.commitResponse = null;\n    // États de chargement et d'erreur\n    this.isLoading = false;\n    this.errorMessage = '';\n    // Options pour les formulaires\n    this.travellerTitles = Object.keys(TravellerTitle).filter(key => !isNaN(Number(TravellerTitle[key]))).map(key => ({\n      value: TravellerTitle[key],\n      label: key\n    }));\n    this.genderOptions = Object.keys(Gender).filter(key => !isNaN(Number(Gender[key]))).map(key => ({\n      value: Gender[key],\n      label: key\n    }));\n    this.passengerTypes = Object.keys(PassengerType).filter(key => !isNaN(Number(PassengerType[key]))).map(key => ({\n      value: PassengerType[key],\n      label: key\n    }));\n    // Initialisation des formulaires\n    this.beginTransactionForm = this.fb.group({\n      currency: ['EUR', Validators.required],\n      culture: ['fr-FR', Validators.required]\n    });\n    this.reservationInfoForm = this.fb.group({\n      transactionId: ['', Validators.required],\n      travellers: this.fb.array([]),\n      reservationNote: [''],\n      agencyReservationNumber: ['']\n    });\n    this.commitTransactionForm = this.fb.group({\n      transactionId: ['', Validators.required],\n      paymentOption: [1],\n      paymentInformation: this.fb.group({\n        accountName: [''],\n        paymentTypeId: [1],\n        paymentPrice: this.fb.group({\n          amount: [0, Validators.required],\n          currency: ['EUR', Validators.required]\n        }),\n        installmentCount: ['1'],\n        paymentDate: [new Date().toISOString()],\n        receiptType: [''],\n        reference: [''],\n        paymentToken: ['']\n      })\n    });\n  }\n  ngOnInit() {\n    // Récupérer les offerIds depuis les paramètres de l'URL\n    this.route.queryParams.subscribe(params => {\n      if (params['offerIds']) {\n        try {\n          this.offerIds = Array.isArray(params['offerIds']) ? params['offerIds'] : [params['offerIds']];\n          if (this.offerIds.length > 0) {\n            console.log('OfferIds récupérés:', this.offerIds);\n          } else {\n            this.errorMessage = 'Aucun ID d\\'offre n\\'a été fourni.';\n          }\n        } catch (error) {\n          console.error('Erreur lors de la récupération des offerIds:', error);\n          this.errorMessage = 'Format d\\'ID d\\'offre invalide.';\n        }\n      } else {\n        this.errorMessage = 'Aucun ID d\\'offre n\\'a été fourni.';\n      }\n    });\n  }\n  // Getter pour accéder au FormArray des voyageurs\n  get travellers() {\n    return this.reservationInfoForm.get('travellers');\n  }\n  // Ajouter un nouveau voyageur au formulaire\n  addTraveller() {\n    const travellerForm = this.fb.group({\n      type: [1, Validators.required],\n      title: [1, Validators.required],\n      passengerType: [1, Validators.required],\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      surname: ['', [Validators.required, Validators.minLength(2)]],\n      isLeader: [this.travellers?.length === 0],\n      birthDate: ['', Validators.required],\n      nationality: this.fb.group({\n        twoLetterCode: ['FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      identityNumber: [''],\n      passportInfo: this.fb.group({\n        serial: [''],\n        number: ['', [Validators.required, Validators.minLength(5)]],\n        expireDate: ['', Validators.required],\n        issueDate: ['', Validators.required],\n        citizenshipCountryCode: ['FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n        issueCountryCode: ['FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      address: this.fb.group({\n        contactPhone: this.fb.group({\n          countryCode: ['+33', Validators.required],\n          areaCode: [''],\n          phoneNumber: ['', [Validators.required, Validators.minLength(5)]]\n        }),\n        email: ['', [Validators.required, Validators.email]],\n        address: ['', [Validators.required, Validators.minLength(5)]],\n        zipCode: ['', [Validators.required, Validators.minLength(3)]],\n        city: this.fb.group({\n          id: ['', Validators.required],\n          name: ['', [Validators.required, Validators.minLength(2)]]\n        }),\n        country: this.fb.group({\n          id: ['FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n          name: ['France', [Validators.required, Validators.minLength(2)]]\n        })\n      }),\n      gender: [1, Validators.required] // Genre (masculin par défaut)\n    });\n    // Ajouter des validateurs personnalisés\n    this.addCustomValidators(travellerForm);\n    this.travellers.push(travellerForm);\n  }\n  // Ajouter des validateurs personnalisés au formulaire de voyageur\n  addCustomValidators(form) {\n    // S'assurer que issueCountryCode est toujours renseigné et correspond à la nationalité si non spécifié\n    const nationalityControl = form.get('nationality.twoLetterCode');\n    const issueCountryCodeControl = form.get('passportInfo.issueCountryCode');\n    // Synchroniser la valeur de issueCountryCode avec la nationalité si elle est modifiée\n    if (nationalityControl && issueCountryCodeControl) {\n      nationalityControl.valueChanges.subscribe(value => {\n        if (value && (!issueCountryCodeControl.value || issueCountryCodeControl.value === '')) {\n          issueCountryCodeControl.setValue(value);\n        }\n      });\n    }\n    // Vérifier que la date d'expiration du passeport est dans le futur\n    const expireDateControl = form.get('passportInfo.expireDate');\n    if (expireDateControl) {\n      expireDateControl.setValidators([Validators.required, control => {\n        const value = control.value;\n        if (!value) return null;\n        const expireDate = new Date(value);\n        const today = new Date();\n        return expireDate > today ? null : {\n          'expireDateInvalid': true\n        };\n      }]);\n    }\n  }\n  // Supprimer un voyageur du formulaire\n  removeTraveller(index) {\n    this.travellers.removeAt(index);\n  }\n  // Démarrer la transaction de réservation\n  beginTransaction() {\n    if (this.beginTransactionForm.invalid) {\n      this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {\n        duration: 3000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    const formValue = this.beginTransactionForm.value;\n    this.bookingService.beginTransaction(this.offerIds, formValue.currency, formValue.culture).subscribe({\n      next: response => {\n        this.isLoading = false;\n        console.log('Réponse reçue:', response);\n        if (response && response.beginResponse && response.beginResponse.body) {\n          this.beginResponse = response.beginResponse;\n          this.transactionId = response.beginResponse.body.transactionId || '';\n          // Mettre à jour le formulaire d'informations de réservation avec l'ID de transaction\n          this.reservationInfoForm.patchValue({\n            transactionId: this.transactionId\n          });\n          // Ajouter les voyageurs existants s'il y en a dans la réponse\n          if (response.beginResponse.body.reservationData && response.beginResponse.body.reservationData.travellers && response.beginResponse.body.reservationData.travellers.length > 0) {\n            // Vider le tableau de voyageurs existant\n            while (this.travellers.length > 0) {\n              this.travellers.removeAt(0);\n            }\n            // Ajouter les voyageurs de la réponse\n            response.beginResponse.body.reservationData.travellers.forEach(traveller => {\n              this.addTravellerFromResponse(traveller);\n            });\n          } else {\n            // Ajouter un voyageur par défaut si aucun n'existe\n            this.addTraveller();\n          }\n          // Passer à l'étape suivante\n          this.currentStep = 2;\n          this.snackBar.open('Transaction démarrée avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = 'Réponse de transaction invalide.';\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        console.error('Erreur lors du démarrage de la transaction:', error);\n        this.errorMessage = error.message || 'Une erreur est survenue lors du démarrage de la transaction.';\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  // Ajouter un voyageur à partir de la réponse de l'API\n  addTravellerFromResponse(traveller) {\n    const travellerForm = this.fb.group({\n      travellerId: [traveller.travellerId || ''],\n      type: [traveller.type || 1, Validators.required],\n      title: [traveller.title || 1, Validators.required],\n      passengerType: [traveller.passengerType || 1, Validators.required],\n      name: [traveller.name || '', [Validators.required, Validators.minLength(2)]],\n      surname: [traveller.surname || '', [Validators.required, Validators.minLength(2)]],\n      isLeader: [traveller.isLeader || this.travellers?.length === 0],\n      birthDate: [traveller.birthDate || '', Validators.required],\n      nationality: this.fb.group({\n        twoLetterCode: [traveller.nationality?.twoLetterCode || 'FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      identityNumber: [traveller.identityNumber || ''],\n      passportInfo: this.fb.group({\n        serial: [traveller.passportInfo?.serial || ''],\n        number: [traveller.passportInfo?.number || '', [Validators.required, Validators.minLength(5)]],\n        expireDate: [traveller.passportInfo?.expireDate || '', Validators.required],\n        issueDate: [traveller.passportInfo?.issueDate || '', Validators.required],\n        citizenshipCountryCode: [traveller.passportInfo?.citizenshipCountryCode || 'FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n        issueCountryCode: [traveller.passportInfo?.issueCountryCode || 'FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      address: this.fb.group({\n        contactPhone: this.fb.group({\n          countryCode: [traveller.address?.contactPhone?.countryCode || '+33', Validators.required],\n          areaCode: [''],\n          phoneNumber: [traveller.address?.contactPhone?.phoneNumber || '', [Validators.required, Validators.minLength(5)]]\n        }),\n        email: [traveller.address?.email || '', [Validators.required, Validators.email]],\n        address: [traveller.address?.address || '', [Validators.required, Validators.minLength(5)]],\n        zipCode: [traveller.address?.zipCode || '', [Validators.required, Validators.minLength(3)]],\n        city: this.fb.group({\n          id: [traveller.address?.city?.id || '', Validators.required],\n          name: [traveller.address?.city?.name || '', [Validators.required, Validators.minLength(2)]]\n        }),\n        country: this.fb.group({\n          id: [traveller.address?.country?.id || 'FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n          name: [traveller.address?.country?.name || 'France', [Validators.required, Validators.minLength(2)]]\n        })\n      }),\n      gender: [traveller.gender || 1, Validators.required]\n    });\n    // Ajouter des validateurs personnalisés\n    this.addCustomValidators(travellerForm);\n    this.travellers.push(travellerForm);\n  }\n  // Définir les informations de réservation\n  setReservationInfo() {\n    if (this.reservationInfoForm.invalid) {\n      // Vérifier les erreurs spécifiques pour donner des messages plus précis\n      let errorMessage = 'Veuillez remplir tous les champs obligatoires.';\n      // Vérifier les erreurs de passeport\n      const travellers = this.travellers.controls;\n      for (let i = 0; i < travellers.length; i++) {\n        const traveller = travellers[i];\n        // Vérifier les erreurs de date d'expiration du passeport\n        const expireDateControl = traveller.get('passportInfo.expireDate');\n        if (expireDateControl?.errors?.['expireDateInvalid']) {\n          errorMessage = `Voyageur ${i + 1}: La date d'expiration du passeport doit être dans le futur.`;\n          break;\n        }\n        // Vérifier les erreurs de code pays\n        const issueCountryCodeControl = traveller.get('passportInfo.issueCountryCode');\n        if (issueCountryCodeControl?.invalid && issueCountryCodeControl?.touched) {\n          errorMessage = `Voyageur ${i + 1}: Le code pays d'émission du passeport est invalide.`;\n          break;\n        }\n        // Vérifier les erreurs de numéro de passeport\n        const passportNumberControl = traveller.get('passportInfo.number');\n        if (passportNumberControl?.invalid && passportNumberControl?.touched) {\n          errorMessage = `Voyageur ${i + 1}: Le numéro de passeport est invalide.`;\n          break;\n        }\n      }\n      this.snackBar.open(errorMessage, 'Fermer', {\n        duration: 5000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    const formValue = this.reservationInfoForm.value;\n    // Formater correctement les dates pour chaque voyageur\n    const formattedTravellers = formValue.travellers.map(traveller => {\n      // Formater la date de naissance\n      if (traveller.birthDate) {\n        if (traveller.birthDate instanceof Date) {\n          traveller.birthDate = this.formatDateForApi(traveller.birthDate);\n        } else if (typeof traveller.birthDate === 'string' && traveller.birthDate.trim() !== '') {\n          traveller.birthDate = this.formatDateForApi(new Date(traveller.birthDate));\n        }\n      }\n      // Formater les dates de passeport si présentes\n      if (traveller.passportInfo) {\n        if (traveller.passportInfo.expireDate) {\n          if (traveller.passportInfo.expireDate instanceof Date) {\n            traveller.passportInfo.expireDate = this.formatDateForApi(traveller.passportInfo.expireDate);\n          } else if (typeof traveller.passportInfo.expireDate === 'string' && traveller.passportInfo.expireDate.trim() !== '') {\n            traveller.passportInfo.expireDate = this.formatDateForApi(new Date(traveller.passportInfo.expireDate));\n          }\n        }\n        if (traveller.passportInfo.issueDate) {\n          if (traveller.passportInfo.issueDate instanceof Date) {\n            traveller.passportInfo.issueDate = this.formatDateForApi(traveller.passportInfo.issueDate);\n          } else if (typeof traveller.passportInfo.issueDate === 'string' && traveller.passportInfo.issueDate.trim() !== '') {\n            traveller.passportInfo.issueDate = this.formatDateForApi(new Date(traveller.passportInfo.issueDate));\n          }\n        }\n      }\n      return traveller;\n    });\n    // Créer la requête d'informations de réservation\n    const request = {\n      transactionId: formValue.transactionId,\n      travellers: formattedTravellers,\n      reservationNote: formValue.reservationNote,\n      agencyReservationNumber: formValue.agencyReservationNumber\n    };\n    this.bookingService.setReservationInfo(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        console.log('Réponse setReservationInfo reçue:', response);\n        if (response && response.infoResponse && response.infoResponse.body) {\n          this.infoResponse = response.infoResponse;\n          // Mettre à jour le formulaire de finalisation de transaction avec l'ID de transaction\n          this.commitTransactionForm.patchValue({\n            transactionId: this.transactionId\n          });\n          // Mettre à jour le montant du paiement si disponible\n          if (response.infoResponse.body.reservationData && response.infoResponse.body.reservationData.reservationInfo && response.infoResponse.body.reservationData.reservationInfo.priceToPay) {\n            const priceToPay = response.infoResponse.body.reservationData.reservationInfo.priceToPay;\n            this.commitTransactionForm.get('paymentInformation.paymentPrice')?.patchValue({\n              amount: priceToPay.amount,\n              currency: priceToPay.currency\n            });\n          }\n          // Passer à l'étape suivante\n          this.currentStep = 3;\n          this.snackBar.open('Informations de réservation définies avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = 'Réponse d\\'informations de réservation invalide.';\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        console.error('Erreur lors de la définition des informations de réservation:', error);\n        // Analyser le message d'erreur pour des problèmes spécifiques\n        let errorMessage = error.message || 'Une erreur est survenue lors de la définition des informations de réservation.';\n        // Vérifier les erreurs spécifiques liées au passeport\n        if (errorMessage.includes('passport') || errorMessage.includes('issueCountryCode')) {\n          errorMessage = 'Erreur de validation du passeport: Veuillez vérifier que toutes les informations de passeport sont complètes, notamment le code pays d\\'émission.';\n        }\n        // Vérifier les erreurs liées aux dates\n        else if (errorMessage.includes('date') || errorMessage.includes('expireDate')) {\n          errorMessage = 'Erreur de validation des dates: Veuillez vérifier que toutes les dates sont au format correct (YYYY-MM-DD).';\n        }\n        // Vérifier les erreurs liées aux informations personnelles\n        else if (errorMessage.includes('name') || errorMessage.includes('surname')) {\n          errorMessage = 'Erreur de validation des informations personnelles: Veuillez vérifier que tous les noms et prénoms sont correctement renseignés.';\n        }\n        this.errorMessage = errorMessage;\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 8000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  // Finaliser la transaction de réservation\n  commitTransaction() {\n    if (this.commitTransactionForm.invalid) {\n      this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {\n        duration: 3000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    const formValue = this.commitTransactionForm.value;\n    this.bookingService.commitTransaction(formValue).subscribe({\n      next: response => {\n        this.isLoading = false;\n        console.log('Réponse commitTransaction reçue:', response);\n        if (response && response.commitResponse && response.commitResponse.body) {\n          this.commitResponse = response.commitResponse;\n          this.snackBar.open('Réservation finalisée avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          // Afficher les détails de la réservation finalisée\n          // Vous pourriez rediriger vers une page de confirmation ici\n        } else {\n          this.errorMessage = 'Réponse de finalisation de transaction invalide.';\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        console.error('Erreur lors de la finalisation de la transaction:', error);\n        this.errorMessage = error.message || 'Une erreur est survenue lors de la finalisation de la transaction.';\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  // Revenir à l'étape précédente\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  // Formater une date pour l'affichage\n  formatDate(dateString) {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    return date.toLocaleDateString();\n  }\n  // Formater une date pour l'API (format ISO 8601: YYYY-MM-DD)\n  formatDateForApi(date) {\n    if (!date) return '';\n    // S'assurer que c'est une instance de Date valide\n    if (!(date instanceof Date) || isNaN(date.getTime())) {\n      console.error('Date invalide:', date);\n      return '';\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  // Formater un prix pour l'affichage\n  formatPrice(amount, currency) {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: currency\n    }).format(amount);\n  }\n  // Formater un ID d'offre pour l'affichage\n  formatOfferId(offerId) {\n    if (!offerId) return 'N/A';\n    // Extraire les informations pertinentes de l'ID d'offre\n    // Format typique: 13$3$1~^006^~AAABloLbX6oAAAAClhW0YYMc26FHjKULIrlKaQAAAZaC2zyuAAAAALH6UPiglvPwEjLokBT6TDI=~^006^~1~^006^~154.66~^006^~~^006^~154.66~^006^~ODBiZTZiMGQtYWYxYy00MzYzLThmNjctODcyNTA0NjVjZjgz\n    // Extraire le début de l'ID (avant le premier ~)\n    const parts = offerId.split('~');\n    const firstPart = parts[0] || '';\n    // Extraire les informations de base (type de vol, classe, etc.)\n    const basicInfo = firstPart.split('$');\n    // Créer un identifiant court\n    const shortId = offerId.substring(0, 8) + '...' + offerId.substring(offerId.length - 8);\n    return `Vol #${basicInfo[0] || 'N/A'} - Référence: ${shortId}`;\n  }\n  static {\n    this.ɵfac = function BookingTransactionComponent_Factory(t) {\n      return new (t || BookingTransactionComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.BookingService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BookingTransactionComponent,\n      selectors: [[\"app-booking-transaction\"]],\n      decls: 157,\n      vars: 30,\n      consts: [[1, \"booking-transaction-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-illustration\"], [\"src\", \"assets/images/booking-banner.jpg\", \"alt\", \"R\\u00E9servation de vol\"], [3, \"linear\", \"selectedIndex\"], [\"stepper\", \"\"], [3, \"completed\"], [\"matStepLabel\", \"\"], [1, \"step-content\"], [1, \"step-header\"], [\"class\", \"error-message\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"response-summary\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"transactionId\", \"readonly\", \"\"], [\"matInput\", \"\", \"formControlName\", \"reservationNote\", \"rows\", \"3\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"agencyReservationNumber\"], [1, \"section-header\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", \"type\", \"button\", \"aria-label\", \"Ajouter un voyageur\", 3, \"click\"], [\"formArrayName\", \"travellers\"], [3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"button-spinner\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"formControlName\", \"paymentOption\"], [3, \"value\"], [\"formGroupName\", \"paymentInformation\"], [\"matInput\", \"\", \"formControlName\", \"accountName\"], [\"formControlName\", \"paymentTypeId\"], [\"formGroupName\", \"paymentPrice\"], [\"matInput\", \"\", \"formControlName\", \"amount\", \"type\", \"number\", \"required\", \"\"], [\"formControlName\", \"currency\"], [\"value\", \"EUR\"], [\"value\", \"USD\"], [\"value\", \"GBP\"], [\"matInput\", \"\", \"formControlName\", \"installmentCount\"], [\"matInput\", \"\", \"formControlName\", \"paymentDate\", 3, \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"paymentDatePicker\", \"\"], [\"matInput\", \"\", \"formControlName\", \"receiptType\"], [\"matInput\", \"\", \"formControlName\", \"reference\"], [\"class\", \"response-summary success-response\", 4, \"ngIf\"], [1, \"error-message\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/search-price\"], [1, \"offer-summary\"], [1, \"offer-ids\"], [1, \"offer-card-container\"], [\"class\", \"offer-card\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"culture\"], [\"value\", \"fr-FR\"], [\"value\", \"en-US\"], [\"value\", \"en-GB\"], [\"mat-button\", \"\", \"type\", \"button\", \"routerLink\", \"/get-offer\"], [1, \"offer-card\"], [1, \"offer-card-header\"], [1, \"fas\", \"fa-plane\"], [1, \"offer-title\"], [1, \"offer-card-content\"], [1, \"offer-info\"], [\"diameter\", \"20\", 1, \"button-spinner\"], [1, \"response-summary\"], [1, \"transaction-details\"], [3, \"expanded\"], [3, \"formGroupName\"], [\"formControlName\", \"type\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"title\"], [\"formControlName\", \"passengerType\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"surname\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"birthDate\", \"required\", \"\", 3, \"matDatepicker\"], [\"birthDatePicker\", \"\"], [\"formControlName\", \"gender\"], [\"formGroupName\", \"nationality\"], [\"matInput\", \"\", \"formControlName\", \"twoLetterCode\", \"required\", \"\", \"placeholder\", \"FR\"], [\"matInput\", \"\", \"formControlName\", \"identityNumber\"], [\"formGroupName\", \"passportInfo\"], [\"matInput\", \"\", \"formControlName\", \"serial\"], [\"matInput\", \"\", \"formControlName\", \"number\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"expireDate\", \"required\", \"\", 3, \"matDatepicker\"], [\"expireDatePicker\", \"\"], [\"matInput\", \"\", \"formControlName\", \"issueDate\", \"required\", \"\", 3, \"matDatepicker\"], [\"issueDatePicker\", \"\"], [\"matInput\", \"\", \"formControlName\", \"citizenshipCountryCode\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"issueCountryCode\", \"required\", \"\"], [\"formGroupName\", \"address\"], [\"formGroupName\", \"contactPhone\"], [\"matInput\", \"\", \"formControlName\", \"countryCode\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"areaCode\"], [\"matInput\", \"\", \"formControlName\", \"phoneNumber\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"required\", \"\", \"type\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"zipCode\", \"required\", \"\"], [\"formGroupName\", \"city\"], [\"matInput\", \"\", \"formControlName\", \"id\", \"required\", \"\"], [\"formGroupName\", \"country\"], [1, \"form-row\", \"traveller-actions\"], [\"formControlName\", \"isLeader\"], [\"mat-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [1, \"reservation-details\"], [1, \"response-summary\", \"success-response\"], [1, \"confirmation-details\"], [1, \"confirmation-message\"], [1, \"confirmation-actions\"]],\n      template: function BookingTransactionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"R\\u00E9servation de vol\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Compl\\u00E9tez votre r\\u00E9servation en quelques \\u00E9tapes simples\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵelement(8, \"img\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-horizontal-stepper\", 7, 8)(11, \"mat-step\", 9);\n          i0.ɵɵtemplate(12, BookingTransactionComponent_ng_template_12_Template, 1, 0, \"ng-template\", 10);\n          i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"h2\");\n          i0.ɵɵtext(16, \"D\\u00E9marrer votre r\\u00E9servation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\");\n          i0.ɵɵtext(18, \"Nous allons commencer le processus de r\\u00E9servation pour les vols s\\u00E9lectionn\\u00E9s.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(19, BookingTransactionComponent_div_19_Template, 5, 1, \"div\", 13);\n          i0.ɵɵtemplate(20, BookingTransactionComponent_div_20_Template, 7, 0, \"div\", 13);\n          i0.ɵɵtemplate(21, BookingTransactionComponent_div_21_Template, 37, 6, \"div\", 14);\n          i0.ɵɵtemplate(22, BookingTransactionComponent_div_22_Template, 16, 3, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"mat-step\", 9);\n          i0.ɵɵtemplate(24, BookingTransactionComponent_ng_template_24_Template, 1, 0, \"ng-template\", 10);\n          i0.ɵɵelementStart(25, \"div\", 11)(26, \"div\", 12)(27, \"h2\");\n          i0.ɵɵtext(28, \"Informations des voyageurs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"p\");\n          i0.ɵɵtext(30, \"Veuillez fournir les informations pour tous les voyageurs.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, BookingTransactionComponent_div_31_Template, 5, 1, \"div\", 13);\n          i0.ɵɵelementStart(32, \"form\", 16);\n          i0.ɵɵlistener(\"ngSubmit\", function BookingTransactionComponent_Template_form_ngSubmit_32_listener() {\n            return ctx.setReservationInfo();\n          });\n          i0.ɵɵelementStart(33, \"div\", 17)(34, \"h3\");\n          i0.ɵɵtext(35, \"Informations g\\u00E9n\\u00E9rales\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 18)(37, \"mat-form-field\", 19)(38, \"mat-label\");\n          i0.ɵɵtext(39, \"ID de transaction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 18)(42, \"mat-form-field\", 19)(43, \"mat-label\");\n          i0.ɵɵtext(44, \"Note de r\\u00E9servation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(45, \"textarea\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 18)(47, \"mat-form-field\", 22)(48, \"mat-label\");\n          i0.ɵɵtext(49, \"Num\\u00E9ro de r\\u00E9servation d'agence\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(50, \"input\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"div\", 17)(52, \"div\", 24)(53, \"h3\");\n          i0.ɵɵtext(54, \"Voyageurs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function BookingTransactionComponent_Template_button_click_55_listener() {\n            return ctx.addTraveller();\n          });\n          i0.ɵɵelementStart(56, \"mat-icon\");\n          i0.ɵɵtext(57, \"add\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"div\", 26)(59, \"mat-accordion\");\n          i0.ɵɵtemplate(60, BookingTransactionComponent_mat_expansion_panel_60_Template, 156, 19, \"mat-expansion-panel\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(61, \"div\", 28)(62, \"button\", 29);\n          i0.ɵɵtemplate(63, BookingTransactionComponent_mat_spinner_63_Template, 1, 0, \"mat-spinner\", 30);\n          i0.ɵɵtemplate(64, BookingTransactionComponent_span_64_Template, 2, 0, \"span\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function BookingTransactionComponent_Template_button_click_65_listener() {\n            return ctx.previousStep();\n          });\n          i0.ɵɵtext(66, \"Retour\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(67, BookingTransactionComponent_div_67_Template, 12, 2, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"mat-step\", 9);\n          i0.ɵɵtemplate(69, BookingTransactionComponent_ng_template_69_Template, 1, 0, \"ng-template\", 10);\n          i0.ɵɵelementStart(70, \"div\", 11)(71, \"div\", 12)(72, \"h2\");\n          i0.ɵɵtext(73, \"Paiement et confirmation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"p\");\n          i0.ɵɵtext(75, \"Finalisez votre r\\u00E9servation en effectuant le paiement.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(76, BookingTransactionComponent_div_76_Template, 5, 1, \"div\", 13);\n          i0.ɵɵelementStart(77, \"form\", 16);\n          i0.ɵɵlistener(\"ngSubmit\", function BookingTransactionComponent_Template_form_ngSubmit_77_listener() {\n            return ctx.commitTransaction();\n          });\n          i0.ɵɵelementStart(78, \"div\", 17)(79, \"h3\");\n          i0.ɵɵtext(80, \"Informations de paiement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"div\", 18)(82, \"mat-form-field\", 19)(83, \"mat-label\");\n          i0.ɵɵtext(84, \"ID de transaction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(85, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"div\", 18)(87, \"mat-form-field\", 22)(88, \"mat-label\");\n          i0.ɵɵtext(89, \"Option de paiement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"mat-select\", 32)(91, \"mat-option\", 33);\n          i0.ɵɵtext(92, \"Carte de cr\\u00E9dit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"mat-option\", 33);\n          i0.ɵɵtext(94, \"Virement bancaire\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"mat-option\", 33);\n          i0.ɵɵtext(96, \"PayPal\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(97, \"div\", 34)(98, \"div\", 18)(99, \"mat-form-field\", 22)(100, \"mat-label\");\n          i0.ɵɵtext(101, \"Nom du titulaire\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(102, \"input\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"mat-form-field\", 22)(104, \"mat-label\");\n          i0.ɵɵtext(105, \"Type de paiement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"mat-select\", 36)(107, \"mat-option\", 33);\n          i0.ɵɵtext(108, \"Carte de cr\\u00E9dit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"mat-option\", 33);\n          i0.ɵɵtext(110, \"Virement bancaire\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"mat-option\", 33);\n          i0.ɵɵtext(112, \"PayPal\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(113, \"div\", 18)(114, \"div\", 37)(115, \"mat-form-field\", 22)(116, \"mat-label\");\n          i0.ɵɵtext(117, \"Montant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(118, \"input\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(119, \"mat-form-field\", 22)(120, \"mat-label\");\n          i0.ɵɵtext(121, \"Devise\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"mat-select\", 39)(123, \"mat-option\", 40);\n          i0.ɵɵtext(124, \"Euro (EUR)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(125, \"mat-option\", 41);\n          i0.ɵɵtext(126, \"Dollar am\\u00E9ricain (USD)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(127, \"mat-option\", 42);\n          i0.ɵɵtext(128, \"Livre sterling (GBP)\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(129, \"div\", 18)(130, \"mat-form-field\", 22)(131, \"mat-label\");\n          i0.ɵɵtext(132, \"Nombre de versements\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(133, \"input\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"mat-form-field\", 22)(135, \"mat-label\");\n          i0.ɵɵtext(136, \"Date de paiement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(137, \"input\", 44)(138, \"mat-datepicker-toggle\", 45)(139, \"mat-datepicker\", null, 46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(141, \"div\", 18)(142, \"mat-form-field\", 22)(143, \"mat-label\");\n          i0.ɵɵtext(144, \"Type de re\\u00E7u\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(145, \"input\", 47);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(146, \"mat-form-field\", 22)(147, \"mat-label\");\n          i0.ɵɵtext(148, \"R\\u00E9f\\u00E9rence\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(149, \"input\", 48);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(150, \"div\", 28)(151, \"button\", 29);\n          i0.ɵɵtemplate(152, BookingTransactionComponent_mat_spinner_152_Template, 1, 0, \"mat-spinner\", 30);\n          i0.ɵɵtemplate(153, BookingTransactionComponent_span_153_Template, 2, 0, \"span\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(154, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function BookingTransactionComponent_Template_button_click_154_listener() {\n            return ctx.previousStep();\n          });\n          i0.ɵɵtext(155, \"Retour\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(156, BookingTransactionComponent_div_156_Template, 20, 2, \"div\", 49);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r14 = i0.ɵɵreference(140);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"linear\", true)(\"selectedIndex\", ctx.currentStep - 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"completed\", ctx.beginResponse !== null);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.offerIds.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.offerIds.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.beginResponse && ctx.beginResponse.body);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"completed\", ctx.infoResponse !== null);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.reservationInfoForm);\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"ngForOf\", ctx.travellers.controls);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.reservationInfoForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.infoResponse && ctx.infoResponse.body);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"completed\", ctx.commitResponse !== null);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.commitTransactionForm);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 3);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 3);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"matDatepicker\", _r14);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r14);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.commitTransactionForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.commitResponse && ctx.commitResponse.body);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i7.MatOption, i8.MatInput, i9.MatFormField, i9.MatLabel, i9.MatHint, i9.MatError, i9.MatSuffix, i10.MatSelect, i11.MatDatepicker, i11.MatDatepickerInput, i11.MatDatepickerToggle, i12.MatButton, i12.MatMiniFabButton, i13.MatIcon, i14.MatProgressSpinner, i15.MatAccordion, i15.MatExpansionPanel, i15.MatExpansionPanelHeader, i15.MatExpansionPanelTitle, i15.MatExpansionPanelDescription, i16.MatStep, i16.MatStepLabel, i16.MatStepper],\n      styles: [\".booking-transaction-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  background: linear-gradient(135deg, #1a73e8, #0d47a1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 40px;\\n  color: white;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  margin: 0 0 10px;\\n  font-weight: 600;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n\\n.header-illustration[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n\\n.header-illustration[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.step-content[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n}\\n\\n.step-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.step-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin: 0 0 10px;\\n  color: #1a73e8;\\n}\\n\\n.step-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0;\\n  color: #5f6368;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin: 0;\\n  color: #202124;\\n}\\n\\n.form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0 0 15px;\\n  color: #5f6368;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n\\n.form-row[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 16px;\\n  margin-top: 20px;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background-color: #fdeded;\\n  color: #d32f2f;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n}\\n\\n.error-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n}\\n\\n.button-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.traveller-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.response-summary[_ngcontent-%COMP%] {\\n  background-color: #e8f0fe;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-top: 30px;\\n}\\n\\n.response-summary[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin: 0 0 15px;\\n  color: #1a73e8;\\n}\\n\\n.transaction-details[_ngcontent-%COMP%], .reservation-details[_ngcontent-%COMP%], .confirmation-details[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n  gap: 16px;\\n}\\n\\n.transaction-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .reservation-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .confirmation-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 8px 0;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\\n}\\n\\n.success-response[_ngcontent-%COMP%] {\\n  background-color: #e6f4ea;\\n}\\n\\n.confirmation-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  margin: 20px 0;\\n  grid-column: 1 / -1;\\n}\\n\\n.confirmation-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #34a853;\\n  font-size: 24px;\\n}\\n\\n.confirmation-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  border: none;\\n}\\n\\n.confirmation-actions[_ngcontent-%COMP%] {\\n  grid-column: 1 / -1;\\n  margin-top: 20px;\\n  text-align: center;\\n}\\n\\n.offer-summary[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.offer-summary[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin: 0 0 15px;\\n  color: #202124;\\n}\\n\\n.offer-ids[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.offer-ids[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 10px;\\n  font-weight: 500;\\n}\\n\\n.offer-card-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  margin-top: 15px;\\n}\\n\\n.offer-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  padding: 16px;\\n  width: 100%;\\n  transition: transform 0.2s, box-shadow 0.2s;\\n  border-left: 4px solid #1a73e8;\\n}\\n\\n.offer-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n.offer-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  color: #1a73e8;\\n}\\n\\n.offer-card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 20px;\\n}\\n\\n.offer-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.offer-card-content[_ngcontent-%COMP%] {\\n  color: #5f6368;\\n}\\n\\n.offer-info[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n\\n  .header-content[_ngcontent-%COMP%] {\\n    padding: 30px;\\n  }\\n\\n  .header-illustration[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n\\n  .form-row[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n    min-width: 100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "TravellerTitle", "Gender", "PassengerType", "i0", "ɵɵtext", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r2", "errorMessage", "ɵɵelement", "ctx_r18", "formatOfferId", "offerId_r21", "ɵɵtemplate", "BookingTransactionComponent_div_21_div_8_Template", "ɵɵlistener", "BookingTransactionComponent_div_21_Template_form_ngSubmit_9_listener", "ɵɵrestoreView", "_r23", "ctx_r22", "ɵɵnextContext", "ɵɵresetView", "beginTransaction", "BookingTransactionComponent_div_21_mat_spinner_33_Template", "BookingTransactionComponent_div_21_span_34_Template", "ɵɵtextInterpolate1", "ctx_r4", "offerIds", "length", "ɵɵproperty", "beginTransactionForm", "isLoading", "ctx_r5", "beginResponse", "body", "transactionId", "formatDate", "expiresOn", "status", "ctx_r7", "ɵɵtextInterpolate2", "tmp_0_0", "traveller_r24", "get", "value", "type_r40", "label", "title_r41", "type_r42", "gender_r43", "BookingTransactionComponent_mat_expansion_panel_60_button_155_Template_button_click_0_listener", "_r46", "i_r25", "index", "ctx_r44", "removeTraveller", "BookingTransactionComponent_mat_expansion_panel_60_mat_panel_description_4_Template", "BookingTransactionComponent_mat_expansion_panel_60_mat_option_11_Template", "BookingTransactionComponent_mat_expansion_panel_60_mat_option_16_Template", "BookingTransactionComponent_mat_expansion_panel_60_mat_option_21_Template", "BookingTransactionComponent_mat_expansion_panel_60_mat_option_43_Template", "BookingTransactionComponent_mat_expansion_panel_60_mat_error_51_Template", "BookingTransactionComponent_mat_expansion_panel_60_mat_error_71_Template", "BookingTransactionComponent_mat_expansion_panel_60_mat_error_82_Template", "BookingTransactionComponent_mat_expansion_panel_60_mat_error_83_Template", "BookingTransactionComponent_mat_expansion_panel_60_button_155_Template", "tmp_2_0", "ctx_r8", "passengerTypes", "traveller<PERSON><PERSON><PERSON>", "_r30", "genderOptions", "tmp_10_0", "invalid", "tmp_11_0", "_r34", "tmp_14_0", "errors", "tmp_15_0", "_r37", "travellers", "ctx_r11", "infoResponse", "reservationData", "ctx_r13", "ctx_r17", "commitResponse", "reservationNumber", "BookingTransactionComponent", "constructor", "fb", "route", "router", "bookingService", "authService", "snackBar", "currentStep", "Object", "keys", "filter", "key", "isNaN", "Number", "map", "group", "currency", "required", "culture", "reservationInfoForm", "array", "reservationNote", "agencyReservationNumber", "commitTransactionForm", "paymentOption", "paymentInformation", "accountName", "paymentTypeId", "paymentPrice", "amount", "installmentCount", "paymentDate", "Date", "toISOString", "receiptType", "reference", "paymentToken", "ngOnInit", "queryParams", "subscribe", "params", "Array", "isArray", "console", "log", "error", "addTraveller", "travellerForm", "type", "title", "passengerType", "name", "<PERSON><PERSON><PERSON><PERSON>", "surname", "<PERSON><PERSON><PERSON><PERSON>", "birthDate", "nationality", "twoLetterCode", "max<PERSON><PERSON><PERSON>", "identityNumber", "passportInfo", "serial", "number", "expireDate", "issueDate", "citizenshipCountryCode", "issueCountryCode", "address", "contactPhone", "countryCode", "areaCode", "phoneNumber", "email", "zipCode", "city", "id", "country", "gender", "addCustomValidators", "push", "form", "nationalityControl", "issueCountryCodeControl", "valueChanges", "setValue", "expireDateControl", "setValidators", "control", "today", "removeAt", "open", "duration", "panelClass", "formValue", "next", "response", "patchValue", "for<PERSON>ach", "traveller", "addTravellerFromResponse", "message", "travellerId", "setReservationInfo", "controls", "i", "touched", "passportNumberControl", "formattedTravellers", "formatDateForApi", "trim", "request", "reservationInfo", "priceToPay", "includes", "commitTransaction", "previousStep", "dateString", "date", "toLocaleDateString", "getTime", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "formatPrice", "Intl", "NumberFormat", "style", "format", "offerId", "parts", "split", "firstPart", "basicInfo", "shortId", "substring", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "BookingService", "i4", "AuthService", "i5", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "BookingTransactionComponent_Template", "rf", "ctx", "BookingTransactionComponent_ng_template_12_Template", "BookingTransactionComponent_div_19_Template", "BookingTransactionComponent_div_20_Template", "BookingTransactionComponent_div_21_Template", "BookingTransactionComponent_div_22_Template", "BookingTransactionComponent_ng_template_24_Template", "BookingTransactionComponent_div_31_Template", "BookingTransactionComponent_Template_form_ngSubmit_32_listener", "BookingTransactionComponent_Template_button_click_55_listener", "BookingTransactionComponent_mat_expansion_panel_60_Template", "BookingTransactionComponent_mat_spinner_63_Template", "BookingTransactionComponent_span_64_Template", "BookingTransactionComponent_Template_button_click_65_listener", "BookingTransactionComponent_div_67_Template", "BookingTransactionComponent_ng_template_69_Template", "BookingTransactionComponent_div_76_Template", "BookingTransactionComponent_Template_form_ngSubmit_77_listener", "BookingTransactionComponent_mat_spinner_152_Template", "BookingTransactionComponent_span_153_Template", "BookingTransactionComponent_Template_button_click_154_listener", "BookingTransactionComponent_div_156_Template", "_r14"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\booking\\booking-transaction\\booking-transaction.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\booking\\booking-transaction\\booking-transaction.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { BookingService } from '../../../services/booking.service';\nimport { AuthService } from '../../../services/auth.service';\nimport {\n  BookingTransactionResponse,\n  BeginTransactionResponse,\n  SetReservationInfoRequest,\n  SetReservationInfoResponse,\n  CommitTransactionResponse,\n  TravellerBeginResponse,\n  TravellerRequest,\n  PassportInfoRequest,\n  AddressRequest,\n  ContactPhoneRequest,\n  CityRequest,\n  CountryRequest,\n  NationalityRequest\n} from '../../../models/booking';\nimport { TravellerTitle, Gender, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-booking-transaction',\n  templateUrl: './booking-transaction.component.html',\n  styleUrls: ['./booking-transaction.component.css']\n})\nexport class BookingTransactionComponent implements OnInit {\n  // Étape actuelle du processus de réservation\n  currentStep = 1;\n\n  // Formulaires pour chaque étape\n  beginTransactionForm: FormGroup;\n  reservationInfoForm: FormGroup;\n  commitTransactionForm: FormGroup;\n\n  // Données de transaction\n  transactionId: string = '';\n  offerIds: string[] = [];\n\n  // Réponses des différentes étapes\n  beginResponse: BeginTransactionResponse | null = null;\n  infoResponse: SetReservationInfoResponse | null = null;\n  commitResponse: CommitTransactionResponse | null = null;\n\n  // États de chargement et d'erreur\n  isLoading = false;\n  errorMessage = '';\n\n  // Options pour les formulaires\n  travellerTitles = Object.keys(TravellerTitle)\n    .filter(key => !isNaN(Number(TravellerTitle[key as keyof typeof TravellerTitle])))\n    .map(key => ({\n      value: TravellerTitle[key as keyof typeof TravellerTitle],\n      label: key\n    }));\n\n  genderOptions = Object.keys(Gender)\n    .filter(key => !isNaN(Number(Gender[key as keyof typeof Gender])))\n    .map(key => ({\n      value: Gender[key as keyof typeof Gender],\n      label: key\n    }));\n\n  passengerTypes = Object.keys(PassengerType)\n    .filter(key => !isNaN(Number(PassengerType[key as keyof typeof PassengerType])))\n    .map(key => ({\n      value: PassengerType[key as keyof typeof PassengerType],\n      label: key\n    }));\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private bookingService: BookingService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar\n  ) {\n    // Initialisation des formulaires\n    this.beginTransactionForm = this.fb.group({\n      currency: ['EUR', Validators.required],\n      culture: ['fr-FR', Validators.required]\n    });\n\n    this.reservationInfoForm = this.fb.group({\n      transactionId: ['', Validators.required],\n      travellers: this.fb.array([]),\n      reservationNote: [''],\n      agencyReservationNumber: ['']\n    });\n\n    this.commitTransactionForm = this.fb.group({\n      transactionId: ['', Validators.required],\n      paymentOption: [1],\n      paymentInformation: this.fb.group({\n        accountName: [''],\n        paymentTypeId: [1],\n        paymentPrice: this.fb.group({\n          amount: [0, Validators.required],\n          currency: ['EUR', Validators.required]\n        }),\n        installmentCount: ['1'],\n        paymentDate: [new Date().toISOString()],\n        receiptType: [''],\n        reference: [''],\n        paymentToken: ['']\n      })\n    });\n  }\n\n  ngOnInit(): void {\n    // Récupérer les offerIds depuis les paramètres de l'URL\n    this.route.queryParams.subscribe(params => {\n      if (params['offerIds']) {\n        try {\n          this.offerIds = Array.isArray(params['offerIds'])\n            ? params['offerIds']\n            : [params['offerIds']];\n\n          if (this.offerIds.length > 0) {\n            console.log('OfferIds récupérés:', this.offerIds);\n          } else {\n            this.errorMessage = 'Aucun ID d\\'offre n\\'a été fourni.';\n          }\n        } catch (error) {\n          console.error('Erreur lors de la récupération des offerIds:', error);\n          this.errorMessage = 'Format d\\'ID d\\'offre invalide.';\n        }\n      } else {\n        this.errorMessage = 'Aucun ID d\\'offre n\\'a été fourni.';\n      }\n    });\n  }\n\n  // Getter pour accéder au FormArray des voyageurs\n  get travellers(): FormArray {\n    return this.reservationInfoForm.get('travellers') as FormArray;\n  }\n\n  // Ajouter un nouveau voyageur au formulaire\n  addTraveller(): void {\n    const travellerForm = this.fb.group({\n      type: [1, Validators.required], // Type de voyageur (adulte par défaut)\n      title: [1, Validators.required], // Titre (M. par défaut)\n      passengerType: [1, Validators.required], // Type de passager (adulte par défaut)\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      surname: ['', [Validators.required, Validators.minLength(2)]],\n      isLeader: [this.travellers?.length === 0], // Premier voyageur est le leader par défaut\n      birthDate: ['', Validators.required],\n      nationality: this.fb.group({\n        twoLetterCode: ['FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      identityNumber: [''],\n      passportInfo: this.fb.group({\n        serial: [''],\n        number: ['', [Validators.required, Validators.minLength(5)]],\n        expireDate: ['', Validators.required],\n        issueDate: ['', Validators.required],\n        citizenshipCountryCode: ['FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n        issueCountryCode: ['FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      address: this.fb.group({\n        contactPhone: this.fb.group({\n          countryCode: ['+33', Validators.required],\n          areaCode: [''],\n          phoneNumber: ['', [Validators.required, Validators.minLength(5)]]\n        }),\n        email: ['', [Validators.required, Validators.email]],\n        address: ['', [Validators.required, Validators.minLength(5)]],\n        zipCode: ['', [Validators.required, Validators.minLength(3)]],\n        city: this.fb.group({\n          id: ['', Validators.required],\n          name: ['', [Validators.required, Validators.minLength(2)]]\n        }),\n        country: this.fb.group({\n          id: ['FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n          name: ['France', [Validators.required, Validators.minLength(2)]]\n        })\n      }),\n      gender: [1, Validators.required] // Genre (masculin par défaut)\n    });\n\n    // Ajouter des validateurs personnalisés\n    this.addCustomValidators(travellerForm);\n\n    this.travellers.push(travellerForm);\n  }\n\n  // Ajouter des validateurs personnalisés au formulaire de voyageur\n  private addCustomValidators(form: FormGroup): void {\n    // S'assurer que issueCountryCode est toujours renseigné et correspond à la nationalité si non spécifié\n    const nationalityControl = form.get('nationality.twoLetterCode');\n    const issueCountryCodeControl = form.get('passportInfo.issueCountryCode');\n\n    // Synchroniser la valeur de issueCountryCode avec la nationalité si elle est modifiée\n    if (nationalityControl && issueCountryCodeControl) {\n      nationalityControl.valueChanges.subscribe(value => {\n        if (value && (!issueCountryCodeControl.value || issueCountryCodeControl.value === '')) {\n          issueCountryCodeControl.setValue(value);\n        }\n      });\n    }\n\n    // Vérifier que la date d'expiration du passeport est dans le futur\n    const expireDateControl = form.get('passportInfo.expireDate');\n    if (expireDateControl) {\n      expireDateControl.setValidators([\n        Validators.required,\n        (control) => {\n          const value = control.value;\n          if (!value) return null;\n\n          const expireDate = new Date(value);\n          const today = new Date();\n\n          return expireDate > today ? null : { 'expireDateInvalid': true };\n        }\n      ]);\n    }\n  }\n\n  // Supprimer un voyageur du formulaire\n  removeTraveller(index: number): void {\n    this.travellers.removeAt(index);\n  }\n\n  // Démarrer la transaction de réservation\n  beginTransaction(): void {\n    if (this.beginTransactionForm.invalid) {\n      this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {\n        duration: 3000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    const formValue = this.beginTransactionForm.value;\n\n    this.bookingService.beginTransaction(\n      this.offerIds,\n      formValue.currency,\n      formValue.culture\n    ).subscribe({\n      next: (response: BookingTransactionResponse) => {\n        this.isLoading = false;\n        console.log('Réponse reçue:', response);\n\n        if (response && response.beginResponse && response.beginResponse.body) {\n          this.beginResponse = response.beginResponse;\n          this.transactionId = response.beginResponse.body.transactionId || '';\n\n          // Mettre à jour le formulaire d'informations de réservation avec l'ID de transaction\n          this.reservationInfoForm.patchValue({\n            transactionId: this.transactionId\n          });\n\n          // Ajouter les voyageurs existants s'il y en a dans la réponse\n          if (response.beginResponse.body.reservationData &&\n              response.beginResponse.body.reservationData.travellers &&\n              response.beginResponse.body.reservationData.travellers.length > 0) {\n\n            // Vider le tableau de voyageurs existant\n            while (this.travellers.length > 0) {\n              this.travellers.removeAt(0);\n            }\n\n            // Ajouter les voyageurs de la réponse\n            response.beginResponse.body.reservationData.travellers.forEach(traveller => {\n              this.addTravellerFromResponse(traveller);\n            });\n          } else {\n            // Ajouter un voyageur par défaut si aucun n'existe\n            this.addTraveller();\n          }\n\n          // Passer à l'étape suivante\n          this.currentStep = 2;\n\n          this.snackBar.open('Transaction démarrée avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = 'Réponse de transaction invalide.';\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        console.error('Erreur lors du démarrage de la transaction:', error);\n        this.errorMessage = error.message || 'Une erreur est survenue lors du démarrage de la transaction.';\n\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n\n  // Ajouter un voyageur à partir de la réponse de l'API\n  addTravellerFromResponse(traveller: TravellerBeginResponse): void {\n    const travellerForm = this.fb.group({\n      travellerId: [traveller.travellerId || ''],\n      type: [traveller.type || 1, Validators.required],\n      title: [traveller.title || 1, Validators.required],\n      passengerType: [traveller.passengerType || 1, Validators.required],\n      name: [traveller.name || '', [Validators.required, Validators.minLength(2)]],\n      surname: [traveller.surname || '', [Validators.required, Validators.minLength(2)]],\n      isLeader: [traveller.isLeader || this.travellers?.length === 0],\n      birthDate: [traveller.birthDate || '', Validators.required],\n      nationality: this.fb.group({\n        twoLetterCode: [traveller.nationality?.twoLetterCode || 'FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      identityNumber: [traveller.identityNumber || ''],\n      passportInfo: this.fb.group({\n        serial: [traveller.passportInfo?.serial || ''],\n        number: [traveller.passportInfo?.number || '', [Validators.required, Validators.minLength(5)]],\n        expireDate: [traveller.passportInfo?.expireDate || '', Validators.required],\n        issueDate: [traveller.passportInfo?.issueDate || '', Validators.required],\n        citizenshipCountryCode: [traveller.passportInfo?.citizenshipCountryCode || 'FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n        issueCountryCode: [traveller.passportInfo?.issueCountryCode || 'FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      address: this.fb.group({\n        contactPhone: this.fb.group({\n          countryCode: [traveller.address?.contactPhone?.countryCode || '+33', Validators.required],\n          areaCode: [''], // areaCode n'existe pas dans le modèle de réponse, donc on utilise une valeur par défaut\n          phoneNumber: [traveller.address?.contactPhone?.phoneNumber || '', [Validators.required, Validators.minLength(5)]]\n        }),\n        email: [traveller.address?.email || '', [Validators.required, Validators.email]],\n        address: [traveller.address?.address || '', [Validators.required, Validators.minLength(5)]],\n        zipCode: [traveller.address?.zipCode || '', [Validators.required, Validators.minLength(3)]],\n        city: this.fb.group({\n          id: [traveller.address?.city?.id || '', Validators.required],\n          name: [traveller.address?.city?.name || '', [Validators.required, Validators.minLength(2)]]\n        }),\n        country: this.fb.group({\n          id: [traveller.address?.country?.id || 'FR', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n          name: [traveller.address?.country?.name || 'France', [Validators.required, Validators.minLength(2)]]\n        })\n      }),\n      gender: [traveller.gender || 1, Validators.required]\n    });\n\n    // Ajouter des validateurs personnalisés\n    this.addCustomValidators(travellerForm);\n\n    this.travellers.push(travellerForm);\n  }\n\n  // Définir les informations de réservation\n  setReservationInfo(): void {\n    if (this.reservationInfoForm.invalid) {\n      // Vérifier les erreurs spécifiques pour donner des messages plus précis\n      let errorMessage = 'Veuillez remplir tous les champs obligatoires.';\n\n      // Vérifier les erreurs de passeport\n      const travellers = this.travellers.controls;\n      for (let i = 0; i < travellers.length; i++) {\n        const traveller = travellers[i] as FormGroup;\n\n        // Vérifier les erreurs de date d'expiration du passeport\n        const expireDateControl = traveller.get('passportInfo.expireDate');\n        if (expireDateControl?.errors?.['expireDateInvalid']) {\n          errorMessage = `Voyageur ${i+1}: La date d'expiration du passeport doit être dans le futur.`;\n          break;\n        }\n\n        // Vérifier les erreurs de code pays\n        const issueCountryCodeControl = traveller.get('passportInfo.issueCountryCode');\n        if (issueCountryCodeControl?.invalid && issueCountryCodeControl?.touched) {\n          errorMessage = `Voyageur ${i+1}: Le code pays d'émission du passeport est invalide.`;\n          break;\n        }\n\n        // Vérifier les erreurs de numéro de passeport\n        const passportNumberControl = traveller.get('passportInfo.number');\n        if (passportNumberControl?.invalid && passportNumberControl?.touched) {\n          errorMessage = `Voyageur ${i+1}: Le numéro de passeport est invalide.`;\n          break;\n        }\n      }\n\n      this.snackBar.open(errorMessage, 'Fermer', {\n        duration: 5000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    const formValue = this.reservationInfoForm.value;\n\n    // Formater correctement les dates pour chaque voyageur\n    const formattedTravellers = formValue.travellers.map(traveller => {\n      // Formater la date de naissance\n      if (traveller.birthDate) {\n        if (traveller.birthDate instanceof Date) {\n          traveller.birthDate = this.formatDateForApi(traveller.birthDate);\n        } else if (typeof traveller.birthDate === 'string' && traveller.birthDate.trim() !== '') {\n          traveller.birthDate = this.formatDateForApi(new Date(traveller.birthDate));\n        }\n      }\n\n      // Formater les dates de passeport si présentes\n      if (traveller.passportInfo) {\n        if (traveller.passportInfo.expireDate) {\n          if (traveller.passportInfo.expireDate instanceof Date) {\n            traveller.passportInfo.expireDate = this.formatDateForApi(traveller.passportInfo.expireDate);\n          } else if (typeof traveller.passportInfo.expireDate === 'string' && traveller.passportInfo.expireDate.trim() !== '') {\n            traveller.passportInfo.expireDate = this.formatDateForApi(new Date(traveller.passportInfo.expireDate));\n          }\n        }\n\n        if (traveller.passportInfo.issueDate) {\n          if (traveller.passportInfo.issueDate instanceof Date) {\n            traveller.passportInfo.issueDate = this.formatDateForApi(traveller.passportInfo.issueDate);\n          } else if (typeof traveller.passportInfo.issueDate === 'string' && traveller.passportInfo.issueDate.trim() !== '') {\n            traveller.passportInfo.issueDate = this.formatDateForApi(new Date(traveller.passportInfo.issueDate));\n          }\n        }\n      }\n\n      return traveller;\n    });\n\n    // Créer la requête d'informations de réservation\n    const request: SetReservationInfoRequest = {\n      transactionId: formValue.transactionId,\n      travellers: formattedTravellers,\n      reservationNote: formValue.reservationNote,\n      agencyReservationNumber: formValue.agencyReservationNumber\n    };\n\n    this.bookingService.setReservationInfo(request).subscribe({\n      next: (response: BookingTransactionResponse) => {\n        this.isLoading = false;\n        console.log('Réponse setReservationInfo reçue:', response);\n\n        if (response && response.infoResponse && response.infoResponse.body) {\n          this.infoResponse = response.infoResponse;\n\n          // Mettre à jour le formulaire de finalisation de transaction avec l'ID de transaction\n          this.commitTransactionForm.patchValue({\n            transactionId: this.transactionId\n          });\n\n          // Mettre à jour le montant du paiement si disponible\n          if (response.infoResponse.body.reservationData &&\n              response.infoResponse.body.reservationData.reservationInfo &&\n              response.infoResponse.body.reservationData.reservationInfo.priceToPay) {\n\n            const priceToPay = response.infoResponse.body.reservationData.reservationInfo.priceToPay;\n\n            this.commitTransactionForm.get('paymentInformation.paymentPrice')?.patchValue({\n              amount: priceToPay.amount,\n              currency: priceToPay.currency\n            });\n          }\n\n          // Passer à l'étape suivante\n          this.currentStep = 3;\n\n          this.snackBar.open('Informations de réservation définies avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = 'Réponse d\\'informations de réservation invalide.';\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        console.error('Erreur lors de la définition des informations de réservation:', error);\n\n        // Analyser le message d'erreur pour des problèmes spécifiques\n        let errorMessage = error.message || 'Une erreur est survenue lors de la définition des informations de réservation.';\n\n        // Vérifier les erreurs spécifiques liées au passeport\n        if (errorMessage.includes('passport') || errorMessage.includes('issueCountryCode')) {\n          errorMessage = 'Erreur de validation du passeport: Veuillez vérifier que toutes les informations de passeport sont complètes, notamment le code pays d\\'émission.';\n        }\n        // Vérifier les erreurs liées aux dates\n        else if (errorMessage.includes('date') || errorMessage.includes('expireDate')) {\n          errorMessage = 'Erreur de validation des dates: Veuillez vérifier que toutes les dates sont au format correct (YYYY-MM-DD).';\n        }\n        // Vérifier les erreurs liées aux informations personnelles\n        else if (errorMessage.includes('name') || errorMessage.includes('surname')) {\n          errorMessage = 'Erreur de validation des informations personnelles: Veuillez vérifier que tous les noms et prénoms sont correctement renseignés.';\n        }\n\n        this.errorMessage = errorMessage;\n\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 8000, // Durée plus longue pour les messages d'erreur détaillés\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n\n  // Finaliser la transaction de réservation\n  commitTransaction(): void {\n    if (this.commitTransactionForm.invalid) {\n      this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {\n        duration: 3000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    const formValue = this.commitTransactionForm.value;\n\n    this.bookingService.commitTransaction(formValue).subscribe({\n      next: (response: BookingTransactionResponse) => {\n        this.isLoading = false;\n        console.log('Réponse commitTransaction reçue:', response);\n\n        if (response && response.commitResponse && response.commitResponse.body) {\n          this.commitResponse = response.commitResponse;\n\n          this.snackBar.open('Réservation finalisée avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n\n          // Afficher les détails de la réservation finalisée\n          // Vous pourriez rediriger vers une page de confirmation ici\n        } else {\n          this.errorMessage = 'Réponse de finalisation de transaction invalide.';\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        console.error('Erreur lors de la finalisation de la transaction:', error);\n        this.errorMessage = error.message || 'Une erreur est survenue lors de la finalisation de la transaction.';\n\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n\n  // Revenir à l'étape précédente\n  previousStep(): void {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n\n  // Formater une date pour l'affichage\n  formatDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    const date = new Date(dateString);\n    return date.toLocaleDateString();\n  }\n\n  // Formater une date pour l'API (format ISO 8601: YYYY-MM-DD)\n  formatDateForApi(date: Date): string {\n    if (!date) return '';\n\n    // S'assurer que c'est une instance de Date valide\n    if (!(date instanceof Date) || isNaN(date.getTime())) {\n      console.error('Date invalide:', date);\n      return '';\n    }\n\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n\n    return `${year}-${month}-${day}`;\n  }\n\n  // Formater un prix pour l'affichage\n  formatPrice(amount: number, currency: string): string {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: currency\n    }).format(amount);\n  }\n\n  // Formater un ID d'offre pour l'affichage\n  formatOfferId(offerId: string): string {\n    if (!offerId) return 'N/A';\n\n    // Extraire les informations pertinentes de l'ID d'offre\n    // Format typique: 13$3$1~^006^~AAABloLbX6oAAAAClhW0YYMc26FHjKULIrlKaQAAAZaC2zyuAAAAALH6UPiglvPwEjLokBT6TDI=~^006^~1~^006^~154.66~^006^~~^006^~154.66~^006^~ODBiZTZiMGQtYWYxYy00MzYzLThmNjctODcyNTA0NjVjZjgz\n\n    // Extraire le début de l'ID (avant le premier ~)\n    const parts = offerId.split('~');\n    const firstPart = parts[0] || '';\n\n    // Extraire les informations de base (type de vol, classe, etc.)\n    const basicInfo = firstPart.split('$');\n\n    // Créer un identifiant court\n    const shortId = offerId.substring(0, 8) + '...' + offerId.substring(offerId.length - 8);\n\n    return `Vol #${basicInfo[0] || 'N/A'} - Référence: ${shortId}`;\n  }\n}\n", "<div class=\"booking-transaction-container\">\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <h1 class=\"page-title\">Réservation de vol</h1>\n      <p class=\"page-subtitle\">Complétez votre réservation en quelques étapes simples</p>\n    </div>\n    <div class=\"header-illustration\">\n      <img src=\"assets/images/booking-banner.jpg\" alt=\"Réservation de vol\">\n    </div>\n  </div>\n\n  <!-- Stepper pour les étapes de réservation -->\n  <mat-horizontal-stepper [linear]=\"true\" #stepper [selectedIndex]=\"currentStep - 1\">\n    <!-- Étape 1: Démarrer la transaction -->\n    <mat-step [completed]=\"beginResponse !== null\">\n      <ng-template matStepLabel>Démarrer la réservation</ng-template>\n\n      <div class=\"step-content\">\n        <div class=\"step-header\">\n          <h2>Démarrer votre réservation</h2>\n          <p>Nous allons commencer le processus de réservation pour les vols sélectionnés.</p>\n        </div>\n\n        <div *ngIf=\"errorMessage\" class=\"error-message\">\n          <mat-icon>error</mat-icon>\n          <span>{{ errorMessage }}</span>\n        </div>\n\n        <div *ngIf=\"offerIds.length === 0\" class=\"error-message\">\n          <mat-icon>error</mat-icon>\n          <span>Aucun vol n'a été sélectionné. Veuillez retourner à la page de recherche et sélectionner un vol.</span>\n          <button mat-raised-button color=\"primary\" routerLink=\"/search-price\">\n            Retour à la recherche\n          </button>\n        </div>\n\n        <div *ngIf=\"offerIds.length > 0\">\n          <div class=\"offer-summary\">\n            <h3>Résumé de votre sélection</h3>\n            <div class=\"offer-ids\">\n              <p>Offres sélectionnées: {{ offerIds.length }}</p>\n              <div class=\"offer-card-container\">\n                <div class=\"offer-card\" *ngFor=\"let offerId of offerIds\">\n                  <div class=\"offer-card-header\">\n                    <i class=\"fas fa-plane\"></i>\n                    <span class=\"offer-title\">{{ formatOfferId(offerId) }}</span>\n                  </div>\n                  <div class=\"offer-card-content\">\n                    <p class=\"offer-info\">Votre vol est prêt à être réservé</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <form [formGroup]=\"beginTransactionForm\" (ngSubmit)=\"beginTransaction()\">\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Devise</mat-label>\n                <mat-select formControlName=\"currency\">\n                  <mat-option value=\"EUR\">Euro (EUR)</mat-option>\n                  <mat-option value=\"USD\">Dollar américain (USD)</mat-option>\n                  <mat-option value=\"GBP\">Livre sterling (GBP)</mat-option>\n                </mat-select>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Culture</mat-label>\n                <mat-select formControlName=\"culture\">\n                  <mat-option value=\"fr-FR\">Français (FR)</mat-option>\n                  <mat-option value=\"en-US\">Anglais (US)</mat-option>\n                  <mat-option value=\"en-GB\">Anglais (GB)</mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-actions\">\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"isLoading\">\n                <mat-spinner *ngIf=\"isLoading\" diameter=\"20\" class=\"button-spinner\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">Démarrer la réservation</span>\n              </button>\n              <button mat-button type=\"button\" routerLink=\"/get-offer\">Annuler</button>\n            </div>\n          </form>\n        </div>\n\n        <!-- Affichage de la réponse de début de transaction -->\n        <div *ngIf=\"beginResponse && beginResponse.body\" class=\"response-summary\">\n          <h3>Détails de la transaction</h3>\n          <div class=\"transaction-details\">\n            <p><strong>ID de transaction:</strong> {{ beginResponse.body.transactionId || 'N/A' }}</p>\n            <p><strong>Expire le:</strong> {{ formatDate(beginResponse.body.expiresOn) }}</p>\n            <p><strong>Statut:</strong> {{ beginResponse.body.status || 'N/A' }}</p>\n          </div>\n        </div>\n      </div>\n    </mat-step>\n\n    <!-- Étape 2: Définir les informations de réservation -->\n    <mat-step [completed]=\"infoResponse !== null\">\n      <ng-template matStepLabel>Informations voyageurs</ng-template>\n\n      <div class=\"step-content\">\n        <div class=\"step-header\">\n          <h2>Informations des voyageurs</h2>\n          <p>Veuillez fournir les informations pour tous les voyageurs.</p>\n        </div>\n\n        <div *ngIf=\"errorMessage\" class=\"error-message\">\n          <mat-icon>error</mat-icon>\n          <span>{{ errorMessage }}</span>\n        </div>\n\n        <form [formGroup]=\"reservationInfoForm\" (ngSubmit)=\"setReservationInfo()\">\n          <div class=\"form-section\">\n            <h3>Informations générales</h3>\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>ID de transaction</mat-label>\n                <input matInput formControlName=\"transactionId\" readonly>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Note de réservation</mat-label>\n                <textarea matInput formControlName=\"reservationNote\" rows=\"3\"></textarea>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Numéro de réservation d'agence</mat-label>\n                <input matInput formControlName=\"agencyReservationNumber\">\n              </mat-form-field>\n            </div>\n          </div>\n\n          <div class=\"form-section\">\n            <div class=\"section-header\">\n              <h3>Voyageurs</h3>\n              <button mat-mini-fab color=\"primary\" type=\"button\" (click)=\"addTraveller()\" aria-label=\"Ajouter un voyageur\">\n                <mat-icon>add</mat-icon>\n              </button>\n            </div>\n\n            <div formArrayName=\"travellers\">\n              <mat-accordion>\n                <mat-expansion-panel *ngFor=\"let traveller of travellers.controls; let i = index\" [expanded]=\"i === 0\">\n                  <mat-expansion-panel-header>\n                    <mat-panel-title>\n                      Voyageur {{ i + 1 }}\n                    </mat-panel-title>\n                    <mat-panel-description *ngIf=\"traveller.get('name')?.value || traveller.get('surname')?.value\">\n                      {{ traveller.get('name')?.value }} {{ traveller.get('surname')?.value }}\n                    </mat-panel-description>\n                  </mat-expansion-panel-header>\n\n                  <div [formGroupName]=\"i\">\n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Type de voyageur</mat-label>\n                        <mat-select formControlName=\"type\">\n                          <mat-option *ngFor=\"let type of passengerTypes\" [value]=\"type.value\">\n                            {{ type.label }}\n                          </mat-option>\n                        </mat-select>\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Titre</mat-label>\n                        <mat-select formControlName=\"title\">\n                          <mat-option *ngFor=\"let title of travellerTitles\" [value]=\"title.value\">\n                            {{ title.label }}\n                          </mat-option>\n                        </mat-select>\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Type de passager</mat-label>\n                        <mat-select formControlName=\"passengerType\">\n                          <mat-option *ngFor=\"let type of passengerTypes\" [value]=\"type.value\">\n                            {{ type.label }}\n                          </mat-option>\n                        </mat-select>\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Prénom</mat-label>\n                        <input matInput formControlName=\"name\" required>\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Nom</mat-label>\n                        <input matInput formControlName=\"surname\" required>\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Date de naissance</mat-label>\n                        <input matInput [matDatepicker]=\"birthDatePicker\" formControlName=\"birthDate\" required>\n                        <mat-datepicker-toggle matSuffix [for]=\"birthDatePicker\"></mat-datepicker-toggle>\n                        <mat-datepicker #birthDatePicker></mat-datepicker>\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Genre</mat-label>\n                        <mat-select formControlName=\"gender\">\n                          <mat-option *ngFor=\"let gender of genderOptions\" [value]=\"gender.value\">\n                            {{ gender.label }}\n                          </mat-option>\n                        </mat-select>\n                      </mat-form-field>\n\n                      <div formGroupName=\"nationality\">\n                        <mat-form-field appearance=\"outline\">\n                          <mat-label>Nationalité (code pays)</mat-label>\n                          <input matInput formControlName=\"twoLetterCode\" required placeholder=\"FR\">\n                          <mat-hint>Code pays à 2 lettres (ex: FR, US, GB)</mat-hint>\n                          <mat-error *ngIf=\"traveller.get('nationality.twoLetterCode')?.invalid\">\n                            Code pays invalide (2 lettres requis)\n                          </mat-error>\n                        </mat-form-field>\n                      </div>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Numéro d'identité</mat-label>\n                        <input matInput formControlName=\"identityNumber\">\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"form-section\">\n                      <h4>Informations de passeport</h4>\n                      <div formGroupName=\"passportInfo\">\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Numéro de série</mat-label>\n                            <input matInput formControlName=\"serial\">\n                          </mat-form-field>\n\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Numéro de passeport</mat-label>\n                            <input matInput formControlName=\"number\" required>\n                            <mat-hint>Minimum 5 caractères</mat-hint>\n                            <mat-error *ngIf=\"traveller.get('passportInfo.number')?.invalid\">\n                              Numéro de passeport invalide (minimum 5 caractères)\n                            </mat-error>\n                          </mat-form-field>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Date d'expiration</mat-label>\n                            <input matInput [matDatepicker]=\"expireDatePicker\" formControlName=\"expireDate\" required>\n                            <mat-datepicker-toggle matSuffix [for]=\"expireDatePicker\"></mat-datepicker-toggle>\n                            <mat-datepicker #expireDatePicker></mat-datepicker>\n                            <mat-hint>Doit être dans le futur</mat-hint>\n                            <mat-error *ngIf=\"traveller.get('passportInfo.expireDate')?.errors?.['expireDateInvalid']\">\n                              La date d'expiration doit être dans le futur\n                            </mat-error>\n                            <mat-error *ngIf=\"traveller.get('passportInfo.expireDate')?.errors?.['required']\">\n                              La date d'expiration est requise\n                            </mat-error>\n                          </mat-form-field>\n\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Date d'émission</mat-label>\n                            <input matInput [matDatepicker]=\"issueDatePicker\" formControlName=\"issueDate\" required>\n                            <mat-datepicker-toggle matSuffix [for]=\"issueDatePicker\"></mat-datepicker-toggle>\n                            <mat-datepicker #issueDatePicker></mat-datepicker>\n                          </mat-form-field>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Code pays de citoyenneté</mat-label>\n                            <input matInput formControlName=\"citizenshipCountryCode\" required>\n                          </mat-form-field>\n\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Code pays d'émission</mat-label>\n                            <input matInput formControlName=\"issueCountryCode\" required>\n                          </mat-form-field>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"form-section\">\n                      <h4>Adresse</h4>\n                      <div formGroupName=\"address\">\n                        <div class=\"form-row\">\n                          <div formGroupName=\"contactPhone\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Code pays</mat-label>\n                              <input matInput formControlName=\"countryCode\" required>\n                            </mat-form-field>\n\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Indicatif régional</mat-label>\n                              <input matInput formControlName=\"areaCode\">\n                            </mat-form-field>\n\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Numéro de téléphone</mat-label>\n                              <input matInput formControlName=\"phoneNumber\" required>\n                            </mat-form-field>\n                          </div>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\" class=\"full-width\">\n                            <mat-label>Email</mat-label>\n                            <input matInput formControlName=\"email\" required type=\"email\">\n                          </mat-form-field>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\" class=\"full-width\">\n                            <mat-label>Adresse</mat-label>\n                            <input matInput formControlName=\"address\" required>\n                          </mat-form-field>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Code postal</mat-label>\n                            <input matInput formControlName=\"zipCode\" required>\n                          </mat-form-field>\n\n                          <div formGroupName=\"city\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>ID de ville</mat-label>\n                              <input matInput formControlName=\"id\" required>\n                            </mat-form-field>\n\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Nom de ville</mat-label>\n                              <input matInput formControlName=\"name\" required>\n                            </mat-form-field>\n                          </div>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <div formGroupName=\"country\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>ID de pays</mat-label>\n                              <input matInput formControlName=\"id\" required>\n                            </mat-form-field>\n\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Nom de pays</mat-label>\n                              <input matInput formControlName=\"name\" required>\n                            </mat-form-field>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"form-row traveller-actions\">\n                      <mat-checkbox formControlName=\"isLeader\">Chef de groupe</mat-checkbox>\n                      <button mat-button color=\"warn\" type=\"button\" (click)=\"removeTraveller(i)\" *ngIf=\"travellers.length > 1\">\n                        <mat-icon>delete</mat-icon> Supprimer ce voyageur\n                      </button>\n                    </div>\n                  </div>\n                </mat-expansion-panel>\n              </mat-accordion>\n            </div>\n          </div>\n\n          <div class=\"form-actions\">\n            <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"isLoading || reservationInfoForm.invalid\">\n              <mat-spinner *ngIf=\"isLoading\" diameter=\"20\" class=\"button-spinner\"></mat-spinner>\n              <span *ngIf=\"!isLoading\">Enregistrer les informations</span>\n            </button>\n            <button mat-button type=\"button\" (click)=\"previousStep()\">Retour</button>\n          </div>\n        </form>\n\n        <!-- Affichage de la réponse d'informations de réservation -->\n        <div *ngIf=\"infoResponse && infoResponse.body\" class=\"response-summary\">\n          <h3>Informations de réservation enregistrées</h3>\n          <div class=\"reservation-details\">\n            <p><strong>ID de transaction:</strong> {{ infoResponse.body.transactionId || 'N/A' }}</p>\n            <p><strong>Nombre de voyageurs:</strong> {{ infoResponse.body.reservationData && infoResponse.body.reservationData.travellers ? infoResponse.body.reservationData.travellers.length : 0 }}</p>\n          </div>\n        </div>\n      </div>\n    </mat-step>\n\n    <!-- Étape 3: Finaliser la transaction -->\n    <mat-step [completed]=\"commitResponse !== null\">\n      <ng-template matStepLabel>Paiement et confirmation</ng-template>\n\n      <div class=\"step-content\">\n        <div class=\"step-header\">\n          <h2>Paiement et confirmation</h2>\n          <p>Finalisez votre réservation en effectuant le paiement.</p>\n        </div>\n\n        <div *ngIf=\"errorMessage\" class=\"error-message\">\n          <mat-icon>error</mat-icon>\n          <span>{{ errorMessage }}</span>\n        </div>\n\n        <form [formGroup]=\"commitTransactionForm\" (ngSubmit)=\"commitTransaction()\">\n          <div class=\"form-section\">\n            <h3>Informations de paiement</h3>\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>ID de transaction</mat-label>\n                <input matInput formControlName=\"transactionId\" readonly>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Option de paiement</mat-label>\n                <mat-select formControlName=\"paymentOption\">\n                  <mat-option [value]=\"1\">Carte de crédit</mat-option>\n                  <mat-option [value]=\"2\">Virement bancaire</mat-option>\n                  <mat-option [value]=\"3\">PayPal</mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div formGroupName=\"paymentInformation\">\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Nom du titulaire</mat-label>\n                  <input matInput formControlName=\"accountName\">\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Type de paiement</mat-label>\n                  <mat-select formControlName=\"paymentTypeId\">\n                    <mat-option [value]=\"1\">Carte de crédit</mat-option>\n                    <mat-option [value]=\"2\">Virement bancaire</mat-option>\n                    <mat-option [value]=\"3\">PayPal</mat-option>\n                  </mat-select>\n                </mat-form-field>\n              </div>\n\n              <div class=\"form-row\">\n                <div formGroupName=\"paymentPrice\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Montant</mat-label>\n                    <input matInput formControlName=\"amount\" type=\"number\" required>\n                  </mat-form-field>\n\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Devise</mat-label>\n                    <mat-select formControlName=\"currency\">\n                      <mat-option value=\"EUR\">Euro (EUR)</mat-option>\n                      <mat-option value=\"USD\">Dollar américain (USD)</mat-option>\n                      <mat-option value=\"GBP\">Livre sterling (GBP)</mat-option>\n                    </mat-select>\n                  </mat-form-field>\n                </div>\n              </div>\n\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Nombre de versements</mat-label>\n                  <input matInput formControlName=\"installmentCount\">\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Date de paiement</mat-label>\n                  <input matInput [matDatepicker]=\"paymentDatePicker\" formControlName=\"paymentDate\">\n                  <mat-datepicker-toggle matSuffix [for]=\"paymentDatePicker\"></mat-datepicker-toggle>\n                  <mat-datepicker #paymentDatePicker></mat-datepicker>\n                </mat-form-field>\n              </div>\n\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Type de reçu</mat-label>\n                  <input matInput formControlName=\"receiptType\">\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Référence</mat-label>\n                  <input matInput formControlName=\"reference\">\n                </mat-form-field>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-actions\">\n            <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"isLoading || commitTransactionForm.invalid\">\n              <mat-spinner *ngIf=\"isLoading\" diameter=\"20\" class=\"button-spinner\"></mat-spinner>\n              <span *ngIf=\"!isLoading\">Finaliser la réservation</span>\n            </button>\n            <button mat-button type=\"button\" (click)=\"previousStep()\">Retour</button>\n          </div>\n        </form>\n\n        <!-- Affichage de la réponse de finalisation de transaction -->\n        <div *ngIf=\"commitResponse && commitResponse.body\" class=\"response-summary success-response\">\n          <h3>Réservation confirmée!</h3>\n          <div class=\"confirmation-details\">\n            <p><strong>Numéro de réservation:</strong> {{ commitResponse.body.reservationNumber || 'N/A' }}</p>\n            <p><strong>ID de transaction:</strong> {{ commitResponse.body.transactionId || 'N/A' }}</p>\n\n            <div class=\"confirmation-message\">\n              <mat-icon>check_circle</mat-icon>\n              <p>Votre réservation a été confirmée avec succès. Vous recevrez bientôt un email de confirmation avec tous les détails de votre voyage.</p>\n            </div>\n\n            <div class=\"confirmation-actions\">\n              <button mat-raised-button color=\"primary\" routerLink=\"/search-price\">\n                Retour à la recherche\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </mat-step>\n  </mat-horizontal-stepper>\n</div>\n"], "mappings": "AACA,SAA4CA,UAAU,QAAQ,gBAAgB;AAoB9E,SAASC,cAAc,EAAEC,MAAM,EAAEC,aAAa,QAAQ,6BAA6B;;;;;;;;;;;;;;;;;;;;ICNnDC,EAAA,CAAAC,MAAA,wCAAuB;;;;;IAQ/CD,EAAA,CAAAE,cAAA,cAAgD;IACpCF,EAAA,CAAAC,MAAA,YAAK;IAAAD,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAC,MAAA,GAAkB;IAAAD,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;IAG1BP,EAAA,CAAAE,cAAA,cAAyD;IAC7CF,EAAA,CAAAC,MAAA,YAAK;IAAAD,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAC,MAAA,qIAAgG;IAAAD,EAAA,CAAAG,YAAA,EAAO;IAC7GH,EAAA,CAAAE,cAAA,iBAAqE;IACnEF,EAAA,CAAAC,MAAA,mCACF;IAAAD,EAAA,CAAAG,YAAA,EAAS;;;;;IASHH,EAAA,CAAAE,cAAA,cAAyD;IAErDF,EAAA,CAAAQ,SAAA,YAA4B;IAC5BR,EAAA,CAAAE,cAAA,eAA0B;IAAAF,EAAA,CAAAC,MAAA,GAA4B;IAAAD,EAAA,CAAAG,YAAA,EAAO;IAE/DH,EAAA,CAAAE,cAAA,cAAgC;IACRF,EAAA,CAAAC,MAAA,iEAAiC;IAAAD,EAAA,CAAAG,YAAA,EAAI;;;;;IAHjCH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,iBAAA,CAAAI,OAAA,CAAAC,aAAA,CAAAC,WAAA,EAA4B;;;;;IAiC1DX,EAAA,CAAAQ,SAAA,sBAAkF;;;;;IAClFR,EAAA,CAAAE,cAAA,WAAyB;IAAAF,EAAA,CAAAC,MAAA,wCAAuB;IAAAD,EAAA,CAAAG,YAAA,EAAO;;;;;;IA3C/DH,EAAA,CAAAE,cAAA,UAAiC;IAEzBF,EAAA,CAAAC,MAAA,+CAAyB;IAAAD,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAE,cAAA,cAAuB;IAClBF,EAAA,CAAAC,MAAA,GAA2C;IAAAD,EAAA,CAAAG,YAAA,EAAI;IAClDH,EAAA,CAAAE,cAAA,cAAkC;IAChCF,EAAA,CAAAY,UAAA,IAAAC,iDAAA,kBAQM;IACRb,EAAA,CAAAG,YAAA,EAAM;IAIVH,EAAA,CAAAE,cAAA,eAAyE;IAAhCF,EAAA,CAAAc,UAAA,sBAAAC,qEAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAYnB,EAAA,CAAAoB,WAAA,CAAAF,OAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IACtErB,EAAA,CAAAE,cAAA,eAAsB;IAEPF,EAAA,CAAAC,MAAA,cAAM;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC7BH,EAAA,CAAAE,cAAA,sBAAuC;IACbF,EAAA,CAAAC,MAAA,kBAAU;IAAAD,EAAA,CAAAG,YAAA,EAAa;IAC/CH,EAAA,CAAAE,cAAA,sBAAwB;IAAAF,EAAA,CAAAC,MAAA,mCAAsB;IAAAD,EAAA,CAAAG,YAAA,EAAa;IAC3DH,EAAA,CAAAE,cAAA,sBAAwB;IAAAF,EAAA,CAAAC,MAAA,4BAAoB;IAAAD,EAAA,CAAAG,YAAA,EAAa;IAI7DH,EAAA,CAAAE,cAAA,0BAAqC;IACxBF,EAAA,CAAAC,MAAA,eAAO;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC9BH,EAAA,CAAAE,cAAA,sBAAsC;IACVF,EAAA,CAAAC,MAAA,0BAAa;IAAAD,EAAA,CAAAG,YAAA,EAAa;IACpDH,EAAA,CAAAE,cAAA,sBAA0B;IAAAF,EAAA,CAAAC,MAAA,oBAAY;IAAAD,EAAA,CAAAG,YAAA,EAAa;IACnDH,EAAA,CAAAE,cAAA,sBAA0B;IAAAF,EAAA,CAAAC,MAAA,oBAAY;IAAAD,EAAA,CAAAG,YAAA,EAAa;IAKzDH,EAAA,CAAAE,cAAA,eAA0B;IAEtBF,EAAA,CAAAY,UAAA,KAAAU,0DAAA,0BAAkF;IAClFtB,EAAA,CAAAY,UAAA,KAAAW,mDAAA,mBAAuD;IACzDvB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAE,cAAA,kBAAyD;IAAAF,EAAA,CAAAC,MAAA,eAAO;IAAAD,EAAA,CAAAG,YAAA,EAAS;;;;IAzCtEH,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAwB,kBAAA,qCAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,KAA2C;IAEA3B,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAA4B,UAAA,YAAAH,MAAA,CAAAC,QAAA,CAAW;IAavD1B,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAA4B,UAAA,cAAAH,MAAA,CAAAI,oBAAA,CAAkC;IAsBoB7B,EAAA,CAAAI,SAAA,IAAsB;IAAtBJ,EAAA,CAAA4B,UAAA,aAAAH,MAAA,CAAAK,SAAA,CAAsB;IAC9D9B,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAA4B,UAAA,SAAAH,MAAA,CAAAK,SAAA,CAAe;IACtB9B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA4B,UAAA,UAAAH,MAAA,CAAAK,SAAA,CAAgB;;;;;IAQ/B9B,EAAA,CAAAE,cAAA,cAA0E;IACpEF,EAAA,CAAAC,MAAA,qCAAyB;IAAAD,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAE,cAAA,cAAiC;IACpBF,EAAA,CAAAC,MAAA,yBAAkB;IAAAD,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAC,MAAA,GAA+C;IAAAD,EAAA,CAAAG,YAAA,EAAI;IAC1FH,EAAA,CAAAE,cAAA,QAAG;IAAQF,EAAA,CAAAC,MAAA,kBAAU;IAAAD,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAC,MAAA,IAA8C;IAAAD,EAAA,CAAAG,YAAA,EAAI;IACjFH,EAAA,CAAAE,cAAA,SAAG;IAAQF,EAAA,CAAAC,MAAA,eAAO;IAAAD,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAC,MAAA,IAAwC;IAAAD,EAAA,CAAAG,YAAA,EAAI;;;;IAFjCH,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAwB,kBAAA,MAAAO,MAAA,CAAAC,aAAA,CAAAC,IAAA,CAAAC,aAAA,cAA+C;IACvDlC,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAwB,kBAAA,MAAAO,MAAA,CAAAI,UAAA,CAAAJ,MAAA,CAAAC,aAAA,CAAAC,IAAA,CAAAG,SAAA,MAA8C;IACjDpC,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAwB,kBAAA,MAAAO,MAAA,CAAAC,aAAA,CAAAC,IAAA,CAAAI,MAAA,cAAwC;;;;;IAQhDrC,EAAA,CAAAC,MAAA,6BAAsB;;;;;IAQ9CD,EAAA,CAAAE,cAAA,cAAgD;IACpCF,EAAA,CAAAC,MAAA,YAAK;IAAAD,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAC,MAAA,GAAkB;IAAAD,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAiC,MAAA,CAAA/B,YAAA,CAAkB;;;;;IA2CdP,EAAA,CAAAE,cAAA,4BAA+F;IAC7FF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAwB;;;;;IADtBH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuC,kBAAA,OAAAC,OAAA,GAAAC,aAAA,CAAAC,GAAA,2BAAAF,OAAA,CAAAG,KAAA,QAAAH,OAAA,GAAAC,aAAA,CAAAC,GAAA,8BAAAF,OAAA,CAAAG,KAAA,MACF;;;;;IAQM3C,EAAA,CAAAE,cAAA,qBAAqE;IACnEF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAA4B,UAAA,UAAAgB,QAAA,CAAAD,KAAA,CAAoB;IAClE3C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAoB,QAAA,CAAAC,KAAA,MACF;;;;;IAOA7C,EAAA,CAAAE,cAAA,qBAAwE;IACtEF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAa;;;;IAFqCH,EAAA,CAAA4B,UAAA,UAAAkB,SAAA,CAAAH,KAAA,CAAqB;IACrE3C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAsB,SAAA,CAAAD,KAAA,MACF;;;;;IAOA7C,EAAA,CAAAE,cAAA,qBAAqE;IACnEF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAA4B,UAAA,UAAAmB,QAAA,CAAAJ,KAAA,CAAoB;IAClE3C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAuB,QAAA,CAAAF,KAAA,MACF;;;;;IA4BA7C,EAAA,CAAAE,cAAA,qBAAwE;IACtEF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAa;;;;IAFoCH,EAAA,CAAA4B,UAAA,UAAAoB,UAAA,CAAAL,KAAA,CAAsB;IACrE3C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAwB,UAAA,CAAAH,KAAA,MACF;;;;;IASA7C,EAAA,CAAAE,cAAA,gBAAuE;IACrEF,EAAA,CAAAC,MAAA,8CACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;;IAuBVH,EAAA,CAAAE,cAAA,gBAAiE;IAC/DF,EAAA,CAAAC,MAAA,sEACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAE,cAAA,gBAA2F;IACzFF,EAAA,CAAAC,MAAA,0DACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAE,cAAA,gBAAkF;IAChFF,EAAA,CAAAC,MAAA,yCACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;;;IAkGlBH,EAAA,CAAAE,cAAA,kBAAyG;IAA3DF,EAAA,CAAAc,UAAA,mBAAAmC,+FAAA;MAAAjD,EAAA,CAAAgB,aAAA,CAAAkC,IAAA;MAAA,MAAAC,KAAA,GAAAnD,EAAA,CAAAmB,aAAA,GAAAiC,KAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAiC,OAAA,CAAAC,eAAA,CAAAH,KAAA,CAAkB;IAAA,EAAC;IACxEnD,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAC,MAAA,aAAM;IAAAD,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAC,MAAA,8BAC9B;IAAAD,EAAA,CAAAG,YAAA,EAAS;;;;;IAzNfH,EAAA,CAAAE,cAAA,8BAAuG;IAGjGF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAkB;IAClBH,EAAA,CAAAY,UAAA,IAAA2C,mFAAA,oCAEwB;IAC1BvD,EAAA,CAAAG,YAAA,EAA6B;IAE7BH,EAAA,CAAAE,cAAA,cAAyB;IAGRF,EAAA,CAAAC,MAAA,uBAAgB;IAAAD,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAE,cAAA,sBAAmC;IACjCF,EAAA,CAAAY,UAAA,KAAA4C,yEAAA,yBAEa;IACfxD,EAAA,CAAAG,YAAA,EAAa;IAGfH,EAAA,CAAAE,cAAA,0BAAqC;IACxBF,EAAA,CAAAC,MAAA,aAAK;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAE,cAAA,sBAAoC;IAClCF,EAAA,CAAAY,UAAA,KAAA6C,yEAAA,yBAEa;IACfzD,EAAA,CAAAG,YAAA,EAAa;IAGfH,EAAA,CAAAE,cAAA,0BAAqC;IACxBF,EAAA,CAAAC,MAAA,wBAAgB;IAAAD,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAE,cAAA,sBAA4C;IAC1CF,EAAA,CAAAY,UAAA,KAAA8C,yEAAA,yBAEa;IACf1D,EAAA,CAAAG,YAAA,EAAa;IAIjBH,EAAA,CAAAE,cAAA,eAAsB;IAEPF,EAAA,CAAAC,MAAA,mBAAM;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC7BH,EAAA,CAAAQ,SAAA,iBAAgD;IAClDR,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAE,cAAA,0BAAqC;IACxBF,EAAA,CAAAC,MAAA,WAAG;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC1BH,EAAA,CAAAQ,SAAA,iBAAmD;IACrDR,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAE,cAAA,0BAAqC;IACxBF,EAAA,CAAAC,MAAA,yBAAiB;IAAAD,EAAA,CAAAG,YAAA,EAAY;IACxCH,EAAA,CAAAQ,SAAA,iBAAuF;IAGzFR,EAAA,CAAAG,YAAA,EAAiB;IAGnBH,EAAA,CAAAE,cAAA,eAAsB;IAEPF,EAAA,CAAAC,MAAA,aAAK;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAE,cAAA,sBAAqC;IACnCF,EAAA,CAAAY,UAAA,KAAA+C,yEAAA,yBAEa;IACf3D,EAAA,CAAAG,YAAA,EAAa;IAGfH,EAAA,CAAAE,cAAA,eAAiC;IAElBF,EAAA,CAAAC,MAAA,oCAAuB;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC9CH,EAAA,CAAAQ,SAAA,iBAA0E;IAC1ER,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAC,MAAA,mDAAsC;IAAAD,EAAA,CAAAG,YAAA,EAAW;IAC3DH,EAAA,CAAAY,UAAA,KAAAgD,wEAAA,wBAEY;IACd5D,EAAA,CAAAG,YAAA,EAAiB;IAGnBH,EAAA,CAAAE,cAAA,0BAAqC;IACxBF,EAAA,CAAAC,MAAA,mCAAiB;IAAAD,EAAA,CAAAG,YAAA,EAAY;IACxCH,EAAA,CAAAQ,SAAA,iBAAiD;IACnDR,EAAA,CAAAG,YAAA,EAAiB;IAGnBH,EAAA,CAAAE,cAAA,eAA0B;IACpBF,EAAA,CAAAC,MAAA,iCAAyB;IAAAD,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAE,cAAA,eAAkC;IAGjBF,EAAA,CAAAC,MAAA,iCAAe;IAAAD,EAAA,CAAAG,YAAA,EAAY;IACtCH,EAAA,CAAAQ,SAAA,iBAAyC;IAC3CR,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAE,cAAA,0BAAqC;IACxBF,EAAA,CAAAC,MAAA,gCAAmB;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAQ,SAAA,iBAAkD;IAClDR,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAC,MAAA,iCAAoB;IAAAD,EAAA,CAAAG,YAAA,EAAW;IACzCH,EAAA,CAAAY,UAAA,KAAAiD,wEAAA,wBAEY;IACd7D,EAAA,CAAAG,YAAA,EAAiB;IAGnBH,EAAA,CAAAE,cAAA,eAAsB;IAEPF,EAAA,CAAAC,MAAA,yBAAiB;IAAAD,EAAA,CAAAG,YAAA,EAAY;IACxCH,EAAA,CAAAQ,SAAA,iBAAyF;IAGzFR,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAC,MAAA,oCAAuB;IAAAD,EAAA,CAAAG,YAAA,EAAW;IAC5CH,EAAA,CAAAY,UAAA,KAAAkD,wEAAA,wBAEY;IACZ9D,EAAA,CAAAY,UAAA,KAAAmD,wEAAA,wBAEY;IACd/D,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAE,cAAA,0BAAqC;IACxBF,EAAA,CAAAC,MAAA,4BAAe;IAAAD,EAAA,CAAAG,YAAA,EAAY;IACtCH,EAAA,CAAAQ,SAAA,iBAAuF;IAGzFR,EAAA,CAAAG,YAAA,EAAiB;IAGnBH,EAAA,CAAAE,cAAA,eAAsB;IAEPF,EAAA,CAAAC,MAAA,qCAAwB;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC/CH,EAAA,CAAAQ,SAAA,iBAAkE;IACpER,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAE,cAAA,0BAAqC;IACxBF,EAAA,CAAAC,MAAA,iCAAoB;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAQ,SAAA,iBAA4D;IAC9DR,EAAA,CAAAG,YAAA,EAAiB;IAKvBH,EAAA,CAAAE,cAAA,gBAA0B;IACpBF,EAAA,CAAAC,MAAA,gBAAO;IAAAD,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAE,cAAA,gBAA6B;IAIVF,EAAA,CAAAC,MAAA,kBAAS;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAChCH,EAAA,CAAAQ,SAAA,kBAAuD;IACzDR,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAE,cAAA,2BAAqC;IACxBF,EAAA,CAAAC,MAAA,gCAAkB;IAAAD,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAQ,SAAA,kBAA2C;IAC7CR,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAE,cAAA,2BAAqC;IACxBF,EAAA,CAAAC,MAAA,2CAAmB;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAQ,SAAA,kBAAuD;IACzDR,EAAA,CAAAG,YAAA,EAAiB;IAIrBH,EAAA,CAAAE,cAAA,gBAAsB;IAEPF,EAAA,CAAAC,MAAA,cAAK;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAQ,SAAA,kBAA8D;IAChER,EAAA,CAAAG,YAAA,EAAiB;IAGnBH,EAAA,CAAAE,cAAA,gBAAsB;IAEPF,EAAA,CAAAC,MAAA,gBAAO;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAC9BH,EAAA,CAAAQ,SAAA,kBAAmD;IACrDR,EAAA,CAAAG,YAAA,EAAiB;IAGnBH,EAAA,CAAAE,cAAA,gBAAsB;IAEPF,EAAA,CAAAC,MAAA,oBAAW;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAQ,SAAA,mBAAmD;IACrDR,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAE,cAAA,iBAA0B;IAEXF,EAAA,CAAAC,MAAA,oBAAW;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAQ,SAAA,mBAA8C;IAChDR,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAE,cAAA,2BAAqC;IACxBF,EAAA,CAAAC,MAAA,qBAAY;IAAAD,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAQ,SAAA,kBAAgD;IAClDR,EAAA,CAAAG,YAAA,EAAiB;IAIrBH,EAAA,CAAAE,cAAA,gBAAsB;IAGLF,EAAA,CAAAC,MAAA,mBAAU;IAAAD,EAAA,CAAAG,YAAA,EAAY;IACjCH,EAAA,CAAAQ,SAAA,mBAA8C;IAChDR,EAAA,CAAAG,YAAA,EAAiB;IAEjBH,EAAA,CAAAE,cAAA,2BAAqC;IACxBF,EAAA,CAAAC,MAAA,oBAAW;IAAAD,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAQ,SAAA,kBAAgD;IAClDR,EAAA,CAAAG,YAAA,EAAiB;IAMzBH,EAAA,CAAAE,cAAA,iBAAwC;IACGF,EAAA,CAAAC,MAAA,uBAAc;IAAAD,EAAA,CAAAG,YAAA,EAAe;IACtEH,EAAA,CAAAY,UAAA,MAAAoD,sEAAA,sBAES;IACXhE,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;;;;IA1NwEH,EAAA,CAAA4B,UAAA,aAAAuB,KAAA,OAAoB;IAGhGnD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwB,kBAAA,eAAA2B,KAAA,UACF;IACwBnD,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAA4B,UAAA,WAAAqC,OAAA,GAAAxB,aAAA,CAAAC,GAAA,2BAAAuB,OAAA,CAAAtB,KAAA,OAAAsB,OAAA,GAAAxB,aAAA,CAAAC,GAAA,8BAAAuB,OAAA,CAAAtB,KAAA,EAAqE;IAK1F3C,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAA4B,UAAA,kBAAAuB,KAAA,CAAmB;IAKanD,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAA4B,UAAA,YAAAsC,MAAA,CAAAC,cAAA,CAAiB;IAShBnE,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA4B,UAAA,YAAAsC,MAAA,CAAAE,eAAA,CAAkB;IASnBpE,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAA4B,UAAA,YAAAsC,MAAA,CAAAC,cAAA,CAAiB;IAoBhCnE,EAAA,CAAAI,SAAA,IAAiC;IAAjCJ,EAAA,CAAA4B,UAAA,kBAAAyC,IAAA,CAAiC;IAChBrE,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA4B,UAAA,QAAAyC,IAAA,CAAuB;IASvBrE,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA4B,UAAA,YAAAsC,MAAA,CAAAI,aAAA,CAAgB;IAWnCtE,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAA4B,UAAA,UAAA2C,QAAA,GAAA9B,aAAA,CAAAC,GAAA,gDAAA6B,QAAA,CAAAC,OAAA,CAAyD;IAyBvDxE,EAAA,CAAAI,SAAA,IAAmD;IAAnDJ,EAAA,CAAA4B,UAAA,UAAA6C,QAAA,GAAAhC,aAAA,CAAAC,GAAA,0CAAA+B,QAAA,CAAAD,OAAA,CAAmD;IAS/CxE,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAA4B,UAAA,kBAAA8C,IAAA,CAAkC;IACjB1E,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAA4B,UAAA,QAAA8C,IAAA,CAAwB;IAG7C1E,EAAA,CAAAI,SAAA,GAA6E;IAA7EJ,EAAA,CAAA4B,UAAA,UAAA+C,QAAA,GAAAlC,aAAA,CAAAC,GAAA,8CAAAiC,QAAA,CAAAC,MAAA,kBAAAD,QAAA,CAAAC,MAAA,sBAA6E;IAG7E5E,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAA4B,UAAA,UAAAiD,QAAA,GAAApC,aAAA,CAAAC,GAAA,8CAAAmC,QAAA,CAAAD,MAAA,kBAAAC,QAAA,CAAAD,MAAA,aAAoE;IAOhE5E,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAA4B,UAAA,kBAAAkD,IAAA,CAAiC;IAChB9E,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA4B,UAAA,QAAAkD,IAAA,CAAuB;IA4Fc9E,EAAA,CAAAI,SAAA,IAA2B;IAA3BJ,EAAA,CAAA4B,UAAA,SAAAsC,MAAA,CAAAa,UAAA,CAAApD,MAAA,KAA2B;;;;;IAY/G3B,EAAA,CAAAQ,SAAA,sBAAkF;;;;;IAClFR,EAAA,CAAAE,cAAA,WAAyB;IAAAF,EAAA,CAAAC,MAAA,mCAA4B;IAAAD,EAAA,CAAAG,YAAA,EAAO;;;;;IAOlEH,EAAA,CAAAE,cAAA,cAAwE;IAClEF,EAAA,CAAAC,MAAA,yDAAwC;IAAAD,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAE,cAAA,eAAiC;IACpBF,EAAA,CAAAC,MAAA,yBAAkB;IAAAD,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAC,MAAA,GAA8C;IAAAD,EAAA,CAAAG,YAAA,EAAI;IACzFH,EAAA,CAAAE,cAAA,QAAG;IAAQF,EAAA,CAAAC,MAAA,4BAAoB;IAAAD,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAC,MAAA,IAAiJ;IAAAD,EAAA,CAAAG,YAAA,EAAI;;;;IADvJH,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAwB,kBAAA,MAAAwD,OAAA,CAAAC,YAAA,CAAAhD,IAAA,CAAAC,aAAA,cAA8C;IAC5ClC,EAAA,CAAAI,SAAA,GAAiJ;IAAjJJ,EAAA,CAAAwB,kBAAA,MAAAwD,OAAA,CAAAC,YAAA,CAAAhD,IAAA,CAAAiD,eAAA,IAAAF,OAAA,CAAAC,YAAA,CAAAhD,IAAA,CAAAiD,eAAA,CAAAH,UAAA,GAAAC,OAAA,CAAAC,YAAA,CAAAhD,IAAA,CAAAiD,eAAA,CAAAH,UAAA,CAAApD,MAAA,SAAiJ;;;;;IAQtK3B,EAAA,CAAAC,MAAA,+BAAwB;;;;;IAQhDD,EAAA,CAAAE,cAAA,cAAgD;IACpCF,EAAA,CAAAC,MAAA,YAAK;IAAAD,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAC,MAAA,GAAkB;IAAAD,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAA8E,OAAA,CAAA5E,YAAA,CAAkB;;;;;IAyFpBP,EAAA,CAAAQ,SAAA,sBAAkF;;;;;IAClFR,EAAA,CAAAE,cAAA,WAAyB;IAAAF,EAAA,CAAAC,MAAA,oCAAwB;IAAAD,EAAA,CAAAG,YAAA,EAAO;;;;;IAO9DH,EAAA,CAAAE,cAAA,eAA6F;IACvFF,EAAA,CAAAC,MAAA,uCAAsB;IAAAD,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAE,cAAA,eAAkC;IACrBF,EAAA,CAAAC,MAAA,uCAAsB;IAAAD,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAC,MAAA,GAAoD;IAAAD,EAAA,CAAAG,YAAA,EAAI;IACnGH,EAAA,CAAAE,cAAA,QAAG;IAAQF,EAAA,CAAAC,MAAA,0BAAkB;IAAAD,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAC,MAAA,IAAgD;IAAAD,EAAA,CAAAG,YAAA,EAAI;IAE3FH,EAAA,CAAAE,cAAA,gBAAkC;IACtBF,EAAA,CAAAC,MAAA,oBAAY;IAAAD,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAE,cAAA,SAAG;IAAAF,EAAA,CAAAC,MAAA,+KAAoI;IAAAD,EAAA,CAAAG,YAAA,EAAI;IAG7IH,EAAA,CAAAE,cAAA,gBAAkC;IAE9BF,EAAA,CAAAC,MAAA,oCACF;IAAAD,EAAA,CAAAG,YAAA,EAAS;;;;IAXgCH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAwB,kBAAA,MAAA4D,OAAA,CAAAC,cAAA,CAAApD,IAAA,CAAAqD,iBAAA,cAAoD;IACxDtF,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAwB,kBAAA,MAAA4D,OAAA,CAAAC,cAAA,CAAApD,IAAA,CAAAC,aAAA,cAAgD;;;AD9dnG,OAAM,MAAOqD,2BAA2B;EA4CtCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB;IALrB,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAjDlB;IACA,KAAAC,WAAW,GAAG,CAAC;IAOf;IACA,KAAA7D,aAAa,GAAW,EAAE;IAC1B,KAAAR,QAAQ,GAAa,EAAE;IAEvB;IACA,KAAAM,aAAa,GAAoC,IAAI;IACrD,KAAAiD,YAAY,GAAsC,IAAI;IACtD,KAAAI,cAAc,GAAqC,IAAI;IAEvD;IACA,KAAAvD,SAAS,GAAG,KAAK;IACjB,KAAAvB,YAAY,GAAG,EAAE;IAEjB;IACA,KAAA6D,eAAe,GAAG4B,MAAM,CAACC,IAAI,CAACpG,cAAc,CAAC,CAC1CqG,MAAM,CAACC,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM,CAACxG,cAAc,CAACsG,GAAkC,CAAC,CAAC,CAAC,CAAC,CACjFG,GAAG,CAACH,GAAG,KAAK;MACXxD,KAAK,EAAE9C,cAAc,CAACsG,GAAkC,CAAC;MACzDtD,KAAK,EAAEsD;KACR,CAAC,CAAC;IAEL,KAAA7B,aAAa,GAAG0B,MAAM,CAACC,IAAI,CAACnG,MAAM,CAAC,CAChCoG,MAAM,CAACC,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM,CAACvG,MAAM,CAACqG,GAA0B,CAAC,CAAC,CAAC,CAAC,CACjEG,GAAG,CAACH,GAAG,KAAK;MACXxD,KAAK,EAAE7C,MAAM,CAACqG,GAA0B,CAAC;MACzCtD,KAAK,EAAEsD;KACR,CAAC,CAAC;IAEL,KAAAhC,cAAc,GAAG6B,MAAM,CAACC,IAAI,CAAClG,aAAa,CAAC,CACxCmG,MAAM,CAACC,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM,CAACtG,aAAa,CAACoG,GAAiC,CAAC,CAAC,CAAC,CAAC,CAC/EG,GAAG,CAACH,GAAG,KAAK;MACXxD,KAAK,EAAE5C,aAAa,CAACoG,GAAiC,CAAC;MACvDtD,KAAK,EAAEsD;KACR,CAAC,CAAC;IAUH;IACA,IAAI,CAACtE,oBAAoB,GAAG,IAAI,CAAC4D,EAAE,CAACc,KAAK,CAAC;MACxCC,QAAQ,EAAE,CAAC,KAAK,EAAE5G,UAAU,CAAC6G,QAAQ,CAAC;MACtCC,OAAO,EAAE,CAAC,OAAO,EAAE9G,UAAU,CAAC6G,QAAQ;KACvC,CAAC;IAEF,IAAI,CAACE,mBAAmB,GAAG,IAAI,CAAClB,EAAE,CAACc,KAAK,CAAC;MACvCrE,aAAa,EAAE,CAAC,EAAE,EAAEtC,UAAU,CAAC6G,QAAQ,CAAC;MACxC1B,UAAU,EAAE,IAAI,CAACU,EAAE,CAACmB,KAAK,CAAC,EAAE,CAAC;MAC7BC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,uBAAuB,EAAE,CAAC,EAAE;KAC7B,CAAC;IAEF,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACtB,EAAE,CAACc,KAAK,CAAC;MACzCrE,aAAa,EAAE,CAAC,EAAE,EAAEtC,UAAU,CAAC6G,QAAQ,CAAC;MACxCO,aAAa,EAAE,CAAC,CAAC,CAAC;MAClBC,kBAAkB,EAAE,IAAI,CAACxB,EAAE,CAACc,KAAK,CAAC;QAChCW,WAAW,EAAE,CAAC,EAAE,CAAC;QACjBC,aAAa,EAAE,CAAC,CAAC,CAAC;QAClBC,YAAY,EAAE,IAAI,CAAC3B,EAAE,CAACc,KAAK,CAAC;UAC1Bc,MAAM,EAAE,CAAC,CAAC,EAAEzH,UAAU,CAAC6G,QAAQ,CAAC;UAChCD,QAAQ,EAAE,CAAC,KAAK,EAAE5G,UAAU,CAAC6G,QAAQ;SACtC,CAAC;QACFa,gBAAgB,EAAE,CAAC,GAAG,CAAC;QACvBC,WAAW,EAAE,CAAC,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAAC;QACvCC,WAAW,EAAE,CAAC,EAAE,CAAC;QACjBC,SAAS,EAAE,CAAC,EAAE,CAAC;QACfC,YAAY,EAAE,CAAC,EAAE;OAClB;KACF,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACnC,KAAK,CAACoC,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAIA,MAAM,CAAC,UAAU,CAAC,EAAE;QACtB,IAAI;UACF,IAAI,CAACtG,QAAQ,GAAGuG,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,UAAU,CAAC,CAAC,GAC7CA,MAAM,CAAC,UAAU,CAAC,GAClB,CAACA,MAAM,CAAC,UAAU,CAAC,CAAC;UAExB,IAAI,IAAI,CAACtG,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YAC5BwG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC1G,QAAQ,CAAC;WAClD,MAAM;YACL,IAAI,CAACnB,YAAY,GAAG,oCAAoC;;SAE3D,CAAC,OAAO8H,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;UACpE,IAAI,CAAC9H,YAAY,GAAG,iCAAiC;;OAExD,MAAM;QACL,IAAI,CAACA,YAAY,GAAG,oCAAoC;;IAE5D,CAAC,CAAC;EACJ;EAEA;EACA,IAAIwE,UAAUA,CAAA;IACZ,OAAO,IAAI,CAAC4B,mBAAmB,CAACjE,GAAG,CAAC,YAAY,CAAc;EAChE;EAEA;EACA4F,YAAYA,CAAA;IACV,MAAMC,aAAa,GAAG,IAAI,CAAC9C,EAAE,CAACc,KAAK,CAAC;MAClCiC,IAAI,EAAE,CAAC,CAAC,EAAE5I,UAAU,CAAC6G,QAAQ,CAAC;MAC9BgC,KAAK,EAAE,CAAC,CAAC,EAAE7I,UAAU,CAAC6G,QAAQ,CAAC;MAC/BiC,aAAa,EAAE,CAAC,CAAC,EAAE9I,UAAU,CAAC6G,QAAQ,CAAC;MACvCkC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC/I,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACjJ,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7DE,QAAQ,EAAE,CAAC,IAAI,CAAC/D,UAAU,EAAEpD,MAAM,KAAK,CAAC,CAAC;MACzCoH,SAAS,EAAE,CAAC,EAAE,EAAEnJ,UAAU,CAAC6G,QAAQ,CAAC;MACpCuC,WAAW,EAAE,IAAI,CAACvD,EAAE,CAACc,KAAK,CAAC;QACzB0C,aAAa,EAAE,CAAC,IAAI,EAAE,CAACrJ,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,EAAEhJ,UAAU,CAACsJ,SAAS,CAAC,CAAC,CAAC,CAAC;OAC9F,CAAC;MACFC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,YAAY,EAAE,IAAI,CAAC3D,EAAE,CAACc,KAAK,CAAC;QAC1B8C,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC1J,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5DW,UAAU,EAAE,CAAC,EAAE,EAAE3J,UAAU,CAAC6G,QAAQ,CAAC;QACrC+C,SAAS,EAAE,CAAC,EAAE,EAAE5J,UAAU,CAAC6G,QAAQ,CAAC;QACpCgD,sBAAsB,EAAE,CAAC,IAAI,EAAE,CAAC7J,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,EAAEhJ,UAAU,CAACsJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACvGQ,gBAAgB,EAAE,CAAC,IAAI,EAAE,CAAC9J,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,EAAEhJ,UAAU,CAACsJ,SAAS,CAAC,CAAC,CAAC,CAAC;OACjG,CAAC;MACFS,OAAO,EAAE,IAAI,CAAClE,EAAE,CAACc,KAAK,CAAC;QACrBqD,YAAY,EAAE,IAAI,CAACnE,EAAE,CAACc,KAAK,CAAC;UAC1BsD,WAAW,EAAE,CAAC,KAAK,EAAEjK,UAAU,CAAC6G,QAAQ,CAAC;UACzCqD,QAAQ,EAAE,CAAC,EAAE,CAAC;UACdC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACnK,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC;SACjE,CAAC;QACFoB,KAAK,EAAE,CAAC,EAAE,EAAE,CAACpK,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACoK,KAAK,CAAC,CAAC;QACpDL,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC/J,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7DqB,OAAO,EAAE,CAAC,EAAE,EAAE,CAACrK,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7DsB,IAAI,EAAE,IAAI,CAACzE,EAAE,CAACc,KAAK,CAAC;UAClB4D,EAAE,EAAE,CAAC,EAAE,EAAEvK,UAAU,CAAC6G,QAAQ,CAAC;UAC7BkC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC/I,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC;SAC1D,CAAC;QACFwB,OAAO,EAAE,IAAI,CAAC3E,EAAE,CAACc,KAAK,CAAC;UACrB4D,EAAE,EAAE,CAAC,IAAI,EAAE,CAACvK,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,EAAEhJ,UAAU,CAACsJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;UACnFP,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC/I,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC;SAChE;OACF,CAAC;MACFyB,MAAM,EAAE,CAAC,CAAC,EAAEzK,UAAU,CAAC6G,QAAQ,CAAC,CAAC;KAClC,CAAC;IAEF;IACA,IAAI,CAAC6D,mBAAmB,CAAC/B,aAAa,CAAC;IAEvC,IAAI,CAACxD,UAAU,CAACwF,IAAI,CAAChC,aAAa,CAAC;EACrC;EAEA;EACQ+B,mBAAmBA,CAACE,IAAe;IACzC;IACA,MAAMC,kBAAkB,GAAGD,IAAI,CAAC9H,GAAG,CAAC,2BAA2B,CAAC;IAChE,MAAMgI,uBAAuB,GAAGF,IAAI,CAAC9H,GAAG,CAAC,+BAA+B,CAAC;IAEzE;IACA,IAAI+H,kBAAkB,IAAIC,uBAAuB,EAAE;MACjDD,kBAAkB,CAACE,YAAY,CAAC5C,SAAS,CAACpF,KAAK,IAAG;QAChD,IAAIA,KAAK,KAAK,CAAC+H,uBAAuB,CAAC/H,KAAK,IAAI+H,uBAAuB,CAAC/H,KAAK,KAAK,EAAE,CAAC,EAAE;UACrF+H,uBAAuB,CAACE,QAAQ,CAACjI,KAAK,CAAC;;MAE3C,CAAC,CAAC;;IAGJ;IACA,MAAMkI,iBAAiB,GAAGL,IAAI,CAAC9H,GAAG,CAAC,yBAAyB,CAAC;IAC7D,IAAImI,iBAAiB,EAAE;MACrBA,iBAAiB,CAACC,aAAa,CAAC,CAC9BlL,UAAU,CAAC6G,QAAQ,EAClBsE,OAAO,IAAI;QACV,MAAMpI,KAAK,GAAGoI,OAAO,CAACpI,KAAK;QAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;QAEvB,MAAM4G,UAAU,GAAG,IAAI/B,IAAI,CAAC7E,KAAK,CAAC;QAClC,MAAMqI,KAAK,GAAG,IAAIxD,IAAI,EAAE;QAExB,OAAO+B,UAAU,GAAGyB,KAAK,GAAG,IAAI,GAAG;UAAE,mBAAmB,EAAE;QAAI,CAAE;MAClE,CAAC,CACF,CAAC;;EAEN;EAEA;EACA1H,eAAeA,CAACF,KAAa;IAC3B,IAAI,CAAC2B,UAAU,CAACkG,QAAQ,CAAC7H,KAAK,CAAC;EACjC;EAEA;EACA/B,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACQ,oBAAoB,CAAC2C,OAAO,EAAE;MACrC,IAAI,CAACsB,QAAQ,CAACoF,IAAI,CAAC,gDAAgD,EAAE,QAAQ,EAAE;QAC7EC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB;OAC9B,CAAC;MACF;;IAGF,IAAI,CAACtJ,SAAS,GAAG,IAAI;IACrB,IAAI,CAACvB,YAAY,GAAG,EAAE;IAEtB,MAAM8K,SAAS,GAAG,IAAI,CAACxJ,oBAAoB,CAACc,KAAK;IAEjD,IAAI,CAACiD,cAAc,CAACvE,gBAAgB,CAClC,IAAI,CAACK,QAAQ,EACb2J,SAAS,CAAC7E,QAAQ,EAClB6E,SAAS,CAAC3E,OAAO,CAClB,CAACqB,SAAS,CAAC;MACVuD,IAAI,EAAGC,QAAoC,IAAI;QAC7C,IAAI,CAACzJ,SAAS,GAAG,KAAK;QACtBqG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEmD,QAAQ,CAAC;QAEvC,IAAIA,QAAQ,IAAIA,QAAQ,CAACvJ,aAAa,IAAIuJ,QAAQ,CAACvJ,aAAa,CAACC,IAAI,EAAE;UACrE,IAAI,CAACD,aAAa,GAAGuJ,QAAQ,CAACvJ,aAAa;UAC3C,IAAI,CAACE,aAAa,GAAGqJ,QAAQ,CAACvJ,aAAa,CAACC,IAAI,CAACC,aAAa,IAAI,EAAE;UAEpE;UACA,IAAI,CAACyE,mBAAmB,CAAC6E,UAAU,CAAC;YAClCtJ,aAAa,EAAE,IAAI,CAACA;WACrB,CAAC;UAEF;UACA,IAAIqJ,QAAQ,CAACvJ,aAAa,CAACC,IAAI,CAACiD,eAAe,IAC3CqG,QAAQ,CAACvJ,aAAa,CAACC,IAAI,CAACiD,eAAe,CAACH,UAAU,IACtDwG,QAAQ,CAACvJ,aAAa,CAACC,IAAI,CAACiD,eAAe,CAACH,UAAU,CAACpD,MAAM,GAAG,CAAC,EAAE;YAErE;YACA,OAAO,IAAI,CAACoD,UAAU,CAACpD,MAAM,GAAG,CAAC,EAAE;cACjC,IAAI,CAACoD,UAAU,CAACkG,QAAQ,CAAC,CAAC,CAAC;;YAG7B;YACAM,QAAQ,CAACvJ,aAAa,CAACC,IAAI,CAACiD,eAAe,CAACH,UAAU,CAAC0G,OAAO,CAACC,SAAS,IAAG;cACzE,IAAI,CAACC,wBAAwB,CAACD,SAAS,CAAC;YAC1C,CAAC,CAAC;WACH,MAAM;YACL;YACA,IAAI,CAACpD,YAAY,EAAE;;UAGrB;UACA,IAAI,CAACvC,WAAW,GAAG,CAAC;UAEpB,IAAI,CAACD,QAAQ,CAACoF,IAAI,CAAC,mCAAmC,EAAE,QAAQ,EAAE;YAChEC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;SACH,MAAM;UACL,IAAI,CAAC7K,YAAY,GAAG,kCAAkC;;MAE1D,CAAC;MACD8H,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvG,SAAS,GAAG,KAAK;QACtBqG,OAAO,CAACE,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QACnE,IAAI,CAAC9H,YAAY,GAAG8H,KAAK,CAACuD,OAAO,IAAI,8DAA8D;QAEnG,IAAI,CAAC9F,QAAQ,CAACoF,IAAI,CAAC,IAAI,CAAC3K,YAAY,EAAE,QAAQ,EAAE;UAC9C4K,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EACAO,wBAAwBA,CAACD,SAAiC;IACxD,MAAMnD,aAAa,GAAG,IAAI,CAAC9C,EAAE,CAACc,KAAK,CAAC;MAClCsF,WAAW,EAAE,CAACH,SAAS,CAACG,WAAW,IAAI,EAAE,CAAC;MAC1CrD,IAAI,EAAE,CAACkD,SAAS,CAAClD,IAAI,IAAI,CAAC,EAAE5I,UAAU,CAAC6G,QAAQ,CAAC;MAChDgC,KAAK,EAAE,CAACiD,SAAS,CAACjD,KAAK,IAAI,CAAC,EAAE7I,UAAU,CAAC6G,QAAQ,CAAC;MAClDiC,aAAa,EAAE,CAACgD,SAAS,CAAChD,aAAa,IAAI,CAAC,EAAE9I,UAAU,CAAC6G,QAAQ,CAAC;MAClEkC,IAAI,EAAE,CAAC+C,SAAS,CAAC/C,IAAI,IAAI,EAAE,EAAE,CAAC/I,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5EC,OAAO,EAAE,CAAC6C,SAAS,CAAC7C,OAAO,IAAI,EAAE,EAAE,CAACjJ,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAClFE,QAAQ,EAAE,CAAC4C,SAAS,CAAC5C,QAAQ,IAAI,IAAI,CAAC/D,UAAU,EAAEpD,MAAM,KAAK,CAAC,CAAC;MAC/DoH,SAAS,EAAE,CAAC2C,SAAS,CAAC3C,SAAS,IAAI,EAAE,EAAEnJ,UAAU,CAAC6G,QAAQ,CAAC;MAC3DuC,WAAW,EAAE,IAAI,CAACvD,EAAE,CAACc,KAAK,CAAC;QACzB0C,aAAa,EAAE,CAACyC,SAAS,CAAC1C,WAAW,EAAEC,aAAa,IAAI,IAAI,EAAE,CAACrJ,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,EAAEhJ,UAAU,CAACsJ,SAAS,CAAC,CAAC,CAAC,CAAC;OACtI,CAAC;MACFC,cAAc,EAAE,CAACuC,SAAS,CAACvC,cAAc,IAAI,EAAE,CAAC;MAChDC,YAAY,EAAE,IAAI,CAAC3D,EAAE,CAACc,KAAK,CAAC;QAC1B8C,MAAM,EAAE,CAACqC,SAAS,CAACtC,YAAY,EAAEC,MAAM,IAAI,EAAE,CAAC;QAC9CC,MAAM,EAAE,CAACoC,SAAS,CAACtC,YAAY,EAAEE,MAAM,IAAI,EAAE,EAAE,CAAC1J,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9FW,UAAU,EAAE,CAACmC,SAAS,CAACtC,YAAY,EAAEG,UAAU,IAAI,EAAE,EAAE3J,UAAU,CAAC6G,QAAQ,CAAC;QAC3E+C,SAAS,EAAE,CAACkC,SAAS,CAACtC,YAAY,EAAEI,SAAS,IAAI,EAAE,EAAE5J,UAAU,CAAC6G,QAAQ,CAAC;QACzEgD,sBAAsB,EAAE,CAACiC,SAAS,CAACtC,YAAY,EAAEK,sBAAsB,IAAI,IAAI,EAAE,CAAC7J,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,EAAEhJ,UAAU,CAACsJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACzJQ,gBAAgB,EAAE,CAACgC,SAAS,CAACtC,YAAY,EAAEM,gBAAgB,IAAI,IAAI,EAAE,CAAC9J,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,EAAEhJ,UAAU,CAACsJ,SAAS,CAAC,CAAC,CAAC,CAAC;OAC7I,CAAC;MACFS,OAAO,EAAE,IAAI,CAAClE,EAAE,CAACc,KAAK,CAAC;QACrBqD,YAAY,EAAE,IAAI,CAACnE,EAAE,CAACc,KAAK,CAAC;UAC1BsD,WAAW,EAAE,CAAC6B,SAAS,CAAC/B,OAAO,EAAEC,YAAY,EAAEC,WAAW,IAAI,KAAK,EAAEjK,UAAU,CAAC6G,QAAQ,CAAC;UACzFqD,QAAQ,EAAE,CAAC,EAAE,CAAC;UACdC,WAAW,EAAE,CAAC2B,SAAS,CAAC/B,OAAO,EAAEC,YAAY,EAAEG,WAAW,IAAI,EAAE,EAAE,CAACnK,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC;SACjH,CAAC;QACFoB,KAAK,EAAE,CAAC0B,SAAS,CAAC/B,OAAO,EAAEK,KAAK,IAAI,EAAE,EAAE,CAACpK,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACoK,KAAK,CAAC,CAAC;QAChFL,OAAO,EAAE,CAAC+B,SAAS,CAAC/B,OAAO,EAAEA,OAAO,IAAI,EAAE,EAAE,CAAC/J,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3FqB,OAAO,EAAE,CAACyB,SAAS,CAAC/B,OAAO,EAAEM,OAAO,IAAI,EAAE,EAAE,CAACrK,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3FsB,IAAI,EAAE,IAAI,CAACzE,EAAE,CAACc,KAAK,CAAC;UAClB4D,EAAE,EAAE,CAACuB,SAAS,CAAC/B,OAAO,EAAEO,IAAI,EAAEC,EAAE,IAAI,EAAE,EAAEvK,UAAU,CAAC6G,QAAQ,CAAC;UAC5DkC,IAAI,EAAE,CAAC+C,SAAS,CAAC/B,OAAO,EAAEO,IAAI,EAAEvB,IAAI,IAAI,EAAE,EAAE,CAAC/I,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC;SAC3F,CAAC;QACFwB,OAAO,EAAE,IAAI,CAAC3E,EAAE,CAACc,KAAK,CAAC;UACrB4D,EAAE,EAAE,CAACuB,SAAS,CAAC/B,OAAO,EAAES,OAAO,EAAED,EAAE,IAAI,IAAI,EAAE,CAACvK,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,EAAEhJ,UAAU,CAACsJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;UACrHP,IAAI,EAAE,CAAC+C,SAAS,CAAC/B,OAAO,EAAES,OAAO,EAAEzB,IAAI,IAAI,QAAQ,EAAE,CAAC/I,UAAU,CAAC6G,QAAQ,EAAE7G,UAAU,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAAC;SACpG;OACF,CAAC;MACFyB,MAAM,EAAE,CAACqB,SAAS,CAACrB,MAAM,IAAI,CAAC,EAAEzK,UAAU,CAAC6G,QAAQ;KACpD,CAAC;IAEF;IACA,IAAI,CAAC6D,mBAAmB,CAAC/B,aAAa,CAAC;IAEvC,IAAI,CAACxD,UAAU,CAACwF,IAAI,CAAChC,aAAa,CAAC;EACrC;EAEA;EACAuD,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACnF,mBAAmB,CAACnC,OAAO,EAAE;MACpC;MACA,IAAIjE,YAAY,GAAG,gDAAgD;MAEnE;MACA,MAAMwE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACgH,QAAQ;MAC3C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjH,UAAU,CAACpD,MAAM,EAAEqK,CAAC,EAAE,EAAE;QAC1C,MAAMN,SAAS,GAAG3G,UAAU,CAACiH,CAAC,CAAc;QAE5C;QACA,MAAMnB,iBAAiB,GAAGa,SAAS,CAAChJ,GAAG,CAAC,yBAAyB,CAAC;QAClE,IAAImI,iBAAiB,EAAEjG,MAAM,GAAG,mBAAmB,CAAC,EAAE;UACpDrE,YAAY,GAAG,YAAYyL,CAAC,GAAC,CAAC,8DAA8D;UAC5F;;QAGF;QACA,MAAMtB,uBAAuB,GAAGgB,SAAS,CAAChJ,GAAG,CAAC,+BAA+B,CAAC;QAC9E,IAAIgI,uBAAuB,EAAElG,OAAO,IAAIkG,uBAAuB,EAAEuB,OAAO,EAAE;UACxE1L,YAAY,GAAG,YAAYyL,CAAC,GAAC,CAAC,sDAAsD;UACpF;;QAGF;QACA,MAAME,qBAAqB,GAAGR,SAAS,CAAChJ,GAAG,CAAC,qBAAqB,CAAC;QAClE,IAAIwJ,qBAAqB,EAAE1H,OAAO,IAAI0H,qBAAqB,EAAED,OAAO,EAAE;UACpE1L,YAAY,GAAG,YAAYyL,CAAC,GAAC,CAAC,wCAAwC;UACtE;;;MAIJ,IAAI,CAAClG,QAAQ,CAACoF,IAAI,CAAC3K,YAAY,EAAE,QAAQ,EAAE;QACzC4K,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB;OAC9B,CAAC;MACF;;IAGF,IAAI,CAACtJ,SAAS,GAAG,IAAI;IACrB,IAAI,CAACvB,YAAY,GAAG,EAAE;IAEtB,MAAM8K,SAAS,GAAG,IAAI,CAAC1E,mBAAmB,CAAChE,KAAK;IAEhD;IACA,MAAMwJ,mBAAmB,GAAGd,SAAS,CAACtG,UAAU,CAACuB,GAAG,CAACoF,SAAS,IAAG;MAC/D;MACA,IAAIA,SAAS,CAAC3C,SAAS,EAAE;QACvB,IAAI2C,SAAS,CAAC3C,SAAS,YAAYvB,IAAI,EAAE;UACvCkE,SAAS,CAAC3C,SAAS,GAAG,IAAI,CAACqD,gBAAgB,CAACV,SAAS,CAAC3C,SAAS,CAAC;SACjE,MAAM,IAAI,OAAO2C,SAAS,CAAC3C,SAAS,KAAK,QAAQ,IAAI2C,SAAS,CAAC3C,SAAS,CAACsD,IAAI,EAAE,KAAK,EAAE,EAAE;UACvFX,SAAS,CAAC3C,SAAS,GAAG,IAAI,CAACqD,gBAAgB,CAAC,IAAI5E,IAAI,CAACkE,SAAS,CAAC3C,SAAS,CAAC,CAAC;;;MAI9E;MACA,IAAI2C,SAAS,CAACtC,YAAY,EAAE;QAC1B,IAAIsC,SAAS,CAACtC,YAAY,CAACG,UAAU,EAAE;UACrC,IAAImC,SAAS,CAACtC,YAAY,CAACG,UAAU,YAAY/B,IAAI,EAAE;YACrDkE,SAAS,CAACtC,YAAY,CAACG,UAAU,GAAG,IAAI,CAAC6C,gBAAgB,CAACV,SAAS,CAACtC,YAAY,CAACG,UAAU,CAAC;WAC7F,MAAM,IAAI,OAAOmC,SAAS,CAACtC,YAAY,CAACG,UAAU,KAAK,QAAQ,IAAImC,SAAS,CAACtC,YAAY,CAACG,UAAU,CAAC8C,IAAI,EAAE,KAAK,EAAE,EAAE;YACnHX,SAAS,CAACtC,YAAY,CAACG,UAAU,GAAG,IAAI,CAAC6C,gBAAgB,CAAC,IAAI5E,IAAI,CAACkE,SAAS,CAACtC,YAAY,CAACG,UAAU,CAAC,CAAC;;;QAI1G,IAAImC,SAAS,CAACtC,YAAY,CAACI,SAAS,EAAE;UACpC,IAAIkC,SAAS,CAACtC,YAAY,CAACI,SAAS,YAAYhC,IAAI,EAAE;YACpDkE,SAAS,CAACtC,YAAY,CAACI,SAAS,GAAG,IAAI,CAAC4C,gBAAgB,CAACV,SAAS,CAACtC,YAAY,CAACI,SAAS,CAAC;WAC3F,MAAM,IAAI,OAAOkC,SAAS,CAACtC,YAAY,CAACI,SAAS,KAAK,QAAQ,IAAIkC,SAAS,CAACtC,YAAY,CAACI,SAAS,CAAC6C,IAAI,EAAE,KAAK,EAAE,EAAE;YACjHX,SAAS,CAACtC,YAAY,CAACI,SAAS,GAAG,IAAI,CAAC4C,gBAAgB,CAAC,IAAI5E,IAAI,CAACkE,SAAS,CAACtC,YAAY,CAACI,SAAS,CAAC,CAAC;;;;MAK1G,OAAOkC,SAAS;IAClB,CAAC,CAAC;IAEF;IACA,MAAMY,OAAO,GAA8B;MACzCpK,aAAa,EAAEmJ,SAAS,CAACnJ,aAAa;MACtC6C,UAAU,EAAEoH,mBAAmB;MAC/BtF,eAAe,EAAEwE,SAAS,CAACxE,eAAe;MAC1CC,uBAAuB,EAAEuE,SAAS,CAACvE;KACpC;IAED,IAAI,CAAClB,cAAc,CAACkG,kBAAkB,CAACQ,OAAO,CAAC,CAACvE,SAAS,CAAC;MACxDuD,IAAI,EAAGC,QAAoC,IAAI;QAC7C,IAAI,CAACzJ,SAAS,GAAG,KAAK;QACtBqG,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEmD,QAAQ,CAAC;QAE1D,IAAIA,QAAQ,IAAIA,QAAQ,CAACtG,YAAY,IAAIsG,QAAQ,CAACtG,YAAY,CAAChD,IAAI,EAAE;UACnE,IAAI,CAACgD,YAAY,GAAGsG,QAAQ,CAACtG,YAAY;UAEzC;UACA,IAAI,CAAC8B,qBAAqB,CAACyE,UAAU,CAAC;YACpCtJ,aAAa,EAAE,IAAI,CAACA;WACrB,CAAC;UAEF;UACA,IAAIqJ,QAAQ,CAACtG,YAAY,CAAChD,IAAI,CAACiD,eAAe,IAC1CqG,QAAQ,CAACtG,YAAY,CAAChD,IAAI,CAACiD,eAAe,CAACqH,eAAe,IAC1DhB,QAAQ,CAACtG,YAAY,CAAChD,IAAI,CAACiD,eAAe,CAACqH,eAAe,CAACC,UAAU,EAAE;YAEzE,MAAMA,UAAU,GAAGjB,QAAQ,CAACtG,YAAY,CAAChD,IAAI,CAACiD,eAAe,CAACqH,eAAe,CAACC,UAAU;YAExF,IAAI,CAACzF,qBAAqB,CAACrE,GAAG,CAAC,iCAAiC,CAAC,EAAE8I,UAAU,CAAC;cAC5EnE,MAAM,EAAEmF,UAAU,CAACnF,MAAM;cACzBb,QAAQ,EAAEgG,UAAU,CAAChG;aACtB,CAAC;;UAGJ;UACA,IAAI,CAACT,WAAW,GAAG,CAAC;UAEpB,IAAI,CAACD,QAAQ,CAACoF,IAAI,CAAC,mDAAmD,EAAE,QAAQ,EAAE;YAChFC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;SACH,MAAM;UACL,IAAI,CAAC7K,YAAY,GAAG,kDAAkD;;MAE1E,CAAC;MACD8H,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvG,SAAS,GAAG,KAAK;QACtBqG,OAAO,CAACE,KAAK,CAAC,+DAA+D,EAAEA,KAAK,CAAC;QAErF;QACA,IAAI9H,YAAY,GAAG8H,KAAK,CAACuD,OAAO,IAAI,gFAAgF;QAEpH;QACA,IAAIrL,YAAY,CAACkM,QAAQ,CAAC,UAAU,CAAC,IAAIlM,YAAY,CAACkM,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UAClFlM,YAAY,GAAG,mJAAmJ;;QAEpK;QAAA,KACK,IAAIA,YAAY,CAACkM,QAAQ,CAAC,MAAM,CAAC,IAAIlM,YAAY,CAACkM,QAAQ,CAAC,YAAY,CAAC,EAAE;UAC7ElM,YAAY,GAAG,6GAA6G;;QAE9H;QAAA,KACK,IAAIA,YAAY,CAACkM,QAAQ,CAAC,MAAM,CAAC,IAAIlM,YAAY,CAACkM,QAAQ,CAAC,SAAS,CAAC,EAAE;UAC1ElM,YAAY,GAAG,kIAAkI;;QAGnJ,IAAI,CAACA,YAAY,GAAGA,YAAY;QAEhC,IAAI,CAACuF,QAAQ,CAACoF,IAAI,CAAC,IAAI,CAAC3K,YAAY,EAAE,QAAQ,EAAE;UAC9C4K,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EACAsB,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC3F,qBAAqB,CAACvC,OAAO,EAAE;MACtC,IAAI,CAACsB,QAAQ,CAACoF,IAAI,CAAC,gDAAgD,EAAE,QAAQ,EAAE;QAC7EC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB;OAC9B,CAAC;MACF;;IAGF,IAAI,CAACtJ,SAAS,GAAG,IAAI;IACrB,IAAI,CAACvB,YAAY,GAAG,EAAE;IAEtB,MAAM8K,SAAS,GAAG,IAAI,CAACtE,qBAAqB,CAACpE,KAAK;IAElD,IAAI,CAACiD,cAAc,CAAC8G,iBAAiB,CAACrB,SAAS,CAAC,CAACtD,SAAS,CAAC;MACzDuD,IAAI,EAAGC,QAAoC,IAAI;QAC7C,IAAI,CAACzJ,SAAS,GAAG,KAAK;QACtBqG,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEmD,QAAQ,CAAC;QAEzD,IAAIA,QAAQ,IAAIA,QAAQ,CAAClG,cAAc,IAAIkG,QAAQ,CAAClG,cAAc,CAACpD,IAAI,EAAE;UACvE,IAAI,CAACoD,cAAc,GAAGkG,QAAQ,CAAClG,cAAc;UAE7C,IAAI,CAACS,QAAQ,CAACoF,IAAI,CAAC,oCAAoC,EAAE,QAAQ,EAAE;YACjEC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UAEF;UACA;SACD,MAAM;UACL,IAAI,CAAC7K,YAAY,GAAG,kDAAkD;;MAE1E,CAAC;MACD8H,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvG,SAAS,GAAG,KAAK;QACtBqG,OAAO,CAACE,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;QACzE,IAAI,CAAC9H,YAAY,GAAG8H,KAAK,CAACuD,OAAO,IAAI,oEAAoE;QAEzG,IAAI,CAAC9F,QAAQ,CAACoF,IAAI,CAAC,IAAI,CAAC3K,YAAY,EAAE,QAAQ,EAAE;UAC9C4K,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EACAuB,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC5G,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEA;EACA5D,UAAUA,CAACyK,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B,MAAMC,IAAI,GAAG,IAAIrF,IAAI,CAACoF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACC,kBAAkB,EAAE;EAClC;EAEA;EACAV,gBAAgBA,CAACS,IAAU;IACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB;IACA,IAAI,EAAEA,IAAI,YAAYrF,IAAI,CAAC,IAAIpB,KAAK,CAACyG,IAAI,CAACE,OAAO,EAAE,CAAC,EAAE;MACpD5E,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEwE,IAAI,CAAC;MACrC,OAAO,EAAE;;IAGX,MAAMG,IAAI,GAAGH,IAAI,CAACI,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACN,IAAI,CAACO,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACN,IAAI,CAACU,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAEnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;EAEA;EACAE,WAAWA,CAACnG,MAAc,EAAEb,QAAgB;IAC1C,OAAO,IAAIiH,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBnH,QAAQ,EAAEA;KACX,CAAC,CAACoH,MAAM,CAACvG,MAAM,CAAC;EACnB;EAEA;EACA3G,aAAaA,CAACmN,OAAe;IAC3B,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B;IACA;IAEA;IACA,MAAMC,KAAK,GAAGD,OAAO,CAACE,KAAK,CAAC,GAAG,CAAC;IAChC,MAAMC,SAAS,GAAGF,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;IAEhC;IACA,MAAMG,SAAS,GAAGD,SAAS,CAACD,KAAK,CAAC,GAAG,CAAC;IAEtC;IACA,MAAMG,OAAO,GAAGL,OAAO,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGN,OAAO,CAACM,SAAS,CAACN,OAAO,CAAClM,MAAM,GAAG,CAAC,CAAC;IAEvF,OAAO,QAAQsM,SAAS,CAAC,CAAC,CAAC,IAAI,KAAK,iBAAiBC,OAAO,EAAE;EAChE;;;uBAxkBW3I,2BAA2B,EAAAvF,EAAA,CAAAoO,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtO,EAAA,CAAAoO,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxO,EAAA,CAAAoO,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAAzO,EAAA,CAAAoO,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA3O,EAAA,CAAAoO,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAA7O,EAAA,CAAAoO,iBAAA,CAAAU,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA3BxJ,2BAA2B;MAAAyJ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5BxCtP,EAAA,CAAAE,cAAA,aAA2C;UAGdF,EAAA,CAAAC,MAAA,8BAAkB;UAAAD,EAAA,CAAAG,YAAA,EAAK;UAC9CH,EAAA,CAAAE,cAAA,WAAyB;UAAAF,EAAA,CAAAC,MAAA,4EAAsD;UAAAD,EAAA,CAAAG,YAAA,EAAI;UAErFH,EAAA,CAAAE,cAAA,aAAiC;UAC/BF,EAAA,CAAAQ,SAAA,aAAqE;UACvER,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAE,cAAA,mCAAmF;UAG/EF,EAAA,CAAAY,UAAA,KAAA4O,mDAAA,0BAA+D;UAE/DxP,EAAA,CAAAE,cAAA,eAA0B;UAElBF,EAAA,CAAAC,MAAA,4CAA0B;UAAAD,EAAA,CAAAG,YAAA,EAAK;UACnCH,EAAA,CAAAE,cAAA,SAAG;UAAAF,EAAA,CAAAC,MAAA,oGAA6E;UAAAD,EAAA,CAAAG,YAAA,EAAI;UAGtFH,EAAA,CAAAY,UAAA,KAAA6O,2CAAA,kBAGM;UAENzP,EAAA,CAAAY,UAAA,KAAA8O,2CAAA,kBAMM;UAEN1P,EAAA,CAAAY,UAAA,KAAA+O,2CAAA,mBAgDM;UAGN3P,EAAA,CAAAY,UAAA,KAAAgP,2CAAA,mBAOM;UACR5P,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAE,cAAA,mBAA8C;UAC5CF,EAAA,CAAAY,UAAA,KAAAiP,mDAAA,0BAA8D;UAE9D7P,EAAA,CAAAE,cAAA,eAA0B;UAElBF,EAAA,CAAAC,MAAA,kCAA0B;UAAAD,EAAA,CAAAG,YAAA,EAAK;UACnCH,EAAA,CAAAE,cAAA,SAAG;UAAAF,EAAA,CAAAC,MAAA,kEAA0D;UAAAD,EAAA,CAAAG,YAAA,EAAI;UAGnEH,EAAA,CAAAY,UAAA,KAAAkP,2CAAA,kBAGM;UAEN9P,EAAA,CAAAE,cAAA,gBAA0E;UAAlCF,EAAA,CAAAc,UAAA,sBAAAiP,+DAAA;YAAA,OAAYR,GAAA,CAAAzD,kBAAA,EAAoB;UAAA,EAAC;UACvE9L,EAAA,CAAAE,cAAA,eAA0B;UACpBF,EAAA,CAAAC,MAAA,wCAAsB;UAAAD,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAE,cAAA,eAAsB;UAEPF,EAAA,CAAAC,MAAA,yBAAiB;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAQ,SAAA,iBAAyD;UAC3DR,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAE,cAAA,eAAsB;UAEPF,EAAA,CAAAC,MAAA,gCAAmB;UAAAD,EAAA,CAAAG,YAAA,EAAY;UAC1CH,EAAA,CAAAQ,SAAA,oBAAyE;UAC3ER,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAE,cAAA,eAAsB;UAEPF,EAAA,CAAAC,MAAA,gDAA8B;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACrDH,EAAA,CAAAQ,SAAA,iBAA0D;UAC5DR,EAAA,CAAAG,YAAA,EAAiB;UAIrBH,EAAA,CAAAE,cAAA,eAA0B;UAElBF,EAAA,CAAAC,MAAA,iBAAS;UAAAD,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAE,cAAA,kBAA6G;UAA1DF,EAAA,CAAAc,UAAA,mBAAAkP,8DAAA;YAAA,OAAST,GAAA,CAAAjH,YAAA,EAAc;UAAA,EAAC;UACzEtI,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAC,MAAA,WAAG;UAAAD,EAAA,CAAAG,YAAA,EAAW;UAI5BH,EAAA,CAAAE,cAAA,eAAgC;UAE5BF,EAAA,CAAAY,UAAA,KAAAqP,2DAAA,qCA4NsB;UACxBjQ,EAAA,CAAAG,YAAA,EAAgB;UAIpBH,EAAA,CAAAE,cAAA,eAA0B;UAEtBF,EAAA,CAAAY,UAAA,KAAAsP,mDAAA,0BAAkF;UAClFlQ,EAAA,CAAAY,UAAA,KAAAuP,4CAAA,mBAA4D;UAC9DnQ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAE,cAAA,kBAA0D;UAAzBF,EAAA,CAAAc,UAAA,mBAAAsP,8DAAA;YAAA,OAASb,GAAA,CAAA5C,YAAA,EAAc;UAAA,EAAC;UAAC3M,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAG,YAAA,EAAS;UAK7EH,EAAA,CAAAY,UAAA,KAAAyP,2CAAA,mBAMM;UACRrQ,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAE,cAAA,mBAAgD;UAC9CF,EAAA,CAAAY,UAAA,KAAA0P,mDAAA,0BAAgE;UAEhEtQ,EAAA,CAAAE,cAAA,eAA0B;UAElBF,EAAA,CAAAC,MAAA,gCAAwB;UAAAD,EAAA,CAAAG,YAAA,EAAK;UACjCH,EAAA,CAAAE,cAAA,SAAG;UAAAF,EAAA,CAAAC,MAAA,mEAAsD;UAAAD,EAAA,CAAAG,YAAA,EAAI;UAG/DH,EAAA,CAAAY,UAAA,KAAA2P,2CAAA,kBAGM;UAENvQ,EAAA,CAAAE,cAAA,gBAA2E;UAAjCF,EAAA,CAAAc,UAAA,sBAAA0P,+DAAA;YAAA,OAAYjB,GAAA,CAAA7C,iBAAA,EAAmB;UAAA,EAAC;UACxE1M,EAAA,CAAAE,cAAA,eAA0B;UACpBF,EAAA,CAAAC,MAAA,gCAAwB;UAAAD,EAAA,CAAAG,YAAA,EAAK;UACjCH,EAAA,CAAAE,cAAA,eAAsB;UAEPF,EAAA,CAAAC,MAAA,yBAAiB;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAQ,SAAA,iBAAyD;UAC3DR,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAE,cAAA,eAAsB;UAEPF,EAAA,CAAAC,MAAA,0BAAkB;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACzCH,EAAA,CAAAE,cAAA,sBAA4C;UAClBF,EAAA,CAAAC,MAAA,4BAAe;UAAAD,EAAA,CAAAG,YAAA,EAAa;UACpDH,EAAA,CAAAE,cAAA,sBAAwB;UAAAF,EAAA,CAAAC,MAAA,yBAAiB;UAAAD,EAAA,CAAAG,YAAA,EAAa;UACtDH,EAAA,CAAAE,cAAA,sBAAwB;UAAAF,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAG,YAAA,EAAa;UAKjDH,EAAA,CAAAE,cAAA,eAAwC;UAGvBF,EAAA,CAAAC,MAAA,yBAAgB;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAQ,SAAA,kBAA8C;UAChDR,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAE,cAAA,2BAAqC;UACxBF,EAAA,CAAAC,MAAA,yBAAgB;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAE,cAAA,uBAA4C;UAClBF,EAAA,CAAAC,MAAA,6BAAe;UAAAD,EAAA,CAAAG,YAAA,EAAa;UACpDH,EAAA,CAAAE,cAAA,uBAAwB;UAAAF,EAAA,CAAAC,MAAA,0BAAiB;UAAAD,EAAA,CAAAG,YAAA,EAAa;UACtDH,EAAA,CAAAE,cAAA,uBAAwB;UAAAF,EAAA,CAAAC,MAAA,eAAM;UAAAD,EAAA,CAAAG,YAAA,EAAa;UAKjDH,EAAA,CAAAE,cAAA,gBAAsB;UAGLF,EAAA,CAAAC,MAAA,gBAAO;UAAAD,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAQ,SAAA,kBAAgE;UAClER,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAE,cAAA,2BAAqC;UACxBF,EAAA,CAAAC,MAAA,eAAM;UAAAD,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAE,cAAA,uBAAuC;UACbF,EAAA,CAAAC,MAAA,mBAAU;UAAAD,EAAA,CAAAG,YAAA,EAAa;UAC/CH,EAAA,CAAAE,cAAA,uBAAwB;UAAAF,EAAA,CAAAC,MAAA,oCAAsB;UAAAD,EAAA,CAAAG,YAAA,EAAa;UAC3DH,EAAA,CAAAE,cAAA,uBAAwB;UAAAF,EAAA,CAAAC,MAAA,6BAAoB;UAAAD,EAAA,CAAAG,YAAA,EAAa;UAMjEH,EAAA,CAAAE,cAAA,gBAAsB;UAEPF,EAAA,CAAAC,MAAA,6BAAoB;UAAAD,EAAA,CAAAG,YAAA,EAAY;UAC3CH,EAAA,CAAAQ,SAAA,kBAAmD;UACrDR,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAE,cAAA,2BAAqC;UACxBF,EAAA,CAAAC,MAAA,yBAAgB;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAQ,SAAA,kBAAkF;UAGpFR,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAE,cAAA,gBAAsB;UAEPF,EAAA,CAAAC,MAAA,0BAAY;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAQ,SAAA,kBAA8C;UAChDR,EAAA,CAAAG,YAAA,EAAiB;UAEjBH,EAAA,CAAAE,cAAA,2BAAqC;UACxBF,EAAA,CAAAC,MAAA,4BAAS;UAAAD,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAQ,SAAA,kBAA4C;UAC9CR,EAAA,CAAAG,YAAA,EAAiB;UAKvBH,EAAA,CAAAE,cAAA,gBAA0B;UAEtBF,EAAA,CAAAY,UAAA,MAAA6P,oDAAA,0BAAkF;UAClFzQ,EAAA,CAAAY,UAAA,MAAA8P,6CAAA,mBAAwD;UAC1D1Q,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAE,cAAA,mBAA0D;UAAzBF,EAAA,CAAAc,UAAA,mBAAA6P,+DAAA;YAAA,OAASpB,GAAA,CAAA5C,YAAA,EAAc;UAAA,EAAC;UAAC3M,EAAA,CAAAC,MAAA,eAAM;UAAAD,EAAA,CAAAG,YAAA,EAAS;UAK7EH,EAAA,CAAAY,UAAA,MAAAgQ,4CAAA,mBAiBM;UACR5Q,EAAA,CAAAG,YAAA,EAAM;;;;UA5fcH,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAA4B,UAAA,gBAAe,kBAAA2N,GAAA,CAAAxJ,WAAA;UAE3B/F,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAA4B,UAAA,cAAA2N,GAAA,CAAAvN,aAAA,UAAoC;UASpChC,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAA4B,UAAA,SAAA2N,GAAA,CAAAhP,YAAA,CAAkB;UAKlBP,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAA4B,UAAA,SAAA2N,GAAA,CAAA7N,QAAA,CAAAC,MAAA,OAA2B;UAQ3B3B,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAA4B,UAAA,SAAA2N,GAAA,CAAA7N,QAAA,CAAAC,MAAA,KAAyB;UAmDzB3B,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAA4B,UAAA,SAAA2N,GAAA,CAAAvN,aAAA,IAAAuN,GAAA,CAAAvN,aAAA,CAAAC,IAAA,CAAyC;UAYzCjC,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAA4B,UAAA,cAAA2N,GAAA,CAAAtK,YAAA,UAAmC;UASnCjF,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAA4B,UAAA,SAAA2N,GAAA,CAAAhP,YAAA,CAAkB;UAKlBP,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAA4B,UAAA,cAAA2N,GAAA,CAAA5I,mBAAA,CAAiC;UAmCY3G,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAA4B,UAAA,YAAA2N,GAAA,CAAAxK,UAAA,CAAAgH,QAAA,CAAwB;UAkOf/L,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAA4B,UAAA,aAAA2N,GAAA,CAAAzN,SAAA,IAAAyN,GAAA,CAAA5I,mBAAA,CAAAnC,OAAA,CAAqD;UAC7FxE,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAA4B,UAAA,SAAA2N,GAAA,CAAAzN,SAAA,CAAe;UACtB9B,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAA4B,UAAA,UAAA2N,GAAA,CAAAzN,SAAA,CAAgB;UAOvB9B,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAA4B,UAAA,SAAA2N,GAAA,CAAAtK,YAAA,IAAAsK,GAAA,CAAAtK,YAAA,CAAAhD,IAAA,CAAuC;UAWvCjC,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAA4B,UAAA,cAAA2N,GAAA,CAAAlK,cAAA,UAAqC;UASrCrF,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAA4B,UAAA,SAAA2N,GAAA,CAAAhP,YAAA,CAAkB;UAKlBP,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAA4B,UAAA,cAAA2N,GAAA,CAAAxI,qBAAA,CAAmC;UAcnB/G,EAAA,CAAAI,SAAA,IAAW;UAAXJ,EAAA,CAAA4B,UAAA,YAAW;UACX5B,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAA4B,UAAA,YAAW;UACX5B,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAA4B,UAAA,YAAW;UAeT5B,EAAA,CAAAI,SAAA,IAAW;UAAXJ,EAAA,CAAA4B,UAAA,YAAW;UACX5B,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAA4B,UAAA,YAAW;UACX5B,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAA4B,UAAA,YAAW;UA+BT5B,EAAA,CAAAI,SAAA,IAAmC;UAAnCJ,EAAA,CAAA4B,UAAA,kBAAAiP,IAAA,CAAmC;UAClB7Q,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAA4B,UAAA,QAAAiP,IAAA,CAAyB;UAoBR7Q,EAAA,CAAAI,SAAA,IAAuD;UAAvDJ,EAAA,CAAA4B,UAAA,aAAA2N,GAAA,CAAAzN,SAAA,IAAAyN,GAAA,CAAAxI,qBAAA,CAAAvC,OAAA,CAAuD;UAC/FxE,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAA4B,UAAA,SAAA2N,GAAA,CAAAzN,SAAA,CAAe;UACtB9B,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAA4B,UAAA,UAAA2N,GAAA,CAAAzN,SAAA,CAAgB;UAOvB9B,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAA4B,UAAA,SAAA2N,GAAA,CAAAlK,cAAA,IAAAkK,GAAA,CAAAlK,cAAA,CAAApD,IAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}