{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport let BookingService = /*#__PURE__*/(() => {\n  class BookingService {\n    constructor(http, authService) {\n      this.http = http;\n      this.authService = authService;\n      this.apiUrl = 'http://localhost:8080/booking';\n    }\n    /**\n     * Méthode principale pour effectuer une transaction de réservation\n     * @param request La requête de transaction de réservation\n     * @returns Une observable de la réponse de transaction de réservation\n     */\n    bookingTransaction(request) {\n      // Utiliser l'endpoint unifié pour toutes les actions de transaction\n      const endpoint = '/booking-transaction';\n      const requestBody = request; // Envoyer l'objet BookingTransactionRequest complet\n      // Récupérer le token d'authentification\n      const token = this.authService.getToken();\n      if (!token) {\n        throw new Error('Vous devez être connecté pour effectuer cette action.');\n      }\n      // Créer les en-têtes avec le token d'authentification\n      const headers = new HttpHeaders({\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${token}`\n      });\n      console.log(`Envoi de la requête ${request.action} à ${endpoint}:`, requestBody);\n      return this.http.post(`${this.apiUrl}${endpoint}`, requestBody, {\n        headers\n      }).pipe(map(response => {\n        console.log(`Réponse reçue de ${endpoint}:`, response);\n        // Vérifier si la réponse est valide\n        if (!response) {\n          throw new Error('Réponse invalide du serveur');\n        }\n        // Vérifier si la réponse est au format BookingTransactionResponse\n        // ou si c'est une réponse directe du type spécifique (BeginTransactionResponse, etc.)\n        let bookingResponse = {\n          action: request.action\n        };\n        // Si la réponse a déjà le format BookingTransactionResponse\n        if (response.action && (response.beginResponse && request.action === 'begin' || response.infoResponse && request.action === 'info' || response.commitResponse && request.action === 'commit')) {\n          bookingResponse = response;\n        }\n        // Si la réponse est une réponse directe (non encapsulée)\n        else {\n          // Adapter la réponse selon l'action\n          switch (request.action) {\n            case 'begin':\n              // Si c'est une réponse BeginTransactionResponse directe\n              if (response.header && response.body && response.body.transactionId) {\n                bookingResponse.beginResponse = response;\n              }\n              // Si c'est un format simplifié ou personnalisé\n              else if (response.transactionId) {\n                bookingResponse.beginResponse = {\n                  header: {\n                    requestId: response.requestId || '',\n                    success: true,\n                    responseTime: response.responseTime || new Date().toISOString(),\n                    messages: response.messages || []\n                  },\n                  body: {\n                    transactionId: response.transactionId,\n                    expiresOn: response.expiresOn || new Date(Date.now() + 3600000).toISOString(),\n                    status: response.status || 1,\n                    transactionType: response.transactionType || 1,\n                    reservationData: response.reservationData || {\n                      travellers: [],\n                      reservationInfo: null,\n                      services: [],\n                      paymentDetail: null,\n                      invoices: []\n                    }\n                  }\n                };\n              } else {\n                throw new Error('Format de réponse non reconnu pour l\\'action begin');\n              }\n              break;\n            case 'info':\n              // Si c'est une réponse SetReservationInfoResponse directe\n              if (response.header && response.body && response.body.transactionId) {\n                bookingResponse.infoResponse = response;\n              } else {\n                throw new Error('Format de réponse non reconnu pour l\\'action info');\n              }\n              break;\n            case 'commit':\n              // Si c'est une réponse CommitTransactionResponse directe\n              if (response.header && response.body && (response.body.reservationNumber || response.body.transactionId)) {\n                bookingResponse.commitResponse = response;\n              } else {\n                throw new Error('Format de réponse non reconnu pour l\\'action commit');\n              }\n              break;\n          }\n        }\n        return bookingResponse;\n      }), catchError(error => {\n        console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);\n        // Améliorer le message d'erreur pour faciliter le débogage\n        let errorMessage = 'Une erreur est survenue lors de la transaction de réservation';\n        if (error.error && error.error.message) {\n          // Erreur provenant du backend avec un message\n          errorMessage = `Erreur: ${error.error.message}`;\n        } else if (error.message) {\n          // Erreur avec un message simple\n          errorMessage = error.message;\n        } else if (error.status) {\n          // Erreur HTTP\n          switch (error.status) {\n            case 401:\n              errorMessage = 'Vous n\\'êtes pas autorisé à effectuer cette action. Veuillez vous reconnecter.';\n              break;\n            case 403:\n              errorMessage = 'Accès refusé. Vous n\\'avez pas les droits nécessaires pour effectuer cette action.';\n              break;\n            case 404:\n              errorMessage = 'Le service de réservation est introuvable. Veuillez contacter l\\'administrateur.';\n              break;\n            case 500:\n              errorMessage = 'Erreur interne du serveur. Veuillez réessayer ultérieurement.';\n              break;\n            default:\n              errorMessage = `Erreur HTTP ${error.status}: ${error.statusText}`;\n          }\n        }\n        // Créer une nouvelle erreur avec un message plus descriptif\n        const enhancedError = new Error(errorMessage);\n        enhancedError.name = 'BookingTransactionError';\n        // Conserver les détails de l'erreur originale\n        enhancedError.originalError = error;\n        throw enhancedError;\n      }));\n    }\n    /**\n     * Méthode pour démarrer une transaction de réservation\n     * @param offerIds Les IDs des offres à réserver\n     * @param currency La devise\n     * @param culture La culture\n     * @returns Une observable de la réponse de début de transaction\n     */\n    beginTransaction(offerIds, currency = 'EUR', culture = 'fr-FR') {\n      const request = {\n        action: 'begin',\n        beginRequest: {\n          offerIds,\n          currency,\n          culture\n        }\n      };\n      return this.bookingTransaction(request);\n    }\n    /**\n     * Méthode pour définir les informations de réservation\n     * @param request La requête d'informations de réservation\n     * @returns Une observable de la réponse d'informations de réservation\n     */\n    setReservationInfo(request) {\n      // Vérifier si tous les passagers sont des enfants ou des bébés\n      const hasAdult = request.travellers.some(traveller => traveller.passengerType === 1 // 1 = Adult\n      );\n      // Si tous les passagers sont des enfants ou des bébés, s'assurer que customerInfo n'est pas inclus\n      if (!hasAdult && request.customerInfo) {\n        console.log('Tous les passagers sont des enfants ou des bébés, customerInfo ne sera pas inclus dans la requête');\n        // Créer une nouvelle requête sans customerInfo\n        const {\n          customerInfo,\n          ...requestWithoutCustomerInfo\n        } = request;\n        request = requestWithoutCustomerInfo;\n      }\n      const bookingRequest = {\n        action: 'info',\n        infoRequest: request\n      };\n      return this.bookingTransaction(bookingRequest);\n    }\n    /**\n     * Méthode pour finaliser une transaction de réservation\n     * @param request La requête de finalisation de transaction\n     * @returns Une observable de la réponse de finalisation de transaction\n     */\n    commitTransaction(request) {\n      const bookingRequest = {\n        action: 'commit',\n        commitRequest: request\n      };\n      return this.bookingTransaction(bookingRequest);\n    }\n    /**\n     * Crée un objet de requête d'informations de réservation par défaut\n     * @param transactionId L'ID de transaction\n     * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n     */\n    createDefaultReservationInfoRequest(transactionId) {\n      return {\n        transactionId,\n        travellers: [],\n        reservationNote: '',\n        agencyReservationNumber: ''\n      };\n    }\n    /**\n     * Crée un objet de requête de finalisation de transaction par défaut\n     * @param transactionId L'ID de transaction\n     * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n     */\n    createDefaultCommitTransactionRequest(transactionId) {\n      return {\n        transactionId,\n        paymentOption: 1,\n        paymentInformation: {\n          paymentTypeId: 1,\n          paymentPrice: {\n            amount: 0,\n            currency: 'EUR'\n          },\n          paymentDate: new Date().toISOString()\n        }\n      };\n    }\n    static {\n      this.ɵfac = function BookingService_Factory(t) {\n        return new (t || BookingService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: BookingService,\n        factory: BookingService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return BookingService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}