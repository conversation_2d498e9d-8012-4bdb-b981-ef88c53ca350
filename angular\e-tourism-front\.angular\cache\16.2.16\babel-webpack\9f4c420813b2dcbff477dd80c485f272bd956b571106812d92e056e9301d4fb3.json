{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './components/login/login.component';\nimport { SearchPriceComponent } from './components/product/search-price/search-price.component';\nimport { GetOfferComponent } from './components/product/get-offer/get-offer.component';\nimport { BookingTransactionComponent } from './components/booking/booking-transaction/booking-transaction.component';\nimport { MainLayoutComponent } from './layout/main-layout/main-layout.component';\nimport { AuthGuard } from './guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: '',\n  component: MainLayoutComponent,\n  canActivate: [AuthGuard],\n  children: [{\n    path: 'search-price',\n    component: SearchPriceComponent\n  }, {\n    path: 'get-offer',\n    component: GetOfferComponent\n  }, {\n    path: 'booking-transaction',\n    component: BookingTransactionComponent\n  }, {\n    path: '',\n    redirectTo: '/search-price',\n    pathMatch: 'full'\n  }]\n}, {\n  path: '**',\n  redirectTo: '/login'\n}];\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {\n    static {\n      this.ɵfac = function AppRoutingModule_Factory(t) {\n        return new (t || AppRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forRoot(routes), RouterModule]\n      });\n    }\n  }\n  return AppRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}