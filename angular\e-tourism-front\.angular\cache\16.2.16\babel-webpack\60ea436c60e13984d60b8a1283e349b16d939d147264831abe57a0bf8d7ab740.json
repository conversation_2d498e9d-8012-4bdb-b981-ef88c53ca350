{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport class BookingService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.apiUrl = 'http://localhost:8080/booking';\n  }\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request) {\n    // Utiliser l'endpoint unifié pour toutes les actions de transaction\n    const endpoint = '/booking-transaction';\n    const requestBody = request; // Envoyer l'objet BookingTransactionRequest complet\n    // Récupérer le token d'authentification\n    const token = this.authService.getToken();\n    if (!token) {\n      throw new Error('Vous devez être connecté pour effectuer cette action.');\n    }\n    // Créer les en-têtes avec le token d'authentification\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n    console.log(`Envoi de la requête ${request.action} à ${endpoint}:`, requestBody);\n    return this.http.post(`${this.apiUrl}${endpoint}`, requestBody, {\n      headers\n    }).pipe(map(response => {\n      console.log(`Réponse reçue de ${endpoint}:`, response);\n      // Vérifier si la réponse est valide\n      if (!response) {\n        throw new Error('Réponse invalide du serveur');\n      }\n      // La réponse du backend est déjà au format BookingTransactionResponse\n      // Nous vérifions simplement qu'elle contient les données attendues\n      if (!response.action) {\n        response.action = request.action; // Assurer que l'action est définie\n      }\n      // Vérifier que la réponse contient les données appropriées selon l'action\n      switch (request.action) {\n        case 'begin':\n          if (!response.beginResponse) {\n            throw new Error('La réponse ne contient pas de données de début de transaction');\n          }\n          break;\n        case 'info':\n          if (!response.infoResponse) {\n            throw new Error('La réponse ne contient pas de données d\\'information de réservation');\n          }\n          break;\n        case 'commit':\n          if (!response.commitResponse) {\n            throw new Error('La réponse ne contient pas de données de finalisation de transaction');\n          }\n          break;\n      }\n      return response;\n    }), catchError(error => {\n      console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);\n      // Améliorer le message d'erreur pour faciliter le débogage\n      let errorMessage = 'Une erreur est survenue lors de la transaction de réservation';\n      if (error.error && error.error.message) {\n        // Erreur provenant du backend avec un message\n        errorMessage = `Erreur: ${error.error.message}`;\n      } else if (error.message) {\n        // Erreur avec un message simple\n        errorMessage = error.message;\n      } else if (error.status) {\n        // Erreur HTTP\n        switch (error.status) {\n          case 401:\n            errorMessage = 'Vous n\\'êtes pas autorisé à effectuer cette action. Veuillez vous reconnecter.';\n            break;\n          case 403:\n            errorMessage = 'Accès refusé. Vous n\\'avez pas les droits nécessaires pour effectuer cette action.';\n            break;\n          case 404:\n            errorMessage = 'Le service de réservation est introuvable. Veuillez contacter l\\'administrateur.';\n            break;\n          case 500:\n            errorMessage = 'Erreur interne du serveur. Veuillez réessayer ultérieurement.';\n            break;\n          default:\n            errorMessage = `Erreur HTTP ${error.status}: ${error.statusText}`;\n        }\n      }\n      // Créer une nouvelle erreur avec un message plus descriptif\n      const enhancedError = new Error(errorMessage);\n      enhancedError.name = 'BookingTransactionError';\n      // Conserver les détails de l'erreur originale\n      enhancedError.originalError = error;\n      throw enhancedError;\n    }));\n  }\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds, currency = 'EUR', culture = 'fr-FR') {\n    const request = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n    return this.bookingTransaction(request);\n  }\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request) {\n    const bookingRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request) {\n    const bookingRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n    return this.bookingTransaction(bookingRequest);\n  }\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId) {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId) {\n    return {\n      transactionId,\n      paymentOption: 1,\n      paymentInformation: {\n        paymentTypeId: 1,\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n  static {\n    this.ɵfac = function BookingService_Factory(t) {\n      return new (t || BookingService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BookingService,\n      factory: BookingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "catchError", "map", "BookingService", "constructor", "http", "authService", "apiUrl", "bookingTransaction", "request", "endpoint", "requestBody", "token", "getToken", "Error", "headers", "console", "log", "action", "post", "pipe", "response", "beginResponse", "infoResponse", "commitResponse", "error", "errorMessage", "message", "status", "statusText", "enhancedError", "name", "originalError", "beginTransaction", "offerIds", "currency", "culture", "beginRequest", "setReservationInfo", "bookingRequest", "infoRequest", "commitTransaction", "commitRequest", "createDefaultReservationInfoRequest", "transactionId", "travellers", "reservationNote", "agencyReservationNumber", "createDefaultCommitTransactionRequest", "paymentOption", "paymentInformation", "paymentTypeId", "paymentPrice", "amount", "paymentDate", "Date", "toISOString", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\booking.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { AuthService } from './auth.service';\nimport {\n  BookingTransactionRequest,\n  BookingTransactionResponse,\n  BeginTransactionRequest,\n  BeginTransactionResponse,\n  SetReservationInfoRequest,\n  SetReservationInfoResponse,\n  CommitTransactionRequest,\n  CommitTransactionResponse\n} from '../models/booking';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class BookingService {\n  private apiUrl = 'http://localhost:8080/booking';\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) { }\n\n  /**\n   * Méthode principale pour effectuer une transaction de réservation\n   * @param request La requête de transaction de réservation\n   * @returns Une observable de la réponse de transaction de réservation\n   */\n  bookingTransaction(request: BookingTransactionRequest): Observable<BookingTransactionResponse> {\n    // Utiliser l'endpoint unifié pour toutes les actions de transaction\n    const endpoint = '/booking-transaction';\n    const requestBody = request; // Envoyer l'objet BookingTransactionRequest complet\n\n    // Récupérer le token d'authentification\n    const token = this.authService.getToken();\n    if (!token) {\n      throw new Error('Vous devez être connecté pour effectuer cette action.');\n    }\n\n    // Créer les en-têtes avec le token d'authentification\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n\n    console.log(`Envoi de la requête ${request.action} à ${endpoint}:`, requestBody);\n\n    return this.http.post<BookingTransactionResponse>(\n      `${this.apiUrl}${endpoint}`,\n      requestBody,\n      { headers }\n    ).pipe(\n      map(response => {\n        console.log(`Réponse reçue de ${endpoint}:`, response);\n\n        // Vérifier si la réponse est valide\n        if (!response) {\n          throw new Error('Réponse invalide du serveur');\n        }\n\n        // La réponse du backend est déjà au format BookingTransactionResponse\n        // Nous vérifions simplement qu'elle contient les données attendues\n        if (!response.action) {\n          response.action = request.action; // Assurer que l'action est définie\n        }\n\n        // Vérifier que la réponse contient les données appropriées selon l'action\n        switch (request.action) {\n          case 'begin':\n            if (!response.beginResponse) {\n              throw new Error('La réponse ne contient pas de données de début de transaction');\n            }\n            break;\n          case 'info':\n            if (!response.infoResponse) {\n              throw new Error('La réponse ne contient pas de données d\\'information de réservation');\n            }\n            break;\n          case 'commit':\n            if (!response.commitResponse) {\n              throw new Error('La réponse ne contient pas de données de finalisation de transaction');\n            }\n            break;\n        }\n\n        return response;\n      }),\n      catchError(error => {\n        console.error(`Erreur lors de la transaction de réservation (${request.action}):`, error);\n\n        // Améliorer le message d'erreur pour faciliter le débogage\n        let errorMessage = 'Une erreur est survenue lors de la transaction de réservation';\n\n        if (error.error && error.error.message) {\n          // Erreur provenant du backend avec un message\n          errorMessage = `Erreur: ${error.error.message}`;\n        } else if (error.message) {\n          // Erreur avec un message simple\n          errorMessage = error.message;\n        } else if (error.status) {\n          // Erreur HTTP\n          switch (error.status) {\n            case 401:\n              errorMessage = 'Vous n\\'êtes pas autorisé à effectuer cette action. Veuillez vous reconnecter.';\n              break;\n            case 403:\n              errorMessage = 'Accès refusé. Vous n\\'avez pas les droits nécessaires pour effectuer cette action.';\n              break;\n            case 404:\n              errorMessage = 'Le service de réservation est introuvable. Veuillez contacter l\\'administrateur.';\n              break;\n            case 500:\n              errorMessage = 'Erreur interne du serveur. Veuillez réessayer ultérieurement.';\n              break;\n            default:\n              errorMessage = `Erreur HTTP ${error.status}: ${error.statusText}`;\n          }\n        }\n\n        // Créer une nouvelle erreur avec un message plus descriptif\n        const enhancedError = new Error(errorMessage);\n        enhancedError.name = 'BookingTransactionError';\n\n        // Conserver les détails de l'erreur originale\n        (enhancedError as any).originalError = error;\n\n        throw enhancedError;\n      })\n    );\n  }\n\n  /**\n   * Méthode pour démarrer une transaction de réservation\n   * @param offerIds Les IDs des offres à réserver\n   * @param currency La devise\n   * @param culture La culture\n   * @returns Une observable de la réponse de début de transaction\n   */\n  beginTransaction(offerIds: string[], currency: string = 'EUR', culture: string = 'fr-FR'): Observable<BookingTransactionResponse> {\n    const request: BookingTransactionRequest = {\n      action: 'begin',\n      beginRequest: {\n        offerIds,\n        currency,\n        culture\n      }\n    };\n\n    return this.bookingTransaction(request);\n  }\n\n  /**\n   * Méthode pour définir les informations de réservation\n   * @param request La requête d'informations de réservation\n   * @returns Une observable de la réponse d'informations de réservation\n   */\n  setReservationInfo(request: SetReservationInfoRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'info',\n      infoRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Méthode pour finaliser une transaction de réservation\n   * @param request La requête de finalisation de transaction\n   * @returns Une observable de la réponse de finalisation de transaction\n   */\n  commitTransaction(request: CommitTransactionRequest): Observable<BookingTransactionResponse> {\n    const bookingRequest: BookingTransactionRequest = {\n      action: 'commit',\n      commitRequest: request\n    };\n\n    return this.bookingTransaction(bookingRequest);\n  }\n\n  /**\n   * Crée un objet de requête d'informations de réservation par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet SetReservationInfoRequest avec des valeurs par défaut\n   */\n  createDefaultReservationInfoRequest(transactionId: string): SetReservationInfoRequest {\n    return {\n      transactionId,\n      travellers: [],\n      reservationNote: '',\n      agencyReservationNumber: ''\n    };\n  }\n\n  /**\n   * Crée un objet de requête de finalisation de transaction par défaut\n   * @param transactionId L'ID de transaction\n   * @returns Un objet CommitTransactionRequest avec des valeurs par défaut\n   */\n  createDefaultCommitTransactionRequest(transactionId: string): CommitTransactionRequest {\n    return {\n      transactionId,\n      paymentOption: 1, // Option de paiement par défaut\n      paymentInformation: {\n        paymentTypeId: 1, // Type de paiement par défaut\n        paymentPrice: {\n          amount: 0,\n          currency: 'EUR'\n        },\n        paymentDate: new Date().toISOString()\n      }\n    };\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;;AAgBhD,OAAM,MAAOC,cAAc;EAGzBC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAJb,KAAAC,MAAM,GAAG,+BAA+B;EAK5C;EAEJ;;;;;EAKAC,kBAAkBA,CAACC,OAAkC;IACnD;IACA,MAAMC,QAAQ,GAAG,sBAAsB;IACvC,MAAMC,WAAW,GAAGF,OAAO,CAAC,CAAC;IAE7B;IACA,MAAMG,KAAK,GAAG,IAAI,CAACN,WAAW,CAACO,QAAQ,EAAE;IACzC,IAAI,CAACD,KAAK,EAAE;MACV,MAAM,IAAIE,KAAK,CAAC,uDAAuD,CAAC;;IAG1E;IACA,MAAMC,OAAO,GAAG,IAAIf,WAAW,CAAC;MAC9B,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAE,UAAUY,KAAK;KACjC,CAAC;IAEFI,OAAO,CAACC,GAAG,CAAC,uBAAuBR,OAAO,CAACS,MAAM,MAAMR,QAAQ,GAAG,EAAEC,WAAW,CAAC;IAEhF,OAAO,IAAI,CAACN,IAAI,CAACc,IAAI,CACnB,GAAG,IAAI,CAACZ,MAAM,GAAGG,QAAQ,EAAE,EAC3BC,WAAW,EACX;MAAEI;IAAO,CAAE,CACZ,CAACK,IAAI,CACJlB,GAAG,CAACmB,QAAQ,IAAG;MACbL,OAAO,CAACC,GAAG,CAAC,oBAAoBP,QAAQ,GAAG,EAAEW,QAAQ,CAAC;MAEtD;MACA,IAAI,CAACA,QAAQ,EAAE;QACb,MAAM,IAAIP,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA;MACA,IAAI,CAACO,QAAQ,CAACH,MAAM,EAAE;QACpBG,QAAQ,CAACH,MAAM,GAAGT,OAAO,CAACS,MAAM,CAAC,CAAC;;MAGpC;MACA,QAAQT,OAAO,CAACS,MAAM;QACpB,KAAK,OAAO;UACV,IAAI,CAACG,QAAQ,CAACC,aAAa,EAAE;YAC3B,MAAM,IAAIR,KAAK,CAAC,+DAA+D,CAAC;;UAElF;QACF,KAAK,MAAM;UACT,IAAI,CAACO,QAAQ,CAACE,YAAY,EAAE;YAC1B,MAAM,IAAIT,KAAK,CAAC,qEAAqE,CAAC;;UAExF;QACF,KAAK,QAAQ;UACX,IAAI,CAACO,QAAQ,CAACG,cAAc,EAAE;YAC5B,MAAM,IAAIV,KAAK,CAAC,sEAAsE,CAAC;;UAEzF;;MAGJ,OAAOO,QAAQ;IACjB,CAAC,CAAC,EACFpB,UAAU,CAACwB,KAAK,IAAG;MACjBT,OAAO,CAACS,KAAK,CAAC,iDAAiDhB,OAAO,CAACS,MAAM,IAAI,EAAEO,KAAK,CAAC;MAEzF;MACA,IAAIC,YAAY,GAAG,+DAA+D;MAElF,IAAID,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACE,OAAO,EAAE;QACtC;QACAD,YAAY,GAAG,WAAWD,KAAK,CAACA,KAAK,CAACE,OAAO,EAAE;OAChD,MAAM,IAAIF,KAAK,CAACE,OAAO,EAAE;QACxB;QACAD,YAAY,GAAGD,KAAK,CAACE,OAAO;OAC7B,MAAM,IAAIF,KAAK,CAACG,MAAM,EAAE;QACvB;QACA,QAAQH,KAAK,CAACG,MAAM;UAClB,KAAK,GAAG;YACNF,YAAY,GAAG,gFAAgF;YAC/F;UACF,KAAK,GAAG;YACNA,YAAY,GAAG,oFAAoF;YACnG;UACF,KAAK,GAAG;YACNA,YAAY,GAAG,kFAAkF;YACjG;UACF,KAAK,GAAG;YACNA,YAAY,GAAG,+DAA+D;YAC9E;UACF;YACEA,YAAY,GAAG,eAAeD,KAAK,CAACG,MAAM,KAAKH,KAAK,CAACI,UAAU,EAAE;;;MAIvE;MACA,MAAMC,aAAa,GAAG,IAAIhB,KAAK,CAACY,YAAY,CAAC;MAC7CI,aAAa,CAACC,IAAI,GAAG,yBAAyB;MAE9C;MACCD,aAAqB,CAACE,aAAa,GAAGP,KAAK;MAE5C,MAAMK,aAAa;IACrB,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;EAOAG,gBAAgBA,CAACC,QAAkB,EAAEC,QAAA,GAAmB,KAAK,EAAEC,OAAA,GAAkB,OAAO;IACtF,MAAM3B,OAAO,GAA8B;MACzCS,MAAM,EAAE,OAAO;MACfmB,YAAY,EAAE;QACZH,QAAQ;QACRC,QAAQ;QACRC;;KAEH;IAED,OAAO,IAAI,CAAC5B,kBAAkB,CAACC,OAAO,CAAC;EACzC;EAEA;;;;;EAKA6B,kBAAkBA,CAAC7B,OAAkC;IACnD,MAAM8B,cAAc,GAA8B;MAChDrB,MAAM,EAAE,MAAM;MACdsB,WAAW,EAAE/B;KACd;IAED,OAAO,IAAI,CAACD,kBAAkB,CAAC+B,cAAc,CAAC;EAChD;EAEA;;;;;EAKAE,iBAAiBA,CAAChC,OAAiC;IACjD,MAAM8B,cAAc,GAA8B;MAChDrB,MAAM,EAAE,QAAQ;MAChBwB,aAAa,EAAEjC;KAChB;IAED,OAAO,IAAI,CAACD,kBAAkB,CAAC+B,cAAc,CAAC;EAChD;EAEA;;;;;EAKAI,mCAAmCA,CAACC,aAAqB;IACvD,OAAO;MACLA,aAAa;MACbC,UAAU,EAAE,EAAE;MACdC,eAAe,EAAE,EAAE;MACnBC,uBAAuB,EAAE;KAC1B;EACH;EAEA;;;;;EAKAC,qCAAqCA,CAACJ,aAAqB;IACzD,OAAO;MACLA,aAAa;MACbK,aAAa,EAAE,CAAC;MAChBC,kBAAkB,EAAE;QAClBC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE;UACZC,MAAM,EAAE,CAAC;UACTlB,QAAQ,EAAE;SACX;QACDmB,WAAW,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;;KAEtC;EACH;;;uBApMWrD,cAAc,EAAAsD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAd3D,cAAc;MAAA4D,OAAA,EAAd5D,cAAc,CAAA6D,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}