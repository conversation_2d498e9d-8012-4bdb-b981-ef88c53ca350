package com.paximum.demo.models;

import java.util.List;


public class OneWayResponse {

  
    private Header header;
    private Body body;

    public OneWayResponse() {
    }

    public Header getHeader() {
        return header;
    }

    public void setHeader(Header header) {
        this.header = header;
    }

    public Body getBody() {
        return body;
    }

    public void setBody(Body body) {
        this.body = body;
    }

    // Classe Header
    public static class Header {
        private String requestId;
        private boolean success;
        private String responseTime;
        private List<Message> messages;

        public Header() {
        }

        public String getRequestId() {
            return requestId;
        }
        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }
        public boolean isSuccess() {
            return success;
        }
        public void setSuccess(boolean success) {
            this.success = success;
        }
        public String getResponseTime() {
            return responseTime;
        }
        public void setResponseTime(String responseTime) {
            this.responseTime = responseTime;
        }
        public List<Message> getMessages() {
            return messages;
        }
        public void setMessages(List<Message> messages) {
            this.messages = messages;
        }
    }

    // Classe Message
    public static class Message {
        private int id;
        private String code;
        private int messageType;
        private String message;

        public Message() {
        }

        public int getId() {
            return id;
        }
        public void setId(int id) {
            this.id = id;
        }
        public String getCode() {
            return code;
        }
        public void setCode(String code) {
            this.code = code;
        }
        public int getMessageType() {
            return messageType;
        }
        public void setMessageType(int messageType) {
            this.messageType = messageType;
        }
        public String getMessage() {
            return message;
        }
        public void setMessage(String message) {
            this.message = message;
        }
    }

    // Classe Body
    public static class Body {
        private String searchId;
        private String expiresOn;
        private List<Flight> flights;

        public Body() {
        }

        public String getSearchId() {
            return searchId;
        }
        public void setSearchId(String searchId) {
            this.searchId = searchId;
        }
        public String getExpiresOn() {
            return expiresOn;
        }
        public void setExpiresOn(String expiresOn) {
            this.expiresOn = expiresOn;
        }
        public List<Flight> getFlights() {
            return flights;
        }
        public void setFlights(List<Flight> flights) {
            this.flights = flights;
        }
    }

    // Classe Flight (correspond aux éléments de "flights")
    public static class Flight {
        private int provider;
        private String id;
        private List<FlightItem> items;
        private List<Offer> offers;
        private List<String> groupKeys;

        public Flight() {
        }

        public int getProvider() {
            return provider;
        }
        public void setProvider(int provider) {
            this.provider = provider;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public List<FlightItem> getItems() {
            return items;
        }
        public void setItems(List<FlightItem> items) {
            this.items = items;
        }
        public List<Offer> getOffers() {
            return offers;
        }
        public void setOffers(List<Offer> offers) {
            this.offers = offers;
        }
        public List<String> getGroupKeys() {
            return groupKeys;
        }
        public void setGroupKeys(List<String> groupKeys) {
            this.groupKeys = groupKeys;
        }
    }

    // Classe FlightItem (les éléments contenus dans "items")
    public static class FlightItem {
        private int segmentNumber;
        private String flightNo;
        private String pnlName;
        private String flightDate;
        private Airline airline;
        private Airline marketingAirline;
        private int duration;
        private int dayChange;
        private Location departure;
        private Location arrival;
        private FlightClass flightClass;
        private int route;
        private List<Segment> segments;
        private int stopCount;
        private FlightProvider flightProvider;
        private List<BaggageInformation> baggageInformations;
        private List<Passenger> passengers;
        private int flightType;

        public FlightItem() {
        }

        public int getSegmentNumber() {
            return segmentNumber;
        }
        public void setSegmentNumber(int segmentNumber) {
            this.segmentNumber = segmentNumber;
        }
        public String getFlightNo() {
            return flightNo;
        }
        public void setFlightNo(String flightNo) {
            this.flightNo = flightNo;
        }
        public String getPnlName() {
            return pnlName;
        }
        public void setPnlName(String pnlName) {
            this.pnlName = pnlName;
        }
        public String getFlightDate() {
            return flightDate;
        }
        public void setFlightDate(String flightDate) {
            this.flightDate = flightDate;
        }
        public Airline getAirline() {
            return airline;
        }
        public void setAirline(Airline airline) {
            this.airline = airline;
        }
        public Airline getMarketingAirline() {
            return marketingAirline;
        }
        public void setMarketingAirline(Airline marketingAirline) {
            this.marketingAirline = marketingAirline;
        }
        public int getDuration() {
            return duration;
        }
        public void setDuration(int duration) {
            this.duration = duration;
        }
        public int getDayChange() {
            return dayChange;
        }
        public void setDayChange(int dayChange) {
            this.dayChange = dayChange;
        }
        public Location getDeparture() {
            return departure;
        }
        public void setDeparture(Location departure) {
            this.departure = departure;
        }
        public Location getArrival() {
            return arrival;
        }
        public void setArrival(Location arrival) {
            this.arrival = arrival;
        }
        public FlightClass getFlightClass() {
            return flightClass;
        }
        public void setFlightClass(FlightClass flightClass) {
            this.flightClass = flightClass;
        }
        public int getRoute() {
            return route;
        }
        public void setRoute(int route) {
            this.route = route;
        }
        public List<Segment> getSegments() {
            return segments;
        }
        public void setSegments(List<Segment> segments) {
            this.segments = segments;
        }
        public int getStopCount() {
            return stopCount;
        }
        public void setStopCount(int stopCount) {
            this.stopCount = stopCount;
        }
        public FlightProvider getFlightProvider() {
            return flightProvider;
        }
        public void setFlightProvider(FlightProvider flightProvider) {
            this.flightProvider = flightProvider;
        }
        public List<BaggageInformation> getBaggageInformations() {
            return baggageInformations;
        }
        public void setBaggageInformations(List<BaggageInformation> baggageInformations) {
            this.baggageInformations = baggageInformations;
        }
        public List<Passenger> getPassengers() {
            return passengers;
        }
        public void setPassengers(List<Passenger> passengers) {
            this.passengers = passengers;
        }
        public int getFlightType() {
            return flightType;
        }
        public void setFlightType(int flightType) {
            this.flightType = flightType;
        }
    }

    // Classe Airline
    public static class Airline {
        private String internationalCode;
        private String thumbnail;
        private String thumbnailFull;
        private String logo;
        private String logoFull;
        private String id;
        private String name;

        public Airline() {
        }

        public String getInternationalCode() {
            return internationalCode;
        }
        public void setInternationalCode(String internationalCode) {
            this.internationalCode = internationalCode;
        }
        public String getThumbnail() {
            return thumbnail;
        }
        public void setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
        }
        public String getThumbnailFull() {
            return thumbnailFull;
        }
        public void setThumbnailFull(String thumbnailFull) {
            this.thumbnailFull = thumbnailFull;
        }
        public String getLogo() {
            return logo;
        }
        public void setLogo(String logo) {
            this.logo = logo;
        }
        public String getLogoFull() {
            return logoFull;
        }
        public void setLogoFull(String logoFull) {
            this.logoFull = logoFull;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
    }

    // Classe FlightClass
    public static class FlightClass {
        private int type;
        private String name;
        private String id;
        private String code;

        public FlightClass() {
        }

        public int getType() {
            return type;
        }
        public void setType(int type) {
            this.type = type;
        }
        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getCode() {
            return code;
        }
        public void setCode(String code) {
            this.code = code;
        }
    }

    // Classe Location (utilisée pour departure et arrival)
    public static class Location {
        private Country country;
        private City city;
        private Airport airport;
        private String date;
        private GeoLocation geoLocation;

        public Location() {
        }

        public Country getCountry() {
            return country;
        }
        public void setCountry(Country country) {
            this.country = country;
        }
        public City getCity() {
            return city;
        }
        public void setCity(City city) {
            this.city = city;
        }
        public Airport getAirport() {
            return airport;
        }
        public void setAirport(Airport airport) {
            this.airport = airport;
        }
        public String getDate() {
            return date;
        }
        public void setDate(String date) {
            this.date = date;
        }
        public GeoLocation getGeoLocation() {
            return geoLocation;
        }
        public void setGeoLocation(GeoLocation geoLocation) {
            this.geoLocation = geoLocation;
        }
    }

    // Classe Country
    public static class Country {
        private String name;
        private int provider;
        private boolean isTopRegion;
        private String id;

        public Country() {
        }

        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
        public int getProvider() {
            return provider;
        }
        public void setProvider(int provider) {
            this.provider = provider;
        }
        public boolean isTopRegion() {
            return isTopRegion;
        }
        public void setTopRegion(boolean isTopRegion) {
            this.isTopRegion = isTopRegion;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }

    // Classe City
    public static class City {
        private String name;
        private int provider;
        private boolean isTopRegion;
        private String id;

        public City() {
        }

        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
        public int getProvider() {
            return provider;
        }
        public void setProvider(int provider) {
            this.provider = provider;
        }
        public boolean isTopRegion() {
            return isTopRegion;
        }
        public void setTopRegion(boolean isTopRegion) {
            this.isTopRegion = isTopRegion;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }

    // Classe Airport
    public static class Airport {
        private GeoLocation geolocation;
        private String name;
        private String id;
        private String code;

        public Airport() {
        }

        public GeoLocation getGeolocation() {
            return geolocation;
        }
        public void setGeolocation(GeoLocation geolocation) {
            this.geolocation = geolocation;
        }
        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getCode() {
            return code;
        }
        public void setCode(String code) {
            this.code = code;
        }
    }

    // Classe GeoLocation
    public static class GeoLocation {
        private String longitude;
        private String latitude;

        public GeoLocation() {
        }

        public String getLongitude() {
            return longitude;
        }
        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }
        public String getLatitude() {
            return latitude;
        }
        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }
    }

    // Classe Segment
    public static class Segment {
        private String id;
        private String flightNo;
        private String pnlName;
        private FlightClass flightClass;
        private Airline airline;
        private Airline marketingAirline;
        private Location departure;
        private Location arrival;
        private int duration;
        private List<BaggageInformation> baggageInformations;
        private List<Service> services;
        private String aircraft;

        public Segment() {
        }

        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getFlightNo() {
            return flightNo;
        }
        public void setFlightNo(String flightNo) {
            this.flightNo = flightNo;
        }
        public String getPnlName() {
            return pnlName;
        }
        public void setPnlName(String pnlName) {
            this.pnlName = pnlName;
        }
        public FlightClass getFlightClass() {
            return flightClass;
        }
        public void setFlightClass(FlightClass flightClass) {
            this.flightClass = flightClass;
        }
        public Airline getAirline() {
            return airline;
        }
        public void setAirline(Airline airline) {
            this.airline = airline;
        }
        public Airline getMarketingAirline() {
            return marketingAirline;
        }
        public void setMarketingAirline(Airline marketingAirline) {
            this.marketingAirline = marketingAirline;
        }
        public Location getDeparture() {
            return departure;
        }
        public void setDeparture(Location departure) {
            this.departure = departure;
        }
        public Location getArrival() {
            return arrival;
        }
        public void setArrival(Location arrival) {
            this.arrival = arrival;
        }
        public int getDuration() {
            return duration;
        }
        public void setDuration(int duration) {
            this.duration = duration;
        }
        public List<BaggageInformation> getBaggageInformations() {
            return baggageInformations;
        }
        public void setBaggageInformations(List<BaggageInformation> baggageInformations) {
            this.baggageInformations = baggageInformations;
        }
        public List<Service> getServices() {
            return services;
        }
        public void setServices(List<Service> services) {
            this.services = services;
        }
        public String getAircraft() {
            return aircraft;
        }
        public void setAircraft(String aircraft) {
            this.aircraft = aircraft;
        }
    }

    // Classe BaggageInformation
    public static class BaggageInformation {
        private String segmentId;
        private int weight;
        private int piece;
        private int baggageType;
        private int unitType;
        private int passengerType;

        public BaggageInformation() {
        }

        public String getSegmentId() {
            return segmentId;
        }
        public void setSegmentId(String segmentId) {
            this.segmentId = segmentId;
        }
        public int getWeight() {
            return weight;
        }
        public void setWeight(int weight) {
            this.weight = weight;
        }
        public int getPiece() {
            return piece;
        }
        public void setPiece(int piece) {
            this.piece = piece;
        }
        public int getBaggageType() {
            return baggageType;
        }
        public void setBaggageType(int baggageType) {
            this.baggageType = baggageType;
        }
        public int getUnitType() {
            return unitType;
        }
        public void setUnitType(int unitType) {
            this.unitType = unitType;
        }
        public int getPassengerType() {
            return passengerType;
        }
        public void setPassengerType(int passengerType) {
            this.passengerType = passengerType;
        }
    }

    // Classe Service
    public static class Service {
        private List<String> segments;
        private String thumbnail;
        private String thumbnailFull;
        private String id;
        private String name;

        public Service() {
        }

        public List<String> getSegments() {
            return segments;
        }
        public void setSegments(List<String> segments) {
            this.segments = segments;
        }
        public String getThumbnail() {
            return thumbnail;
        }
        public void setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
        }
        public String getThumbnailFull() {
            return thumbnailFull;
        }
        public void setThumbnailFull(String thumbnailFull) {
            this.thumbnailFull = thumbnailFull;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
    }

    // Classe FlightProvider
    public static class FlightProvider {
        private String displayName;
        private String id;
        private String name;

        public FlightProvider() {
        }

        public String getDisplayName() {
            return displayName;
        }
        public void setDisplayName(String displayName) {
            this.displayName = displayName;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
    }

    // Classe Passenger
    public static class Passenger {
        private int type;
        private List<Integer> ages;
        private int count;

        public Passenger() {
        }

        public int getType() {
            return type;
        }
        public void setType(int type) {
            this.type = type;
        }
        public List<Integer> getAges() {
            return ages;
        }
        public void setAges(List<Integer> ages) {
            this.ages = ages;
        }
        public int getCount() {
            return count;
        }
        public void setCount(int count) {
            this.count = count;
        }
    }

    // Classe Offer
    public static class Offer {
        private int segmentNumber;
        private Price singleAdultPrice;
        private PriceBreakDown priceBreakDown;
        private Price serviceFee;
        private SeatInfo seatInfo;
        private List<FlightClass> flightClassInformations;
        private List<BaggageInformation> baggageInformations;
        private List<Service> services;
        private ReservableInfo reservableInfo;
        private List<String> groupKeys;
        private Fees fees;
        private boolean isPackageOffer;
        private boolean hasBrand;
        private int route;
        private FlightProvider flightProvider;
        private FlightBrandInfo flightBrandInfo;
        private String expiresOn;
        private String offerId;
        private Price price;
        private int provider;

        public Offer() {
        }

        public int getSegmentNumber() {
            return segmentNumber;
        }
        public void setSegmentNumber(int segmentNumber) {
            this.segmentNumber = segmentNumber;
        }
        public Price getSingleAdultPrice() {
            return singleAdultPrice;
        }
        public void setSingleAdultPrice(Price singleAdultPrice) {
            this.singleAdultPrice = singleAdultPrice;
        }
        public PriceBreakDown getPriceBreakDown() {
            return priceBreakDown;
        }
        public void setPriceBreakDown(PriceBreakDown priceBreakDown) {
            this.priceBreakDown = priceBreakDown;
        }
        public Price getServiceFee() {
            return serviceFee;
        }
        public void setServiceFee(Price serviceFee) {
            this.serviceFee = serviceFee;
        }
        public SeatInfo getSeatInfo() {
            return seatInfo;
        }
        public void setSeatInfo(SeatInfo seatInfo) {
            this.seatInfo = seatInfo;
        }
        public List<FlightClass> getFlightClassInformations() {
            return flightClassInformations;
        }
        public void setFlightClassInformations(List<FlightClass> flightClassInformations) {
            this.flightClassInformations = flightClassInformations;
        }
        public List<BaggageInformation> getBaggageInformations() {
            return baggageInformations;
        }
        public void setBaggageInformations(List<BaggageInformation> baggageInformations) {
            this.baggageInformations = baggageInformations;
        }
        public List<Service> getServices() {
            return services;
        }
        public void setServices(List<Service> services) {
            this.services = services;
        }
        public ReservableInfo getReservableInfo() {
            return reservableInfo;
        }
        public void setReservableInfo(ReservableInfo reservableInfo) {
            this.reservableInfo = reservableInfo;
        }
        public List<String> getGroupKeys() {
            return groupKeys;
        }
        public void setGroupKeys(List<String> groupKeys) {
            this.groupKeys = groupKeys;
        }
        public Fees getFees() {
            return fees;
        }
        public void setFees(Fees fees) {
            this.fees = fees;
        }
        public boolean isPackageOffer() {
            return isPackageOffer;
        }
        public void setPackageOffer(boolean isPackageOffer) {
            this.isPackageOffer = isPackageOffer;
        }
        public boolean isHasBrand() {
            return hasBrand;
        }
        public void setHasBrand(boolean hasBrand) {
            this.hasBrand = hasBrand;
        }
        public int getRoute() {
            return route;
        }
        public void setRoute(int route) {
            this.route = route;
        }
        public FlightProvider getFlightProvider() {
            return flightProvider;
        }
        public void setFlightProvider(FlightProvider flightProvider) {
            this.flightProvider = flightProvider;
        }
        public FlightBrandInfo getFlightBrandInfo() {
            return flightBrandInfo;
        }
        public void setFlightBrandInfo(FlightBrandInfo flightBrandInfo) {
            this.flightBrandInfo = flightBrandInfo;
        }
        public String getExpiresOn() {
            return expiresOn;
        }
        public void setExpiresOn(String expiresOn) {
            this.expiresOn = expiresOn;
        }
        public String getOfferId() {
            return offerId;
        }
        public void setOfferId(String offerId) {
            this.offerId = offerId;
        }
        public Price getPrice() {
            return price;
        }
        public void setPrice(Price price) {
            this.price = price;
        }
        public int getProvider() {
            return provider;
        }
        public void setProvider(int provider) {
            this.provider = provider;
        }
    }

    // Classe Price
    public static class Price {
        private double amount;
        private String currency;

        public Price() {
        }

        public double getAmount() {
            return amount;
        }
        public void setAmount(double amount) {
            this.amount = amount;
        }
        public String getCurrency() {
            return currency;
        }
        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }

    // Classe PriceBreakDown
    public static class PriceBreakDown {
        private List<PriceBreakDownItem> items;

        public PriceBreakDown() {
        }

        public List<PriceBreakDownItem> getItems() {
            return items;
        }
        public void setItems(List<PriceBreakDownItem> items) {
            this.items = items;
        }
    }

    // Classe PriceBreakDownItem
    public static class PriceBreakDownItem {
        private int passengerType;
        private int passengerCount;
        private Price price;
        private Price airportTax;

        public PriceBreakDownItem() {
        }

        public int getPassengerType() {
            return passengerType;
        }
        public void setPassengerType(int passengerType) {
            this.passengerType = passengerType;
        }
        public int getPassengerCount() {
            return passengerCount;
        }
        public void setPassengerCount(int passengerCount) {
            this.passengerCount = passengerCount;
        }
        public Price getPrice() {
            return price;
        }
        public void setPrice(Price price) {
            this.price = price;
        }
        public Price getAirportTax() {
            return airportTax;
        }
        public void setAirportTax(Price airportTax) {
            this.airportTax = airportTax;
        }
    }

    // Classe SeatInfo
    public static class SeatInfo {
        private int availableSeatCount;
        private int availableSeatCountType;

        public SeatInfo() {
        }

        public int getAvailableSeatCount() {
            return availableSeatCount;
        }
        public void setAvailableSeatCount(int availableSeatCount) {
            this.availableSeatCount = availableSeatCount;
        }
        public int getAvailableSeatCountType() {
            return availableSeatCountType;
        }
        public void setAvailableSeatCountType(int availableSeatCountType) {
            this.availableSeatCountType = availableSeatCountType;
        }
    }

    // Classe Fees
    public static class Fees {
        private String key;
        private OneWayFees oneWay;

        public Fees() {
        }

        public String getKey() {
            return key;
        }
        public void setKey(String key) {
            this.key = key;
        }
        public OneWayFees getOneWay() {
            return oneWay;
        }
        public void setOneWay(OneWayFees oneWay) {
            this.oneWay = oneWay;
        }
    }

    // Classe OneWayFees
    public static class OneWayFees {
        private List<FeeItem> items;
        private Price price;

        public OneWayFees() {
        }

        public List<FeeItem> getItems() {
            return items;
        }
        public void setItems(List<FeeItem> items) {
            this.items = items;
        }
        public Price getPrice() {
            return price;
        }
        public void setPrice(Price price) {
            this.price = price;
        }
    }

    // Classe FeeItem
    public static class FeeItem {
        private int passengerType;
        private int quantity;
        private double price;
        private double totalPrice;

        public FeeItem() {
        }

        public int getPassengerType() {
            return passengerType;
        }
        public void setPassengerType(int passengerType) {
            this.passengerType = passengerType;
        }
        public int getQuantity() {
            return quantity;
        }
        public void setQuantity(int quantity) {
            this.quantity = quantity;
        }
        public double getPrice() {
            return price;
        }
        public void setPrice(double price) {
            this.price = price;
        }
        public double getTotalPrice() {
            return totalPrice;
        }
        public void setTotalPrice(double totalPrice) {
            this.totalPrice = totalPrice;
        }
    }

    // Classe FlightBrandInfo
    public static class FlightBrandInfo {
        private List<Feature> features;
        private String id;
        private String name;

        public FlightBrandInfo() {
        }

        public List<Feature> getFeatures() {
            return features;
        }
        
        public void setFeatures(List<Feature> features) {
            this.features = features;
        }
        
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
    }

    // Classe Feature (dans FlightBrandInfo)
    public static class Feature {
        private String commercialName;
        private int serviceGroup;
        private int pricingType;
        private List<Explanation> explanations;

        public Feature() {
        }

        public String getCommercialName() {
            return commercialName;
        }
        
        public void setCommercialName(String commercialName) {
            this.commercialName = commercialName;
        }
        
        public int getServiceGroup() {
            return serviceGroup;
        }
        
        public void setServiceGroup(int serviceGroup) {
            this.serviceGroup = serviceGroup;
        }
        
        public int getPricingType() {
            return pricingType;
        }
        
        public void setPricingType(int pricingType) {
            this.pricingType = pricingType;
        }
        
        public List<Explanation> getExplanations() {
            return explanations;
        }
        
        public void setExplanations(List<Explanation> explanations) {
            this.explanations = explanations;
        }
    }

    // Classe Explanation
    public static class Explanation {
        private String text;

        public Explanation() {
        }

        public String getText() {
            return text;
        }
        
        public void setText(String text) {
            this.text = text;
        }
    }

    // Classe ReservableInfo
    public static class ReservableInfo {
        private boolean reservable;

        public ReservableInfo() {
        }

        public boolean isReservable() {
            return reservable;
        }
        public void setReservable(boolean reservable) {
            this.reservable = reservable;
        }
    }
}
