// Modèle pour la réponse GetOffers
export interface GetOffersResponse {
    header: Header;
    body: Body;
}

export interface Header {
    requestId: string;
    success: boolean;
    responseTime: string;
    messages: Message[];
}

export interface Message {
    id: number;
    code: string;
    messageType: number;
    message: string;
}

export interface Body {
    offers: Offer[];
}

export interface Offer {
    flightId: string;
    segmentNumber: number;
    flightClassInformations: FlightClassInformation[];
    baggageInformations: BaggageInformation[];
    groupKeys: string[];
    offerIds: OfferId[];
    isPackageOffer?: boolean;
    packageOffer?: boolean;
    route: number;
    flightBrandInfo: FlightBrandInfo;
    offerId: string;
    price: Price;
    provider: number;
}

export interface FlightClassInformation {
    type: number;
    segmentId: string;
    name: string;
    id: string;
    code: string;
}

export interface BaggageInformation {
    segmentId: string;
    weight: number;
    piece: number;
    baggageType: number;
    unitType: number;
    passengerType: number;
}

export interface OfferId {
    groupKey: string;
    offerId: string;
}

export interface FlightBrandInfo {
    features: Feature[];
    id: string;
    name: string;
}

export interface Feature {
    commercialName: string;
    serviceGroup: number;
    pricingType: number;
    explanations: Explanation[];
}

export interface Explanation {
    text: string;
}

export interface Price {
    amount: number;
    currency: string;
}
