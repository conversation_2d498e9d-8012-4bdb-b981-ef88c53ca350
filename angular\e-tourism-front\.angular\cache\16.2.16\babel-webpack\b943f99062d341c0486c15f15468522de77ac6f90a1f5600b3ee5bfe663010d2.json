{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/product.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/autocomplete\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/datepicker\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nfunction SearchPriceComponent_mat_option_33_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", location_r11.city, \" \");\n  }\n}\nfunction SearchPriceComponent_mat_option_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, SearchPriceComponent_mat_option_33_span_2_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r11);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", location_r11.name, \" \", location_r11.code ? \"(\" + location_r11.code + \")\" : \"\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r11.type === 5 && location_r11.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_56_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", location_r14.city, \" \");\n  }\n}\nfunction SearchPriceComponent_mat_option_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, SearchPriceComponent_mat_option_56_span_2_Template, 2, 1, \"span\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const location_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", location_r14);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", location_r14.name, \" \", location_r14.code ? \"(\" + location_r14.code + \")\" : \"\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", location_r14.type === 5 && location_r14.city);\n  }\n}\nfunction SearchPriceComponent_mat_option_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r17.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r17.label, \" \");\n  }\n}\nfunction SearchPriceComponent_mat_option_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", flightClass_r18.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flightClass_r18.label, \" \");\n  }\n}\nfunction SearchPriceComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"mat-spinner\", 57);\n    i0.ɵɵelementStart(2, \"p\", 58);\n    i0.ɵɵtext(3, \"Recherche des meilleurs vols...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵelement(2, \"i\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.errorMessage);\n  }\n}\nfunction SearchPriceComponent_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"i\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Aucun vol trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Essayez de modifier vos crit\\u00E8res de recherche\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r20 = i0.ɵɵnextContext().$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.getStopsCount(flight_r20), \" escale(s) \");\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"i\", 67);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Vol direct\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r20.offers[0] == null ? null : flight_r20.offers[0].flightBrandInfo == null ? null : flight_r20.offers[0].flightBrandInfo.name);\n  }\n}\nfunction SearchPriceComponent_div_102_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"div\", 72)(3, \"div\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 74)(6, \"span\", 75);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 76);\n    i0.ɵɵelementStart(9, \"span\", 77);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 78)(12, \"div\", 79);\n    i0.ɵɵtext(13, \"\\u00E0 partir de\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 80);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 81)(17, \"div\", 82)(18, \"div\", 83)(19, \"div\", 84)(20, \"div\", 85);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 86);\n    i0.ɵɵelement(23, \"i\", 87);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 88);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 89);\n    i0.ɵɵelement(28, \"div\", 90);\n    i0.ɵɵelementStart(29, \"div\", 91);\n    i0.ɵɵtemplate(30, SearchPriceComponent_div_102_div_5_div_30_Template, 2, 1, \"div\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"div\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 93)(33, \"div\", 94);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 95);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 96)(38, \"div\", 97);\n    i0.ɵɵelement(39, \"i\", 98);\n    i0.ɵɵelementStart(40, \"span\");\n    i0.ɵɵtext(41, \"Bagages inclus\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(42, SearchPriceComponent_div_102_div_5_div_42_Template, 4, 0, \"div\", 99);\n    i0.ɵɵtemplate(43, SearchPriceComponent_div_102_div_5_div_43_Template, 4, 1, \"div\", 99);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 100)(45, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_102_div_5_Template_button_click_45_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const flight_r20 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.showFlightDetails(flight_r20));\n    });\n    i0.ɵɵelement(46, \"i\", 102);\n    i0.ɵɵtext(47, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function SearchPriceComponent_div_102_div_5_Template_button_click_48_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const flight_r20 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.selectThisFlight(flight_r20));\n    });\n    i0.ɵɵelement(49, \"i\", 104);\n    i0.ɵɵtext(50, \" S\\u00E9lectionner \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const flight_r20 = ctx.$implicit;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(flight_r20.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r19.getDepartureCity(flight_r20));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r19.getArrivalCity(flight_r20));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r19.getMinPrice(flight_r20));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.getDepartureTime(flight_r20), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.formatDuration(ctx_r19.getFlightDuration(flight_r20)), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.getArrivalTime(flight_r20), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"has-stops\", ctx_r19.hasStops(flight_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.hasStops(flight_r20));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.getDepartureAirportCode(flight_r20), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.getArrivalAirportCode(flight_r20), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", (flight_r20.items[0] == null ? null : flight_r20.items[0].segments.length) === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r20.offers[0] == null ? null : flight_r20.offers[0].flightBrandInfo == null ? null : flight_r20.offers[0].flightBrandInfo.name);\n  }\n}\nfunction SearchPriceComponent_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"h3\", 66);\n    i0.ɵɵelement(2, \"i\", 67);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 68);\n    i0.ɵɵtemplate(5, SearchPriceComponent_div_102_div_5_Template, 51, 14, \"div\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Vols disponibles (\", ctx_r10.searchResults.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.searchResults);\n  }\n}\nexport class SearchPriceComponent {\n  constructor(fb, productService, router) {\n    this.fb = fb;\n    this.productService = productService;\n    this.router = router;\n    this.departureLocations = [];\n    this.arrivalLocations = [];\n    this.isLoading = false;\n    this.searchResults = [];\n    this.hasSearched = false;\n    this.errorMessage = '';\n    this.lastSearchId = '';\n    // Passenger type options\n    this.passengerTypes = [{\n      value: PassengerType.Adult,\n      label: 'Adult'\n    }, {\n      value: PassengerType.Child,\n      label: 'Child'\n    }, {\n      value: PassengerType.Infant,\n      label: 'Infant'\n    }];\n    // Flight class options\n    this.flightClasses = [{\n      value: FlightClassType.PROMO,\n      label: 'Promo'\n    }, {\n      value: FlightClassType.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClassType.BUSINESS,\n      label: 'Business'\n    }];\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required],\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required],\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required],\n      // Options de vol\n      flightClass: [0, Validators.required],\n      nonStop: [false],\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n  ngOnInit() {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n  // Méthode pour afficher les détails du vol\n  showFlightDetails(flight) {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n    header.appendChild(logo);\n    header.appendChild(title);\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n        generalInfo.appendChild(airlineInfo);\n      }\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n      routeSection.appendChild(routeVisual);\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n          segmentsList.appendChild(segmentItem);\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n            segmentsList.appendChild(layover);\n          }\n        });\n        routeSection.appendChild(segmentsList);\n      }\n    }\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n        // ID de l'offre\n        const offerId = this.createInfoRow('Offer ID', offer.offerId || offer.id || 'N/A');\n        offerId.style.gridColumn = '1 / -1';\n        offerDetails.appendChild(offerId);\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n        offerItem.appendChild(offerDetails);\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n          const baggageList = document.createElement('ul');\n          baggageList.style.listStyle = 'none';\n          baggageList.style.padding = '0';\n          baggageList.style.margin = '0';\n          offer.baggageInformations.forEach(baggage => {\n            const baggageItem = document.createElement('li');\n            baggageItem.style.marginBottom = '5px';\n            baggageItem.innerHTML = `<i class=\"fas fa-suitcase\" style=\"color: #666; margin-right: 8px;\"></i> ${this.getBaggageTypeName(baggage.baggageType)}`;\n            baggageList.appendChild(baggageItem);\n          });\n          offerItem.appendChild(baggageList);\n        }\n        offersList.appendChild(offerItem);\n      });\n      offersSection.appendChild(offersList);\n    }\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n  // Méthode utilitaire pour créer une section\n  createSection(title, iconClass) {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n    return section;\n  }\n  // Méthode utilitaire pour créer une ligne d'information\n  createInfoRow(label, value) {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n    return row;\n  }\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight) {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n  preloadLocations() {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n  setupAutocomplete() {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.departureLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('departureLocation')?.setValue('');\n      });\n    });\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.arrivalLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('arrivalLocation')?.setValue('');\n      });\n    });\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  displayLocation(location) {\n    if (!location) return '';\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n  onSearch() {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n    const formValue = this.searchForm.value;\n    // Créer la requête de recherche entièrement dynamique\n    const request = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [{\n        id: formValue.departureLocation?.id || '',\n        type: formValue.departureLocationType\n      }],\n      ArrivalLocations: [{\n        id: formValue.arrivalLocation?.id || '',\n        type: formValue.arrivalLocationType\n      }],\n      Passengers: [{\n        type: formValue.passengerType,\n        count: formValue.passengerCount\n      }],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n    this.productService.searchPrice(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.header.success) {\n          this.searchResults = response.body.flights;\n          // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n          console.log('Full API Response:', JSON.stringify(response, null, 2));\n          // Analyser les données de disponibilité\n          if (response.body && response.body.flights && response.body.flights.length > 0) {\n            console.group('Availability Analysis');\n            console.log('Total flights:', response.body.flights.length);\n            // Compter les vols avec des offres\n            const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n            console.log('Flights with offers:', flightsWithOffers.length);\n            // Analyser les valeurs de disponibilité\n            const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability !== undefined ? o.availability : o.seatInfo ? o.seatInfo.availableSeatCount : 0));\n            console.log('Availability values:', availabilityValues);\n            // Compter les différentes valeurs de disponibilité\n            const availabilityCounts = availabilityValues.reduce((acc, val) => {\n              if (val !== undefined) {\n                acc[val] = (acc[val] || 0) + 1;\n              }\n              return acc;\n            }, {});\n            console.log('Availability counts:', availabilityCounts);\n            // Vérifier les vols réservables\n            const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true));\n            console.log('Reservable flights:', reservableFlights.length);\n            console.groupEnd();\n          }\n          // Vérifier si searchId existe dans le corps de la réponse\n          if (response.body && response.body.searchId) {\n            this.lastSearchId = response.body.searchId;\n            console.log('Search ID found in body.searchId:', this.lastSearchId);\n          }\n          // Vérifier si searchId existe dans l'en-tête de la réponse\n          else if (response.header && response.header.requestId) {\n            this.lastSearchId = response.header.requestId;\n            console.log('Search ID found in header.requestId:', this.lastSearchId);\n          }\n          // Vérifier d'autres emplacements possibles\n          else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n            this.lastSearchId = response.body.flights[0].id;\n            console.log('Using flight ID as search ID:', this.lastSearchId);\n          } else {\n            console.error('No search ID found in the response!');\n            console.log('Response structure:', Object.keys(response));\n            if (response.body) console.log('Body structure:', Object.keys(response.body));\n            if (response.header) console.log('Header structure:', Object.keys(response.header));\n          }\n        } else {\n          this.errorMessage = 'The search could not be completed. Please try again.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred during the search. Please try again.';\n        console.error('Error searching flights:', error);\n      }\n    });\n  }\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  // Formater la durée en heures et minutes\n  formatDuration(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n  // Formater la date pour l'affichage\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n    const minOffer = flight.offers.reduce((min, offer) => offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n  // Méthodes d'assistance pour l'affichage sécurisé des données de vol\n  getDepartureCity(flight) {\n    if (!flight?.items?.[0]?.segments?.[0]?.departure?.city?.name) {\n      return 'Départ';\n    }\n    return flight.items[0].segments[0].departure.city.name;\n  }\n  getArrivalCity(flight) {\n    if (!flight?.items?.[0]?.segments || flight.items[0].segments.length === 0) {\n      return 'Arrivée';\n    }\n    const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n    return lastSegment?.arrival?.city?.name || 'Arrivée';\n  }\n  getDepartureTime(flight) {\n    if (!flight?.items?.[0]?.segments?.[0]?.departure?.date) {\n      return '--:--';\n    }\n    const date = new Date(flight.items[0].segments[0].departure.date);\n    return date.toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getArrivalTime(flight) {\n    if (!flight?.items?.[0]?.segments || flight.items[0].segments.length === 0) {\n      return '--:--';\n    }\n    const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n    if (!lastSegment?.arrival?.date) {\n      return '--:--';\n    }\n    const date = new Date(lastSegment.arrival.date);\n    return date.toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getFlightDuration(flight) {\n    return flight?.items?.[0]?.duration || 0;\n  }\n  hasStops(flight) {\n    return flight?.items?.[0]?.segments && flight.items[0].segments.length > 1;\n  }\n  getStopsCount(flight) {\n    if (!flight?.items?.[0]?.segments) {\n      return 0;\n    }\n    return flight.items[0].segments.length - 1;\n  }\n  getDepartureAirportCode(flight) {\n    return flight?.items?.[0]?.segments?.[0]?.departure?.airport?.code || '';\n  }\n  getArrivalAirportCode(flight) {\n    if (!flight?.items?.[0]?.segments || flight.items[0].segments.length === 0) {\n      return '';\n    }\n    const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n    return lastSegment?.arrival?.airport?.code || '';\n  }\n  isDirectFlight(flight) {\n    return flight?.items?.[0]?.segments && flight.items[0].segments.length === 1;\n  }\n  hasBrandInfo(flight) {\n    return !!flight?.offers?.[0]?.flightBrandInfo?.name;\n  }\n  getBrandName(flight) {\n    return flight?.offers?.[0]?.flightBrandInfo?.name || '';\n  }\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight) {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    return availabilityValue > 0;\n  }\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations() {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations() {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations() {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString) {\n    if (!dateString) return 'N/A';\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType) {\n    switch (baggageType) {\n      case 1:\n        return 'Cabin Baggage';\n      case 2:\n        return 'Checked Baggage';\n      case 3:\n        return 'Hand Baggage';\n      default:\n        return 'Baggage';\n    }\n  }\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType) {\n    switch (passengerType) {\n      case 1:\n        return 'Adult';\n      case 2:\n        return 'Child';\n      case 3:\n        return 'Infant';\n      default:\n        return 'Passenger';\n    }\n  }\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment, nextSegment) {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date || !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n  static {\n    this.ɵfac = function SearchPriceComponent_Factory(t) {\n      return new (t || SearchPriceComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchPriceComponent,\n      selectors: [[\"app-search-price\"]],\n      decls: 103,\n      vars: 23,\n      consts: [[1, \"search-price-container\"], [1, \"search-layout\"], [1, \"search-card-container\"], [1, \"search-card\"], [1, \"search-card-header\"], [1, \"search-card-title\"], [1, \"fas\", \"fa-search\"], [1, \"search-card-subtitle\"], [1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"location-container\"], [1, \"form-group\", \"location-group\"], [\"for\", \"departureLocation\"], [1, \"location-input-container\"], [1, \"location-type-selector\"], [\"appearance\", \"outline\"], [\"formControlName\", \"departureLocationType\"], [3, \"value\"], [1, \"location-autocomplete\"], [\"type\", \"text\", \"id\", \"departureLocation\", \"matInput\", \"\", \"formControlName\", \"departureLocation\", 3, \"matAutocomplete\", \"click\"], [\"matPrefix\", \"\"], [3, \"displayWith\"], [\"departureAuto\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"swap-button\", 3, \"click\"], [1, \"fas\", \"fa-exchange-alt\"], [\"for\", \"arrivalLocation\"], [\"formControlName\", \"arrivalLocationType\"], [\"type\", \"text\", \"id\", \"arrivalLocation\", \"matInput\", \"\", \"formControlName\", \"arrivalLocation\", 3, \"matAutocomplete\", \"click\"], [\"arrivalAuto\", \"matAutocomplete\"], [1, \"date-passengers-container\"], [1, \"form-group\", \"date-group\"], [\"for\", \"departureDate\"], [\"matInput\", \"\", \"formControlName\", \"departureDate\", \"id\", \"departureDate\", 3, \"min\", \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"picker\", \"\"], [1, \"form-group\", \"passenger-group\"], [\"for\", \"passengerCount\"], [1, \"passenger-inputs\"], [\"formControlName\", \"passengerType\", \"id\", \"passengerType\"], [\"matInput\", \"\", \"type\", \"number\", \"min\", \"1\", \"max\", \"9\", \"formControlName\", \"passengerCount\", \"id\", \"passengerCount\"], [1, \"flight-options-container\"], [1, \"form-group\", \"class-group\"], [\"for\", \"flightClass\"], [\"formControlName\", \"flightClass\", \"id\", \"flightClass\"], [1, \"form-group\", \"nonstop-group\"], [1, \"checkbox-container\"], [\"formControlName\", \"nonStop\", \"id\", \"nonStop\", \"color\", \"primary\"], [1, \"search-button-container\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [1, \"search-results-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"no-results-container\", 4, \"ngIf\"], [\"class\", \"results-list\", 4, \"ngIf\"], [\"class\", \"location-city\", 4, \"ngIf\"], [1, \"location-city\"], [1, \"loading-container\"], [\"diameter\", \"50\", \"color\", \"accent\"], [1, \"loading-text\"], [1, \"error-container\"], [1, \"error-icon\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"error-message\"], [1, \"no-results-container\"], [1, \"no-results-icon\"], [1, \"results-list\"], [1, \"results-title\"], [1, \"fas\", \"fa-plane\"], [1, \"flight-cards\"], [\"class\", \"flight-card animate-fade-in\", 4, \"ngFor\", \"ngForOf\"], [1, \"flight-card\", \"animate-fade-in\"], [1, \"flight-card-header\"], [1, \"flight-info\"], [1, \"flight-id\"], [1, \"flight-route\"], [1, \"departure\"], [1, \"fas\", \"fa-long-arrow-alt-right\"], [1, \"arrival\"], [1, \"flight-price\"], [1, \"price-label\"], [1, \"price-amount\"], [1, \"flight-card-content\"], [1, \"flight-details\"], [1, \"segment-info\"], [1, \"time-info\"], [1, \"departure-time\"], [1, \"flight-duration\"], [1, \"fas\", \"fa-clock\"], [1, \"arrival-time\"], [1, \"route-line\"], [1, \"route-point\"], [1, \"route-path\"], [\"class\", \"stops-indicator\", 4, \"ngIf\"], [1, \"location-info\"], [1, \"departure-location\"], [1, \"arrival-location\"], [1, \"flight-features\"], [1, \"feature\"], [1, \"fas\", \"fa-suitcase\"], [\"class\", \"feature\", 4, \"ngIf\"], [1, \"flight-actions\"], [1, \"view-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [1, \"select-flight-button\", 3, \"click\"], [1, \"fas\", \"fa-check-circle\"], [1, \"stops-indicator\"], [1, \"fas\", \"fa-tag\"]],\n      template: function SearchPriceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementStart(7, \"h2\");\n          i0.ɵɵtext(8, \"Rechercher un vol\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7);\n          i0.ɵɵtext(10, \" Trouvez les meilleurs tarifs pour votre prochain voyage \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function SearchPriceComponent_Template_form_ngSubmit_11_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"label\", 11);\n          i0.ɵɵtext(15, \"D\\u00E9part\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 12)(17, \"div\", 13)(18, \"mat-form-field\", 14)(19, \"mat-select\", 15)(20, \"mat-option\", 16);\n          i0.ɵɵtext(21, \"Ville\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-option\", 16);\n          i0.ɵɵtext(23, \"A\\u00E9roport\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(24, \"div\", 17)(25, \"mat-form-field\", 14)(26, \"mat-label\");\n          i0.ɵɵtext(27, \"Ville ou a\\u00E9roport de d\\u00E9part\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"input\", 18);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_28_listener() {\n            return ctx.showAllDepartureLocations();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"mat-icon\", 19);\n          i0.ɵɵtext(30, \"flight_takeoff\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"mat-autocomplete\", 20, 21);\n          i0.ɵɵtemplate(33, SearchPriceComponent_mat_option_33_Template, 3, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(34, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_button_click_34_listener() {\n            return ctx.swapLocations();\n          });\n          i0.ɵɵelement(35, \"i\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 10)(37, \"label\", 25);\n          i0.ɵɵtext(38, \"Arriv\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 12)(40, \"div\", 13)(41, \"mat-form-field\", 14)(42, \"mat-select\", 26)(43, \"mat-option\", 16);\n          i0.ɵɵtext(44, \"Ville\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"mat-option\", 16);\n          i0.ɵɵtext(46, \"A\\u00E9roport\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"div\", 17)(48, \"mat-form-field\", 14)(49, \"mat-label\");\n          i0.ɵɵtext(50, \"Ville ou a\\u00E9roport d'arriv\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"input\", 27);\n          i0.ɵɵlistener(\"click\", function SearchPriceComponent_Template_input_click_51_listener() {\n            return ctx.showAllArrivalLocations();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"mat-icon\", 19);\n          i0.ɵɵtext(53, \"flight_land\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-autocomplete\", 20, 28);\n          i0.ɵɵtemplate(56, SearchPriceComponent_mat_option_56_Template, 3, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(57, \"div\", 29)(58, \"div\", 30)(59, \"label\", 31);\n          i0.ɵɵtext(60, \"Date de d\\u00E9part\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 14)(62, \"mat-label\");\n          i0.ɵɵtext(63, \"Choisir une date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 32)(65, \"mat-datepicker-toggle\", 33)(66, \"mat-datepicker\", null, 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 35)(69, \"label\", 36);\n          i0.ɵɵtext(70, \"Passagers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 37)(72, \"mat-form-field\", 14)(73, \"mat-label\");\n          i0.ɵɵtext(74, \"Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"mat-select\", 38);\n          i0.ɵɵtemplate(76, SearchPriceComponent_mat_option_76_Template, 2, 2, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"mat-form-field\", 14)(78, \"mat-label\");\n          i0.ɵɵtext(79, \"Nombre\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(80, \"input\", 39);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(81, \"div\", 40)(82, \"div\", 41)(83, \"label\", 42);\n          i0.ɵɵtext(84, \"Classe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"mat-form-field\", 14)(86, \"mat-label\");\n          i0.ɵɵtext(87, \"Classe de vol\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"mat-select\", 43);\n          i0.ɵɵtemplate(89, SearchPriceComponent_mat_option_89_Template, 2, 2, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"div\", 44)(91, \"div\", 45)(92, \"mat-checkbox\", 46);\n          i0.ɵɵtext(93, \" Vol direct uniquement \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(94, \"div\", 47)(95, \"button\", 48);\n          i0.ɵɵelement(96, \"i\", 6);\n          i0.ɵɵtext(97, \" Rechercher \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(98, \"div\", 49);\n          i0.ɵɵtemplate(99, SearchPriceComponent_div_99_Template, 4, 0, \"div\", 50);\n          i0.ɵɵtemplate(100, SearchPriceComponent_div_100_Template, 5, 1, \"div\", 51);\n          i0.ɵɵtemplate(101, SearchPriceComponent_div_101_Template, 7, 0, \"div\", 52);\n          i0.ɵɵtemplate(102, SearchPriceComponent_div_102_Template, 6, 2, \"div\", 53);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(32);\n          const _r2 = i0.ɵɵreference(55);\n          const _r4 = i0.ɵɵreference(67);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.departureLocations);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matAutocomplete\", _r2);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayLocation);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrivalLocations);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"min\", ctx.minDate)(\"matDatepicker\", _r4);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.passengerTypes);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"has-results\", ctx.hasSearched);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.searchResults.length === 0 && ctx.hasSearched && !ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.searchResults.length > 0);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i5.MatAutocomplete, i6.MatOption, i5.MatAutocompleteTrigger, i7.MatInput, i8.MatFormField, i8.MatLabel, i8.MatPrefix, i8.MatSuffix, i9.MatSelect, i10.MatDatepicker, i10.MatDatepickerInput, i10.MatDatepickerToggle, i11.MatIcon, i12.MatProgressSpinner],\n      styles: [\"/* Styles globaux pour \\u00E9viter le d\\u00E9filement horizontal */\\n* {\\n  box-sizing: border-box;\\n}\\n\\n/* Conteneur principal - \\u00C9vite le d\\u00E9filement horizontal */\\n.search-price-container {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 10px;\\n  width: 100%;\\n  max-width: 100%; /* Utilisation de toute la largeur de l'\\u00E9cran */\\n  margin: 0;\\n  position: relative;\\n  z-index: 1;\\n  box-sizing: border-box;\\n  overflow-x: hidden;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-price-container {\\n    padding: 0;\\n    margin: 0;\\n  }\\n}\\n\\n.search-price-container::before {\\n  content: '';\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background:\\n    linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.03) 0%, rgba(var(--primary-color-rgb), 0) 50%),\\n    radial-gradient(circle at top right, rgba(var(--secondary-color-rgb), 0.03) 0%, rgba(var(--secondary-color-rgb), 0) 70%);\\n  z-index: -1;\\n  pointer-events: none;\\n}\\n\\n/* En-t\\u00EAte de page */\\n.page-header {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  margin-bottom: 30px;\\n  position: relative;\\n  overflow: hidden;\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.page-header::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.1));\\n  z-index: 1;\\n}\\n\\n.header-content {\\n  max-width: 800px;\\n  padding: 40px 20px;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 2;\\n  color: white;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n.page-title {\\n  font-size: 36px;\\n  font-weight: 700;\\n  color: white;\\n  margin-bottom: 15px;\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.page-title::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background: white;\\n  border-radius: 3px;\\n}\\n\\n.page-subtitle {\\n  font-size: 18px;\\n  color: rgba(255, 255, 255, 0.9);\\n  line-height: 1.5;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  margin-top: 20px;\\n}\\n\\n.header-illustration {\\n  width: 100%;\\n  height: 300px;\\n  overflow: hidden;\\n}\\n\\n.header-illustration img {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n  transition: transform 10s ease;\\n}\\n\\n.page-header:hover .header-illustration img {\\n  transform: scale(1.1);\\n}\\n\\n/* Disposition pour les \\u00E9crans larges - Carte de recherche horizontale en haut */\\n@media (min-width: 992px) {\\n  .page-header {\\n    display: none;\\n  }\\n\\n  .search-price-container {\\n    flex-direction: column;\\n    align-items: stretch;\\n    padding: 0;\\n    margin: 0;\\n    width: 100%;\\n  }\\n\\n  .search-content {\\n    display: flex;\\n    flex-direction: column;\\n    width: 100%;\\n    gap: 0;\\n  }\\n\\n  .search-form-container {\\n    position: sticky;\\n    top: 0;\\n    width: 100%;\\n    max-height: none;\\n    overflow: visible;\\n    margin-bottom: 20px;\\n    padding: 0;\\n    border-radius: 0;\\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n    z-index: 100;\\n    background-color: white;\\n  }\\n\\n  .search-results-container {\\n    width: 100%;\\n    margin-left: 0;\\n    padding: 20px;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n  }\\n}\\n\\n/* Formulaire de recherche */\\n.search-form-container {\\n  background-color: white;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  padding: 25px;\\n  margin-bottom: 30px;\\n}\\n\\n/* Logo pour la version desktop */\\n.sidebar-logo {\\n  display: none;\\n}\\n\\n/* Style sp\\u00E9cifique pour les \\u00E9crans larges */\\n@media (min-width: 992px) {\\n  .search-form-container {\\n    padding: 0;\\n    margin-bottom: 20px;\\n    border-radius: 0;\\n  }\\n\\n  .sidebar-logo {\\n    display: block;\\n    background-color: var(--primary-color);\\n    color: white;\\n    padding: 15px 20px;\\n    text-align: left;\\n  }\\n\\n  .logo-container {\\n    display: flex;\\n    align-items: center;\\n    gap: 10px;\\n    max-width: 1200px;\\n    margin: 0 auto;\\n    width: 100%;\\n  }\\n\\n  .logo-icon {\\n    font-size: 22px;\\n  }\\n\\n  .logo-text {\\n    font-size: 22px;\\n    font-weight: 700;\\n    letter-spacing: 0.5px;\\n  }\\n}\\n\\n.search-form-header {\\n  margin-bottom: 25px;\\n  text-align: center;\\n  padding: 20px 20px 0 20px;\\n}\\n\\n.search-form-header h2 {\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n  font-size: 26px;\\n  font-weight: 600;\\n}\\n\\n.search-form-header p {\\n  color: #666;\\n  font-size: 15px;\\n}\\n\\n@media (min-width: 992px) {\\n  .search-form-header {\\n    max-width: 1200px;\\n    margin: 0 auto 15px auto;\\n    text-align: left;\\n    padding: 20px 20px 0 20px;\\n  }\\n}\\n\\n.search-form {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.form-group {\\n  margin-bottom: 15px;\\n}\\n\\n.form-row {\\n  display: flex;\\n  gap: 15px;\\n}\\n\\n.half-width {\\n  flex: 1;\\n}\\n\\nlabel {\\n  display: block;\\n  margin-bottom: 5px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 14px;\\n}\\n\\n.form-control {\\n  width: 100%;\\n  padding: 10px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 5px;\\n  font-size: 14px;\\n  transition: border-color 0.3s;\\n}\\n\\n.form-control:focus {\\n  border-color: #2989d8;\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(41, 137, 216, 0.2);\\n}\\n\\n.checkbox-group {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.checkbox-group input {\\n  margin-right: 8px;\\n}\\n\\n.search-button {\\n  padding: 12px;\\n  background-color: #2989d8;\\n  color: white;\\n  border: none;\\n  border-radius: 5px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.3s;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-top: 10px;\\n}\\n\\n.search-button:hover {\\n  background-color: #1e5799;\\n}\\n\\n.search-button:disabled {\\n  background-color: #b3d4f0;\\n  cursor: not-allowed;\\n}\\n\\n.error-message {\\n  color: #e74c3c;\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n\\n/* Options avanc\\u00E9es */\\ndetails {\\n  margin-top: 10px;\\n  margin-bottom: 15px;\\n}\\n\\nsummary {\\n  cursor: pointer;\\n  color: #2989d8;\\n  font-weight: 500;\\n  padding: 5px 0;\\n}\\n\\nsummary:hover {\\n  text-decoration: underline;\\n}\\n\\n.advanced-options {\\n  margin-top: 10px;\\n  padding: 15px;\\n  background-color: #f8f9fa;\\n  border-radius: 5px;\\n  border: 1px solid #eee;\\n}\\n\\n/* Styles pour les listes d\\u00E9roulantes */\\n::ng-deep .mat-autocomplete-panel {\\n  max-height: 300px !important;\\n}\\n\\n::ng-deep .mat-option {\\n  height: auto !important;\\n  line-height: 1.2 !important;\\n  padding: 10px 16px !important;\\n}\\n\\n::ng-deep .mat-option small {\\n  color: #666;\\n  display: block;\\n  margin-top: 2px;\\n}\\n\\n/* R\\u00E9sultats de recherche - Design optimis\\u00E9 pour \\u00E9viter le d\\u00E9filement horizontal */\\n.search-results-container {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  box-shadow:\\n    0 5px 15px rgba(0, 0, 0, 0.08),\\n    0 0 0 1px rgba(var(--primary-color-rgb), 0.05);\\n  padding: 20px;\\n  position: relative;\\n  overflow-x: hidden;\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  box-sizing: border-box;\\n  width: 100%;\\n}\\n\\n.search-results-container::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 6px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\\n  z-index: 1;\\n}\\n\\n.loading-container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px;\\n  text-align: center;\\n}\\n\\n.loading-container p {\\n  margin-top: 20px;\\n  color: var(--primary-color);\\n  font-weight: 500;\\n  animation: pulse 1.5s infinite;\\n}\\n\\n.spinner {\\n  width: 30px;\\n  height: 30px;\\n  border: 3px solid rgba(var(--primary-color-rgb), 0.2);\\n  border-radius: 50%;\\n  border-top-color: var(--primary-color);\\n  animation: spin 1s cubic-bezier(0.6, 0.2, 0.4, 0.8) infinite;\\n  box-shadow: 0 0 10px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.spinner.large {\\n  width: 50px;\\n  height: 50px;\\n  border-width: 4px;\\n}\\n\\n@keyframes spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n.error-container {\\n  padding: 30px;\\n  background-color: rgba(231, 76, 60, 0.05);\\n  border-radius: 16px;\\n  text-align: center;\\n  border: 1px solid rgba(231, 76, 60, 0.1);\\n  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.05);\\n  animation: scaleIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);\\n}\\n\\n.error-container h4 {\\n  color: #e74c3c;\\n  margin-bottom: 10px;\\n  font-size: 18px;\\n}\\n\\n.error-container p {\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.results-header {\\n  margin-bottom: 30px;\\n  position: relative;\\n  padding-bottom: 15px;\\n}\\n\\n.results-header::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0.1) 0%,\\n    rgba(var(--primary-color-rgb), 0.05) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n/* Provider information */\\n.provider-info {\\n  display: block;\\n  font-size: 12px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-top: 4px;\\n}\\n\\n/* Availability information */\\n.availability {\\n  font-size: 12px;\\n  display: block;\\n  margin-top: 4px;\\n  font-weight: 500;\\n}\\n\\n.availability i.fa-check-circle {\\n  color: #4CAF50;\\n}\\n\\n.availability i.fa-check-circle + span {\\n  color: #4CAF50;\\n}\\n\\n.availability i.fa-exclamation-triangle {\\n  color: #F44336;\\n}\\n\\n.availability i.fa-exclamation-triangle + span {\\n  color: #F44336;\\n}\\n\\n/* Expiration information */\\n.expiration {\\n  color: #FF9800;\\n  font-size: 12px;\\n  display: block;\\n  margin-top: 4px;\\n}\\n\\n/* Branded fare badge */\\n.flight-badge.branded {\\n  background-color: #9C27B0;\\n}\\n\\n/* Feature groups */\\n.feature-group {\\n  margin-bottom: 15px;\\n}\\n\\n.feature-group h4 {\\n  font-size: 14px;\\n  margin-bottom: 8px;\\n  color: rgba(0, 0, 0, 0.7);\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.feature-group .feature {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n  margin-bottom: 4px;\\n  padding-left: 20px;\\n}\\n\\n/* Offer ID */\\n.offer-id {\\n  font-family: monospace;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n}\\n\\n/* Price breakdown section */\\n.price-breakdown-section {\\n  margin: 15px 0;\\n}\\n\\n.price-breakdown-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.price-breakdown-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.price-breakdown-summary:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.price-breakdown-content {\\n  padding: 15px;\\n}\\n\\n.breakdown-group h4 {\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.breakdown-item {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 8px;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.breakdown-item.service-fee {\\n  color: #FF5722;\\n}\\n\\n.breakdown-total {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-top: 10px;\\n  padding-top: 10px;\\n  border-top: 1px dashed rgba(0, 0, 0, 0.1);\\n  font-weight: 600;\\n  color: rgba(0, 0, 0, 0.8);\\n}\\n\\n/* Segments section */\\n.segments-section {\\n  margin: 15px 0;\\n}\\n\\n.segments-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.segments-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(0, 0, 0, 0.02);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.segments-summary:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.segments-content {\\n  padding: 15px;\\n}\\n\\n.segment-item {\\n  margin-bottom: 20px;\\n  padding-bottom: 15px;\\n  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);\\n}\\n\\n.segment-item:last-child {\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n  border-bottom: none;\\n}\\n\\n.segment-header {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 10px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.segment-number {\\n  font-weight: 600;\\n}\\n\\n.segment-route {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 10px;\\n}\\n\\n.layover-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 13px;\\n  color: #FF9800;\\n  margin-top: 10px;\\n  padding: 8px;\\n  background-color: rgba(255, 152, 0, 0.05);\\n  border-radius: 4px;\\n}\\n\\n/* Branded fare section */\\n.branded-fare-section {\\n  margin: 15px 0;\\n}\\n\\n.branded-fare-details {\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.branded-fare-summary {\\n  padding: 12px 15px;\\n  background-color: rgba(156, 39, 176, 0.05);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  color: #9C27B0;\\n}\\n\\n.branded-fare-summary:hover {\\n  background-color: rgba(156, 39, 176, 0.1);\\n}\\n\\n.branded-fare-content {\\n  padding: 15px;\\n}\\n\\n.branded-fare-description {\\n  margin-bottom: 15px;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.branded-fare-features h4 {\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature-item {\\n  margin-bottom: 10px;\\n}\\n\\n.feature-name {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n\\n.feature-description {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.6);\\n}\\n\\n.results-header h3 {\\n  color: var(--primary-dark);\\n  margin-bottom: 8px;\\n  font-size: 24px;\\n  font-weight: 600;\\n}\\n\\n.results-header p {\\n  color: rgba(0, 0, 0, 0.6);\\n  font-size: 14px;\\n}\\n\\n.flight-list {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 25px;\\n}\\n\\n.flight-card {\\n  background-color: white;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  box-shadow:\\n    0 2px 8px rgba(0, 0, 0, 0.05),\\n    0 0 0 1px rgba(0, 0, 0, 0.03);\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.flight-card::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.02) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  pointer-events: none;\\n}\\n\\n.flight-card:hover {\\n  transform: translateY(-8px) scale(1.01);\\n  box-shadow:\\n    0 15px 30px rgba(0, 0, 0, 0.08),\\n    0 0 0 1px rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n.flight-card.unavailable {\\n  opacity: 0.7;\\n  transform: none !important;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03) !important;\\n}\\n\\n.flight-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px;\\n  background-color: #f9f9f9;\\n  border-bottom: 1px solid #eaeaea;\\n  position: relative;\\n  flex-wrap: wrap;\\n}\\n\\n.flight-header::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -1px;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.airline-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.airline-logo {\\n  width: 40px;\\n  height: 40px;\\n  object-fit: contain;\\n  padding: 5px;\\n  background-color: white;\\n  border-radius: 50%;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .airline-logo {\\n  transform: scale(1.1);\\n}\\n\\n.airline-name {\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: var(--primary-dark);\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .airline-name {\\n  color: var(--primary-color);\\n}\\n\\n.flight-price {\\n  text-align: right;\\n  position: relative;\\n}\\n\\n.price {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: var(--primary-color);\\n  display: block;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n\\n.flight-card:hover .price {\\n  color: var(--secondary-color);\\n  transform: scale(1.05);\\n}\\n\\n.price::before {\\n  content: '';\\n  position: absolute;\\n  bottom: -3px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background-color: var(--secondary-color);\\n  transition: width 0.3s ease;\\n}\\n\\n.flight-card:hover .price::before {\\n  width: 100%;\\n}\\n\\n.availability {\\n  font-size: 13px;\\n  color: #e74c3c;\\n  font-weight: 500;\\n  margin-top: 5px;\\n}\\n\\n.flight-details {\\n  padding: 15px;\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.flight-route {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n  position: relative;\\n  width: 100%;\\n}\\n\\n@media (max-width: 768px) {\\n  .flight-route {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n}\\n\\n.departure, .arrival {\\n  flex: 1;\\n  position: relative;\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .departure {\\n  transform: translateX(-5px);\\n}\\n\\n.flight-card:hover .arrival {\\n  transform: translateX(5px);\\n}\\n\\n.time {\\n  font-size: 22px;\\n  font-weight: 700;\\n  margin-bottom: 8px;\\n  color: var(--primary-dark);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.time i {\\n  color: var(--primary-color);\\n  font-size: 18px;\\n  opacity: 0;\\n  transform: translateY(5px);\\n  transition: all 0.3s ease;\\n}\\n\\n.flight-card:hover .departure .time i {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.flight-card:hover .arrival .time i {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.location {\\n  font-size: 15px;\\n  color: rgba(0, 0, 0, 0.6);\\n  font-weight: 500;\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .location {\\n  color: var(--primary-color);\\n}\\n\\n.location small {\\n  display: block;\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.4);\\n  margin-top: 3px;\\n}\\n\\n.flight-duration {\\n  flex: 1;\\n  text-align: center;\\n  padding: 0 20px;\\n  position: relative;\\n  transition: transform 0.3s ease;\\n}\\n\\n.flight-card:hover .flight-duration {\\n  transform: translateY(-5px);\\n}\\n\\n.duration-line {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 10px;\\n  position: relative;\\n}\\n\\n.dot {\\n  width: 10px;\\n  height: 10px;\\n  background-color: var(--primary-color);\\n  border-radius: 50%;\\n  position: relative;\\n  z-index: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 0 0 4px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.flight-card:hover .dot {\\n  background-color: var(--secondary-color);\\n  transform: scale(1.2);\\n  box-shadow: 0 0 0 6px rgba(var(--secondary-color-rgb), 0.15);\\n}\\n\\n.line {\\n  flex: 1;\\n  height: 2px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));\\n  margin: 0 8px;\\n  position: relative;\\n  transition: height 0.3s ease, background 0.3s ease;\\n}\\n\\n.flight-card:hover .line {\\n  height: 3px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\\n}\\n\\n.duration-text {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n  color: var(--primary-dark);\\n  transition: color 0.3s ease;\\n}\\n\\n.flight-card:hover .duration-text {\\n  color: var(--primary-color);\\n}\\n\\n.stops {\\n  font-size: 13px;\\n  color: rgba(0, 0, 0, 0.5);\\n  font-weight: 500;\\n  padding: 4px 12px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  border-radius: 50px;\\n  display: inline-block;\\n  transition: all 0.3s ease;\\n}\\n\\n.flight-card:hover .stops {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n}\\n\\n.flight-info {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 14px;\\n  color: rgba(0, 0, 0, 0.5);\\n  margin-top: 20px;\\n  padding-top: 15px;\\n  border-top: 1px dashed rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.flight-actions {\\n  padding: 20px 25px;\\n  border-top: 1px solid rgba(var(--primary-color-rgb), 0.08);\\n  display: flex;\\n  gap: 15px;\\n  justify-content: flex-end;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.flight-actions::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 1px;\\n  background: linear-gradient(90deg,\\n    rgba(var(--primary-color-rgb), 0) 0%,\\n    rgba(var(--primary-color-rgb), 0.1) 50%,\\n    rgba(var(--primary-color-rgb), 0) 100%);\\n}\\n\\n.view-details-button {\\n  padding: 10px 18px;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  color: var(--primary-dark);\\n  border: 1px solid rgba(var(--primary-color-rgb), 0.1);\\n  border-radius: 50px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.view-details-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.1) 0%, rgba(var(--primary-color-rgb), 0) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.view-details-button i {\\n  font-size: 16px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.view-details-button:hover {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.1);\\n}\\n\\n.view-details-button:hover::before {\\n  opacity: 1;\\n}\\n\\n.view-details-button:hover i {\\n  transform: translateX(3px);\\n}\\n\\n.select-button {\\n  padding: 10px 24px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  color: white;\\n  border: none;\\n  border-radius: 50px;\\n  font-size: 15px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.select-button::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);\\n  z-index: 1;\\n}\\n\\n.select-button::after {\\n  content: '';\\n  position: absolute;\\n  top: -50%;\\n  left: -50%;\\n  width: 200%;\\n  height: 200%;\\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 60%);\\n  opacity: 0;\\n  transform: scale(0.5);\\n  transition: transform 0.8s ease, opacity 0.8s ease;\\n  z-index: 1;\\n}\\n\\n.select-button i {\\n  font-size: 16px;\\n  transition: transform 0.3s ease;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.select-button span {\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.select-button:hover {\\n  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));\\n  transform: translateY(-3px) scale(1.02);\\n  box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n.select-button:hover::after {\\n  opacity: 1;\\n  transform: scale(1);\\n}\\n\\n.select-button:hover i {\\n  transform: translateX(3px);\\n  animation: pulse 1s infinite;\\n}\\n\\n.select-button:active {\\n  transform: translateY(-1px) scale(1);\\n  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n.select-button:disabled {\\n  background: linear-gradient(135deg, #b0b0b0, #d0d0d0);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  opacity: 0.7;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"/* Styles sp\\u00E9cifiques pour les cartes de recherche */\\n\\n/* Styles pour les champs Material */\\n::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {\\n  color: rgba(var(--primary-color-rgb), 0.3);\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {\\n  color: var(--primary-color);\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {\\n  padding: 0.5em 0;\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline .mat-form-field-wrapper {\\n  margin: 0;\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {\\n  top: 1.5em;\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {\\n  transform: translateY(-1.1em) scale(0.75);\\n}\\n\\n::ng-deep .mat-form-field-prefix {\\n  color: var(--primary-color);\\n  opacity: 0.8;\\n  margin-right: 8px;\\n}\\n\\n::ng-deep .mat-form-field-appearance-outline .mat-select-arrow-wrapper {\\n  transform: translateY(0);\\n}\\n\\n/* Styles pour l'autocompl\\u00E9tion */\\n::ng-deep .mat-autocomplete-panel {\\n  border-radius: var(--border-radius-medium);\\n  box-shadow: var(--elevation-3);\\n}\\n\\n::ng-deep .mat-option {\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n  padding: 8px 16px;\\n}\\n\\n::ng-deep .mat-option:hover:not(.mat-option-disabled) {\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n::ng-deep .mat-option.mat-selected:not(.mat-option-multiple):not(.mat-option-disabled) {\\n  background-color: rgba(var(--primary-color-rgb), 0.1);\\n  color: var(--primary-color);\\n}\\n\\n/* Styles pour le datepicker */\\n::ng-deep .mat-datepicker-toggle {\\n  color: var(--primary-color);\\n}\\n\\n::ng-deep .mat-calendar-body-selected {\\n  background-color: var(--primary-color);\\n}\\n\\n::ng-deep .mat-calendar-body-today:not(.mat-calendar-body-selected) {\\n  border-color: var(--primary-color);\\n}\\n\\n/* Styles pour les checkbox */\\n::ng-deep .mat-checkbox-checked.mat-primary .mat-checkbox-background {\\n  background-color: var(--primary-color);\\n}\\n\\n::ng-deep .mat-checkbox:not(.mat-checkbox-disabled).mat-primary .mat-checkbox-ripple .mat-ripple-element {\\n  background-color: rgba(var(--primary-color-rgb), 0.2);\\n}\\n\\n/* Styles pour le spinner */\\n::ng-deep .mat-progress-spinner circle, .mat-spinner circle {\\n  stroke: var(--accent-color);\\n}\\n\\n/* Styles pour les inputs num\\u00E9riques */\\n::ng-deep input[type=number]::-webkit-inner-spin-button,\\n::ng-deep input[type=number]::-webkit-outer-spin-button {\\n  opacity: 1;\\n  margin-left: 8px;\\n}\\n\\n/* Animation pour les cartes de vol */\\n@keyframes fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.animate-fade-in {\\n  animation: fadeInUp 0.5s ease-out forwards;\\n}\\n\\n/* D\\u00E9lai d'animation pour les cartes de vol */\\n.flight-card:nth-child(1) {\\n  animation-delay: 0.1s;\\n}\\n\\n.flight-card:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n\\n.flight-card:nth-child(3) {\\n  animation-delay: 0.3s;\\n}\\n\\n.flight-card:nth-child(4) {\\n  animation-delay: 0.4s;\\n}\\n\\n.flight-card:nth-child(5) {\\n  animation-delay: 0.5s;\\n}\\n\\n.flight-card:nth-child(n+6) {\\n  animation-delay: 0.6s;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9wcm9kdWN0L3NlYXJjaC1wcmljZS9zZWFyY2gtY2FyZC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsb0RBQW9EOztBQUVwRCxvQ0FBb0M7QUFDcEM7RUFDRSwwQ0FBMEM7QUFDNUM7O0FBRUE7RUFDRSwyQkFBMkI7QUFDN0I7O0FBRUE7RUFDRSxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxTQUFTO0FBQ1g7O0FBRUE7RUFDRSxVQUFVO0FBQ1o7O0FBRUE7RUFDRSx5Q0FBeUM7QUFDM0M7O0FBRUE7RUFDRSwyQkFBMkI7RUFDM0IsWUFBWTtFQUNaLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLHdCQUF3QjtBQUMxQjs7QUFFQSxpQ0FBaUM7QUFDakM7RUFDRSwwQ0FBMEM7RUFDMUMsOEJBQThCO0FBQ2hDOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGdCQUFnQjtFQUNoQixpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxzREFBc0Q7QUFDeEQ7O0FBRUE7RUFDRSxxREFBcUQ7RUFDckQsMkJBQTJCO0FBQzdCOztBQUVBLDhCQUE4QjtBQUM5QjtFQUNFLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLHNDQUFzQztBQUN4Qzs7QUFFQTtFQUNFLGtDQUFrQztBQUNwQzs7QUFFQSw2QkFBNkI7QUFDN0I7RUFDRSxzQ0FBc0M7QUFDeEM7O0FBRUE7RUFDRSxxREFBcUQ7QUFDdkQ7O0FBRUEsMkJBQTJCO0FBQzNCO0VBQ0UsMkJBQTJCO0FBQzdCOztBQUVBLHNDQUFzQztBQUN0Qzs7RUFFRSxVQUFVO0VBQ1YsZ0JBQWdCO0FBQ2xCOztBQUVBLHFDQUFxQztBQUNyQztFQUNFO0lBQ0UsVUFBVTtJQUNWLDJCQUEyQjtFQUM3QjtFQUNBO0lBQ0UsVUFBVTtJQUNWLHdCQUF3QjtFQUMxQjtBQUNGOztBQUVBO0VBQ0UsMENBQTBDO0FBQzVDOztBQUVBLDZDQUE2QztBQUM3QztFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2QiIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBzcMODwqljaWZpcXVlcyBwb3VyIGxlcyBjYXJ0ZXMgZGUgcmVjaGVyY2hlICovXG5cbi8qIFN0eWxlcyBwb3VyIGxlcyBjaGFtcHMgTWF0ZXJpYWwgKi9cbjo6bmctZGVlcCAubWF0LWZvcm0tZmllbGQtYXBwZWFyYW5jZS1vdXRsaW5lIC5tYXQtZm9ybS1maWVsZC1vdXRsaW5lIHtcbiAgY29sb3I6IHJnYmEodmFyKC0tcHJpbWFyeS1jb2xvci1yZ2IpLCAwLjMpO1xufVxuXG46Om5nLWRlZXAgLm1hdC1mb3JtLWZpZWxkLWFwcGVhcmFuY2Utb3V0bGluZS5tYXQtZm9jdXNlZCAubWF0LWZvcm0tZmllbGQtb3V0bGluZS10aGljayB7XG4gIGNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbn1cblxuOjpuZy1kZWVwIC5tYXQtZm9ybS1maWVsZC1hcHBlYXJhbmNlLW91dGxpbmUgLm1hdC1mb3JtLWZpZWxkLWluZml4IHtcbiAgcGFkZGluZzogMC41ZW0gMDtcbn1cblxuOjpuZy1kZWVwIC5tYXQtZm9ybS1maWVsZC1hcHBlYXJhbmNlLW91dGxpbmUgLm1hdC1mb3JtLWZpZWxkLXdyYXBwZXIge1xuICBtYXJnaW46IDA7XG59XG5cbjo6bmctZGVlcCAubWF0LWZvcm0tZmllbGQtYXBwZWFyYW5jZS1vdXRsaW5lIC5tYXQtZm9ybS1maWVsZC1sYWJlbCB7XG4gIHRvcDogMS41ZW07XG59XG5cbjo6bmctZGVlcCAubWF0LWZvcm0tZmllbGQtYXBwZWFyYW5jZS1vdXRsaW5lLm1hdC1mb3JtLWZpZWxkLWNhbi1mbG9hdC5tYXQtZm9ybS1maWVsZC1zaG91bGQtZmxvYXQgLm1hdC1mb3JtLWZpZWxkLWxhYmVsIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xLjFlbSkgc2NhbGUoMC43NSk7XG59XG5cbjo6bmctZGVlcCAubWF0LWZvcm0tZmllbGQtcHJlZml4IHtcbiAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICBvcGFjaXR5OiAwLjg7XG4gIG1hcmdpbi1yaWdodDogOHB4O1xufVxuXG46Om5nLWRlZXAgLm1hdC1mb3JtLWZpZWxkLWFwcGVhcmFuY2Utb3V0bGluZSAubWF0LXNlbGVjdC1hcnJvdy13cmFwcGVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xufVxuXG4vKiBTdHlsZXMgcG91ciBsJ2F1dG9jb21wbMODwql0aW9uICovXG46Om5nLWRlZXAgLm1hdC1hdXRvY29tcGxldGUtcGFuZWwge1xuICBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzLW1lZGl1bSk7XG4gIGJveC1zaGFkb3c6IHZhcigtLWVsZXZhdGlvbi0zKTtcbn1cblxuOjpuZy1kZWVwIC5tYXQtb3B0aW9uIHtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIGxpbmUtaGVpZ2h0OiAxLjQ7XG4gIHBhZGRpbmc6IDhweCAxNnB4O1xufVxuXG46Om5nLWRlZXAgLm1hdC1vcHRpb246aG92ZXI6bm90KC5tYXQtb3B0aW9uLWRpc2FibGVkKSB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEodmFyKC0tcHJpbWFyeS1jb2xvci1yZ2IpLCAwLjA1KTtcbn1cblxuOjpuZy1kZWVwIC5tYXQtb3B0aW9uLm1hdC1zZWxlY3RlZDpub3QoLm1hdC1vcHRpb24tbXVsdGlwbGUpOm5vdCgubWF0LW9wdGlvbi1kaXNhYmxlZCkge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKHZhcigtLXByaW1hcnktY29sb3ItcmdiKSwgMC4xKTtcbiAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xufVxuXG4vKiBTdHlsZXMgcG91ciBsZSBkYXRlcGlja2VyICovXG46Om5nLWRlZXAgLm1hdC1kYXRlcGlja2VyLXRvZ2dsZSB7XG4gIGNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbn1cblxuOjpuZy1kZWVwIC5tYXQtY2FsZW5kYXItYm9keS1zZWxlY3RlZCB7XG4gIGJhY2tncm91bmQtY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xufVxuXG46Om5nLWRlZXAgLm1hdC1jYWxlbmRhci1ib2R5LXRvZGF5Om5vdCgubWF0LWNhbGVuZGFyLWJvZHktc2VsZWN0ZWQpIHtcbiAgYm9yZGVyLWNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbn1cblxuLyogU3R5bGVzIHBvdXIgbGVzIGNoZWNrYm94ICovXG46Om5nLWRlZXAgLm1hdC1jaGVja2JveC1jaGVja2VkLm1hdC1wcmltYXJ5IC5tYXQtY2hlY2tib3gtYmFja2dyb3VuZCB7XG4gIGJhY2tncm91bmQtY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xufVxuXG46Om5nLWRlZXAgLm1hdC1jaGVja2JveDpub3QoLm1hdC1jaGVja2JveC1kaXNhYmxlZCkubWF0LXByaW1hcnkgLm1hdC1jaGVja2JveC1yaXBwbGUgLm1hdC1yaXBwbGUtZWxlbWVudCB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEodmFyKC0tcHJpbWFyeS1jb2xvci1yZ2IpLCAwLjIpO1xufVxuXG4vKiBTdHlsZXMgcG91ciBsZSBzcGlubmVyICovXG46Om5nLWRlZXAgLm1hdC1wcm9ncmVzcy1zcGlubmVyIGNpcmNsZSwgLm1hdC1zcGlubmVyIGNpcmNsZSB7XG4gIHN0cm9rZTogdmFyKC0tYWNjZW50LWNvbG9yKTtcbn1cblxuLyogU3R5bGVzIHBvdXIgbGVzIGlucHV0cyBudW3Dg8KpcmlxdWVzICovXG46Om5nLWRlZXAgaW5wdXRbdHlwZT1udW1iZXJdOjotd2Via2l0LWlubmVyLXNwaW4tYnV0dG9uLFxuOjpuZy1kZWVwIGlucHV0W3R5cGU9bnVtYmVyXTo6LXdlYmtpdC1vdXRlci1zcGluLWJ1dHRvbiB7XG4gIG9wYWNpdHk6IDE7XG4gIG1hcmdpbi1sZWZ0OiA4cHg7XG59XG5cbi8qIEFuaW1hdGlvbiBwb3VyIGxlcyBjYXJ0ZXMgZGUgdm9sICovXG5Aa2V5ZnJhbWVzIGZhZGVJblVwIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMjBweCk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICB9XG59XG5cbi5hbmltYXRlLWZhZGUtaW4ge1xuICBhbmltYXRpb246IGZhZGVJblVwIDAuNXMgZWFzZS1vdXQgZm9yd2FyZHM7XG59XG5cbi8qIETDg8KpbGFpIGQnYW5pbWF0aW9uIHBvdXIgbGVzIGNhcnRlcyBkZSB2b2wgKi9cbi5mbGlnaHQtY2FyZDpudGgtY2hpbGQoMSkge1xuICBhbmltYXRpb24tZGVsYXk6IDAuMXM7XG59XG5cbi5mbGlnaHQtY2FyZDpudGgtY2hpbGQoMikge1xuICBhbmltYXRpb24tZGVsYXk6IDAuMnM7XG59XG5cbi5mbGlnaHQtY2FyZDpudGgtY2hpbGQoMykge1xuICBhbmltYXRpb24tZGVsYXk6IDAuM3M7XG59XG5cbi5mbGlnaHQtY2FyZDpudGgtY2hpbGQoNCkge1xuICBhbmltYXRpb24tZGVsYXk6IDAuNHM7XG59XG5cbi5mbGlnaHQtY2FyZDpudGgtY2hpbGQoNSkge1xuICBhbmltYXRpb24tZGVsYXk6IDAuNXM7XG59XG5cbi5mbGlnaHQtY2FyZDpudGgtY2hpbGQobis2KSB7XG4gIGFuaW1hdGlvbi1kZWxheTogMC42cztcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "of", "FlightClassType", "LocationType", "PassengerType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "location_r11", "city", "ɵɵtemplate", "SearchPriceComponent_mat_option_33_span_2_Template", "ɵɵproperty", "ɵɵtextInterpolate2", "name", "code", "type", "location_r14", "SearchPriceComponent_mat_option_56_span_2_Template", "type_r17", "value", "label", "flightClass_r18", "ɵɵelement", "ɵɵtextInterpolate", "ctx_r8", "errorMessage", "ctx_r21", "getStopsCount", "flight_r20", "offers", "flightBrandInfo", "SearchPriceComponent_div_102_div_5_div_30_Template", "SearchPriceComponent_div_102_div_5_div_42_Template", "SearchPriceComponent_div_102_div_5_div_43_Template", "ɵɵlistener", "SearchPriceComponent_div_102_div_5_Template_button_click_45_listener", "restoredCtx", "ɵɵrestoreView", "_r27", "$implicit", "ctx_r26", "ɵɵnextContext", "ɵɵresetView", "showFlightDetails", "SearchPriceComponent_div_102_div_5_Template_button_click_48_listener", "ctx_r28", "selectThisFlight", "id", "ctx_r19", "getDepartureCity", "getArrivalCity", "getMinPrice", "getDepartureTime", "formatDuration", "getFlightDuration", "getArrivalTime", "ɵɵclassProp", "hasStops", "getDepartureAirportCode", "getArrivalAirportCode", "items", "segments", "length", "SearchPriceComponent_div_102_div_5_Template", "ctx_r10", "searchResults", "SearchPriceComponent", "constructor", "fb", "productService", "router", "departureLocations", "arrivalLocations", "isLoading", "hasSearched", "lastSearchId", "passengerTypes", "Adult", "Child", "Infant", "flightClasses", "PROMO", "ECONOMY", "BUSINESS", "minDate", "Date", "toISOString", "split", "searchForm", "group", "productType", "required", "serviceTypes", "departureLocation", "departureLocationType", "arrivalLocation", "arrivalLocationType", "departureDate", "passengerCount", "min", "max", "passengerType", "flightClass", "nonStop", "culture", "currency", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightBaggageGetOption", "ngOnInit", "setupAutocomplete", "preloadLocations", "console", "log", "flight", "modalDiv", "document", "createElement", "style", "position", "top", "left", "width", "height", "backgroundColor", "zIndex", "display", "justifyContent", "alignItems", "modalContent", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "color", "fontFamily", "closeButton", "innerHTML", "right", "border", "background", "fontSize", "cursor", "transition", "on<PERSON><PERSON>ver", "onmouseout", "onclick", "body", "<PERSON><PERSON><PERSON><PERSON>", "header", "marginBottom", "paddingBottom", "borderBottom", "logo", "title", "textContent", "margin", "fontWeight", "append<PERSON><PERSON><PERSON>", "detailsContainer", "flexDirection", "gap", "generalInfo", "createSection", "item", "airline", "airlineInfo", "thumbnailFull", "airlineLogo", "src", "alt", "marginRight", "airlineIcon", "airlineName", "internationalCode", "flightNumberRow", "createInfoRow", "flightNo", "flightDateRow", "flightDate", "toLocaleDateString", "durationRow", "duration", "classRow", "stopsRow", "stopCount", "routeSection", "routeVisual", "departure", "textAlign", "flex", "departureTime", "date", "toLocaleTimeString", "hour", "minute", "departureAirport", "marginTop", "airport", "departureCity", "connectionLine", "line", "plane", "marginLeft", "arrival", "arrivalTime", "arrivalAirport", "arrivalCity", "segmentsTitle", "segmentsList", "for<PERSON>ach", "segment", "index", "segmentItem", "segmentHeader", "segmentTitle", "segmentDuration", "segmentRoute", "segmentDeparture", "depTime", "depAirport", "arrow", "segmentArrival", "arrTime", "arrAirport", "layover", "currentArrival", "getTime", "nextDeparture", "layoverTime", "Math", "floor", "offersSection", "offersList", "offer", "offerItem", "offerHeader", "offerTitle", "offerPrice", "price", "amount", "offerDetails", "gridTemplateColumns", "offerId", "gridColumn", "availabilityValue", "availability", "undefined", "seatInfo", "availableSeatCount", "expiresOn", "expires", "toLocaleString", "brandedFare", "reservableInfo", "reservable", "baggageInformations", "baggageTitle", "baggageList", "listStyle", "baggage", "baggageItem", "getBaggageTypeName", "baggageType", "services", "servicesSection", "servicesList", "service", "serviceItem", "iconClass", "section", "section<PERSON><PERSON><PERSON>", "icon", "className", "sectionTitle", "row", "labelElement", "valueElement", "searchId", "navigate", "queryParams", "error", "get", "getLocationsByType", "subscribe", "locations", "valueChanges", "locationType", "setValue", "pipe", "filter", "location", "toLowerCase", "includes", "displayLocation", "displayText", "Airport", "onSearch", "invalid", "markFormGroupTouched", "formValue", "request", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "ArrivalLocations", "Passengers", "count", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "searchPrice", "next", "response", "success", "flights", "JSON", "stringify", "flightsWithOffers", "f", "availabilityValues", "flatMap", "o", "availabilityCounts", "reduce", "acc", "val", "reservableFlights", "some", "groupEnd", "requestId", "Object", "keys", "messages", "message", "formGroup", "values", "controls", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "minutes", "hours", "mins", "formatDate", "dateString", "weekday", "day", "month", "min<PERSON>ffer", "formattedAmount", "lastSegment", "isDirectFlight", "hasBrandInfo", "getBrandName", "isFlightAvailable", "showAllDepartureLocations", "input", "getElementById", "focus", "dispatchEvent", "Event", "showAllArrivalLocations", "swapLocations", "patchValue", "formatExpirationDate", "getPassengerTypeName", "calculateLayoverTime", "currentSegment", "nextSegment", "diffMs", "diffMins", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProductService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "SearchPriceComponent_Template", "rf", "ctx", "SearchPriceComponent_Template_form_ngSubmit_11_listener", "SearchPriceComponent_Template_input_click_28_listener", "SearchPriceComponent_mat_option_33_Template", "SearchPriceComponent_Template_button_click_34_listener", "SearchPriceComponent_Template_input_click_51_listener", "SearchPriceComponent_mat_option_56_Template", "SearchPriceComponent_mat_option_76_Template", "SearchPriceComponent_mat_option_89_Template", "SearchPriceComponent_div_99_Template", "SearchPriceComponent_div_100_Template", "SearchPriceComponent_div_101_Template", "SearchPriceComponent_div_102_Template", "_r0", "_r2", "_r4"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { Observable, of } from 'rxjs';\nimport { ProductService } from '../../../services/product.service';\nimport { LocationOption } from '../../../models/price-search-request.model';\nimport { PriceSearchRequest } from '../../../models/price-search-request.model';\nimport { PriceSearchResponse, Flight } from '../../../models/price-search-response.model';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css'],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchPriceComponent implements OnInit {\n  searchForm: FormGroup;\n  departureLocations: LocationOption[] = [];\n  arrivalLocations: LocationOption[] = [];\n  isLoading = false;\n  searchResults: Flight[] = [];\n  hasSearched = false;\n  errorMessage = '';\n  lastSearchId = '';\n\n  // Passenger type options\n  passengerTypes = [\n    { value: PassengerType.Adult, label: 'Adult' },\n    { value: PassengerType.Child, label: 'Child' },\n    { value: PassengerType.Infant, label: 'Infant' }\n  ];\n\n  // Flight class options\n  flightClasses = [\n    { value: FlightClassType.PROMO, label: 'Promo' },\n    { value: FlightClassType.ECONOMY, label: 'Economy' },\n    { value: FlightClassType.BUSINESS, label: 'Business' }\n  ];\n\n  // Date minimale (aujourd'hui)\n  minDate: string;\n\n  constructor(\n    private fb: FormBuilder,\n    private productService: ProductService,\n    private router: Router\n  ) {\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required], // Flight par défaut\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required], // Type 2 (City) par défaut\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required], // Type 5 (Airport) par défaut\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required], // Adult par défaut\n\n      // Options de vol\n      flightClass: [0, Validators.required], // PROMO par défaut\n      nonStop: [false],\n\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n\n  ngOnInit(): void {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n\n  // Méthode pour afficher les détails du vol\n  showFlightDetails(flight: Flight): void {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n\n    header.appendChild(logo);\n    header.appendChild(title);\n\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n\n        generalInfo.appendChild(airlineInfo);\n      }\n\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n\n      routeSection.appendChild(routeVisual);\n\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n\n          segmentsList.appendChild(segmentItem);\n\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n\n            segmentsList.appendChild(layover);\n          }\n        });\n\n        routeSection.appendChild(segmentsList);\n      }\n    }\n\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n\n        // ID de l'offre\n        const offerId = this.createInfoRow('Offer ID', offer.offerId || offer.id || 'N/A');\n        offerId.style.gridColumn = '1 / -1';\n        offerDetails.appendChild(offerId);\n\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability :\n                                 (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n\n        offerItem.appendChild(offerDetails);\n\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n\n          const baggageList = document.createElement('ul');\n          baggageList.style.listStyle = 'none';\n          baggageList.style.padding = '0';\n          baggageList.style.margin = '0';\n\n          offer.baggageInformations.forEach(baggage => {\n            const baggageItem = document.createElement('li');\n            baggageItem.style.marginBottom = '5px';\n            baggageItem.innerHTML = `<i class=\"fas fa-suitcase\" style=\"color: #666; margin-right: 8px;\"></i> ${this.getBaggageTypeName(baggage.baggageType)}`;\n            baggageList.appendChild(baggageItem);\n          });\n\n          offerItem.appendChild(baggageList);\n        }\n\n        offersList.appendChild(offerItem);\n      });\n\n      offersSection.appendChild(offersList);\n    }\n\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n\n  // Méthode utilitaire pour créer une section\n  private createSection(title: string, iconClass: string): HTMLElement {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n\n    return section;\n  }\n\n  // Méthode utilitaire pour créer une ligne d'information\n  private createInfoRow(label: string, value: string): HTMLElement {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n\n    return row;\n  }\n\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight: Flight): void {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n\n  preloadLocations(): void {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n\n  setupAutocomplete(): void {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.departureLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('departureLocation')?.setValue('');\n        });\n      });\n\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.arrivalLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('arrivalLocation')?.setValue('');\n        });\n      });\n\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.departureLocations = locations;\n      });\n\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n  }\n\n  displayLocation(location: LocationOption | null): string {\n    if (!location) return '';\n\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n\n  onSearch(): void {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n\n    const formValue = this.searchForm.value;\n\n    // Créer la requête de recherche entièrement dynamique\n    const request: PriceSearchRequest = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [\n        {\n          id: formValue.departureLocation?.id || '',\n          type: formValue.departureLocationType\n        }\n      ],\n      ArrivalLocations: [\n        {\n          id: formValue.arrivalLocation?.id || '',\n          type: formValue.arrivalLocationType\n        }\n      ],\n      Passengers: [\n        {\n          type: formValue.passengerType,\n          count: formValue.passengerCount\n        }\n      ],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n\n    this.productService.searchPrice(request)\n      .subscribe({\n        next: (response: PriceSearchResponse) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            console.log('Full API Response:', JSON.stringify(response, null, 2));\n\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o =>\n                o.availability !== undefined ? o.availability : (o.seatInfo ? o.seatInfo.availableSeatCount : 0)\n              ));\n              console.log('Availability values:', availabilityValues);\n\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc, val) => {\n                if (val !== undefined) {\n                  acc[val] = (acc[val] || 0) + 1;\n                }\n                return acc;\n              }, {} as Record<number, number>);\n              console.log('Availability counts:', availabilityCounts);\n\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f =>\n                f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true)\n              );\n              console.log('Reservable flights:', reservableFlights.length);\n\n              console.groupEnd();\n            }\n\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n  }\n\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup: FormGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n\n  // Formater la durée en heures et minutes\n  formatDuration(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n\n  // Formater la date pour l'affichage\n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight: Flight): string {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n\n    const minOffer = flight.offers.reduce((min, offer) =>\n      offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n\n  // Méthodes d'assistance pour l'affichage sécurisé des données de vol\n  getDepartureCity(flight: Flight): string {\n    if (!flight?.items?.[0]?.segments?.[0]?.departure?.city?.name) {\n      return 'Départ';\n    }\n    return flight.items[0].segments[0].departure.city.name;\n  }\n\n  getArrivalCity(flight: Flight): string {\n    if (!flight?.items?.[0]?.segments || flight.items[0].segments.length === 0) {\n      return 'Arrivée';\n    }\n    const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n    return lastSegment?.arrival?.city?.name || 'Arrivée';\n  }\n\n  getDepartureTime(flight: Flight): string {\n    if (!flight?.items?.[0]?.segments?.[0]?.departure?.date) {\n      return '--:--';\n    }\n    const date = new Date(flight.items[0].segments[0].departure.date);\n    return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n  }\n\n  getArrivalTime(flight: Flight): string {\n    if (!flight?.items?.[0]?.segments || flight.items[0].segments.length === 0) {\n      return '--:--';\n    }\n    const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n    if (!lastSegment?.arrival?.date) {\n      return '--:--';\n    }\n    const date = new Date(lastSegment.arrival.date);\n    return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n  }\n\n  getFlightDuration(flight: Flight): number {\n    return flight?.items?.[0]?.duration || 0;\n  }\n\n  hasStops(flight: Flight): boolean {\n    return flight?.items?.[0]?.segments && flight.items[0].segments.length > 1;\n  }\n\n  getStopsCount(flight: Flight): number {\n    if (!flight?.items?.[0]?.segments) {\n      return 0;\n    }\n    return flight.items[0].segments.length - 1;\n  }\n\n  getDepartureAirportCode(flight: Flight): string {\n    return flight?.items?.[0]?.segments?.[0]?.departure?.airport?.code || '';\n  }\n\n  getArrivalAirportCode(flight: Flight): string {\n    if (!flight?.items?.[0]?.segments || flight.items[0].segments.length === 0) {\n      return '';\n    }\n    const lastSegment = flight.items[0].segments[flight.items[0].segments.length - 1];\n    return lastSegment?.arrival?.airport?.code || '';\n  }\n\n  isDirectFlight(flight: Flight): boolean {\n    return flight?.items?.[0]?.segments && flight.items[0].segments.length === 1;\n  }\n\n  hasBrandInfo(flight: Flight): boolean {\n    return !!flight?.offers?.[0]?.flightBrandInfo?.name;\n  }\n\n  getBrandName(flight: Flight): string {\n    return flight?.offers?.[0]?.flightBrandInfo?.name || '';\n  }\n\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight: Flight): boolean {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability :\n                             (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n    return availabilityValue > 0;\n  }\n\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations(): void {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations(): void {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations(): void {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType: number): string {\n    switch (baggageType) {\n      case 1: return 'Cabin Baggage';\n      case 2: return 'Checked Baggage';\n      case 3: return 'Hand Baggage';\n      default: return 'Baggage';\n    }\n  }\n\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType: number): string {\n    switch (passengerType) {\n      case 1: return 'Adult';\n      case 2: return 'Child';\n      case 3: return 'Infant';\n      default: return 'Passenger';\n    }\n  }\n\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment: any, nextSegment: any): string {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date ||\n        !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n}\n", "<div class=\"search-price-container\">\n  <div class=\"search-layout\">\n    <!-- Carte de recherche -->\n    <div class=\"search-card-container\">\n      <div class=\"search-card\">\n        <div class=\"search-card-header\">\n          <div class=\"search-card-title\">\n            <i class=\"fas fa-search\"></i>\n            <h2>Rechercher un vol</h2>\n          </div>\n          <div class=\"search-card-subtitle\">\n            Trouvez les meilleurs tarifs pour votre prochain voyage\n          </div>\n        </div>\n\n        <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSearch()\" class=\"search-form\">\n          <!-- Emplacements de départ et d'arrivée -->\n          <div class=\"location-container\">\n            <div class=\"form-group location-group\">\n              <label for=\"departureLocation\">Départ</label>\n              <div class=\"location-input-container\">\n                <div class=\"location-type-selector\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-select formControlName=\"departureLocationType\">\n                      <mat-option [value]=\"2\">Ville</mat-option>\n                      <mat-option [value]=\"5\">Aéroport</mat-option>\n                    </mat-select>\n                  </mat-form-field>\n                </div>\n                <div class=\"location-autocomplete\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Ville ou aéroport de départ</mat-label>\n                    <input type=\"text\"\n                           id=\"departureLocation\"\n                           matInput\n                           [matAutocomplete]=\"departureAuto\"\n                           formControlName=\"departureLocation\"\n                           (click)=\"showAllDepartureLocations()\">\n                    <mat-icon matPrefix>flight_takeoff</mat-icon>\n                    <mat-autocomplete #departureAuto=\"matAutocomplete\" [displayWith]=\"displayLocation\">\n                      <mat-option *ngFor=\"let location of departureLocations\" [value]=\"location\">\n                        {{ location.name }} {{ location.code ? '(' + location.code + ')' : '' }}\n                        <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">\n                          - {{ location.city }}\n                        </span>\n                      </mat-option>\n                    </mat-autocomplete>\n                  </mat-form-field>\n                </div>\n              </div>\n            </div>\n\n            <button type=\"button\" class=\"swap-button\" (click)=\"swapLocations()\">\n              <i class=\"fas fa-exchange-alt\"></i>\n            </button>\n\n            <div class=\"form-group location-group\">\n              <label for=\"arrivalLocation\">Arrivée</label>\n              <div class=\"location-input-container\">\n                <div class=\"location-type-selector\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-select formControlName=\"arrivalLocationType\">\n                      <mat-option [value]=\"2\">Ville</mat-option>\n                      <mat-option [value]=\"5\">Aéroport</mat-option>\n                    </mat-select>\n                  </mat-form-field>\n                </div>\n                <div class=\"location-autocomplete\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Ville ou aéroport d'arrivée</mat-label>\n                    <input type=\"text\"\n                           id=\"arrivalLocation\"\n                           matInput\n                           [matAutocomplete]=\"arrivalAuto\"\n                           formControlName=\"arrivalLocation\"\n                           (click)=\"showAllArrivalLocations()\">\n                    <mat-icon matPrefix>flight_land</mat-icon>\n                    <mat-autocomplete #arrivalAuto=\"matAutocomplete\" [displayWith]=\"displayLocation\">\n                      <mat-option *ngFor=\"let location of arrivalLocations\" [value]=\"location\">\n                        {{ location.name }} {{ location.code ? '(' + location.code + ')' : '' }}\n                        <span *ngIf=\"location.type === 5 && location.city\" class=\"location-city\">\n                          - {{ location.city }}\n                        </span>\n                      </mat-option>\n                    </mat-autocomplete>\n                  </mat-form-field>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Date et passagers -->\n          <div class=\"date-passengers-container\">\n            <div class=\"form-group date-group\">\n              <label for=\"departureDate\">Date de départ</label>\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Choisir une date</mat-label>\n                <input matInput [min]=\"minDate\" [matDatepicker]=\"picker\" formControlName=\"departureDate\" id=\"departureDate\">\n                <mat-datepicker-toggle matSuffix [for]=\"picker\"></mat-datepicker-toggle>\n                <mat-datepicker #picker></mat-datepicker>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-group passenger-group\">\n              <label for=\"passengerCount\">Passagers</label>\n              <div class=\"passenger-inputs\">\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Type</mat-label>\n                  <mat-select formControlName=\"passengerType\" id=\"passengerType\">\n                    <mat-option *ngFor=\"let type of passengerTypes\" [value]=\"type.value\">\n                      {{ type.label }}\n                    </mat-option>\n                  </mat-select>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Nombre</mat-label>\n                  <input matInput type=\"number\" min=\"1\" max=\"9\" formControlName=\"passengerCount\" id=\"passengerCount\">\n                </mat-form-field>\n              </div>\n            </div>\n          </div>\n\n          <!-- Options de vol -->\n          <div class=\"flight-options-container\">\n            <div class=\"form-group class-group\">\n              <label for=\"flightClass\">Classe</label>\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Classe de vol</mat-label>\n                <mat-select formControlName=\"flightClass\" id=\"flightClass\">\n                  <mat-option *ngFor=\"let flightClass of flightClasses\" [value]=\"flightClass.value\">\n                    {{ flightClass.label }}\n                  </mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-group nonstop-group\">\n              <div class=\"checkbox-container\">\n                <mat-checkbox formControlName=\"nonStop\" id=\"nonStop\" color=\"primary\">\n                  Vol direct uniquement\n                </mat-checkbox>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton de recherche -->\n          <div class=\"search-button-container\">\n            <button type=\"submit\" class=\"search-button\" [disabled]=\"isLoading\">\n              <i class=\"fas fa-search\"></i>\n              Rechercher\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n\n    <!-- Résultats de recherche -->\n    <div class=\"search-results-container\" [class.has-results]=\"hasSearched\">\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <mat-spinner diameter=\"50\" color=\"accent\"></mat-spinner>\n        <p class=\"loading-text\">Recherche des meilleurs vols...</p>\n      </div>\n\n      <div *ngIf=\"errorMessage && !isLoading\" class=\"error-container\">\n        <div class=\"error-icon\">\n          <i class=\"fas fa-exclamation-circle\"></i>\n        </div>\n        <p class=\"error-message\">{{ errorMessage }}</p>\n      </div>\n\n      <div *ngIf=\"!isLoading && searchResults.length === 0 && hasSearched && !errorMessage\" class=\"no-results-container\">\n        <div class=\"no-results-icon\">\n          <i class=\"fas fa-search\"></i>\n        </div>\n        <h3>Aucun vol trouvé</h3>\n        <p>Essayez de modifier vos critères de recherche</p>\n      </div>\n\n      <div *ngIf=\"!isLoading && searchResults.length > 0\" class=\"results-list\">\n        <h3 class=\"results-title\">\n          <i class=\"fas fa-plane\"></i>\n          Vols disponibles ({{ searchResults.length }})\n        </h3>\n\n        <div class=\"flight-cards\">\n          <div *ngFor=\"let flight of searchResults\" class=\"flight-card animate-fade-in\">\n            <div class=\"flight-card-header\">\n              <div class=\"flight-info\">\n                <div class=\"flight-id\">{{ flight.id }}</div>\n                <div class=\"flight-route\">\n                  <span class=\"departure\">{{ getDepartureCity(flight) }}</span>\n                  <i class=\"fas fa-long-arrow-alt-right\"></i>\n                  <span class=\"arrival\">{{ getArrivalCity(flight) }}</span>\n                </div>\n              </div>\n              <div class=\"flight-price\">\n                <div class=\"price-label\">à partir de</div>\n                <div class=\"price-amount\">{{ getMinPrice(flight) }}</div>\n              </div>\n            </div>\n\n            <div class=\"flight-card-content\">\n              <div class=\"flight-details\">\n                <div class=\"segment-info\">\n                  <div class=\"time-info\">\n                    <div class=\"departure-time\">\n                      {{ getDepartureTime(flight) }}\n                    </div>\n                    <div class=\"flight-duration\">\n                      <i class=\"fas fa-clock\"></i>\n                      {{ formatDuration(getFlightDuration(flight)) }}\n                    </div>\n                    <div class=\"arrival-time\">\n                      {{ getArrivalTime(flight) }}\n                    </div>\n                  </div>\n                  <div class=\"route-line\">\n                    <div class=\"route-point\"></div>\n                    <div class=\"route-path\" [class.has-stops]=\"hasStops(flight)\">\n                      <div *ngIf=\"hasStops(flight)\" class=\"stops-indicator\">\n                        {{ getStopsCount(flight) }} escale(s)\n                      </div>\n                    </div>\n                    <div class=\"route-point\"></div>\n                  </div>\n                  <div class=\"location-info\">\n                    <div class=\"departure-location\">\n                      {{ getDepartureAirportCode(flight) }}\n                    </div>\n                    <div class=\"arrival-location\">\n                      {{ getArrivalAirportCode(flight) }}\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"flight-features\">\n                  <div class=\"feature\">\n                    <i class=\"fas fa-suitcase\"></i>\n                    <span>Bagages inclus</span>\n                  </div>\n                  <div class=\"feature\" *ngIf=\"flight.items[0]?.segments.length === 1\">\n                    <i class=\"fas fa-plane\"></i>\n                    <span>Vol direct</span>\n                  </div>\n                  <div class=\"feature\" *ngIf=\"flight.offers[0]?.flightBrandInfo?.name\">\n                    <i class=\"fas fa-tag\"></i>\n                    <span>{{ flight.offers[0]?.flightBrandInfo?.name }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"flight-actions\">\n                <button class=\"view-details-button\" (click)=\"showFlightDetails(flight)\">\n                  <i class=\"fas fa-info-circle\"></i>\n                  Détails\n                </button>\n                <button class=\"select-flight-button\" (click)=\"selectThisFlight(flight)\">\n                  <i class=\"fas fa-check-circle\"></i>\n                  Sélectionner\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnF,SAAqBC,EAAE,QAAQ,MAAM;AAKrC,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;;;;;;;;;;;;;;;;ICiClEC,EAAA,CAAAC,cAAA,eAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,QAAAC,YAAA,CAAAC,IAAA,MACF;;;;;IAJFP,EAAA,CAAAC,cAAA,qBAA2E;IACzED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAQ,UAAA,IAAAC,kDAAA,mBAEO;IACTT,EAAA,CAAAG,YAAA,EAAa;;;;IAL2CH,EAAA,CAAAU,UAAA,UAAAJ,YAAA,CAAkB;IACxEN,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAW,kBAAA,MAAAL,YAAA,CAAAM,IAAA,OAAAN,YAAA,CAAAO,IAAA,SAAAP,YAAA,CAAAO,IAAA,iBACA;IAAOb,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAU,UAAA,SAAAJ,YAAA,CAAAQ,IAAA,UAAAR,YAAA,CAAAC,IAAA,CAA0C;;;;;IAsCjDP,EAAA,CAAAC,cAAA,eAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,QAAAU,YAAA,CAAAR,IAAA,MACF;;;;;IAJFP,EAAA,CAAAC,cAAA,qBAAyE;IACvED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAQ,UAAA,IAAAQ,kDAAA,mBAEO;IACThB,EAAA,CAAAG,YAAA,EAAa;;;;IALyCH,EAAA,CAAAU,UAAA,UAAAK,YAAA,CAAkB;IACtEf,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAW,kBAAA,MAAAI,YAAA,CAAAH,IAAA,OAAAG,YAAA,CAAAF,IAAA,SAAAE,YAAA,CAAAF,IAAA,iBACA;IAAOb,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAU,UAAA,SAAAK,YAAA,CAAAD,IAAA,UAAAC,YAAA,CAAAR,IAAA,CAA0C;;;;;IA6BrDP,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAU,UAAA,UAAAO,QAAA,CAAAC,KAAA,CAAoB;IAClElB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAY,QAAA,CAAAE,KAAA,MACF;;;;;IAmBFnB,EAAA,CAAAC,cAAA,qBAAkF;IAChFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFyCH,EAAA,CAAAU,UAAA,UAAAU,eAAA,CAAAF,KAAA,CAA2B;IAC/ElB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAe,eAAA,CAAAD,KAAA,MACF;;;;;IA2BZnB,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAqB,SAAA,sBAAwD;IACxDrB,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAG7DH,EAAA,CAAAC,cAAA,cAAgE;IAE5DD,EAAA,CAAAqB,SAAA,YAAyC;IAC3CrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAtBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAsB,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;IAG7CxB,EAAA,CAAAC,cAAA,cAAmH;IAE/GD,EAAA,CAAAqB,SAAA,WAA6B;IAC/BrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,4BAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yDAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IA4CtCH,EAAA,CAAAC,cAAA,eAAsD;IACpDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAoB,OAAA,CAAAC,aAAA,CAAAC,UAAA,iBACF;;;;;IAmBJ3B,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAqB,SAAA,YAA4B;IAC5BrB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEzBH,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAqB,SAAA,aAA0B;IAC1BrB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApDH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAsB,iBAAA,CAAAK,UAAA,CAAAC,MAAA,qBAAAD,UAAA,CAAAC,MAAA,IAAAC,eAAA,kBAAAF,UAAA,CAAAC,MAAA,IAAAC,eAAA,CAAAjB,IAAA,CAA6C;;;;;;IA7D7DZ,EAAA,CAAAC,cAAA,cAA8E;IAGjDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAA0B;IACAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7DH,EAAA,CAAAqB,SAAA,YAA2C;IAC3CrB,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG7DH,EAAA,CAAAC,cAAA,eAA0B;IACCD,EAAA,CAAAE,MAAA,wBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAI7DH,EAAA,CAAAC,cAAA,eAAiC;IAKvBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAqB,SAAA,aAA4B;IAC5BrB,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAqB,SAAA,eAA+B;IAC/BrB,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAQ,UAAA,KAAAsB,kDAAA,kBAEM;IACR9B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAqB,SAAA,eAA+B;IACjCrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAEvBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIVH,EAAA,CAAAC,cAAA,eAA6B;IAEzBD,EAAA,CAAAqB,SAAA,aAA+B;IAC/BrB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7BH,EAAA,CAAAQ,UAAA,KAAAuB,kDAAA,kBAGM;IACN/B,EAAA,CAAAQ,UAAA,KAAAwB,kDAAA,kBAGM;IACRhC,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAA4B;IACUD,EAAA,CAAAiC,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,WAAA,GAAAnC,EAAA,CAAAoC,aAAA,CAAAC,IAAA;MAAA,MAAAV,UAAA,GAAAQ,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAF,OAAA,CAAAG,iBAAA,CAAAf,UAAA,CAAyB;IAAA,EAAC;IACrE3B,EAAA,CAAAqB,SAAA,cAAkC;IAClCrB,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAwE;IAAnCD,EAAA,CAAAiC,UAAA,mBAAAU,qEAAA;MAAA,MAAAR,WAAA,GAAAnC,EAAA,CAAAoC,aAAA,CAAAC,IAAA;MAAA,MAAAV,UAAA,GAAAQ,WAAA,CAAAG,SAAA;MAAA,MAAAM,OAAA,GAAA5C,EAAA,CAAAwC,aAAA;MAAA,OAASxC,EAAA,CAAAyC,WAAA,CAAAG,OAAA,CAAAC,gBAAA,CAAAlB,UAAA,CAAwB;IAAA,EAAC;IACrE3B,EAAA,CAAAqB,SAAA,cAAmC;IACnCrB,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAvEcH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAsB,iBAAA,CAAAK,UAAA,CAAAmB,EAAA,CAAe;IAEZ9C,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAsB,iBAAA,CAAAyB,OAAA,CAAAC,gBAAA,CAAArB,UAAA,EAA8B;IAEhC3B,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAsB,iBAAA,CAAAyB,OAAA,CAAAE,cAAA,CAAAtB,UAAA,EAA4B;IAK1B3B,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAsB,iBAAA,CAAAyB,OAAA,CAAAG,WAAA,CAAAvB,UAAA,EAAyB;IAS7C3B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0C,OAAA,CAAAI,gBAAA,CAAAxB,UAAA,OACF;IAGE3B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0C,OAAA,CAAAK,cAAA,CAAAL,OAAA,CAAAM,iBAAA,CAAA1B,UAAA,QACF;IAEE3B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0C,OAAA,CAAAO,cAAA,CAAA3B,UAAA,OACF;IAIwB3B,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAuD,WAAA,cAAAR,OAAA,CAAAS,QAAA,CAAA7B,UAAA,EAAoC;IACpD3B,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAU,UAAA,SAAAqC,OAAA,CAAAS,QAAA,CAAA7B,UAAA,EAAsB;IAQ5B3B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0C,OAAA,CAAAU,uBAAA,CAAA9B,UAAA,OACF;IAEE3B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0C,OAAA,CAAAW,qBAAA,CAAA/B,UAAA,OACF;IASoB3B,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAU,UAAA,UAAAiB,UAAA,CAAAgC,KAAA,qBAAAhC,UAAA,CAAAgC,KAAA,IAAAC,QAAA,CAAAC,MAAA,QAA4C;IAI5C7D,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAU,UAAA,SAAAiB,UAAA,CAAAC,MAAA,qBAAAD,UAAA,CAAAC,MAAA,IAAAC,eAAA,kBAAAF,UAAA,CAAAC,MAAA,IAAAC,eAAA,CAAAjB,IAAA,CAA6C;;;;;IAlE/EZ,EAAA,CAAAC,cAAA,cAAyE;IAErED,EAAA,CAAAqB,SAAA,YAA4B;IAC5BrB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAQ,UAAA,IAAAsD,2CAAA,oBA6EM;IACR9D,EAAA,CAAAG,YAAA,EAAM;;;;IAlFJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,wBAAA0D,OAAA,CAAAC,aAAA,CAAAH,MAAA,OACF;IAG0B7D,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAU,UAAA,YAAAqD,OAAA,CAAAC,aAAA,CAAgB;;;ADzKlD,OAAM,MAAOC,oBAAoB;EA2B/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IA5BhB,KAAAC,kBAAkB,GAAqB,EAAE;IACzC,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAR,aAAa,GAAa,EAAE;IAC5B,KAAAS,WAAW,GAAG,KAAK;IACnB,KAAAjD,YAAY,GAAG,EAAE;IACjB,KAAAkD,YAAY,GAAG,EAAE;IAEjB;IACA,KAAAC,cAAc,GAAG,CACf;MAAEzD,KAAK,EAAEnB,aAAa,CAAC6E,KAAK;MAAEzD,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAEnB,aAAa,CAAC8E,KAAK;MAAE1D,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAED,KAAK,EAAEnB,aAAa,CAAC+E,MAAM;MAAE3D,KAAK,EAAE;IAAQ,CAAE,CACjD;IAED;IACA,KAAA4D,aAAa,GAAG,CACd;MAAE7D,KAAK,EAAErB,eAAe,CAACmF,KAAK;MAAE7D,KAAK,EAAE;IAAO,CAAE,EAChD;MAAED,KAAK,EAAErB,eAAe,CAACoF,OAAO;MAAE9D,KAAK,EAAE;IAAS,CAAE,EACpD;MAAED,KAAK,EAAErB,eAAe,CAACqF,QAAQ;MAAE/D,KAAK,EAAE;IAAU,CAAE,CACvD;IAUC;IACA,IAAI,CAACgE,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAErD;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACpB,EAAE,CAACqB,KAAK,CAAC;MAC9B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAElG,UAAU,CAACmG,QAAQ,CAAC;MACrCC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,EAAEpG,UAAU,CAACmG,QAAQ,CAAC;MAC1CE,iBAAiB,EAAE,CAAC,EAAE,EAAErG,UAAU,CAACmG,QAAQ,CAAC;MAC5CG,qBAAqB,EAAE,CAAC,CAAC,EAAEtG,UAAU,CAACmG,QAAQ,CAAC;MAC/CI,eAAe,EAAE,CAAC,EAAE,EAAEvG,UAAU,CAACmG,QAAQ,CAAC;MAC1CK,mBAAmB,EAAE,CAAC,CAAC,EAAExG,UAAU,CAACmG,QAAQ,CAAC;MAC7CM,aAAa,EAAE,CAAC,IAAI,CAACb,OAAO,EAAE5F,UAAU,CAACmG,QAAQ,CAAC;MAClDO,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC1G,UAAU,CAACmG,QAAQ,EAAEnG,UAAU,CAAC2G,GAAG,CAAC,CAAC,CAAC,EAAE3G,UAAU,CAAC4G,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChFC,aAAa,EAAE,CAAC,CAAC,EAAE7G,UAAU,CAACmG,QAAQ,CAAC;MAEvC;MACAW,WAAW,EAAE,CAAC,CAAC,EAAE9G,UAAU,CAACmG,QAAQ,CAAC;MACrCY,OAAO,EAAE,CAAC,KAAK,CAAC;MAEhB;MACAC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBC,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,wBAAwB,EAAE,CAAC,KAAK,CAAC;MACjCC,6BAA6B,EAAE,CAAC,IAAI,CAAC;MACrCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAE5B;MACAC,sBAAsB,EAAE,CAAC,CAAC;KAC3B,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACAC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EACjF;EAEA;EACAxE,iBAAiBA,CAACyE,MAAc;IAC9B;IACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9CF,QAAQ,CAACG,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCJ,QAAQ,CAACG,KAAK,CAACE,GAAG,GAAG,GAAG;IACxBL,QAAQ,CAACG,KAAK,CAACG,IAAI,GAAG,GAAG;IACzBN,QAAQ,CAACG,KAAK,CAACI,KAAK,GAAG,MAAM;IAC7BP,QAAQ,CAACG,KAAK,CAACK,MAAM,GAAG,MAAM;IAC9BR,QAAQ,CAACG,KAAK,CAACM,eAAe,GAAG,oBAAoB;IACrDT,QAAQ,CAACG,KAAK,CAACO,MAAM,GAAG,MAAM;IAC9BV,QAAQ,CAACG,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC/BX,QAAQ,CAACG,KAAK,CAACS,cAAc,GAAG,QAAQ;IACxCZ,QAAQ,CAACG,KAAK,CAACU,UAAU,GAAG,QAAQ;IAEpC;IACA,MAAMC,YAAY,GAAGb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDY,YAAY,CAACX,KAAK,CAACM,eAAe,GAAG,OAAO;IAC5CK,YAAY,CAACX,KAAK,CAACY,OAAO,GAAG,MAAM;IACnCD,YAAY,CAACX,KAAK,CAACa,YAAY,GAAG,MAAM;IACxCF,YAAY,CAACX,KAAK,CAACc,QAAQ,GAAG,KAAK;IACnCH,YAAY,CAACX,KAAK,CAACe,SAAS,GAAG,KAAK;IACpCJ,YAAY,CAACX,KAAK,CAACgB,QAAQ,GAAG,MAAM;IACpCL,YAAY,CAACX,KAAK,CAACC,QAAQ,GAAG,UAAU;IACxCU,YAAY,CAACX,KAAK,CAACiB,SAAS,GAAG,gCAAgC;IAC/DN,YAAY,CAACX,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCP,YAAY,CAACX,KAAK,CAACmB,UAAU,GAAG,mBAAmB;IAEnD;IACA,MAAMC,WAAW,GAAGtB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACpDqB,WAAW,CAACC,SAAS,GAAG,SAAS;IACjCD,WAAW,CAACpB,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvCmB,WAAW,CAACpB,KAAK,CAACE,GAAG,GAAG,MAAM;IAC9BkB,WAAW,CAACpB,KAAK,CAACsB,KAAK,GAAG,MAAM;IAChCF,WAAW,CAACpB,KAAK,CAACuB,MAAM,GAAG,MAAM;IACjCH,WAAW,CAACpB,KAAK,CAACwB,UAAU,GAAG,MAAM;IACrCJ,WAAW,CAACpB,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACnCL,WAAW,CAACpB,KAAK,CAAC0B,MAAM,GAAG,SAAS;IACpCN,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnCE,WAAW,CAACpB,KAAK,CAAC2B,UAAU,GAAG,YAAY;IAC3CP,WAAW,CAACQ,WAAW,GAAG,MAAMR,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnEE,WAAW,CAACS,UAAU,GAAG,MAAMT,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IAClEE,WAAW,CAACU,OAAO,GAAG,MAAMhC,QAAQ,CAACiC,IAAI,CAACC,WAAW,CAACnC,QAAQ,CAAC;IAE/D;IACA,MAAMoC,MAAM,GAAGnC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5CkC,MAAM,CAACjC,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC7ByB,MAAM,CAACjC,KAAK,CAACU,UAAU,GAAG,QAAQ;IAClCuB,MAAM,CAACjC,KAAK,CAACkC,YAAY,GAAG,MAAM;IAClCD,MAAM,CAACjC,KAAK,CAACmC,aAAa,GAAG,MAAM;IACnCF,MAAM,CAACjC,KAAK,CAACoC,YAAY,GAAG,gBAAgB;IAE5C,MAAMC,IAAI,GAAGvC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CsC,IAAI,CAAChB,SAAS,GAAG,2FAA2F;IAE5G,MAAMiB,KAAK,GAAGxC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IAC1CuC,KAAK,CAACC,WAAW,GAAG,yBAAyB;IAC7CD,KAAK,CAACtC,KAAK,CAACwC,MAAM,GAAG,GAAG;IACxBF,KAAK,CAACtC,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC7Ba,KAAK,CAACtC,KAAK,CAACyC,UAAU,GAAG,KAAK;IAC9BH,KAAK,CAACtC,KAAK,CAACkB,KAAK,GAAG,SAAS;IAE7Be,MAAM,CAACS,WAAW,CAACL,IAAI,CAAC;IACxBJ,MAAM,CAACS,WAAW,CAACJ,KAAK,CAAC;IAEzB;IACA,MAAMK,gBAAgB,GAAG7C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACtD4C,gBAAgB,CAAC3C,KAAK,CAACQ,OAAO,GAAG,MAAM;IACvCmC,gBAAgB,CAAC3C,KAAK,CAAC4C,aAAa,GAAG,QAAQ;IAC/CD,gBAAgB,CAAC3C,KAAK,CAAC6C,GAAG,GAAG,MAAM;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9E;IACA,IAAInD,MAAM,CAACxD,KAAK,IAAIwD,MAAM,CAACxD,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAM0G,IAAI,GAAGpD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,IAAI4G,IAAI,CAACC,OAAO,EAAE;QAChB,MAAMC,WAAW,GAAGpD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDmD,WAAW,CAAClD,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClC0C,WAAW,CAAClD,KAAK,CAACU,UAAU,GAAG,QAAQ;QACvCwC,WAAW,CAAClD,KAAK,CAACkC,YAAY,GAAG,MAAM;QAEvC;QACA,IAAIc,IAAI,CAACC,OAAO,CAACE,aAAa,EAAE;UAC9B,MAAMC,WAAW,GAAGtD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDqD,WAAW,CAACC,GAAG,GAAGL,IAAI,CAACC,OAAO,CAACE,aAAa;UAC5CC,WAAW,CAACE,GAAG,GAAGN,IAAI,CAACC,OAAO,CAAC5J,IAAI;UACnC+J,WAAW,CAACpD,KAAK,CAACK,MAAM,GAAG,MAAM;UACjC+C,WAAW,CAACpD,KAAK,CAACuD,WAAW,GAAG,MAAM;UACtCL,WAAW,CAACR,WAAW,CAACU,WAAW,CAAC;SACrC,MAAM;UACL,MAAMI,WAAW,GAAG1D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDyD,WAAW,CAACnC,SAAS,GAAG,wFAAwF;UAChH6B,WAAW,CAACR,WAAW,CAACc,WAAW,CAAC;;QAGtC,MAAMC,WAAW,GAAG3D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjD0D,WAAW,CAACpC,SAAS,GAAG,WAAW2B,IAAI,CAACC,OAAO,CAAC5J,IAAI,cAAc2J,IAAI,CAACC,OAAO,CAACS,iBAAiB,GAAG;QACnGD,WAAW,CAACzD,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCyB,WAAW,CAACR,WAAW,CAACe,WAAW,CAAC;QAEpCX,WAAW,CAACJ,WAAW,CAACQ,WAAW,CAAC;;MAGtC;MACA,MAAMS,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC,eAAe,EAAEZ,IAAI,CAACa,QAAQ,IAAI,KAAK,CAAC;MACnFf,WAAW,CAACJ,WAAW,CAACiB,eAAe,CAAC;MAExC;MACA,MAAMG,aAAa,GAAG,IAAI,CAACF,aAAa,CAAC,aAAa,EAAE,IAAI/F,IAAI,CAACmF,IAAI,CAACe,UAAU,CAAC,CAACC,kBAAkB,EAAE,CAAC;MACvGlB,WAAW,CAACJ,WAAW,CAACoB,aAAa,CAAC;MAEtC;MACA,MAAMG,WAAW,GAAG,IAAI,CAACL,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC/H,cAAc,CAACmH,IAAI,CAACkB,QAAQ,CAAC,CAAC;MACtFpB,WAAW,CAACJ,WAAW,CAACuB,WAAW,CAAC;MAEpC;MACA,IAAIjB,IAAI,CAAClE,WAAW,EAAE;QACpB,MAAMqF,QAAQ,GAAG,IAAI,CAACP,aAAa,CAAC,OAAO,EAAE,GAAGZ,IAAI,CAAClE,WAAW,CAACzF,IAAI,KAAK2J,IAAI,CAAClE,WAAW,CAACxF,IAAI,GAAG,CAAC;QACnGwJ,WAAW,CAACJ,WAAW,CAACyB,QAAQ,CAAC;;MAGnC;MACA,MAAMC,QAAQ,GAAG,IAAI,CAACR,aAAa,CAAC,OAAO,EAAEZ,IAAI,CAACqB,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,GAAGrB,IAAI,CAACqB,SAAS,UAAU,CAAC;MAClHvB,WAAW,CAACJ,WAAW,CAAC0B,QAAQ,CAAC;;IAGnC;IACA,MAAME,YAAY,GAAG,IAAI,CAACvB,aAAa,CAAC,eAAe,EAAE,UAAU,CAAC;IAEpE,IAAInD,MAAM,CAACxD,KAAK,IAAIwD,MAAM,CAACxD,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAM0G,IAAI,GAAGpD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,MAAMmI,WAAW,GAAGzE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACjDwE,WAAW,CAACvE,KAAK,CAACQ,OAAO,GAAG,MAAM;MAClC+D,WAAW,CAACvE,KAAK,CAACU,UAAU,GAAG,QAAQ;MACvC6D,WAAW,CAACvE,KAAK,CAACS,cAAc,GAAG,eAAe;MAClD8D,WAAW,CAACvE,KAAK,CAACwC,MAAM,GAAG,QAAQ;MACnC+B,WAAW,CAACvE,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEvC;MACA,MAAMuE,SAAS,GAAG1E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/CyE,SAAS,CAACxE,KAAK,CAACyE,SAAS,GAAG,QAAQ;MACpCD,SAAS,CAACxE,KAAK,CAAC0E,IAAI,GAAG,GAAG;MAE1B,IAAI1B,IAAI,CAACwB,SAAS,EAAE;QAClB,MAAMG,aAAa,GAAG7E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnD4E,aAAa,CAAC3E,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCkD,aAAa,CAAC3E,KAAK,CAACyC,UAAU,GAAG,MAAM;QACvCkC,aAAa,CAACpC,WAAW,GAAG,IAAI1E,IAAI,CAACmF,IAAI,CAACwB,SAAS,CAACI,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAErH,MAAMC,gBAAgB,GAAGlF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACtDiF,gBAAgB,CAAChF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACxCuD,gBAAgB,CAAChF,KAAK,CAACiF,SAAS,GAAG,KAAK;QACxCD,gBAAgB,CAAC3D,SAAS,GAAG,WAAW2B,IAAI,CAACwB,SAAS,CAACU,OAAO,EAAE5L,IAAI,IAAI,KAAK,WAAW;QAExF,MAAM6L,aAAa,GAAGrF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnDoF,aAAa,CAACnF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC0D,aAAa,CAACnF,KAAK,CAACkB,KAAK,GAAG,MAAM;QAClCiE,aAAa,CAAC5C,WAAW,GAAGS,IAAI,CAACwB,SAAS,CAACxL,IAAI,EAAEK,IAAI,IAAI,KAAK;QAE9DmL,SAAS,CAAC9B,WAAW,CAACiC,aAAa,CAAC;QACpCH,SAAS,CAAC9B,WAAW,CAACsC,gBAAgB,CAAC;QACvCR,SAAS,CAAC9B,WAAW,CAACyC,aAAa,CAAC;;MAGtC;MACA,MAAMC,cAAc,GAAGtF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACpDqF,cAAc,CAACpF,KAAK,CAAC0E,IAAI,GAAG,GAAG;MAC/BU,cAAc,CAACpF,KAAK,CAACQ,OAAO,GAAG,MAAM;MACrC4E,cAAc,CAACpF,KAAK,CAACU,UAAU,GAAG,QAAQ;MAC1C0E,cAAc,CAACpF,KAAK,CAACS,cAAc,GAAG,QAAQ;MAC9C2E,cAAc,CAACpF,KAAK,CAACY,OAAO,GAAG,QAAQ;MAEvC,MAAMyE,IAAI,GAAGvF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC1CsF,IAAI,CAACrF,KAAK,CAACK,MAAM,GAAG,KAAK;MACzBgF,IAAI,CAACrF,KAAK,CAACM,eAAe,GAAG,MAAM;MACnC+E,IAAI,CAACrF,KAAK,CAACI,KAAK,GAAG,MAAM;MACzBiF,IAAI,CAACrF,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEhC,MAAMqF,KAAK,GAAGxF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC3CuF,KAAK,CAACjE,SAAS,GAAG,iGAAiG;MACnHiE,KAAK,CAACtF,KAAK,CAACC,QAAQ,GAAG,UAAU;MACjCqF,KAAK,CAACtF,KAAK,CAACE,GAAG,GAAG,MAAM;MACxBoF,KAAK,CAACtF,KAAK,CAACG,IAAI,GAAG,KAAK;MACxBmF,KAAK,CAACtF,KAAK,CAACuF,UAAU,GAAG,MAAM;MAC/BD,KAAK,CAACtF,KAAK,CAACM,eAAe,GAAG,OAAO;MACrCgF,KAAK,CAACtF,KAAK,CAACY,OAAO,GAAG,OAAO;MAE7ByE,IAAI,CAAC3C,WAAW,CAAC4C,KAAK,CAAC;MACvBF,cAAc,CAAC1C,WAAW,CAAC2C,IAAI,CAAC;MAEhC;MACA,MAAMG,OAAO,GAAG1F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CyF,OAAO,CAACxF,KAAK,CAACyE,SAAS,GAAG,QAAQ;MAClCe,OAAO,CAACxF,KAAK,CAAC0E,IAAI,GAAG,GAAG;MAExB,IAAI1B,IAAI,CAACwC,OAAO,EAAE;QAChB,MAAMC,WAAW,GAAG3F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjD0F,WAAW,CAACzF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCgE,WAAW,CAACzF,KAAK,CAACyC,UAAU,GAAG,MAAM;QACrCgD,WAAW,CAAClD,WAAW,GAAG,IAAI1E,IAAI,CAACmF,IAAI,CAACwC,OAAO,CAACZ,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAEjH,MAAMW,cAAc,GAAG5F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACpD2F,cAAc,CAAC1F,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACtCiE,cAAc,CAAC1F,KAAK,CAACiF,SAAS,GAAG,KAAK;QACtCS,cAAc,CAACrE,SAAS,GAAG,WAAW2B,IAAI,CAACwC,OAAO,CAACN,OAAO,EAAE5L,IAAI,IAAI,KAAK,WAAW;QAEpF,MAAMqM,WAAW,GAAG7F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjD4F,WAAW,CAAC3F,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCkE,WAAW,CAAC3F,KAAK,CAACkB,KAAK,GAAG,MAAM;QAChCyE,WAAW,CAACpD,WAAW,GAAGS,IAAI,CAACwC,OAAO,CAACxM,IAAI,EAAEK,IAAI,IAAI,KAAK;QAE1DmM,OAAO,CAAC9C,WAAW,CAAC+C,WAAW,CAAC;QAChCD,OAAO,CAAC9C,WAAW,CAACgD,cAAc,CAAC;QACnCF,OAAO,CAAC9C,WAAW,CAACiD,WAAW,CAAC;;MAGlCpB,WAAW,CAAC7B,WAAW,CAAC8B,SAAS,CAAC;MAClCD,WAAW,CAAC7B,WAAW,CAAC0C,cAAc,CAAC;MACvCb,WAAW,CAAC7B,WAAW,CAAC8C,OAAO,CAAC;MAEhClB,YAAY,CAAC5B,WAAW,CAAC6B,WAAW,CAAC;MAErC;MACA,IAAIvB,IAAI,CAAC3G,QAAQ,IAAI2G,IAAI,CAAC3G,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMsJ,aAAa,GAAG9F,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAClD6F,aAAa,CAACrD,WAAW,GAAG,iBAAiB;QAC7CqD,aAAa,CAAC5F,KAAK,CAACiF,SAAS,GAAG,MAAM;QACtCW,aAAa,CAAC5F,KAAK,CAACkC,YAAY,GAAG,MAAM;QACzC0D,aAAa,CAAC5F,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCmE,aAAa,CAAC5F,KAAK,CAACyC,UAAU,GAAG,KAAK;QACtC6B,YAAY,CAAC5B,WAAW,CAACkD,aAAa,CAAC;QAEvC,MAAMC,YAAY,GAAG/F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClD8F,YAAY,CAAC7F,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnCqF,YAAY,CAAC7F,KAAK,CAAC4C,aAAa,GAAG,QAAQ;QAC3CiD,YAAY,CAAC7F,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/BG,IAAI,CAAC3G,QAAQ,CAACyJ,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAI;UACvC,MAAMC,WAAW,GAAGnG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDkG,WAAW,CAACjG,KAAK,CAACY,OAAO,GAAG,MAAM;UAClCqF,WAAW,CAACjG,KAAK,CAACM,eAAe,GAAG,SAAS;UAC7C2F,WAAW,CAACjG,KAAK,CAACa,YAAY,GAAG,KAAK;UACtCoF,WAAW,CAACjG,KAAK,CAACuB,MAAM,GAAG,gBAAgB;UAE3C,MAAM2E,aAAa,GAAGpG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACnDmG,aAAa,CAAClG,KAAK,CAACQ,OAAO,GAAG,MAAM;UACpC0F,aAAa,CAAClG,KAAK,CAACS,cAAc,GAAG,eAAe;UACpDyF,aAAa,CAAClG,KAAK,CAACkC,YAAY,GAAG,MAAM;UACzCgE,aAAa,CAAClG,KAAK,CAACmC,aAAa,GAAG,MAAM;UAC1C+D,aAAa,CAAClG,KAAK,CAACoC,YAAY,GAAG,gBAAgB;UAEnD,MAAM+D,YAAY,GAAGrG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClDoG,YAAY,CAAC9E,SAAS,GAAG,mBAAmB2E,KAAK,GAAG,CAAC,cAAcD,OAAO,CAAC9C,OAAO,EAAE5J,IAAI,IAAI,SAAS,IAAI0M,OAAO,CAAClC,QAAQ,EAAE;UAE3H,MAAMuC,eAAe,GAAGtG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACrDqG,eAAe,CAAC7D,WAAW,GAAG,IAAI,CAAC1G,cAAc,CAACkK,OAAO,CAAC7B,QAAQ,CAAC;UACnEkC,eAAe,CAACpG,KAAK,CAACkB,KAAK,GAAG,MAAM;UAEpCgF,aAAa,CAACxD,WAAW,CAACyD,YAAY,CAAC;UACvCD,aAAa,CAACxD,WAAW,CAAC0D,eAAe,CAAC;UAC1CH,WAAW,CAACvD,WAAW,CAACwD,aAAa,CAAC;UAEtC;UACA,MAAMG,YAAY,GAAGvG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClDsG,YAAY,CAACrG,KAAK,CAACQ,OAAO,GAAG,MAAM;UACnC6F,YAAY,CAACrG,KAAK,CAACU,UAAU,GAAG,QAAQ;UACxC2F,YAAY,CAACrG,KAAK,CAACS,cAAc,GAAG,eAAe;UAEnD;UACA,MAAM6F,gBAAgB,GAAGxG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACtDuG,gBAAgB,CAACtG,KAAK,CAAC0E,IAAI,GAAG,GAAG;UAEjC,IAAIqB,OAAO,CAACvB,SAAS,EAAE;YACrB,MAAM+B,OAAO,GAAGzG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CwG,OAAO,CAACvG,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjC8D,OAAO,CAAChE,WAAW,GAAG,IAAI1E,IAAI,CAACkI,OAAO,CAACvB,SAAS,CAACI,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAElH,MAAMyB,UAAU,GAAG1G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChDyG,UAAU,CAACxG,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClC+E,UAAU,CAACjE,WAAW,GAAG,GAAGwD,OAAO,CAACvB,SAAS,CAACU,OAAO,EAAE5L,IAAI,IAAI,KAAK,KAAKyM,OAAO,CAACvB,SAAS,CAACxL,IAAI,EAAEK,IAAI,IAAI,KAAK,GAAG;YAEjHiN,gBAAgB,CAAC5D,WAAW,CAAC6D,OAAO,CAAC;YACrCD,gBAAgB,CAAC5D,WAAW,CAAC8D,UAAU,CAAC;;UAG1C;UACA,MAAMC,KAAK,GAAG3G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAC3C0G,KAAK,CAACpF,SAAS,GAAG,qFAAqF;UAEvG;UACA,MAAMqF,cAAc,GAAG5G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACpD2G,cAAc,CAAC1G,KAAK,CAAC0E,IAAI,GAAG,GAAG;UAC/BgC,cAAc,CAAC1G,KAAK,CAACyE,SAAS,GAAG,OAAO;UAExC,IAAIsB,OAAO,CAACP,OAAO,EAAE;YACnB,MAAMmB,OAAO,GAAG7G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7C4G,OAAO,CAAC3G,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCkE,OAAO,CAACpE,WAAW,GAAG,IAAI1E,IAAI,CAACkI,OAAO,CAACP,OAAO,CAACZ,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAEhH,MAAM6B,UAAU,GAAG9G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChD6G,UAAU,CAAC5G,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClCmF,UAAU,CAACrE,WAAW,GAAG,GAAGwD,OAAO,CAACP,OAAO,CAACN,OAAO,EAAE5L,IAAI,IAAI,KAAK,KAAKyM,OAAO,CAACP,OAAO,CAACxM,IAAI,EAAEK,IAAI,IAAI,KAAK,GAAG;YAE7GqN,cAAc,CAAChE,WAAW,CAACiE,OAAO,CAAC;YACnCD,cAAc,CAAChE,WAAW,CAACkE,UAAU,CAAC;;UAGxCP,YAAY,CAAC3D,WAAW,CAAC4D,gBAAgB,CAAC;UAC1CD,YAAY,CAAC3D,WAAW,CAAC+D,KAAK,CAAC;UAC/BJ,YAAY,CAAC3D,WAAW,CAACgE,cAAc,CAAC;UACxCT,WAAW,CAACvD,WAAW,CAAC2D,YAAY,CAAC;UAErCR,YAAY,CAACnD,WAAW,CAACuD,WAAW,CAAC;UAErC;UACA,IAAID,KAAK,GAAGhD,IAAI,CAAC3G,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACpC,MAAMuK,OAAO,GAAG/G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7C8G,OAAO,CAAC7G,KAAK,CAACyE,SAAS,GAAG,QAAQ;YAClCoC,OAAO,CAAC7G,KAAK,CAACY,OAAO,GAAG,MAAM;YAC9BiG,OAAO,CAAC7G,KAAK,CAACkB,KAAK,GAAG,SAAS;YAC/B2F,OAAO,CAAC7G,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAE/B;YACA,MAAMqF,cAAc,GAAG,IAAIjJ,IAAI,CAACkI,OAAO,CAACP,OAAO,EAAEZ,IAAI,IAAI,CAAC,CAAC,CAACmC,OAAO,EAAE;YACrE,MAAMC,aAAa,GAAG,IAAInJ,IAAI,CAACmF,IAAI,CAAC3G,QAAQ,CAAC2J,KAAK,GAAG,CAAC,CAAC,CAACxB,SAAS,EAAEI,IAAI,IAAI,CAAC,CAAC,CAACmC,OAAO,EAAE;YACvF,MAAME,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,aAAa,GAAGF,cAAc,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;YAE9ED,OAAO,CAACxF,SAAS,GAAG,yCAAyC,IAAI,CAACxF,cAAc,CAACoL,WAAW,CAAC,eAAelB,OAAO,CAACP,OAAO,EAAExM,IAAI,EAAEK,IAAI,IAAI,iBAAiB,EAAE;YAE9JwM,YAAY,CAACnD,WAAW,CAACmE,OAAO,CAAC;;QAErC,CAAC,CAAC;QAEFvC,YAAY,CAAC5B,WAAW,CAACmD,YAAY,CAAC;;;IAI1C;IACA,MAAMuB,aAAa,GAAG,IAAI,CAACrE,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAE5D,IAAInD,MAAM,CAACvF,MAAM,IAAIuF,MAAM,CAACvF,MAAM,CAACiC,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAM+K,UAAU,GAAGvH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAChDsH,UAAU,CAACrH,KAAK,CAACQ,OAAO,GAAG,MAAM;MACjC6G,UAAU,CAACrH,KAAK,CAAC4C,aAAa,GAAG,QAAQ;MACzCyE,UAAU,CAACrH,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE7BjD,MAAM,CAACvF,MAAM,CAACyL,OAAO,CAAC,CAACwB,KAAK,EAAEtB,KAAK,KAAI;QACrC,MAAMuB,SAAS,GAAGzH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC/CwH,SAAS,CAACvH,KAAK,CAACY,OAAO,GAAG,MAAM;QAChC2G,SAAS,CAACvH,KAAK,CAACM,eAAe,GAAG,SAAS;QAC3CiH,SAAS,CAACvH,KAAK,CAACa,YAAY,GAAG,KAAK;QACpC0G,SAAS,CAACvH,KAAK,CAACuB,MAAM,GAAG,gBAAgB;QAEzC;QACA,MAAMiG,WAAW,GAAG1H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDyH,WAAW,CAACxH,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCgH,WAAW,CAACxH,KAAK,CAACS,cAAc,GAAG,eAAe;QAClD+G,WAAW,CAACxH,KAAK,CAACkC,YAAY,GAAG,MAAM;QACvCsF,WAAW,CAACxH,KAAK,CAACmC,aAAa,GAAG,MAAM;QACxCqF,WAAW,CAACxH,KAAK,CAACoC,YAAY,GAAG,gBAAgB;QAEjD,MAAMqF,UAAU,GAAG3H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChD0H,UAAU,CAACpG,SAAS,GAAG,iBAAiB2E,KAAK,GAAG,CAAC,WAAW;QAC5DyB,UAAU,CAACzH,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAElC,MAAMiG,UAAU,GAAG5H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChD2H,UAAU,CAAC1H,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAClCiG,UAAU,CAAC1H,KAAK,CAACyC,UAAU,GAAG,MAAM;QACpCiF,UAAU,CAAC1H,KAAK,CAACkB,KAAK,GAAG,SAAS;QAClCwG,UAAU,CAACnF,WAAW,GAAG,GAAG+E,KAAK,CAACK,KAAK,CAACC,MAAM,IAAIN,KAAK,CAACK,KAAK,CAAC1I,QAAQ,EAAE;QAExEuI,WAAW,CAAC9E,WAAW,CAAC+E,UAAU,CAAC;QACnCD,WAAW,CAAC9E,WAAW,CAACgF,UAAU,CAAC;QACnCH,SAAS,CAAC7E,WAAW,CAAC8E,WAAW,CAAC;QAElC;QACA,MAAMK,YAAY,GAAG/H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClD8H,YAAY,CAAC7H,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnCqH,YAAY,CAAC7H,KAAK,CAAC8H,mBAAmB,GAAG,uCAAuC;QAChFD,YAAY,CAAC7H,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B;QACA,MAAMkF,OAAO,GAAG,IAAI,CAACnE,aAAa,CAAC,UAAU,EAAE0D,KAAK,CAACS,OAAO,IAAIT,KAAK,CAAC/L,EAAE,IAAI,KAAK,CAAC;QAClFwM,OAAO,CAAC/H,KAAK,CAACgI,UAAU,GAAG,QAAQ;QACnCH,YAAY,CAACnF,WAAW,CAACqF,OAAO,CAAC;QAEjC;QACA,MAAME,iBAAiB,GAAGX,KAAK,CAACY,YAAY,KAAKC,SAAS,GAAGb,KAAK,CAACY,YAAY,GACrDZ,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACC,kBAAkB,GAAG,CAAE;QACjF,MAAMH,YAAY,GAAG,IAAI,CAACtE,aAAa,CAAC,cAAc,EAAEqE,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,eAAe,CAAC;QAC9GJ,YAAY,CAACnF,WAAW,CAACwF,YAAY,CAAC;QAEtC;QACA,IAAIZ,KAAK,CAACgB,SAAS,EAAE;UACnB,MAAMC,OAAO,GAAG,IAAI,CAAC3E,aAAa,CAAC,YAAY,EAAE,IAAI/F,IAAI,CAACyJ,KAAK,CAACgB,SAAS,CAAC,CAACE,cAAc,EAAE,CAAC;UAC5FX,YAAY,CAACnF,WAAW,CAAC6F,OAAO,CAAC;;QAGnC;QACA,IAAIjB,KAAK,CAACmB,WAAW,EAAE;UACrB,MAAMA,WAAW,GAAG,IAAI,CAAC7E,aAAa,CAAC,cAAc,EAAE0D,KAAK,CAACmB,WAAW,CAACpP,IAAI,CAAC;UAC9EwO,YAAY,CAACnF,WAAW,CAAC+F,WAAW,CAAC;;QAGvC;QACA,IAAInB,KAAK,CAACoB,cAAc,EAAE;UACxB,MAAMC,UAAU,GAAG,IAAI,CAAC/E,aAAa,CAAC,YAAY,EAAE0D,KAAK,CAACoB,cAAc,CAACC,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC;UACnGd,YAAY,CAACnF,WAAW,CAACiG,UAAU,CAAC;;QAGtCpB,SAAS,CAAC7E,WAAW,CAACmF,YAAY,CAAC;QAEnC;QACA,IAAIP,KAAK,CAACsB,mBAAmB,IAAItB,KAAK,CAACsB,mBAAmB,CAACtM,MAAM,GAAG,CAAC,EAAE;UACrE,MAAMuM,YAAY,GAAG/I,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;UACjD8I,YAAY,CAACtG,WAAW,GAAG,qBAAqB;UAChDsG,YAAY,CAAC7I,KAAK,CAACiF,SAAS,GAAG,MAAM;UACrC4D,YAAY,CAAC7I,KAAK,CAACkC,YAAY,GAAG,MAAM;UACxC2G,YAAY,CAAC7I,KAAK,CAACyB,QAAQ,GAAG,MAAM;UACpCoH,YAAY,CAAC7I,KAAK,CAACyC,UAAU,GAAG,KAAK;UACrC8E,SAAS,CAAC7E,WAAW,CAACmG,YAAY,CAAC;UAEnC,MAAMC,WAAW,GAAGhJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;UAChD+I,WAAW,CAAC9I,KAAK,CAAC+I,SAAS,GAAG,MAAM;UACpCD,WAAW,CAAC9I,KAAK,CAACY,OAAO,GAAG,GAAG;UAC/BkI,WAAW,CAAC9I,KAAK,CAACwC,MAAM,GAAG,GAAG;UAE9B8E,KAAK,CAACsB,mBAAmB,CAAC9C,OAAO,CAACkD,OAAO,IAAG;YAC1C,MAAMC,WAAW,GAAGnJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;YAChDkJ,WAAW,CAACjJ,KAAK,CAACkC,YAAY,GAAG,KAAK;YACtC+G,WAAW,CAAC5H,SAAS,GAAG,2EAA2E,IAAI,CAAC6H,kBAAkB,CAACF,OAAO,CAACG,WAAW,CAAC,EAAE;YACjJL,WAAW,CAACpG,WAAW,CAACuG,WAAW,CAAC;UACtC,CAAC,CAAC;UAEF1B,SAAS,CAAC7E,WAAW,CAACoG,WAAW,CAAC;;QAGpCzB,UAAU,CAAC3E,WAAW,CAAC6E,SAAS,CAAC;MACnC,CAAC,CAAC;MAEFH,aAAa,CAAC1E,WAAW,CAAC2E,UAAU,CAAC;;IAGvC;IACA,IAAIzH,MAAM,CAACxD,KAAK,IAAIwD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,IAAIwD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACgN,QAAQ,IAAIxJ,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACgN,QAAQ,CAAC9M,MAAM,GAAG,CAAC,EAAE;MACtG,MAAM+M,eAAe,GAAG,IAAI,CAACtG,aAAa,CAAC,UAAU,EAAE,mBAAmB,CAAC;MAE3E,MAAMuG,YAAY,GAAGxJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;MACjDuJ,YAAY,CAACtJ,KAAK,CAAC+I,SAAS,GAAG,MAAM;MACrCO,YAAY,CAACtJ,KAAK,CAACY,OAAO,GAAG,GAAG;MAChC0I,YAAY,CAACtJ,KAAK,CAACwC,MAAM,GAAG,GAAG;MAC/B8G,YAAY,CAACtJ,KAAK,CAACQ,OAAO,GAAG,MAAM;MACnC8I,YAAY,CAACtJ,KAAK,CAAC8H,mBAAmB,GAAG,uCAAuC;MAChFwB,YAAY,CAACtJ,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE/BjD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACgN,QAAQ,CAACtD,OAAO,CAACyD,OAAO,IAAG;QACzC,MAAMC,WAAW,GAAG1J,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAChDyJ,WAAW,CAACxJ,KAAK,CAACY,OAAO,GAAG,MAAM;QAClC4I,WAAW,CAACxJ,KAAK,CAACM,eAAe,GAAG,SAAS;QAC7CkJ,WAAW,CAACxJ,KAAK,CAACa,YAAY,GAAG,KAAK;QACtC2I,WAAW,CAACnI,SAAS,GAAG,2EAA2EkI,OAAO,CAAClQ,IAAI,IAAI,SAAS,EAAE;QAC9HiQ,YAAY,CAAC5G,WAAW,CAAC8G,WAAW,CAAC;MACvC,CAAC,CAAC;MAEFH,eAAe,CAAC3G,WAAW,CAAC4G,YAAY,CAAC;MACzC3G,gBAAgB,CAACD,WAAW,CAAC2G,eAAe,CAAC;;IAG/C;IACA1G,gBAAgB,CAACD,WAAW,CAACI,WAAW,CAAC;IACzCH,gBAAgB,CAACD,WAAW,CAAC4B,YAAY,CAAC;IAC1C3B,gBAAgB,CAACD,WAAW,CAAC0E,aAAa,CAAC;IAE3C;IACAzG,YAAY,CAAC+B,WAAW,CAACtB,WAAW,CAAC;IACrCT,YAAY,CAAC+B,WAAW,CAACT,MAAM,CAAC;IAChCtB,YAAY,CAAC+B,WAAW,CAACC,gBAAgB,CAAC;IAC1C9C,QAAQ,CAAC6C,WAAW,CAAC/B,YAAY,CAAC;IAElC;IACAb,QAAQ,CAACiC,IAAI,CAACW,WAAW,CAAC7C,QAAQ,CAAC;EACrC;EAEA;EACQkD,aAAaA,CAACT,KAAa,EAAEmH,SAAiB;IACpD,MAAMC,OAAO,GAAG5J,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7C2J,OAAO,CAAC1J,KAAK,CAACM,eAAe,GAAG,SAAS;IACzCoJ,OAAO,CAAC1J,KAAK,CAACa,YAAY,GAAG,KAAK;IAClC6I,OAAO,CAAC1J,KAAK,CAACY,OAAO,GAAG,MAAM;IAC9B8I,OAAO,CAAC1J,KAAK,CAACiB,SAAS,GAAG,+BAA+B;IAEzD,MAAM0I,aAAa,GAAG7J,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACnD4J,aAAa,CAAC3J,KAAK,CAACQ,OAAO,GAAG,MAAM;IACpCmJ,aAAa,CAAC3J,KAAK,CAACU,UAAU,GAAG,QAAQ;IACzCiJ,aAAa,CAAC3J,KAAK,CAACkC,YAAY,GAAG,MAAM;IAEzC,MAAM0H,IAAI,GAAG9J,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC6J,IAAI,CAACC,SAAS,GAAG,OAAOJ,SAAS,EAAE;IACnCG,IAAI,CAAC5J,KAAK,CAACkB,KAAK,GAAG,SAAS;IAC5B0I,IAAI,CAAC5J,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC5BmI,IAAI,CAAC5J,KAAK,CAACuD,WAAW,GAAG,MAAM;IAE/B,MAAMuG,YAAY,GAAGhK,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IACjD+J,YAAY,CAACvH,WAAW,GAAGD,KAAK;IAChCwH,YAAY,CAAC9J,KAAK,CAACwC,MAAM,GAAG,GAAG;IAC/BsH,YAAY,CAAC9J,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCqI,YAAY,CAAC9J,KAAK,CAACyC,UAAU,GAAG,KAAK;IAErCkH,aAAa,CAACjH,WAAW,CAACkH,IAAI,CAAC;IAC/BD,aAAa,CAACjH,WAAW,CAACoH,YAAY,CAAC;IACvCJ,OAAO,CAAChH,WAAW,CAACiH,aAAa,CAAC;IAElC,OAAOD,OAAO;EAChB;EAEA;EACQ9F,aAAaA,CAAChK,KAAa,EAAED,KAAa;IAChD,MAAMoQ,GAAG,GAAGjK,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACzCgK,GAAG,CAAC/J,KAAK,CAACkC,YAAY,GAAG,MAAM;IAE/B,MAAM8H,YAAY,GAAGlK,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDiK,YAAY,CAACzH,WAAW,GAAG3I,KAAK;IAChCoQ,YAAY,CAAChK,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCuI,YAAY,CAAChK,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjC8I,YAAY,CAAChK,KAAK,CAACkC,YAAY,GAAG,KAAK;IAEvC,MAAM+H,YAAY,GAAGnK,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDkK,YAAY,CAAC1H,WAAW,GAAG5I,KAAK;IAChCsQ,YAAY,CAACjK,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAEpCsI,GAAG,CAACrH,WAAW,CAACsH,YAAY,CAAC;IAC7BD,GAAG,CAACrH,WAAW,CAACuH,YAAY,CAAC;IAE7B,OAAOF,GAAG;EACZ;EAEA;EACAzO,gBAAgBA,CAACsE,MAAc;IAC7B,IAAIA,MAAM,IAAIA,MAAM,CAACvF,MAAM,IAAIuF,MAAM,CAACvF,MAAM,CAACiC,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAIyL,OAAO,GAAGnI,MAAM,CAACvF,MAAM,CAAC,CAAC,CAAC,CAAC0N,OAAO,IAAInI,MAAM,CAACvF,MAAM,CAAC,CAAC,CAAC,CAACkB,EAAE;MAE7D;MACA,IAAI2O,QAAQ,GAAG,IAAI,CAAC/M,YAAY;MAEhC;MACA,IAAI,CAAC+M,QAAQ,EAAE;QACbA,QAAQ,GAAGtK,MAAM,CAACrE,EAAE;;MAGtBmE,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEuK,QAAQ,EAAE,cAAc,EAAEnC,OAAO,CAAC;MAExF;MACA,IAAI,CAACjL,MAAM,CAACqN,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UACXF,QAAQ,EAAEA,QAAQ;UAClBnC,OAAO,EAAEA;;OAEZ,CAAC;KACH,MAAM;MACLrI,OAAO,CAAC2K,KAAK,CAAC,sCAAsC,EAAEzK,MAAM,CAAC;;EAEjE;EAEAH,gBAAgBA,CAAA;IACd;IACA;IACA;EAAA;EAGFD,iBAAiBA,CAAA;IACf;IACA,MAAMlB,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAACsM,GAAG,CAAC,uBAAuB,CAAC,EAAE3Q,KAAK,IAAI,CAAC;IACtF,MAAM6E,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAACsM,GAAG,CAAC,qBAAqB,CAAC,EAAE3Q,KAAK,IAAI,CAAC;IAElF,IAAI,CAACkD,cAAc,CAAC0N,kBAAkB,CAACjM,qBAAqB,CAAC,CAACkM,SAAS,CAACC,SAAS,IAAG;MAClF,IAAI,CAAC1N,kBAAkB,GAAG0N,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAAC5N,cAAc,CAAC0N,kBAAkB,CAAC/L,mBAAmB,CAAC,CAACgM,SAAS,CAACC,SAAS,IAAG;MAChF,IAAI,CAACzN,gBAAgB,GAAGyN,SAAS;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAACzM,UAAU,CAACsM,GAAG,CAAC,uBAAuB,CAAC,EAAEI,YAAY,CACvDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAAC9N,cAAc,CAAC0N,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAAC1N,kBAAkB,GAAG0N,SAAS;QACnC;QACA,IAAI,CAACzM,UAAU,CAACsM,GAAG,CAAC,mBAAmB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC5M,UAAU,CAACsM,GAAG,CAAC,qBAAqB,CAAC,EAAEI,YAAY,CACrDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAAC9N,cAAc,CAAC0N,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAACzN,gBAAgB,GAAGyN,SAAS;QACjC;QACA,IAAI,CAACzM,UAAU,CAACsM,GAAG,CAAC,iBAAiB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACtD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC5M,UAAU,CAACsM,GAAG,CAAC,mBAAmB,CAAC,EAAEI,YAAY,CACnDG,IAAI,CACH5S,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACwB,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMgR,YAAY,GAAG,IAAI,CAAC3M,UAAU,CAACsM,GAAG,CAAC,uBAAuB,CAAC,EAAE3Q,KAAK,IAAI,CAAC;QAC7E;QACA,IAAIA,KAAK,CAAC2C,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACO,cAAc,CAAC0N,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9DzS,GAAG,CAACqS,SAAS,IAAIA,SAAS,CAACK,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAAC1R,IAAI,CAAC2R,WAAW,EAAE,CAACC,QAAQ,CAACtR,KAAK,CAACqR,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACzR,IAAI,IAAIyR,QAAQ,CAACzR,IAAI,CAAC0R,WAAW,EAAE,CAACC,QAAQ,CAACtR,KAAK,CAACqR,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAACnO,cAAc,CAAC0N,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAOtS,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAmS,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAAC1N,kBAAkB,GAAG0N,SAAS;IACrC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACzM,UAAU,CAACsM,GAAG,CAAC,iBAAiB,CAAC,EAAEI,YAAY,CACjDG,IAAI,CACH5S,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACwB,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMgR,YAAY,GAAG,IAAI,CAAC3M,UAAU,CAACsM,GAAG,CAAC,qBAAqB,CAAC,EAAE3Q,KAAK,IAAI,CAAC;QAC3E;QACA,IAAIA,KAAK,CAAC2C,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACO,cAAc,CAAC0N,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9DzS,GAAG,CAACqS,SAAS,IAAIA,SAAS,CAACK,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAAC1R,IAAI,CAAC2R,WAAW,EAAE,CAACC,QAAQ,CAACtR,KAAK,CAACqR,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACzR,IAAI,IAAIyR,QAAQ,CAACzR,IAAI,CAAC0R,WAAW,EAAE,CAACC,QAAQ,CAACtR,KAAK,CAACqR,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAACnO,cAAc,CAAC0N,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAOtS,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAmS,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACzN,gBAAgB,GAAGyN,SAAS;IACnC,CAAC,CAAC;EACN;EAEAS,eAAeA,CAACH,QAA+B;IAC7C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAII,WAAW,GAAGJ,QAAQ,CAAC1R,IAAI;IAC/B,IAAI0R,QAAQ,CAACzR,IAAI,EAAE;MACjB6R,WAAW,IAAI,KAAKJ,QAAQ,CAACzR,IAAI,GAAG;;IAEtC,IAAIyR,QAAQ,CAACxR,IAAI,KAAKhB,YAAY,CAAC6S,OAAO,IAAIL,QAAQ,CAAC/R,IAAI,EAAE;MAC3DmS,WAAW,IAAI,MAAMJ,QAAQ,CAAC/R,IAAI,EAAE;;IAEtC,OAAOmS,WAAW;EACpB;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACrN,UAAU,CAACsN,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACvN,UAAU,CAAC;MAC1C;;IAGF,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,IAAI,CAAChD,YAAY,GAAG,EAAE;IACtB,IAAI,CAACiD,WAAW,GAAG,IAAI;IAEvB,MAAMsO,SAAS,GAAG,IAAI,CAACxN,UAAU,CAACrE,KAAK;IAEvC;IACA,MAAM8R,OAAO,GAAuB;MAClCC,WAAW,EAAEF,SAAS,CAACtN,WAAW;MAClCyN,YAAY,EAAEH,SAAS,CAACpN,YAAY;MACpCwN,OAAO,EAAEJ,SAAS,CAAC/M,aAAa;MAChCoN,kBAAkB,EAAE,CAClB;QACEtQ,EAAE,EAAEiQ,SAAS,CAACnN,iBAAiB,EAAE9C,EAAE,IAAI,EAAE;QACzChC,IAAI,EAAEiS,SAAS,CAAClN;OACjB,CACF;MACDwN,gBAAgB,EAAE,CAChB;QACEvQ,EAAE,EAAEiQ,SAAS,CAACjN,eAAe,EAAEhD,EAAE,IAAI,EAAE;QACvChC,IAAI,EAAEiS,SAAS,CAAChN;OACjB,CACF;MACDuN,UAAU,EAAE,CACV;QACExS,IAAI,EAAEiS,SAAS,CAAC3M,aAAa;QAC7BmN,KAAK,EAAER,SAAS,CAAC9M;OAClB,CACF;MACDuN,qBAAqB,EAAET,SAAS,CAACzM,OAAO;MACxCmN,oBAAoB,EAAE;QACpBC,oBAAoB,EAAE;UACpB7M,sBAAsB,EAAEkM,SAAS,CAAClM;;OAErC;MACDJ,sBAAsB,EAAEsM,SAAS,CAACtM,sBAAsB;MACxDC,wBAAwB,EAAEqM,SAAS,CAACrM,wBAAwB;MAC5DC,6BAA6B,EAAEoM,SAAS,CAACpM,6BAA6B;MACtEC,mBAAmB,EAAEmM,SAAS,CAACnM,mBAAmB;MAClD7B,aAAa,EAAE,CAACgO,SAAS,CAAC1M,WAAW,CAAC;MACtCsN,OAAO,EAAEZ,SAAS,CAACxM,OAAO;MAC1BqN,QAAQ,EAAEb,SAAS,CAACvM;KACrB;IAED,IAAI,CAACpC,cAAc,CAACyP,WAAW,CAACb,OAAO,CAAC,CACrCjB,SAAS,CAAC;MACT+B,IAAI,EAAGC,QAA6B,IAAI;QACtC,IAAI,CAACvP,SAAS,GAAG,KAAK;QACtB,IAAIuP,QAAQ,CAACvK,MAAM,CAACwK,OAAO,EAAE;UAC3B,IAAI,CAAChQ,aAAa,GAAG+P,QAAQ,CAACzK,IAAI,CAAC2K,OAAO;UAE1C;UACAhN,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgN,IAAI,CAACC,SAAS,CAACJ,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEpE;UACA,IAAIA,QAAQ,CAACzK,IAAI,IAAIyK,QAAQ,CAACzK,IAAI,CAAC2K,OAAO,IAAIF,QAAQ,CAACzK,IAAI,CAAC2K,OAAO,CAACpQ,MAAM,GAAG,CAAC,EAAE;YAC9EoD,OAAO,CAACzB,KAAK,CAAC,uBAAuB,CAAC;YACtCyB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE6M,QAAQ,CAACzK,IAAI,CAAC2K,OAAO,CAACpQ,MAAM,CAAC;YAE3D;YACA,MAAMuQ,iBAAiB,GAAGL,QAAQ,CAACzK,IAAI,CAAC2K,OAAO,CAAC5B,MAAM,CAACgC,CAAC,IAAIA,CAAC,CAACzS,MAAM,IAAIyS,CAAC,CAACzS,MAAM,CAACiC,MAAM,GAAG,CAAC,CAAC;YAC5FoD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEkN,iBAAiB,CAACvQ,MAAM,CAAC;YAE7D;YACA,MAAMyQ,kBAAkB,GAAGF,iBAAiB,CAACG,OAAO,CAACF,CAAC,IAAIA,CAAC,CAACzS,MAAM,CAACjC,GAAG,CAAC6U,CAAC,IACtEA,CAAC,CAAC/E,YAAY,KAAKC,SAAS,GAAG8E,CAAC,CAAC/E,YAAY,GAAI+E,CAAC,CAAC7E,QAAQ,GAAG6E,CAAC,CAAC7E,QAAQ,CAACC,kBAAkB,GAAG,CAAE,CACjG,CAAC;YACF3I,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoN,kBAAkB,CAAC;YAEvD;YACA,MAAMG,kBAAkB,GAAGH,kBAAkB,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAI;cAChE,IAAIA,GAAG,KAAKlF,SAAS,EAAE;gBACrBiF,GAAG,CAACC,GAAG,CAAC,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEhC,OAAOD,GAAG;YACZ,CAAC,EAAE,EAA4B,CAAC;YAChC1N,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEuN,kBAAkB,CAAC;YAEvD;YACA,MAAMI,iBAAiB,GAAGT,iBAAiB,CAAC/B,MAAM,CAACgC,CAAC,IAClDA,CAAC,CAACzS,MAAM,CAACkT,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACvE,cAAc,IAAIuE,CAAC,CAACvE,cAAc,CAACC,UAAU,KAAK,IAAI,CAAC,CAC7E;YACDjJ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE2N,iBAAiB,CAAChR,MAAM,CAAC;YAE5DoD,OAAO,CAAC8N,QAAQ,EAAE;;UAGpB;UACA,IAAIhB,QAAQ,CAACzK,IAAI,IAAIyK,QAAQ,CAACzK,IAAI,CAACmI,QAAQ,EAAE;YAC3C,IAAI,CAAC/M,YAAY,GAAGqP,QAAQ,CAACzK,IAAI,CAACmI,QAAQ;YAC1CxK,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACxC,YAAY,CAAC;;UAErE;UAAA,KACK,IAAIqP,QAAQ,CAACvK,MAAM,IAAIuK,QAAQ,CAACvK,MAAM,CAACwL,SAAS,EAAE;YACrD,IAAI,CAACtQ,YAAY,GAAGqP,QAAQ,CAACvK,MAAM,CAACwL,SAAS;YAC7C/N,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACxC,YAAY,CAAC;;UAExE;UAAA,KACK,IAAIqP,QAAQ,CAACzK,IAAI,IAAIyK,QAAQ,CAACzK,IAAI,CAAC2K,OAAO,IAAIF,QAAQ,CAACzK,IAAI,CAAC2K,OAAO,CAACpQ,MAAM,GAAG,CAAC,IAAIkQ,QAAQ,CAACzK,IAAI,CAAC2K,OAAO,CAAC,CAAC,CAAC,CAACnR,EAAE,EAAE;YAClH,IAAI,CAAC4B,YAAY,GAAGqP,QAAQ,CAACzK,IAAI,CAAC2K,OAAO,CAAC,CAAC,CAAC,CAACnR,EAAE;YAC/CmE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACxC,YAAY,CAAC;WAChE,MAAM;YACLuC,OAAO,CAAC2K,KAAK,CAAC,qCAAqC,CAAC;YACpD3K,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE+N,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAC,CAAC;YACzD,IAAIA,QAAQ,CAACzK,IAAI,EAAErC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+N,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAACzK,IAAI,CAAC,CAAC;YAC7E,IAAIyK,QAAQ,CAACvK,MAAM,EAAEvC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE+N,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAACvK,MAAM,CAAC,CAAC;;SAEtF,MAAM;UACL,IAAI,CAAChI,YAAY,GAAG,sDAAsD;UAC1E,IAAIuS,QAAQ,CAACvK,MAAM,CAAC2L,QAAQ,IAAIpB,QAAQ,CAACvK,MAAM,CAAC2L,QAAQ,CAACtR,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAACrC,YAAY,GAAGuS,QAAQ,CAACvK,MAAM,CAAC2L,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACDxD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpN,SAAS,GAAG,KAAK;QACtB,IAAI,CAAChD,YAAY,GAAG,wDAAwD;QAC5EyF,OAAO,CAAC2K,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEA;EACAkB,oBAAoBA,CAACuC,SAAoB;IACvCJ,MAAM,CAACK,MAAM,CAACD,SAAS,CAACE,QAAQ,CAAC,CAAClI,OAAO,CAACmI,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAYlW,SAAS,EAAE;QAChC,IAAI,CAACwT,oBAAoB,CAAC0C,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA;EACApS,cAAcA,CAACsS,OAAe;IAC5B,MAAMC,KAAK,GAAGlH,IAAI,CAACC,KAAK,CAACgH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,KAAK,KAAKC,IAAI,KAAK;EAC/B;EAEA;EACAC,UAAUA,CAACC,UAAkB;IAC3B,MAAM3J,IAAI,GAAG,IAAI/G,IAAI,CAAC0Q,UAAU,CAAC;IACjC,OAAO3J,IAAI,CAACZ,kBAAkB,CAAC,OAAO,EAAE;MACtCwK,OAAO,EAAE,OAAO;MAChBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACd5J,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACApJ,WAAWA,CAACiE,MAAc;IACxB,IAAI,CAACA,MAAM,CAACvF,MAAM,IAAIuF,MAAM,CAACvF,MAAM,CAACiC,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd,MAAMqS,QAAQ,GAAG/O,MAAM,CAACvF,MAAM,CAAC8S,MAAM,CAAC,CAACxO,GAAG,EAAE2I,KAAK,KAC/CA,KAAK,CAACK,KAAK,CAACC,MAAM,GAAGjJ,GAAG,CAACgJ,KAAK,CAACC,MAAM,GAAGN,KAAK,GAAG3I,GAAG,EAAEiB,MAAM,CAACvF,MAAM,CAAC,CAAC,CAAC,CAAC;IAExE,OAAOsU,QAAQ,CAAChH,KAAK,CAACiH,eAAe,IAAI,GAAGD,QAAQ,CAAChH,KAAK,CAACC,MAAM,IAAI+G,QAAQ,CAAChH,KAAK,CAAC1I,QAAQ,EAAE;EAChG;EAEA;EACAxD,gBAAgBA,CAACmE,MAAc;IAC7B,IAAI,CAACA,MAAM,EAAExD,KAAK,GAAG,CAAC,CAAC,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAEmI,SAAS,EAAExL,IAAI,EAAEK,IAAI,EAAE;MAC7D,OAAO,QAAQ;;IAEjB,OAAOuG,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACmI,SAAS,CAACxL,IAAI,CAACK,IAAI;EACxD;EAEAqC,cAAcA,CAACkE,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAExD,KAAK,GAAG,CAAC,CAAC,EAAEC,QAAQ,IAAIuD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1E,OAAO,SAAS;;IAElB,MAAMuS,WAAW,GAAGjP,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACuD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;IACjF,OAAOuS,WAAW,EAAErJ,OAAO,EAAExM,IAAI,EAAEK,IAAI,IAAI,SAAS;EACtD;EAEAuC,gBAAgBA,CAACgE,MAAc;IAC7B,IAAI,CAACA,MAAM,EAAExD,KAAK,GAAG,CAAC,CAAC,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAEmI,SAAS,EAAEI,IAAI,EAAE;MACvD,OAAO,OAAO;;IAEhB,MAAMA,IAAI,GAAG,IAAI/G,IAAI,CAAC+B,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACmI,SAAS,CAACI,IAAI,CAAC;IACjE,OAAOA,IAAI,CAACC,kBAAkB,CAAC,EAAE,EAAE;MAACC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAC;IAAS,CAAC,CAAC;EACzE;EAEAhJ,cAAcA,CAAC6D,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAExD,KAAK,GAAG,CAAC,CAAC,EAAEC,QAAQ,IAAIuD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1E,OAAO,OAAO;;IAEhB,MAAMuS,WAAW,GAAGjP,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACuD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;IACjF,IAAI,CAACuS,WAAW,EAAErJ,OAAO,EAAEZ,IAAI,EAAE;MAC/B,OAAO,OAAO;;IAEhB,MAAMA,IAAI,GAAG,IAAI/G,IAAI,CAACgR,WAAW,CAACrJ,OAAO,CAACZ,IAAI,CAAC;IAC/C,OAAOA,IAAI,CAACC,kBAAkB,CAAC,EAAE,EAAE;MAACC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAC;IAAS,CAAC,CAAC;EACzE;EAEAjJ,iBAAiBA,CAAC8D,MAAc;IAC9B,OAAOA,MAAM,EAAExD,KAAK,GAAG,CAAC,CAAC,EAAE8H,QAAQ,IAAI,CAAC;EAC1C;EAEAjI,QAAQA,CAAC2D,MAAc;IACrB,OAAOA,MAAM,EAAExD,KAAK,GAAG,CAAC,CAAC,EAAEC,QAAQ,IAAIuD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,MAAM,GAAG,CAAC;EAC5E;EAEAnC,aAAaA,CAACyF,MAAc;IAC1B,IAAI,CAACA,MAAM,EAAExD,KAAK,GAAG,CAAC,CAAC,EAAEC,QAAQ,EAAE;MACjC,OAAO,CAAC;;IAEV,OAAOuD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,MAAM,GAAG,CAAC;EAC5C;EAEAJ,uBAAuBA,CAAC0D,MAAc;IACpC,OAAOA,MAAM,EAAExD,KAAK,GAAG,CAAC,CAAC,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAEmI,SAAS,EAAEU,OAAO,EAAE5L,IAAI,IAAI,EAAE;EAC1E;EAEA6C,qBAAqBA,CAACyD,MAAc;IAClC,IAAI,CAACA,MAAM,EAAExD,KAAK,GAAG,CAAC,CAAC,EAAEC,QAAQ,IAAIuD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1E,OAAO,EAAE;;IAEX,MAAMuS,WAAW,GAAGjP,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACuD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;IACjF,OAAOuS,WAAW,EAAErJ,OAAO,EAAEN,OAAO,EAAE5L,IAAI,IAAI,EAAE;EAClD;EAEAwV,cAAcA,CAAClP,MAAc;IAC3B,OAAOA,MAAM,EAAExD,KAAK,GAAG,CAAC,CAAC,EAAEC,QAAQ,IAAIuD,MAAM,CAACxD,KAAK,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACC,MAAM,KAAK,CAAC;EAC9E;EAEAyS,YAAYA,CAACnP,MAAc;IACzB,OAAO,CAAC,CAACA,MAAM,EAAEvF,MAAM,GAAG,CAAC,CAAC,EAAEC,eAAe,EAAEjB,IAAI;EACrD;EAEA2V,YAAYA,CAACpP,MAAc;IACzB,OAAOA,MAAM,EAAEvF,MAAM,GAAG,CAAC,CAAC,EAAEC,eAAe,EAAEjB,IAAI,IAAI,EAAE;EACzD;EAEA;EACA4V,iBAAiBA,CAACrP,MAAc;IAC9B;IACA,IAAI,CAACA,MAAM,CAACvF,MAAM,IAAIuF,MAAM,CAACvF,MAAM,CAACiC,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd;IACA,MAAMgL,KAAK,GAAG1H,MAAM,CAACvF,MAAM,CAAC,CAAC,CAAC;IAC9B,MAAM4N,iBAAiB,GAAGX,KAAK,CAACY,YAAY,KAAKC,SAAS,GAAGb,KAAK,CAACY,YAAY,GACrDZ,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IACjF,OAAOJ,iBAAiB,GAAG,CAAC;EAC9B;EAEA;EACAiH,yBAAyBA,CAAA;IACvB,MAAMvE,YAAY,GAAG,IAAI,CAAC3M,UAAU,CAACsM,GAAG,CAAC,uBAAuB,CAAC,EAAE3Q,KAAK,IAAI,CAAC;IAC7E,IAAI,CAACkD,cAAc,CAAC0N,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAAC1N,kBAAkB,GAAG0N,SAAS;MACnC;MACA,MAAM0E,KAAK,GAAGrP,QAAQ,CAACsP,cAAc,CAAC,mBAAmB,CAAqB;MAC9E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAC,uBAAuBA,CAAA;IACrB,MAAM7E,YAAY,GAAG,IAAI,CAAC3M,UAAU,CAACsM,GAAG,CAAC,qBAAqB,CAAC,EAAE3Q,KAAK,IAAI,CAAC;IAC3E,IAAI,CAACkD,cAAc,CAAC0N,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACzN,gBAAgB,GAAGyN,SAAS;MACjC;MACA,MAAM0E,KAAK,GAAGrP,QAAQ,CAACsP,cAAc,CAAC,iBAAiB,CAAqB;MAC5E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAE,aAAaA,CAAA;IACX,MAAMpR,iBAAiB,GAAG,IAAI,CAACL,UAAU,CAACsM,GAAG,CAAC,mBAAmB,CAAC,EAAE3Q,KAAK;IACzE,MAAM2E,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAACsM,GAAG,CAAC,uBAAuB,CAAC,EAAE3Q,KAAK;IACjF,MAAM4E,eAAe,GAAG,IAAI,CAACP,UAAU,CAACsM,GAAG,CAAC,iBAAiB,CAAC,EAAE3Q,KAAK;IACrE,MAAM6E,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAACsM,GAAG,CAAC,qBAAqB,CAAC,EAAE3Q,KAAK;IAE7E,IAAI,CAACqE,UAAU,CAAC0R,UAAU,CAAC;MACzBrR,iBAAiB,EAAEE,eAAe;MAClCD,qBAAqB,EAAEE,mBAAmB;MAC1CD,eAAe,EAAEF,iBAAiB;MAClCG,mBAAmB,EAAEF;KACtB,CAAC;EACJ;EAEA;EACAqR,oBAAoBA,CAACpB,UAAkB;IACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B;IACA,MAAM3J,IAAI,GAAG,IAAI/G,IAAI,CAAC0Q,UAAU,CAAC;IACjC,OAAO3J,IAAI,CAAC4D,cAAc,EAAE;EAC9B;EAEA;EACAU,kBAAkBA,CAACC,WAAmB;IACpC,QAAQA,WAAW;MACjB,KAAK,CAAC;QAAE,OAAO,eAAe;MAC9B,KAAK,CAAC;QAAE,OAAO,iBAAiB;MAChC,KAAK,CAAC;QAAE,OAAO,cAAc;MAC7B;QAAS,OAAO,SAAS;;EAE7B;EAEA;EACAyG,oBAAoBA,CAAC/Q,aAAqB;IACxC,QAAQA,aAAa;MACnB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,WAAW;;EAE/B;EAEA;EACAgR,oBAAoBA,CAACC,cAAmB,EAAEC,WAAgB;IACxD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAACtK,OAAO,IAAI,CAACsK,cAAc,CAACtK,OAAO,CAACZ,IAAI,IAC1E,CAACmL,WAAW,IAAI,CAACA,WAAW,CAACvL,SAAS,IAAI,CAACuL,WAAW,CAACvL,SAAS,CAACI,IAAI,EAAE;MACzE,OAAO,SAAS;;IAGlB,MAAMa,WAAW,GAAG,IAAI5H,IAAI,CAACiS,cAAc,CAACtK,OAAO,CAACZ,IAAI,CAAC,CAACmC,OAAO,EAAE;IACnE,MAAMpC,aAAa,GAAG,IAAI9G,IAAI,CAACkS,WAAW,CAACvL,SAAS,CAACI,IAAI,CAAC,CAACmC,OAAO,EAAE;IACpE,MAAMiJ,MAAM,GAAGrL,aAAa,GAAGc,WAAW;IAC1C,MAAMwK,QAAQ,GAAG/I,IAAI,CAACC,KAAK,CAAC6I,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIC,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,KAAK;KACxB,MAAM;MACL,MAAM7B,KAAK,GAAGlH,IAAI,CAACC,KAAK,CAAC8I,QAAQ,GAAG,EAAE,CAAC;MACvC,MAAM5B,IAAI,GAAG4B,QAAQ,GAAG,EAAE;MAC1B,OAAO,GAAG7B,KAAK,KAAKC,IAAI,KAAK;;EAEjC;;;uBAllCW3R,oBAAoB,EAAAjE,EAAA,CAAAyX,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3X,EAAA,CAAAyX,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA7X,EAAA,CAAAyX,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApB9T,oBAAoB;MAAA+T,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjCtY,EAAA,CAAAC,cAAA,aAAoC;UAOxBD,EAAA,CAAAqB,SAAA,WAA6B;UAC7BrB,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,wBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5BH,EAAA,CAAAC,cAAA,aAAkC;UAChCD,EAAA,CAAAE,MAAA,iEACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA2E;UAA5CD,EAAA,CAAAiC,UAAA,sBAAAuW,wDAAA;YAAA,OAAYD,GAAA,CAAA3F,QAAA,EAAU;UAAA,EAAC;UAEpD5S,EAAA,CAAAC,cAAA,cAAgC;UAEGD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAAC,cAAA,eAAsC;UAIND,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC1CH,EAAA,CAAAC,cAAA,sBAAwB;UAAAD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAInDH,EAAA,CAAAC,cAAA,eAAmC;UAEpBD,EAAA,CAAAE,MAAA,6CAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClDH,EAAA,CAAAC,cAAA,iBAK6C;UAAtCD,EAAA,CAAAiC,UAAA,mBAAAwW,sDAAA;YAAA,OAASF,GAAA,CAAA9B,yBAAA,EAA2B;UAAA,EAAC;UAL5CzW,EAAA,CAAAG,YAAA,EAK6C;UAC7CH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,gCAAmF;UACjFD,EAAA,CAAAQ,UAAA,KAAAkY,2CAAA,yBAKa;UACf1Y,EAAA,CAAAG,YAAA,EAAmB;UAM3BH,EAAA,CAAAC,cAAA,kBAAoE;UAA1BD,EAAA,CAAAiC,UAAA,mBAAA0W,uDAAA;YAAA,OAASJ,GAAA,CAAAvB,aAAA,EAAe;UAAA,EAAC;UACjEhX,EAAA,CAAAqB,SAAA,aAAmC;UACrCrB,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAuC;UACRD,EAAA,CAAAE,MAAA,oBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,eAAsC;UAIND,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC1CH,EAAA,CAAAC,cAAA,sBAAwB;UAAAD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAInDH,EAAA,CAAAC,cAAA,eAAmC;UAEpBD,EAAA,CAAAE,MAAA,6CAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClDH,EAAA,CAAAC,cAAA,iBAK2C;UAApCD,EAAA,CAAAiC,UAAA,mBAAA2W,sDAAA;YAAA,OAASL,GAAA,CAAAxB,uBAAA,EAAyB;UAAA,EAAC;UAL1C/W,EAAA,CAAAG,YAAA,EAK2C;UAC3CH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAAC,cAAA,gCAAiF;UAC/ED,EAAA,CAAAQ,UAAA,KAAAqY,2CAAA,yBAKa;UACf7Y,EAAA,CAAAG,YAAA,EAAmB;UAQ7BH,EAAA,CAAAC,cAAA,eAAuC;UAERD,EAAA,CAAAE,MAAA,2BAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,0BAAqC;UACxBD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAqB,SAAA,iBAA4G;UAG9GrB,EAAA,CAAAG,YAAA,EAAiB;UAGnBH,EAAA,CAAAC,cAAA,eAAwC;UACVD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAAC,cAAA,eAA8B;UAEfD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC3BH,EAAA,CAAAC,cAAA,sBAA+D;UAC7DD,EAAA,CAAAQ,UAAA,KAAAsY,2CAAA,yBAEa;UACf9Y,EAAA,CAAAG,YAAA,EAAa;UAGfH,EAAA,CAAAC,cAAA,0BAAqC;UACxBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAqB,SAAA,iBAAmG;UACrGrB,EAAA,CAAAG,YAAA,EAAiB;UAMvBH,EAAA,CAAAC,cAAA,eAAsC;UAETD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvCH,EAAA,CAAAC,cAAA,0BAAqC;UACxBD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAC,cAAA,sBAA2D;UACzDD,EAAA,CAAAQ,UAAA,KAAAuY,2CAAA,yBAEa;UACf/Y,EAAA,CAAAG,YAAA,EAAa;UAIjBH,EAAA,CAAAC,cAAA,eAAsC;UAGhCD,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAe;UAMrBH,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAqB,SAAA,YAA6B;UAC7BrB,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOjBH,EAAA,CAAAC,cAAA,eAAwE;UACtED,EAAA,CAAAQ,UAAA,KAAAwY,oCAAA,kBAGM;UAENhZ,EAAA,CAAAQ,UAAA,MAAAyY,qCAAA,kBAKM;UAENjZ,EAAA,CAAAQ,UAAA,MAAA0Y,qCAAA,kBAMM;UAENlZ,EAAA,CAAAQ,UAAA,MAAA2Y,qCAAA,kBAsFM;UACRnZ,EAAA,CAAAG,YAAA,EAAM;;;;;;UA3PIH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAU,UAAA,cAAA6X,GAAA,CAAAhT,UAAA,CAAwB;UASJvF,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UACXV,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UAUlBV,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAU,UAAA,oBAAA0Y,GAAA,CAAiC;UAIWpZ,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAU,UAAA,gBAAA6X,GAAA,CAAA9F,eAAA,CAA+B;UAC/CzS,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAU,UAAA,YAAA6X,GAAA,CAAAjU,kBAAA,CAAqB;UAsB1CtE,EAAA,CAAAI,SAAA,IAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UACXV,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAU,UAAA,YAAW;UAUlBV,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAU,UAAA,oBAAA2Y,GAAA,CAA+B;UAIWrZ,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAU,UAAA,gBAAA6X,GAAA,CAAA9F,eAAA,CAA+B;UAC7CzS,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAU,UAAA,YAAA6X,GAAA,CAAAhU,gBAAA,CAAmB;UAmB1CvE,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAU,UAAA,QAAA6X,GAAA,CAAApT,OAAA,CAAe,kBAAAmU,GAAA;UACEtZ,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAU,UAAA,QAAA4Y,GAAA,CAAc;UAWdtZ,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAU,UAAA,YAAA6X,GAAA,CAAA5T,cAAA,CAAiB;UAqBZ3E,EAAA,CAAAI,SAAA,IAAgB;UAAhBJ,EAAA,CAAAU,UAAA,YAAA6X,GAAA,CAAAxT,aAAA,CAAgB;UAkBd/E,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAU,UAAA,aAAA6X,GAAA,CAAA/T,SAAA,CAAsB;UAUpCxE,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAuD,WAAA,gBAAAgV,GAAA,CAAA9T,WAAA,CAAiC;UAC/DzE,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAU,UAAA,SAAA6X,GAAA,CAAA/T,SAAA,CAAe;UAKfxE,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAU,UAAA,SAAA6X,GAAA,CAAA/W,YAAA,KAAA+W,GAAA,CAAA/T,SAAA,CAAgC;UAOhCxE,EAAA,CAAAI,SAAA,GAA8E;UAA9EJ,EAAA,CAAAU,UAAA,UAAA6X,GAAA,CAAA/T,SAAA,IAAA+T,GAAA,CAAAvU,aAAA,CAAAH,MAAA,UAAA0U,GAAA,CAAA9T,WAAA,KAAA8T,GAAA,CAAA/W,YAAA,CAA8E;UAQ9ExB,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAU,UAAA,UAAA6X,GAAA,CAAA/T,SAAA,IAAA+T,GAAA,CAAAvU,aAAA,CAAAH,MAAA,KAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}