package com.paximum.demo.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MultiCityRequest {

    @JsonProperty("ProductType")
    private Integer productType;

    @JsonProperty("ServiceTypes")
    private List<String> serviceTypes;

    @JsonProperty("FlightSegments")
    private List<FlightSegment> flightSegments;

    @JsonProperty("Passengers")
    private List<Passenger> passengers;

    @JsonProperty("showOnlyNonStopFlight")
    private boolean showOnlyNonStopFlight;

    @JsonProperty("additionalParameters")
    private AdditionalParameters additionalParameters;

    @JsonProperty("acceptPendingProviders")
    private boolean acceptPendingProviders;

    @JsonProperty("forceFlightBundlePackage")
    private boolean forceFlightBundlePackage;

    @JsonProperty("disablePackageOfferTotalPrice")
    private boolean disablePackageOfferTotalPrice;

    @JsonProperty("calculateFlightFees")
    private boolean calculateFlightFees;

    @JsonProperty("flightClasses")
    private List<Integer> flightClasses;

    @JsonProperty("Culture")
    private String culture;

    @JsonProperty("Currency")
    private String currency;

    // Getters and setters
    public Integer getProductType() {
        return productType;
    }
    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public List<String> getServiceTypes() {
        return serviceTypes;
    }
    public void setServiceTypes(List<String> serviceTypes) {
        this.serviceTypes = serviceTypes;
    }

    public List<FlightSegment> getFlightSegments() {
        return flightSegments;
    }
    public void setFlightSegments(List<FlightSegment> flightSegments) {
        this.flightSegments = flightSegments;
    }

    public List<Passenger> getPassengers() {
        return passengers;
    }
    public void setPassengers(List<Passenger> passengers) {
        this.passengers = passengers;
    }

    public boolean isShowOnlyNonStopFlight() {
        return showOnlyNonStopFlight;
    }
    public void setShowOnlyNonStopFlight(boolean showOnlyNonStopFlight) {
        this.showOnlyNonStopFlight = showOnlyNonStopFlight;
    }

    public AdditionalParameters getAdditionalParameters() {
        return additionalParameters;
    }
    public void setAdditionalParameters(AdditionalParameters additionalParameters) {
        this.additionalParameters = additionalParameters;
    }

    public boolean isAcceptPendingProviders() {
        return acceptPendingProviders;
    }
    public void setAcceptPendingProviders(boolean acceptPendingProviders) {
        this.acceptPendingProviders = acceptPendingProviders;
    }

    public boolean isForceFlightBundlePackage() {
        return forceFlightBundlePackage;
    }
    public void setForceFlightBundlePackage(boolean forceFlightBundlePackage) {
        this.forceFlightBundlePackage = forceFlightBundlePackage;
    }

    public boolean isDisablePackageOfferTotalPrice() {
        return disablePackageOfferTotalPrice;
    }
    public void setDisablePackageOfferTotalPrice(boolean disablePackageOfferTotalPrice) {
        this.disablePackageOfferTotalPrice = disablePackageOfferTotalPrice;
    }

    public boolean isCalculateFlightFees() {
        return calculateFlightFees;
    }
    public void setCalculateFlightFees(boolean calculateFlightFees) {
        this.calculateFlightFees = calculateFlightFees;
    }

    public List<Integer> getFlightClasses() {
        return flightClasses;
    }
    public void setFlightClasses(List<Integer> flightClasses) {
        this.flightClasses = flightClasses;
    }

    public String getCulture() {
        return culture;
    }
    public void setCulture(String culture) {
        this.culture = culture;
    }

    public String getCurrency() {
        return currency;
    }
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    // Classes imbriquées
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FlightSegment {
        @JsonProperty("CheckIn")
        private String checkIn;

        @JsonProperty("DepartureLocations")
        private List<Location> departureLocations;

        @JsonProperty("ArrivalLocations")
        private List<Location> arrivalLocations;

        public String getCheckIn() {
            return checkIn;
        }
        public void setCheckIn(String checkIn) {
            this.checkIn = checkIn;
        }

        public List<Location> getDepartureLocations() {
            return departureLocations;
        }
        public void setDepartureLocations(List<Location> departureLocations) {
            this.departureLocations = departureLocations;
        }

        public List<Location> getArrivalLocations() {
            return arrivalLocations;
        }
        public void setArrivalLocations(List<Location> arrivalLocations) {
            this.arrivalLocations = arrivalLocations;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Location {
        @JsonProperty("Id")
        private String id;
        @JsonProperty("Type")
        private Integer type;
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public Integer getType() {
            return type;
        }
        public void setType(Integer type) {
            this.type = type;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Passenger {
        @JsonProperty("Type")
        private Integer type;
        @JsonProperty("Count")
        private Integer count;
        public Integer getType() {
            return type;
        }
        public void setType(Integer type) {
            this.type = type;
        }
        public Integer getCount() {
            return count;
        }
        public void setCount(Integer count) {
            this.count = count;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AdditionalParameters {
        @JsonProperty("GetOptionsParameters")
        private GetOptionsParameters getOptionsParameters;
        @JsonProperty("CorporateCodes")
        private List<CorporateCode> corporateCodes;

        public GetOptionsParameters getGetOptionsParameters() {
            return getOptionsParameters;
        }
        public void setGetOptionsParameters(GetOptionsParameters getOptionsParameters) {
            this.getOptionsParameters = getOptionsParameters;
        }
        public List<CorporateCode> getCorporateCodes() {
            return corporateCodes;
        }
        public void setCorporateCodes(List<CorporateCode> corporateCodes) {
            this.corporateCodes = corporateCodes;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GetOptionsParameters {
        @JsonProperty("FlightBaggageGetOption")
        private Integer flightBaggageGetOption;

        public Integer getFlightBaggageGetOption() {
            return flightBaggageGetOption;
        }
        public void setFlightBaggageGetOption(Integer flightBaggageGetOption) {
            this.flightBaggageGetOption = flightBaggageGetOption;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CorporateCode {
        @JsonProperty("Code")
        private String code;
        @JsonProperty("Rule")
        private Rule rule;

        public String getCode() {
            return code;
        }
        public void setCode(String code) {
            this.code = code;
        }
        public Rule getRule() {
            return rule;
        }
        public void setRule(Rule rule) {
            this.rule = rule;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Rule {
        @JsonProperty("Airline")
        private String airline;
        @JsonProperty("Supplier")
        private String supplier;
        public String getAirline() {
            return airline;
        }
        public void setAirline(String airline) {
            this.airline = airline;
        }
        public String getSupplier() {
            return supplier;
        }
        public void setSupplier(String supplier) {
            this.supplier = supplier;
        }
    }
}
