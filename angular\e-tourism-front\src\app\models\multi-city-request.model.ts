import { FlightBaggageGetOption, FlightClassType, LocationType, ProductType } from './enums.model';

export interface MultiCityRequest {
  ProductType: ProductType;
  ServiceTypes: string[];
  FlightSegments: FlightSegment[];
  CheckIns?: string[]; // Ajout du paramètre CheckIns pour les vols multi-destinations
  CheckIn?: string; // Ajout du paramètre CheckIn pour compatibilité
  Passengers: Passenger[];
  showOnlyNonStopFlight: boolean;
  additionalParameters?: AdditionalParameters;
  acceptPendingProviders: boolean;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  calculateFlightFees: boolean;
  flightClasses: FlightClassType[];
  Culture: string;
  Currency: string;
}

export interface FlightSegment {
  CheckIn: string;
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
}

export interface Location {
  id: string;
  type: LocationType;
}

export interface Passenger {
  type: number;
  count: number;
}

export interface AdditionalParameters {
  getOptionsParameters?: GetOptionsParameters;
  CorporateCodes?: CorporateCode[];
}

export interface GetOptionsParameters {
  flightBaggageGetOption: FlightBaggageGetOption;
}

export interface CorporateCode {
  Code: string;
  Rule: Rule;
}

export interface Rule {
  Airline: string;
  Supplier: string;
}
