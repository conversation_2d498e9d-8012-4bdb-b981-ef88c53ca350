<mat-form-field appearance="outline" class="country-selector">
  <mat-label>{{ label }}</mat-label>
  <input type="text"
         matInput
         [formControl]="countryControl"
         [matAutocomplete]="auto"
         [placeholder]="placeholder"
         [required]="required">
  <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn">
    <mat-option *ngFor="let country of filteredCountries | async" [value]="country">
      <div class="country-option">
        <img class="country-flag" [src]="country.flag" [alt]="country.name">
        <span>{{ country.name }}</span>
        <span class="country-code">({{ country.code }})</span>
      </div>
    </mat-option>
  </mat-autocomplete>
  <mat-icon matSuffix>public</mat-icon>
</mat-form-field>
