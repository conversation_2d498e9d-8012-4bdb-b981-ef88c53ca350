{"ast": null, "code": "import { of } from 'rxjs';\nimport { map, shareReplay, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CountryService {\n  constructor(http) {\n    this.http = http;\n    this.countriesCache = [];\n    // Initialiser la liste des pays\n    this.countries$ = this.http.get('https://restcountries.com/v3.1/all?fields=name,cca2,flags').pipe(map(countries => countries.map(country => ({\n      name: country.name.common,\n      code: country.cca2,\n      flag: country.flags.svg\n    }))), map(countries => countries.sort((a, b) => a.name.localeCompare(b.name))), shareReplay(1), catchError(error => {\n      console.error('Erreur lors de la récupération des pays:', error);\n      return of(this.getDefaultCountries());\n    }));\n    // Mettre en cache la liste des pays\n    this.countries$.subscribe(countries => {\n      this.countriesCache = countries;\n    });\n  }\n  /**\n   * Récupérer la liste des pays\n   * @returns Observable de la liste des pays\n   */\n  getCountries() {\n    return this.countries$;\n  }\n  /**\n   * Récupérer un pays par son code\n   * @param code Code du pays (2 lettres)\n   * @returns Le pays correspondant ou undefined\n   */\n  getCountryByCode(code) {\n    return this.countriesCache.find(country => country.code === code);\n  }\n  /**\n   * Récupérer un pays par son nom\n   * @param name Nom du pays\n   * @returns Le pays correspondant ou undefined\n   */\n  getCountryByName(name) {\n    return this.countriesCache.find(country => country.name.toLowerCase() === name.toLowerCase());\n  }\n  /**\n   * Liste par défaut des pays en cas d'erreur de l'API\n   * @returns Liste des pays les plus courants\n   */\n  getDefaultCountries() {\n    return [{\n      name: 'France',\n      code: 'FR',\n      flag: 'https://flagcdn.com/fr.svg'\n    }, {\n      name: 'Germany',\n      code: 'DE',\n      flag: 'https://flagcdn.com/de.svg'\n    }, {\n      name: 'United Kingdom',\n      code: 'GB',\n      flag: 'https://flagcdn.com/gb.svg'\n    }, {\n      name: 'United States',\n      code: 'US',\n      flag: 'https://flagcdn.com/us.svg'\n    }, {\n      name: 'Spain',\n      code: 'ES',\n      flag: 'https://flagcdn.com/es.svg'\n    }, {\n      name: 'Italy',\n      code: 'IT',\n      flag: 'https://flagcdn.com/it.svg'\n    }, {\n      name: 'Canada',\n      code: 'CA',\n      flag: 'https://flagcdn.com/ca.svg'\n    }, {\n      name: 'Australia',\n      code: 'AU',\n      flag: 'https://flagcdn.com/au.svg'\n    }, {\n      name: 'Japan',\n      code: 'JP',\n      flag: 'https://flagcdn.com/jp.svg'\n    }, {\n      name: 'China',\n      code: 'CN',\n      flag: 'https://flagcdn.com/cn.svg'\n    }, {\n      name: 'Brazil',\n      code: 'BR',\n      flag: 'https://flagcdn.com/br.svg'\n    }, {\n      name: 'India',\n      code: 'IN',\n      flag: 'https://flagcdn.com/in.svg'\n    }, {\n      name: 'Russia',\n      code: 'RU',\n      flag: 'https://flagcdn.com/ru.svg'\n    }, {\n      name: 'South Africa',\n      code: 'ZA',\n      flag: 'https://flagcdn.com/za.svg'\n    }, {\n      name: 'Mexico',\n      code: 'MX',\n      flag: 'https://flagcdn.com/mx.svg'\n    }, {\n      name: 'Argentina',\n      code: 'AR',\n      flag: 'https://flagcdn.com/ar.svg'\n    }, {\n      name: 'Turkey',\n      code: 'TR',\n      flag: 'https://flagcdn.com/tr.svg'\n    }, {\n      name: 'Egypt',\n      code: 'EG',\n      flag: 'https://flagcdn.com/eg.svg'\n    }, {\n      name: 'Morocco',\n      code: 'MA',\n      flag: 'https://flagcdn.com/ma.svg'\n    }, {\n      name: 'Tunisia',\n      code: 'TN',\n      flag: 'https://flagcdn.com/tn.svg'\n    }];\n  }\n  static {\n    this.ɵfac = function CountryService_Factory(t) {\n      return new (t || CountryService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CountryService,\n      factory: CountryService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "map", "shareReplay", "catchError", "CountryService", "constructor", "http", "countriesCache", "countries$", "get", "pipe", "countries", "country", "name", "common", "code", "cca2", "flag", "flags", "svg", "sort", "a", "b", "localeCompare", "error", "console", "getDefaultCountries", "subscribe", "getCountries", "getCountryByCode", "find", "getCountryByName", "toLowerCase", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\country.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, of } from 'rxjs';\nimport { map, shareReplay, catchError } from 'rxjs/operators';\n\nexport interface Country {\n  name: string;\n  code: string;\n  flag: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class CountryService {\n  private countries$: Observable<Country[]>;\n  private countriesCache: Country[] = [];\n\n  constructor(private http: HttpClient) {\n    // Initialiser la liste des pays\n    this.countries$ = this.http.get<any[]>('https://restcountries.com/v3.1/all?fields=name,cca2,flags')\n      .pipe(\n        map(countries => countries.map(country => ({\n          name: country.name.common,\n          code: country.cca2,\n          flag: country.flags.svg\n        }))),\n        map(countries => countries.sort((a, b) => a.name.localeCompare(b.name))),\n        shareReplay(1),\n        catchError(error => {\n          console.error('Erreur lors de la récupération des pays:', error);\n          return of(this.getDefaultCountries());\n        })\n      );\n\n    // Mettre en cache la liste des pays\n    this.countries$.subscribe(countries => {\n      this.countriesCache = countries;\n    });\n  }\n\n  /**\n   * Récupérer la liste des pays\n   * @returns Observable de la liste des pays\n   */\n  getCountries(): Observable<Country[]> {\n    return this.countries$;\n  }\n\n  /**\n   * Récupérer un pays par son code\n   * @param code Code du pays (2 lettres)\n   * @returns Le pays correspondant ou undefined\n   */\n  getCountryByCode(code: string): Country | undefined {\n    return this.countriesCache.find(country => country.code === code);\n  }\n\n  /**\n   * Récupérer un pays par son nom\n   * @param name Nom du pays\n   * @returns Le pays correspondant ou undefined\n   */\n  getCountryByName(name: string): Country | undefined {\n    return this.countriesCache.find(country => \n      country.name.toLowerCase() === name.toLowerCase()\n    );\n  }\n\n  /**\n   * Liste par défaut des pays en cas d'erreur de l'API\n   * @returns Liste des pays les plus courants\n   */\n  private getDefaultCountries(): Country[] {\n    return [\n      { name: 'France', code: 'FR', flag: 'https://flagcdn.com/fr.svg' },\n      { name: 'Germany', code: 'DE', flag: 'https://flagcdn.com/de.svg' },\n      { name: 'United Kingdom', code: 'GB', flag: 'https://flagcdn.com/gb.svg' },\n      { name: 'United States', code: 'US', flag: 'https://flagcdn.com/us.svg' },\n      { name: 'Spain', code: 'ES', flag: 'https://flagcdn.com/es.svg' },\n      { name: 'Italy', code: 'IT', flag: 'https://flagcdn.com/it.svg' },\n      { name: 'Canada', code: 'CA', flag: 'https://flagcdn.com/ca.svg' },\n      { name: 'Australia', code: 'AU', flag: 'https://flagcdn.com/au.svg' },\n      { name: 'Japan', code: 'JP', flag: 'https://flagcdn.com/jp.svg' },\n      { name: 'China', code: 'CN', flag: 'https://flagcdn.com/cn.svg' },\n      { name: 'Brazil', code: 'BR', flag: 'https://flagcdn.com/br.svg' },\n      { name: 'India', code: 'IN', flag: 'https://flagcdn.com/in.svg' },\n      { name: 'Russia', code: 'RU', flag: 'https://flagcdn.com/ru.svg' },\n      { name: 'South Africa', code: 'ZA', flag: 'https://flagcdn.com/za.svg' },\n      { name: 'Mexico', code: 'MX', flag: 'https://flagcdn.com/mx.svg' },\n      { name: 'Argentina', code: 'AR', flag: 'https://flagcdn.com/ar.svg' },\n      { name: 'Turkey', code: 'TR', flag: 'https://flagcdn.com/tr.svg' },\n      { name: 'Egypt', code: 'EG', flag: 'https://flagcdn.com/eg.svg' },\n      { name: 'Morocco', code: 'MA', flag: 'https://flagcdn.com/ma.svg' },\n      { name: 'Tunisia', code: 'TN', flag: 'https://flagcdn.com/tn.svg' }\n    ];\n  }\n}\n"], "mappings": "AAEA,SAAqBA,EAAE,QAAQ,MAAM;AACrC,SAASC,GAAG,EAAEC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;;;AAW7D,OAAM,MAAOC,cAAc;EAIzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,cAAc,GAAc,EAAE;IAGpC;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACF,IAAI,CAACG,GAAG,CAAQ,2DAA2D,CAAC,CAChGC,IAAI,CACHT,GAAG,CAACU,SAAS,IAAIA,SAAS,CAACV,GAAG,CAACW,OAAO,KAAK;MACzCC,IAAI,EAAED,OAAO,CAACC,IAAI,CAACC,MAAM;MACzBC,IAAI,EAAEH,OAAO,CAACI,IAAI;MAClBC,IAAI,EAAEL,OAAO,CAACM,KAAK,CAACC;KACrB,CAAC,CAAC,CAAC,EACJlB,GAAG,CAACU,SAAS,IAAIA,SAAS,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACR,IAAI,CAACU,aAAa,CAACD,CAAC,CAACT,IAAI,CAAC,CAAC,CAAC,EACxEX,WAAW,CAAC,CAAC,CAAC,EACdC,UAAU,CAACqB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,OAAOxB,EAAE,CAAC,IAAI,CAAC0B,mBAAmB,EAAE,CAAC;IACvC,CAAC,CAAC,CACH;IAEH;IACA,IAAI,CAAClB,UAAU,CAACmB,SAAS,CAAChB,SAAS,IAAG;MACpC,IAAI,CAACJ,cAAc,GAAGI,SAAS;IACjC,CAAC,CAAC;EACJ;EAEA;;;;EAIAiB,YAAYA,CAAA;IACV,OAAO,IAAI,CAACpB,UAAU;EACxB;EAEA;;;;;EAKAqB,gBAAgBA,CAACd,IAAY;IAC3B,OAAO,IAAI,CAACR,cAAc,CAACuB,IAAI,CAAClB,OAAO,IAAIA,OAAO,CAACG,IAAI,KAAKA,IAAI,CAAC;EACnE;EAEA;;;;;EAKAgB,gBAAgBA,CAAClB,IAAY;IAC3B,OAAO,IAAI,CAACN,cAAc,CAACuB,IAAI,CAAClB,OAAO,IACrCA,OAAO,CAACC,IAAI,CAACmB,WAAW,EAAE,KAAKnB,IAAI,CAACmB,WAAW,EAAE,CAClD;EACH;EAEA;;;;EAIQN,mBAAmBA,CAAA;IACzB,OAAO,CACL;MAAEb,IAAI,EAAE,QAAQ;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEJ,IAAI,EAAE,SAAS;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACnE;MAAEJ,IAAI,EAAE,gBAAgB;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAC1E;MAAEJ,IAAI,EAAE,eAAe;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACzE;MAAEJ,IAAI,EAAE,OAAO;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEJ,IAAI,EAAE,OAAO;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEJ,IAAI,EAAE,QAAQ;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEJ,IAAI,EAAE,WAAW;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACrE;MAAEJ,IAAI,EAAE,OAAO;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEJ,IAAI,EAAE,OAAO;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEJ,IAAI,EAAE,QAAQ;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEJ,IAAI,EAAE,OAAO;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEJ,IAAI,EAAE,QAAQ;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEJ,IAAI,EAAE,cAAc;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACxE;MAAEJ,IAAI,EAAE,QAAQ;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEJ,IAAI,EAAE,WAAW;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACrE;MAAEJ,IAAI,EAAE,QAAQ;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EAClE;MAAEJ,IAAI,EAAE,OAAO;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACjE;MAAEJ,IAAI,EAAE,SAAS;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,EACnE;MAAEJ,IAAI,EAAE,SAAS;MAAEE,IAAI,EAAE,IAAI;MAAEE,IAAI,EAAE;IAA4B,CAAE,CACpE;EACH;;;uBAlFWb,cAAc,EAAA6B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdhC,cAAc;MAAAiC,OAAA,EAAdjC,cAAc,CAAAkC,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}