{"ast": null, "code": "import { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nexport function expand(project, concurrent = Infinity, scheduler) {\n  concurrent = (concurrent || 0) < 1 ? Infinity : concurrent;\n  return operate((source, subscriber) => mergeInternals(source, subscriber, project, concurrent, undefined, true, scheduler));\n}\n//# sourceMappingURL=expand.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}