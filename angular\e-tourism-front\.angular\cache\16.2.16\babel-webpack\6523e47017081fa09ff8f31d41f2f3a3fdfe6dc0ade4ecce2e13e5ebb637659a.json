{"ast": null, "code": "import { of } from 'rxjs';\nimport { map, shareReplay, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let CountryService = /*#__PURE__*/(() => {\n  class CountryService {\n    constructor(http) {\n      this.http = http;\n      this.countriesCache = [];\n      // Initialiser la liste des pays\n      this.countries$ = this.http.get('https://restcountries.com/v3.1/all?fields=name,cca2,flags').pipe(map(countries => countries.map(country => ({\n        name: country.name.common,\n        code: country.cca2,\n        flag: country.flags.svg\n      }))), map(countries => countries.sort((a, b) => a.name.localeCompare(b.name))), shareReplay(1), catchError(error => {\n        console.error('Erreur lors de la récupération des pays:', error);\n        return of(this.getDefaultCountries());\n      }));\n      // Mettre en cache la liste des pays\n      this.countries$.subscribe(countries => {\n        this.countriesCache = countries;\n      });\n    }\n    /**\n     * Récupérer la liste des pays\n     * @returns Observable de la liste des pays\n     */\n    getCountries() {\n      return this.countries$;\n    }\n    /**\n     * Récupérer un pays par son code\n     * @param code Code du pays (2 lettres)\n     * @returns Le pays correspondant ou undefined\n     */\n    getCountryByCode(code) {\n      return this.countriesCache.find(country => country.code === code);\n    }\n    /**\n     * Récupérer un pays par son nom\n     * @param name Nom du pays\n     * @returns Le pays correspondant ou undefined\n     */\n    getCountryByName(name) {\n      return this.countriesCache.find(country => country.name.toLowerCase() === name.toLowerCase());\n    }\n    /**\n     * Liste par défaut des pays en cas d'erreur de l'API\n     * @returns Liste des pays les plus courants\n     */\n    getDefaultCountries() {\n      return [{\n        name: 'France',\n        code: 'FR',\n        flag: 'https://flagcdn.com/fr.svg'\n      }, {\n        name: 'Germany',\n        code: 'DE',\n        flag: 'https://flagcdn.com/de.svg'\n      }, {\n        name: 'United Kingdom',\n        code: 'GB',\n        flag: 'https://flagcdn.com/gb.svg'\n      }, {\n        name: 'United States',\n        code: 'US',\n        flag: 'https://flagcdn.com/us.svg'\n      }, {\n        name: 'Spain',\n        code: 'ES',\n        flag: 'https://flagcdn.com/es.svg'\n      }, {\n        name: 'Italy',\n        code: 'IT',\n        flag: 'https://flagcdn.com/it.svg'\n      }, {\n        name: 'Canada',\n        code: 'CA',\n        flag: 'https://flagcdn.com/ca.svg'\n      }, {\n        name: 'Australia',\n        code: 'AU',\n        flag: 'https://flagcdn.com/au.svg'\n      }, {\n        name: 'Japan',\n        code: 'JP',\n        flag: 'https://flagcdn.com/jp.svg'\n      }, {\n        name: 'China',\n        code: 'CN',\n        flag: 'https://flagcdn.com/cn.svg'\n      }, {\n        name: 'Brazil',\n        code: 'BR',\n        flag: 'https://flagcdn.com/br.svg'\n      }, {\n        name: 'India',\n        code: 'IN',\n        flag: 'https://flagcdn.com/in.svg'\n      }, {\n        name: 'Russia',\n        code: 'RU',\n        flag: 'https://flagcdn.com/ru.svg'\n      }, {\n        name: 'South Africa',\n        code: 'ZA',\n        flag: 'https://flagcdn.com/za.svg'\n      }, {\n        name: 'Mexico',\n        code: 'MX',\n        flag: 'https://flagcdn.com/mx.svg'\n      }, {\n        name: 'Argentina',\n        code: 'AR',\n        flag: 'https://flagcdn.com/ar.svg'\n      }, {\n        name: 'Turkey',\n        code: 'TR',\n        flag: 'https://flagcdn.com/tr.svg'\n      }, {\n        name: 'Egypt',\n        code: 'EG',\n        flag: 'https://flagcdn.com/eg.svg'\n      }, {\n        name: 'Morocco',\n        code: 'MA',\n        flag: 'https://flagcdn.com/ma.svg'\n      }, {\n        name: 'Tunisia',\n        code: 'TN',\n        flag: 'https://flagcdn.com/tn.svg'\n      }];\n    }\n    static {\n      this.ɵfac = function CountryService_Factory(t) {\n        return new (t || CountryService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: CountryService,\n        factory: CountryService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return CountryService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}