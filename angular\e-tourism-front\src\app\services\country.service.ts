import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, shareReplay, catchError } from 'rxjs/operators';

export interface Country {
  name: string;
  code: string;
  flag: string;
}

@Injectable({
  providedIn: 'root'
})
export class CountryService {
  private countries$: Observable<Country[]>;
  private countriesCache: Country[] = [];

  constructor(private http: HttpClient) {
    // Initialiser la liste des pays
    this.countries$ = this.http.get<any[]>('https://restcountries.com/v3.1/all?fields=name,cca2,flags')
      .pipe(
        map(countries => countries.map(country => ({
          name: country.name.common,
          code: country.cca2,
          flag: country.flags.svg
        }))),
        map(countries => countries.sort((a, b) => a.name.localeCompare(b.name))),
        shareReplay(1),
        catchError(error => {
          console.error('Erreur lors de la récupération des pays:', error);
          return of(this.getDefaultCountries());
        })
      );

    // Mettre en cache la liste des pays
    this.countries$.subscribe(countries => {
      this.countriesCache = countries;
    });
  }

  /**
   * Récupérer la liste des pays
   * @returns Observable de la liste des pays
   */
  getCountries(): Observable<Country[]> {
    return this.countries$;
  }

  /**
   * Récupérer un pays par son code
   * @param code Code du pays (2 lettres)
   * @returns Le pays correspondant ou undefined
   */
  getCountryByCode(code: string): Country | undefined {
    return this.countriesCache.find(country => country.code === code);
  }

  /**
   * Récupérer un pays par son nom
   * @param name Nom du pays
   * @returns Le pays correspondant ou undefined
   */
  getCountryByName(name: string): Country | undefined {
    return this.countriesCache.find(country => 
      country.name.toLowerCase() === name.toLowerCase()
    );
  }

  /**
   * Liste par défaut des pays en cas d'erreur de l'API
   * @returns Liste des pays les plus courants
   */
  private getDefaultCountries(): Country[] {
    return [
      { name: 'France', code: 'FR', flag: 'https://flagcdn.com/fr.svg' },
      { name: 'Germany', code: 'DE', flag: 'https://flagcdn.com/de.svg' },
      { name: 'United Kingdom', code: 'GB', flag: 'https://flagcdn.com/gb.svg' },
      { name: 'United States', code: 'US', flag: 'https://flagcdn.com/us.svg' },
      { name: 'Spain', code: 'ES', flag: 'https://flagcdn.com/es.svg' },
      { name: 'Italy', code: 'IT', flag: 'https://flagcdn.com/it.svg' },
      { name: 'Canada', code: 'CA', flag: 'https://flagcdn.com/ca.svg' },
      { name: 'Australia', code: 'AU', flag: 'https://flagcdn.com/au.svg' },
      { name: 'Japan', code: 'JP', flag: 'https://flagcdn.com/jp.svg' },
      { name: 'China', code: 'CN', flag: 'https://flagcdn.com/cn.svg' },
      { name: 'Brazil', code: 'BR', flag: 'https://flagcdn.com/br.svg' },
      { name: 'India', code: 'IN', flag: 'https://flagcdn.com/in.svg' },
      { name: 'Russia', code: 'RU', flag: 'https://flagcdn.com/ru.svg' },
      { name: 'South Africa', code: 'ZA', flag: 'https://flagcdn.com/za.svg' },
      { name: 'Mexico', code: 'MX', flag: 'https://flagcdn.com/mx.svg' },
      { name: 'Argentina', code: 'AR', flag: 'https://flagcdn.com/ar.svg' },
      { name: 'Turkey', code: 'TR', flag: 'https://flagcdn.com/tr.svg' },
      { name: 'Egypt', code: 'EG', flag: 'https://flagcdn.com/eg.svg' },
      { name: 'Morocco', code: 'MA', flag: 'https://flagcdn.com/ma.svg' },
      { name: 'Tunisia', code: 'TN', flag: 'https://flagcdn.com/tn.svg' }
    ];
  }
}
