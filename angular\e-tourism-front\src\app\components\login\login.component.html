<div class="login-page">
  <div class="login-background">
    <div class="login-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
      <div class="shape shape-4"></div>
      <div class="shape shape-5"></div>
    </div>
    <div class="login-illustration">
      <img src="assets/images/login-illustration.svg" alt="Travel Illustration">
    </div>
  </div>

  <div class="login-container">
    <div class="login-card animate-slide-up">
      <div class="login-logo">
        <div class="logo-icon">
          <i class="fas fa-plane-departure"></i>
        </div>
        <h1>E-Tourism</h1>
      </div>

      <div class="login-header">
        <h2>Bienvenue</h2>
        <p>Connectez-vous pour accéder à votre compte</p>
      </div>

      <form (ngSubmit)="onSubmit()" #loginForm="ngForm" class="login-form">
        <div class="form-group">
          <label for="agency">Agence</label>
          <div class="input-with-icon">
            <i class="fas fa-building"></i>
            <input
              type="text"
              id="agency"
              name="agency"
              [(ngModel)]="credentials.Agency"
              required
              class="form-control"
              placeholder="Entrez le nom de votre agence">
            <div class="input-focus-border"></div>
          </div>
        </div>

        <div class="form-group">
          <label for="username">Nom d'utilisateur</label>
          <div class="input-with-icon">
            <i class="fas fa-user"></i>
            <input
              type="text"
              id="username"
              name="username"
              [(ngModel)]="credentials.User"
              required
              class="form-control"
              placeholder="Entrez votre nom d'utilisateur">
            <div class="input-focus-border"></div>
          </div>
        </div>

        <div class="form-group">
          <label for="password">Mot de passe</label>
          <div class="input-with-icon">
            <i class="fas fa-lock"></i>
            <input
              [type]="hidePassword ? 'password' : 'text'"
              id="password"
              name="password"
              [(ngModel)]="credentials.Password"
              required
              class="form-control"
              placeholder="Entrez votre mot de passe">
            <button
              type="button"
              class="password-toggle"
              (click)="hidePassword = !hidePassword"
              aria-label="Afficher/masquer le mot de passe"
              title="{{ hidePassword ? 'Afficher le mot de passe' : 'Masquer le mot de passe' }}">
              <i class="fas" [ngClass]="hidePassword ? 'fa-eye' : 'fa-eye-slash'"></i>
            </button>
            <div class="input-focus-border"></div>
          </div>
        </div>

        <div *ngIf="error" class="error-message">
          <i class="fas fa-exclamation-circle"></i>
          <span>{{ error }}</span>
        </div>

        <button
          type="submit"
          [disabled]="!loginForm.form.valid || loading"
          class="login-button">
          <div *ngIf="loading" class="spinner-container">
            <div class="spinner"></div>
          </div>
          <span *ngIf="!loading">Se connecter</span>
        </button>
      </form>

      <div class="login-footer">
        <div class="footer-decoration">
          <div class="decoration-line"></div>
          <i class="fas fa-globe-americas"></i>
          <div class="decoration-line"></div>
        </div>
        <p>© 2023 E-Tourism. Tous droits réservés.</p>
      </div>
    </div>
  </div>
</div>
