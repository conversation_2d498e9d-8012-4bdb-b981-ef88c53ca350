{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Travel<PERSON>T<PERSON><PERSON>, Gender, PassengerType } from '../../../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../services/booking.service\";\nimport * as i4 from \"../../../services/auth.service\";\nimport * as i5 from \"../../../services/shared-data.service\";\nimport * as i6 from \"../../../services/country.service\";\nimport * as i7 from \"@angular/material/snack-bar\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/form-field\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/datepicker\";\nimport * as i14 from \"@angular/material/button\";\nimport * as i15 from \"@angular/material/icon\";\nimport * as i16 from \"@angular/material/progress-spinner\";\nimport * as i17 from \"@angular/material/expansion\";\nimport * as i18 from \"@angular/material/stepper\";\nfunction BookingTransactionComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate(ctx_r1.transactionId ? \"R\\u00E9servation d\\u00E9marr\\u00E9e\" : \"D\\u00E9marrer la r\\u00E9servation\");\n  }\n}\nfunction BookingTransactionComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.errorMessage);\n  }\n}\nfunction BookingTransactionComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Aucun vol n'a \\u00E9t\\u00E9 s\\u00E9lectionn\\u00E9. Veuillez retourner \\u00E0 la page de recherche et s\\u00E9lectionner un vol.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 52);\n    i0.ɵɵtext(6, \" Retour \\u00E0 la recherche \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BookingTransactionComponent_div_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"i\", 65);\n    i0.ɵɵelementStart(3, \"span\", 66);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 67)(6, \"p\", 68);\n    i0.ɵɵtext(7, \"Votre vol est pr\\u00EAt \\u00E0 \\u00EAtre r\\u00E9serv\\u00E9\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const offerId_r21 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r18.formatOfferId(offerId_r21));\n  }\n}\nfunction BookingTransactionComponent_div_21_mat_spinner_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 69);\n  }\n}\nfunction BookingTransactionComponent_div_21_span_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"D\\u00E9marrer la r\\u00E9servation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 53)(2, \"h3\");\n    i0.ɵɵtext(3, \"R\\u00E9sum\\u00E9 de votre s\\u00E9lection\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 54)(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 55);\n    i0.ɵɵtemplate(8, BookingTransactionComponent_div_21_div_8_Template, 8, 1, \"div\", 56);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"form\", 17);\n    i0.ɵɵlistener(\"ngSubmit\", function BookingTransactionComponent_div_21_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.beginTransaction());\n    });\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"mat-form-field\", 23)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"Devise\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-select\", 57)(15, \"mat-option\", 41);\n    i0.ɵɵtext(16, \"Euro (EUR)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-option\", 42);\n    i0.ɵɵtext(18, \"Dollar am\\u00E9ricain (USD)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-option\", 43);\n    i0.ɵɵtext(20, \"Livre sterling (GBP)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-hint\");\n    i0.ɵɵtext(22, \"Devise utilis\\u00E9e pour la transaction\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"mat-form-field\", 23)(24, \"mat-label\");\n    i0.ɵɵtext(25, \"Culture\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-select\", 58)(27, \"mat-option\", 59);\n    i0.ɵɵtext(28, \"Fran\\u00E7ais (FR)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"mat-option\", 60);\n    i0.ɵɵtext(30, \"Anglais (US)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"mat-option\", 61);\n    i0.ɵɵtext(32, \"Anglais (GB)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"mat-hint\");\n    i0.ɵɵtext(34, \"Langue utilis\\u00E9e pour la transaction\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 29)(36, \"button\", 30);\n    i0.ɵɵtemplate(37, BookingTransactionComponent_div_21_mat_spinner_37_Template, 1, 0, \"mat-spinner\", 31);\n    i0.ɵɵtemplate(38, BookingTransactionComponent_div_21_span_38_Template, 2, 0, \"span\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 62);\n    i0.ɵɵtext(40, \"Annuler\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Offres s\\u00E9lectionn\\u00E9es: \", ctx_r4.offerIds.length, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.offerIds);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.beginTransactionForm);\n    i0.ɵɵadvance(27);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isLoading);\n  }\n}\nfunction BookingTransactionComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"h3\");\n    i0.ɵɵtext(2, \"D\\u00E9tails de la transaction\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 71)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Expire le:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"strong\");\n    i0.ɵɵtext(10, \"Statut:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.formatDate(ctx_r5.beginResponse.body.expiresOn), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.beginResponse.body.status === 1 ? \"Active\" : ctx_r5.beginResponse.body.status || \"N/A\", \"\");\n  }\n}\nfunction BookingTransactionComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Informations voyageurs\");\n  }\n}\nfunction BookingTransactionComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r7.errorMessage);\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_panel_description_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-panel-description\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const traveller_r24 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", (tmp_0_0 = traveller_r24.get(\"name\")) == null ? null : tmp_0_0.value, \" \", (tmp_0_0 = traveller_r24.get(\"surname\")) == null ? null : tmp_0_0.value, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r42 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r42.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r42.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r43 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", title_r43.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", title_r43.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r44 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r44.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r44.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_option_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const gender_r45 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", gender_r45.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", gender_r45.label, \" \");\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code pays invalide (2 lettres requis) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Num\\u00E9ro de passeport invalide (minimum 5 caract\\u00E8res) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" La date d'expiration doit \\u00EAtre dans le futur \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" La date d'expiration est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code pays invalide (2 lettres requis) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_mat_error_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code pays invalide (2 lettres requis) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_button_171_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function BookingTransactionComponent_mat_expansion_panel_56_button_171_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const i_r25 = i0.ɵɵnextContext().index;\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.removeTraveller(i_r25));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Supprimer ce voyageur \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_mat_expansion_panel_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 72)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, BookingTransactionComponent_mat_expansion_panel_56_mat_panel_description_4_Template, 2, 2, \"mat-panel-description\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 73)(6, \"div\", 20)(7, \"mat-form-field\", 23)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Type de voyageur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-select\", 74);\n    i0.ɵɵtemplate(11, BookingTransactionComponent_mat_expansion_panel_56_mat_option_11_Template, 2, 2, \"mat-option\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-hint\");\n    i0.ɵɵtext(13, \"Veuillez s\\u00E9lectionner un type de voyageur\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 23)(15, \"mat-label\");\n    i0.ɵɵtext(16, \"Titre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-select\", 76);\n    i0.ɵɵtemplate(18, BookingTransactionComponent_mat_expansion_panel_56_mat_option_18_Template, 2, 2, \"mat-option\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-hint\");\n    i0.ɵɵtext(20, \"M., Mme, etc.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-form-field\", 23)(22, \"mat-label\");\n    i0.ɵɵtext(23, \"Type de passager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-select\", 77);\n    i0.ɵɵtemplate(25, BookingTransactionComponent_mat_expansion_panel_56_mat_option_25_Template, 2, 2, \"mat-option\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"mat-hint\");\n    i0.ɵɵtext(27, \"Adulte, Enfant, etc.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 20)(29, \"mat-form-field\", 23)(30, \"mat-label\");\n    i0.ɵɵtext(31, \"Pr\\u00E9nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"input\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-form-field\", 23)(34, \"mat-label\");\n    i0.ɵɵtext(35, \"Nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"input\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"mat-form-field\", 23)(38, \"mat-label\");\n    i0.ɵɵtext(39, \"Date de naissance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(40, \"input\", 80)(41, \"mat-datepicker-toggle\", 46)(42, \"mat-datepicker\", null, 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 20)(45, \"mat-form-field\", 23)(46, \"mat-label\");\n    i0.ɵɵtext(47, \"Genre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"mat-select\", 82);\n    i0.ɵɵtemplate(49, BookingTransactionComponent_mat_expansion_panel_56_mat_option_49_Template, 2, 2, \"mat-option\", 75);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 83)(51, \"mat-form-field\", 23)(52, \"mat-label\");\n    i0.ɵɵtext(53, \"Nationalit\\u00E9 (code pays)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"input\", 84);\n    i0.ɵɵelementStart(55, \"mat-hint\");\n    i0.ɵɵtext(56, \"Code pays \\u00E0 2 lettres (ex: FR, US, GB)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(57, BookingTransactionComponent_mat_expansion_panel_56_mat_error_57_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"mat-form-field\", 23)(59, \"mat-label\");\n    i0.ɵɵtext(60, \"Num\\u00E9ro d'identit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(61, \"input\", 85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 18)(63, \"h4\");\n    i0.ɵɵtext(64, \"Informations de passeport\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"div\", 86)(66, \"div\", 20)(67, \"mat-form-field\", 23)(68, \"mat-label\");\n    i0.ɵɵtext(69, \"Num\\u00E9ro de s\\u00E9rie\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(70, \"input\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"mat-form-field\", 23)(72, \"mat-label\");\n    i0.ɵɵtext(73, \"Num\\u00E9ro de passeport\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(74, \"input\", 88);\n    i0.ɵɵelementStart(75, \"mat-hint\");\n    i0.ɵɵtext(76, \"Minimum 5 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(77, BookingTransactionComponent_mat_expansion_panel_56_mat_error_77_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"div\", 20)(79, \"mat-form-field\", 23)(80, \"mat-label\");\n    i0.ɵɵtext(81, \"Date d'expiration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(82, \"input\", 89)(83, \"mat-datepicker-toggle\", 46)(84, \"mat-datepicker\", null, 90);\n    i0.ɵɵelementStart(86, \"mat-hint\");\n    i0.ɵɵtext(87, \"Doit \\u00EAtre dans le futur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(88, BookingTransactionComponent_mat_expansion_panel_56_mat_error_88_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵtemplate(89, BookingTransactionComponent_mat_expansion_panel_56_mat_error_89_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"mat-form-field\", 23)(91, \"mat-label\");\n    i0.ɵɵtext(92, \"Date d'\\u00E9mission\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(93, \"input\", 91)(94, \"mat-datepicker-toggle\", 46)(95, \"mat-datepicker\", null, 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(97, \"div\", 20)(98, \"mat-form-field\", 23)(99, \"mat-label\");\n    i0.ɵɵtext(100, \"Code pays de citoyennet\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(101, \"input\", 93);\n    i0.ɵɵelementStart(102, \"mat-hint\");\n    i0.ɵɵtext(103, \"Code pays \\u00E0 2 lettres (ex: FR, US, GB)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(104, BookingTransactionComponent_mat_expansion_panel_56_mat_error_104_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"mat-form-field\", 23)(106, \"mat-label\");\n    i0.ɵɵtext(107, \"Code pays d'\\u00E9mission\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(108, \"input\", 94);\n    i0.ɵɵelementStart(109, \"mat-hint\");\n    i0.ɵɵtext(110, \"Code pays \\u00E0 2 lettres (ex: FR, US, GB)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(111, BookingTransactionComponent_mat_expansion_panel_56_mat_error_111_Template, 2, 0, \"mat-error\", 14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(112, \"div\", 18)(113, \"h4\");\n    i0.ɵɵtext(114, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"div\", 95)(116, \"div\", 20)(117, \"div\", 96)(118, \"mat-form-field\", 23)(119, \"mat-label\");\n    i0.ɵɵtext(120, \"Code pays\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(121, \"input\", 97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(122, \"mat-form-field\", 23)(123, \"mat-label\");\n    i0.ɵɵtext(124, \"Indicatif r\\u00E9gional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(125, \"input\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(126, \"mat-form-field\", 23)(127, \"mat-label\");\n    i0.ɵɵtext(128, \"Num\\u00E9ro de t\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(129, \"input\", 99);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(130, \"div\", 20)(131, \"mat-form-field\", 21)(132, \"mat-label\");\n    i0.ɵɵtext(133, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(134, \"input\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(135, \"div\", 20)(136, \"mat-form-field\", 21)(137, \"mat-label\");\n    i0.ɵɵtext(138, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(139, \"input\", 101);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(140, \"div\", 20)(141, \"mat-form-field\", 23)(142, \"mat-label\");\n    i0.ɵɵtext(143, \"Code postal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(144, \"input\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(145, \"div\", 103)(146, \"mat-form-field\", 23)(147, \"mat-label\");\n    i0.ɵɵtext(148, \"ID de ville\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(149, \"input\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(150, \"mat-form-field\", 23)(151, \"mat-label\");\n    i0.ɵɵtext(152, \"Nom de ville\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(153, \"input\", 78);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(154, \"div\", 20)(155, \"div\", 105)(156, \"mat-form-field\", 23)(157, \"mat-label\");\n    i0.ɵɵtext(158, \"ID de pays\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(159, \"input\", 106);\n    i0.ɵɵelementStart(160, \"mat-hint\");\n    i0.ɵɵtext(161, \"Code pays \\u00E0 2 lettres (ex: FR, US, GB)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(162, \"mat-form-field\", 23)(163, \"mat-label\");\n    i0.ɵɵtext(164, \"Nom de pays\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(165, \"input\", 107);\n    i0.ɵɵelementStart(166, \"mat-hint\");\n    i0.ɵɵtext(167, \"Nom complet du pays (ex: France, \\u00C9tats-Unis)\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(168, \"div\", 108)(169, \"mat-checkbox\", 109);\n    i0.ɵɵtext(170, \"Chef de groupe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(171, BookingTransactionComponent_mat_expansion_panel_56_button_171_Template, 4, 0, \"button\", 110);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const traveller_r24 = ctx.$implicit;\n    const i_r25 = ctx.index;\n    const _r30 = i0.ɵɵreference(43);\n    const _r34 = i0.ɵɵreference(85);\n    const _r37 = i0.ɵɵreference(96);\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_14_0;\n    let tmp_15_0;\n    let tmp_18_0;\n    let tmp_19_0;\n    i0.ɵɵproperty(\"expanded\", i_r25 === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Voyageur \", i_r25 + 1, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = traveller_r24.get(\"name\")) == null ? null : tmp_2_0.value) || ((tmp_2_0 = traveller_r24.get(\"surname\")) == null ? null : tmp_2_0.value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroupName\", i_r25);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.passengerTypes);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.travellerTitles);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.passengerTypes);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"matDatepicker\", _r30);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r30);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.genderOptions);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_10_0 = traveller_r24.get(\"nationality.twoLetterCode\")) == null ? null : tmp_10_0.invalid);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngIf\", (tmp_11_0 = traveller_r24.get(\"passportInfo.number\")) == null ? null : tmp_11_0.invalid);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"matDatepicker\", _r34);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r34);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_14_0 = traveller_r24.get(\"passportInfo.expireDate\")) == null ? null : tmp_14_0.errors == null ? null : tmp_14_0.errors[\"expireDateInvalid\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_15_0 = traveller_r24.get(\"passportInfo.expireDate\")) == null ? null : tmp_15_0.errors == null ? null : tmp_15_0.errors[\"required\"]);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"matDatepicker\", _r37);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", _r37);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", (tmp_18_0 = traveller_r24.get(\"passportInfo.citizenshipCountryCode\")) == null ? null : tmp_18_0.invalid);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_19_0 = traveller_r24.get(\"passportInfo.issueCountryCode\")) == null ? null : tmp_19_0.invalid);\n    i0.ɵɵadvance(60);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.travellers.length > 1);\n  }\n}\nfunction BookingTransactionComponent_mat_spinner_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 69);\n  }\n}\nfunction BookingTransactionComponent_span_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Enregistrer les informations\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"h3\");\n    i0.ɵɵtext(2, \"Informations de r\\u00E9servation enregistr\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 112)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Nombre de voyageurs:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.infoResponse.body.reservationData && ctx_r11.infoResponse.body.reservationData.travellers ? ctx_r11.infoResponse.body.reservationData.travellers.length : 0, \"\");\n  }\n}\nfunction BookingTransactionComponent_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Paiement et confirmation\");\n  }\n}\nfunction BookingTransactionComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r13.errorMessage);\n  }\n}\nfunction BookingTransactionComponent_mat_spinner_144_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 69);\n  }\n}\nfunction BookingTransactionComponent_span_145_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Finaliser la r\\u00E9servation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingTransactionComponent_div_148_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"h3\");\n    i0.ɵɵtext(2, \"R\\u00E9servation confirm\\u00E9e!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 114)(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Num\\u00E9ro de r\\u00E9servation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 115)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12, \"Votre r\\u00E9servation a \\u00E9t\\u00E9 confirm\\u00E9e avec succ\\u00E8s. Vous recevrez bient\\u00F4t un email de confirmation avec tous les d\\u00E9tails de votre voyage.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 116)(14, \"button\", 52);\n    i0.ɵɵtext(15, \" Retour \\u00E0 la recherche \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.commitResponse.body.reservationNumber || \"N/A\", \"\");\n  }\n}\nexport class BookingTransactionComponent {\n  constructor(fb, route, router, bookingService, authService, sharedDataService, countryService, snackBar) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.bookingService = bookingService;\n    this.authService = authService;\n    this.sharedDataService = sharedDataService;\n    this.countryService = countryService;\n    this.snackBar = snackBar;\n    // Étape actuelle du processus de réservation\n    this.currentStep = 1;\n    // Données de transaction\n    this.transactionId = '';\n    this.offerIds = [];\n    this.searchId = '';\n    this.passengersParam = '';\n    // Informations de passagers\n    this.passengerCounts = {};\n    // Réponses des différentes étapes\n    this.beginResponse = null;\n    this.infoResponse = null;\n    this.commitResponse = null;\n    // États de chargement et d'erreur\n    this.isLoading = false;\n    this.errorMessage = '';\n    // Options pour les formulaires\n    this.travellerTitles = Object.keys(TravellerTitle).filter(key => !isNaN(Number(TravellerTitle[key]))).map(key => ({\n      value: TravellerTitle[key],\n      label: key\n    }));\n    this.genderOptions = Object.keys(Gender).filter(key => !isNaN(Number(Gender[key]))).map(key => ({\n      value: Gender[key],\n      label: key\n    }));\n    this.passengerTypes = Object.keys(PassengerType).filter(key => !isNaN(Number(PassengerType[key]))).map(key => ({\n      value: PassengerType[key],\n      label: key\n    }));\n    // Liste des pays pour le sélecteur\n    this.countries = [];\n    // Initialisation des formulaires sans valeurs par défaut\n    this.beginTransactionForm = this.fb.group({\n      currency: ['', Validators.required],\n      culture: ['', Validators.required]\n    });\n    this.reservationInfoForm = this.fb.group({\n      transactionId: ['', Validators.required],\n      travellers: this.fb.array([]),\n      reservationNote: [''],\n      agencyReservationNumber: ['']\n    });\n    this.commitTransactionForm = this.fb.group({\n      transactionId: ['', Validators.required],\n      paymentOption: [null],\n      paymentInformation: this.fb.group({\n        accountName: [''],\n        paymentTypeId: [null],\n        paymentPrice: this.fb.group({\n          amount: [null, Validators.required],\n          currency: ['', Validators.required]\n        }),\n        installmentCount: [''],\n        paymentDate: [''],\n        receiptType: [''],\n        reference: [''],\n        paymentToken: ['']\n      })\n    });\n  }\n  ngOnInit() {\n    // Charger la liste des pays\n    this.countryService.getCountries().subscribe(countries => {\n      this.countries = countries;\n    });\n    // Récupérer les offerIds, transactionId et informations de passagers depuis les paramètres de l'URL\n    this.route.queryParams.subscribe(params => {\n      // Récupérer les offerIds\n      if (params['offerIds']) {\n        try {\n          this.offerIds = Array.isArray(params['offerIds']) ? params['offerIds'] : [params['offerIds']];\n          if (this.offerIds.length > 0) {\n            console.log('OfferIds récupérés:', this.offerIds);\n          } else {\n            this.errorMessage = 'Aucun ID d\\'offre n\\'a été fourni.';\n          }\n        } catch (error) {\n          console.error('Erreur lors de la récupération des offerIds:', error);\n          this.errorMessage = 'Format d\\'ID d\\'offre invalide.';\n        }\n      } else {\n        this.errorMessage = 'Aucun ID d\\'offre n\\'a été fourni.';\n      }\n      // Récupérer le searchId s'il est présent\n      if (params['searchId']) {\n        this.searchId = params['searchId'];\n        console.log('SearchId récupéré:', this.searchId);\n      }\n      // Récupérer les informations de passagers\n      if (params['passengers']) {\n        this.passengersParam = params['passengers'];\n        try {\n          this.passengerCounts = JSON.parse(this.passengersParam);\n          // Mettre à jour le service partagé avec les informations de passagers\n          this.sharedDataService.setPassengerCounts(this.passengerCounts);\n          console.log('Passenger counts parsed successfully:', this.passengerCounts);\n        } catch (error) {\n          console.error('Error parsing passenger information:', error);\n          // Utiliser les valeurs par défaut du service\n          this.passengerCounts = this.sharedDataService.getPassengerCounts();\n        }\n      } else {\n        // Utiliser les valeurs par défaut du service\n        this.passengerCounts = this.sharedDataService.getPassengerCounts();\n      }\n      // Vérifier si une transaction a déjà été démarrée automatiquement\n      if (params['transactionId'] && params['autoStarted'] === 'true') {\n        console.log('Transaction déjà démarrée avec ID:', params['transactionId']);\n        this.transactionId = params['transactionId'];\n        // Mettre à jour le formulaire d'informations de réservation avec l'ID de transaction\n        this.reservationInfoForm.patchValue({\n          transactionId: this.transactionId\n        });\n        // Créer un objet de réponse simulé pour l'étape 1\n        this.beginResponse = {\n          header: {\n            requestId: '',\n            success: true,\n            responseTime: new Date().toISOString(),\n            messages: []\n          },\n          body: {\n            transactionId: this.transactionId,\n            expiresOn: new Date(Date.now() + 3600000).toISOString(),\n            status: 1,\n            transactionType: 1,\n            reservationData: {\n              travellers: [],\n              reservationInfo: {\n                bookingNumber: '',\n                agency: {\n                  code: '',\n                  name: '',\n                  country: {\n                    id: '',\n                    name: ''\n                  },\n                  address: {},\n                  ownAgency: false,\n                  aceExport: false\n                },\n                agencyUser: {\n                  id: 0,\n                  name: '',\n                  surname: '',\n                  email: ''\n                },\n                beginDate: new Date().toISOString(),\n                endDate: new Date().toISOString(),\n                note: '',\n                salePrice: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                supplementDiscount: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                passengerEB: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                agencyEB: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                passengerAmountToPay: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                agencyAmountToPay: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                discount: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                agencyBalance: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                passengerBalance: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                agencyCommission: {\n                  amount: 0,\n                  currency: 'EUR',\n                  rate: 0\n                },\n                brokerCommission: {\n                  amount: 0,\n                  currency: 'EUR',\n                  rate: 0\n                },\n                agencySupplementCommission: {\n                  amount: 0,\n                  currency: 'EUR',\n                  rate: 0\n                },\n                promotionAmount: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                priceToPay: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                agencyPriceToPay: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                passengerPriceToPay: {\n                  amount: 0,\n                  currency: 'EUR'\n                },\n                totalPrice: {\n                  amount: 0,\n                  currency: 'EUR'\n                }\n              },\n              services: [],\n              paymentDetail: {},\n              invoices: []\n            }\n          }\n        };\n        // Créer dynamiquement les voyageurs en fonction des informations de passagers\n        this.createTravellersFromPassengerCounts();\n        // Passer directement à l'étape 2\n        this.currentStep = 2;\n        // Afficher un message d'information\n        this.snackBar.open('Transaction déjà démarrée. Veuillez compléter les informations des voyageurs.', 'Fermer', {\n          duration: 5000,\n          panelClass: ['info-snackbar']\n        });\n      }\n    });\n  }\n  // Créer dynamiquement les voyageurs en fonction des informations de passagers\n  createTravellersFromPassengerCounts() {\n    // Vider le tableau de voyageurs existant\n    while (this.travellers.length > 0) {\n      this.travellers.removeAt(0);\n    }\n    // Parcourir les types de passagers et créer le nombre correspondant de voyageurs\n    Object.entries(this.passengerCounts).forEach(([typeStr, count]) => {\n      const type = parseInt(typeStr);\n      for (let i = 0; i < count; i++) {\n        // Créer un voyageur avec le type de passager correspondant\n        const travellerForm = this.fb.group({\n          type: [type, Validators.required],\n          title: [null, Validators.required],\n          passengerType: [type, Validators.required],\n          name: ['', [Validators.required, Validators.minLength(2)]],\n          surname: ['', [Validators.required, Validators.minLength(2)]],\n          isLeader: [this.travellers?.length === 0],\n          birthDate: ['', Validators.required],\n          nationality: this.fb.group({\n            twoLetterCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n          }),\n          identityNumber: [''],\n          passportInfo: this.fb.group({\n            serial: [''],\n            number: ['', [Validators.required, Validators.minLength(5)]],\n            expireDate: ['', Validators.required],\n            issueDate: ['', Validators.required],\n            citizenshipCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n            issueCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n          }),\n          address: this.fb.group({\n            contactPhone: this.fb.group({\n              countryCode: ['', Validators.required],\n              areaCode: [''],\n              phoneNumber: ['', [Validators.required, Validators.minLength(5)]]\n            }),\n            email: ['', [Validators.required, Validators.email]],\n            address: ['', [Validators.required, Validators.minLength(5)]],\n            zipCode: ['', [Validators.required, Validators.minLength(3)]],\n            city: this.fb.group({\n              id: ['', Validators.required],\n              name: ['', [Validators.required, Validators.minLength(2)]]\n            }),\n            country: this.fb.group({\n              id: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n              name: ['', [Validators.required, Validators.minLength(2)]]\n            })\n          }),\n          gender: [null, Validators.required]\n        });\n        // Ajouter des validateurs personnalisés\n        this.addCustomValidators(travellerForm);\n        // Ajouter le voyageur au formulaire\n        this.travellers.push(travellerForm);\n      }\n    });\n    // S'assurer qu'il y a au moins un voyageur\n    if (this.travellers.length === 0) {\n      this.addTraveller();\n    }\n  }\n  // Getter pour accéder au FormArray des voyageurs\n  get travellers() {\n    return this.reservationInfoForm.get('travellers');\n  }\n  // Ajouter un nouveau voyageur au formulaire\n  addTraveller() {\n    const travellerForm = this.fb.group({\n      type: [null, Validators.required],\n      title: [null, Validators.required],\n      passengerType: [null, Validators.required],\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      surname: ['', [Validators.required, Validators.minLength(2)]],\n      isLeader: [this.travellers?.length === 0],\n      birthDate: ['', Validators.required],\n      nationality: this.fb.group({\n        twoLetterCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      identityNumber: [''],\n      passportInfo: this.fb.group({\n        serial: [''],\n        number: ['', [Validators.required, Validators.minLength(5)]],\n        expireDate: ['', Validators.required],\n        issueDate: ['', Validators.required],\n        citizenshipCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n        issueCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      address: this.fb.group({\n        contactPhone: this.fb.group({\n          countryCode: ['', Validators.required],\n          areaCode: [''],\n          phoneNumber: ['', [Validators.required, Validators.minLength(5)]]\n        }),\n        email: ['', [Validators.required, Validators.email]],\n        address: ['', [Validators.required, Validators.minLength(5)]],\n        zipCode: ['', [Validators.required, Validators.minLength(3)]],\n        city: this.fb.group({\n          id: ['', Validators.required],\n          name: ['', [Validators.required, Validators.minLength(2)]]\n        }),\n        country: this.fb.group({\n          id: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n          name: ['', [Validators.required, Validators.minLength(2)]]\n        })\n      }),\n      gender: [null, Validators.required] // Genre\n    });\n    // Ajouter des validateurs personnalisés\n    this.addCustomValidators(travellerForm);\n    this.travellers.push(travellerForm);\n  }\n  // Ajouter des validateurs personnalisés au formulaire de voyageur\n  addCustomValidators(form) {\n    // Ne pas synchroniser automatiquement issueCountryCode avec la nationalité\n    // pour permettre une saisie entièrement dynamique\n    const nationalityControl = form.get('nationality.twoLetterCode');\n    const issueCountryCodeControl = form.get('passportInfo.issueCountryCode');\n    // Nous ne synchronisons plus automatiquement les valeurs\n    // L'utilisateur doit remplir tous les champs manuellement\n    // Vérifier que la date d'expiration du passeport est dans le futur\n    const expireDateControl = form.get('passportInfo.expireDate');\n    if (expireDateControl) {\n      expireDateControl.setValidators([Validators.required, control => {\n        const value = control.value;\n        if (!value) return null;\n        const expireDate = new Date(value);\n        const today = new Date();\n        return expireDate > today ? null : {\n          'expireDateInvalid': true\n        };\n      }]);\n    }\n  }\n  // Supprimer un voyageur du formulaire\n  removeTraveller(index) {\n    this.travellers.removeAt(index);\n  }\n  // Démarrer la transaction de réservation\n  beginTransaction() {\n    if (this.beginTransactionForm.invalid) {\n      this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {\n        duration: 3000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    const formValue = this.beginTransactionForm.value;\n    this.bookingService.beginTransaction(this.offerIds, formValue.currency, formValue.culture).subscribe({\n      next: response => {\n        this.isLoading = false;\n        console.log('Réponse reçue:', response);\n        if (response && response.beginResponse && response.beginResponse.body) {\n          this.beginResponse = response.beginResponse;\n          this.transactionId = response.beginResponse.body.transactionId || '';\n          // Mettre à jour le formulaire d'informations de réservation avec l'ID de transaction\n          this.reservationInfoForm.patchValue({\n            transactionId: this.transactionId\n          });\n          // Ajouter les voyageurs existants s'il y en a dans la réponse\n          if (response.beginResponse.body.reservationData && response.beginResponse.body.reservationData.travellers && response.beginResponse.body.reservationData.travellers.length > 0) {\n            // Vider le tableau de voyageurs existant\n            while (this.travellers.length > 0) {\n              this.travellers.removeAt(0);\n            }\n            // Ajouter les voyageurs de la réponse\n            response.beginResponse.body.reservationData.travellers.forEach(traveller => {\n              this.addTravellerFromResponse(traveller);\n            });\n          } else {\n            // Créer dynamiquement les voyageurs en fonction des informations de passagers\n            this.createTravellersFromPassengerCounts();\n          }\n          // Passer à l'étape suivante\n          this.currentStep = 2;\n          this.snackBar.open('Transaction démarrée avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = 'Réponse de transaction invalide.';\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        console.error('Erreur lors du démarrage de la transaction:', error);\n        this.errorMessage = error.message || 'Une erreur est survenue lors du démarrage de la transaction.';\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  // Ajouter un voyageur à partir de la réponse de l'API\n  addTravellerFromResponse(traveller) {\n    // Utiliser uniquement les données fournies par l'API, sans valeurs par défaut\n    const travellerForm = this.fb.group({\n      travellerId: [traveller.travellerId || ''],\n      type: [traveller.type, Validators.required],\n      title: [traveller.title, Validators.required],\n      passengerType: [traveller.passengerType, Validators.required],\n      name: [traveller.name || '', [Validators.required, Validators.minLength(2)]],\n      surname: [traveller.surname || '', [Validators.required, Validators.minLength(2)]],\n      isLeader: [traveller.isLeader !== undefined ? traveller.isLeader : this.travellers?.length === 0],\n      birthDate: [traveller.birthDate || '', Validators.required],\n      nationality: this.fb.group({\n        twoLetterCode: [traveller.nationality?.twoLetterCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      identityNumber: [traveller.identityNumber || ''],\n      passportInfo: this.fb.group({\n        serial: [traveller.passportInfo?.serial || ''],\n        number: [traveller.passportInfo?.number || '', [Validators.required, Validators.minLength(5)]],\n        expireDate: [traveller.passportInfo?.expireDate || '', Validators.required],\n        issueDate: [traveller.passportInfo?.issueDate || '', Validators.required],\n        citizenshipCountryCode: [traveller.passportInfo?.citizenshipCountryCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n        issueCountryCode: [traveller.passportInfo?.issueCountryCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      address: this.fb.group({\n        contactPhone: this.fb.group({\n          countryCode: [traveller.address?.contactPhone?.countryCode || '', Validators.required],\n          areaCode: [''],\n          phoneNumber: [traveller.address?.contactPhone?.phoneNumber || '', [Validators.required, Validators.minLength(5)]]\n        }),\n        email: [traveller.address?.email || '', [Validators.required, Validators.email]],\n        address: [traveller.address?.address || '', [Validators.required, Validators.minLength(5)]],\n        zipCode: [traveller.address?.zipCode || '', [Validators.required, Validators.minLength(3)]],\n        city: this.fb.group({\n          id: [traveller.address?.city?.id || '', Validators.required],\n          name: [traveller.address?.city?.name || '', [Validators.required, Validators.minLength(2)]]\n        }),\n        country: this.fb.group({\n          id: [traveller.address?.country?.id || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n          name: [traveller.address?.country?.name || '', [Validators.required, Validators.minLength(2)]]\n        })\n      }),\n      gender: [traveller.gender, Validators.required]\n    });\n    // Ajouter des validateurs personnalisés\n    this.addCustomValidators(travellerForm);\n    this.travellers.push(travellerForm);\n  }\n  // Définir les informations de réservation\n  setReservationInfo() {\n    if (this.reservationInfoForm.invalid) {\n      // Vérifier les erreurs spécifiques pour donner des messages plus précis\n      let errorMessage = 'Veuillez remplir tous les champs obligatoires.';\n      // Vérifier les erreurs de passeport\n      const travellers = this.travellers.controls;\n      for (let i = 0; i < travellers.length; i++) {\n        const traveller = travellers[i];\n        // Vérifier les erreurs de date d'expiration du passeport\n        const expireDateControl = traveller.get('passportInfo.expireDate');\n        if (expireDateControl?.errors?.['expireDateInvalid']) {\n          errorMessage = `Voyageur ${i + 1}: La date d'expiration du passeport doit être dans le futur.`;\n          break;\n        }\n        // Vérifier les erreurs de code pays\n        const issueCountryCodeControl = traveller.get('passportInfo.issueCountryCode');\n        if (issueCountryCodeControl?.invalid && issueCountryCodeControl?.touched) {\n          errorMessage = `Voyageur ${i + 1}: Le code pays d'émission du passeport est invalide.`;\n          break;\n        }\n        // Vérifier les erreurs de numéro de passeport\n        const passportNumberControl = traveller.get('passportInfo.number');\n        if (passportNumberControl?.invalid && passportNumberControl?.touched) {\n          errorMessage = `Voyageur ${i + 1}: Le numéro de passeport est invalide.`;\n          break;\n        }\n      }\n      this.snackBar.open(errorMessage, 'Fermer', {\n        duration: 5000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    const formValue = this.reservationInfoForm.value;\n    // Formater correctement les dates pour chaque voyageur\n    const formattedTravellers = formValue.travellers.map(traveller => {\n      // Créer un nouvel objet pour éviter de modifier l'original\n      const formattedTraveller = {\n        ...traveller\n      };\n      // Formater la date de naissance\n      if (formattedTraveller.birthDate) {\n        // Convertir en Date si c'est un objet Date ou une chaîne\n        const birthDate = new Date(formattedTraveller.birthDate);\n        if (!isNaN(birthDate.getTime())) {\n          formattedTraveller.birthDate = this.formatDateForApi(birthDate);\n        }\n      }\n      // Formater les dates de passeport si présentes\n      if (formattedTraveller.passportInfo) {\n        const passportInfo = {\n          ...formattedTraveller.passportInfo\n        };\n        if (passportInfo.expireDate) {\n          const expireDate = new Date(passportInfo.expireDate);\n          if (!isNaN(expireDate.getTime())) {\n            passportInfo.expireDate = this.formatDateForApi(expireDate);\n          }\n        }\n        if (passportInfo.issueDate) {\n          const issueDate = new Date(passportInfo.issueDate);\n          if (!isNaN(issueDate.getTime())) {\n            passportInfo.issueDate = this.formatDateForApi(issueDate);\n          }\n        }\n        formattedTraveller.passportInfo = passportInfo;\n      }\n      return formattedTraveller;\n    });\n    // Vérifier si tous les passagers sont des enfants ou des bébés\n    const hasAdult = formattedTravellers.some(traveller => traveller.passengerType === PassengerType.Adult);\n    // Créer la requête d'informations de réservation\n    // Ne pas inclure customerInfo si tous les passagers sont des enfants ou des bébés\n    const request = {\n      transactionId: formValue.transactionId,\n      travellers: formattedTravellers,\n      reservationNote: formValue.reservationNote,\n      agencyReservationNumber: formValue.agencyReservationNumber\n    };\n    // Afficher un message de log pour indiquer si customerInfo est inclus ou non\n    console.log('Requête setReservationInfo - Présence d\\'adultes:', hasAdult);\n    console.log('Requête setReservationInfo - customerInfo non inclus car tous les passagers sont des enfants ou des bébés');\n    this.bookingService.setReservationInfo(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        console.log('Réponse setReservationInfo reçue:', response);\n        if (response && response.infoResponse && response.infoResponse.body) {\n          this.infoResponse = response.infoResponse;\n          // Mettre à jour le formulaire de finalisation de transaction avec l'ID de transaction\n          this.commitTransactionForm.patchValue({\n            transactionId: this.transactionId\n          });\n          // Mettre à jour le montant du paiement si disponible\n          if (response.infoResponse.body.reservationData && response.infoResponse.body.reservationData.reservationInfo && response.infoResponse.body.reservationData.reservationInfo.priceToPay) {\n            const priceToPay = response.infoResponse.body.reservationData.reservationInfo.priceToPay;\n            this.commitTransactionForm.get('paymentInformation.paymentPrice')?.patchValue({\n              amount: priceToPay.amount,\n              currency: priceToPay.currency\n            });\n          }\n          // Passer à l'étape suivante\n          this.currentStep = 3;\n          this.snackBar.open('Informations de réservation définies avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = 'Réponse d\\'informations de réservation invalide.';\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        console.error('Erreur lors de la définition des informations de réservation:', error);\n        // Analyser le message d'erreur pour des problèmes spécifiques\n        let errorMessage = error.message || 'Une erreur est survenue lors de la définition des informations de réservation.';\n        // Vérifier les erreurs spécifiques liées au passeport\n        if (errorMessage.includes('passport') || errorMessage.includes('issueCountryCode')) {\n          errorMessage = 'Erreur de validation du passeport: Veuillez vérifier que toutes les informations de passeport sont complètes, notamment le code pays d\\'émission.';\n        }\n        // Vérifier les erreurs liées aux dates\n        else if (errorMessage.includes('date') || errorMessage.includes('expireDate')) {\n          errorMessage = 'Erreur de validation des dates: Veuillez vérifier que toutes les dates sont au format correct (YYYY-MM-DD).';\n        }\n        // Vérifier les erreurs liées aux informations personnelles\n        else if (errorMessage.includes('name') || errorMessage.includes('surname')) {\n          errorMessage = 'Erreur de validation des informations personnelles: Veuillez vérifier que tous les noms et prénoms sont correctement renseignés.';\n        }\n        this.errorMessage = errorMessage;\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 8000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  // Finaliser la transaction de réservation\n  commitTransaction() {\n    if (this.commitTransactionForm.invalid) {\n      this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {\n        duration: 3000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    const formValue = this.commitTransactionForm.value;\n    this.bookingService.commitTransaction(formValue).subscribe({\n      next: response => {\n        this.isLoading = false;\n        console.log('Réponse commitTransaction reçue:', response);\n        if (response && response.commitResponse && response.commitResponse.body) {\n          this.commitResponse = response.commitResponse;\n          this.snackBar.open('Réservation finalisée avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n          // Afficher les détails de la réservation finalisée\n          // Vous pourriez rediriger vers une page de confirmation ici\n        } else {\n          this.errorMessage = 'Réponse de finalisation de transaction invalide.';\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        console.error('Erreur lors de la finalisation de la transaction:', error);\n        this.errorMessage = error.message || 'Une erreur est survenue lors de la finalisation de la transaction.';\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n  // Revenir à l'étape précédente\n  previousStep() {\n    // Si la transaction a été démarrée automatiquement, ne pas permettre de revenir à l'étape 1\n    if (this.currentStep > 2 || this.currentStep === 2 && !this.isAutoStarted()) {\n      this.currentStep--;\n    } else {\n      // Si on est à l'étape 2 et que la transaction a été démarrée automatiquement,\n      // proposer de retourner à la page de sélection de vol\n      this.snackBar.open('La transaction a déjà été démarrée. Voulez-vous annuler et retourner à la sélection de vol?', 'Retour', {\n        duration: 5000,\n        panelClass: ['warning-snackbar']\n      }).onAction().subscribe(() => {\n        this.router.navigate(['/get-offer'], {\n          queryParams: {\n            searchId: this.searchId,\n            offerId: this.offerIds[0]\n          }\n        });\n      });\n    }\n  }\n  // Vérifier si la transaction a été démarrée automatiquement\n  isAutoStarted() {\n    return this.route.snapshot.queryParams['autoStarted'] === 'true';\n  }\n  // Formater une date pour l'affichage\n  formatDate(dateString) {\n    if (!dateString) return 'N/A';\n    const date = new Date(dateString);\n    return date.toLocaleDateString();\n  }\n  // Formater une date pour l'API (format ISO 8601: YYYY-MM-DD)\n  formatDateForApi(date) {\n    if (!date) return '';\n    // S'assurer que c'est une instance de Date valide\n    if (!(date instanceof Date) || isNaN(date.getTime())) {\n      console.error('Date invalide:', date);\n      return '';\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  // Formater un prix pour l'affichage\n  formatPrice(amount, currency) {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: currency\n    }).format(amount);\n  }\n  // Gérer la sélection d'un pays\n  onCountrySelected(country, travellerIndex, field) {\n    console.log(`Pays sélectionné pour le voyageur ${travellerIndex}, champ ${field}:`, country);\n    const traveller = this.travellers.at(travellerIndex);\n    // Mettre à jour le champ correspondant\n    switch (field) {\n      case 'nationality':\n        traveller.get('nationality.twoLetterCode')?.setValue(country.code);\n        break;\n      case 'citizenshipCountryCode':\n        traveller.get('passportInfo.citizenshipCountryCode')?.setValue(country.code);\n        break;\n      case 'issueCountryCode':\n        traveller.get('passportInfo.issueCountryCode')?.setValue(country.code);\n        break;\n      case 'country':\n        traveller.get('address.country.id')?.setValue(country.code);\n        traveller.get('address.country.name')?.setValue(country.name);\n        break;\n    }\n  }\n  // Formater un ID d'offre pour l'affichage\n  formatOfferId(offerId) {\n    if (!offerId) return 'N/A';\n    // Extraire les informations pertinentes de l'ID d'offre\n    // Format typique: 13$3$1~^006^~AAABloLbX6oAAAAClhW0YYMc26FHjKULIrlKaQAAAZaC2zyuAAAAALH6UPiglvPwEjLokBT6TDI=~^006^~1~^006^~154.66~^006^~~^006^~154.66~^006^~ODBiZTZiMGQtYWYxYy00MzYzLThmNjctODcyNTA0NjVjZjgz\n    // Extraire le début de l'ID (avant le premier ~)\n    const parts = offerId.split('~');\n    const firstPart = parts[0] || '';\n    // Extraire les informations de base (type de vol, classe, etc.)\n    const basicInfo = firstPart.split('$');\n    // Créer un identifiant court\n    const shortId = offerId.substring(0, 8) + '...' + offerId.substring(offerId.length - 8);\n    return `Vol #${basicInfo[0] || 'N/A'} - Référence: ${shortId}`;\n  }\n  static {\n    this.ɵfac = function BookingTransactionComponent_Factory(t) {\n      return new (t || BookingTransactionComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.BookingService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.SharedDataService), i0.ɵɵdirectiveInject(i6.CountryService), i0.ɵɵdirectiveInject(i7.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BookingTransactionComponent,\n      selectors: [[\"app-booking-transaction\"]],\n      decls: 149,\n      vars: 31,\n      consts: [[1, \"booking-transaction-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"header-illustration\"], [\"src\", \"assets/images/booking-banner.jpg\", \"alt\", \"R\\u00E9servation de vol\"], [3, \"linear\", \"selectedIndex\"], [\"stepper\", \"\"], [3, \"completed\", \"editable\"], [\"matStepLabel\", \"\"], [1, \"step-content\"], [1, \"step-header\"], [\"class\", \"error-message\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"response-summary\", 4, \"ngIf\"], [3, \"completed\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"form-section\"], [\"type\", \"hidden\", \"formControlName\", \"transactionId\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"reservationNote\", \"rows\", \"3\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"agencyReservationNumber\"], [1, \"section-header\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", \"type\", \"button\", \"aria-label\", \"Ajouter un voyageur\", 3, \"click\"], [\"formArrayName\", \"travellers\"], [3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"button-spinner\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"formControlName\", \"paymentOption\"], [3, \"value\"], [\"formGroupName\", \"paymentInformation\"], [\"matInput\", \"\", \"formControlName\", \"accountName\"], [\"formControlName\", \"paymentTypeId\"], [\"formGroupName\", \"paymentPrice\"], [\"matInput\", \"\", \"formControlName\", \"amount\", \"type\", \"number\", \"required\", \"\"], [\"formControlName\", \"currency\"], [\"value\", \"EUR\"], [\"value\", \"USD\"], [\"value\", \"GBP\"], [\"matInput\", \"\", \"formControlName\", \"installmentCount\"], [\"matInput\", \"\", \"formControlName\", \"paymentDate\", 3, \"matDatepicker\"], [\"matSuffix\", \"\", 3, \"for\"], [\"paymentDatePicker\", \"\"], [\"matInput\", \"\", \"formControlName\", \"receiptType\"], [\"matInput\", \"\", \"formControlName\", \"reference\"], [\"class\", \"response-summary success-response\", 4, \"ngIf\"], [1, \"error-message\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/search-price\"], [1, \"offer-summary\"], [1, \"offer-ids\"], [1, \"offer-card-container\"], [\"class\", \"offer-card\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"currency\", \"placeholder\", \"S\\u00E9lectionnez une devise\"], [\"formControlName\", \"culture\", \"placeholder\", \"S\\u00E9lectionnez une langue\"], [\"value\", \"fr-FR\"], [\"value\", \"en-US\"], [\"value\", \"en-GB\"], [\"mat-button\", \"\", \"type\", \"button\", \"routerLink\", \"/get-offer\"], [1, \"offer-card\"], [1, \"offer-card-header\"], [1, \"fas\", \"fa-plane\"], [1, \"offer-title\"], [1, \"offer-card-content\"], [1, \"offer-info\"], [\"diameter\", \"20\", 1, \"button-spinner\"], [1, \"response-summary\"], [1, \"transaction-details\"], [3, \"expanded\"], [3, \"formGroupName\"], [\"formControlName\", \"type\", \"placeholder\", \"S\\u00E9lectionnez un type\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"title\", \"placeholder\", \"S\\u00E9lectionnez un titre\"], [\"formControlName\", \"passengerType\", \"placeholder\", \"S\\u00E9lectionnez un type\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"surname\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"birthDate\", \"required\", \"\", 3, \"matDatepicker\"], [\"birthDatePicker\", \"\"], [\"formControlName\", \"gender\"], [\"formGroupName\", \"nationality\"], [\"matInput\", \"\", \"formControlName\", \"twoLetterCode\", \"required\", \"\", \"placeholder\", \"Entrez le code pays\"], [\"matInput\", \"\", \"formControlName\", \"identityNumber\"], [\"formGroupName\", \"passportInfo\"], [\"matInput\", \"\", \"formControlName\", \"serial\"], [\"matInput\", \"\", \"formControlName\", \"number\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"expireDate\", \"required\", \"\", 3, \"matDatepicker\"], [\"expireDatePicker\", \"\"], [\"matInput\", \"\", \"formControlName\", \"issueDate\", \"required\", \"\", 3, \"matDatepicker\"], [\"issueDatePicker\", \"\"], [\"matInput\", \"\", \"formControlName\", \"citizenshipCountryCode\", \"required\", \"\", \"placeholder\", \"Entrez le code pays\"], [\"matInput\", \"\", \"formControlName\", \"issueCountryCode\", \"required\", \"\", \"placeholder\", \"Entrez le code pays\"], [\"formGroupName\", \"address\"], [\"formGroupName\", \"contactPhone\"], [\"matInput\", \"\", \"formControlName\", \"countryCode\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"areaCode\"], [\"matInput\", \"\", \"formControlName\", \"phoneNumber\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"required\", \"\", \"type\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"zipCode\", \"required\", \"\"], [\"formGroupName\", \"city\"], [\"matInput\", \"\", \"formControlName\", \"id\", \"required\", \"\"], [\"formGroupName\", \"country\"], [\"matInput\", \"\", \"formControlName\", \"id\", \"required\", \"\", \"placeholder\", \"Entrez le code pays\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"required\", \"\", \"placeholder\", \"Entrez le nom du pays\"], [1, \"form-row\", \"traveller-actions\"], [\"formControlName\", \"isLeader\"], [\"mat-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [1, \"reservation-details\"], [1, \"response-summary\", \"success-response\"], [1, \"confirmation-details\"], [1, \"confirmation-message\"], [1, \"confirmation-actions\"]],\n      template: function BookingTransactionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"R\\u00E9servation de vol\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Compl\\u00E9tez votre r\\u00E9servation en quelques \\u00E9tapes simples\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵelement(8, \"img\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-horizontal-stepper\", 7, 8)(11, \"mat-step\", 9);\n          i0.ɵɵtemplate(12, BookingTransactionComponent_ng_template_12_Template, 1, 1, \"ng-template\", 10);\n          i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"h2\");\n          i0.ɵɵtext(16, \"D\\u00E9marrer votre r\\u00E9servation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\");\n          i0.ɵɵtext(18, \"Nous allons commencer le processus de r\\u00E9servation pour les vols s\\u00E9lectionn\\u00E9s.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(19, BookingTransactionComponent_div_19_Template, 5, 1, \"div\", 13);\n          i0.ɵɵtemplate(20, BookingTransactionComponent_div_20_Template, 7, 0, \"div\", 13);\n          i0.ɵɵtemplate(21, BookingTransactionComponent_div_21_Template, 41, 6, \"div\", 14);\n          i0.ɵɵtemplate(22, BookingTransactionComponent_div_22_Template, 12, 2, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"mat-step\", 16);\n          i0.ɵɵtemplate(24, BookingTransactionComponent_ng_template_24_Template, 1, 0, \"ng-template\", 10);\n          i0.ɵɵelementStart(25, \"div\", 11)(26, \"div\", 12)(27, \"h2\");\n          i0.ɵɵtext(28, \"Informations des voyageurs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"p\");\n          i0.ɵɵtext(30, \"Veuillez fournir les informations pour tous les voyageurs.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, BookingTransactionComponent_div_31_Template, 5, 1, \"div\", 13);\n          i0.ɵɵelementStart(32, \"form\", 17);\n          i0.ɵɵlistener(\"ngSubmit\", function BookingTransactionComponent_Template_form_ngSubmit_32_listener() {\n            return ctx.setReservationInfo();\n          });\n          i0.ɵɵelementStart(33, \"div\", 18)(34, \"h3\");\n          i0.ɵɵtext(35, \"Informations g\\u00E9n\\u00E9rales\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"input\", 19);\n          i0.ɵɵelementStart(37, \"div\", 20)(38, \"mat-form-field\", 21)(39, \"mat-label\");\n          i0.ɵɵtext(40, \"Note de r\\u00E9servation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(41, \"textarea\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 20)(43, \"mat-form-field\", 23)(44, \"mat-label\");\n          i0.ɵɵtext(45, \"Num\\u00E9ro de r\\u00E9servation d'agence\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"input\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"div\", 18)(48, \"div\", 25)(49, \"h3\");\n          i0.ɵɵtext(50, \"Voyageurs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function BookingTransactionComponent_Template_button_click_51_listener() {\n            return ctx.addTraveller();\n          });\n          i0.ɵɵelementStart(52, \"mat-icon\");\n          i0.ɵɵtext(53, \"add\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 27)(55, \"mat-accordion\");\n          i0.ɵɵtemplate(56, BookingTransactionComponent_mat_expansion_panel_56_Template, 172, 21, \"mat-expansion-panel\", 28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(57, \"div\", 29)(58, \"button\", 30);\n          i0.ɵɵtemplate(59, BookingTransactionComponent_mat_spinner_59_Template, 1, 0, \"mat-spinner\", 31);\n          i0.ɵɵtemplate(60, BookingTransactionComponent_span_60_Template, 2, 0, \"span\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function BookingTransactionComponent_Template_button_click_61_listener() {\n            return ctx.previousStep();\n          });\n          i0.ɵɵtext(62, \"Retour\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(63, BookingTransactionComponent_div_63_Template, 8, 1, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"mat-step\", 16);\n          i0.ɵɵtemplate(65, BookingTransactionComponent_ng_template_65_Template, 1, 0, \"ng-template\", 10);\n          i0.ɵɵelementStart(66, \"div\", 11)(67, \"div\", 12)(68, \"h2\");\n          i0.ɵɵtext(69, \"Paiement et confirmation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"p\");\n          i0.ɵɵtext(71, \"Finalisez votre r\\u00E9servation en effectuant le paiement.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(72, BookingTransactionComponent_div_72_Template, 5, 1, \"div\", 13);\n          i0.ɵɵelementStart(73, \"form\", 17);\n          i0.ɵɵlistener(\"ngSubmit\", function BookingTransactionComponent_Template_form_ngSubmit_73_listener() {\n            return ctx.commitTransaction();\n          });\n          i0.ɵɵelementStart(74, \"div\", 18)(75, \"h3\");\n          i0.ɵɵtext(76, \"Informations de paiement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(77, \"input\", 19);\n          i0.ɵɵelementStart(78, \"div\", 20)(79, \"mat-form-field\", 23)(80, \"mat-label\");\n          i0.ɵɵtext(81, \"Option de paiement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"mat-select\", 33)(83, \"mat-option\", 34);\n          i0.ɵɵtext(84, \"Carte de cr\\u00E9dit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"mat-option\", 34);\n          i0.ɵɵtext(86, \"Virement bancaire\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"mat-option\", 34);\n          i0.ɵɵtext(88, \"PayPal\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(89, \"div\", 35)(90, \"div\", 20)(91, \"mat-form-field\", 23)(92, \"mat-label\");\n          i0.ɵɵtext(93, \"Nom du titulaire\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(94, \"input\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"mat-form-field\", 23)(96, \"mat-label\");\n          i0.ɵɵtext(97, \"Type de paiement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"mat-select\", 37)(99, \"mat-option\", 34);\n          i0.ɵɵtext(100, \"Carte de cr\\u00E9dit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"mat-option\", 34);\n          i0.ɵɵtext(102, \"Virement bancaire\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"mat-option\", 34);\n          i0.ɵɵtext(104, \"PayPal\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(105, \"div\", 20)(106, \"div\", 38)(107, \"mat-form-field\", 23)(108, \"mat-label\");\n          i0.ɵɵtext(109, \"Montant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(110, \"input\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"mat-form-field\", 23)(112, \"mat-label\");\n          i0.ɵɵtext(113, \"Devise\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(114, \"mat-select\", 40)(115, \"mat-option\", 41);\n          i0.ɵɵtext(116, \"Euro (EUR)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"mat-option\", 42);\n          i0.ɵɵtext(118, \"Dollar am\\u00E9ricain (USD)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(119, \"mat-option\", 43);\n          i0.ɵɵtext(120, \"Livre sterling (GBP)\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(121, \"div\", 20)(122, \"mat-form-field\", 23)(123, \"mat-label\");\n          i0.ɵɵtext(124, \"Nombre de versements\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(125, \"input\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(126, \"mat-form-field\", 23)(127, \"mat-label\");\n          i0.ɵɵtext(128, \"Date de paiement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(129, \"input\", 45)(130, \"mat-datepicker-toggle\", 46)(131, \"mat-datepicker\", null, 47);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(133, \"div\", 20)(134, \"mat-form-field\", 23)(135, \"mat-label\");\n          i0.ɵɵtext(136, \"Type de re\\u00E7u\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(137, \"input\", 48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(138, \"mat-form-field\", 23)(139, \"mat-label\");\n          i0.ɵɵtext(140, \"R\\u00E9f\\u00E9rence\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(141, \"input\", 49);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(142, \"div\", 29)(143, \"button\", 30);\n          i0.ɵɵtemplate(144, BookingTransactionComponent_mat_spinner_144_Template, 1, 0, \"mat-spinner\", 31);\n          i0.ɵɵtemplate(145, BookingTransactionComponent_span_145_Template, 2, 0, \"span\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(146, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function BookingTransactionComponent_Template_button_click_146_listener() {\n            return ctx.previousStep();\n          });\n          i0.ɵɵtext(147, \"Retour\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(148, BookingTransactionComponent_div_148_Template, 16, 1, \"div\", 50);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r14 = i0.ɵɵreference(132);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"linear\", true)(\"selectedIndex\", ctx.currentStep - 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"completed\", ctx.beginResponse !== null)(\"editable\", !ctx.transactionId);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.offerIds.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.offerIds.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.beginResponse && ctx.beginResponse.body);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"completed\", ctx.infoResponse !== null);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.reservationInfoForm);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngForOf\", ctx.travellers.controls);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.reservationInfoForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.infoResponse && ctx.infoResponse.body);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"completed\", ctx.commitResponse !== null);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.commitTransactionForm);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 3);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 3);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"matDatepicker\", _r14);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r14);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.commitTransactionForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.commitResponse && ctx.commitResponse.body);\n        }\n      },\n      dependencies: [i8.NgForOf, i8.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i9.MatOption, i10.MatInput, i11.MatFormField, i11.MatLabel, i11.MatHint, i11.MatError, i11.MatSuffix, i12.MatSelect, i13.MatDatepicker, i13.MatDatepickerInput, i13.MatDatepickerToggle, i14.MatButton, i14.MatMiniFabButton, i15.MatIcon, i16.MatProgressSpinner, i17.MatAccordion, i17.MatExpansionPanel, i17.MatExpansionPanelHeader, i17.MatExpansionPanelTitle, i17.MatExpansionPanelDescription, i18.MatStep, i18.MatStepLabel, i18.MatStepper],\n      styles: [\".booking-transaction-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  background: linear-gradient(135deg, #1a73e8, #0d47a1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 40px;\\n  color: white;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  margin: 0 0 10px;\\n  font-weight: 600;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n\\n.header-illustration[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n\\n.header-illustration[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.step-content[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n}\\n\\n.step-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.step-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin: 0 0 10px;\\n  color: #1a73e8;\\n}\\n\\n.step-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0;\\n  color: #5f6368;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin: 0;\\n  color: #202124;\\n}\\n\\n.form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0 0 15px;\\n  color: #5f6368;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n\\n.form-row[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 16px;\\n  margin-top: 20px;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background-color: #fdeded;\\n  color: #d32f2f;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n}\\n\\n.error-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n}\\n\\n.button-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.traveller-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.response-summary[_ngcontent-%COMP%] {\\n  background-color: #e8f0fe;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-top: 30px;\\n}\\n\\n.response-summary[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin: 0 0 15px;\\n  color: #1a73e8;\\n}\\n\\n.transaction-details[_ngcontent-%COMP%], .reservation-details[_ngcontent-%COMP%], .confirmation-details[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n  gap: 16px;\\n}\\n\\n.transaction-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .reservation-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .confirmation-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 8px 0;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\\n}\\n\\n.success-response[_ngcontent-%COMP%] {\\n  background-color: #e6f4ea;\\n}\\n\\n.confirmation-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  margin: 20px 0;\\n  grid-column: 1 / -1;\\n}\\n\\n.confirmation-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #34a853;\\n  font-size: 24px;\\n}\\n\\n.confirmation-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  border: none;\\n}\\n\\n.confirmation-actions[_ngcontent-%COMP%] {\\n  grid-column: 1 / -1;\\n  margin-top: 20px;\\n  text-align: center;\\n}\\n\\n.offer-summary[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.offer-summary[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin: 0 0 15px;\\n  color: #202124;\\n}\\n\\n.offer-ids[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.offer-ids[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 10px;\\n  font-weight: 500;\\n}\\n\\n.offer-card-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  margin-top: 15px;\\n}\\n\\n.offer-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  padding: 16px;\\n  width: 100%;\\n  transition: transform 0.2s, box-shadow 0.2s;\\n  border-left: 4px solid #1a73e8;\\n}\\n\\n.offer-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n.offer-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  color: #1a73e8;\\n}\\n\\n.offer-card-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 20px;\\n}\\n\\n.offer-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 16px;\\n}\\n\\n.offer-card-content[_ngcontent-%COMP%] {\\n  color: #5f6368;\\n}\\n\\n.offer-info[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .page-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n\\n  .header-content[_ngcontent-%COMP%] {\\n    padding: 30px;\\n  }\\n\\n  .header-illustration[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n\\n  .form-row[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n    min-width: 100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "TravellerTitle", "Gender", "PassengerType", "i0", "ɵɵtext", "ɵɵtextInterpolate", "ctx_r1", "transactionId", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "ctx_r2", "errorMessage", "ɵɵelement", "ctx_r18", "formatOfferId", "offerId_r21", "ɵɵtemplate", "BookingTransactionComponent_div_21_div_8_Template", "ɵɵlistener", "BookingTransactionComponent_div_21_Template_form_ngSubmit_9_listener", "ɵɵrestoreView", "_r23", "ctx_r22", "ɵɵnextContext", "ɵɵresetView", "beginTransaction", "BookingTransactionComponent_div_21_mat_spinner_37_Template", "BookingTransactionComponent_div_21_span_38_Template", "ɵɵtextInterpolate1", "ctx_r4", "offerIds", "length", "ɵɵproperty", "beginTransactionForm", "isLoading", "ctx_r5", "formatDate", "beginResponse", "body", "expiresOn", "status", "ctx_r7", "ɵɵtextInterpolate2", "tmp_0_0", "traveller_r24", "get", "value", "type_r42", "label", "title_r43", "type_r44", "gender_r45", "BookingTransactionComponent_mat_expansion_panel_56_button_171_Template_button_click_0_listener", "_r48", "i_r25", "index", "ctx_r46", "removeTraveller", "BookingTransactionComponent_mat_expansion_panel_56_mat_panel_description_4_Template", "BookingTransactionComponent_mat_expansion_panel_56_mat_option_11_Template", "BookingTransactionComponent_mat_expansion_panel_56_mat_option_18_Template", "BookingTransactionComponent_mat_expansion_panel_56_mat_option_25_Template", "BookingTransactionComponent_mat_expansion_panel_56_mat_option_49_Template", "BookingTransactionComponent_mat_expansion_panel_56_mat_error_57_Template", "BookingTransactionComponent_mat_expansion_panel_56_mat_error_77_Template", "BookingTransactionComponent_mat_expansion_panel_56_mat_error_88_Template", "BookingTransactionComponent_mat_expansion_panel_56_mat_error_89_Template", "BookingTransactionComponent_mat_expansion_panel_56_mat_error_104_Template", "BookingTransactionComponent_mat_expansion_panel_56_mat_error_111_Template", "BookingTransactionComponent_mat_expansion_panel_56_button_171_Template", "tmp_2_0", "ctx_r8", "passengerTypes", "traveller<PERSON><PERSON><PERSON>", "_r30", "genderOptions", "tmp_10_0", "invalid", "tmp_11_0", "_r34", "tmp_14_0", "errors", "tmp_15_0", "_r37", "tmp_18_0", "tmp_19_0", "travellers", "ctx_r11", "infoResponse", "reservationData", "ctx_r13", "ctx_r17", "commitResponse", "reservationNumber", "BookingTransactionComponent", "constructor", "fb", "route", "router", "bookingService", "authService", "sharedDataService", "countryService", "snackBar", "currentStep", "searchId", "passengersParam", "passengerCounts", "Object", "keys", "filter", "key", "isNaN", "Number", "map", "countries", "group", "currency", "required", "culture", "reservationInfoForm", "array", "reservationNote", "agencyReservationNumber", "commitTransactionForm", "paymentOption", "paymentInformation", "accountName", "paymentTypeId", "paymentPrice", "amount", "installmentCount", "paymentDate", "receiptType", "reference", "paymentToken", "ngOnInit", "getCountries", "subscribe", "queryParams", "params", "Array", "isArray", "console", "log", "error", "JSON", "parse", "set<PERSON>assengerCounts", "getPassengerCounts", "patchValue", "header", "requestId", "success", "responseTime", "Date", "toISOString", "messages", "now", "transactionType", "reservationInfo", "bookingNumber", "agency", "code", "name", "country", "id", "address", "ownAgency", "aceExport", "agencyUser", "surname", "email", "beginDate", "endDate", "note", "salePrice", "supplementDiscount", "passengerEB", "agencyEB", "passengerAmountToPay", "agencyAmountToPay", "discount", "agencyBalance", "passengerBalance", "agencyCommission", "rate", "brokerCommission", "agencySupplementCommission", "promotionAmount", "priceToPay", "agencyPriceToPay", "passengerPriceToPay", "totalPrice", "services", "paymentDetail", "invoices", "createTravellersFromPassengerCounts", "open", "duration", "panelClass", "removeAt", "entries", "for<PERSON>ach", "typeStr", "count", "type", "parseInt", "i", "travellerForm", "title", "passengerType", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "birthDate", "nationality", "twoLetterCode", "max<PERSON><PERSON><PERSON>", "identityNumber", "passportInfo", "serial", "number", "expireDate", "issueDate", "citizenshipCountryCode", "issueCountryCode", "contactPhone", "countryCode", "areaCode", "phoneNumber", "zipCode", "city", "gender", "addCustomValidators", "push", "addTraveller", "form", "nationalityControl", "issueCountryCodeControl", "expireDateControl", "setValidators", "control", "today", "formValue", "next", "response", "traveller", "addTravellerFromResponse", "message", "travellerId", "undefined", "setReservationInfo", "controls", "touched", "passportNumberControl", "formattedTravellers", "formattedTraveller", "getTime", "formatDateForApi", "hasAdult", "some", "Adult", "request", "includes", "commitTransaction", "previousStep", "isAutoStarted", "onAction", "navigate", "offerId", "snapshot", "dateString", "date", "toLocaleDateString", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "formatPrice", "Intl", "NumberFormat", "style", "format", "onCountrySelected", "travellerIndex", "field", "at", "setValue", "parts", "split", "firstPart", "basicInfo", "shortId", "substring", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "BookingService", "i4", "AuthService", "i5", "SharedDataService", "i6", "CountryService", "i7", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "BookingTransactionComponent_Template", "rf", "ctx", "BookingTransactionComponent_ng_template_12_Template", "BookingTransactionComponent_div_19_Template", "BookingTransactionComponent_div_20_Template", "BookingTransactionComponent_div_21_Template", "BookingTransactionComponent_div_22_Template", "BookingTransactionComponent_ng_template_24_Template", "BookingTransactionComponent_div_31_Template", "BookingTransactionComponent_Template_form_ngSubmit_32_listener", "BookingTransactionComponent_Template_button_click_51_listener", "BookingTransactionComponent_mat_expansion_panel_56_Template", "BookingTransactionComponent_mat_spinner_59_Template", "BookingTransactionComponent_span_60_Template", "BookingTransactionComponent_Template_button_click_61_listener", "BookingTransactionComponent_div_63_Template", "BookingTransactionComponent_ng_template_65_Template", "BookingTransactionComponent_div_72_Template", "BookingTransactionComponent_Template_form_ngSubmit_73_listener", "BookingTransactionComponent_mat_spinner_144_Template", "BookingTransactionComponent_span_145_Template", "BookingTransactionComponent_Template_button_click_146_listener", "BookingTransactionComponent_div_148_Template", "_r14"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\booking\\booking-transaction\\booking-transaction.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\booking\\booking-transaction\\booking-transaction.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { BookingService } from '../../../services/booking.service';\nimport { AuthService } from '../../../services/auth.service';\nimport { SharedDataService } from '../../../services/shared-data.service';\nimport { CountryService, Country } from '../../../services/country.service';\nimport {\n  BookingTransactionResponse,\n  BeginTransactionResponse,\n  SetReservationInfoRequest,\n  SetReservationInfoResponse,\n  CommitTransactionResponse,\n  TravellerBeginResponse,\n  TravellerRequest,\n  PassportInfoRequest,\n  AddressRequest,\n  ContactPhoneRequest,\n  CityRequest,\n  CountryRequest,\n  NationalityRequest\n} from '../../../models/booking';\nimport { TravellerTitle, Gender, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-booking-transaction',\n  templateUrl: './booking-transaction.component.html',\n  styleUrls: ['./booking-transaction.component.css']\n})\nexport class BookingTransactionComponent implements OnInit {\n  // Étape actuelle du processus de réservation\n  currentStep = 1;\n\n  // Formulaires pour chaque étape\n  beginTransactionForm: FormGroup;\n  reservationInfoForm: FormGroup;\n  commitTransactionForm: FormGroup;\n\n  // Données de transaction\n  transactionId: string = '';\n  offerIds: string[] = [];\n  searchId: string = '';\n  passengersParam: string = '';\n\n  // Informations de passagers\n  passengerCounts: { [key: number]: number } = {};\n\n  // Réponses des différentes étapes\n  beginResponse: BeginTransactionResponse | null = null;\n  infoResponse: SetReservationInfoResponse | null = null;\n  commitResponse: CommitTransactionResponse | null = null;\n\n  // États de chargement et d'erreur\n  isLoading = false;\n  errorMessage = '';\n\n  // Options pour les formulaires\n  travellerTitles = Object.keys(TravellerTitle)\n    .filter(key => !isNaN(Number(TravellerTitle[key as keyof typeof TravellerTitle])))\n    .map(key => ({\n      value: TravellerTitle[key as keyof typeof TravellerTitle],\n      label: key\n    }));\n\n  genderOptions = Object.keys(Gender)\n    .filter(key => !isNaN(Number(Gender[key as keyof typeof Gender])))\n    .map(key => ({\n      value: Gender[key as keyof typeof Gender],\n      label: key\n    }));\n\n  passengerTypes = Object.keys(PassengerType)\n    .filter(key => !isNaN(Number(PassengerType[key as keyof typeof PassengerType])))\n    .map(key => ({\n      value: PassengerType[key as keyof typeof PassengerType],\n      label: key\n    }));\n\n  // Liste des pays pour le sélecteur\n  countries: Country[] = [];\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private bookingService: BookingService,\n    private authService: AuthService,\n    private sharedDataService: SharedDataService,\n    private countryService: CountryService,\n    private snackBar: MatSnackBar\n  ) {\n    // Initialisation des formulaires sans valeurs par défaut\n    this.beginTransactionForm = this.fb.group({\n      currency: ['', Validators.required],\n      culture: ['', Validators.required]\n    });\n\n    this.reservationInfoForm = this.fb.group({\n      transactionId: ['', Validators.required],\n      travellers: this.fb.array([]),\n      reservationNote: [''],\n      agencyReservationNumber: ['']\n    });\n\n    this.commitTransactionForm = this.fb.group({\n      transactionId: ['', Validators.required],\n      paymentOption: [null],\n      paymentInformation: this.fb.group({\n        accountName: [''],\n        paymentTypeId: [null],\n        paymentPrice: this.fb.group({\n          amount: [null, Validators.required],\n          currency: ['', Validators.required]\n        }),\n        installmentCount: [''],\n        paymentDate: [''],\n        receiptType: [''],\n        reference: [''],\n        paymentToken: ['']\n      })\n    });\n  }\n\n  ngOnInit(): void {\n    // Charger la liste des pays\n    this.countryService.getCountries().subscribe(countries => {\n      this.countries = countries;\n    });\n    // Récupérer les offerIds, transactionId et informations de passagers depuis les paramètres de l'URL\n    this.route.queryParams.subscribe(params => {\n      // Récupérer les offerIds\n      if (params['offerIds']) {\n        try {\n          this.offerIds = Array.isArray(params['offerIds'])\n            ? params['offerIds']\n            : [params['offerIds']];\n\n          if (this.offerIds.length > 0) {\n            console.log('OfferIds récupérés:', this.offerIds);\n          } else {\n            this.errorMessage = 'Aucun ID d\\'offre n\\'a été fourni.';\n          }\n        } catch (error) {\n          console.error('Erreur lors de la récupération des offerIds:', error);\n          this.errorMessage = 'Format d\\'ID d\\'offre invalide.';\n        }\n      } else {\n        this.errorMessage = 'Aucun ID d\\'offre n\\'a été fourni.';\n      }\n\n      // Récupérer le searchId s'il est présent\n      if (params['searchId']) {\n        this.searchId = params['searchId'];\n        console.log('SearchId récupéré:', this.searchId);\n      }\n\n      // Récupérer les informations de passagers\n      if (params['passengers']) {\n        this.passengersParam = params['passengers'];\n        try {\n          this.passengerCounts = JSON.parse(this.passengersParam);\n          // Mettre à jour le service partagé avec les informations de passagers\n          this.sharedDataService.setPassengerCounts(this.passengerCounts);\n          console.log('Passenger counts parsed successfully:', this.passengerCounts);\n        } catch (error) {\n          console.error('Error parsing passenger information:', error);\n          // Utiliser les valeurs par défaut du service\n          this.passengerCounts = this.sharedDataService.getPassengerCounts();\n        }\n      } else {\n        // Utiliser les valeurs par défaut du service\n        this.passengerCounts = this.sharedDataService.getPassengerCounts();\n      }\n\n      // Vérifier si une transaction a déjà été démarrée automatiquement\n      if (params['transactionId'] && params['autoStarted'] === 'true') {\n        console.log('Transaction déjà démarrée avec ID:', params['transactionId']);\n        this.transactionId = params['transactionId'];\n\n        // Mettre à jour le formulaire d'informations de réservation avec l'ID de transaction\n        this.reservationInfoForm.patchValue({\n          transactionId: this.transactionId\n        });\n\n        // Créer un objet de réponse simulé pour l'étape 1\n        this.beginResponse = {\n          header: {\n            requestId: '',\n            success: true,\n            responseTime: new Date().toISOString(),\n            messages: []\n          },\n          body: {\n            transactionId: this.transactionId,\n            expiresOn: new Date(Date.now() + 3600000).toISOString(), // Expire dans 1 heure\n            status: 1,\n            transactionType: 1,\n            reservationData: {\n              travellers: [],\n              reservationInfo: {\n                bookingNumber: '',\n                agency: {\n                  code: '',\n                  name: '',\n                  country: { id: '', name: '' },\n                  address: {},\n                  ownAgency: false,\n                  aceExport: false\n                },\n                agencyUser: {\n                  id: 0,\n                  name: '',\n                  surname: '',\n                  email: ''\n                },\n                beginDate: new Date().toISOString(),\n                endDate: new Date().toISOString(),\n                note: '',\n                salePrice: { amount: 0, currency: 'EUR' },\n                supplementDiscount: { amount: 0, currency: 'EUR' },\n                passengerEB: { amount: 0, currency: 'EUR' },\n                agencyEB: { amount: 0, currency: 'EUR' },\n                passengerAmountToPay: { amount: 0, currency: 'EUR' },\n                agencyAmountToPay: { amount: 0, currency: 'EUR' },\n                discount: { amount: 0, currency: 'EUR' },\n                agencyBalance: { amount: 0, currency: 'EUR' },\n                passengerBalance: { amount: 0, currency: 'EUR' },\n                agencyCommission: { amount: 0, currency: 'EUR', rate: 0 },\n                brokerCommission: { amount: 0, currency: 'EUR', rate: 0 },\n                agencySupplementCommission: { amount: 0, currency: 'EUR', rate: 0 },\n                promotionAmount: { amount: 0, currency: 'EUR' },\n                priceToPay: { amount: 0, currency: 'EUR' },\n                agencyPriceToPay: { amount: 0, currency: 'EUR' },\n                passengerPriceToPay: { amount: 0, currency: 'EUR' },\n                totalPrice: { amount: 0, currency: 'EUR' }\n              },\n              services: [],\n              paymentDetail: {},\n              invoices: []\n            }\n          }\n        };\n\n        // Créer dynamiquement les voyageurs en fonction des informations de passagers\n        this.createTravellersFromPassengerCounts();\n\n        // Passer directement à l'étape 2\n        this.currentStep = 2;\n\n        // Afficher un message d'information\n        this.snackBar.open('Transaction déjà démarrée. Veuillez compléter les informations des voyageurs.', 'Fermer', {\n          duration: 5000,\n          panelClass: ['info-snackbar']\n        });\n      }\n    });\n  }\n\n  // Créer dynamiquement les voyageurs en fonction des informations de passagers\n  createTravellersFromPassengerCounts(): void {\n    // Vider le tableau de voyageurs existant\n    while (this.travellers.length > 0) {\n      this.travellers.removeAt(0);\n    }\n\n    // Parcourir les types de passagers et créer le nombre correspondant de voyageurs\n    Object.entries(this.passengerCounts).forEach(([typeStr, count]) => {\n      const type = parseInt(typeStr);\n      for (let i = 0; i < count; i++) {\n        // Créer un voyageur avec le type de passager correspondant\n        const travellerForm = this.fb.group({\n          type: [type, Validators.required], // Type de voyageur (adulte, enfant, etc.)\n          title: [null, Validators.required], // Titre (M., Mme, etc.)\n          passengerType: [type, Validators.required], // Type de passager (même que le type de voyageur)\n          name: ['', [Validators.required, Validators.minLength(2)]],\n          surname: ['', [Validators.required, Validators.minLength(2)]],\n          isLeader: [this.travellers?.length === 0], // Premier voyageur est le leader par défaut\n          birthDate: ['', Validators.required],\n          nationality: this.fb.group({\n            twoLetterCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n          }),\n          identityNumber: [''],\n          passportInfo: this.fb.group({\n            serial: [''],\n            number: ['', [Validators.required, Validators.minLength(5)]],\n            expireDate: ['', Validators.required],\n            issueDate: ['', Validators.required],\n            citizenshipCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n            issueCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n          }),\n          address: this.fb.group({\n            contactPhone: this.fb.group({\n              countryCode: ['', Validators.required],\n              areaCode: [''],\n              phoneNumber: ['', [Validators.required, Validators.minLength(5)]]\n            }),\n            email: ['', [Validators.required, Validators.email]],\n            address: ['', [Validators.required, Validators.minLength(5)]],\n            zipCode: ['', [Validators.required, Validators.minLength(3)]],\n            city: this.fb.group({\n              id: ['', Validators.required],\n              name: ['', [Validators.required, Validators.minLength(2)]]\n            }),\n            country: this.fb.group({\n              id: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n              name: ['', [Validators.required, Validators.minLength(2)]]\n            })\n          }),\n          gender: [null, Validators.required]\n        });\n\n        // Ajouter des validateurs personnalisés\n        this.addCustomValidators(travellerForm);\n\n        // Ajouter le voyageur au formulaire\n        this.travellers.push(travellerForm);\n      }\n    });\n\n    // S'assurer qu'il y a au moins un voyageur\n    if (this.travellers.length === 0) {\n      this.addTraveller();\n    }\n  }\n\n  // Getter pour accéder au FormArray des voyageurs\n  get travellers(): FormArray {\n    return this.reservationInfoForm.get('travellers') as FormArray;\n  }\n\n  // Ajouter un nouveau voyageur au formulaire\n  addTraveller(): void {\n    const travellerForm = this.fb.group({\n      type: [null, Validators.required], // Type de voyageur\n      title: [null, Validators.required], // Titre\n      passengerType: [null, Validators.required], // Type de passager\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      surname: ['', [Validators.required, Validators.minLength(2)]],\n      isLeader: [this.travellers?.length === 0], // Premier voyageur est le leader par défaut\n      birthDate: ['', Validators.required],\n      nationality: this.fb.group({\n        twoLetterCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      identityNumber: [''],\n      passportInfo: this.fb.group({\n        serial: [''],\n        number: ['', [Validators.required, Validators.minLength(5)]],\n        expireDate: ['', Validators.required],\n        issueDate: ['', Validators.required],\n        citizenshipCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n        issueCountryCode: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      address: this.fb.group({\n        contactPhone: this.fb.group({\n          countryCode: ['', Validators.required],\n          areaCode: [''],\n          phoneNumber: ['', [Validators.required, Validators.minLength(5)]]\n        }),\n        email: ['', [Validators.required, Validators.email]],\n        address: ['', [Validators.required, Validators.minLength(5)]],\n        zipCode: ['', [Validators.required, Validators.minLength(3)]],\n        city: this.fb.group({\n          id: ['', Validators.required],\n          name: ['', [Validators.required, Validators.minLength(2)]]\n        }),\n        country: this.fb.group({\n          id: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n          name: ['', [Validators.required, Validators.minLength(2)]]\n        })\n      }),\n      gender: [null, Validators.required] // Genre\n    });\n\n    // Ajouter des validateurs personnalisés\n    this.addCustomValidators(travellerForm);\n\n    this.travellers.push(travellerForm);\n  }\n\n  // Ajouter des validateurs personnalisés au formulaire de voyageur\n  private addCustomValidators(form: FormGroup): void {\n    // Ne pas synchroniser automatiquement issueCountryCode avec la nationalité\n    // pour permettre une saisie entièrement dynamique\n    const nationalityControl = form.get('nationality.twoLetterCode');\n    const issueCountryCodeControl = form.get('passportInfo.issueCountryCode');\n\n    // Nous ne synchronisons plus automatiquement les valeurs\n    // L'utilisateur doit remplir tous les champs manuellement\n\n    // Vérifier que la date d'expiration du passeport est dans le futur\n    const expireDateControl = form.get('passportInfo.expireDate');\n    if (expireDateControl) {\n      expireDateControl.setValidators([\n        Validators.required,\n        (control) => {\n          const value = control.value;\n          if (!value) return null;\n\n          const expireDate = new Date(value);\n          const today = new Date();\n\n          return expireDate > today ? null : { 'expireDateInvalid': true };\n        }\n      ]);\n    }\n  }\n\n  // Supprimer un voyageur du formulaire\n  removeTraveller(index: number): void {\n    this.travellers.removeAt(index);\n  }\n\n  // Démarrer la transaction de réservation\n  beginTransaction(): void {\n    if (this.beginTransactionForm.invalid) {\n      this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {\n        duration: 3000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    const formValue = this.beginTransactionForm.value;\n\n    this.bookingService.beginTransaction(\n      this.offerIds,\n      formValue.currency,\n      formValue.culture\n    ).subscribe({\n      next: (response: BookingTransactionResponse) => {\n        this.isLoading = false;\n        console.log('Réponse reçue:', response);\n\n        if (response && response.beginResponse && response.beginResponse.body) {\n          this.beginResponse = response.beginResponse;\n          this.transactionId = response.beginResponse.body.transactionId || '';\n\n          // Mettre à jour le formulaire d'informations de réservation avec l'ID de transaction\n          this.reservationInfoForm.patchValue({\n            transactionId: this.transactionId\n          });\n\n          // Ajouter les voyageurs existants s'il y en a dans la réponse\n          if (response.beginResponse.body.reservationData &&\n              response.beginResponse.body.reservationData.travellers &&\n              response.beginResponse.body.reservationData.travellers.length > 0) {\n\n            // Vider le tableau de voyageurs existant\n            while (this.travellers.length > 0) {\n              this.travellers.removeAt(0);\n            }\n\n            // Ajouter les voyageurs de la réponse\n            response.beginResponse.body.reservationData.travellers.forEach(traveller => {\n              this.addTravellerFromResponse(traveller);\n            });\n          } else {\n            // Créer dynamiquement les voyageurs en fonction des informations de passagers\n            this.createTravellersFromPassengerCounts();\n          }\n\n          // Passer à l'étape suivante\n          this.currentStep = 2;\n\n          this.snackBar.open('Transaction démarrée avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = 'Réponse de transaction invalide.';\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        console.error('Erreur lors du démarrage de la transaction:', error);\n        this.errorMessage = error.message || 'Une erreur est survenue lors du démarrage de la transaction.';\n\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n\n  // Ajouter un voyageur à partir de la réponse de l'API\n  addTravellerFromResponse(traveller: TravellerBeginResponse): void {\n    // Utiliser uniquement les données fournies par l'API, sans valeurs par défaut\n    const travellerForm = this.fb.group({\n      travellerId: [traveller.travellerId || ''],\n      type: [traveller.type, Validators.required],\n      title: [traveller.title, Validators.required],\n      passengerType: [traveller.passengerType, Validators.required],\n      name: [traveller.name || '', [Validators.required, Validators.minLength(2)]],\n      surname: [traveller.surname || '', [Validators.required, Validators.minLength(2)]],\n      isLeader: [traveller.isLeader !== undefined ? traveller.isLeader : this.travellers?.length === 0],\n      birthDate: [traveller.birthDate || '', Validators.required],\n      nationality: this.fb.group({\n        twoLetterCode: [traveller.nationality?.twoLetterCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      identityNumber: [traveller.identityNumber || ''],\n      passportInfo: this.fb.group({\n        serial: [traveller.passportInfo?.serial || ''],\n        number: [traveller.passportInfo?.number || '', [Validators.required, Validators.minLength(5)]],\n        expireDate: [traveller.passportInfo?.expireDate || '', Validators.required],\n        issueDate: [traveller.passportInfo?.issueDate || '', Validators.required],\n        citizenshipCountryCode: [traveller.passportInfo?.citizenshipCountryCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n        issueCountryCode: [traveller.passportInfo?.issueCountryCode || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]]\n      }),\n      address: this.fb.group({\n        contactPhone: this.fb.group({\n          countryCode: [traveller.address?.contactPhone?.countryCode || '', Validators.required],\n          areaCode: [''], // areaCode n'existe pas dans le modèle de réponse\n          phoneNumber: [traveller.address?.contactPhone?.phoneNumber || '', [Validators.required, Validators.minLength(5)]]\n        }),\n        email: [traveller.address?.email || '', [Validators.required, Validators.email]],\n        address: [traveller.address?.address || '', [Validators.required, Validators.minLength(5)]],\n        zipCode: [traveller.address?.zipCode || '', [Validators.required, Validators.minLength(3)]],\n        city: this.fb.group({\n          id: [traveller.address?.city?.id || '', Validators.required],\n          name: [traveller.address?.city?.name || '', [Validators.required, Validators.minLength(2)]]\n        }),\n        country: this.fb.group({\n          id: [traveller.address?.country?.id || '', [Validators.required, Validators.minLength(2), Validators.maxLength(2)]],\n          name: [traveller.address?.country?.name || '', [Validators.required, Validators.minLength(2)]]\n        })\n      }),\n      gender: [traveller.gender, Validators.required]\n    });\n\n    // Ajouter des validateurs personnalisés\n    this.addCustomValidators(travellerForm);\n\n    this.travellers.push(travellerForm);\n  }\n\n  // Définir les informations de réservation\n  setReservationInfo(): void {\n    if (this.reservationInfoForm.invalid) {\n      // Vérifier les erreurs spécifiques pour donner des messages plus précis\n      let errorMessage = 'Veuillez remplir tous les champs obligatoires.';\n\n      // Vérifier les erreurs de passeport\n      const travellers = this.travellers.controls;\n      for (let i = 0; i < travellers.length; i++) {\n        const traveller = travellers[i] as FormGroup;\n\n        // Vérifier les erreurs de date d'expiration du passeport\n        const expireDateControl = traveller.get('passportInfo.expireDate');\n        if (expireDateControl?.errors?.['expireDateInvalid']) {\n          errorMessage = `Voyageur ${i+1}: La date d'expiration du passeport doit être dans le futur.`;\n          break;\n        }\n\n        // Vérifier les erreurs de code pays\n        const issueCountryCodeControl = traveller.get('passportInfo.issueCountryCode');\n        if (issueCountryCodeControl?.invalid && issueCountryCodeControl?.touched) {\n          errorMessage = `Voyageur ${i+1}: Le code pays d'émission du passeport est invalide.`;\n          break;\n        }\n\n        // Vérifier les erreurs de numéro de passeport\n        const passportNumberControl = traveller.get('passportInfo.number');\n        if (passportNumberControl?.invalid && passportNumberControl?.touched) {\n          errorMessage = `Voyageur ${i+1}: Le numéro de passeport est invalide.`;\n          break;\n        }\n      }\n\n      this.snackBar.open(errorMessage, 'Fermer', {\n        duration: 5000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    const formValue = this.reservationInfoForm.value;\n\n    // Formater correctement les dates pour chaque voyageur\n    const formattedTravellers = formValue.travellers.map((traveller: any) => {\n      // Créer un nouvel objet pour éviter de modifier l'original\n      const formattedTraveller = { ...traveller };\n\n      // Formater la date de naissance\n      if (formattedTraveller.birthDate) {\n        // Convertir en Date si c'est un objet Date ou une chaîne\n        const birthDate = new Date(formattedTraveller.birthDate);\n        if (!isNaN(birthDate.getTime())) {\n          formattedTraveller.birthDate = this.formatDateForApi(birthDate);\n        }\n      }\n\n      // Formater les dates de passeport si présentes\n      if (formattedTraveller.passportInfo) {\n        const passportInfo = { ...formattedTraveller.passportInfo };\n\n        if (passportInfo.expireDate) {\n          const expireDate = new Date(passportInfo.expireDate);\n          if (!isNaN(expireDate.getTime())) {\n            passportInfo.expireDate = this.formatDateForApi(expireDate);\n          }\n        }\n\n        if (passportInfo.issueDate) {\n          const issueDate = new Date(passportInfo.issueDate);\n          if (!isNaN(issueDate.getTime())) {\n            passportInfo.issueDate = this.formatDateForApi(issueDate);\n          }\n        }\n\n        formattedTraveller.passportInfo = passportInfo;\n      }\n\n      return formattedTraveller;\n    });\n\n    // Vérifier si tous les passagers sont des enfants ou des bébés\n    const hasAdult = formattedTravellers.some((traveller: TravellerRequest) =>\n      traveller.passengerType === PassengerType.Adult\n    );\n\n    // Créer la requête d'informations de réservation\n    // Ne pas inclure customerInfo si tous les passagers sont des enfants ou des bébés\n    const request: SetReservationInfoRequest = {\n      transactionId: formValue.transactionId,\n      travellers: formattedTravellers,\n      reservationNote: formValue.reservationNote,\n      agencyReservationNumber: formValue.agencyReservationNumber\n    };\n\n    // Afficher un message de log pour indiquer si customerInfo est inclus ou non\n    console.log('Requête setReservationInfo - Présence d\\'adultes:', hasAdult);\n    console.log('Requête setReservationInfo - customerInfo non inclus car tous les passagers sont des enfants ou des bébés');\n\n    this.bookingService.setReservationInfo(request).subscribe({\n      next: (response: BookingTransactionResponse) => {\n        this.isLoading = false;\n        console.log('Réponse setReservationInfo reçue:', response);\n\n        if (response && response.infoResponse && response.infoResponse.body) {\n          this.infoResponse = response.infoResponse;\n\n          // Mettre à jour le formulaire de finalisation de transaction avec l'ID de transaction\n          this.commitTransactionForm.patchValue({\n            transactionId: this.transactionId\n          });\n\n          // Mettre à jour le montant du paiement si disponible\n          if (response.infoResponse.body.reservationData &&\n              response.infoResponse.body.reservationData.reservationInfo &&\n              response.infoResponse.body.reservationData.reservationInfo.priceToPay) {\n\n            const priceToPay = response.infoResponse.body.reservationData.reservationInfo.priceToPay;\n\n            this.commitTransactionForm.get('paymentInformation.paymentPrice')?.patchValue({\n              amount: priceToPay.amount,\n              currency: priceToPay.currency\n            });\n          }\n\n          // Passer à l'étape suivante\n          this.currentStep = 3;\n\n          this.snackBar.open('Informations de réservation définies avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n        } else {\n          this.errorMessage = 'Réponse d\\'informations de réservation invalide.';\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        console.error('Erreur lors de la définition des informations de réservation:', error);\n\n        // Analyser le message d'erreur pour des problèmes spécifiques\n        let errorMessage = error.message || 'Une erreur est survenue lors de la définition des informations de réservation.';\n\n        // Vérifier les erreurs spécifiques liées au passeport\n        if (errorMessage.includes('passport') || errorMessage.includes('issueCountryCode')) {\n          errorMessage = 'Erreur de validation du passeport: Veuillez vérifier que toutes les informations de passeport sont complètes, notamment le code pays d\\'émission.';\n        }\n        // Vérifier les erreurs liées aux dates\n        else if (errorMessage.includes('date') || errorMessage.includes('expireDate')) {\n          errorMessage = 'Erreur de validation des dates: Veuillez vérifier que toutes les dates sont au format correct (YYYY-MM-DD).';\n        }\n        // Vérifier les erreurs liées aux informations personnelles\n        else if (errorMessage.includes('name') || errorMessage.includes('surname')) {\n          errorMessage = 'Erreur de validation des informations personnelles: Veuillez vérifier que tous les noms et prénoms sont correctement renseignés.';\n        }\n\n        this.errorMessage = errorMessage;\n\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 8000, // Durée plus longue pour les messages d'erreur détaillés\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n\n  // Finaliser la transaction de réservation\n  commitTransaction(): void {\n    if (this.commitTransactionForm.invalid) {\n      this.snackBar.open('Veuillez remplir tous les champs obligatoires.', 'Fermer', {\n        duration: 3000,\n        panelClass: ['error-snackbar']\n      });\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    const formValue = this.commitTransactionForm.value;\n\n    this.bookingService.commitTransaction(formValue).subscribe({\n      next: (response: BookingTransactionResponse) => {\n        this.isLoading = false;\n        console.log('Réponse commitTransaction reçue:', response);\n\n        if (response && response.commitResponse && response.commitResponse.body) {\n          this.commitResponse = response.commitResponse;\n\n          this.snackBar.open('Réservation finalisée avec succès!', 'Fermer', {\n            duration: 3000,\n            panelClass: ['success-snackbar']\n          });\n\n          // Afficher les détails de la réservation finalisée\n          // Vous pourriez rediriger vers une page de confirmation ici\n        } else {\n          this.errorMessage = 'Réponse de finalisation de transaction invalide.';\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        console.error('Erreur lors de la finalisation de la transaction:', error);\n        this.errorMessage = error.message || 'Une erreur est survenue lors de la finalisation de la transaction.';\n\n        this.snackBar.open(this.errorMessage, 'Fermer', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    });\n  }\n\n  // Revenir à l'étape précédente\n  previousStep(): void {\n    // Si la transaction a été démarrée automatiquement, ne pas permettre de revenir à l'étape 1\n    if (this.currentStep > 2 || (this.currentStep === 2 && !this.isAutoStarted())) {\n      this.currentStep--;\n    } else {\n      // Si on est à l'étape 2 et que la transaction a été démarrée automatiquement,\n      // proposer de retourner à la page de sélection de vol\n      this.snackBar.open('La transaction a déjà été démarrée. Voulez-vous annuler et retourner à la sélection de vol?', 'Retour', {\n        duration: 5000,\n        panelClass: ['warning-snackbar']\n      }).onAction().subscribe(() => {\n        this.router.navigate(['/get-offer'], {\n          queryParams: {\n            searchId: this.searchId,\n            offerId: this.offerIds[0]\n          }\n        });\n      });\n    }\n  }\n\n  // Vérifier si la transaction a été démarrée automatiquement\n  isAutoStarted(): boolean {\n    return this.route.snapshot.queryParams['autoStarted'] === 'true';\n  }\n\n  // Formater une date pour l'affichage\n  formatDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    const date = new Date(dateString);\n    return date.toLocaleDateString();\n  }\n\n  // Formater une date pour l'API (format ISO 8601: YYYY-MM-DD)\n  formatDateForApi(date: Date): string {\n    if (!date) return '';\n\n    // S'assurer que c'est une instance de Date valide\n    if (!(date instanceof Date) || isNaN(date.getTime())) {\n      console.error('Date invalide:', date);\n      return '';\n    }\n\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n\n    return `${year}-${month}-${day}`;\n  }\n\n  // Formater un prix pour l'affichage\n  formatPrice(amount: number, currency: string): string {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: currency\n    }).format(amount);\n  }\n\n  // Gérer la sélection d'un pays\n  onCountrySelected(country: Country, travellerIndex: number, field: string): void {\n    console.log(`Pays sélectionné pour le voyageur ${travellerIndex}, champ ${field}:`, country);\n\n    const traveller = this.travellers.at(travellerIndex) as FormGroup;\n\n    // Mettre à jour le champ correspondant\n    switch (field) {\n      case 'nationality':\n        traveller.get('nationality.twoLetterCode')?.setValue(country.code);\n        break;\n      case 'citizenshipCountryCode':\n        traveller.get('passportInfo.citizenshipCountryCode')?.setValue(country.code);\n        break;\n      case 'issueCountryCode':\n        traveller.get('passportInfo.issueCountryCode')?.setValue(country.code);\n        break;\n      case 'country':\n        traveller.get('address.country.id')?.setValue(country.code);\n        traveller.get('address.country.name')?.setValue(country.name);\n        break;\n    }\n  }\n\n  // Formater un ID d'offre pour l'affichage\n  formatOfferId(offerId: string): string {\n    if (!offerId) return 'N/A';\n\n    // Extraire les informations pertinentes de l'ID d'offre\n    // Format typique: 13$3$1~^006^~AAABloLbX6oAAAAClhW0YYMc26FHjKULIrlKaQAAAZaC2zyuAAAAALH6UPiglvPwEjLokBT6TDI=~^006^~1~^006^~154.66~^006^~~^006^~154.66~^006^~ODBiZTZiMGQtYWYxYy00MzYzLThmNjctODcyNTA0NjVjZjgz\n\n    // Extraire le début de l'ID (avant le premier ~)\n    const parts = offerId.split('~');\n    const firstPart = parts[0] || '';\n\n    // Extraire les informations de base (type de vol, classe, etc.)\n    const basicInfo = firstPart.split('$');\n\n    // Créer un identifiant court\n    const shortId = offerId.substring(0, 8) + '...' + offerId.substring(offerId.length - 8);\n\n    return `Vol #${basicInfo[0] || 'N/A'} - Référence: ${shortId}`;\n  }\n}\n", "<div class=\"booking-transaction-container\">\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <h1 class=\"page-title\">Réservation de vol</h1>\n      <p class=\"page-subtitle\">Complétez votre réservation en quelques étapes simples</p>\n    </div>\n    <div class=\"header-illustration\">\n      <img src=\"assets/images/booking-banner.jpg\" alt=\"Réservation de vol\">\n    </div>\n  </div>\n\n  <!-- Stepper pour les étapes de réservation -->\n  <mat-horizontal-stepper [linear]=\"true\" #stepper [selectedIndex]=\"currentStep - 1\">\n    <!-- Étape 1: Démarrer la transaction (masquée si déjà démarrée automatiquement) -->\n    <mat-step [completed]=\"beginResponse !== null\" [editable]=\"!transactionId\">\n      <ng-template matStepLabel>{{ transactionId ? 'Réservation démarrée' : 'Démarrer la réservation' }}</ng-template>\n\n      <div class=\"step-content\">\n        <div class=\"step-header\">\n          <h2>Démarrer votre réservation</h2>\n          <p>Nous allons commencer le processus de réservation pour les vols sélectionnés.</p>\n        </div>\n\n        <div *ngIf=\"errorMessage\" class=\"error-message\">\n          <mat-icon>error</mat-icon>\n          <span>{{ errorMessage }}</span>\n        </div>\n\n        <div *ngIf=\"offerIds.length === 0\" class=\"error-message\">\n          <mat-icon>error</mat-icon>\n          <span>Aucun vol n'a été sélectionné. Veuillez retourner à la page de recherche et sélectionner un vol.</span>\n          <button mat-raised-button color=\"primary\" routerLink=\"/search-price\">\n            Retour à la recherche\n          </button>\n        </div>\n\n        <div *ngIf=\"offerIds.length > 0\">\n          <div class=\"offer-summary\">\n            <h3>Résumé de votre sélection</h3>\n            <div class=\"offer-ids\">\n              <p>Offres sélectionnées: {{ offerIds.length }}</p>\n              <div class=\"offer-card-container\">\n                <div class=\"offer-card\" *ngFor=\"let offerId of offerIds\">\n                  <div class=\"offer-card-header\">\n                    <i class=\"fas fa-plane\"></i>\n                    <span class=\"offer-title\">{{ formatOfferId(offerId) }}</span>\n                  </div>\n                  <div class=\"offer-card-content\">\n                    <p class=\"offer-info\">Votre vol est prêt à être réservé</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <form [formGroup]=\"beginTransactionForm\" (ngSubmit)=\"beginTransaction()\">\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Devise</mat-label>\n                <mat-select formControlName=\"currency\" placeholder=\"Sélectionnez une devise\">\n                  <mat-option value=\"EUR\">Euro (EUR)</mat-option>\n                  <mat-option value=\"USD\">Dollar américain (USD)</mat-option>\n                  <mat-option value=\"GBP\">Livre sterling (GBP)</mat-option>\n                </mat-select>\n                <mat-hint>Devise utilisée pour la transaction</mat-hint>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Culture</mat-label>\n                <mat-select formControlName=\"culture\" placeholder=\"Sélectionnez une langue\">\n                  <mat-option value=\"fr-FR\">Français (FR)</mat-option>\n                  <mat-option value=\"en-US\">Anglais (US)</mat-option>\n                  <mat-option value=\"en-GB\">Anglais (GB)</mat-option>\n                </mat-select>\n                <mat-hint>Langue utilisée pour la transaction</mat-hint>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-actions\">\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"isLoading\">\n                <mat-spinner *ngIf=\"isLoading\" diameter=\"20\" class=\"button-spinner\"></mat-spinner>\n                <span *ngIf=\"!isLoading\">Démarrer la réservation</span>\n              </button>\n              <button mat-button type=\"button\" routerLink=\"/get-offer\">Annuler</button>\n            </div>\n          </form>\n        </div>\n\n        <!-- Affichage de la réponse de début de transaction -->\n        <div *ngIf=\"beginResponse && beginResponse.body\" class=\"response-summary\">\n          <h3>Détails de la transaction</h3>\n          <div class=\"transaction-details\">\n            <!-- ID de transaction masqué pour une interface plus professionnelle -->\n            <p><strong>Expire le:</strong> {{ formatDate(beginResponse.body.expiresOn) }}</p>\n            <p><strong>Statut:</strong> {{ beginResponse.body.status === 1 ? 'Active' : beginResponse.body.status || 'N/A' }}</p>\n          </div>\n        </div>\n      </div>\n    </mat-step>\n\n    <!-- Étape 2: Définir les informations de réservation -->\n    <mat-step [completed]=\"infoResponse !== null\">\n      <ng-template matStepLabel>Informations voyageurs</ng-template>\n\n      <div class=\"step-content\">\n        <div class=\"step-header\">\n          <h2>Informations des voyageurs</h2>\n          <p>Veuillez fournir les informations pour tous les voyageurs.</p>\n        </div>\n\n        <div *ngIf=\"errorMessage\" class=\"error-message\">\n          <mat-icon>error</mat-icon>\n          <span>{{ errorMessage }}</span>\n        </div>\n\n        <form [formGroup]=\"reservationInfoForm\" (ngSubmit)=\"setReservationInfo()\">\n          <div class=\"form-section\">\n            <h3>Informations générales</h3>\n            <!-- Champ transactionId masqué mais toujours présent dans le formulaire -->\n            <input type=\"hidden\" formControlName=\"transactionId\">\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Note de réservation</mat-label>\n                <textarea matInput formControlName=\"reservationNote\" rows=\"3\"></textarea>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Numéro de réservation d'agence</mat-label>\n                <input matInput formControlName=\"agencyReservationNumber\">\n              </mat-form-field>\n            </div>\n          </div>\n\n          <div class=\"form-section\">\n            <div class=\"section-header\">\n              <h3>Voyageurs</h3>\n              <button mat-mini-fab color=\"primary\" type=\"button\" (click)=\"addTraveller()\" aria-label=\"Ajouter un voyageur\">\n                <mat-icon>add</mat-icon>\n              </button>\n            </div>\n\n            <div formArrayName=\"travellers\">\n              <mat-accordion>\n                <mat-expansion-panel *ngFor=\"let traveller of travellers.controls; let i = index\" [expanded]=\"i === 0\">\n                  <mat-expansion-panel-header>\n                    <mat-panel-title>\n                      Voyageur {{ i + 1 }}\n                    </mat-panel-title>\n                    <mat-panel-description *ngIf=\"traveller.get('name')?.value || traveller.get('surname')?.value\">\n                      {{ traveller.get('name')?.value }} {{ traveller.get('surname')?.value }}\n                    </mat-panel-description>\n                  </mat-expansion-panel-header>\n\n                  <div [formGroupName]=\"i\">\n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Type de voyageur</mat-label>\n                        <mat-select formControlName=\"type\" placeholder=\"Sélectionnez un type\">\n                          <mat-option *ngFor=\"let type of passengerTypes\" [value]=\"type.value\">\n                            {{ type.label }}\n                          </mat-option>\n                        </mat-select>\n                        <mat-hint>Veuillez sélectionner un type de voyageur</mat-hint>\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Titre</mat-label>\n                        <mat-select formControlName=\"title\" placeholder=\"Sélectionnez un titre\">\n                          <mat-option *ngFor=\"let title of travellerTitles\" [value]=\"title.value\">\n                            {{ title.label }}\n                          </mat-option>\n                        </mat-select>\n                        <mat-hint>M., Mme, etc.</mat-hint>\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Type de passager</mat-label>\n                        <mat-select formControlName=\"passengerType\" placeholder=\"Sélectionnez un type\">\n                          <mat-option *ngFor=\"let type of passengerTypes\" [value]=\"type.value\">\n                            {{ type.label }}\n                          </mat-option>\n                        </mat-select>\n                        <mat-hint>Adulte, Enfant, etc.</mat-hint>\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Prénom</mat-label>\n                        <input matInput formControlName=\"name\" required>\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Nom</mat-label>\n                        <input matInput formControlName=\"surname\" required>\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Date de naissance</mat-label>\n                        <input matInput [matDatepicker]=\"birthDatePicker\" formControlName=\"birthDate\" required>\n                        <mat-datepicker-toggle matSuffix [for]=\"birthDatePicker\"></mat-datepicker-toggle>\n                        <mat-datepicker #birthDatePicker></mat-datepicker>\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Genre</mat-label>\n                        <mat-select formControlName=\"gender\">\n                          <mat-option *ngFor=\"let gender of genderOptions\" [value]=\"gender.value\">\n                            {{ gender.label }}\n                          </mat-option>\n                        </mat-select>\n                      </mat-form-field>\n\n                      <div formGroupName=\"nationality\">\n                        <mat-form-field appearance=\"outline\">\n                          <mat-label>Nationalité (code pays)</mat-label>\n                          <input matInput formControlName=\"twoLetterCode\" required placeholder=\"Entrez le code pays\">\n                          <mat-hint>Code pays à 2 lettres (ex: FR, US, GB)</mat-hint>\n                          <mat-error *ngIf=\"traveller.get('nationality.twoLetterCode')?.invalid\">\n                            Code pays invalide (2 lettres requis)\n                          </mat-error>\n                        </mat-form-field>\n                      </div>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Numéro d'identité</mat-label>\n                        <input matInput formControlName=\"identityNumber\">\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"form-section\">\n                      <h4>Informations de passeport</h4>\n                      <div formGroupName=\"passportInfo\">\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Numéro de série</mat-label>\n                            <input matInput formControlName=\"serial\">\n                          </mat-form-field>\n\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Numéro de passeport</mat-label>\n                            <input matInput formControlName=\"number\" required>\n                            <mat-hint>Minimum 5 caractères</mat-hint>\n                            <mat-error *ngIf=\"traveller.get('passportInfo.number')?.invalid\">\n                              Numéro de passeport invalide (minimum 5 caractères)\n                            </mat-error>\n                          </mat-form-field>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Date d'expiration</mat-label>\n                            <input matInput [matDatepicker]=\"expireDatePicker\" formControlName=\"expireDate\" required>\n                            <mat-datepicker-toggle matSuffix [for]=\"expireDatePicker\"></mat-datepicker-toggle>\n                            <mat-datepicker #expireDatePicker></mat-datepicker>\n                            <mat-hint>Doit être dans le futur</mat-hint>\n                            <mat-error *ngIf=\"traveller.get('passportInfo.expireDate')?.errors?.['expireDateInvalid']\">\n                              La date d'expiration doit être dans le futur\n                            </mat-error>\n                            <mat-error *ngIf=\"traveller.get('passportInfo.expireDate')?.errors?.['required']\">\n                              La date d'expiration est requise\n                            </mat-error>\n                          </mat-form-field>\n\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Date d'émission</mat-label>\n                            <input matInput [matDatepicker]=\"issueDatePicker\" formControlName=\"issueDate\" required>\n                            <mat-datepicker-toggle matSuffix [for]=\"issueDatePicker\"></mat-datepicker-toggle>\n                            <mat-datepicker #issueDatePicker></mat-datepicker>\n                          </mat-form-field>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Code pays de citoyenneté</mat-label>\n                            <input matInput formControlName=\"citizenshipCountryCode\" required placeholder=\"Entrez le code pays\">\n                            <mat-hint>Code pays à 2 lettres (ex: FR, US, GB)</mat-hint>\n                            <mat-error *ngIf=\"traveller.get('passportInfo.citizenshipCountryCode')?.invalid\">\n                              Code pays invalide (2 lettres requis)\n                            </mat-error>\n                          </mat-form-field>\n\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Code pays d'émission</mat-label>\n                            <input matInput formControlName=\"issueCountryCode\" required placeholder=\"Entrez le code pays\">\n                            <mat-hint>Code pays à 2 lettres (ex: FR, US, GB)</mat-hint>\n                            <mat-error *ngIf=\"traveller.get('passportInfo.issueCountryCode')?.invalid\">\n                              Code pays invalide (2 lettres requis)\n                            </mat-error>\n                          </mat-form-field>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"form-section\">\n                      <h4>Adresse</h4>\n                      <div formGroupName=\"address\">\n                        <div class=\"form-row\">\n                          <div formGroupName=\"contactPhone\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Code pays</mat-label>\n                              <input matInput formControlName=\"countryCode\" required>\n                            </mat-form-field>\n\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Indicatif régional</mat-label>\n                              <input matInput formControlName=\"areaCode\">\n                            </mat-form-field>\n\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Numéro de téléphone</mat-label>\n                              <input matInput formControlName=\"phoneNumber\" required>\n                            </mat-form-field>\n                          </div>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\" class=\"full-width\">\n                            <mat-label>Email</mat-label>\n                            <input matInput formControlName=\"email\" required type=\"email\">\n                          </mat-form-field>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\" class=\"full-width\">\n                            <mat-label>Adresse</mat-label>\n                            <input matInput formControlName=\"address\" required>\n                          </mat-form-field>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <mat-form-field appearance=\"outline\">\n                            <mat-label>Code postal</mat-label>\n                            <input matInput formControlName=\"zipCode\" required>\n                          </mat-form-field>\n\n                          <div formGroupName=\"city\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>ID de ville</mat-label>\n                              <input matInput formControlName=\"id\" required>\n                            </mat-form-field>\n\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Nom de ville</mat-label>\n                              <input matInput formControlName=\"name\" required>\n                            </mat-form-field>\n                          </div>\n                        </div>\n\n                        <div class=\"form-row\">\n                          <div formGroupName=\"country\">\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>ID de pays</mat-label>\n                              <input matInput formControlName=\"id\" required placeholder=\"Entrez le code pays\">\n                              <mat-hint>Code pays à 2 lettres (ex: FR, US, GB)</mat-hint>\n                            </mat-form-field>\n\n                            <mat-form-field appearance=\"outline\">\n                              <mat-label>Nom de pays</mat-label>\n                              <input matInput formControlName=\"name\" required placeholder=\"Entrez le nom du pays\">\n                              <mat-hint>Nom complet du pays (ex: France, États-Unis)</mat-hint>\n                            </mat-form-field>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"form-row traveller-actions\">\n                      <mat-checkbox formControlName=\"isLeader\">Chef de groupe</mat-checkbox>\n                      <button mat-button color=\"warn\" type=\"button\" (click)=\"removeTraveller(i)\" *ngIf=\"travellers.length > 1\">\n                        <mat-icon>delete</mat-icon> Supprimer ce voyageur\n                      </button>\n                    </div>\n                  </div>\n                </mat-expansion-panel>\n              </mat-accordion>\n            </div>\n          </div>\n\n          <div class=\"form-actions\">\n            <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"isLoading || reservationInfoForm.invalid\">\n              <mat-spinner *ngIf=\"isLoading\" diameter=\"20\" class=\"button-spinner\"></mat-spinner>\n              <span *ngIf=\"!isLoading\">Enregistrer les informations</span>\n            </button>\n            <button mat-button type=\"button\" (click)=\"previousStep()\">Retour</button>\n          </div>\n        </form>\n\n        <!-- Affichage de la réponse d'informations de réservation -->\n        <div *ngIf=\"infoResponse && infoResponse.body\" class=\"response-summary\">\n          <h3>Informations de réservation enregistrées</h3>\n          <div class=\"reservation-details\">\n            <p><strong>Nombre de voyageurs:</strong> {{ infoResponse.body.reservationData && infoResponse.body.reservationData.travellers ? infoResponse.body.reservationData.travellers.length : 0 }}</p>\n            <!-- ID de transaction masqué pour une interface plus professionnelle -->\n          </div>\n        </div>\n      </div>\n    </mat-step>\n\n    <!-- Étape 3: Finaliser la transaction -->\n    <mat-step [completed]=\"commitResponse !== null\">\n      <ng-template matStepLabel>Paiement et confirmation</ng-template>\n\n      <div class=\"step-content\">\n        <div class=\"step-header\">\n          <h2>Paiement et confirmation</h2>\n          <p>Finalisez votre réservation en effectuant le paiement.</p>\n        </div>\n\n        <div *ngIf=\"errorMessage\" class=\"error-message\">\n          <mat-icon>error</mat-icon>\n          <span>{{ errorMessage }}</span>\n        </div>\n\n        <form [formGroup]=\"commitTransactionForm\" (ngSubmit)=\"commitTransaction()\">\n          <div class=\"form-section\">\n            <h3>Informations de paiement</h3>\n            <!-- Champ transactionId masqué mais toujours présent dans le formulaire -->\n            <input type=\"hidden\" formControlName=\"transactionId\">\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Option de paiement</mat-label>\n                <mat-select formControlName=\"paymentOption\">\n                  <mat-option [value]=\"1\">Carte de crédit</mat-option>\n                  <mat-option [value]=\"2\">Virement bancaire</mat-option>\n                  <mat-option [value]=\"3\">PayPal</mat-option>\n                </mat-select>\n              </mat-form-field>\n            </div>\n\n            <div formGroupName=\"paymentInformation\">\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Nom du titulaire</mat-label>\n                  <input matInput formControlName=\"accountName\">\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Type de paiement</mat-label>\n                  <mat-select formControlName=\"paymentTypeId\">\n                    <mat-option [value]=\"1\">Carte de crédit</mat-option>\n                    <mat-option [value]=\"2\">Virement bancaire</mat-option>\n                    <mat-option [value]=\"3\">PayPal</mat-option>\n                  </mat-select>\n                </mat-form-field>\n              </div>\n\n              <div class=\"form-row\">\n                <div formGroupName=\"paymentPrice\">\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Montant</mat-label>\n                    <input matInput formControlName=\"amount\" type=\"number\" required>\n                  </mat-form-field>\n\n                  <mat-form-field appearance=\"outline\">\n                    <mat-label>Devise</mat-label>\n                    <mat-select formControlName=\"currency\">\n                      <mat-option value=\"EUR\">Euro (EUR)</mat-option>\n                      <mat-option value=\"USD\">Dollar américain (USD)</mat-option>\n                      <mat-option value=\"GBP\">Livre sterling (GBP)</mat-option>\n                    </mat-select>\n                  </mat-form-field>\n                </div>\n              </div>\n\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Nombre de versements</mat-label>\n                  <input matInput formControlName=\"installmentCount\">\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Date de paiement</mat-label>\n                  <input matInput [matDatepicker]=\"paymentDatePicker\" formControlName=\"paymentDate\">\n                  <mat-datepicker-toggle matSuffix [for]=\"paymentDatePicker\"></mat-datepicker-toggle>\n                  <mat-datepicker #paymentDatePicker></mat-datepicker>\n                </mat-form-field>\n              </div>\n\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Type de reçu</mat-label>\n                  <input matInput formControlName=\"receiptType\">\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Référence</mat-label>\n                  <input matInput formControlName=\"reference\">\n                </mat-form-field>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-actions\">\n            <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"isLoading || commitTransactionForm.invalid\">\n              <mat-spinner *ngIf=\"isLoading\" diameter=\"20\" class=\"button-spinner\"></mat-spinner>\n              <span *ngIf=\"!isLoading\">Finaliser la réservation</span>\n            </button>\n            <button mat-button type=\"button\" (click)=\"previousStep()\">Retour</button>\n          </div>\n        </form>\n\n        <!-- Affichage de la réponse de finalisation de transaction -->\n        <div *ngIf=\"commitResponse && commitResponse.body\" class=\"response-summary success-response\">\n          <h3>Réservation confirmée!</h3>\n          <div class=\"confirmation-details\">\n            <p><strong>Numéro de réservation:</strong> {{ commitResponse.body.reservationNumber || 'N/A' }}</p>\n            <!-- ID de transaction masqué pour une interface plus professionnelle -->\n\n            <div class=\"confirmation-message\">\n              <mat-icon>check_circle</mat-icon>\n              <p>Votre réservation a été confirmée avec succès. Vous recevrez bientôt un email de confirmation avec tous les détails de votre voyage.</p>\n            </div>\n\n            <div class=\"confirmation-actions\">\n              <button mat-raised-button color=\"primary\" routerLink=\"/search-price\">\n                Retour à la recherche\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </mat-step>\n  </mat-horizontal-stepper>\n</div>\n"], "mappings": "AACA,SAA4CA,UAAU,QAAQ,gBAAgB;AAsB9E,SAASC,cAAc,EAAEC,MAAM,EAAEC,aAAa,QAAQ,6BAA6B;;;;;;;;;;;;;;;;;;;;;;ICRnDC,EAAA,CAAAC,MAAA,GAAwE;;;;IAAxED,EAAA,CAAAE,iBAAA,CAAAC,MAAA,CAAAC,aAAA,+EAAwE;;;;;IAQhGJ,EAAA,CAAAK,cAAA,cAAgD;IACpCL,EAAA,CAAAC,MAAA,YAAK;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAC1BN,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAC,MAAA,GAAkB;IAAAD,EAAA,CAAAM,YAAA,EAAO;;;;IAAzBN,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAE,iBAAA,CAAAM,MAAA,CAAAC,YAAA,CAAkB;;;;;IAG1BT,EAAA,CAAAK,cAAA,cAAyD;IAC7CL,EAAA,CAAAC,MAAA,YAAK;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAC1BN,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAC,MAAA,qIAAgG;IAAAD,EAAA,CAAAM,YAAA,EAAO;IAC7GN,EAAA,CAAAK,cAAA,iBAAqE;IACnEL,EAAA,CAAAC,MAAA,mCACF;IAAAD,EAAA,CAAAM,YAAA,EAAS;;;;;IASHN,EAAA,CAAAK,cAAA,cAAyD;IAErDL,EAAA,CAAAU,SAAA,YAA4B;IAC5BV,EAAA,CAAAK,cAAA,eAA0B;IAAAL,EAAA,CAAAC,MAAA,GAA4B;IAAAD,EAAA,CAAAM,YAAA,EAAO;IAE/DN,EAAA,CAAAK,cAAA,cAAgC;IACRL,EAAA,CAAAC,MAAA,iEAAiC;IAAAD,EAAA,CAAAM,YAAA,EAAI;;;;;IAHjCN,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAE,iBAAA,CAAAS,OAAA,CAAAC,aAAA,CAAAC,WAAA,EAA4B;;;;;IAmC1Db,EAAA,CAAAU,SAAA,sBAAkF;;;;;IAClFV,EAAA,CAAAK,cAAA,WAAyB;IAAAL,EAAA,CAAAC,MAAA,wCAAuB;IAAAD,EAAA,CAAAM,YAAA,EAAO;;;;;;IA7C/DN,EAAA,CAAAK,cAAA,UAAiC;IAEzBL,EAAA,CAAAC,MAAA,+CAAyB;IAAAD,EAAA,CAAAM,YAAA,EAAK;IAClCN,EAAA,CAAAK,cAAA,cAAuB;IAClBL,EAAA,CAAAC,MAAA,GAA2C;IAAAD,EAAA,CAAAM,YAAA,EAAI;IAClDN,EAAA,CAAAK,cAAA,cAAkC;IAChCL,EAAA,CAAAc,UAAA,IAAAC,iDAAA,kBAQM;IACRf,EAAA,CAAAM,YAAA,EAAM;IAIVN,EAAA,CAAAK,cAAA,eAAyE;IAAhCL,EAAA,CAAAgB,UAAA,sBAAAC,qEAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAYrB,EAAA,CAAAsB,WAAA,CAAAF,OAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IACtEvB,EAAA,CAAAK,cAAA,eAAsB;IAEPL,EAAA,CAAAC,MAAA,cAAM;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC7BN,EAAA,CAAAK,cAAA,sBAA6E;IACnDL,EAAA,CAAAC,MAAA,kBAAU;IAAAD,EAAA,CAAAM,YAAA,EAAa;IAC/CN,EAAA,CAAAK,cAAA,sBAAwB;IAAAL,EAAA,CAAAC,MAAA,mCAAsB;IAAAD,EAAA,CAAAM,YAAA,EAAa;IAC3DN,EAAA,CAAAK,cAAA,sBAAwB;IAAAL,EAAA,CAAAC,MAAA,4BAAoB;IAAAD,EAAA,CAAAM,YAAA,EAAa;IAE3DN,EAAA,CAAAK,cAAA,gBAAU;IAAAL,EAAA,CAAAC,MAAA,gDAAmC;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAG1DN,EAAA,CAAAK,cAAA,0BAAqC;IACxBL,EAAA,CAAAC,MAAA,eAAO;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC9BN,EAAA,CAAAK,cAAA,sBAA4E;IAChDL,EAAA,CAAAC,MAAA,0BAAa;IAAAD,EAAA,CAAAM,YAAA,EAAa;IACpDN,EAAA,CAAAK,cAAA,sBAA0B;IAAAL,EAAA,CAAAC,MAAA,oBAAY;IAAAD,EAAA,CAAAM,YAAA,EAAa;IACnDN,EAAA,CAAAK,cAAA,sBAA0B;IAAAL,EAAA,CAAAC,MAAA,oBAAY;IAAAD,EAAA,CAAAM,YAAA,EAAa;IAErDN,EAAA,CAAAK,cAAA,gBAAU;IAAAL,EAAA,CAAAC,MAAA,gDAAmC;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAI5DN,EAAA,CAAAK,cAAA,eAA0B;IAEtBL,EAAA,CAAAc,UAAA,KAAAU,0DAAA,0BAAkF;IAClFxB,EAAA,CAAAc,UAAA,KAAAW,mDAAA,mBAAuD;IACzDzB,EAAA,CAAAM,YAAA,EAAS;IACTN,EAAA,CAAAK,cAAA,kBAAyD;IAAAL,EAAA,CAAAC,MAAA,eAAO;IAAAD,EAAA,CAAAM,YAAA,EAAS;;;;IA3CtEN,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAA0B,kBAAA,qCAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,KAA2C;IAEA7B,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAA8B,UAAA,YAAAH,MAAA,CAAAC,QAAA,CAAW;IAavD5B,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA8B,UAAA,cAAAH,MAAA,CAAAI,oBAAA,CAAkC;IAwBoB/B,EAAA,CAAAO,SAAA,IAAsB;IAAtBP,EAAA,CAAA8B,UAAA,aAAAH,MAAA,CAAAK,SAAA,CAAsB;IAC9DhC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAA8B,UAAA,SAAAH,MAAA,CAAAK,SAAA,CAAe;IACtBhC,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAA8B,UAAA,UAAAH,MAAA,CAAAK,SAAA,CAAgB;;;;;IAQ/BhC,EAAA,CAAAK,cAAA,cAA0E;IACpEL,EAAA,CAAAC,MAAA,qCAAyB;IAAAD,EAAA,CAAAM,YAAA,EAAK;IAClCN,EAAA,CAAAK,cAAA,cAAiC;IAEpBL,EAAA,CAAAC,MAAA,iBAAU;IAAAD,EAAA,CAAAM,YAAA,EAAS;IAACN,EAAA,CAAAC,MAAA,GAA8C;IAAAD,EAAA,CAAAM,YAAA,EAAI;IACjFN,EAAA,CAAAK,cAAA,QAAG;IAAQL,EAAA,CAAAC,MAAA,eAAO;IAAAD,EAAA,CAAAM,YAAA,EAAS;IAACN,EAAA,CAAAC,MAAA,IAAqF;IAAAD,EAAA,CAAAM,YAAA,EAAI;;;;IADtFN,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAA0B,kBAAA,MAAAO,MAAA,CAAAC,UAAA,CAAAD,MAAA,CAAAE,aAAA,CAAAC,IAAA,CAAAC,SAAA,MAA8C;IACjDrC,EAAA,CAAAO,SAAA,GAAqF;IAArFP,EAAA,CAAA0B,kBAAA,MAAAO,MAAA,CAAAE,aAAA,CAAAC,IAAA,CAAAE,MAAA,oBAAAL,MAAA,CAAAE,aAAA,CAAAC,IAAA,CAAAE,MAAA,cAAqF;;;;;IAQ7FtC,EAAA,CAAAC,MAAA,6BAAsB;;;;;IAQ9CD,EAAA,CAAAK,cAAA,cAAgD;IACpCL,EAAA,CAAAC,MAAA,YAAK;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAC1BN,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAC,MAAA,GAAkB;IAAAD,EAAA,CAAAM,YAAA,EAAO;;;;IAAzBN,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAE,iBAAA,CAAAqC,MAAA,CAAA9B,YAAA,CAAkB;;;;;IAuCdT,EAAA,CAAAK,cAAA,4BAA+F;IAC7FL,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAM,YAAA,EAAwB;;;;;IADtBN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAwC,kBAAA,OAAAC,OAAA,GAAAC,aAAA,CAAAC,GAAA,2BAAAF,OAAA,CAAAG,KAAA,QAAAH,OAAA,GAAAC,aAAA,CAAAC,GAAA,8BAAAF,OAAA,CAAAG,KAAA,MACF;;;;;IAQM5C,EAAA,CAAAK,cAAA,qBAAqE;IACnEL,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAM,YAAA,EAAa;;;;IAFmCN,EAAA,CAAA8B,UAAA,UAAAe,QAAA,CAAAD,KAAA,CAAoB;IAClE5C,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA0B,kBAAA,MAAAmB,QAAA,CAAAC,KAAA,MACF;;;;;IAQA9C,EAAA,CAAAK,cAAA,qBAAwE;IACtEL,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAM,YAAA,EAAa;;;;IAFqCN,EAAA,CAAA8B,UAAA,UAAAiB,SAAA,CAAAH,KAAA,CAAqB;IACrE5C,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA0B,kBAAA,MAAAqB,SAAA,CAAAD,KAAA,MACF;;;;;IAQA9C,EAAA,CAAAK,cAAA,qBAAqE;IACnEL,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAM,YAAA,EAAa;;;;IAFmCN,EAAA,CAAA8B,UAAA,UAAAkB,QAAA,CAAAJ,KAAA,CAAoB;IAClE5C,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA0B,kBAAA,MAAAsB,QAAA,CAAAF,KAAA,MACF;;;;;IA6BA9C,EAAA,CAAAK,cAAA,qBAAwE;IACtEL,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAM,YAAA,EAAa;;;;IAFoCN,EAAA,CAAA8B,UAAA,UAAAmB,UAAA,CAAAL,KAAA,CAAsB;IACrE5C,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA0B,kBAAA,MAAAuB,UAAA,CAAAH,KAAA,MACF;;;;;IASA9C,EAAA,CAAAK,cAAA,gBAAuE;IACrEL,EAAA,CAAAC,MAAA,8CACF;IAAAD,EAAA,CAAAM,YAAA,EAAY;;;;;IAuBVN,EAAA,CAAAK,cAAA,gBAAiE;IAC/DL,EAAA,CAAAC,MAAA,sEACF;IAAAD,EAAA,CAAAM,YAAA,EAAY;;;;;IAWZN,EAAA,CAAAK,cAAA,gBAA2F;IACzFL,EAAA,CAAAC,MAAA,0DACF;IAAAD,EAAA,CAAAM,YAAA,EAAY;;;;;IACZN,EAAA,CAAAK,cAAA,gBAAkF;IAChFL,EAAA,CAAAC,MAAA,yCACF;IAAAD,EAAA,CAAAM,YAAA,EAAY;;;;;IAgBZN,EAAA,CAAAK,cAAA,gBAAiF;IAC/EL,EAAA,CAAAC,MAAA,8CACF;IAAAD,EAAA,CAAAM,YAAA,EAAY;;;;;IAOZN,EAAA,CAAAK,cAAA,gBAA2E;IACzEL,EAAA,CAAAC,MAAA,8CACF;IAAAD,EAAA,CAAAM,YAAA,EAAY;;;;;;IAiFlBN,EAAA,CAAAK,cAAA,kBAAyG;IAA3DL,EAAA,CAAAgB,UAAA,mBAAAkC,+FAAA;MAAAlD,EAAA,CAAAkB,aAAA,CAAAiC,IAAA;MAAA,MAAAC,KAAA,GAAApD,EAAA,CAAAqB,aAAA,GAAAgC,KAAA;MAAA,MAAAC,OAAA,GAAAtD,EAAA,CAAAqB,aAAA;MAAA,OAASrB,EAAA,CAAAsB,WAAA,CAAAgC,OAAA,CAAAC,eAAA,CAAAH,KAAA,CAAkB;IAAA,EAAC;IACxEpD,EAAA,CAAAK,cAAA,eAAU;IAAAL,EAAA,CAAAC,MAAA,aAAM;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAACN,EAAA,CAAAC,MAAA,8BAC9B;IAAAD,EAAA,CAAAM,YAAA,EAAS;;;;;IAtOfN,EAAA,CAAAK,cAAA,8BAAuG;IAGjGL,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAM,YAAA,EAAkB;IAClBN,EAAA,CAAAc,UAAA,IAAA0C,mFAAA,oCAEwB;IAC1BxD,EAAA,CAAAM,YAAA,EAA6B;IAE7BN,EAAA,CAAAK,cAAA,cAAyB;IAGRL,EAAA,CAAAC,MAAA,uBAAgB;IAAAD,EAAA,CAAAM,YAAA,EAAY;IACvCN,EAAA,CAAAK,cAAA,sBAAsE;IACpEL,EAAA,CAAAc,UAAA,KAAA2C,yEAAA,yBAEa;IACfzD,EAAA,CAAAM,YAAA,EAAa;IACbN,EAAA,CAAAK,cAAA,gBAAU;IAAAL,EAAA,CAAAC,MAAA,sDAAyC;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAGhEN,EAAA,CAAAK,cAAA,0BAAqC;IACxBL,EAAA,CAAAC,MAAA,aAAK;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC5BN,EAAA,CAAAK,cAAA,sBAAwE;IACtEL,EAAA,CAAAc,UAAA,KAAA4C,yEAAA,yBAEa;IACf1D,EAAA,CAAAM,YAAA,EAAa;IACbN,EAAA,CAAAK,cAAA,gBAAU;IAAAL,EAAA,CAAAC,MAAA,qBAAa;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAGpCN,EAAA,CAAAK,cAAA,0BAAqC;IACxBL,EAAA,CAAAC,MAAA,wBAAgB;IAAAD,EAAA,CAAAM,YAAA,EAAY;IACvCN,EAAA,CAAAK,cAAA,sBAA+E;IAC7EL,EAAA,CAAAc,UAAA,KAAA6C,yEAAA,yBAEa;IACf3D,EAAA,CAAAM,YAAA,EAAa;IACbN,EAAA,CAAAK,cAAA,gBAAU;IAAAL,EAAA,CAAAC,MAAA,4BAAoB;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAI7CN,EAAA,CAAAK,cAAA,eAAsB;IAEPL,EAAA,CAAAC,MAAA,mBAAM;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC7BN,EAAA,CAAAU,SAAA,iBAAgD;IAClDV,EAAA,CAAAM,YAAA,EAAiB;IAEjBN,EAAA,CAAAK,cAAA,0BAAqC;IACxBL,EAAA,CAAAC,MAAA,WAAG;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC1BN,EAAA,CAAAU,SAAA,iBAAmD;IACrDV,EAAA,CAAAM,YAAA,EAAiB;IAEjBN,EAAA,CAAAK,cAAA,0BAAqC;IACxBL,EAAA,CAAAC,MAAA,yBAAiB;IAAAD,EAAA,CAAAM,YAAA,EAAY;IACxCN,EAAA,CAAAU,SAAA,iBAAuF;IAGzFV,EAAA,CAAAM,YAAA,EAAiB;IAGnBN,EAAA,CAAAK,cAAA,eAAsB;IAEPL,EAAA,CAAAC,MAAA,aAAK;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC5BN,EAAA,CAAAK,cAAA,sBAAqC;IACnCL,EAAA,CAAAc,UAAA,KAAA8C,yEAAA,yBAEa;IACf5D,EAAA,CAAAM,YAAA,EAAa;IAGfN,EAAA,CAAAK,cAAA,eAAiC;IAElBL,EAAA,CAAAC,MAAA,oCAAuB;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC9CN,EAAA,CAAAU,SAAA,iBAA2F;IAC3FV,EAAA,CAAAK,cAAA,gBAAU;IAAAL,EAAA,CAAAC,MAAA,mDAAsC;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAC3DN,EAAA,CAAAc,UAAA,KAAA+C,wEAAA,wBAEY;IACd7D,EAAA,CAAAM,YAAA,EAAiB;IAGnBN,EAAA,CAAAK,cAAA,0BAAqC;IACxBL,EAAA,CAAAC,MAAA,mCAAiB;IAAAD,EAAA,CAAAM,YAAA,EAAY;IACxCN,EAAA,CAAAU,SAAA,iBAAiD;IACnDV,EAAA,CAAAM,YAAA,EAAiB;IAGnBN,EAAA,CAAAK,cAAA,eAA0B;IACpBL,EAAA,CAAAC,MAAA,iCAAyB;IAAAD,EAAA,CAAAM,YAAA,EAAK;IAClCN,EAAA,CAAAK,cAAA,eAAkC;IAGjBL,EAAA,CAAAC,MAAA,iCAAe;IAAAD,EAAA,CAAAM,YAAA,EAAY;IACtCN,EAAA,CAAAU,SAAA,iBAAyC;IAC3CV,EAAA,CAAAM,YAAA,EAAiB;IAEjBN,EAAA,CAAAK,cAAA,0BAAqC;IACxBL,EAAA,CAAAC,MAAA,gCAAmB;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC1CN,EAAA,CAAAU,SAAA,iBAAkD;IAClDV,EAAA,CAAAK,cAAA,gBAAU;IAAAL,EAAA,CAAAC,MAAA,iCAAoB;IAAAD,EAAA,CAAAM,YAAA,EAAW;IACzCN,EAAA,CAAAc,UAAA,KAAAgD,wEAAA,wBAEY;IACd9D,EAAA,CAAAM,YAAA,EAAiB;IAGnBN,EAAA,CAAAK,cAAA,eAAsB;IAEPL,EAAA,CAAAC,MAAA,yBAAiB;IAAAD,EAAA,CAAAM,YAAA,EAAY;IACxCN,EAAA,CAAAU,SAAA,iBAAyF;IAGzFV,EAAA,CAAAK,cAAA,gBAAU;IAAAL,EAAA,CAAAC,MAAA,oCAAuB;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAC5CN,EAAA,CAAAc,UAAA,KAAAiD,wEAAA,wBAEY;IACZ/D,EAAA,CAAAc,UAAA,KAAAkD,wEAAA,wBAEY;IACdhE,EAAA,CAAAM,YAAA,EAAiB;IAEjBN,EAAA,CAAAK,cAAA,0BAAqC;IACxBL,EAAA,CAAAC,MAAA,4BAAe;IAAAD,EAAA,CAAAM,YAAA,EAAY;IACtCN,EAAA,CAAAU,SAAA,iBAAuF;IAGzFV,EAAA,CAAAM,YAAA,EAAiB;IAGnBN,EAAA,CAAAK,cAAA,eAAsB;IAEPL,EAAA,CAAAC,MAAA,sCAAwB;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC/CN,EAAA,CAAAU,SAAA,kBAAoG;IACpGV,EAAA,CAAAK,cAAA,iBAAU;IAAAL,EAAA,CAAAC,MAAA,oDAAsC;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAC3DN,EAAA,CAAAc,UAAA,MAAAmD,yEAAA,wBAEY;IACdjE,EAAA,CAAAM,YAAA,EAAiB;IAEjBN,EAAA,CAAAK,cAAA,2BAAqC;IACxBL,EAAA,CAAAC,MAAA,kCAAoB;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC3CN,EAAA,CAAAU,SAAA,kBAA8F;IAC9FV,EAAA,CAAAK,cAAA,iBAAU;IAAAL,EAAA,CAAAC,MAAA,oDAAsC;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAC3DN,EAAA,CAAAc,UAAA,MAAAoD,yEAAA,wBAEY;IACdlE,EAAA,CAAAM,YAAA,EAAiB;IAKvBN,EAAA,CAAAK,cAAA,gBAA0B;IACpBL,EAAA,CAAAC,MAAA,gBAAO;IAAAD,EAAA,CAAAM,YAAA,EAAK;IAChBN,EAAA,CAAAK,cAAA,gBAA6B;IAIVL,EAAA,CAAAC,MAAA,kBAAS;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAChCN,EAAA,CAAAU,SAAA,kBAAuD;IACzDV,EAAA,CAAAM,YAAA,EAAiB;IAEjBN,EAAA,CAAAK,cAAA,2BAAqC;IACxBL,EAAA,CAAAC,MAAA,gCAAkB;IAAAD,EAAA,CAAAM,YAAA,EAAY;IACzCN,EAAA,CAAAU,SAAA,kBAA2C;IAC7CV,EAAA,CAAAM,YAAA,EAAiB;IAEjBN,EAAA,CAAAK,cAAA,2BAAqC;IACxBL,EAAA,CAAAC,MAAA,2CAAmB;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC1CN,EAAA,CAAAU,SAAA,kBAAuD;IACzDV,EAAA,CAAAM,YAAA,EAAiB;IAIrBN,EAAA,CAAAK,cAAA,gBAAsB;IAEPL,EAAA,CAAAC,MAAA,cAAK;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC5BN,EAAA,CAAAU,SAAA,mBAA8D;IAChEV,EAAA,CAAAM,YAAA,EAAiB;IAGnBN,EAAA,CAAAK,cAAA,gBAAsB;IAEPL,EAAA,CAAAC,MAAA,gBAAO;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAC9BN,EAAA,CAAAU,SAAA,mBAAmD;IACrDV,EAAA,CAAAM,YAAA,EAAiB;IAGnBN,EAAA,CAAAK,cAAA,gBAAsB;IAEPL,EAAA,CAAAC,MAAA,oBAAW;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAClCN,EAAA,CAAAU,SAAA,mBAAmD;IACrDV,EAAA,CAAAM,YAAA,EAAiB;IAEjBN,EAAA,CAAAK,cAAA,iBAA0B;IAEXL,EAAA,CAAAC,MAAA,oBAAW;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAClCN,EAAA,CAAAU,SAAA,mBAA8C;IAChDV,EAAA,CAAAM,YAAA,EAAiB;IAEjBN,EAAA,CAAAK,cAAA,2BAAqC;IACxBL,EAAA,CAAAC,MAAA,qBAAY;IAAAD,EAAA,CAAAM,YAAA,EAAY;IACnCN,EAAA,CAAAU,SAAA,kBAAgD;IAClDV,EAAA,CAAAM,YAAA,EAAiB;IAIrBN,EAAA,CAAAK,cAAA,gBAAsB;IAGLL,EAAA,CAAAC,MAAA,mBAAU;IAAAD,EAAA,CAAAM,YAAA,EAAY;IACjCN,EAAA,CAAAU,SAAA,mBAAgF;IAChFV,EAAA,CAAAK,cAAA,iBAAU;IAAAL,EAAA,CAAAC,MAAA,oDAAsC;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAG7DN,EAAA,CAAAK,cAAA,2BAAqC;IACxBL,EAAA,CAAAC,MAAA,oBAAW;IAAAD,EAAA,CAAAM,YAAA,EAAY;IAClCN,EAAA,CAAAU,SAAA,mBAAoF;IACpFV,EAAA,CAAAK,cAAA,iBAAU;IAAAL,EAAA,CAAAC,MAAA,0DAA4C;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAO3EN,EAAA,CAAAK,cAAA,iBAAwC;IACGL,EAAA,CAAAC,MAAA,uBAAc;IAAAD,EAAA,CAAAM,YAAA,EAAe;IACtEN,EAAA,CAAAc,UAAA,MAAAqD,sEAAA,sBAES;IACXnE,EAAA,CAAAM,YAAA,EAAM;;;;;;;;;;;;;;;;IAvOwEN,EAAA,CAAA8B,UAAA,aAAAsB,KAAA,OAAoB;IAGhGpD,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA0B,kBAAA,eAAA0B,KAAA,UACF;IACwBpD,EAAA,CAAAO,SAAA,GAAqE;IAArEP,EAAA,CAAA8B,UAAA,WAAAsC,OAAA,GAAA1B,aAAA,CAAAC,GAAA,2BAAAyB,OAAA,CAAAxB,KAAA,OAAAwB,OAAA,GAAA1B,aAAA,CAAAC,GAAA,8BAAAyB,OAAA,CAAAxB,KAAA,EAAqE;IAK1F5C,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAA8B,UAAA,kBAAAsB,KAAA,CAAmB;IAKapD,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAA8B,UAAA,YAAAuC,MAAA,CAAAC,cAAA,CAAiB;IAUhBtE,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAA8B,UAAA,YAAAuC,MAAA,CAAAE,eAAA,CAAkB;IAUnBvE,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAA8B,UAAA,YAAAuC,MAAA,CAAAC,cAAA,CAAiB;IAqBhCtE,EAAA,CAAAO,SAAA,IAAiC;IAAjCP,EAAA,CAAA8B,UAAA,kBAAA0C,IAAA,CAAiC;IAChBxE,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAA8B,UAAA,QAAA0C,IAAA,CAAuB;IASvBxE,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAA8B,UAAA,YAAAuC,MAAA,CAAAI,aAAA,CAAgB;IAWnCzE,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAA8B,UAAA,UAAA4C,QAAA,GAAAhC,aAAA,CAAAC,GAAA,gDAAA+B,QAAA,CAAAC,OAAA,CAAyD;IAyBvD3E,EAAA,CAAAO,SAAA,IAAmD;IAAnDP,EAAA,CAAA8B,UAAA,UAAA8C,QAAA,GAAAlC,aAAA,CAAAC,GAAA,0CAAAiC,QAAA,CAAAD,OAAA,CAAmD;IAS/C3E,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA8B,UAAA,kBAAA+C,IAAA,CAAkC;IACjB7E,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAA8B,UAAA,QAAA+C,IAAA,CAAwB;IAG7C7E,EAAA,CAAAO,SAAA,GAA6E;IAA7EP,EAAA,CAAA8B,UAAA,UAAAgD,QAAA,GAAApC,aAAA,CAAAC,GAAA,8CAAAmC,QAAA,CAAAC,MAAA,kBAAAD,QAAA,CAAAC,MAAA,sBAA6E;IAG7E/E,EAAA,CAAAO,SAAA,GAAoE;IAApEP,EAAA,CAAA8B,UAAA,UAAAkD,QAAA,GAAAtC,aAAA,CAAAC,GAAA,8CAAAqC,QAAA,CAAAD,MAAA,kBAAAC,QAAA,CAAAD,MAAA,aAAoE;IAOhE/E,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAA8B,UAAA,kBAAAmD,IAAA,CAAiC;IAChBjF,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAA8B,UAAA,QAAAmD,IAAA,CAAuB;IAU5CjF,EAAA,CAAAO,SAAA,IAAmE;IAAnEP,EAAA,CAAA8B,UAAA,UAAAoD,QAAA,GAAAxC,aAAA,CAAAC,GAAA,0DAAAuC,QAAA,CAAAP,OAAA,CAAmE;IASnE3E,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAA8B,UAAA,UAAAqD,QAAA,GAAAzC,aAAA,CAAAC,GAAA,oDAAAwC,QAAA,CAAAR,OAAA,CAA6D;IAmFH3E,EAAA,CAAAO,SAAA,IAA2B;IAA3BP,EAAA,CAAA8B,UAAA,SAAAuC,MAAA,CAAAe,UAAA,CAAAvD,MAAA,KAA2B;;;;;IAY/G7B,EAAA,CAAAU,SAAA,sBAAkF;;;;;IAClFV,EAAA,CAAAK,cAAA,WAAyB;IAAAL,EAAA,CAAAC,MAAA,mCAA4B;IAAAD,EAAA,CAAAM,YAAA,EAAO;;;;;IAOlEN,EAAA,CAAAK,cAAA,cAAwE;IAClEL,EAAA,CAAAC,MAAA,yDAAwC;IAAAD,EAAA,CAAAM,YAAA,EAAK;IACjDN,EAAA,CAAAK,cAAA,eAAiC;IACpBL,EAAA,CAAAC,MAAA,2BAAoB;IAAAD,EAAA,CAAAM,YAAA,EAAS;IAACN,EAAA,CAAAC,MAAA,GAAiJ;IAAAD,EAAA,CAAAM,YAAA,EAAI;;;;IAArJN,EAAA,CAAAO,SAAA,GAAiJ;IAAjJP,EAAA,CAAA0B,kBAAA,MAAA2D,OAAA,CAAAC,YAAA,CAAAlD,IAAA,CAAAmD,eAAA,IAAAF,OAAA,CAAAC,YAAA,CAAAlD,IAAA,CAAAmD,eAAA,CAAAH,UAAA,GAAAC,OAAA,CAAAC,YAAA,CAAAlD,IAAA,CAAAmD,eAAA,CAAAH,UAAA,CAAAvD,MAAA,SAAiJ;;;;;IAStK7B,EAAA,CAAAC,MAAA,+BAAwB;;;;;IAQhDD,EAAA,CAAAK,cAAA,cAAgD;IACpCL,EAAA,CAAAC,MAAA,YAAK;IAAAD,EAAA,CAAAM,YAAA,EAAW;IAC1BN,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAC,MAAA,GAAkB;IAAAD,EAAA,CAAAM,YAAA,EAAO;;;;IAAzBN,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAE,iBAAA,CAAAsF,OAAA,CAAA/E,YAAA,CAAkB;;;;;IAqFpBT,EAAA,CAAAU,SAAA,sBAAkF;;;;;IAClFV,EAAA,CAAAK,cAAA,WAAyB;IAAAL,EAAA,CAAAC,MAAA,oCAAwB;IAAAD,EAAA,CAAAM,YAAA,EAAO;;;;;IAO9DN,EAAA,CAAAK,cAAA,eAA6F;IACvFL,EAAA,CAAAC,MAAA,uCAAsB;IAAAD,EAAA,CAAAM,YAAA,EAAK;IAC/BN,EAAA,CAAAK,cAAA,eAAkC;IACrBL,EAAA,CAAAC,MAAA,uCAAsB;IAAAD,EAAA,CAAAM,YAAA,EAAS;IAACN,EAAA,CAAAC,MAAA,GAAoD;IAAAD,EAAA,CAAAM,YAAA,EAAI;IAGnGN,EAAA,CAAAK,cAAA,eAAkC;IACtBL,EAAA,CAAAC,MAAA,oBAAY;IAAAD,EAAA,CAAAM,YAAA,EAAW;IACjCN,EAAA,CAAAK,cAAA,SAAG;IAAAL,EAAA,CAAAC,MAAA,+KAAoI;IAAAD,EAAA,CAAAM,YAAA,EAAI;IAG7IN,EAAA,CAAAK,cAAA,gBAAkC;IAE9BL,EAAA,CAAAC,MAAA,oCACF;IAAAD,EAAA,CAAAM,YAAA,EAAS;;;;IAXgCN,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAA0B,kBAAA,MAAA+D,OAAA,CAAAC,cAAA,CAAAtD,IAAA,CAAAuD,iBAAA,cAAoD;;;ADle3G,OAAM,MAAOC,2BAA2B;EAoDtCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,iBAAoC,EACpCC,cAA8B,EAC9BC,QAAqB;IAPrB,KAAAP,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,QAAQ,GAARA,QAAQ;IA3DlB;IACA,KAAAC,WAAW,GAAG,CAAC;IAOf;IACA,KAAAlG,aAAa,GAAW,EAAE;IAC1B,KAAAwB,QAAQ,GAAa,EAAE;IACvB,KAAA2E,QAAQ,GAAW,EAAE;IACrB,KAAAC,eAAe,GAAW,EAAE;IAE5B;IACA,KAAAC,eAAe,GAA8B,EAAE;IAE/C;IACA,KAAAtE,aAAa,GAAoC,IAAI;IACrD,KAAAmD,YAAY,GAAsC,IAAI;IACtD,KAAAI,cAAc,GAAqC,IAAI;IAEvD;IACA,KAAA1D,SAAS,GAAG,KAAK;IACjB,KAAAvB,YAAY,GAAG,EAAE;IAEjB;IACA,KAAA8D,eAAe,GAAGmC,MAAM,CAACC,IAAI,CAAC9G,cAAc,CAAC,CAC1C+G,MAAM,CAACC,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM,CAAClH,cAAc,CAACgH,GAAkC,CAAC,CAAC,CAAC,CAAC,CACjFG,GAAG,CAACH,GAAG,KAAK;MACXjE,KAAK,EAAE/C,cAAc,CAACgH,GAAkC,CAAC;MACzD/D,KAAK,EAAE+D;KACR,CAAC,CAAC;IAEL,KAAApC,aAAa,GAAGiC,MAAM,CAACC,IAAI,CAAC7G,MAAM,CAAC,CAChC8G,MAAM,CAACC,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM,CAACjH,MAAM,CAAC+G,GAA0B,CAAC,CAAC,CAAC,CAAC,CACjEG,GAAG,CAACH,GAAG,KAAK;MACXjE,KAAK,EAAE9C,MAAM,CAAC+G,GAA0B,CAAC;MACzC/D,KAAK,EAAE+D;KACR,CAAC,CAAC;IAEL,KAAAvC,cAAc,GAAGoC,MAAM,CAACC,IAAI,CAAC5G,aAAa,CAAC,CACxC6G,MAAM,CAACC,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM,CAAChH,aAAa,CAAC8G,GAAiC,CAAC,CAAC,CAAC,CAAC,CAC/EG,GAAG,CAACH,GAAG,KAAK;MACXjE,KAAK,EAAE7C,aAAa,CAAC8G,GAAiC,CAAC;MACvD/D,KAAK,EAAE+D;KACR,CAAC,CAAC;IAEL;IACA,KAAAI,SAAS,GAAc,EAAE;IAYvB;IACA,IAAI,CAAClF,oBAAoB,GAAG,IAAI,CAAC+D,EAAE,CAACoB,KAAK,CAAC;MACxCC,QAAQ,EAAE,CAAC,EAAE,EAAEvH,UAAU,CAACwH,QAAQ,CAAC;MACnCC,OAAO,EAAE,CAAC,EAAE,EAAEzH,UAAU,CAACwH,QAAQ;KAClC,CAAC;IAEF,IAAI,CAACE,mBAAmB,GAAG,IAAI,CAACxB,EAAE,CAACoB,KAAK,CAAC;MACvC9G,aAAa,EAAE,CAAC,EAAE,EAAER,UAAU,CAACwH,QAAQ,CAAC;MACxChC,UAAU,EAAE,IAAI,CAACU,EAAE,CAACyB,KAAK,CAAC,EAAE,CAAC;MAC7BC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,uBAAuB,EAAE,CAAC,EAAE;KAC7B,CAAC;IAEF,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAAC5B,EAAE,CAACoB,KAAK,CAAC;MACzC9G,aAAa,EAAE,CAAC,EAAE,EAAER,UAAU,CAACwH,QAAQ,CAAC;MACxCO,aAAa,EAAE,CAAC,IAAI,CAAC;MACrBC,kBAAkB,EAAE,IAAI,CAAC9B,EAAE,CAACoB,KAAK,CAAC;QAChCW,WAAW,EAAE,CAAC,EAAE,CAAC;QACjBC,aAAa,EAAE,CAAC,IAAI,CAAC;QACrBC,YAAY,EAAE,IAAI,CAACjC,EAAE,CAACoB,KAAK,CAAC;UAC1Bc,MAAM,EAAE,CAAC,IAAI,EAAEpI,UAAU,CAACwH,QAAQ,CAAC;UACnCD,QAAQ,EAAE,CAAC,EAAE,EAAEvH,UAAU,CAACwH,QAAQ;SACnC,CAAC;QACFa,gBAAgB,EAAE,CAAC,EAAE,CAAC;QACtBC,WAAW,EAAE,CAAC,EAAE,CAAC;QACjBC,WAAW,EAAE,CAAC,EAAE,CAAC;QACjBC,SAAS,EAAE,CAAC,EAAE,CAAC;QACfC,YAAY,EAAE,CAAC,EAAE;OAClB;KACF,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAAClC,cAAc,CAACmC,YAAY,EAAE,CAACC,SAAS,CAACvB,SAAS,IAAG;MACvD,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC5B,CAAC,CAAC;IACF;IACA,IAAI,CAAClB,KAAK,CAAC0C,WAAW,CAACD,SAAS,CAACE,MAAM,IAAG;MACxC;MACA,IAAIA,MAAM,CAAC,UAAU,CAAC,EAAE;QACtB,IAAI;UACF,IAAI,CAAC9G,QAAQ,GAAG+G,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,UAAU,CAAC,CAAC,GAC7CA,MAAM,CAAC,UAAU,CAAC,GAClB,CAACA,MAAM,CAAC,UAAU,CAAC,CAAC;UAExB,IAAI,IAAI,CAAC9G,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YAC5BgH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAClH,QAAQ,CAAC;WAClD,MAAM;YACL,IAAI,CAACnB,YAAY,GAAG,oCAAoC;;SAE3D,CAAC,OAAOsI,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;UACpE,IAAI,CAACtI,YAAY,GAAG,iCAAiC;;OAExD,MAAM;QACL,IAAI,CAACA,YAAY,GAAG,oCAAoC;;MAG1D;MACA,IAAIiI,MAAM,CAAC,UAAU,CAAC,EAAE;QACtB,IAAI,CAACnC,QAAQ,GAAGmC,MAAM,CAAC,UAAU,CAAC;QAClCG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACvC,QAAQ,CAAC;;MAGlD;MACA,IAAImC,MAAM,CAAC,YAAY,CAAC,EAAE;QACxB,IAAI,CAAClC,eAAe,GAAGkC,MAAM,CAAC,YAAY,CAAC;QAC3C,IAAI;UACF,IAAI,CAACjC,eAAe,GAAGuC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACzC,eAAe,CAAC;UACvD;UACA,IAAI,CAACL,iBAAiB,CAAC+C,kBAAkB,CAAC,IAAI,CAACzC,eAAe,CAAC;UAC/DoC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACrC,eAAe,CAAC;SAC3E,CAAC,OAAOsC,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC5D;UACA,IAAI,CAACtC,eAAe,GAAG,IAAI,CAACN,iBAAiB,CAACgD,kBAAkB,EAAE;;OAErE,MAAM;QACL;QACA,IAAI,CAAC1C,eAAe,GAAG,IAAI,CAACN,iBAAiB,CAACgD,kBAAkB,EAAE;;MAGpE;MACA,IAAIT,MAAM,CAAC,eAAe,CAAC,IAAIA,MAAM,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;QAC/DG,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEJ,MAAM,CAAC,eAAe,CAAC,CAAC;QAC1E,IAAI,CAACtI,aAAa,GAAGsI,MAAM,CAAC,eAAe,CAAC;QAE5C;QACA,IAAI,CAACpB,mBAAmB,CAAC8B,UAAU,CAAC;UAClChJ,aAAa,EAAE,IAAI,CAACA;SACrB,CAAC;QAEF;QACA,IAAI,CAAC+B,aAAa,GAAG;UACnBkH,MAAM,EAAE;YACNC,SAAS,EAAE,EAAE;YACbC,OAAO,EAAE,IAAI;YACbC,YAAY,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;YACtCC,QAAQ,EAAE;WACX;UACDvH,IAAI,EAAE;YACJhC,aAAa,EAAE,IAAI,CAACA,aAAa;YACjCiC,SAAS,EAAE,IAAIoH,IAAI,CAACA,IAAI,CAACG,GAAG,EAAE,GAAG,OAAO,CAAC,CAACF,WAAW,EAAE;YACvDpH,MAAM,EAAE,CAAC;YACTuH,eAAe,EAAE,CAAC;YAClBtE,eAAe,EAAE;cACfH,UAAU,EAAE,EAAE;cACd0E,eAAe,EAAE;gBACfC,aAAa,EAAE,EAAE;gBACjBC,MAAM,EAAE;kBACNC,IAAI,EAAE,EAAE;kBACRC,IAAI,EAAE,EAAE;kBACRC,OAAO,EAAE;oBAAEC,EAAE,EAAE,EAAE;oBAAEF,IAAI,EAAE;kBAAE,CAAE;kBAC7BG,OAAO,EAAE,EAAE;kBACXC,SAAS,EAAE,KAAK;kBAChBC,SAAS,EAAE;iBACZ;gBACDC,UAAU,EAAE;kBACVJ,EAAE,EAAE,CAAC;kBACLF,IAAI,EAAE,EAAE;kBACRO,OAAO,EAAE,EAAE;kBACXC,KAAK,EAAE;iBACR;gBACDC,SAAS,EAAE,IAAIlB,IAAI,EAAE,CAACC,WAAW,EAAE;gBACnCkB,OAAO,EAAE,IAAInB,IAAI,EAAE,CAACC,WAAW,EAAE;gBACjCmB,IAAI,EAAE,EAAE;gBACRC,SAAS,EAAE;kBAAE9C,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBACzC4D,kBAAkB,EAAE;kBAAE/C,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBAClD6D,WAAW,EAAE;kBAAEhD,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBAC3C8D,QAAQ,EAAE;kBAAEjD,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBACxC+D,oBAAoB,EAAE;kBAAElD,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBACpDgE,iBAAiB,EAAE;kBAAEnD,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBACjDiE,QAAQ,EAAE;kBAAEpD,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBACxCkE,aAAa,EAAE;kBAAErD,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBAC7CmE,gBAAgB,EAAE;kBAAEtD,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBAChDoE,gBAAgB,EAAE;kBAAEvD,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE,KAAK;kBAAEqE,IAAI,EAAE;gBAAC,CAAE;gBACzDC,gBAAgB,EAAE;kBAAEzD,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE,KAAK;kBAAEqE,IAAI,EAAE;gBAAC,CAAE;gBACzDE,0BAA0B,EAAE;kBAAE1D,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE,KAAK;kBAAEqE,IAAI,EAAE;gBAAC,CAAE;gBACnEG,eAAe,EAAE;kBAAE3D,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBAC/CyE,UAAU,EAAE;kBAAE5D,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBAC1C0E,gBAAgB,EAAE;kBAAE7D,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBAChD2E,mBAAmB,EAAE;kBAAE9D,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK,CAAE;gBACnD4E,UAAU,EAAE;kBAAE/D,MAAM,EAAE,CAAC;kBAAEb,QAAQ,EAAE;gBAAK;eACzC;cACD6E,QAAQ,EAAE,EAAE;cACZC,aAAa,EAAE,EAAE;cACjBC,QAAQ,EAAE;;;SAGf;QAED;QACA,IAAI,CAACC,mCAAmC,EAAE;QAE1C;QACA,IAAI,CAAC7F,WAAW,GAAG,CAAC;QAEpB;QACA,IAAI,CAACD,QAAQ,CAAC+F,IAAI,CAAC,+EAA+E,EAAE,QAAQ,EAAE;UAC5GC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,eAAe;SAC7B,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA;EACAH,mCAAmCA,CAAA;IACjC;IACA,OAAO,IAAI,CAAC/G,UAAU,CAACvD,MAAM,GAAG,CAAC,EAAE;MACjC,IAAI,CAACuD,UAAU,CAACmH,QAAQ,CAAC,CAAC,CAAC;;IAG7B;IACA7F,MAAM,CAAC8F,OAAO,CAAC,IAAI,CAAC/F,eAAe,CAAC,CAACgG,OAAO,CAAC,CAAC,CAACC,OAAO,EAAEC,KAAK,CAAC,KAAI;MAChE,MAAMC,IAAI,GAAGC,QAAQ,CAACH,OAAO,CAAC;MAC9B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,EAAEG,CAAC,EAAE,EAAE;QAC9B;QACA,MAAMC,aAAa,GAAG,IAAI,CAACjH,EAAE,CAACoB,KAAK,CAAC;UAClC0F,IAAI,EAAE,CAACA,IAAI,EAAEhN,UAAU,CAACwH,QAAQ,CAAC;UACjC4F,KAAK,EAAE,CAAC,IAAI,EAAEpN,UAAU,CAACwH,QAAQ,CAAC;UAClC6F,aAAa,EAAE,CAACL,IAAI,EAAEhN,UAAU,CAACwH,QAAQ,CAAC;UAC1C8C,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1DzC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7K,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7DC,QAAQ,EAAE,CAAC,IAAI,CAAC/H,UAAU,EAAEvD,MAAM,KAAK,CAAC,CAAC;UACzCuL,SAAS,EAAE,CAAC,EAAE,EAAExN,UAAU,CAACwH,QAAQ,CAAC;UACpCiG,WAAW,EAAE,IAAI,CAACvH,EAAE,CAACoB,KAAK,CAAC;YACzBoG,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC1N,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC;WAC5F,CAAC;UACFC,cAAc,EAAE,CAAC,EAAE,CAAC;UACpBC,YAAY,EAAE,IAAI,CAAC3H,EAAE,CAACoB,KAAK,CAAC;YAC1BwG,MAAM,EAAE,CAAC,EAAE,CAAC;YACZC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC/N,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5DU,UAAU,EAAE,CAAC,EAAE,EAAEhO,UAAU,CAACwH,QAAQ,CAAC;YACrCyG,SAAS,EAAE,CAAC,EAAE,EAAEjO,UAAU,CAACwH,QAAQ,CAAC;YACpC0G,sBAAsB,EAAE,CAAC,EAAE,EAAE,CAAClO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YACrGQ,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAACnO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC;WAC/F,CAAC;UACFlD,OAAO,EAAE,IAAI,CAACvE,EAAE,CAACoB,KAAK,CAAC;YACrB8G,YAAY,EAAE,IAAI,CAAClI,EAAE,CAACoB,KAAK,CAAC;cAC1B+G,WAAW,EAAE,CAAC,EAAE,EAAErO,UAAU,CAACwH,QAAQ,CAAC;cACtC8G,QAAQ,EAAE,CAAC,EAAE,CAAC;cACdC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACvO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC;aACjE,CAAC;YACFxC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC9K,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAAC8K,KAAK,CAAC,CAAC;YACpDL,OAAO,EAAE,CAAC,EAAE,EAAE,CAACzK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7DkB,OAAO,EAAE,CAAC,EAAE,EAAE,CAACxO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7DmB,IAAI,EAAE,IAAI,CAACvI,EAAE,CAACoB,KAAK,CAAC;cAClBkD,EAAE,EAAE,CAAC,EAAE,EAAExK,UAAU,CAACwH,QAAQ,CAAC;cAC7B8C,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC;aAC1D,CAAC;YACF/C,OAAO,EAAE,IAAI,CAACrE,EAAE,CAACoB,KAAK,CAAC;cACrBkD,EAAE,EAAE,CAAC,EAAE,EAAE,CAACxK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;cACjFrD,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC;aAC1D;WACF,CAAC;UACFoB,MAAM,EAAE,CAAC,IAAI,EAAE1O,UAAU,CAACwH,QAAQ;SACnC,CAAC;QAEF;QACA,IAAI,CAACmH,mBAAmB,CAACxB,aAAa,CAAC;QAEvC;QACA,IAAI,CAAC3H,UAAU,CAACoJ,IAAI,CAACzB,aAAa,CAAC;;IAEvC,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAAC3H,UAAU,CAACvD,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAAC4M,YAAY,EAAE;;EAEvB;EAEA;EACA,IAAIrJ,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACkC,mBAAmB,CAAC3E,GAAG,CAAC,YAAY,CAAc;EAChE;EAEA;EACA8L,YAAYA,CAAA;IACV,MAAM1B,aAAa,GAAG,IAAI,CAACjH,EAAE,CAACoB,KAAK,CAAC;MAClC0F,IAAI,EAAE,CAAC,IAAI,EAAEhN,UAAU,CAACwH,QAAQ,CAAC;MACjC4F,KAAK,EAAE,CAAC,IAAI,EAAEpN,UAAU,CAACwH,QAAQ,CAAC;MAClC6F,aAAa,EAAE,CAAC,IAAI,EAAErN,UAAU,CAACwH,QAAQ,CAAC;MAC1C8C,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DzC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7K,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7DC,QAAQ,EAAE,CAAC,IAAI,CAAC/H,UAAU,EAAEvD,MAAM,KAAK,CAAC,CAAC;MACzCuL,SAAS,EAAE,CAAC,EAAE,EAAExN,UAAU,CAACwH,QAAQ,CAAC;MACpCiG,WAAW,EAAE,IAAI,CAACvH,EAAE,CAACoB,KAAK,CAAC;QACzBoG,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC1N,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC;OAC5F,CAAC;MACFC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,YAAY,EAAE,IAAI,CAAC3H,EAAE,CAACoB,KAAK,CAAC;QAC1BwG,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC/N,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5DU,UAAU,EAAE,CAAC,EAAE,EAAEhO,UAAU,CAACwH,QAAQ,CAAC;QACrCyG,SAAS,EAAE,CAAC,EAAE,EAAEjO,UAAU,CAACwH,QAAQ,CAAC;QACpC0G,sBAAsB,EAAE,CAAC,EAAE,EAAE,CAAClO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACrGQ,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAACnO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC;OAC/F,CAAC;MACFlD,OAAO,EAAE,IAAI,CAACvE,EAAE,CAACoB,KAAK,CAAC;QACrB8G,YAAY,EAAE,IAAI,CAAClI,EAAE,CAACoB,KAAK,CAAC;UAC1B+G,WAAW,EAAE,CAAC,EAAE,EAAErO,UAAU,CAACwH,QAAQ,CAAC;UACtC8G,QAAQ,EAAE,CAAC,EAAE,CAAC;UACdC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACvO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC;SACjE,CAAC;QACFxC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC9K,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAAC8K,KAAK,CAAC,CAAC;QACpDL,OAAO,EAAE,CAAC,EAAE,EAAE,CAACzK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7DkB,OAAO,EAAE,CAAC,EAAE,EAAE,CAACxO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7DmB,IAAI,EAAE,IAAI,CAACvI,EAAE,CAACoB,KAAK,CAAC;UAClBkD,EAAE,EAAE,CAAC,EAAE,EAAExK,UAAU,CAACwH,QAAQ,CAAC;UAC7B8C,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC;SAC1D,CAAC;QACF/C,OAAO,EAAE,IAAI,CAACrE,EAAE,CAACoB,KAAK,CAAC;UACrBkD,EAAE,EAAE,CAAC,EAAE,EAAE,CAACxK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;UACjFrD,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC;SAC1D;OACF,CAAC;MACFoB,MAAM,EAAE,CAAC,IAAI,EAAE1O,UAAU,CAACwH,QAAQ,CAAC,CAAC;KACrC,CAAC;IAEF;IACA,IAAI,CAACmH,mBAAmB,CAACxB,aAAa,CAAC;IAEvC,IAAI,CAAC3H,UAAU,CAACoJ,IAAI,CAACzB,aAAa,CAAC;EACrC;EAEA;EACQwB,mBAAmBA,CAACG,IAAe;IACzC;IACA;IACA,MAAMC,kBAAkB,GAAGD,IAAI,CAAC/L,GAAG,CAAC,2BAA2B,CAAC;IAChE,MAAMiM,uBAAuB,GAAGF,IAAI,CAAC/L,GAAG,CAAC,+BAA+B,CAAC;IAEzE;IACA;IAEA;IACA,MAAMkM,iBAAiB,GAAGH,IAAI,CAAC/L,GAAG,CAAC,yBAAyB,CAAC;IAC7D,IAAIkM,iBAAiB,EAAE;MACrBA,iBAAiB,CAACC,aAAa,CAAC,CAC9BlP,UAAU,CAACwH,QAAQ,EAClB2H,OAAO,IAAI;QACV,MAAMnM,KAAK,GAAGmM,OAAO,CAACnM,KAAK;QAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;QAEvB,MAAMgL,UAAU,GAAG,IAAInE,IAAI,CAAC7G,KAAK,CAAC;QAClC,MAAMoM,KAAK,GAAG,IAAIvF,IAAI,EAAE;QAExB,OAAOmE,UAAU,GAAGoB,KAAK,GAAG,IAAI,GAAG;UAAE,mBAAmB,EAAE;QAAI,CAAE;MAClE,CAAC,CACF,CAAC;;EAEN;EAEA;EACAzL,eAAeA,CAACF,KAAa;IAC3B,IAAI,CAAC+B,UAAU,CAACmH,QAAQ,CAAClJ,KAAK,CAAC;EACjC;EAEA;EACA9B,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACQ,oBAAoB,CAAC4C,OAAO,EAAE;MACrC,IAAI,CAAC0B,QAAQ,CAAC+F,IAAI,CAAC,gDAAgD,EAAE,QAAQ,EAAE;QAC7EC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB;OAC9B,CAAC;MACF;;IAGF,IAAI,CAACtK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACvB,YAAY,GAAG,EAAE;IAEtB,MAAMwO,SAAS,GAAG,IAAI,CAAClN,oBAAoB,CAACa,KAAK;IAEjD,IAAI,CAACqD,cAAc,CAAC1E,gBAAgB,CAClC,IAAI,CAACK,QAAQ,EACbqN,SAAS,CAAC9H,QAAQ,EAClB8H,SAAS,CAAC5H,OAAO,CAClB,CAACmB,SAAS,CAAC;MACV0G,IAAI,EAAGC,QAAoC,IAAI;QAC7C,IAAI,CAACnN,SAAS,GAAG,KAAK;QACtB6G,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqG,QAAQ,CAAC;QAEvC,IAAIA,QAAQ,IAAIA,QAAQ,CAAChN,aAAa,IAAIgN,QAAQ,CAAChN,aAAa,CAACC,IAAI,EAAE;UACrE,IAAI,CAACD,aAAa,GAAGgN,QAAQ,CAAChN,aAAa;UAC3C,IAAI,CAAC/B,aAAa,GAAG+O,QAAQ,CAAChN,aAAa,CAACC,IAAI,CAAChC,aAAa,IAAI,EAAE;UAEpE;UACA,IAAI,CAACkH,mBAAmB,CAAC8B,UAAU,CAAC;YAClChJ,aAAa,EAAE,IAAI,CAACA;WACrB,CAAC;UAEF;UACA,IAAI+O,QAAQ,CAAChN,aAAa,CAACC,IAAI,CAACmD,eAAe,IAC3C4J,QAAQ,CAAChN,aAAa,CAACC,IAAI,CAACmD,eAAe,CAACH,UAAU,IACtD+J,QAAQ,CAAChN,aAAa,CAACC,IAAI,CAACmD,eAAe,CAACH,UAAU,CAACvD,MAAM,GAAG,CAAC,EAAE;YAErE;YACA,OAAO,IAAI,CAACuD,UAAU,CAACvD,MAAM,GAAG,CAAC,EAAE;cACjC,IAAI,CAACuD,UAAU,CAACmH,QAAQ,CAAC,CAAC,CAAC;;YAG7B;YACA4C,QAAQ,CAAChN,aAAa,CAACC,IAAI,CAACmD,eAAe,CAACH,UAAU,CAACqH,OAAO,CAAC2C,SAAS,IAAG;cACzE,IAAI,CAACC,wBAAwB,CAACD,SAAS,CAAC;YAC1C,CAAC,CAAC;WACH,MAAM;YACL;YACA,IAAI,CAACjD,mCAAmC,EAAE;;UAG5C;UACA,IAAI,CAAC7F,WAAW,GAAG,CAAC;UAEpB,IAAI,CAACD,QAAQ,CAAC+F,IAAI,CAAC,mCAAmC,EAAE,QAAQ,EAAE;YAChEC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;SACH,MAAM;UACL,IAAI,CAAC7L,YAAY,GAAG,kCAAkC;;MAE1D,CAAC;MACDsI,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/G,SAAS,GAAG,KAAK;QACtB6G,OAAO,CAACE,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QACnE,IAAI,CAACtI,YAAY,GAAGsI,KAAK,CAACuG,OAAO,IAAI,8DAA8D;QAEnG,IAAI,CAACjJ,QAAQ,CAAC+F,IAAI,CAAC,IAAI,CAAC3L,YAAY,EAAE,QAAQ,EAAE;UAC9C4L,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EACA+C,wBAAwBA,CAACD,SAAiC;IACxD;IACA,MAAMrC,aAAa,GAAG,IAAI,CAACjH,EAAE,CAACoB,KAAK,CAAC;MAClCqI,WAAW,EAAE,CAACH,SAAS,CAACG,WAAW,IAAI,EAAE,CAAC;MAC1C3C,IAAI,EAAE,CAACwC,SAAS,CAACxC,IAAI,EAAEhN,UAAU,CAACwH,QAAQ,CAAC;MAC3C4F,KAAK,EAAE,CAACoC,SAAS,CAACpC,KAAK,EAAEpN,UAAU,CAACwH,QAAQ,CAAC;MAC7C6F,aAAa,EAAE,CAACmC,SAAS,CAACnC,aAAa,EAAErN,UAAU,CAACwH,QAAQ,CAAC;MAC7D8C,IAAI,EAAE,CAACkF,SAAS,CAAClF,IAAI,IAAI,EAAE,EAAE,CAACtK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5EzC,OAAO,EAAE,CAAC2E,SAAS,CAAC3E,OAAO,IAAI,EAAE,EAAE,CAAC7K,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAClFC,QAAQ,EAAE,CAACiC,SAAS,CAACjC,QAAQ,KAAKqC,SAAS,GAAGJ,SAAS,CAACjC,QAAQ,GAAG,IAAI,CAAC/H,UAAU,EAAEvD,MAAM,KAAK,CAAC,CAAC;MACjGuL,SAAS,EAAE,CAACgC,SAAS,CAAChC,SAAS,IAAI,EAAE,EAAExN,UAAU,CAACwH,QAAQ,CAAC;MAC3DiG,WAAW,EAAE,IAAI,CAACvH,EAAE,CAACoB,KAAK,CAAC;QACzBoG,aAAa,EAAE,CAAC8B,SAAS,CAAC/B,WAAW,EAAEC,aAAa,IAAI,EAAE,EAAE,CAAC1N,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC;OACpI,CAAC;MACFC,cAAc,EAAE,CAAC4B,SAAS,CAAC5B,cAAc,IAAI,EAAE,CAAC;MAChDC,YAAY,EAAE,IAAI,CAAC3H,EAAE,CAACoB,KAAK,CAAC;QAC1BwG,MAAM,EAAE,CAAC0B,SAAS,CAAC3B,YAAY,EAAEC,MAAM,IAAI,EAAE,CAAC;QAC9CC,MAAM,EAAE,CAACyB,SAAS,CAAC3B,YAAY,EAAEE,MAAM,IAAI,EAAE,EAAE,CAAC/N,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9FU,UAAU,EAAE,CAACwB,SAAS,CAAC3B,YAAY,EAAEG,UAAU,IAAI,EAAE,EAAEhO,UAAU,CAACwH,QAAQ,CAAC;QAC3EyG,SAAS,EAAE,CAACuB,SAAS,CAAC3B,YAAY,EAAEI,SAAS,IAAI,EAAE,EAAEjO,UAAU,CAACwH,QAAQ,CAAC;QACzE0G,sBAAsB,EAAE,CAACsB,SAAS,CAAC3B,YAAY,EAAEK,sBAAsB,IAAI,EAAE,EAAE,CAAClO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACvJQ,gBAAgB,EAAE,CAACqB,SAAS,CAAC3B,YAAY,EAAEM,gBAAgB,IAAI,EAAE,EAAE,CAACnO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC;OAC3I,CAAC;MACFlD,OAAO,EAAE,IAAI,CAACvE,EAAE,CAACoB,KAAK,CAAC;QACrB8G,YAAY,EAAE,IAAI,CAAClI,EAAE,CAACoB,KAAK,CAAC;UAC1B+G,WAAW,EAAE,CAACmB,SAAS,CAAC/E,OAAO,EAAE2D,YAAY,EAAEC,WAAW,IAAI,EAAE,EAAErO,UAAU,CAACwH,QAAQ,CAAC;UACtF8G,QAAQ,EAAE,CAAC,EAAE,CAAC;UACdC,WAAW,EAAE,CAACiB,SAAS,CAAC/E,OAAO,EAAE2D,YAAY,EAAEG,WAAW,IAAI,EAAE,EAAE,CAACvO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC;SACjH,CAAC;QACFxC,KAAK,EAAE,CAAC0E,SAAS,CAAC/E,OAAO,EAAEK,KAAK,IAAI,EAAE,EAAE,CAAC9K,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAAC8K,KAAK,CAAC,CAAC;QAChFL,OAAO,EAAE,CAAC+E,SAAS,CAAC/E,OAAO,EAAEA,OAAO,IAAI,EAAE,EAAE,CAACzK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3FkB,OAAO,EAAE,CAACgB,SAAS,CAAC/E,OAAO,EAAE+D,OAAO,IAAI,EAAE,EAAE,CAACxO,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3FmB,IAAI,EAAE,IAAI,CAACvI,EAAE,CAACoB,KAAK,CAAC;UAClBkD,EAAE,EAAE,CAACgF,SAAS,CAAC/E,OAAO,EAAEgE,IAAI,EAAEjE,EAAE,IAAI,EAAE,EAAExK,UAAU,CAACwH,QAAQ,CAAC;UAC5D8C,IAAI,EAAE,CAACkF,SAAS,CAAC/E,OAAO,EAAEgE,IAAI,EAAEnE,IAAI,IAAI,EAAE,EAAE,CAACtK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC;SAC3F,CAAC;QACF/C,OAAO,EAAE,IAAI,CAACrE,EAAE,CAACoB,KAAK,CAAC;UACrBkD,EAAE,EAAE,CAACgF,SAAS,CAAC/E,OAAO,EAAEF,OAAO,EAAEC,EAAE,IAAI,EAAE,EAAE,CAACxK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,EAAEtN,UAAU,CAAC2N,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;UACnHrD,IAAI,EAAE,CAACkF,SAAS,CAAC/E,OAAO,EAAEF,OAAO,EAAED,IAAI,IAAI,EAAE,EAAE,CAACtK,UAAU,CAACwH,QAAQ,EAAExH,UAAU,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC;SAC9F;OACF,CAAC;MACFoB,MAAM,EAAE,CAACc,SAAS,CAACd,MAAM,EAAE1O,UAAU,CAACwH,QAAQ;KAC/C,CAAC;IAEF;IACA,IAAI,CAACmH,mBAAmB,CAACxB,aAAa,CAAC;IAEvC,IAAI,CAAC3H,UAAU,CAACoJ,IAAI,CAACzB,aAAa,CAAC;EACrC;EAEA;EACA0C,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACnI,mBAAmB,CAAC3C,OAAO,EAAE;MACpC;MACA,IAAIlE,YAAY,GAAG,gDAAgD;MAEnE;MACA,MAAM2E,UAAU,GAAG,IAAI,CAACA,UAAU,CAACsK,QAAQ;MAC3C,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1H,UAAU,CAACvD,MAAM,EAAEiL,CAAC,EAAE,EAAE;QAC1C,MAAMsC,SAAS,GAAGhK,UAAU,CAAC0H,CAAC,CAAc;QAE5C;QACA,MAAM+B,iBAAiB,GAAGO,SAAS,CAACzM,GAAG,CAAC,yBAAyB,CAAC;QAClE,IAAIkM,iBAAiB,EAAE9J,MAAM,GAAG,mBAAmB,CAAC,EAAE;UACpDtE,YAAY,GAAG,YAAYqM,CAAC,GAAC,CAAC,8DAA8D;UAC5F;;QAGF;QACA,MAAM8B,uBAAuB,GAAGQ,SAAS,CAACzM,GAAG,CAAC,+BAA+B,CAAC;QAC9E,IAAIiM,uBAAuB,EAAEjK,OAAO,IAAIiK,uBAAuB,EAAEe,OAAO,EAAE;UACxElP,YAAY,GAAG,YAAYqM,CAAC,GAAC,CAAC,sDAAsD;UACpF;;QAGF;QACA,MAAM8C,qBAAqB,GAAGR,SAAS,CAACzM,GAAG,CAAC,qBAAqB,CAAC;QAClE,IAAIiN,qBAAqB,EAAEjL,OAAO,IAAIiL,qBAAqB,EAAED,OAAO,EAAE;UACpElP,YAAY,GAAG,YAAYqM,CAAC,GAAC,CAAC,wCAAwC;UACtE;;;MAIJ,IAAI,CAACzG,QAAQ,CAAC+F,IAAI,CAAC3L,YAAY,EAAE,QAAQ,EAAE;QACzC4L,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB;OAC9B,CAAC;MACF;;IAGF,IAAI,CAACtK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACvB,YAAY,GAAG,EAAE;IAEtB,MAAMwO,SAAS,GAAG,IAAI,CAAC3H,mBAAmB,CAAC1E,KAAK;IAEhD;IACA,MAAMiN,mBAAmB,GAAGZ,SAAS,CAAC7J,UAAU,CAAC4B,GAAG,CAAEoI,SAAc,IAAI;MACtE;MACA,MAAMU,kBAAkB,GAAG;QAAE,GAAGV;MAAS,CAAE;MAE3C;MACA,IAAIU,kBAAkB,CAAC1C,SAAS,EAAE;QAChC;QACA,MAAMA,SAAS,GAAG,IAAI3D,IAAI,CAACqG,kBAAkB,CAAC1C,SAAS,CAAC;QACxD,IAAI,CAACtG,KAAK,CAACsG,SAAS,CAAC2C,OAAO,EAAE,CAAC,EAAE;UAC/BD,kBAAkB,CAAC1C,SAAS,GAAG,IAAI,CAAC4C,gBAAgB,CAAC5C,SAAS,CAAC;;;MAInE;MACA,IAAI0C,kBAAkB,CAACrC,YAAY,EAAE;QACnC,MAAMA,YAAY,GAAG;UAAE,GAAGqC,kBAAkB,CAACrC;QAAY,CAAE;QAE3D,IAAIA,YAAY,CAACG,UAAU,EAAE;UAC3B,MAAMA,UAAU,GAAG,IAAInE,IAAI,CAACgE,YAAY,CAACG,UAAU,CAAC;UACpD,IAAI,CAAC9G,KAAK,CAAC8G,UAAU,CAACmC,OAAO,EAAE,CAAC,EAAE;YAChCtC,YAAY,CAACG,UAAU,GAAG,IAAI,CAACoC,gBAAgB,CAACpC,UAAU,CAAC;;;QAI/D,IAAIH,YAAY,CAACI,SAAS,EAAE;UAC1B,MAAMA,SAAS,GAAG,IAAIpE,IAAI,CAACgE,YAAY,CAACI,SAAS,CAAC;UAClD,IAAI,CAAC/G,KAAK,CAAC+G,SAAS,CAACkC,OAAO,EAAE,CAAC,EAAE;YAC/BtC,YAAY,CAACI,SAAS,GAAG,IAAI,CAACmC,gBAAgB,CAACnC,SAAS,CAAC;;;QAI7DiC,kBAAkB,CAACrC,YAAY,GAAGA,YAAY;;MAGhD,OAAOqC,kBAAkB;IAC3B,CAAC,CAAC;IAEF;IACA,MAAMG,QAAQ,GAAGJ,mBAAmB,CAACK,IAAI,CAAEd,SAA2B,IACpEA,SAAS,CAACnC,aAAa,KAAKlN,aAAa,CAACoQ,KAAK,CAChD;IAED;IACA;IACA,MAAMC,OAAO,GAA8B;MACzChQ,aAAa,EAAE6O,SAAS,CAAC7O,aAAa;MACtCgF,UAAU,EAAEyK,mBAAmB;MAC/BrI,eAAe,EAAEyH,SAAS,CAACzH,eAAe;MAC1CC,uBAAuB,EAAEwH,SAAS,CAACxH;KACpC;IAED;IACAoB,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEmH,QAAQ,CAAC;IAC1EpH,OAAO,CAACC,GAAG,CAAC,2GAA2G,CAAC;IAExH,IAAI,CAAC7C,cAAc,CAACwJ,kBAAkB,CAACW,OAAO,CAAC,CAAC5H,SAAS,CAAC;MACxD0G,IAAI,EAAGC,QAAoC,IAAI;QAC7C,IAAI,CAACnN,SAAS,GAAG,KAAK;QACtB6G,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEqG,QAAQ,CAAC;QAE1D,IAAIA,QAAQ,IAAIA,QAAQ,CAAC7J,YAAY,IAAI6J,QAAQ,CAAC7J,YAAY,CAAClD,IAAI,EAAE;UACnE,IAAI,CAACkD,YAAY,GAAG6J,QAAQ,CAAC7J,YAAY;UAEzC;UACA,IAAI,CAACoC,qBAAqB,CAAC0B,UAAU,CAAC;YACpChJ,aAAa,EAAE,IAAI,CAACA;WACrB,CAAC;UAEF;UACA,IAAI+O,QAAQ,CAAC7J,YAAY,CAAClD,IAAI,CAACmD,eAAe,IAC1C4J,QAAQ,CAAC7J,YAAY,CAAClD,IAAI,CAACmD,eAAe,CAACuE,eAAe,IAC1DqF,QAAQ,CAAC7J,YAAY,CAAClD,IAAI,CAACmD,eAAe,CAACuE,eAAe,CAAC8B,UAAU,EAAE;YAEzE,MAAMA,UAAU,GAAGuD,QAAQ,CAAC7J,YAAY,CAAClD,IAAI,CAACmD,eAAe,CAACuE,eAAe,CAAC8B,UAAU;YAExF,IAAI,CAAClE,qBAAqB,CAAC/E,GAAG,CAAC,iCAAiC,CAAC,EAAEyG,UAAU,CAAC;cAC5EpB,MAAM,EAAE4D,UAAU,CAAC5D,MAAM;cACzBb,QAAQ,EAAEyE,UAAU,CAACzE;aACtB,CAAC;;UAGJ;UACA,IAAI,CAACb,WAAW,GAAG,CAAC;UAEpB,IAAI,CAACD,QAAQ,CAAC+F,IAAI,CAAC,mDAAmD,EAAE,QAAQ,EAAE;YAChFC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;SACH,MAAM;UACL,IAAI,CAAC7L,YAAY,GAAG,kDAAkD;;MAE1E,CAAC;MACDsI,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/G,SAAS,GAAG,KAAK;QACtB6G,OAAO,CAACE,KAAK,CAAC,+DAA+D,EAAEA,KAAK,CAAC;QAErF;QACA,IAAItI,YAAY,GAAGsI,KAAK,CAACuG,OAAO,IAAI,gFAAgF;QAEpH;QACA,IAAI7O,YAAY,CAAC4P,QAAQ,CAAC,UAAU,CAAC,IAAI5P,YAAY,CAAC4P,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UAClF5P,YAAY,GAAG,mJAAmJ;;QAEpK;QAAA,KACK,IAAIA,YAAY,CAAC4P,QAAQ,CAAC,MAAM,CAAC,IAAI5P,YAAY,CAAC4P,QAAQ,CAAC,YAAY,CAAC,EAAE;UAC7E5P,YAAY,GAAG,6GAA6G;;QAE9H;QAAA,KACK,IAAIA,YAAY,CAAC4P,QAAQ,CAAC,MAAM,CAAC,IAAI5P,YAAY,CAAC4P,QAAQ,CAAC,SAAS,CAAC,EAAE;UAC1E5P,YAAY,GAAG,kIAAkI;;QAGnJ,IAAI,CAACA,YAAY,GAAGA,YAAY;QAEhC,IAAI,CAAC4F,QAAQ,CAAC+F,IAAI,CAAC,IAAI,CAAC3L,YAAY,EAAE,QAAQ,EAAE;UAC9C4L,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EACAgE,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC5I,qBAAqB,CAAC/C,OAAO,EAAE;MACtC,IAAI,CAAC0B,QAAQ,CAAC+F,IAAI,CAAC,gDAAgD,EAAE,QAAQ,EAAE;QAC7EC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,gBAAgB;OAC9B,CAAC;MACF;;IAGF,IAAI,CAACtK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACvB,YAAY,GAAG,EAAE;IAEtB,MAAMwO,SAAS,GAAG,IAAI,CAACvH,qBAAqB,CAAC9E,KAAK;IAElD,IAAI,CAACqD,cAAc,CAACqK,iBAAiB,CAACrB,SAAS,CAAC,CAACzG,SAAS,CAAC;MACzD0G,IAAI,EAAGC,QAAoC,IAAI;QAC7C,IAAI,CAACnN,SAAS,GAAG,KAAK;QACtB6G,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEqG,QAAQ,CAAC;QAEzD,IAAIA,QAAQ,IAAIA,QAAQ,CAACzJ,cAAc,IAAIyJ,QAAQ,CAACzJ,cAAc,CAACtD,IAAI,EAAE;UACvE,IAAI,CAACsD,cAAc,GAAGyJ,QAAQ,CAACzJ,cAAc;UAE7C,IAAI,CAACW,QAAQ,CAAC+F,IAAI,CAAC,oCAAoC,EAAE,QAAQ,EAAE;YACjEC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,CAAC,kBAAkB;WAChC,CAAC;UAEF;UACA;SACD,MAAM;UACL,IAAI,CAAC7L,YAAY,GAAG,kDAAkD;;MAE1E,CAAC;MACDsI,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/G,SAAS,GAAG,KAAK;QACtB6G,OAAO,CAACE,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;QACzE,IAAI,CAACtI,YAAY,GAAGsI,KAAK,CAACuG,OAAO,IAAI,oEAAoE;QAEzG,IAAI,CAACjJ,QAAQ,CAAC+F,IAAI,CAAC,IAAI,CAAC3L,YAAY,EAAE,QAAQ,EAAE;UAC9C4L,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,CAAC,gBAAgB;SAC9B,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EACAiE,YAAYA,CAAA;IACV;IACA,IAAI,IAAI,CAACjK,WAAW,GAAG,CAAC,IAAK,IAAI,CAACA,WAAW,KAAK,CAAC,IAAI,CAAC,IAAI,CAACkK,aAAa,EAAG,EAAE;MAC7E,IAAI,CAAClK,WAAW,EAAE;KACnB,MAAM;MACL;MACA;MACA,IAAI,CAACD,QAAQ,CAAC+F,IAAI,CAAC,6FAA6F,EAAE,QAAQ,EAAE;QAC1HC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC,kBAAkB;OAChC,CAAC,CAACmE,QAAQ,EAAE,CAACjI,SAAS,CAAC,MAAK;QAC3B,IAAI,CAACxC,MAAM,CAAC0K,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;UACnCjI,WAAW,EAAE;YACXlC,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBoK,OAAO,EAAE,IAAI,CAAC/O,QAAQ,CAAC,CAAC;;SAE3B,CAAC;MACJ,CAAC,CAAC;;EAEN;EAEA;EACA4O,aAAaA,CAAA;IACX,OAAO,IAAI,CAACzK,KAAK,CAAC6K,QAAQ,CAACnI,WAAW,CAAC,aAAa,CAAC,KAAK,MAAM;EAClE;EAEA;EACAvG,UAAUA,CAAC2O,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B,MAAMC,IAAI,GAAG,IAAIrH,IAAI,CAACoH,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACC,kBAAkB,EAAE;EAClC;EAEA;EACAf,gBAAgBA,CAACc,IAAU;IACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB;IACA,IAAI,EAAEA,IAAI,YAAYrH,IAAI,CAAC,IAAI3C,KAAK,CAACgK,IAAI,CAACf,OAAO,EAAE,CAAC,EAAE;MACpDlH,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAE+H,IAAI,CAAC;MACrC,OAAO,EAAE;;IAGX,MAAME,IAAI,GAAGF,IAAI,CAACG,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAEnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;EAEA;EACAE,WAAWA,CAACxJ,MAAc,EAAEb,QAAgB;IAC1C,OAAO,IAAIsK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBxK,QAAQ,EAAEA;KACX,CAAC,CAACyK,MAAM,CAAC5J,MAAM,CAAC;EACnB;EAEA;EACA6J,iBAAiBA,CAAC1H,OAAgB,EAAE2H,cAAsB,EAAEC,KAAa;IACvElJ,OAAO,CAACC,GAAG,CAAC,qCAAqCgJ,cAAc,WAAWC,KAAK,GAAG,EAAE5H,OAAO,CAAC;IAE5F,MAAMiF,SAAS,GAAG,IAAI,CAAChK,UAAU,CAAC4M,EAAE,CAACF,cAAc,CAAc;IAEjE;IACA,QAAQC,KAAK;MACX,KAAK,aAAa;QAChB3C,SAAS,CAACzM,GAAG,CAAC,2BAA2B,CAAC,EAAEsP,QAAQ,CAAC9H,OAAO,CAACF,IAAI,CAAC;QAClE;MACF,KAAK,wBAAwB;QAC3BmF,SAAS,CAACzM,GAAG,CAAC,qCAAqC,CAAC,EAAEsP,QAAQ,CAAC9H,OAAO,CAACF,IAAI,CAAC;QAC5E;MACF,KAAK,kBAAkB;QACrBmF,SAAS,CAACzM,GAAG,CAAC,+BAA+B,CAAC,EAAEsP,QAAQ,CAAC9H,OAAO,CAACF,IAAI,CAAC;QACtE;MACF,KAAK,SAAS;QACZmF,SAAS,CAACzM,GAAG,CAAC,oBAAoB,CAAC,EAAEsP,QAAQ,CAAC9H,OAAO,CAACF,IAAI,CAAC;QAC3DmF,SAAS,CAACzM,GAAG,CAAC,sBAAsB,CAAC,EAAEsP,QAAQ,CAAC9H,OAAO,CAACD,IAAI,CAAC;QAC7D;;EAEN;EAEA;EACAtJ,aAAaA,CAAC+P,OAAe;IAC3B,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B;IACA;IAEA;IACA,MAAMuB,KAAK,GAAGvB,OAAO,CAACwB,KAAK,CAAC,GAAG,CAAC;IAChC,MAAMC,SAAS,GAAGF,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;IAEhC;IACA,MAAMG,SAAS,GAAGD,SAAS,CAACD,KAAK,CAAC,GAAG,CAAC;IAEtC;IACA,MAAMG,OAAO,GAAG3B,OAAO,CAAC4B,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG5B,OAAO,CAAC4B,SAAS,CAAC5B,OAAO,CAAC9O,MAAM,GAAG,CAAC,CAAC;IAEvF,OAAO,QAAQwQ,SAAS,CAAC,CAAC,CAAC,IAAI,KAAK,iBAAiBC,OAAO,EAAE;EAChE;;;uBA3zBW1M,2BAA2B,EAAA5F,EAAA,CAAAwS,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1S,EAAA,CAAAwS,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5S,EAAA,CAAAwS,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA7S,EAAA,CAAAwS,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA/S,EAAA,CAAAwS,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAAjT,EAAA,CAAAwS,iBAAA,CAAAU,EAAA,CAAAC,iBAAA,GAAAnT,EAAA,CAAAwS,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAArT,EAAA,CAAAwS,iBAAA,CAAAc,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA3B3N,2BAA2B;MAAA4N,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9BxC9T,EAAA,CAAAK,cAAA,aAA2C;UAGdL,EAAA,CAAAC,MAAA,8BAAkB;UAAAD,EAAA,CAAAM,YAAA,EAAK;UAC9CN,EAAA,CAAAK,cAAA,WAAyB;UAAAL,EAAA,CAAAC,MAAA,4EAAsD;UAAAD,EAAA,CAAAM,YAAA,EAAI;UAErFN,EAAA,CAAAK,cAAA,aAAiC;UAC/BL,EAAA,CAAAU,SAAA,aAAqE;UACvEV,EAAA,CAAAM,YAAA,EAAM;UAIRN,EAAA,CAAAK,cAAA,mCAAmF;UAG/EL,EAAA,CAAAc,UAAA,KAAAkT,mDAAA,0BAAgH;UAEhHhU,EAAA,CAAAK,cAAA,eAA0B;UAElBL,EAAA,CAAAC,MAAA,4CAA0B;UAAAD,EAAA,CAAAM,YAAA,EAAK;UACnCN,EAAA,CAAAK,cAAA,SAAG;UAAAL,EAAA,CAAAC,MAAA,oGAA6E;UAAAD,EAAA,CAAAM,YAAA,EAAI;UAGtFN,EAAA,CAAAc,UAAA,KAAAmT,2CAAA,kBAGM;UAENjU,EAAA,CAAAc,UAAA,KAAAoT,2CAAA,kBAMM;UAENlU,EAAA,CAAAc,UAAA,KAAAqT,2CAAA,mBAkDM;UAGNnU,EAAA,CAAAc,UAAA,KAAAsT,2CAAA,mBAOM;UACRpU,EAAA,CAAAM,YAAA,EAAM;UAIRN,EAAA,CAAAK,cAAA,oBAA8C;UAC5CL,EAAA,CAAAc,UAAA,KAAAuT,mDAAA,0BAA8D;UAE9DrU,EAAA,CAAAK,cAAA,eAA0B;UAElBL,EAAA,CAAAC,MAAA,kCAA0B;UAAAD,EAAA,CAAAM,YAAA,EAAK;UACnCN,EAAA,CAAAK,cAAA,SAAG;UAAAL,EAAA,CAAAC,MAAA,kEAA0D;UAAAD,EAAA,CAAAM,YAAA,EAAI;UAGnEN,EAAA,CAAAc,UAAA,KAAAwT,2CAAA,kBAGM;UAENtU,EAAA,CAAAK,cAAA,gBAA0E;UAAlCL,EAAA,CAAAgB,UAAA,sBAAAuT,+DAAA;YAAA,OAAYR,GAAA,CAAAtE,kBAAA,EAAoB;UAAA,EAAC;UACvEzP,EAAA,CAAAK,cAAA,eAA0B;UACpBL,EAAA,CAAAC,MAAA,wCAAsB;UAAAD,EAAA,CAAAM,YAAA,EAAK;UAE/BN,EAAA,CAAAU,SAAA,iBAAqD;UAErDV,EAAA,CAAAK,cAAA,eAAsB;UAEPL,EAAA,CAAAC,MAAA,gCAAmB;UAAAD,EAAA,CAAAM,YAAA,EAAY;UAC1CN,EAAA,CAAAU,SAAA,oBAAyE;UAC3EV,EAAA,CAAAM,YAAA,EAAiB;UAGnBN,EAAA,CAAAK,cAAA,eAAsB;UAEPL,EAAA,CAAAC,MAAA,gDAA8B;UAAAD,EAAA,CAAAM,YAAA,EAAY;UACrDN,EAAA,CAAAU,SAAA,iBAA0D;UAC5DV,EAAA,CAAAM,YAAA,EAAiB;UAIrBN,EAAA,CAAAK,cAAA,eAA0B;UAElBL,EAAA,CAAAC,MAAA,iBAAS;UAAAD,EAAA,CAAAM,YAAA,EAAK;UAClBN,EAAA,CAAAK,cAAA,kBAA6G;UAA1DL,EAAA,CAAAgB,UAAA,mBAAAwT,8DAAA;YAAA,OAAST,GAAA,CAAAtF,YAAA,EAAc;UAAA,EAAC;UACzEzO,EAAA,CAAAK,cAAA,gBAAU;UAAAL,EAAA,CAAAC,MAAA,WAAG;UAAAD,EAAA,CAAAM,YAAA,EAAW;UAI5BN,EAAA,CAAAK,cAAA,eAAgC;UAE5BL,EAAA,CAAAc,UAAA,KAAA2T,2DAAA,qCAyOsB;UACxBzU,EAAA,CAAAM,YAAA,EAAgB;UAIpBN,EAAA,CAAAK,cAAA,eAA0B;UAEtBL,EAAA,CAAAc,UAAA,KAAA4T,mDAAA,0BAAkF;UAClF1U,EAAA,CAAAc,UAAA,KAAA6T,4CAAA,mBAA4D;UAC9D3U,EAAA,CAAAM,YAAA,EAAS;UACTN,EAAA,CAAAK,cAAA,kBAA0D;UAAzBL,EAAA,CAAAgB,UAAA,mBAAA4T,8DAAA;YAAA,OAASb,GAAA,CAAAxD,YAAA,EAAc;UAAA,EAAC;UAACvQ,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAM,YAAA,EAAS;UAK7EN,EAAA,CAAAc,UAAA,KAAA+T,2CAAA,kBAMM;UACR7U,EAAA,CAAAM,YAAA,EAAM;UAIRN,EAAA,CAAAK,cAAA,oBAAgD;UAC9CL,EAAA,CAAAc,UAAA,KAAAgU,mDAAA,0BAAgE;UAEhE9U,EAAA,CAAAK,cAAA,eAA0B;UAElBL,EAAA,CAAAC,MAAA,gCAAwB;UAAAD,EAAA,CAAAM,YAAA,EAAK;UACjCN,EAAA,CAAAK,cAAA,SAAG;UAAAL,EAAA,CAAAC,MAAA,mEAAsD;UAAAD,EAAA,CAAAM,YAAA,EAAI;UAG/DN,EAAA,CAAAc,UAAA,KAAAiU,2CAAA,kBAGM;UAEN/U,EAAA,CAAAK,cAAA,gBAA2E;UAAjCL,EAAA,CAAAgB,UAAA,sBAAAgU,+DAAA;YAAA,OAAYjB,GAAA,CAAAzD,iBAAA,EAAmB;UAAA,EAAC;UACxEtQ,EAAA,CAAAK,cAAA,eAA0B;UACpBL,EAAA,CAAAC,MAAA,gCAAwB;UAAAD,EAAA,CAAAM,YAAA,EAAK;UAEjCN,EAAA,CAAAU,SAAA,iBAAqD;UAErDV,EAAA,CAAAK,cAAA,eAAsB;UAEPL,EAAA,CAAAC,MAAA,0BAAkB;UAAAD,EAAA,CAAAM,YAAA,EAAY;UACzCN,EAAA,CAAAK,cAAA,sBAA4C;UAClBL,EAAA,CAAAC,MAAA,4BAAe;UAAAD,EAAA,CAAAM,YAAA,EAAa;UACpDN,EAAA,CAAAK,cAAA,sBAAwB;UAAAL,EAAA,CAAAC,MAAA,yBAAiB;UAAAD,EAAA,CAAAM,YAAA,EAAa;UACtDN,EAAA,CAAAK,cAAA,sBAAwB;UAAAL,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAM,YAAA,EAAa;UAKjDN,EAAA,CAAAK,cAAA,eAAwC;UAGvBL,EAAA,CAAAC,MAAA,wBAAgB;UAAAD,EAAA,CAAAM,YAAA,EAAY;UACvCN,EAAA,CAAAU,SAAA,iBAA8C;UAChDV,EAAA,CAAAM,YAAA,EAAiB;UAEjBN,EAAA,CAAAK,cAAA,0BAAqC;UACxBL,EAAA,CAAAC,MAAA,wBAAgB;UAAAD,EAAA,CAAAM,YAAA,EAAY;UACvCN,EAAA,CAAAK,cAAA,sBAA4C;UAClBL,EAAA,CAAAC,MAAA,6BAAe;UAAAD,EAAA,CAAAM,YAAA,EAAa;UACpDN,EAAA,CAAAK,cAAA,uBAAwB;UAAAL,EAAA,CAAAC,MAAA,0BAAiB;UAAAD,EAAA,CAAAM,YAAA,EAAa;UACtDN,EAAA,CAAAK,cAAA,uBAAwB;UAAAL,EAAA,CAAAC,MAAA,eAAM;UAAAD,EAAA,CAAAM,YAAA,EAAa;UAKjDN,EAAA,CAAAK,cAAA,gBAAsB;UAGLL,EAAA,CAAAC,MAAA,gBAAO;UAAAD,EAAA,CAAAM,YAAA,EAAY;UAC9BN,EAAA,CAAAU,SAAA,kBAAgE;UAClEV,EAAA,CAAAM,YAAA,EAAiB;UAEjBN,EAAA,CAAAK,cAAA,2BAAqC;UACxBL,EAAA,CAAAC,MAAA,eAAM;UAAAD,EAAA,CAAAM,YAAA,EAAY;UAC7BN,EAAA,CAAAK,cAAA,uBAAuC;UACbL,EAAA,CAAAC,MAAA,mBAAU;UAAAD,EAAA,CAAAM,YAAA,EAAa;UAC/CN,EAAA,CAAAK,cAAA,uBAAwB;UAAAL,EAAA,CAAAC,MAAA,oCAAsB;UAAAD,EAAA,CAAAM,YAAA,EAAa;UAC3DN,EAAA,CAAAK,cAAA,uBAAwB;UAAAL,EAAA,CAAAC,MAAA,6BAAoB;UAAAD,EAAA,CAAAM,YAAA,EAAa;UAMjEN,EAAA,CAAAK,cAAA,gBAAsB;UAEPL,EAAA,CAAAC,MAAA,6BAAoB;UAAAD,EAAA,CAAAM,YAAA,EAAY;UAC3CN,EAAA,CAAAU,SAAA,kBAAmD;UACrDV,EAAA,CAAAM,YAAA,EAAiB;UAEjBN,EAAA,CAAAK,cAAA,2BAAqC;UACxBL,EAAA,CAAAC,MAAA,yBAAgB;UAAAD,EAAA,CAAAM,YAAA,EAAY;UACvCN,EAAA,CAAAU,SAAA,kBAAkF;UAGpFV,EAAA,CAAAM,YAAA,EAAiB;UAGnBN,EAAA,CAAAK,cAAA,gBAAsB;UAEPL,EAAA,CAAAC,MAAA,0BAAY;UAAAD,EAAA,CAAAM,YAAA,EAAY;UACnCN,EAAA,CAAAU,SAAA,kBAA8C;UAChDV,EAAA,CAAAM,YAAA,EAAiB;UAEjBN,EAAA,CAAAK,cAAA,2BAAqC;UACxBL,EAAA,CAAAC,MAAA,4BAAS;UAAAD,EAAA,CAAAM,YAAA,EAAY;UAChCN,EAAA,CAAAU,SAAA,kBAA4C;UAC9CV,EAAA,CAAAM,YAAA,EAAiB;UAKvBN,EAAA,CAAAK,cAAA,gBAA0B;UAEtBL,EAAA,CAAAc,UAAA,MAAAmU,oDAAA,0BAAkF;UAClFjV,EAAA,CAAAc,UAAA,MAAAoU,6CAAA,mBAAwD;UAC1DlV,EAAA,CAAAM,YAAA,EAAS;UACTN,EAAA,CAAAK,cAAA,mBAA0D;UAAzBL,EAAA,CAAAgB,UAAA,mBAAAmU,+DAAA;YAAA,OAASpB,GAAA,CAAAxD,YAAA,EAAc;UAAA,EAAC;UAACvQ,EAAA,CAAAC,MAAA,eAAM;UAAAD,EAAA,CAAAM,YAAA,EAAS;UAK7EN,EAAA,CAAAc,UAAA,MAAAsU,4CAAA,mBAiBM;UACRpV,EAAA,CAAAM,YAAA,EAAM;;;;UAngBcN,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAA8B,UAAA,gBAAe,kBAAAiS,GAAA,CAAAzN,WAAA;UAE3BtG,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAA8B,UAAA,cAAAiS,GAAA,CAAA5R,aAAA,UAAoC,cAAA4R,GAAA,CAAA3T,aAAA;UASpCJ,EAAA,CAAAO,SAAA,GAAkB;UAAlBP,EAAA,CAAA8B,UAAA,SAAAiS,GAAA,CAAAtT,YAAA,CAAkB;UAKlBT,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAA8B,UAAA,SAAAiS,GAAA,CAAAnS,QAAA,CAAAC,MAAA,OAA2B;UAQ3B7B,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAA8B,UAAA,SAAAiS,GAAA,CAAAnS,QAAA,CAAAC,MAAA,KAAyB;UAqDzB7B,EAAA,CAAAO,SAAA,GAAyC;UAAzCP,EAAA,CAAA8B,UAAA,SAAAiS,GAAA,CAAA5R,aAAA,IAAA4R,GAAA,CAAA5R,aAAA,CAAAC,IAAA,CAAyC;UAYzCpC,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAA8B,UAAA,cAAAiS,GAAA,CAAAzO,YAAA,UAAmC;UASnCtF,EAAA,CAAAO,SAAA,GAAkB;UAAlBP,EAAA,CAAA8B,UAAA,SAAAiS,GAAA,CAAAtT,YAAA,CAAkB;UAKlBT,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAA8B,UAAA,cAAAiS,GAAA,CAAAzM,mBAAA,CAAiC;UA+BYtH,EAAA,CAAAO,SAAA,IAAwB;UAAxBP,EAAA,CAAA8B,UAAA,YAAAiS,GAAA,CAAA3O,UAAA,CAAAsK,QAAA,CAAwB;UA+Of1P,EAAA,CAAAO,SAAA,GAAqD;UAArDP,EAAA,CAAA8B,UAAA,aAAAiS,GAAA,CAAA/R,SAAA,IAAA+R,GAAA,CAAAzM,mBAAA,CAAA3C,OAAA,CAAqD;UAC7F3E,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAA8B,UAAA,SAAAiS,GAAA,CAAA/R,SAAA,CAAe;UACtBhC,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAA8B,UAAA,UAAAiS,GAAA,CAAA/R,SAAA,CAAgB;UAOvBhC,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAA8B,UAAA,SAAAiS,GAAA,CAAAzO,YAAA,IAAAyO,GAAA,CAAAzO,YAAA,CAAAlD,IAAA,CAAuC;UAWvCpC,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAA8B,UAAA,cAAAiS,GAAA,CAAArO,cAAA,UAAqC;UASrC1F,EAAA,CAAAO,SAAA,GAAkB;UAAlBP,EAAA,CAAA8B,UAAA,SAAAiS,GAAA,CAAAtT,YAAA,CAAkB;UAKlBT,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAA8B,UAAA,cAAAiS,GAAA,CAAArM,qBAAA,CAAmC;UAUnB1H,EAAA,CAAAO,SAAA,IAAW;UAAXP,EAAA,CAAA8B,UAAA,YAAW;UACX9B,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAA8B,UAAA,YAAW;UACX9B,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAA8B,UAAA,YAAW;UAeT9B,EAAA,CAAAO,SAAA,IAAW;UAAXP,EAAA,CAAA8B,UAAA,YAAW;UACX9B,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAA8B,UAAA,YAAW;UACX9B,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAA8B,UAAA,YAAW;UA+BT9B,EAAA,CAAAO,SAAA,IAAmC;UAAnCP,EAAA,CAAA8B,UAAA,kBAAAuT,IAAA,CAAmC;UAClBrV,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAA8B,UAAA,QAAAuT,IAAA,CAAyB;UAoBRrV,EAAA,CAAAO,SAAA,IAAuD;UAAvDP,EAAA,CAAA8B,UAAA,aAAAiS,GAAA,CAAA/R,SAAA,IAAA+R,GAAA,CAAArM,qBAAA,CAAA/C,OAAA,CAAuD;UAC/F3E,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAA8B,UAAA,SAAAiS,GAAA,CAAA/R,SAAA,CAAe;UACtBhC,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAA8B,UAAA,UAAAiS,GAAA,CAAA/R,SAAA,CAAgB;UAOvBhC,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAA8B,UAAA,SAAAiS,GAAA,CAAArO,cAAA,IAAAqO,GAAA,CAAArO,cAAA,CAAAtD,IAAA,CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}