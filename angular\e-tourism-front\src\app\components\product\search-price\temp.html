<!-- Departure Date -->
<div class="form-group">
  <label for="departureDate">{{ currentSearchType === SEARCH_TYPE_ROUND_TRIP ? 'Departure' : 'Date' }}</label>
  <div class="input-with-icon">
    <i class="fas fa-calendar-alt"></i>
    <input
      type="date"
      id="departureDate"
      formControlName="departureDate"
      [min]="minDate"
      class="form-control"
    >
  </div>
  <div *ngIf="searchForm.get('departureDate')?.invalid && searchForm.get('departureDate')?.touched" class="error-message">
    <i class="fas fa-exclamation-circle"></i> Please select a departure date
  </div>
</div>

<!-- Return Date (visible only for round trip) -->
<div class="form-group" *ngIf="currentSearchType === SEARCH_TYPE_ROUND_TRIP">
  <label for="returnDate">Return</label>
  <div class="input-with-icon">
    <i class="fas fa-calendar-alt"></i>
    <input
      type="date"
      id="returnDate"
      formControlName="returnDate"
      [min]="searchForm.get('departureDate')?.value || minDate"
      class="form-control"
    >
  </div>
  <div *ngIf="searchForm.get('returnDate')?.invalid && searchForm.get('returnDate')?.touched" class="error-message">
    <i class="fas fa-exclamation-circle"></i> Please select a return date
  </div>
</div>
