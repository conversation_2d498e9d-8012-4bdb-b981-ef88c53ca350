{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './components/login/login.component';\nimport { SearchPriceComponent } from './components/product/search-price/search-price.component';\nimport { GetOfferComponent } from './components/product/get-offer/get-offer.component';\nimport { BookingTransactionComponent } from './components/booking/booking-transaction/booking-transaction.component';\nimport { MainLayoutComponent } from './layout/main-layout/main-layout.component';\nimport { AuthGuard } from './guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: '',\n  component: MainLayoutComponent,\n  canActivate: [AuthGuard],\n  children: [{\n    path: 'search-price',\n    component: SearchPriceComponent\n  }, {\n    path: 'get-offer',\n    component: GetOfferComponent\n  }, {\n    path: 'booking-transaction',\n    component: BookingTransactionComponent\n  }, {\n    path: '',\n    redirectTo: '/search-price',\n    pathMatch: 'full'\n  }]\n}, {\n  path: '**',\n  redirectTo: '/login'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "SearchPriceComponent", "GetOfferComponent", "BookingTransactionComponent", "MainLayoutComponent", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "component", "canActivate", "children", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { LoginComponent } from './components/login/login.component';\nimport { SearchPriceComponent } from './components/product/search-price/search-price.component';\nimport { GetOfferComponent } from './components/product/get-offer/get-offer.component';\nimport { BookingTransactionComponent } from './components/booking/booking-transaction/booking-transaction.component';\nimport { MainLayoutComponent } from './layout/main-layout/main-layout.component';\nimport { AuthGuard } from './guards/auth.guard';\n\nconst routes: Routes = [\n  { path: 'login', component: LoginComponent },\n  {\n    path: '',\n    component: MainLayoutComponent,\n    canActivate: [AuthGuard],\n    children: [\n      { path: 'search-price', component: SearchPriceComponent },\n      { path: 'get-offer', component: GetOfferComponent },\n      { path: 'booking-transaction', component: BookingTransactionComponent },\n      { path: '', redirectTo: '/search-price', pathMatch: 'full' }\n    ]\n  },\n  { path: '**', redirectTo: '/login' }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,oBAAoB,QAAQ,0DAA0D;AAC/F,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,2BAA2B,QAAQ,wEAAwE;AACpH,SAASC,mBAAmB,QAAQ,4CAA4C;AAChF,SAASC,SAAS,QAAQ,qBAAqB;;;AAE/C,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAER;AAAc,CAAE,EAC5C;EACEO,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ,mBAAmB;EAC9BK,WAAW,EAAE,CAACJ,SAAS,CAAC;EACxBK,QAAQ,EAAE,CACR;IAAEH,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEP;EAAoB,CAAE,EACzD;IAAEM,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEN;EAAiB,CAAE,EACnD;IAAEK,IAAI,EAAE,qBAAqB;IAAEC,SAAS,EAAEL;EAA2B,CAAE,EACvE;IAAEI,IAAI,EAAE,EAAE;IAAEI,UAAU,EAAE,eAAe;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE/D,EACD;EAAEL,IAAI,EAAE,IAAI;EAAEI,UAAU,EAAE;AAAQ,CAAE,CACrC;AAMD,OAAM,MAAOE,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBd,YAAY,CAACe,OAAO,CAACR,MAAM,CAAC,EAC5BP,YAAY;IAAA;EAAA;;;2EAEXc,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAjB,YAAA;IAAAkB,OAAA,GAFjBlB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}