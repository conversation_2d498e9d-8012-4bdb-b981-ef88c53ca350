import { Component, OnInit, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CountryService, Country } from '../../../services/country.service';
import { Observable } from 'rxjs';
import { FormControl } from '@angular/forms';
import { map, startWith } from 'rxjs/operators';

@Component({
  selector: 'app-country-selector',
  templateUrl: './country-selector.component.html',
  styleUrls: ['./country-selector.component.css'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CountrySelectorComponent),
      multi: true
    }
  ]
})
export class CountrySelectorComponent implements OnInit, ControlValueAccessor {
  @Input() label: string = 'Pays';
  @Input() placeholder: string = 'Sélectionnez un pays';
  @Input() required: boolean = false;
  @Output() countrySelected = new EventEmitter<Country>();

  countries: Country[] = [];
  filteredCountries: Observable<Country[]>;
  countryControl = new FormControl();
  selectedCountry: Country | null = null;

  // Fonctions pour ControlValueAccessor
  onChange: any = () => {};
  onTouched: any = () => {};

  constructor(private countryService: CountryService) {
    this.filteredCountries = this.countryControl.valueChanges.pipe(
      startWith(''),
      map(value => this._filter(value || ''))
    );
  }

  ngOnInit(): void {
    this.countryService.getCountries().subscribe(countries => {
      this.countries = countries;
    });

    this.countryControl.valueChanges.subscribe(value => {
      if (typeof value === 'string') {
        // L'utilisateur a saisi du texte, chercher le pays correspondant
        const country = this.countries.find(c => 
          c.name.toLowerCase().includes(value.toLowerCase()) || 
          c.code.toLowerCase() === value.toLowerCase()
        );
        if (country) {
          this.selectCountry(country);
        }
      } else if (value && value.code) {
        // L'utilisateur a sélectionné un pays dans la liste
        this.selectCountry(value);
      }
    });
  }

  // Méthode pour filtrer les pays en fonction de la saisie
  private _filter(value: string): Country[] {
    const filterValue = value.toLowerCase();
    return this.countries.filter(country => 
      country.name.toLowerCase().includes(filterValue) || 
      country.code.toLowerCase().includes(filterValue)
    );
  }

  // Méthode pour afficher le nom du pays dans l'autocomplete
  displayFn(country: Country): string {
    return country && country.name ? country.name : '';
  }

  // Méthode pour sélectionner un pays
  selectCountry(country: Country): void {
    this.selectedCountry = country;
    this.onChange(country.code);
    this.onTouched();
    this.countrySelected.emit(country);
  }

  // Méthodes pour ControlValueAccessor
  writeValue(value: any): void {
    if (value && typeof value === 'string') {
      // Si la valeur est un code de pays, chercher le pays correspondant
      const country = this.countryService.getCountryByCode(value);
      if (country) {
        this.selectedCountry = country;
        this.countryControl.setValue(country);
      } else {
        this.selectedCountry = null;
        this.countryControl.setValue('');
      }
    } else {
      this.selectedCountry = null;
      this.countryControl.setValue('');
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    if (isDisabled) {
      this.countryControl.disable();
    } else {
      this.countryControl.enable();
    }
  }
}
