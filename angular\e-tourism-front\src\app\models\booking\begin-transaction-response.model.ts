/**
 * Model for Begin Transaction Response
 * Response from starting a booking transaction
 */
export interface BeginTransactionResponse {
    header: Header;
    body: Body;
}

export interface Header {
    requestId: string;
    success: boolean;
    responseTime: string;
    messages: Message[];
}

export interface Message {
    id: number;
    code: string;
    messageType: number;
    message: string;
}

export interface Body {
    transactionId: string;
    expiresOn: string;
    reservationData: ReservationData;
    status: number;
    transactionType: number;
}

export interface ReservationData {
    travellers: Traveller[];
    reservationInfo: ReservationInfo;
    services: Service[];
    paymentDetail: PaymentDetail;
    invoices: Invoice[];
}

export interface Traveller {
    travellerId: string;
    type: number;
    title: number;
    name: string;
    surname: string;
    isLeader: boolean;
    birthDate: string;
    nationality: Nationality;
    identityNumber?: string;
    passportInfo?: PassportInfo;
    address?: Address;
    gender: number;
    orderNumber: number;
    passengerType: number;
}

export interface Nationality {
    twoLetterCode: string;
}

export interface PassportInfo {
    serial?: string;
    number: string;
    expireDate: string;
    issueDate: string;
    citizenshipCountryCode: string;
    issueCountryCode: string;
}

export interface Address {
    contactPhone?: ContactPhone;
    email?: string;
    address?: string;
    zipCode?: string;
    city?: City;
    country?: Country;
}

export interface ContactPhone {
    countryCode: string;
    phoneNumber: string;
}

export interface City {
    id: string;
    name: string;
}

export interface Country {
    id: string;
    name: string;
}

export interface ReservationInfo {
    bookingNumber: string;
    agency: Agency;
    agencyUser: AgencyUser;
    beginDate: string;
    endDate: string;
    note: string;
    agencyReservationNumber?: string;
    salePrice: Price;
    supplementDiscount: Price;
    passengerEB: Price;
    agencyEB: Price;
    passengerAmountToPay: Price;
    agencyAmountToPay: Price;
    discount: Price;
    agencyBalance: Price;
    passengerBalance: Price;
    agencyCommission: Commission;
    brokerCommission: Commission;
    agencySupplementCommission: Commission;
    promotionAmount: Price;
    priceToPay: Price;
    agencyPriceToPay: Price;
    passengerPriceToPay: Price;
    totalPrice: Price;
}

export interface Agency {
    code: string;
    name: string;
    country: Country;
    address: Address;
    ownAgency: boolean;
    aceExport: boolean;
}

export interface AgencyUser {
    id: number;
    name: string;
    surname: string;
    email: string;
}

export interface Price {
    amount: number;
    currency: string;
}

export interface Commission {
    amount: number;
    currency: string;
    rate: number;
}

export interface Service {
    serviceId: string;
    serviceType: number;
    serviceStatus: number;
    confirmationStatus: number;
    beginDate: string;
    endDate: string;
    nights: number;
    serviceDetails: ServiceDetails;
}

export interface ServiceDetails {
    // Can be extended as needed based on service type
}

export interface PaymentDetail {
    // Can be extended as needed
}

export interface Invoice {
    // Can be extended as needed
}
