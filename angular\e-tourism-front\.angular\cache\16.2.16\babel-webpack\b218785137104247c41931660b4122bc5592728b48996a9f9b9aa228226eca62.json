{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction LoginComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction LoginComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(authService, router, snackBar) {\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.credentials = {\n      Agency: '',\n      User: '',\n      Password: ''\n    };\n    this.loading = false;\n    this.error = '';\n    this.hidePassword = true;\n  }\n  ngOnInit() {\n    // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n    this.authService.logout();\n    // Animation d'entrée\n    document.body.classList.add('login-page-active');\n  }\n  onSubmit() {\n    if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n      this.error = 'Please fill in all required fields.';\n      return;\n    }\n    this.loading = true;\n    this.error = '';\n    this.authService.login(this.credentials).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.header.success) {\n          this.snackBar.open('Login successful! Welcome back.', 'Close', {\n            duration: 3000,\n            panelClass: 'success-snackbar',\n            verticalPosition: 'top'\n          });\n          this.router.navigate(['/accueil']);\n        } else {\n          this.error = 'Authentication failed. Please check your credentials.';\n        }\n      },\n      error: err => {\n        this.loading = false;\n        this.error = 'An error occurred during login. Please try again.';\n        console.error('Login error:', err);\n      }\n    });\n  }\n  ngOnDestroy() {\n    document.body.classList.remove('login-page-active');\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 53,\n      vars: 9,\n      consts: [[1, \"login-page\"], [1, \"login-container\"], [1, \"login-illustration\"], [1, \"decoration\", \"decoration-1\"], [1, \"decoration\", \"decoration-2\"], [1, \"decoration\", \"decoration-3\"], [1, \"decoration\", \"decoration-4\"], [1, \"login-illustration-content\"], [\"src\", \"assets/images/travel-illustration.svg\", \"alt\", \"Travel Illustration\", 1, \"illustration-image\"], [1, \"login-form-container\"], [1, \"login-form-content\", \"animate-slide-up\"], [1, \"login-logo\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"login-header\"], [1, \"login-form\", 3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"agency\"], [1, \"input-with-icon\"], [\"type\", \"text\", \"id\", \"agency\", \"name\", \"agency\", \"required\", \"\", \"placeholder\", \"Enter your agency name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"fas\", \"fa-building\"], [\"for\", \"username\"], [\"type\", \"text\", \"id\", \"username\", \"name\", \"username\", \"required\", \"\", \"placeholder\", \"Enter your username\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"fas\", \"fa-user\"], [\"for\", \"password\"], [\"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"type\", \"ngModel\", \"ngModelChange\"], [1, \"fas\", \"fa-lock\"], [\"type\", \"button\", 1, \"password-toggle\", 3, \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"class\", \"spinner-container\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"login-footer\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-circle\"], [1, \"spinner-container\"], [1, \"spinner\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵelementStart(7, \"div\", 7);\n          i0.ɵɵelement(8, \"img\", 8);\n          i0.ɵɵelementStart(9, \"h2\");\n          i0.ɵɵtext(10, \"Discover the World with E-Tourism\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\");\n          i0.ɵɵtext(12, \"Your gateway to seamless travel experiences. Book flights, hotels, and more with just a few clicks.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10)(15, \"div\", 11);\n          i0.ɵɵelement(16, \"i\", 12);\n          i0.ɵɵelementStart(17, \"h1\");\n          i0.ɵɵtext(18, \"E-Tourism\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 13)(20, \"h2\");\n          i0.ɵɵtext(21, \"Welcome Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"Sign in to continue to your account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"form\", 14, 15);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_24_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(26, \"div\", 16)(27, \"label\", 17);\n          i0.ɵɵtext(28, \"Agency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 18)(30, \"input\", 19);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_30_listener($event) {\n            return ctx.credentials.Agency = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(31, \"i\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"label\", 21);\n          i0.ɵɵtext(34, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 18)(36, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_36_listener($event) {\n            return ctx.credentials.User = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"i\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 16)(39, \"label\", 24);\n          i0.ɵɵtext(40, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 18)(42, \"input\", 25);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_42_listener($event) {\n            return ctx.credentials.Password = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"i\", 26);\n          i0.ɵɵelementStart(44, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_44_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelement(45, \"i\", 28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(46, LoginComponent_div_46_Template, 4, 1, \"div\", 29);\n          i0.ɵɵelementStart(47, \"button\", 30);\n          i0.ɵɵtemplate(48, LoginComponent_div_48_Template, 2, 0, \"div\", 31);\n          i0.ɵɵtemplate(49, LoginComponent_span_49_Template, 2, 0, \"span\", 32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 33)(51, \"p\");\n          i0.ɵɵtext(52, \"\\u00A9 2023 E-Tourism. All rights reserved.\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(25);\n          i0.ɵɵadvance(30);\n          i0.ɵɵproperty(\"ngModel\", ctx.credentials.Agency);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.credentials.User);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\")(\"ngModel\", ctx.credentials.Password);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", ctx.hidePassword ? \"fa-eye\" : \"fa-eye-slash\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !_r0.form.valid || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm],\n      styles: [\"\\n\\n[_ngcontent-%COMP%]:root {\\n  --ocean-blue: #1a73e8;\\n  --turquoise: #40c4aa;\\n  --sky-blue: #7ec8e3;\\n  --sand: #f8d0a0;\\n  --coral: #ff7e67;\\n  --sunset-orange: #ff9a76;\\n  --cloud-white: #f9fbfd;\\n  --night-blue: #2c3e50;\\n  --text-dark: #2d3748;\\n  --text-medium: #4a5568;\\n  --text-light: #718096;\\n}\\n\\n\\n\\n.login-page[_ngcontent-%COMP%] {\\n  position: relative;\\n  min-height: 100vh;\\n  width: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  font-family: 'Poppins', 'Segoe UI', sans-serif;\\n  background: linear-gradient(135deg, var(--sky-blue), var(--turquoise));\\n  padding: 1.5rem;\\n}\\n\\n\\n\\n.decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.15);\\n  z-index: 0;\\n}\\n\\n.decoration-1[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: 300px;\\n  top: -100px;\\n  right: 10%;\\n  animation: _ngcontent-%COMP%_float 20s infinite alternate ease-in-out;\\n}\\n\\n.decoration-2[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  bottom: -50px;\\n  left: 5%;\\n  animation: _ngcontent-%COMP%_float 15s infinite alternate-reverse ease-in-out;\\n}\\n\\n.decoration-3[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  top: 70%;\\n  right: 15%;\\n  animation: _ngcontent-%COMP%_pulse 10s infinite alternate ease-in-out;\\n}\\n\\n.decoration-4[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  top: 20%;\\n  left: 10%;\\n  animation: _ngcontent-%COMP%_float 12s infinite alternate ease-in-out;\\n}\\n\\n\\n\\n.login-card[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  max-width: 420px;\\n  background-color: white;\\n  border-radius: 20px;\\n  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.05);\\n  overflow: hidden;\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_slideUp 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;\\n}\\n\\n\\n\\n.login-card-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: linear-gradient(135deg, var(--ocean-blue), var(--turquoise));\\n  padding: 2rem;\\n  text-align: center;\\n  overflow: hidden;\\n}\\n\\n.login-card-header[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\" preserveAspectRatio=\\\"none\\\"><path d=\\\"M0,0 L100,0 L100,20 C75,40 25,40 0,20 Z\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/></svg>');\\n  background-size: 100% 100%;\\n  background-repeat: no-repeat;\\n  opacity: 0.6;\\n}\\n\\n.login-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 1rem;\\n}\\n\\n.login-logo[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  color: white;\\n  margin-right: 0.75rem;\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));\\n}\\n\\n.login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  color: white;\\n  text-align: center;\\n}\\n\\n.login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n\\n.login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  opacity: 0.9;\\n  margin: 0;\\n}\\n\\n\\n\\n.login-card-body[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.25rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  font-weight: 500;\\n  color: var(--text-dark);\\n  font-size: 0.9rem;\\n}\\n\\n.input-with-icon[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.input-with-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 16px;\\n  color: var(--text-light);\\n  font-size: 1rem;\\n  transition: color 0.3s ease;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 12px 12px 45px;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 10px;\\n  font-size: 0.95rem;\\n  transition: all 0.3s ease;\\n  background-color: white;\\n  color: var(--text-dark);\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--turquoise);\\n  box-shadow: 0 0 0 3px rgba(64, 196, 170, 0.15);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus    + i[_ngcontent-%COMP%] {\\n  color: var(--turquoise);\\n}\\n\\n.form-control[_ngcontent-%COMP%]::placeholder {\\n  color: var(--text-light);\\n}\\n\\n.password-toggle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 14px;\\n  background: none;\\n  border: none;\\n  color: var(--text-light);\\n  cursor: pointer;\\n  padding: 0;\\n  font-size: 1rem;\\n  transition: color 0.3s ease;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:hover {\\n  color: var(--ocean-blue);\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  color: #e53e3e;\\n  background-color: rgba(229, 62, 62, 0.08);\\n  padding: 0.75rem 1rem;\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n  font-size: 0.9rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 48px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  margin-top: 0.5rem;\\n  position: relative;\\n  background: linear-gradient(135deg, var(--ocean-blue), var(--turquoise));\\n  color: white;\\n  border: none;\\n  border-radius: 10px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 10px rgba(26, 115, 232, 0.2);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 15px rgba(26, 115, 232, 0.3);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(0);\\n  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.2);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  background: #e2e8f0;\\n  cursor: not-allowed;\\n  box-shadow: none;\\n}\\n\\n.spinner-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  border-top-color: white;\\n  animation: _ngcontent-%COMP%_spin 0.8s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  to { transform: rotate(360deg); }\\n}\\n\\n.login-footer[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  text-align: center;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n\\n.login-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-light);\\n  font-size: 0.8rem;\\n  margin: 0;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0% {\\n    transform: translateY(0) rotate(0deg);\\n  }\\n  100% {\\n    transform: translateY(30px) rotate(10deg);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n    opacity: 0.5;\\n  }\\n  100% {\\n    transform: scale(1.2);\\n    opacity: 0.2;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\nbody.login-page-active[_nghost-%COMP%], body.login-page-active   [_nghost-%COMP%] {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n\\n\\n@media (max-width: 500px) {\\n  .login-page[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n\\n  .login-card-header[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n\\n  .login-card-body[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n\\n  .login-logo[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .login-logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n\\n  .login-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n\\n  .login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "LoginComponent", "constructor", "authService", "router", "snackBar", "credentials", "Agency", "User", "Password", "loading", "hidePassword", "ngOnInit", "logout", "document", "body", "classList", "add", "onSubmit", "login", "subscribe", "next", "response", "header", "success", "open", "duration", "panelClass", "verticalPosition", "navigate", "err", "console", "ngOnDestroy", "remove", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_24_listener", "LoginComponent_Template_input_ngModelChange_30_listener", "$event", "LoginComponent_Template_input_ngModelChange_36_listener", "LoginComponent_Template_input_ngModelChange_42_listener", "LoginComponent_Template_button_click_44_listener", "ɵɵtemplate", "LoginComponent_div_46_Template", "LoginComponent_div_48_Template", "LoginComponent_span_49_Template", "ɵɵproperty", "_r0", "form", "valid"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { AuthRequest } from '../../models/auth-request.model';\nimport { MatSnackBar } from '@angular/material/snack-bar';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  credentials: AuthRequest = {\n    Agency: '',\n    User: '',\n    Password: ''\n  };\n\n  loading = false;\n  error = '';\n  hidePassword = true;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) { }\n\n  ngOnInit(): void {\n    // Déconnexion forcée pour s'assurer que l'utilisateur commence toujours par la page de login\n    this.authService.logout();\n\n    // Animation d'entrée\n    document.body.classList.add('login-page-active');\n  }\n\n  onSubmit(): void {\n    if (!this.credentials.Agency || !this.credentials.User || !this.credentials.Password) {\n      this.error = 'Please fill in all required fields.';\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    this.authService.login(this.credentials)\n      .subscribe({\n        next: (response) => {\n          this.loading = false;\n          if (response.header.success) {\n            this.snackBar.open('Login successful! Welcome back.', 'Close', {\n              duration: 3000,\n              panelClass: 'success-snackbar',\n              verticalPosition: 'top'\n            });\n            this.router.navigate(['/accueil']);\n          } else {\n            this.error = 'Authentication failed. Please check your credentials.';\n          }\n        },\n        error: (err) => {\n          this.loading = false;\n          this.error = 'An error occurred during login. Please try again.';\n          console.error('Login error:', err);\n        }\n      });\n  }\n\n  ngOnDestroy(): void {\n    document.body.classList.remove('login-page-active');\n  }\n}\n", "<div class=\"login-page\">\n  <div class=\"login-container\">\n    <!-- Section gauche avec illustration -->\n    <div class=\"login-illustration\">\n      <!-- Formes décoratives -->\n      <div class=\"decoration decoration-1\"></div>\n      <div class=\"decoration decoration-2\"></div>\n      <div class=\"decoration decoration-3\"></div>\n      <div class=\"decoration decoration-4\"></div>\n\n      <div class=\"login-illustration-content\">\n        <img src=\"assets/images/travel-illustration.svg\" alt=\"Travel Illustration\" class=\"illustration-image\">\n        <h2>Discover the World with E-Tourism</h2>\n        <p>Your gateway to seamless travel experiences. Book flights, hotels, and more with just a few clicks.</p>\n      </div>\n    </div>\n\n    <!-- Section droite avec formulaire -->\n    <div class=\"login-form-container\">\n      <div class=\"login-form-content animate-slide-up\">\n        <div class=\"login-logo\">\n          <i class=\"fas fa-plane-departure\"></i>\n          <h1>E-Tourism</h1>\n        </div>\n\n        <div class=\"login-header\">\n          <h2>Welcome Back</h2>\n          <p>Sign in to continue to your account</p>\n        </div>\n\n        <form (ngSubmit)=\"onSubmit()\" #loginForm=\"ngForm\" class=\"login-form\">\n          <div class=\"form-group\">\n            <label for=\"agency\">Agency</label>\n            <div class=\"input-with-icon\">\n              <input\n                type=\"text\"\n                id=\"agency\"\n                name=\"agency\"\n                [(ngModel)]=\"credentials.Agency\"\n                required\n                class=\"form-control\"\n                placeholder=\"Enter your agency name\">\n              <i class=\"fas fa-building\"></i>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"username\">Username</label>\n            <div class=\"input-with-icon\">\n              <input\n                type=\"text\"\n                id=\"username\"\n                name=\"username\"\n                [(ngModel)]=\"credentials.User\"\n                required\n                class=\"form-control\"\n                placeholder=\"Enter your username\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"password\">Password</label>\n            <div class=\"input-with-icon\">\n              <input\n                [type]=\"hidePassword ? 'password' : 'text'\"\n                id=\"password\"\n                name=\"password\"\n                [(ngModel)]=\"credentials.Password\"\n                required\n                class=\"form-control\"\n                placeholder=\"Enter your password\">\n              <i class=\"fas fa-lock\"></i>\n              <button\n                type=\"button\"\n                class=\"password-toggle\"\n                (click)=\"hidePassword = !hidePassword\">\n                <i class=\"fas\" [ngClass]=\"hidePassword ? 'fa-eye' : 'fa-eye-slash'\"></i>\n              </button>\n            </div>\n          </div>\n\n          <div *ngIf=\"error\" class=\"error-message\">\n            <i class=\"fas fa-exclamation-circle\"></i>\n            <span>{{ error }}</span>\n          </div>\n\n          <button\n            type=\"submit\"\n            [disabled]=\"!loginForm.form.valid || loading\"\n            class=\"login-button\">\n            <div *ngIf=\"loading\" class=\"spinner-container\">\n              <div class=\"spinner\"></div>\n            </div>\n            <span *ngIf=\"!loading\">Sign In</span>\n          </button>\n        </form>\n\n        <div class=\"login-footer\">\n          <p>© 2023 E-Tourism. All rights reserved.</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICkFUA,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAAE,SAAA,YAAyC;IACzCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAlBJ,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAOjBR,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;ADnFjD,OAAM,MAAOK,cAAc;EAWzBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAblB,KAAAC,WAAW,GAAgB;MACzBC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE;KACX;IAED,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAV,KAAK,GAAG,EAAE;IACV,KAAAW,YAAY,GAAG,IAAI;EAMf;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACT,WAAW,CAACU,MAAM,EAAE;IAEzB;IACAC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAClD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACZ,WAAW,CAACC,MAAM,IAAI,CAAC,IAAI,CAACD,WAAW,CAACE,IAAI,IAAI,CAAC,IAAI,CAACF,WAAW,CAACG,QAAQ,EAAE;MACpF,IAAI,CAACT,KAAK,GAAG,qCAAqC;MAClD;;IAGF,IAAI,CAACU,OAAO,GAAG,IAAI;IACnB,IAAI,CAACV,KAAK,GAAG,EAAE;IAEf,IAAI,CAACG,WAAW,CAACgB,KAAK,CAAC,IAAI,CAACb,WAAW,CAAC,CACrCc,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACZ,OAAO,GAAG,KAAK;QACpB,IAAIY,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;UAC3B,IAAI,CAACnB,QAAQ,CAACoB,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;YAC7DC,QAAQ,EAAE,IAAI;YACdC,UAAU,EAAE,kBAAkB;YAC9BC,gBAAgB,EAAE;WACnB,CAAC;UACF,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;SACnC,MAAM;UACL,IAAI,CAAC7B,KAAK,GAAG,uDAAuD;;MAExE,CAAC;MACDA,KAAK,EAAG8B,GAAG,IAAI;QACb,IAAI,CAACpB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACV,KAAK,GAAG,mDAAmD;QAChE+B,OAAO,CAAC/B,KAAK,CAAC,cAAc,EAAE8B,GAAG,CAAC;MACpC;KACD,CAAC;EACN;EAEAE,WAAWA,CAAA;IACTlB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACiB,MAAM,CAAC,mBAAmB,CAAC;EACrD;;;uBA3DWhC,cAAc,EAAAT,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAA0C,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAA0C,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAdvC,cAAc;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX3BvD,EAAA,CAAAC,cAAA,aAAwB;UAKlBD,EAAA,CAAAE,SAAA,aAA2C;UAK3CF,EAAA,CAAAC,cAAA,aAAwC;UACtCD,EAAA,CAAAE,SAAA,aAAsG;UACtGF,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,yCAAiC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC1CJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,2GAAmG;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAK9GJ,EAAA,CAAAC,cAAA,cAAkC;UAG5BD,EAAA,CAAAE,SAAA,aAAsC;UACtCF,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGpBJ,EAAA,CAAAC,cAAA,eAA0B;UACpBD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACrBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,2CAAmC;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAG5CJ,EAAA,CAAAC,cAAA,oBAAqE;UAA/DD,EAAA,CAAAyD,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAA9B,QAAA,EAAU;UAAA,EAAC;UAC3B1B,EAAA,CAAAC,cAAA,eAAwB;UACFD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAClCJ,EAAA,CAAAC,cAAA,eAA6B;UAKzBD,EAAA,CAAAyD,UAAA,2BAAAE,wDAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAC,MAAA,GAAA6C,MAAA;UAAA,EAAgC;UAJlC5D,EAAA,CAAAI,YAAA,EAOuC;UACvCJ,EAAA,CAAAE,SAAA,aAA+B;UACjCF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACtCJ,EAAA,CAAAC,cAAA,eAA6B;UAKzBD,EAAA,CAAAyD,UAAA,2BAAAI,wDAAAD,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAE,IAAA,GAAA4C,MAAA;UAAA,EAA8B;UAJhC5D,EAAA,CAAAI,YAAA,EAOoC;UACpCJ,EAAA,CAAAE,SAAA,aAA2B;UAC7BF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACtCJ,EAAA,CAAAC,cAAA,eAA6B;UAKzBD,EAAA,CAAAyD,UAAA,2BAAAK,wDAAAF,MAAA;YAAA,OAAAJ,GAAA,CAAA1C,WAAA,CAAAG,QAAA,GAAA2C,MAAA;UAAA,EAAkC;UAJpC5D,EAAA,CAAAI,YAAA,EAOoC;UACpCJ,EAAA,CAAAE,SAAA,aAA2B;UAC3BF,EAAA,CAAAC,cAAA,kBAGyC;UAAvCD,EAAA,CAAAyD,UAAA,mBAAAM,iDAAA;YAAA,OAAAP,GAAA,CAAArC,YAAA,IAAAqC,GAAA,CAAArC,YAAA;UAAA,EAAsC;UACtCnB,EAAA,CAAAE,SAAA,aAAwE;UAC1EF,EAAA,CAAAI,YAAA,EAAS;UAIbJ,EAAA,CAAAgE,UAAA,KAAAC,8BAAA,kBAGM;UAENjE,EAAA,CAAAC,cAAA,kBAGuB;UACrBD,EAAA,CAAAgE,UAAA,KAAAE,8BAAA,kBAEM;UACNlE,EAAA,CAAAgE,UAAA,KAAAG,+BAAA,mBAAqC;UACvCnE,EAAA,CAAAI,YAAA,EAAS;UAGXJ,EAAA,CAAAC,cAAA,eAA0B;UACrBD,EAAA,CAAAG,MAAA,mDAAsC;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;;UA7DvCJ,EAAA,CAAAK,SAAA,IAAgC;UAAhCL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAA1C,WAAA,CAAAC,MAAA,CAAgC;UAehCf,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAA1C,WAAA,CAAAE,IAAA,CAA8B;UAY9BhB,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAArC,YAAA,uBAA2C,YAAAqC,GAAA,CAAA1C,WAAA,CAAAG,QAAA;UAY5BjB,EAAA,CAAAK,SAAA,GAAoD;UAApDL,EAAA,CAAAoE,UAAA,YAAAZ,GAAA,CAAArC,YAAA,6BAAoD;UAKnEnB,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAAhD,KAAA,CAAW;UAOfR,EAAA,CAAAK,SAAA,GAA6C;UAA7CL,EAAA,CAAAoE,UAAA,cAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,IAAAf,GAAA,CAAAtC,OAAA,CAA6C;UAEvClB,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAoE,UAAA,SAAAZ,GAAA,CAAAtC,OAAA,CAAa;UAGZlB,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAoE,UAAA,UAAAZ,GAAA,CAAAtC,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}