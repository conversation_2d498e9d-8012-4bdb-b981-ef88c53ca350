{"ast": null, "code": "import { FlightClassType, PassengerType } from 'src/app/models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/services/product.service\";\nimport * as i3 from \"src/app/services/booking.service\";\nimport * as i4 from \"src/app/services/shared-data.service\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nfunction GetOfferComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading flight options...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GetOfferComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"p\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.errorMessage);\n  }\n}\nfunction GetOfferComponent_div_8_div_26_div_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"div\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const feature_r11 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r9.getFeatureIcon(feature_r11.serviceGroup));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r11.commercialName);\n  }\n}\nfunction GetOfferComponent_div_8_div_26_div_1_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const baggage_r13 = ctx.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r12.getBaggageIcon(baggage_r13.baggageType));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r12.getBaggageTypeName(baggage_r13.baggageType), \": \", baggage_r13.piece > 0 ? baggage_r13.piece + \" piece(s)\" : \"\", \" \", baggage_r13.weight > 0 ? baggage_r13.weight + \" \" + ctx_r12.getUnitTypeName(baggage_r13.unitType) : \"\", \" \");\n  }\n}\nfunction GetOfferComponent_div_8_div_26_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, GetOfferComponent_div_8_div_26_div_1_div_15_div_1_Template, 4, 5, \"div\", 45);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const offer_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(2, 1, offer_r8.baggageInformations, 0, 2));\n  }\n}\nfunction GetOfferComponent_div_8_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_div_26_div_1_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const offer_r8 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r15.selectOffer(offer_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 30)(3, \"span\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 33);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 34);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 35);\n    i0.ɵɵtemplate(13, GetOfferComponent_div_8_div_26_div_1_div_13_Template, 6, 3, \"div\", 36);\n    i0.ɵɵpipe(14, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, GetOfferComponent_div_8_div_26_div_1_div_15_Template, 3, 5, \"div\", 37);\n    i0.ɵɵelementStart(16, \"div\", 38)(17, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_div_26_div_1_Template_button_click_17_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const offer_r8 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      ctx_r17.bookFlight(offer_r8);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(18, \" Select This Fare \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const offer_r8 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", ctx_r7.isSelected(offer_r8));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"business\", ctx_r7.isBusinessClass(offer_r8));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getOfferClassName(offer_r8), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.getOfferClassCode(offer_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 10, offer_r8.price.amount, offer_r8.price.currency), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", offer_r8.flightBrandInfo && offer_r8.flightBrandInfo.name || \"Standard\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(14, 13, ctx_r7.getImportantFeatures(offer_r8), 0, 3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", offer_r8.baggageInformations && offer_r8.baggageInformations.length > 0);\n  }\n}\nfunction GetOfferComponent_div_8_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, GetOfferComponent_div_8_div_26_div_1_Template, 19, 17, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getFilteredOffers());\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baggage_r25 = ctx.$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r20.getBaggageTypeName(baggage_r25.baggageType), \": \", baggage_r25.piece > 0 ? baggage_r25.piece + \" piece(s)\" : \"\", \" \");\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 55);\n    i0.ɵɵtext(2, \" WiFi\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵtext(2, \" Meals\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 57);\n    i0.ɵɵtext(2, \" Refundable\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵtext(2, \" Changeable\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GetOfferComponent_div_8_div_27_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_div_27_tr_17_Template_tr_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const offer_r19 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r26.selectOffer(offer_r19));\n    });\n    i0.ɵɵelementStart(1, \"td\", 50)(2, \"div\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 32);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtemplate(9, GetOfferComponent_div_8_div_27_tr_17_div_9_Template, 2, 2, \"div\", 51);\n    i0.ɵɵpipe(10, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"div\", 52);\n    i0.ɵɵtemplate(13, GetOfferComponent_div_8_div_27_tr_17_span_13_Template, 3, 0, \"span\", 53);\n    i0.ɵɵtemplate(14, GetOfferComponent_div_8_div_27_tr_17_span_14_Template, 3, 0, \"span\", 53);\n    i0.ɵɵtemplate(15, GetOfferComponent_div_8_div_27_tr_17_span_15_Template, 3, 0, \"span\", 53);\n    i0.ɵɵtemplate(16, GetOfferComponent_div_8_div_27_tr_17_span_16_Template, 3, 0, \"span\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"td\", 54);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\")(21, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_div_27_tr_17_Template_button_click_21_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const offer_r19 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      ctx_r28.bookFlight(offer_r19);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(22, \" Select \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const offer_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", ctx_r18.isSelected(offer_r19));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"business\", ctx_r18.isBusinessClass(offer_r19));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.getOfferClassName(offer_r19), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r18.getOfferClassCode(offer_r19));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(offer_r19.flightBrandInfo && offer_r19.flightBrandInfo.name || \"Standard\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(10, 13, offer_r19.baggageInformations, 0, 2));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.hasFeature(offer_r19, \"wifi\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.hasFeature(offer_r19, \"meal\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.hasFeature(offer_r19, \"refund\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.hasFeature(offer_r19, \"rebook\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(19, 17, offer_r19.price.amount, offer_r19.price.currency));\n  }\n}\nfunction GetOfferComponent_div_8_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"table\", 48)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Class\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Brand\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Features\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Price\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Action\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, GetOfferComponent_div_8_div_27_tr_17_Template, 23, 20, \"tr\", 49);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.getFilteredOffers());\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_37_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 90);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 91);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementStart(5, \"span\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const classInfo_r33 = ctx.$implicit;\n    const ctx_r32 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(classInfo_r33.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r32.getFlightClassTypeName(classInfo_r33.type), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", classInfo_r33.code, \")\");\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87);\n    i0.ɵɵelement(2, \"i\", 88);\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Class Details\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 89);\n    i0.ɵɵtemplate(6, GetOfferComponent_div_8_div_28_div_37_div_6_Template, 7, 3, \"div\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r29.selectedOffer.flightClassInformations);\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_38_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98);\n    i0.ɵɵelement(2, \"i\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 99)(4, \"div\", 100);\n    i0.ɵɵtext(5, \"Checked Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 101);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const baggage_r36 = ctx.$implicit;\n    const ctx_r34 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\" \", baggage_r36.piece > 0 ? baggage_r36.piece + \" piece(s)\" : \"\", \" \", baggage_r36.weight > 0 ? baggage_r36.weight + \" \" + ctx_r34.getUnitTypeName(baggage_r36.unitType) : \"\", \" \");\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_38_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"div\", 98);\n    i0.ɵɵelement(2, \"i\", 103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 99)(4, \"div\", 100);\n    i0.ɵɵtext(5, \"Cabin Baggage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 101);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const baggage_r37 = ctx.$implicit;\n    const ctx_r35 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\" \", baggage_r37.piece > 0 ? baggage_r37.piece + \" piece(s)\" : \"\", \" \", baggage_r37.weight > 0 ? baggage_r37.weight + \" \" + ctx_r35.getUnitTypeName(baggage_r37.unitType) : \"\", \" \");\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 87);\n    i0.ɵɵelement(2, \"i\", 93);\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Baggage Allowance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 94);\n    i0.ɵɵtemplate(6, GetOfferComponent_div_8_div_28_div_38_div_6_Template, 8, 2, \"div\", 95);\n    i0.ɵɵpipe(7, \"slice\");\n    i0.ɵɵtemplate(8, GetOfferComponent_div_8_div_28_div_38_div_8_Template, 8, 2, \"div\", 96);\n    i0.ɵɵpipe(9, \"slice\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(7, 2, ctx_r30.selectedOffer.baggageInformations, 0, 1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(9, 6, ctx_r30.selectedOffer.baggageInformations, 1, 2));\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_39_div_6_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r39 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", feature_r39.explanations[0].text, \" \");\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_39_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"div\", 109);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 110)(4, \"div\", 111);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, GetOfferComponent_div_8_div_28_div_39_div_6_div_6_Template, 2, 1, \"div\", 112);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r39 = ctx.$implicit;\n    const ctx_r38 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r38.getFeatureIcon(feature_r39.serviceGroup));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r39.commercialName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", feature_r39.explanations && feature_r39.explanations.length > 0);\n  }\n}\nfunction GetOfferComponent_div_8_div_28_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104)(1, \"div\", 87);\n    i0.ɵɵelement(2, \"i\", 105);\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Included Services\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 106);\n    i0.ɵɵtemplate(6, GetOfferComponent_div_8_div_28_div_39_div_6_Template, 7, 4, \"div\", 107);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r31.getImportantFeatures(ctx_r31.selectedOffer));\n  }\n}\nfunction GetOfferComponent_div_8_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"div\", 61)(3, \"h3\");\n    i0.ɵɵtext(4, \"Selected Fare Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 62)(6, \"span\", 63);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"currency\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 64);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"div\", 66)(13, \"div\", 67)(14, \"div\", 68);\n    i0.ɵɵelement(15, \"i\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 70)(17, \"div\", 71);\n    i0.ɵɵtext(18, \"Flight\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 72);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 73)(22, \"div\", 68);\n    i0.ɵɵelement(23, \"i\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 70)(25, \"div\", 71);\n    i0.ɵɵtext(26, \"Brand\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 72);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 75)(30, \"div\", 68);\n    i0.ɵɵelement(31, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 70)(33, \"div\", 71);\n    i0.ɵɵtext(34, \"Passenger\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 72);\n    i0.ɵɵtext(36, \"1 Adult\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(37, GetOfferComponent_div_8_div_28_div_37_Template, 7, 1, \"div\", 77);\n    i0.ɵɵtemplate(38, GetOfferComponent_div_8_div_28_div_38_Template, 10, 10, \"div\", 78);\n    i0.ɵɵtemplate(39, GetOfferComponent_div_8_div_28_div_39_Template, 7, 1, \"div\", 79);\n    i0.ɵɵelementStart(40, \"div\", 80)(41, \"div\", 81)(42, \"div\", 82);\n    i0.ɵɵelement(43, \"span\", 83)(44, \"span\", 83)(45, \"span\", 83)(46, \"span\", 83)(47, \"span\", 83)(48, \"span\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_div_28_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r42.bookFlight(ctx_r42.selectedOffer));\n    });\n    i0.ɵɵelement(50, \"i\", 85);\n    i0.ɵɵtext(51, \" Book This Flight \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 9, ctx_r6.selectedOffer.price.amount, ctx_r6.selectedOffer.price.currency));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"business\", ctx_r6.isBusinessClass(ctx_r6.selectedOffer));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getOfferClassName(ctx_r6.selectedOffer), \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r6.selectedOffer.flightId);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r6.selectedOffer.flightBrandInfo && ctx_r6.selectedOffer.flightBrandInfo.name || \"Flexible\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectedOffer.flightClassInformations && ctx_r6.selectedOffer.flightClassInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectedOffer.baggageInformations && ctx_r6.selectedOffer.baggageInformations.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectedOffer.flightBrandInfo && ctx_r6.selectedOffer.flightBrandInfo.features && ctx_r6.selectedOffer.flightBrandInfo.features.length > 0);\n  }\n}\nfunction GetOfferComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13)(3, \"div\", 14)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵtext(7, \"Istanbul (IST) \\u2192 Tunis (TUN)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 16)(9, \"div\", 17)(10, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.setViewMode(\"cards\"));\n    });\n    i0.ɵɵelement(11, \"i\", 19);\n    i0.ɵɵtext(12, \" Cards \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.setViewMode(\"table\"));\n    });\n    i0.ɵɵelement(14, \"i\", 20);\n    i0.ɵɵtext(15, \" Table \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 21)(17, \"span\", 22);\n    i0.ɵɵtext(18, \"Filter by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 23)(20, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.filterByClass(\"all\"));\n    });\n    i0.ɵɵtext(21, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.filterByClass(\"economy\"));\n    });\n    i0.ɵɵtext(23, \"Economy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function GetOfferComponent_div_8_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.filterByClass(\"business\"));\n    });\n    i0.ɵɵtext(25, \"Business\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(26, GetOfferComponent_div_8_div_26_Template, 2, 1, \"div\", 24);\n    i0.ɵɵtemplate(27, GetOfferComponent_div_8_div_27_Template, 18, 1, \"div\", 25);\n    i0.ɵɵtemplate(28, GetOfferComponent_div_8_div_28_Template, 52, 12, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.offers[0].flightId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r2.viewMode === \"cards\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", ctx_r2.viewMode === \"table\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"active\", ctx_r2.selectedClassFilter === \"all\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r2.selectedClassFilter === \"economy\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r2.selectedClassFilter === \"business\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.viewMode === \"cards\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.viewMode === \"table\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedOffer);\n  }\n}\nfunction GetOfferComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114)(1, \"p\");\n    i0.ɵɵtext(2, \"No flight options available for the selected criteria.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let GetOfferComponent = /*#__PURE__*/(() => {\n  class GetOfferComponent {\n    constructor(route, router, productService, bookingService, sharedDataService, snackBar) {\n      this.route = route;\n      this.router = router;\n      this.productService = productService;\n      this.bookingService = bookingService;\n      this.sharedDataService = sharedDataService;\n      this.snackBar = snackBar;\n      // Paramètres de requête\n      this.searchId = '';\n      this.offerId = '';\n      this.passengersParam = '';\n      // Données\n      this.offers = [];\n      this.selectedOffer = null;\n      this.passengerCounts = {};\n      // État de l'interface\n      this.isLoading = false;\n      this.errorMessage = '';\n      this.viewMode = 'cards';\n      this.selectedClassFilter = 'all';\n    }\n    ngOnInit() {\n      this.route.queryParams.subscribe(params => {\n        this.searchId = params['searchId'];\n        this.offerId = params['offerId'];\n        this.passengersParam = params['passengers'];\n        console.log('GetOfferComponent received params:', params);\n        console.log('SearchId:', this.searchId);\n        console.log('OfferId:', this.offerId);\n        console.log('Passengers:', this.passengersParam);\n        // Récupérer les informations de passagers\n        if (this.passengersParam) {\n          try {\n            this.passengerCounts = JSON.parse(this.passengersParam);\n            // Mettre à jour le service partagé avec les informations de passagers\n            this.sharedDataService.setPassengerCounts(this.passengerCounts);\n            console.log('Passenger counts parsed successfully:', this.passengerCounts);\n          } catch (error) {\n            console.error('Error parsing passenger information:', error);\n            // Utiliser les valeurs par défaut du service\n            this.passengerCounts = this.sharedDataService.getPassengerCounts();\n          }\n        } else {\n          // Utiliser les valeurs par défaut du service\n          this.passengerCounts = this.sharedDataService.getPassengerCounts();\n        }\n        // Vérifier si les paramètres requis sont présents\n        const missingParams = [];\n        if (!this.searchId) missingParams.push('searchId');\n        if (!this.offerId) missingParams.push('offerId');\n        if (missingParams.length === 0) {\n          // Tous les paramètres sont présents\n          this.loadOfferDetails();\n        } else {\n          // Paramètres manquants\n          this.errorMessage = `Missing parameters: ${missingParams.join(', ')}. Please go back and try again.`;\n          console.error('Missing parameters:', {\n            searchId: this.searchId,\n            offerId: this.offerId\n          });\n          // Essayer de récupérer les paramètres manquants de l'URL\n          const url = window.location.href;\n          console.log('Current URL:', url);\n          // Analyser l'URL pour trouver des paramètres potentiellement mal formatés\n          const urlParams = new URLSearchParams(window.location.search);\n          const paramsObj = {};\n          urlParams.forEach((value, key) => {\n            paramsObj[key] = value;\n          });\n          console.log('All URL params:', paramsObj);\n        }\n      });\n    }\n    loadOfferDetails() {\n      this.isLoading = true;\n      this.errorMessage = '';\n      const request = this.productService.createDefaultGetOffersRequest(this.searchId, [this.offerId]);\n      console.log('Sending GetOffersRequest:', request);\n      this.productService.getOffers(request).subscribe({\n        next: response => {\n          this.isLoading = false;\n          console.log('GetOffers response:', response);\n          if (response.header.success && response.body.offers && response.body.offers.length > 0) {\n            console.log('Offers found:', response.body.offers);\n            this.offers = response.body.offers;\n            // Sélectionner la première offre par défaut\n            if (this.offers.length > 0) {\n              this.selectedOffer = this.offers[0];\n            }\n            // Trier les offres par prix (du moins cher au plus cher)\n            this.sortOffersByPrice();\n          } else {\n            console.error('No offers found in response or response not successful');\n            console.log('Response header success:', response.header.success);\n            console.log('Response body offers:', response.body.offers);\n            this.errorMessage = 'No flight options found.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              console.log('Error message from API:', response.header.messages[0].message);\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred while loading flight options. Please try again.';\n          console.error('Error getting offers:', error);\n          // Afficher plus de détails sur l'erreur\n          if (error.error) {\n            console.log('Error details:', error.error);\n          }\n          if (error.message) {\n            console.log('Error message:', error.message);\n          }\n          if (error.status) {\n            console.log('Error status:', error.status);\n          }\n        }\n      });\n    }\n    // Méthodes de tri et de filtrage\n    sortOffersByPrice() {\n      this.offers.sort((a, b) => a.price.amount - b.price.amount);\n    }\n    getFilteredOffers() {\n      if (this.selectedClassFilter === 'all') {\n        return this.offers;\n      }\n      return this.offers.filter(offer => {\n        const isBusinessClass = this.isBusinessClass(offer);\n        return this.selectedClassFilter === 'business' && isBusinessClass || this.selectedClassFilter === 'economy' && !isBusinessClass;\n      });\n    }\n    filterByClass(classType) {\n      this.selectedClassFilter = classType;\n    }\n    setViewMode(mode) {\n      this.viewMode = mode;\n    }\n    // Méthodes de sélection\n    selectOffer(offer) {\n      this.selectedOffer = offer;\n    }\n    isSelected(offer) {\n      return this.selectedOffer === offer;\n    }\n    goBack() {\n      this.router.navigate(['/search-price']);\n    }\n    bookFlight(offer) {\n      console.log('Starting booking transaction with offer ID:', offer.offerId);\n      // Afficher un indicateur de chargement\n      this.isLoading = true;\n      this.errorMessage = '';\n      // Démarrer la transaction automatiquement\n      this.bookingService.beginTransaction([offer.offerId]).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response && response.beginResponse && response.beginResponse.body) {\n            const transactionId = response.beginResponse.body.transactionId;\n            console.log('Transaction started successfully with ID:', transactionId);\n            // Naviguer vers la page de réservation avec le transactionId et les informations de passagers\n            // Cela permettra au composant booking-transaction de sauter l'étape 1\n            this.router.navigate(['/booking-transaction'], {\n              queryParams: {\n                offerIds: offer.offerId,\n                transactionId: transactionId,\n                searchId: this.searchId,\n                passengers: this.passengersParam,\n                autoStarted: 'true'\n              }\n            });\n            // Afficher un message de succès\n            this.snackBar.open('Réservation initiée avec succès', 'Fermer', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n          } else {\n            this.errorMessage = 'Erreur lors du démarrage de la transaction. Veuillez réessayer.';\n            console.error('Invalid response from beginTransaction:', response);\n            // Afficher un message d'erreur\n            this.snackBar.open(this.errorMessage, 'Fermer', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = 'Une erreur est survenue lors du démarrage de la réservation. Veuillez réessayer.';\n          console.error('Error starting transaction:', error);\n          // Afficher un message d'erreur\n          this.snackBar.open(this.errorMessage, 'Fermer', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          // En cas d'erreur, rediriger vers la page de réservation traditionnelle\n          this.router.navigate(['/booking-transaction'], {\n            queryParams: {\n              offerIds: offer.offerId,\n              searchId: this.searchId,\n              passengers: this.passengersParam\n            }\n          });\n        }\n      });\n    }\n    // Méthodes d'analyse des offres\n    isBusinessClass(offer) {\n      if (!offer.flightClassInformations || offer.flightClassInformations.length === 0) {\n        return false;\n      }\n      return offer.flightClassInformations.some(classInfo => classInfo.type === FlightClassType.BUSINESS);\n    }\n    getOfferClassName(offer) {\n      if (!offer.flightClassInformations || offer.flightClassInformations.length === 0) {\n        return 'Unknown';\n      }\n      return offer.flightClassInformations[0].name;\n    }\n    getOfferClassCode(offer) {\n      if (!offer.flightClassInformations || offer.flightClassInformations.length === 0) {\n        return '';\n      }\n      return offer.flightClassInformations[0].code;\n    }\n    // Méthodes pour les caractéristiques des offres\n    hasFeature(offer, featureName) {\n      if (!offer.flightBrandInfo || !offer.flightBrandInfo.features) {\n        return false;\n      }\n      return offer.flightBrandInfo.features.some(feature => feature.commercialName.toLowerCase().includes(featureName.toLowerCase()));\n    }\n    getImportantFeatures(offer) {\n      if (!offer || !offer.flightBrandInfo || !offer.flightBrandInfo.features) {\n        return [];\n      }\n      // Filtrer les caractéristiques pour n'afficher que les plus importantes\n      // et éviter les doublons\n      const uniqueFeatures = new Map();\n      offer.flightBrandInfo.features.forEach(feature => {\n        // Ne pas ajouter de doublons basés sur le nom commercial\n        if (!uniqueFeatures.has(feature.commercialName)) {\n          uniqueFeatures.set(feature.commercialName, feature);\n        }\n      });\n      // Convertir la Map en tableau et limiter à 6 caractéristiques maximum\n      return Array.from(uniqueFeatures.values()).slice(0, 6);\n    }\n    // Méthodes utilitaires pour afficher des valeurs lisibles\n    getProviderName(providerCode) {\n      const providers = {\n        1: 'Provider A',\n        2: 'Provider B',\n        3: 'Provider C'\n      };\n      return providers[providerCode] || `Provider ${providerCode}`;\n    }\n    getFlightClassTypeName(typeCode) {\n      switch (typeCode) {\n        case FlightClassType.PROMO:\n          return 'Promo';\n        case FlightClassType.ECONOMY:\n          return 'Economy';\n        case FlightClassType.BUSINESS:\n          return 'Business';\n        default:\n          return `Class Type ${typeCode}`;\n      }\n    }\n    getPassengerTypeName(typeCode) {\n      switch (typeCode) {\n        case PassengerType.Adult:\n          return 'Adult';\n        case PassengerType.Child:\n          return 'Child';\n        case PassengerType.Infant:\n          return 'Infant';\n        default:\n          return `Passenger Type ${typeCode}`;\n      }\n    }\n    getUnitTypeName(unitTypeCode) {\n      const unitTypes = {\n        1: 'kg',\n        2: 'lb'\n      };\n      return unitTypes[unitTypeCode] || '';\n    }\n    getBaggageTypeName(baggageTypeCode) {\n      const baggageTypes = {\n        1: 'Checked Baggage',\n        2: 'Cabin Baggage',\n        3: 'Hand Baggage'\n      };\n      return baggageTypes[baggageTypeCode] || `Baggage Type ${baggageTypeCode}`;\n    }\n    // Méthodes pour les icônes\n    getBaggageIcon(baggageTypeCode) {\n      const icons = {\n        1: 'fas fa-suitcase',\n        2: 'fas fa-briefcase',\n        3: 'fas fa-shopping-bag'\n      };\n      return icons[baggageTypeCode] || 'fas fa-luggage-cart';\n    }\n    getFeatureIcon(serviceGroupCode) {\n      const icons = {\n        0: 'fas fa-star',\n        1: 'fas fa-suitcase',\n        2: 'fas fa-utensils',\n        3: 'fas fa-wifi',\n        4: 'fas fa-couch',\n        5: 'fas fa-hamburger' // Meals\n      };\n\n      return icons[serviceGroupCode] || 'fas fa-star';\n    }\n    static {\n      this.ɵfac = function GetOfferComponent_Factory(t) {\n        return new (t || GetOfferComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.BookingService), i0.ɵɵdirectiveInject(i4.SharedDataService), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: GetOfferComponent,\n        selectors: [[\"app-get-offer\"]],\n        decls: 10,\n        vars: 4,\n        consts: [[1, \"get-offer-container\"], [1, \"offer-header\"], [1, \"back-button\", 3, \"click\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"offers-container\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"spinner\", \"large\"], [1, \"error-container\"], [1, \"error-message\"], [1, \"offers-container\"], [1, \"flight-summary-card\"], [1, \"flight-summary-header\"], [1, \"flight-info\"], [1, \"flight-route\"], [1, \"view-controls\"], [1, \"view-toggle\"], [3, \"click\"], [1, \"fas\", \"fa-th-large\"], [1, \"fas\", \"fa-table\"], [1, \"filter-controls\"], [1, \"filter-label\"], [1, \"filter-buttons\"], [\"class\", \"offers-grid\", 4, \"ngIf\"], [\"class\", \"offers-table-container\", 4, \"ngIf\"], [\"class\", \"selected-offer-details\", 4, \"ngIf\"], [1, \"offers-grid\"], [\"class\", \"offer-card\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"offer-card\", 3, \"click\"], [1, \"offer-class\"], [1, \"class-badge\"], [1, \"class-code\"], [1, \"offer-price\"], [1, \"offer-brand\"], [1, \"offer-features\"], [\"class\", \"feature-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"offer-baggage\", 4, \"ngIf\"], [1, \"offer-actions\"], [1, \"book-button\", 3, \"click\"], [1, \"feature-item\"], [1, \"feature-icon\"], [1, \"feature-content\"], [1, \"feature-name\"], [1, \"offer-baggage\"], [\"class\", \"baggage-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"baggage-item\"], [1, \"offers-table-container\"], [1, \"offers-table\"], [3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"class-cell\"], [4, \"ngFor\", \"ngForOf\"], [1, \"table-features\"], [4, \"ngIf\"], [1, \"price-cell\"], [1, \"fas\", \"fa-wifi\"], [1, \"fas\", \"fa-utensils\"], [1, \"fas\", \"fa-undo\"], [1, \"fas\", \"fa-exchange-alt\"], [1, \"selected-offer-details\"], [1, \"details-header\"], [1, \"details-header-content\"], [1, \"selected-price\"], [1, \"price-amount\"], [1, \"selected-class-badge\"], [1, \"details-content\"], [1, \"info-cards\"], [1, \"info-card\", \"flight-card\"], [1, \"info-card-icon\"], [1, \"fas\", \"fa-plane\"], [1, \"info-card-content\"], [1, \"info-card-label\"], [1, \"info-card-value\"], [1, \"info-card\", \"brand-card\"], [1, \"fas\", \"fa-tag\"], [1, \"info-card\", \"passenger-card\"], [1, \"fas\", \"fa-user\"], [\"class\", \"details-section class-details\", 4, \"ngIf\"], [\"class\", \"details-section baggage-section\", 4, \"ngIf\"], [\"class\", \"details-section services-section\", 4, \"ngIf\"], [1, \"booking-section\"], [1, \"button-container\"], [1, \"particles-container\"], [1, \"particle\"], [1, \"book-flight-button\", \"animate-pulse\", 3, \"click\"], [1, \"fas\", \"fa-check-circle\"], [1, \"details-section\", \"class-details\"], [1, \"section-header\"], [1, \"fas\", \"fa-chair\"], [1, \"class-details-content\"], [1, \"class-name\"], [1, \"class-type\"], [1, \"details-section\", \"baggage-section\"], [1, \"fas\", \"fa-suitcase\"], [1, \"baggage-cards\"], [\"class\", \"baggage-card checked\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"baggage-card cabin\", 4, \"ngFor\", \"ngForOf\"], [1, \"baggage-card\", \"checked\"], [1, \"baggage-icon\"], [1, \"baggage-details\"], [1, \"baggage-type\"], [1, \"baggage-value\"], [1, \"baggage-card\", \"cabin\"], [1, \"fas\", \"fa-briefcase\"], [1, \"details-section\", \"services-section\"], [1, \"fas\", \"fa-concierge-bell\"], [1, \"services-grid\"], [\"class\", \"service-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"service-item\"], [1, \"service-icon\"], [1, \"service-details\"], [1, \"service-name\"], [\"class\", \"service-description\", 4, \"ngIf\"], [1, \"service-description\"], [1, \"no-results\"]],\n        template: function GetOfferComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3, \"Flight Options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function GetOfferComponent_Template_button_click_4_listener() {\n              return ctx.goBack();\n            });\n            i0.ɵɵtext(5, \"Back to Search\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(6, GetOfferComponent_div_6_Template, 4, 0, \"div\", 3);\n            i0.ɵɵtemplate(7, GetOfferComponent_div_7_Template, 3, 1, \"div\", 4);\n            i0.ɵɵtemplate(8, GetOfferComponent_div_8_Template, 29, 14, \"div\", 5);\n            i0.ɵɵtemplate(9, GetOfferComponent_div_9_Template, 3, 0, \"div\", 6);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage && !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.offers && ctx.offers.length > 0 && !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.offers && ctx.offers.length === 0 && !ctx.isLoading);\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i6.SlicePipe, i6.CurrencyPipe],\n        styles: [\".get-offer-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px;font-family:Segoe UI,Roboto,Helvetica Neue,sans-serif}.offer-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.offer-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#2989d8;margin:0}.back-button[_ngcontent-%COMP%]{background-color:#f0f0f0;border:none;border-radius:4px;padding:8px 16px;cursor:pointer;display:flex;align-items:center;font-weight:500;color:#333;transition:background-color .3s}.back-button[_ngcontent-%COMP%]:hover{background-color:#e0e0e0}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px}.spinner[_ngcontent-%COMP%]{width:20px;height:20px;border:3px solid rgba(41,137,216,.3);border-radius:50%;border-top-color:#2989d8;animation:_ngcontent-%COMP%_spin 1s ease-in-out infinite}.spinner.large[_ngcontent-%COMP%]{width:40px;height:40px;border-width:4px}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}.error-container[_ngcontent-%COMP%]{padding:20px;background-color:#fdecea;border-radius:5px;text-align:center}.error-message[_ngcontent-%COMP%]{color:#e74c3c;margin:0}.offers-container[_ngcontent-%COMP%]{margin-top:20px}.flight-summary-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:8px;box-shadow:0 2px 10px #0000001a;margin-bottom:20px;overflow:hidden}.flight-summary-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:20px;background:linear-gradient(135deg,#2989d8,#1e5799);color:#fff}.flight-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:22px;margin:0 0 5px;font-weight:600}.flight-route[_ngcontent-%COMP%]{font-size:16px;opacity:.9}.view-controls[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:10px}.view-toggle[_ngcontent-%COMP%]{display:flex;background-color:#fff3;border-radius:4px;overflow:hidden}.view-toggle[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:none;border:none;color:#fff;padding:8px 12px;font-size:14px;cursor:pointer;display:flex;align-items:center;gap:6px;transition:background-color .2s}.view-toggle[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%]{background-color:#ffffff4d;font-weight:500}.view-toggle[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover:not(.active){background-color:#ffffff26}.filter-controls[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.filter-label[_ngcontent-%COMP%]{font-size:14px;color:#ffffffe6}.filter-buttons[_ngcontent-%COMP%]{display:flex;gap:5px}.filter-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background-color:#fff3;border:none;border-radius:4px;padding:5px 10px;font-size:13px;color:#fff;cursor:pointer;transition:all .2s}.filter-buttons[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%]{background-color:#fff;color:#2989d8;font-weight:500}.filter-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover:not(.active){background-color:#ffffff4d}.offers-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:20px;margin-bottom:20px}.offer-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:8px;box-shadow:0 2px 10px #0000001a;overflow:hidden;transition:transform .2s,box-shadow .2s;cursor:pointer;position:relative;border:2px solid transparent}.offer-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 16px #0000001a}.offer-card.selected[_ngcontent-%COMP%]{border-color:#2989d8;box-shadow:0 0 0 2px #2989d833}.offer-section[_ngcontent-%COMP%]{padding:20px;border-bottom:1px solid #eee}.offer-section[_ngcontent-%COMP%]:last-child{border-bottom:none}.offer-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#2989d8;margin-top:0;margin-bottom:15px;font-size:18px}.offer-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#555;margin-top:15px;margin-bottom:10px;font-size:16px}.info-row[_ngcontent-%COMP%]{display:flex;margin-bottom:8px}.info-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{flex:0 0 150px;font-weight:500;color:#666}.info-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{flex:1;color:#333}.price-display[_ngcontent-%COMP%]{text-align:center}.price-amount[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#2989d8}.class-info[_ngcontent-%COMP%], .baggage-info[_ngcontent-%COMP%], .feature[_ngcontent-%COMP%]{background-color:#f8f9fa;border-radius:4px;padding:12px;margin-bottom:10px}.class-info[_ngcontent-%COMP%]:last-child, .baggage-info[_ngcontent-%COMP%]:last-child, .feature[_ngcontent-%COMP%]:last-child{margin-bottom:0}.offer-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:15px;border-bottom:1px solid #eee}.offer-class[_ngcontent-%COMP%]{display:flex;flex-direction:column}.class-badge[_ngcontent-%COMP%]{display:inline-block;padding:4px 8px;background-color:#e8f4fd;color:#2989d8;border-radius:4px;font-size:14px;font-weight:500;margin-bottom:4px}.class-badge.business[_ngcontent-%COMP%]{background-color:#fff8e1;color:#ffa000}.class-code[_ngcontent-%COMP%]{font-size:12px;color:#666}.offer-price[_ngcontent-%COMP%]{font-size:20px;font-weight:700;color:#2989d8}.offer-brand[_ngcontent-%COMP%]{padding:10px 15px;font-size:16px;font-weight:500;color:#333;background-color:#f8f9fa}.offer-features[_ngcontent-%COMP%]{padding:15px;display:flex;flex-wrap:wrap;gap:8px}.feature-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:8px 12px;background-color:#f1f3f4;border-radius:16px;font-size:13px;color:#555}.feature-icon[_ngcontent-%COMP%]{color:#2989d8;font-size:14px}.offer-baggage[_ngcontent-%COMP%]{padding:0 15px 15px;font-size:14px;color:#666}.baggage-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:5px}.baggage-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#2989d8}.offer-actions[_ngcontent-%COMP%]{padding:15px;text-align:center;border-top:1px solid #eee}.book-button[_ngcontent-%COMP%]{background-color:#2989d8;color:#fff;border:none;border-radius:4px;padding:10px 20px;font-size:15px;font-weight:600;cursor:pointer;transition:background-color .3s;width:100%;display:inline-block;text-align:center;text-decoration:none;box-sizing:border-box}.book-button[_ngcontent-%COMP%]:hover{background-color:#1e5799;color:#fff;text-decoration:none}.offers-table-container[_ngcontent-%COMP%]{background-color:#fff;border-radius:8px;box-shadow:0 2px 10px #0000001a;overflow:hidden;margin-bottom:20px}.offers-table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse}.offers-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .offers-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:15px;text-align:left;border-bottom:1px solid #eee}.offers-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:#f8f9fa;font-weight:600;color:#555}.offers-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:background-color .2s;cursor:pointer}.offers-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.offers-table[_ngcontent-%COMP%]   tr.selected[_ngcontent-%COMP%]{background-color:#e8f4fd}.class-cell[_ngcontent-%COMP%]{width:120px}.price-cell[_ngcontent-%COMP%]{font-weight:700;color:#2989d8}.table-features[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:10px}.table-features[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:flex;align-items:center;gap:5px;font-size:13px;color:#555}.table-features[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#2989d8}.selected-offer-details[_ngcontent-%COMP%]{background-color:#fff;border-radius:16px;box-shadow:0 10px 30px #00000014;overflow:hidden;margin-top:40px;border:1px solid rgba(0,0,0,.04);position:relative;transition:transform .3s ease,box-shadow .3s ease}.selected-offer-details[_ngcontent-%COMP%]:hover{box-shadow:0 15px 40px #0000001f}.details-header[_ngcontent-%COMP%]{padding:24px 30px;background:linear-gradient(135deg,var(--primary-color),var(--ocean-blue));color:#fff;position:relative;display:flex;justify-content:space-between;align-items:center}.details-header[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgzMCkiPjxwYXRoIGQ9Ik0gMCwxMCBMIDIwLDEwIiBzdHJva2U9InJnYmEoMjU1LDI1NSwyNTUsMC4xKSIgc3Ryb2tlLXdpZHRoPSIxIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+);opacity:.3;z-index:0}.details-header-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;position:relative;z-index:1}.details-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;color:#fff;font-size:24px;font-weight:700;letter-spacing:.5px;text-shadow:0 2px 4px rgba(0,0,0,.1)}.selected-price[_ngcontent-%COMP%]{display:flex;align-items:center}.price-amount[_ngcontent-%COMP%]{font-size:26px;font-weight:700;color:#fff;text-shadow:0 2px 4px rgba(0,0,0,.1);position:relative}.price-amount[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:-4px;left:0;width:100%;height:2px;background-color:#ffffff4d;border-radius:2px}.selected-class-badge[_ngcontent-%COMP%]{background-color:#ffffff26;color:#fff;padding:10px 20px;border-radius:30px;font-weight:600;font-size:15px;display:flex;align-items:center;justify-content:center;-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);box-shadow:0 4px 12px #0000001a;position:relative;z-index:1;transition:transform .3s ease,background-color .3s ease}.selected-class-badge[_ngcontent-%COMP%]:hover{transform:translateY(-2px);background-color:#ffffff40}.selected-class-badge.business[_ngcontent-%COMP%]{background-color:#ffc10740;color:#fff}.selected-class-badge.business[_ngcontent-%COMP%]:hover{background-color:#ffc10759}.details-content[_ngcontent-%COMP%]{padding:30px}.info-cards[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:20px;margin-bottom:35px}.info-card[_ngcontent-%COMP%]{flex:1;min-width:200px;background-color:#fff;border-radius:12px;padding:20px;display:flex;align-items:center;gap:15px;box-shadow:0 4px 15px #0000000d;border:1px solid rgba(0,0,0,.04);transition:transform .3s ease,box-shadow .3s ease}.info-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 8px 25px #00000014}.info-card-icon[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:12px;display:flex;align-items:center;justify-content:center;font-size:20px;flex-shrink:0}.flight-card[_ngcontent-%COMP%]   .info-card-icon[_ngcontent-%COMP%]{background-color:rgba(var(--primary-color-rgb),.1);color:var(--primary-color)}.brand-card[_ngcontent-%COMP%]   .info-card-icon[_ngcontent-%COMP%]{background-color:rgba(var(--accent-color-rgb),.1);color:var(--accent-color)}.passenger-card[_ngcontent-%COMP%]   .info-card-icon[_ngcontent-%COMP%]{background-color:rgba(var(--secondary-color-rgb),.1);color:var(--secondary-color)}.info-card-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:5px}.info-card-label[_ngcontent-%COMP%]{font-size:14px;color:#666;font-weight:500}.info-card-value[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#333}.details-section[_ngcontent-%COMP%]{margin-bottom:35px;padding-bottom:35px;border-bottom:1px solid rgba(0,0,0,.06)}.details-section[_ngcontent-%COMP%]:last-child{margin-bottom:0;padding-bottom:0;border-bottom:none}.section-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;margin-bottom:25px}.section-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:var(--primary-color);font-size:20px}.details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#333;margin:0;font-size:20px;font-weight:600;letter-spacing:.3px}.class-details-content[_ngcontent-%COMP%]{background-color:#f9f9f9;border-radius:12px;padding:20px;border:1px solid rgba(0,0,0,.04)}.class-name[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#333;margin-bottom:5px}.class-type[_ngcontent-%COMP%]{font-size:15px;color:#666}.class-code[_ngcontent-%COMP%]{font-size:14px;color:#888;font-family:monospace}.baggage-cards[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:20px}.baggage-card[_ngcontent-%COMP%]{flex:1;min-width:200px;background-color:#fff;border-radius:12px;padding:20px;display:flex;align-items:center;gap:15px;box-shadow:0 4px 15px #0000000d;border:1px solid rgba(0,0,0,.04);transition:transform .3s ease,box-shadow .3s ease}.baggage-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 8px 25px #00000014}.baggage-card.checked[_ngcontent-%COMP%]{border-left:4px solid var(--primary-color)}.baggage-card.cabin[_ngcontent-%COMP%]{border-left:4px solid var(--accent-color)}.baggage-icon[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:12px;display:flex;align-items:center;justify-content:center;font-size:20px;flex-shrink:0}.baggage-card.checked[_ngcontent-%COMP%]   .baggage-icon[_ngcontent-%COMP%]{background-color:rgba(var(--primary-color-rgb),.1);color:var(--primary-color)}.baggage-card.cabin[_ngcontent-%COMP%]   .baggage-icon[_ngcontent-%COMP%]{background-color:rgba(var(--accent-color-rgb),.1);color:var(--accent-color)}.baggage-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:5px}.baggage-type[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#333}.baggage-value[_ngcontent-%COMP%]{font-size:14px;color:#666}.services-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:20px}.service-item[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;padding:20px;display:flex;align-items:flex-start;gap:15px;box-shadow:0 4px 15px #0000000d;border:1px solid rgba(0,0,0,.04);transition:transform .3s ease,box-shadow .3s ease}.service-item[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 8px 25px #00000014}.service-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:10px;background-color:rgba(var(--primary-color-rgb),.1);display:flex;align-items:center;justify-content:center;font-size:18px;color:var(--primary-color);flex-shrink:0}.service-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:5px}.service-name[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#333}.service-description[_ngcontent-%COMP%]{font-size:14px;color:#666;line-height:1.5}.booking-section[_ngcontent-%COMP%]{margin-top:40px;text-align:center;display:flex;flex-direction:column;align-items:center}.button-container[_ngcontent-%COMP%]{position:relative;display:inline-block}.particles-container[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}.particle[_ngcontent-%COMP%]{position:absolute;width:8px;height:8px;background-color:rgba(var(--primary-color-rgb),.6);border-radius:50%;opacity:0;pointer-events:none}.particle[_ngcontent-%COMP%]:nth-child(1){top:20%;left:0;animation:_ngcontent-%COMP%_particle-animation-1 3s infinite ease-in-out}.particle[_ngcontent-%COMP%]:nth-child(2){top:60%;left:0;animation:_ngcontent-%COMP%_particle-animation-2 4s infinite ease-in-out}.particle[_ngcontent-%COMP%]:nth-child(3){top:40%;right:0;animation:_ngcontent-%COMP%_particle-animation-3 3.5s infinite ease-in-out}.particle[_ngcontent-%COMP%]:nth-child(4){top:80%;right:0;animation:_ngcontent-%COMP%_particle-animation-4 4.5s infinite ease-in-out}.particle[_ngcontent-%COMP%]:nth-child(5){top:10%;left:50%;animation:_ngcontent-%COMP%_particle-animation-5 5s infinite ease-in-out}.particle[_ngcontent-%COMP%]:nth-child(6){top:90%;left:50%;animation:_ngcontent-%COMP%_particle-animation-6 5.5s infinite ease-in-out}@keyframes _ngcontent-%COMP%_particle-animation-1{0%,to{opacity:0;transform:translate(0)}20%,80%{opacity:.8;transform:translate(-20px,-10px)}}@keyframes _ngcontent-%COMP%_particle-animation-2{0%,to{opacity:0;transform:translate(0)}20%,80%{opacity:.6;transform:translate(-15px,10px)}}@keyframes _ngcontent-%COMP%_particle-animation-3{0%,to{opacity:0;transform:translate(0)}20%,80%{opacity:.8;transform:translate(20px,-10px)}}@keyframes _ngcontent-%COMP%_particle-animation-4{0%,to{opacity:0;transform:translate(0)}20%,80%{opacity:.6;transform:translate(15px,10px)}}@keyframes _ngcontent-%COMP%_particle-animation-5{0%,to{opacity:0;transform:translate(0)}20%,80%{opacity:.7;transform:translateY(-20px)}}@keyframes _ngcontent-%COMP%_particle-animation-6{0%,to{opacity:0;transform:translate(0)}20%,80%{opacity:.7;transform:translateY(20px)}}.book-flight-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-color),var(--primary-dark));color:#fff;border:none;padding:16px 48px;font-size:18px;font-weight:600;border-radius:50px;cursor:pointer;display:flex;align-items:center;gap:12px;transition:all .3s ease;box-shadow:0 8px 25px rgba(var(--primary-color-rgb),.3);position:relative;overflow:hidden;animation:_ngcontent-%COMP%_button-float 3s ease-in-out infinite alternate}@keyframes _ngcontent-%COMP%_button-float{0%{transform:translateY(0)}to{transform:translateY(-8px)}}.book-flight-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .7s ease}.book-flight-button[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle,rgba(var(--primary-color-rgb),.1) 0%,transparent 70%);opacity:0;transition:opacity .5s ease;pointer-events:none}.book-flight-button[_ngcontent-%COMP%]:hover{transform:translateY(-5px) scale(1.05);box-shadow:0 15px 35px rgba(var(--primary-color-rgb),.5);animation:none}.book-flight-button[_ngcontent-%COMP%]:hover:before{left:100%}.book-flight-button[_ngcontent-%COMP%]:hover:after{opacity:1;animation:_ngcontent-%COMP%_glow 1.5s infinite alternate}@keyframes _ngcontent-%COMP%_glow{0%{opacity:.3}to{opacity:.6}}.book-flight-button[_ngcontent-%COMP%]:active{transform:translateY(-2px) scale(.98)}.book-flight-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px;animation:_ngcontent-%COMP%_icon-pulse 1.5s infinite alternate}@keyframes _ngcontent-%COMP%_icon-pulse{0%{transform:scale(1)}to{transform:scale(1.2)}}.no-results[_ngcontent-%COMP%]{padding:40px;text-align:center;background-color:#fff;border-radius:8px;box-shadow:0 2px 10px #0000001a}.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:16px;margin:0}\"]\n      });\n    }\n  }\n  return GetOfferComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}