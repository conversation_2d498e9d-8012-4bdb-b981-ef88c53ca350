{"ast": null, "code": "import { of } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { FlightClassType, LocationType, ProductType } from '../models/enums.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ProductService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8080/product';\n    // Données statiques pour les emplacements par type (à remplacer par un appel API réel)\n    this.countryLocations = [{\n      id: 'FR',\n      name: 'France',\n      type: 1,\n      code: 'FR',\n      country: 'France'\n    }, {\n      id: 'UK',\n      name: 'United Kingdom',\n      type: 1,\n      code: 'UK',\n      country: 'United Kingdom'\n    }, {\n      id: 'US',\n      name: 'United States',\n      type: 1,\n      code: 'US',\n      country: 'United States'\n    }, {\n      id: 'ES',\n      name: 'Spain',\n      type: 1,\n      code: 'ES',\n      country: 'Spain'\n    }, {\n      id: 'IT',\n      name: 'Italy',\n      type: 1,\n      code: 'IT',\n      country: 'Italy'\n    }, {\n      id: 'DE',\n      name: 'Germany',\n      type: 1,\n      code: 'DE',\n      country: 'Germany'\n    }, {\n      id: 'TR',\n      name: 'Turkey',\n      type: 1,\n      code: 'TR',\n      country: 'Turkey'\n    }, {\n      id: 'AE',\n      name: 'United Arab Emirates',\n      type: 1,\n      code: 'AE',\n      country: 'United Arab Emirates'\n    }, {\n      id: 'AU',\n      name: 'Australia',\n      type: 1,\n      code: 'AU',\n      country: 'Australia'\n    }, {\n      id: 'JP',\n      name: 'Japan',\n      type: 1,\n      code: 'JP',\n      country: 'Japan'\n    }, {\n      id: 'TN',\n      name: 'Tunisia',\n      type: 1,\n      code: 'TN',\n      country: 'Tunisia'\n    }, {\n      id: 'MA',\n      name: 'Morocco',\n      type: 1,\n      code: 'MA',\n      country: 'Morocco'\n    }, {\n      id: 'EG',\n      name: 'Egypt',\n      type: 1,\n      code: 'EG',\n      country: 'Egypt'\n    }, {\n      id: 'CA',\n      name: 'Canada',\n      type: 1,\n      code: 'CA',\n      country: 'Canada'\n    }, {\n      id: 'MX',\n      name: 'Mexico',\n      type: 1,\n      code: 'MX',\n      country: 'Mexico'\n    }];\n    this.cityLocations = [{\n      id: 'PAR',\n      name: 'Paris',\n      type: 2,\n      code: 'PAR',\n      country: 'France'\n    }, {\n      id: 'LON',\n      name: 'London',\n      type: 2,\n      code: 'LON',\n      country: 'United Kingdom'\n    }, {\n      id: 'NYC',\n      name: 'New York',\n      type: 2,\n      code: 'NYC',\n      country: 'United States'\n    }, {\n      id: 'MAD',\n      name: 'Madrid',\n      type: 2,\n      code: 'MAD',\n      country: 'Spain'\n    }, {\n      id: 'BCN',\n      name: 'Barcelona',\n      type: 2,\n      code: 'BCN',\n      country: 'Spain'\n    }, {\n      id: 'ROM',\n      name: 'Rome',\n      type: 2,\n      code: 'ROM',\n      country: 'Italy'\n    }, {\n      id: 'BER',\n      name: 'Berlin',\n      type: 2,\n      code: 'BER',\n      country: 'Germany'\n    }, {\n      id: 'IST',\n      name: 'Istanbul',\n      type: 2,\n      code: 'IST',\n      country: 'Turkey'\n    }, {\n      id: 'DXB',\n      name: 'Dubai',\n      type: 2,\n      code: 'DXB',\n      country: 'United Arab Emirates'\n    }, {\n      id: 'SYD',\n      name: 'Sydney',\n      type: 2,\n      code: 'SYD',\n      country: 'Australia'\n    }, {\n      id: 'TYO',\n      name: 'Tokyo',\n      type: 2,\n      code: 'TYO',\n      country: 'Japan'\n    }, {\n      id: 'TUN',\n      name: 'Tunis',\n      type: 2,\n      code: 'TUN',\n      country: 'Tunisia'\n    }, {\n      id: 'CAS',\n      name: 'Casablanca',\n      type: 2,\n      code: 'CAS',\n      country: 'Morocco'\n    }, {\n      id: 'CAI',\n      name: 'Cairo',\n      type: 2,\n      code: 'CAI',\n      country: 'Egypt'\n    }, {\n      id: 'YTO',\n      name: 'Toronto',\n      type: 2,\n      code: 'YTO',\n      country: 'Canada'\n    }, {\n      id: 'MEX',\n      name: 'Mexico City',\n      type: 2,\n      code: 'MEX',\n      country: 'Mexico'\n    }];\n    this.townLocations = [{\n      id: 'NIC',\n      name: 'Nice',\n      type: 3,\n      code: 'NIC',\n      country: 'France'\n    }, {\n      id: 'MAN',\n      name: 'Manchester',\n      type: 3,\n      code: 'MAN',\n      country: 'United Kingdom'\n    }, {\n      id: 'BOS',\n      name: 'Boston',\n      type: 3,\n      code: 'BOS',\n      country: 'United States'\n    }, {\n      id: 'VAL',\n      name: 'Valencia',\n      type: 3,\n      code: 'VAL',\n      country: 'Spain'\n    }, {\n      id: 'NAP',\n      name: 'Naples',\n      type: 3,\n      code: 'NAP',\n      country: 'Italy'\n    }, {\n      id: 'MUC',\n      name: 'Munich',\n      type: 3,\n      code: 'MUC',\n      country: 'Germany'\n    }, {\n      id: 'ANK',\n      name: 'Ankara',\n      type: 3,\n      code: 'ANK',\n      country: 'Turkey'\n    }, {\n      id: 'SHJ',\n      name: 'Sharjah',\n      type: 3,\n      code: 'SHJ',\n      country: 'United Arab Emirates'\n    }, {\n      id: 'MEL',\n      name: 'Melbourne',\n      type: 3,\n      code: 'MEL',\n      country: 'Australia'\n    }, {\n      id: 'OSA',\n      name: 'Osaka',\n      type: 3,\n      code: 'OSA',\n      country: 'Japan'\n    }];\n    this.villageLocations = [{\n      id: 'CDV',\n      name: 'Courchevel',\n      type: 4,\n      code: 'CDV',\n      country: 'France'\n    }, {\n      id: 'OXF',\n      name: 'Oxford',\n      type: 4,\n      code: 'OXF',\n      country: 'United Kingdom'\n    }, {\n      id: 'ASP',\n      name: 'Aspen',\n      type: 4,\n      code: 'ASP',\n      country: 'United States'\n    }, {\n      id: 'IBZ',\n      name: 'Ibiza',\n      type: 4,\n      code: 'IBZ',\n      country: 'Spain'\n    }, {\n      id: 'PSA',\n      name: 'Pisa',\n      type: 4,\n      code: 'PSA',\n      country: 'Italy'\n    }, {\n      id: 'BAD',\n      name: 'Baden-Baden',\n      type: 4,\n      code: 'BAD',\n      country: 'Germany'\n    }, {\n      id: 'BOD',\n      name: 'Bodrum',\n      type: 4,\n      code: 'BOD',\n      country: 'Turkey'\n    }, {\n      id: 'FUJ',\n      name: 'Fujairah',\n      type: 4,\n      code: 'FUJ',\n      country: 'United Arab Emirates'\n    }, {\n      id: 'BNK',\n      name: 'Ballina',\n      type: 4,\n      code: 'BNK',\n      country: 'Australia'\n    }, {\n      id: 'MYJ',\n      name: 'Matsuyama',\n      type: 4,\n      code: 'MYJ',\n      country: 'Japan'\n    }];\n    this.airportLocations = [{\n      id: 'CDG',\n      name: 'Charles de Gaulle Airport',\n      type: 5,\n      code: 'CDG',\n      country: 'France',\n      city: 'Paris'\n    }, {\n      id: 'ORY',\n      name: 'Orly Airport',\n      type: 5,\n      code: 'ORY',\n      country: 'France',\n      city: 'Paris'\n    }, {\n      id: 'LHR',\n      name: 'Heathrow Airport',\n      type: 5,\n      code: 'LHR',\n      country: 'United Kingdom',\n      city: 'London'\n    }, {\n      id: 'LGW',\n      name: 'Gatwick Airport',\n      type: 5,\n      code: 'LGW',\n      country: 'United Kingdom',\n      city: 'London'\n    }, {\n      id: 'JFK',\n      name: 'John F. Kennedy Airport',\n      type: 5,\n      code: 'JFK',\n      country: 'United States',\n      city: 'New York'\n    }, {\n      id: 'LGA',\n      name: 'LaGuardia Airport',\n      type: 5,\n      code: 'LGA',\n      country: 'United States',\n      city: 'New York'\n    }, {\n      id: 'FCO',\n      name: 'Leonardo da Vinci Airport',\n      type: 5,\n      code: 'FCO',\n      country: 'Italy',\n      city: 'Rome'\n    }, {\n      id: 'TXL',\n      name: 'Tegel Airport',\n      type: 5,\n      code: 'TXL',\n      country: 'Germany',\n      city: 'Berlin'\n    }, {\n      id: 'SAW',\n      name: 'Sabiha Gökçen Airport',\n      type: 5,\n      code: 'SAW',\n      country: 'Turkey',\n      city: 'Istanbul'\n    }, {\n      id: 'DXB',\n      name: 'Dubai International Airport',\n      type: 5,\n      code: 'DXB',\n      country: 'United Arab Emirates',\n      city: 'Dubai'\n    }, {\n      id: 'SYD',\n      name: 'Sydney Airport',\n      type: 5,\n      code: 'SYD',\n      country: 'Australia',\n      city: 'Sydney'\n    }, {\n      id: 'HND',\n      name: 'Haneda Airport',\n      type: 5,\n      code: 'HND',\n      country: 'Japan',\n      city: 'Tokyo'\n    }, {\n      id: 'NRT',\n      name: 'Narita Airport',\n      type: 5,\n      code: 'NRT',\n      country: 'Japan',\n      city: 'Tokyo'\n    }, {\n      id: 'TUN',\n      name: 'Tunis Carthage Airport',\n      type: 5,\n      code: 'TUN',\n      country: 'Tunisia',\n      city: 'Tunis'\n    }, {\n      id: 'CMN',\n      name: 'Mohammed V Airport',\n      type: 5,\n      code: 'CMN',\n      country: 'Morocco',\n      city: 'Casablanca'\n    }, {\n      id: 'CAI',\n      name: 'Cairo International Airport',\n      type: 5,\n      code: 'CAI',\n      country: 'Egypt',\n      city: 'Cairo'\n    }, {\n      id: 'YYZ',\n      name: 'Toronto Pearson Airport',\n      type: 5,\n      code: 'YYZ',\n      country: 'Canada',\n      city: 'Toronto'\n    }, {\n      id: 'MEX',\n      name: 'Mexico City International Airport',\n      type: 5,\n      code: 'MEX',\n      country: 'Mexico',\n      city: 'Mexico City'\n    }];\n    // Toutes les locations combinées\n    this.locations = [];\n    // Initialiser la liste combinée\n    this.locations = [...this.countryLocations, ...this.cityLocations, ...this.townLocations, ...this.villageLocations, ...this.airportLocations];\n  }\n  searchPrice(request) {\n    return this.http.post(`${this.apiUrl}/pricesearch`, request).pipe(catchError(error => {\n      console.error('Error searching prices:', error);\n      throw error;\n    }));\n  }\n  getOffers(request) {\n    return this.http.post(`${this.apiUrl}/getoffers`, request).pipe(catchError(error => {\n      console.error('Error getting offers:', error);\n      throw error;\n    }));\n  }\n  getLocations(query) {\n    // Dans une application réelle, cela devrait être un appel API\n    // Pour l'instant, nous filtrons les données statiques\n    return of(this.locations.filter(location => location.name.toLowerCase().includes(query.toLowerCase()) || location.code && location.code.toLowerCase().includes(query.toLowerCase())));\n  }\n  // Méthode pour obtenir toutes les locations disponibles\n  getAllLocations() {\n    // Dans une application réelle, cela devrait être un appel API\n    // Pour l'instant, nous retournons toutes les données statiques\n    return of(this.locations);\n  }\n  // Méthode pour obtenir les locations par type\n  getLocationsByType(locationType) {\n    switch (locationType) {\n      case 1:\n        // Country\n        return of(this.countryLocations);\n      case 2:\n        // City\n        return of(this.cityLocations);\n      case 3:\n        // Town\n        return of(this.townLocations);\n      case 4:\n        // Village\n        return of(this.villageLocations);\n      case 5:\n        // Airport\n        return of(this.airportLocations);\n      default:\n        return of(this.locations);\n    }\n  }\n  // Méthode utilitaire pour créer une requête de recherche de prix par défaut\n  createDefaultPriceSearchRequest(departureLocationId, arrivalLocationId, departureDate, passengerCount = 1) {\n    return {\n      ProductType: ProductType.Flight,\n      ServiceTypes: [\"Flight\"],\n      CheckIn: departureDate,\n      DepartureLocations: [{\n        id: departureLocationId,\n        type: LocationType.Airport\n      }],\n      ArrivalLocations: [{\n        id: arrivalLocationId,\n        type: LocationType.Airport\n      }],\n      Passengers: [{\n        type: 1,\n        count: passengerCount\n      }],\n      showOnlyNonStopFlight: false,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: 0 // All\n        }\n      },\n\n      acceptPendingProviders: true,\n      forceFlightBundlePackage: false,\n      disablePackageOfferTotalPrice: false,\n      calculateFlightFees: true,\n      flightClasses: [FlightClassType.ECONOMY],\n      Culture: \"fr-FR\",\n      Currency: \"EUR\"\n    };\n  }\n  // Méthode utilitaire pour créer une requête GetOffers par défaut\n  createDefaultGetOffersRequest(searchId, offerIds, productType = 3,\n  // Flight par défaut\n  currency = 'EUR', culture = 'en-US') {\n    return {\n      productType: productType,\n      searchId: searchId,\n      offerIds: offerIds,\n      currency: currency,\n      culture: culture\n    };\n  }\n  static {\n    this.ɵfac = function ProductService_Factory(t) {\n      return new (t || ProductService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProductService,\n      factory: ProductService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "catchError", "FlightClassType", "LocationType", "ProductType", "ProductService", "constructor", "http", "apiUrl", "countryLocations", "id", "name", "type", "code", "country", "cityLocations", "townLocations", "villageLocations", "airportLocations", "city", "locations", "searchPrice", "request", "post", "pipe", "error", "console", "getOffers", "getLocations", "query", "filter", "location", "toLowerCase", "includes", "getAllLocations", "getLocationsByType", "locationType", "createDefaultPriceSearchRequest", "departureLocationId", "arrivalLocationId", "departureDate", "passengerCount", "Flight", "ServiceTypes", "CheckIn", "DepartureLocations", "Airport", "ArrivalLocations", "Passengers", "count", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "flightBaggageGetOption", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightClasses", "ECONOMY", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "createDefaultGetOffersRequest", "searchId", "offerIds", "productType", "currency", "culture", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\services\\product.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, of } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { LocationOption, PriceSearchRequest } from '../models/price-search-request.model';\nimport { PriceSearchResponse } from '../models/price-search-response.model';\nimport { FlightClassType, LocationType, ProductType } from '../models/enums.model';\nimport { GetOffersRequest } from '../models/get-offers-request.model';\nimport { GetOffersResponse } from '../models/get-offers-response.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ProductService {\n  private apiUrl = 'http://localhost:8080/product';\n\n  // Données statiques pour les emplacements par type (à remplacer par un appel API réel)\n  private countryLocations: LocationOption[] = [\n    { id: 'FR', name: 'France', type: 1, code: 'FR', country: 'France' },\n    { id: 'UK', name: 'United Kingdom', type: 1, code: 'UK', country: 'United Kingdom' },\n    { id: 'US', name: 'United States', type: 1, code: 'US', country: 'United States' },\n    { id: 'ES', name: 'Spain', type: 1, code: 'ES', country: 'Spain' },\n    { id: 'IT', name: 'Italy', type: 1, code: 'IT', country: 'Italy' },\n    { id: 'DE', name: 'Germany', type: 1, code: 'DE', country: 'Germany' },\n    { id: 'TR', name: 'Turkey', type: 1, code: 'TR', country: 'Turkey' },\n    { id: 'AE', name: 'United Arab Emirates', type: 1, code: 'AE', country: 'United Arab Emirates' },\n    { id: 'AU', name: 'Australia', type: 1, code: 'AU', country: 'Australia' },\n    { id: 'JP', name: 'Japan', type: 1, code: 'JP', country: 'Japan' },\n    { id: 'TN', name: 'Tunisia', type: 1, code: 'TN', country: 'Tunisia' },\n    { id: 'MA', name: 'Morocco', type: 1, code: 'MA', country: 'Morocco' },\n    { id: 'EG', name: 'Egypt', type: 1, code: 'EG', country: 'Egypt' },\n    { id: 'CA', name: 'Canada', type: 1, code: 'CA', country: 'Canada' },\n    { id: 'MX', name: 'Mexico', type: 1, code: 'MX', country: 'Mexico' }\n  ];\n\n  private cityLocations: LocationOption[] = [\n    { id: 'PAR', name: 'Paris', type: 2, code: 'PAR', country: 'France' },\n    { id: 'LON', name: 'London', type: 2, code: 'LON', country: 'United Kingdom' },\n    { id: 'NYC', name: 'New York', type: 2, code: 'NYC', country: 'United States' },\n    { id: 'MAD', name: 'Madrid', type: 2, code: 'MAD', country: 'Spain' },\n    { id: 'BCN', name: 'Barcelona', type: 2, code: 'BCN', country: 'Spain' },\n    { id: 'ROM', name: 'Rome', type: 2, code: 'ROM', country: 'Italy' },\n    { id: 'BER', name: 'Berlin', type: 2, code: 'BER', country: 'Germany' },\n    { id: 'IST', name: 'Istanbul', type: 2, code: 'IST', country: 'Turkey' },\n    { id: 'DXB', name: 'Dubai', type: 2, code: 'DXB', country: 'United Arab Emirates' },\n    { id: 'SYD', name: 'Sydney', type: 2, code: 'SYD', country: 'Australia' },\n    { id: 'TYO', name: 'Tokyo', type: 2, code: 'TYO', country: 'Japan' },\n    { id: 'TUN', name: 'Tunis', type: 2, code: 'TUN', country: 'Tunisia' },\n    { id: 'CAS', name: 'Casablanca', type: 2, code: 'CAS', country: 'Morocco' },\n    { id: 'CAI', name: 'Cairo', type: 2, code: 'CAI', country: 'Egypt' },\n    { id: 'YTO', name: 'Toronto', type: 2, code: 'YTO', country: 'Canada' },\n    { id: 'MEX', name: 'Mexico City', type: 2, code: 'MEX', country: 'Mexico' }\n  ];\n\n  private townLocations: LocationOption[] = [\n    { id: 'NIC', name: 'Nice', type: 3, code: 'NIC', country: 'France' },\n    { id: 'MAN', name: 'Manchester', type: 3, code: 'MAN', country: 'United Kingdom' },\n    { id: 'BOS', name: 'Boston', type: 3, code: 'BOS', country: 'United States' },\n    { id: 'VAL', name: 'Valencia', type: 3, code: 'VAL', country: 'Spain' },\n    { id: 'NAP', name: 'Naples', type: 3, code: 'NAP', country: 'Italy' },\n    { id: 'MUC', name: 'Munich', type: 3, code: 'MUC', country: 'Germany' },\n    { id: 'ANK', name: 'Ankara', type: 3, code: 'ANK', country: 'Turkey' },\n    { id: 'SHJ', name: 'Sharjah', type: 3, code: 'SHJ', country: 'United Arab Emirates' },\n    { id: 'MEL', name: 'Melbourne', type: 3, code: 'MEL', country: 'Australia' },\n    { id: 'OSA', name: 'Osaka', type: 3, code: 'OSA', country: 'Japan' }\n  ];\n\n  private villageLocations: LocationOption[] = [\n    { id: 'CDV', name: 'Courchevel', type: 4, code: 'CDV', country: 'France' },\n    { id: 'OXF', name: 'Oxford', type: 4, code: 'OXF', country: 'United Kingdom' },\n    { id: 'ASP', name: 'Aspen', type: 4, code: 'ASP', country: 'United States' },\n    { id: 'IBZ', name: 'Ibiza', type: 4, code: 'IBZ', country: 'Spain' },\n    { id: 'PSA', name: 'Pisa', type: 4, code: 'PSA', country: 'Italy' },\n    { id: 'BAD', name: 'Baden-Baden', type: 4, code: 'BAD', country: 'Germany' },\n    { id: 'BOD', name: 'Bodrum', type: 4, code: 'BOD', country: 'Turkey' },\n    { id: 'FUJ', name: 'Fujairah', type: 4, code: 'FUJ', country: 'United Arab Emirates' },\n    { id: 'BNK', name: 'Ballina', type: 4, code: 'BNK', country: 'Australia' },\n    { id: 'MYJ', name: 'Matsuyama', type: 4, code: 'MYJ', country: 'Japan' }\n  ];\n\n  private airportLocations: LocationOption[] = [\n    { id: 'CDG', name: 'Charles de Gaulle Airport', type: 5, code: 'CDG', country: 'France', city: 'Paris' },\n    { id: 'ORY', name: 'Orly Airport', type: 5, code: 'ORY', country: 'France', city: 'Paris' },\n    { id: 'LHR', name: 'Heathrow Airport', type: 5, code: 'LHR', country: 'United Kingdom', city: 'London' },\n    { id: 'LGW', name: 'Gatwick Airport', type: 5, code: 'LGW', country: 'United Kingdom', city: 'London' },\n    { id: 'JFK', name: 'John F. Kennedy Airport', type: 5, code: 'JFK', country: 'United States', city: 'New York' },\n    { id: 'LGA', name: 'LaGuardia Airport', type: 5, code: 'LGA', country: 'United States', city: 'New York' },\n    { id: 'FCO', name: 'Leonardo da Vinci Airport', type: 5, code: 'FCO', country: 'Italy', city: 'Rome' },\n    { id: 'TXL', name: 'Tegel Airport', type: 5, code: 'TXL', country: 'Germany', city: 'Berlin' },\n    { id: 'SAW', name: 'Sabiha Gökçen Airport', type: 5, code: 'SAW', country: 'Turkey', city: 'Istanbul' },\n    { id: 'DXB', name: 'Dubai International Airport', type: 5, code: 'DXB', country: 'United Arab Emirates', city: 'Dubai' },\n    { id: 'SYD', name: 'Sydney Airport', type: 5, code: 'SYD', country: 'Australia', city: 'Sydney' },\n    { id: 'HND', name: 'Haneda Airport', type: 5, code: 'HND', country: 'Japan', city: 'Tokyo' },\n    { id: 'NRT', name: 'Narita Airport', type: 5, code: 'NRT', country: 'Japan', city: 'Tokyo' },\n    { id: 'TUN', name: 'Tunis Carthage Airport', type: 5, code: 'TUN', country: 'Tunisia', city: 'Tunis' },\n    { id: 'CMN', name: 'Mohammed V Airport', type: 5, code: 'CMN', country: 'Morocco', city: 'Casablanca' },\n    { id: 'CAI', name: 'Cairo International Airport', type: 5, code: 'CAI', country: 'Egypt', city: 'Cairo' },\n    { id: 'YYZ', name: 'Toronto Pearson Airport', type: 5, code: 'YYZ', country: 'Canada', city: 'Toronto' },\n    { id: 'MEX', name: 'Mexico City International Airport', type: 5, code: 'MEX', country: 'Mexico', city: 'Mexico City' }\n  ];\n\n  // Toutes les locations combinées\n  private locations: LocationOption[] = [];\n\n  constructor(private http: HttpClient) {\n    // Initialiser la liste combinée\n    this.locations = [\n      ...this.countryLocations,\n      ...this.cityLocations,\n      ...this.townLocations,\n      ...this.villageLocations,\n      ...this.airportLocations\n    ];\n  }\n\n  searchPrice(request: PriceSearchRequest): Observable<PriceSearchResponse> {\n    return this.http.post<PriceSearchResponse>(`${this.apiUrl}/pricesearch`, request)\n      .pipe(\n        catchError(error => {\n          console.error('Error searching prices:', error);\n          throw error;\n        })\n      );\n  }\n\n  getOffers(request: GetOffersRequest): Observable<GetOffersResponse> {\n    return this.http.post<GetOffersResponse>(`${this.apiUrl}/getoffers`, request)\n      .pipe(\n        catchError(error => {\n          console.error('Error getting offers:', error);\n          throw error;\n        })\n      );\n  }\n\n  getLocations(query: string): Observable<LocationOption[]> {\n    // Dans une application réelle, cela devrait être un appel API\n    // Pour l'instant, nous filtrons les données statiques\n    return of(this.locations.filter(location =>\n      location.name.toLowerCase().includes(query.toLowerCase()) ||\n      (location.code && location.code.toLowerCase().includes(query.toLowerCase()))\n    ));\n  }\n\n  // Méthode pour obtenir toutes les locations disponibles\n  getAllLocations(): Observable<LocationOption[]> {\n    // Dans une application réelle, cela devrait être un appel API\n    // Pour l'instant, nous retournons toutes les données statiques\n    return of(this.locations);\n  }\n\n  // Méthode pour obtenir les locations par type\n  getLocationsByType(locationType: number): Observable<LocationOption[]> {\n    switch(locationType) {\n      case 1: // Country\n        return of(this.countryLocations);\n      case 2: // City\n        return of(this.cityLocations);\n      case 3: // Town\n        return of(this.townLocations);\n      case 4: // Village\n        return of(this.villageLocations);\n      case 5: // Airport\n        return of(this.airportLocations);\n      default:\n        return of(this.locations);\n    }\n  }\n\n  // Méthode utilitaire pour créer une requête de recherche de prix par défaut\n  createDefaultPriceSearchRequest(\n    departureLocationId: string,\n    arrivalLocationId: string,\n    departureDate: string,\n    passengerCount: number = 1\n  ): PriceSearchRequest {\n    return {\n      ProductType: ProductType.Flight,\n      ServiceTypes: [\"Flight\"],\n      CheckIn: departureDate,\n      DepartureLocations: [\n        {\n          id: departureLocationId,\n          type: LocationType.Airport\n        }\n      ],\n      ArrivalLocations: [\n        {\n          id: arrivalLocationId,\n          type: LocationType.Airport\n        }\n      ],\n      Passengers: [\n        {\n          type: 1, // Adult\n          count: passengerCount\n        }\n      ],\n      showOnlyNonStopFlight: false,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: 0 // All\n        }\n      },\n      acceptPendingProviders: true,\n      forceFlightBundlePackage: false,\n      disablePackageOfferTotalPrice: false,\n      calculateFlightFees: true,\n      flightClasses: [FlightClassType.ECONOMY],\n      Culture: \"fr-FR\",\n      Currency: \"EUR\"\n    };\n  }\n\n  // Méthode utilitaire pour créer une requête GetOffers par défaut\n  createDefaultGetOffersRequest(\n    searchId: string,\n    offerIds: string[],\n    productType: number = 3, // Flight par défaut\n    currency: string = 'EUR',\n    culture: string = 'en-US'\n  ): GetOffersRequest {\n    return {\n      productType: productType,\n      searchId: searchId,\n      offerIds: offerIds,\n      currency: currency,\n      culture: culture\n    };\n  }\n}\n"], "mappings": "AAEA,SAAqBA,EAAE,QAAQ,MAAM;AACrC,SAASC,UAAU,QAAa,gBAAgB;AAGhD,SAASC,eAAe,EAAEC,YAAY,EAAEC,WAAW,QAAQ,uBAAuB;;;AAOlF,OAAM,MAAOC,cAAc;EA2FzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IA1FhB,KAAAC,MAAM,GAAG,+BAA+B;IAEhD;IACQ,KAAAC,gBAAgB,GAAqB,CAC3C;MAAEC,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAQ,CAAE,EACpE;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAgB,CAAE,EACpF;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAe,CAAE,EAClF;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAO,CAAE,EAClE;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAO,CAAE,EAClE;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAS,CAAE,EACtE;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAQ,CAAE,EACpE;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,sBAAsB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAsB,CAAE,EAChG;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAW,CAAE,EAC1E;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAO,CAAE,EAClE;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAS,CAAE,EACtE;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAS,CAAE,EACtE;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAO,CAAE,EAClE;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAQ,CAAE,EACpE;MAAEJ,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAQ,CAAE,CACrE;IAEO,KAAAC,aAAa,GAAqB,CACxC;MAAEL,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAQ,CAAE,EACrE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAE,EAC9E;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAe,CAAE,EAC/E;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAO,CAAE,EACrE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAO,CAAE,EACxE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAO,CAAE,EACnE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAS,CAAE,EACvE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAQ,CAAE,EACxE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAsB,CAAE,EACnF;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAW,CAAE,EACzE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAO,CAAE,EACpE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAS,CAAE,EACtE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAS,CAAE,EAC3E;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAO,CAAE,EACpE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAQ,CAAE,EACvE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,aAAa;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAQ,CAAE,CAC5E;IAEO,KAAAE,aAAa,GAAqB,CACxC;MAAEN,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAQ,CAAE,EACpE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAE,EAClF;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAe,CAAE,EAC7E;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAO,CAAE,EACvE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAO,CAAE,EACrE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAS,CAAE,EACvE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAQ,CAAE,EACtE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAsB,CAAE,EACrF;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAW,CAAE,EAC5E;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAO,CAAE,CACrE;IAEO,KAAAG,gBAAgB,GAAqB,CAC3C;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAQ,CAAE,EAC1E;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAE,EAC9E;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAe,CAAE,EAC5E;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAO,CAAE,EACpE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAO,CAAE,EACnE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,aAAa;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAS,CAAE,EAC5E;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAQ,CAAE,EACtE;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAsB,CAAE,EACtF;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAW,CAAE,EAC1E;MAAEJ,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAO,CAAE,CACzE;IAEO,KAAAI,gBAAgB,GAAqB,CAC3C;MAAER,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,2BAA2B;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,QAAQ;MAAEK,IAAI,EAAE;IAAO,CAAE,EACxG;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,QAAQ;MAAEK,IAAI,EAAE;IAAO,CAAE,EAC3F;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,kBAAkB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,gBAAgB;MAAEK,IAAI,EAAE;IAAQ,CAAE,EACxG;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,iBAAiB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,gBAAgB;MAAEK,IAAI,EAAE;IAAQ,CAAE,EACvG;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,yBAAyB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,eAAe;MAAEK,IAAI,EAAE;IAAU,CAAE,EAChH;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,mBAAmB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,eAAe;MAAEK,IAAI,EAAE;IAAU,CAAE,EAC1G;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,2BAA2B;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,OAAO;MAAEK,IAAI,EAAE;IAAM,CAAE,EACtG;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,SAAS;MAAEK,IAAI,EAAE;IAAQ,CAAE,EAC9F;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,uBAAuB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,QAAQ;MAAEK,IAAI,EAAE;IAAU,CAAE,EACvG;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,6BAA6B;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,sBAAsB;MAAEK,IAAI,EAAE;IAAO,CAAE,EACxH;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,WAAW;MAAEK,IAAI,EAAE;IAAQ,CAAE,EACjG;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,OAAO;MAAEK,IAAI,EAAE;IAAO,CAAE,EAC5F;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,OAAO;MAAEK,IAAI,EAAE;IAAO,CAAE,EAC5F;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,wBAAwB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,SAAS;MAAEK,IAAI,EAAE;IAAO,CAAE,EACtG;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,oBAAoB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,SAAS;MAAEK,IAAI,EAAE;IAAY,CAAE,EACvG;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,6BAA6B;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,OAAO;MAAEK,IAAI,EAAE;IAAO,CAAE,EACzG;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,yBAAyB;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,QAAQ;MAAEK,IAAI,EAAE;IAAS,CAAE,EACxG;MAAET,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,mCAAmC;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,QAAQ;MAAEK,IAAI,EAAE;IAAa,CAAE,CACvH;IAED;IACQ,KAAAC,SAAS,GAAqB,EAAE;IAGtC;IACA,IAAI,CAACA,SAAS,GAAG,CACf,GAAG,IAAI,CAACX,gBAAgB,EACxB,GAAG,IAAI,CAACM,aAAa,EACrB,GAAG,IAAI,CAACC,aAAa,EACrB,GAAG,IAAI,CAACC,gBAAgB,EACxB,GAAG,IAAI,CAACC,gBAAgB,CACzB;EACH;EAEAG,WAAWA,CAACC,OAA2B;IACrC,OAAO,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAsB,GAAG,IAAI,CAACf,MAAM,cAAc,EAAEc,OAAO,CAAC,CAC9EE,IAAI,CACHvB,UAAU,CAACwB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEAE,SAASA,CAACL,OAAyB;IACjC,OAAO,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAoB,GAAG,IAAI,CAACf,MAAM,YAAY,EAAEc,OAAO,CAAC,CAC1EE,IAAI,CACHvB,UAAU,CAACwB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEAG,YAAYA,CAACC,KAAa;IACxB;IACA;IACA,OAAO7B,EAAE,CAAC,IAAI,CAACoB,SAAS,CAACU,MAAM,CAACC,QAAQ,IACtCA,QAAQ,CAACpB,IAAI,CAACqB,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAAClB,IAAI,IAAIkB,QAAQ,CAAClB,IAAI,CAACmB,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAE,CAC7E,CAAC;EACJ;EAEA;EACAE,eAAeA,CAAA;IACb;IACA;IACA,OAAOlC,EAAE,CAAC,IAAI,CAACoB,SAAS,CAAC;EAC3B;EAEA;EACAe,kBAAkBA,CAACC,YAAoB;IACrC,QAAOA,YAAY;MACjB,KAAK,CAAC;QAAE;QACN,OAAOpC,EAAE,CAAC,IAAI,CAACS,gBAAgB,CAAC;MAClC,KAAK,CAAC;QAAE;QACN,OAAOT,EAAE,CAAC,IAAI,CAACe,aAAa,CAAC;MAC/B,KAAK,CAAC;QAAE;QACN,OAAOf,EAAE,CAAC,IAAI,CAACgB,aAAa,CAAC;MAC/B,KAAK,CAAC;QAAE;QACN,OAAOhB,EAAE,CAAC,IAAI,CAACiB,gBAAgB,CAAC;MAClC,KAAK,CAAC;QAAE;QACN,OAAOjB,EAAE,CAAC,IAAI,CAACkB,gBAAgB,CAAC;MAClC;QACE,OAAOlB,EAAE,CAAC,IAAI,CAACoB,SAAS,CAAC;;EAE/B;EAEA;EACAiB,+BAA+BA,CAC7BC,mBAA2B,EAC3BC,iBAAyB,EACzBC,aAAqB,EACrBC,cAAA,GAAyB,CAAC;IAE1B,OAAO;MACLrC,WAAW,EAAEA,WAAW,CAACsC,MAAM;MAC/BC,YAAY,EAAE,CAAC,QAAQ,CAAC;MACxBC,OAAO,EAAEJ,aAAa;MACtBK,kBAAkB,EAAE,CAClB;QACEnC,EAAE,EAAE4B,mBAAmB;QACvB1B,IAAI,EAAET,YAAY,CAAC2C;OACpB,CACF;MACDC,gBAAgB,EAAE,CAChB;QACErC,EAAE,EAAE6B,iBAAiB;QACrB3B,IAAI,EAAET,YAAY,CAAC2C;OACpB,CACF;MACDE,UAAU,EAAE,CACV;QACEpC,IAAI,EAAE,CAAC;QACPqC,KAAK,EAAER;OACR,CACF;MACDS,qBAAqB,EAAE,KAAK;MAC5BC,oBAAoB,EAAE;QACpBC,oBAAoB,EAAE;UACpBC,sBAAsB,EAAE,CAAC,CAAC;;OAE7B;;MACDC,sBAAsB,EAAE,IAAI;MAC5BC,wBAAwB,EAAE,KAAK;MAC/BC,6BAA6B,EAAE,KAAK;MACpCC,mBAAmB,EAAE,IAAI;MACzBC,aAAa,EAAE,CAACxD,eAAe,CAACyD,OAAO,CAAC;MACxCC,OAAO,EAAE,OAAO;MAChBC,QAAQ,EAAE;KACX;EACH;EAEA;EACAC,6BAA6BA,CAC3BC,QAAgB,EAChBC,QAAkB,EAClBC,WAAA,GAAsB,CAAC;EAAE;EACzBC,QAAA,GAAmB,KAAK,EACxBC,OAAA,GAAkB,OAAO;IAEzB,OAAO;MACLF,WAAW,EAAEA,WAAW;MACxBF,QAAQ,EAAEA,QAAQ;MAClBC,QAAQ,EAAEA,QAAQ;MAClBE,QAAQ,EAAEA,QAAQ;MAClBC,OAAO,EAAEA;KACV;EACH;;;uBAxNW9D,cAAc,EAAA+D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdlE,cAAc;MAAAmE,OAAA,EAAdnE,cAAc,CAAAoE,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}