{"ast": null, "code": "import { trigger, transition, style, animate, query, stagger } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/button\";\nexport class AccueilComponent {\n  constructor(authService, router, snackBar) {\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.userName = '';\n    // Destinations populaires\n    this.destinations = [{\n      name: 'Istanbul',\n      country: 'Turquie',\n      code: 'IST',\n      image: 'istanbul.jpg'\n    }, {\n      name: 'Tunis',\n      country: 'Tunisie',\n      code: 'TUN',\n      image: 'tunis.jpg'\n    }, {\n      name: 'Paris',\n      country: 'France',\n      code: 'CDG',\n      image: 'paris.jpg'\n    }, {\n      name: 'Dubai',\n      country: 'Émirats Arabes Unis',\n      code: 'DXB',\n      image: 'dubai.jpg'\n    }];\n    // Témoignages\n    this.testimonials = [{\n      name: '<PERSON>',\n      location: 'Paris, France',\n      rating: 5,\n      text: 'Service exceptionnel! J\\'ai trouvé un vol à un prix imbattable et la réservation a été simple et rapide.'\n    }, {\n      name: 'Thomas Dubois',\n      location: 'Lyon, France',\n      rating: 4.5,\n      text: 'Interface intuitive et options de vol nombreuses. J\\'ai pu comparer facilement et choisir la meilleure option pour mon budget.'\n    }, {\n      name: 'Marie Leroy',\n      location: 'Marseille, France',\n      rating: 5,\n      text: 'Le support client est remarquable. J\\'ai eu besoin d\\'aide pour modifier ma réservation et tout a été résolu rapidement.'\n    }];\n  }\n  ngOnInit() {\n    // Animation d'entrée pour la page\n    document.body.classList.add('accueil-page-active');\n    // Récupérer les informations de l'utilisateur\n    const userInfo = this.authService.getUserInfo();\n    if (userInfo) {\n      this.userName = userInfo.name || userInfo.username || 'Utilisateur';\n    }\n  }\n  ngOnDestroy() {\n    // Nettoyage de la classe lors de la destruction du composant\n    document.body.classList.remove('accueil-page-active');\n  }\n  navigateToSearchPrice() {\n    this.router.navigate(['/search-price']);\n  }\n  showMoreInfo() {\n    this.snackBar.open('Plus d\\'informations seront disponibles prochainement!', 'Fermer', {\n      duration: 3000,\n      panelClass: 'info-snackbar',\n      verticalPosition: 'bottom'\n    });\n  }\n  // Méthode pour générer un tableau d'étoiles pour les avis\n  getStars(rating) {\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n    const stars = Array(fullStars).fill(1);\n    if (hasHalfStar) {\n      stars.push(0.5);\n    }\n    return stars;\n  }\n  static {\n    this.ɵfac = function AccueilComponent_Factory(t) {\n      return new (t || AccueilComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccueilComponent,\n      selectors: [[\"app-accueil\"]],\n      decls: 255,\n      vars: 0,\n      consts: [[1, \"accueil-container\"], [1, \"travelease-header\"], [1, \"travelease-logo\"], [1, \"fas\", \"fa-plane\"], [1, \"search-flights-section\"], [1, \"search-flights-container\"], [1, \"search-title\"], [1, \"search-subtitle\"], [1, \"search-form\"], [1, \"search-row\"], [1, \"search-group\"], [1, \"search-input-group\"], [1, \"search-type\"], [1, \"type-selector\"], [1, \"fas\", \"fa-map-marker-alt\"], [\"value\", \"city\"], [\"value\", \"airport\"], [1, \"location-input\"], [1, \"fas\", \"fa-plane-departure\"], [\"type\", \"text\", \"placeholder\", \"City...\"], [1, \"exchange-button\"], [1, \"fas\", \"fa-exchange-alt\"], [1, \"fas\", \"fa-plane-arrival\"], [\"type\", \"text\", \"placeholder\", \"Airport...\"], [1, \"search-group\", \"date-group\"], [1, \"date-input\"], [1, \"far\", \"fa-calendar-alt\"], [\"type\", \"date\", \"placeholder\", \"01/05/2023\"], [1, \"search-group\", \"passengers-group\"], [1, \"passengers-input\"], [1, \"fas\", \"fa-users\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [\"value\", \"5\"], [1, \"search-group\", \"class-group\"], [1, \"class-input\"], [1, \"fas\", \"fa-chair\"], [\"value\", \"economy\"], [\"value\", \"business\"], [\"value\", \"first\"], [1, \"search-row\", \"search-actions\"], [1, \"nonstop-check\"], [1, \"checkbox-container\"], [\"type\", \"checkbox\"], [1, \"checkmark\"], [1, \"search-button\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"advanced-options\"], [1, \"advanced-button\"], [1, \"fas\", \"fa-cog\"], [1, \"features-section\"], [1, \"features-background\"], [\"src\", \"assets/images/features-background.svg\", \"alt\", \"Background pattern\"], [1, \"section-title\"], [1, \"section-subtitle\"], [1, \"features-grid\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-100\"], [1, \"feature-card-inner\"], [1, \"feature-icon\", \"blue-gradient\"], [1, \"feature-title\"], [1, \"feature-description\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-200\"], [1, \"feature-icon\", \"orange-gradient\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-300\"], [1, \"feature-icon\", \"teal-gradient\"], [1, \"fas\", \"fa-credit-card\"], [1, \"feature-card\", \"animate-fade-in\", \"animate-delay-400\"], [1, \"feature-icon\", \"purple-gradient\"], [1, \"fas\", \"fa-headset\"], [1, \"destinations-section\"], [1, \"destinations-grid\"], [1, \"destination-card\", \"istanbul\", \"animate-fade-in\"], [1, \"destination-image\"], [1, \"destination-overlay\"], [1, \"destination-content\"], [1, \"destination-code\"], [1, \"destination-card\", \"tunis\", \"animate-fade-in\", \"animate-delay-100\"], [1, \"destination-card\", \"paris\", \"animate-fade-in\", \"animate-delay-200\"], [1, \"destination-card\", \"dubai\", \"animate-fade-in\", \"animate-delay-300\"], [1, \"cta-section\"], [1, \"cta-background\"], [\"src\", \"assets/images/cta-background.svg\", \"alt\", \"CTA Background\"], [1, \"cta-content\"], [1, \"cta-title\"], [1, \"cta-description\"], [\"mat-flat-button\", \"\", 1, \"cta-button\", 3, \"click\"], [1, \"testimonials-section\"], [1, \"testimonials-container\"], [1, \"testimonial-card\", \"animate-fade-in\"], [1, \"testimonial-content\"], [1, \"testimonial-rating\"], [1, \"fas\", \"fa-star\"], [1, \"testimonial-text\"], [1, \"testimonial-author\"], [1, \"testimonial-avatar\"], [1, \"fas\", \"fa-user\"], [1, \"testimonial-info\"], [1, \"testimonial-card\", \"animate-fade-in\", \"animate-delay-100\"], [1, \"fas\", \"fa-star-half-alt\"], [1, \"testimonial-card\", \"animate-fade-in\", \"animate-delay-200\"]],\n      template: function AccueilComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵtext(4, \" TravelEase \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"h2\", 6);\n          i0.ɵɵtext(8, \"Search Flights\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 7);\n          i0.ɵɵtext(10, \"Enter your travel details below\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10)(14, \"label\");\n          i0.ɵɵtext(15, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 11)(17, \"div\", 12)(18, \"span\");\n          i0.ɵɵtext(19, \"From Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 13);\n          i0.ɵɵelement(21, \"i\", 14);\n          i0.ɵɵelementStart(22, \"select\")(23, \"option\", 15);\n          i0.ɵɵtext(24, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"option\", 16);\n          i0.ɵɵtext(26, \"Airport\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 17);\n          i0.ɵɵelement(28, \"i\", 18)(29, \"input\", 19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 20)(31, \"button\");\n          i0.ɵɵelement(32, \"i\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 10)(34, \"label\");\n          i0.ɵɵtext(35, \"To\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 11)(37, \"div\", 12)(38, \"span\");\n          i0.ɵɵtext(39, \"To Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 13);\n          i0.ɵɵelement(41, \"i\", 3);\n          i0.ɵɵelementStart(42, \"select\")(43, \"option\", 15);\n          i0.ɵɵtext(44, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"option\", 16);\n          i0.ɵɵtext(46, \"Airport\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"div\", 17);\n          i0.ɵɵelement(48, \"i\", 22)(49, \"input\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(50, \"div\", 9)(51, \"div\", 24)(52, \"label\");\n          i0.ɵɵtext(53, \"Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 25);\n          i0.ɵɵelement(55, \"i\", 26)(56, \"input\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 28)(58, \"label\");\n          i0.ɵɵtext(59, \"Passengers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 29);\n          i0.ɵɵelement(61, \"i\", 30);\n          i0.ɵɵelementStart(62, \"select\")(63, \"option\", 31);\n          i0.ɵɵtext(64, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"option\", 32);\n          i0.ɵɵtext(66, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"option\", 33);\n          i0.ɵɵtext(68, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"option\", 34);\n          i0.ɵɵtext(70, \"4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"option\", 35);\n          i0.ɵɵtext(72, \"5\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(73, \"div\", 36)(74, \"label\");\n          i0.ɵɵtext(75, \"Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 37);\n          i0.ɵɵelement(77, \"i\", 38);\n          i0.ɵɵelementStart(78, \"select\")(79, \"option\", 39);\n          i0.ɵɵtext(80, \"Economy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"option\", 40);\n          i0.ɵɵtext(82, \"Business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"option\", 41);\n          i0.ɵɵtext(84, \"First Class\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(85, \"div\", 42)(86, \"div\", 43)(87, \"label\", 44);\n          i0.ɵɵelement(88, \"input\", 45)(89, \"span\", 46);\n          i0.ɵɵtext(90, \" Non-stop flights only \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"button\", 47);\n          i0.ɵɵlistener(\"click\", function AccueilComponent_Template_button_click_91_listener() {\n            return ctx.navigateToSearchPrice();\n          });\n          i0.ɵɵelement(92, \"i\", 48);\n          i0.ɵɵtext(93, \" Search \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"div\", 49)(95, \"button\", 50);\n          i0.ɵɵelement(96, \"i\", 51);\n          i0.ɵɵtext(97, \" Advanced Options \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(98, \"div\", 52)(99, \"div\", 53);\n          i0.ɵɵelement(100, \"img\", 54);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"h2\", 55);\n          i0.ɵɵtext(102, \"Pourquoi nous choisir\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"p\", 56);\n          i0.ɵɵtext(104, \"Nous offrons une exp\\u00E9rience de r\\u00E9servation de vol simple et efficace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"div\", 57)(106, \"div\", 58)(107, \"div\", 59)(108, \"div\", 60);\n          i0.ɵɵelement(109, \"i\", 48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"h3\", 61);\n          i0.ɵɵtext(111, \"Recherche facile\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"p\", 62);\n          i0.ɵɵtext(113, \"Trouvez rapidement des vols avec notre moteur de recherche puissant\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(114, \"div\", 63)(115, \"div\", 59)(116, \"div\", 64);\n          i0.ɵɵelement(117, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"h3\", 61);\n          i0.ɵɵtext(119, \"Comparez les options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"p\", 62);\n          i0.ɵɵtext(121, \"Comparez diff\\u00E9rentes options de vol pour trouver la meilleure offre\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(122, \"div\", 65)(123, \"div\", 59)(124, \"div\", 66);\n          i0.ɵɵelement(125, \"i\", 67);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(126, \"h3\", 61);\n          i0.ɵɵtext(127, \"R\\u00E9servation s\\u00E9curis\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"p\", 62);\n          i0.ɵɵtext(129, \"R\\u00E9servez vos vols en toute s\\u00E9curit\\u00E9 avec notre plateforme de confiance\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(130, \"div\", 68)(131, \"div\", 59)(132, \"div\", 69);\n          i0.ɵɵelement(133, \"i\", 70);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"h3\", 61);\n          i0.ɵɵtext(135, \"Support 24/7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(136, \"p\", 62);\n          i0.ɵɵtext(137, \"Obtenez de l'aide \\u00E0 tout moment avec notre \\u00E9quipe de support d\\u00E9di\\u00E9e\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(138, \"div\", 71)(139, \"h2\", 55);\n          i0.ɵɵtext(140, \"Destinations populaires\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(141, \"p\", 56);\n          i0.ɵɵtext(142, \"Explorez nos destinations les plus recherch\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(143, \"div\", 72)(144, \"div\", 73)(145, \"div\", 74);\n          i0.ɵɵelement(146, \"div\", 75);\n          i0.ɵɵelementStart(147, \"div\", 76)(148, \"h3\");\n          i0.ɵɵtext(149, \"Istanbul\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(150, \"p\");\n          i0.ɵɵtext(151, \"Turquie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"span\", 77);\n          i0.ɵɵtext(153, \"IST\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(154, \"div\", 78)(155, \"div\", 74);\n          i0.ɵɵelement(156, \"div\", 75);\n          i0.ɵɵelementStart(157, \"div\", 76)(158, \"h3\");\n          i0.ɵɵtext(159, \"Tunis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(160, \"p\");\n          i0.ɵɵtext(161, \"Tunisie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(162, \"span\", 77);\n          i0.ɵɵtext(163, \"TUN\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(164, \"div\", 79)(165, \"div\", 74);\n          i0.ɵɵelement(166, \"div\", 75);\n          i0.ɵɵelementStart(167, \"div\", 76)(168, \"h3\");\n          i0.ɵɵtext(169, \"Paris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(170, \"p\");\n          i0.ɵɵtext(171, \"France\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(172, \"span\", 77);\n          i0.ɵɵtext(173, \"CDG\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(174, \"div\", 80)(175, \"div\", 74);\n          i0.ɵɵelement(176, \"div\", 75);\n          i0.ɵɵelementStart(177, \"div\", 76)(178, \"h3\");\n          i0.ɵɵtext(179, \"Dubai\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(180, \"p\");\n          i0.ɵɵtext(181, \"\\u00C9mirats Arabes Unis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(182, \"span\", 77);\n          i0.ɵɵtext(183, \"DXB\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(184, \"div\", 81)(185, \"div\", 82);\n          i0.ɵɵelement(186, \"img\", 83);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(187, \"div\", 84)(188, \"h2\", 85);\n          i0.ɵɵtext(189, \"Pr\\u00EAt \\u00E0 commencer votre voyage?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(190, \"p\", 86);\n          i0.ɵɵtext(191, \"R\\u00E9servez votre vol maintenant et embarquez pour votre prochaine aventure\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(192, \"button\", 87);\n          i0.ɵɵlistener(\"click\", function AccueilComponent_Template_button_click_192_listener() {\n            return ctx.navigateToSearchPrice();\n          });\n          i0.ɵɵelement(193, \"i\", 48);\n          i0.ɵɵtext(194, \" Rechercher des vols \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(195, \"div\", 88)(196, \"h2\", 55);\n          i0.ɵɵtext(197, \"Ce que disent nos clients\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(198, \"p\", 56);\n          i0.ɵɵtext(199, \"D\\u00E9couvrez les exp\\u00E9riences de nos voyageurs satisfaits\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(200, \"div\", 89)(201, \"div\", 90)(202, \"div\", 91)(203, \"div\", 92);\n          i0.ɵɵelement(204, \"i\", 93)(205, \"i\", 93)(206, \"i\", 93)(207, \"i\", 93)(208, \"i\", 93);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(209, \"p\", 94);\n          i0.ɵɵtext(210, \"\\\"Service exceptionnel! J'ai trouv\\u00E9 un vol \\u00E0 un prix imbattable et la r\\u00E9servation a \\u00E9t\\u00E9 simple et rapide.\\\"\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(211, \"div\", 95)(212, \"div\", 96);\n          i0.ɵɵelement(213, \"i\", 97);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(214, \"div\", 98)(215, \"h4\");\n          i0.ɵɵtext(216, \"Sophie Martin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(217, \"p\");\n          i0.ɵɵtext(218, \"Paris, France\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(219, \"div\", 99)(220, \"div\", 91)(221, \"div\", 92);\n          i0.ɵɵelement(222, \"i\", 93)(223, \"i\", 93)(224, \"i\", 93)(225, \"i\", 93)(226, \"i\", 100);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(227, \"p\", 94);\n          i0.ɵɵtext(228, \"\\\"Interface intuitive et options de vol nombreuses. J'ai pu comparer facilement et choisir la meilleure option pour mon budget.\\\"\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(229, \"div\", 95)(230, \"div\", 96);\n          i0.ɵɵelement(231, \"i\", 97);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(232, \"div\", 98)(233, \"h4\");\n          i0.ɵɵtext(234, \"Thomas Dubois\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(235, \"p\");\n          i0.ɵɵtext(236, \"Lyon, France\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(237, \"div\", 101)(238, \"div\", 91)(239, \"div\", 92);\n          i0.ɵɵelement(240, \"i\", 93)(241, \"i\", 93)(242, \"i\", 93)(243, \"i\", 93)(244, \"i\", 93);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(245, \"p\", 94);\n          i0.ɵɵtext(246, \"\\\"Le support client est remarquable. J'ai eu besoin d'aide pour modifier ma r\\u00E9servation et tout a \\u00E9t\\u00E9 r\\u00E9solu rapidement.\\\"\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(247, \"div\", 95)(248, \"div\", 96);\n          i0.ɵɵelement(249, \"i\", 97);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(250, \"div\", 98)(251, \"h4\");\n          i0.ɵɵtext(252, \"Marie Leroy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(253, \"p\");\n          i0.ɵɵtext(254, \"Marseille, France\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n      },\n      dependencies: [i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i5.MatButton],\n      styles: [\".accueil-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow-x: hidden;\\n}\\n\\n\\n\\n.primary-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\\n  color: white;\\n}\\n\\n.blue-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-color), var(--sky-blue));\\n  color: white;\\n}\\n\\n.orange-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--secondary-color), var(--sunset-orange));\\n  color: white;\\n}\\n\\n.teal-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--accent-color), var(--teal));\\n  color: white;\\n}\\n\\n.purple-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--purple), var(--deep-purple));\\n  color: white;\\n}\\n\\n\\n\\n.section-title[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 2.25rem;\\n  font-weight: 700;\\n  color: var(--text-primary);\\n  margin-bottom: var(--spacing-md);\\n  position: relative;\\n  display: inline-block;\\n  left: 50%;\\n  transform: translateX(-50%);\\n}\\n\\n.section-title[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(to right, var(--primary-color), var(--accent-color));\\n  border-radius: 2px;\\n}\\n\\n.section-subtitle[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 1.1rem;\\n  color: var(--text-secondary);\\n  max-width: 700px;\\n  margin: 0 auto var(--spacing-xl);\\n}\\n\\n\\n\\n.hero-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: var(--spacing-xxl) 0;\\n  overflow: hidden;\\n  margin-bottom: var(--spacing-xxl);\\n  min-height: 600px;\\n}\\n\\n.hero-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.05) 0%, rgba(var(--primary-color-rgb), 0.1) 100%);\\n  z-index: -1;\\n  border-radius: var(--border-radius-large);\\n  overflow: hidden;\\n}\\n\\n.hero-shapes[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.shape[_ngcontent-%COMP%] {\\n  position: absolute;\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n  border-radius: 50%;\\n}\\n\\n.shape-1[_ngcontent-%COMP%] {\\n  width: 500px;\\n  height: 500px;\\n  top: -250px;\\n  right: -100px;\\n  background: radial-gradient(circle at center, rgba(var(--primary-color-rgb), 0.08) 0%, rgba(var(--primary-color-rgb), 0.03) 70%, transparent 100%);\\n  animation: float 20s infinite alternate ease-in-out;\\n}\\n\\n.shape-2[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: 300px;\\n  bottom: -150px;\\n  left: -50px;\\n  background: radial-gradient(circle at center, rgba(var(--primary-color-rgb), 0.08) 0%, rgba(var(--primary-color-rgb), 0.03) 70%, transparent 100%);\\n  animation: float 25s infinite alternate-reverse ease-in-out;\\n}\\n\\n.shape-3[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  background: radial-gradient(circle at center, rgba(var(--primary-color-rgb), 0.08) 0%, rgba(var(--primary-color-rgb), 0.03) 70%, transparent 100%);\\n  animation: pulse 15s infinite alternate ease-in-out;\\n}\\n\\n.hero-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0 var(--spacing-xl);\\n  max-width: 600px;\\n  z-index: 1;\\n}\\n\\n.hero-title[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 800;\\n  color: var(--primary-dark);\\n  margin-bottom: var(--spacing-md);\\n  line-height: 1.2;\\n  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n\\n.hero-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  color: var(--text-secondary);\\n  margin-bottom: var(--spacing-lg);\\n  line-height: 1.6;\\n}\\n\\n.hero-cta-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: var(--spacing-md);\\n  align-items: center;\\n}\\n\\n.hero-cta[_ngcontent-%COMP%] {\\n  padding: var(--spacing-md) var(--spacing-lg);\\n  font-size: 1rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n  border-radius: var(--border-radius-medium);\\n  transition: all var(--transition-fast);\\n  box-shadow: var(--elevation-2);\\n}\\n\\n.hero-cta[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n.hero-cta[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: var(--elevation-3);\\n}\\n\\n.hero-cta[_ngcontent-%COMP%]:active {\\n  transform: translateY(-1px);\\n}\\n\\n.hero-cta-secondary[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: var(--primary-color);\\n  border: 2px solid var(--primary-color);\\n}\\n\\n.hero-cta-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(var(--primary-color-rgb), 0.05);\\n}\\n\\n.hero-image[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: var(--spacing-lg);\\n  z-index: 1;\\n}\\n\\n.hero-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  max-height: 500px;\\n  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));\\n}\\n\\n\\n\\n.features-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: var(--spacing-xxl) 0;\\n  margin-bottom: var(--spacing-xxl);\\n  overflow: hidden;\\n}\\n\\n.features-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: -1;\\n}\\n\\n.features-background[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: var(--spacing-lg);\\n  padding: 0 var(--spacing-md);\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  border-radius: var(--border-radius-large);\\n  overflow: hidden;\\n  height: 100%;\\n  perspective: 1000px;\\n}\\n\\n.feature-card-inner[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: var(--border-radius-large);\\n  box-shadow: var(--elevation-2);\\n  padding: var(--spacing-xl);\\n  height: 100%;\\n  transition: transform var(--transition-medium), box-shadow var(--transition-medium);\\n  transform-style: preserve-3d;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]:hover   .feature-card-inner[_ngcontent-%COMP%] {\\n  transform: translateY(-10px) rotateX(5deg);\\n  box-shadow: var(--elevation-4);\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 20px;\\n  margin-bottom: var(--spacing-lg);\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n.feature-icon[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);\\n  animation: shimmer 2s infinite;\\n}\\n\\n.feature-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: white;\\n  z-index: 1;\\n}\\n\\n.feature-title[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n  margin-bottom: var(--spacing-md);\\n}\\n\\n.feature-description[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 1rem;\\n  line-height: 1.6;\\n}\\n\\n\\n\\n.destinations-section[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xxl) 0;\\n  margin-bottom: var(--spacing-xxl);\\n}\\n\\n.destinations-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: var(--spacing-lg);\\n  padding: 0 var(--spacing-md);\\n}\\n\\n.destination-card[_ngcontent-%COMP%] {\\n  border-radius: var(--border-radius-large);\\n  overflow: hidden;\\n  height: 250px;\\n  position: relative;\\n  box-shadow: var(--elevation-2);\\n  transition: transform var(--transition-medium), box-shadow var(--transition-medium);\\n}\\n\\n.destination-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: var(--elevation-4);\\n}\\n\\n.destination-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.destination-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.2) 50%, rgba(0, 0, 0, 0) 100%);\\n  z-index: 1;\\n}\\n\\n.destination-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  padding: var(--spacing-lg);\\n  color: white;\\n  z-index: 2;\\n}\\n\\n.destination-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.destination-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  opacity: 0.9;\\n  margin: 0;\\n}\\n\\n.destination-code[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: var(--spacing-md);\\n  right: var(--spacing-md);\\n  background-color: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  padding: 4px 8px;\\n  border-radius: var(--border-radius-small);\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  letter-spacing: 1px;\\n}\\n\\n\\n\\n.istanbul[_ngcontent-%COMP%]   .destination-image[_ngcontent-%COMP%] {\\n  background: url('/assets/images/destinations/istanbul.jpg') center/cover no-repeat;\\n}\\n\\n.tunis[_ngcontent-%COMP%]   .destination-image[_ngcontent-%COMP%] {\\n  background: url('/assets/images/destinations/tunis.jpg') center/cover no-repeat;\\n}\\n\\n.paris[_ngcontent-%COMP%]   .destination-image[_ngcontent-%COMP%] {\\n  background: url('/assets/images/destinations/paris.jpg') center/cover no-repeat;\\n}\\n\\n.dubai[_ngcontent-%COMP%]   .destination-image[_ngcontent-%COMP%] {\\n  background: url('/assets/images/destinations/dubai.jpg') center/cover no-repeat;\\n}\\n\\n\\n\\n.cta-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: var(--border-radius-large);\\n  padding: var(--spacing-xxl) var(--spacing-xl);\\n  margin: 0 var(--spacing-md) var(--spacing-xxl);\\n  color: white;\\n  text-align: center;\\n  overflow: hidden;\\n}\\n\\n.cta-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: -1;\\n}\\n\\n.cta-background[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.cta-content[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.cta-title[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  font-weight: 700;\\n  margin-bottom: var(--spacing-md);\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n\\n.cta-description[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: var(--spacing-lg);\\n  opacity: 0.9;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n\\n.cta-button[_ngcontent-%COMP%] {\\n  padding: var(--spacing-md) var(--spacing-xl);\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  background-color: white;\\n  color: var(--primary-color);\\n  border-radius: var(--border-radius-medium);\\n  box-shadow: var(--elevation-3);\\n  transition: all var(--transition-fast);\\n  display: inline-flex;\\n  align-items: center;\\n  gap: var(--spacing-sm);\\n}\\n\\n.cta-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: var(--elevation-4);\\n}\\n\\n.cta-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(-1px);\\n  box-shadow: var(--elevation-2);\\n}\\n\\n.cta-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n\\n\\n\\n.testimonials-section[_ngcontent-%COMP%] {\\n  padding: var(--spacing-xxl) 0;\\n  margin-bottom: var(--spacing-xxl);\\n}\\n\\n.testimonials-container[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: var(--spacing-lg);\\n  padding: 0 var(--spacing-md);\\n}\\n\\n.testimonial-card[_ngcontent-%COMP%] {\\n  border-radius: var(--border-radius-large);\\n  overflow: hidden;\\n  height: 100%;\\n}\\n\\n.testimonial-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: var(--border-radius-large);\\n  box-shadow: var(--elevation-2);\\n  padding: var(--spacing-xl);\\n  height: 100%;\\n  transition: transform var(--transition-medium), box-shadow var(--transition-medium);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.testimonial-card[_ngcontent-%COMP%]:hover   .testimonial-content[_ngcontent-%COMP%] {\\n  transform: translateY(-5px);\\n  box-shadow: var(--elevation-3);\\n}\\n\\n.testimonial-rating[_ngcontent-%COMP%] {\\n  margin-bottom: var(--spacing-md);\\n  color: var(--amber);\\n}\\n\\n.testimonial-rating[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 2px;\\n}\\n\\n.testimonial-text[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: var(--text-secondary);\\n  line-height: 1.6;\\n  margin-bottom: var(--spacing-lg);\\n  flex-grow: 1;\\n  font-style: italic;\\n}\\n\\n.testimonial-author[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-md);\\n}\\n\\n.testimonial-avatar[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 1.5rem;\\n}\\n\\n.testimonial-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n  margin: 0 0 4px;\\n}\\n\\n.testimonial-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--text-secondary);\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 992px) {\\n  .hero-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: var(--spacing-xl) var(--spacing-md);\\n    min-height: auto;\\n  }\\n\\n  .hero-content[_ngcontent-%COMP%] {\\n    text-align: center;\\n    padding: var(--spacing-lg) var(--spacing-md);\\n    order: 2;\\n    max-width: 100%;\\n  }\\n\\n  .hero-image[_ngcontent-%COMP%] {\\n    order: 1;\\n    margin-bottom: var(--spacing-lg);\\n  }\\n\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n\\n  .hero-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n    margin-left: auto;\\n    margin-right: auto;\\n  }\\n\\n  .hero-cta-container[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    flex-wrap: wrap;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .cta-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .cta-description[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .testimonials-container[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .hero-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .hero-cta-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n\\n  .hero-cta[_ngcontent-%COMP%], .hero-cta-secondary[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n\\n  .features-grid[_ngcontent-%COMP%], .destinations-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .cta-section[_ngcontent-%COMP%] {\\n    padding: var(--spacing-xl) var(--spacing-md);\\n  }\\n\\n  .cta-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n\\n  .section-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n\\n  .cta-button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      data: {\n        animation: [trigger('fadeIn', [transition(':enter', [style({\n          opacity: 0\n        }), animate('600ms ease-in', style({\n          opacity: 1\n        }))])]), trigger('slideUp', [transition(':enter', [style({\n          transform: 'translateY(30px)',\n          opacity: 0\n        }), animate('600ms ease-out', style({\n          transform: 'translateY(0)',\n          opacity: 1\n        }))])]), trigger('staggerFadeIn', [transition('* => *', [query(':enter', [style({\n          opacity: 0,\n          transform: 'translateY(30px)'\n        }), stagger('100ms', [animate('600ms ease-out', style({\n          opacity: 1,\n          transform: 'translateY(0)'\n        }))])], {\n          optional: true\n        })])])]\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["trigger", "transition", "style", "animate", "query", "stagger", "AccueilComponent", "constructor", "authService", "router", "snackBar", "userName", "destinations", "name", "country", "code", "image", "testimonials", "location", "rating", "text", "ngOnInit", "document", "body", "classList", "add", "userInfo", "getUserInfo", "username", "ngOnDestroy", "remove", "navigateToSearchPrice", "navigate", "showMoreInfo", "open", "duration", "panelClass", "verticalPosition", "getStars", "fullStars", "Math", "floor", "hasHalfStar", "stars", "Array", "fill", "push", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "AccueilComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AccueilComponent_Template_button_click_91_listener", "AccueilComponent_Template_button_click_192_listener", "opacity", "transform", "optional"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\accueil\\accueil.component.ts", "C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\accueil\\accueil.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { trigger, transition, style, animate, query, stagger } from '@angular/animations';\nimport { MatSnackBar } from '@angular/material/snack-bar';\n\n@Component({\n  selector: 'app-accueil',\n  templateUrl: './accueil.component.html',\n  styleUrls: ['./accueil.component.css'],\n  animations: [\n    trigger('fadeIn', [\n      transition(':enter', [\n        style({ opacity: 0 }),\n        animate('600ms ease-in', style({ opacity: 1 }))\n      ])\n    ]),\n    trigger('slideUp', [\n      transition(':enter', [\n        style({ transform: 'translateY(30px)', opacity: 0 }),\n        animate('600ms ease-out', style({ transform: 'translateY(0)', opacity: 1 }))\n      ])\n    ]),\n    trigger('staggerFadeIn', [\n      transition('* => *', [\n        query(':enter', [\n          style({ opacity: 0, transform: 'translateY(30px)' }),\n          stagger('100ms', [\n            animate('600ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))\n          ])\n        ], { optional: true })\n      ])\n    ])\n  ]\n})\nexport class AccueilComponent implements OnInit, OnDestroy {\n  userName: string = '';\n\n  // Destinations populaires\n  destinations = [\n    { name: 'Istanbul', country: 'Turquie', code: 'IST', image: 'istanbul.jpg' },\n    { name: 'Tunis', country: 'Tunisie', code: 'TUN', image: 'tunis.jpg' },\n    { name: 'Paris', country: 'France', code: 'CDG', image: 'paris.jpg' },\n    { name: 'Dubai', country: 'Émirats Arabes Unis', code: 'DXB', image: 'dubai.jpg' }\n  ];\n\n  // Témoignages\n  testimonials = [\n    {\n      name: 'Sophie Martin',\n      location: 'Paris, France',\n      rating: 5,\n      text: 'Service exceptionnel! J\\'ai trouvé un vol à un prix imbattable et la réservation a été simple et rapide.'\n    },\n    {\n      name: 'Thomas Dubois',\n      location: 'Lyon, France',\n      rating: 4.5,\n      text: 'Interface intuitive et options de vol nombreuses. J\\'ai pu comparer facilement et choisir la meilleure option pour mon budget.'\n    },\n    {\n      name: 'Marie Leroy',\n      location: 'Marseille, France',\n      rating: 5,\n      text: 'Le support client est remarquable. J\\'ai eu besoin d\\'aide pour modifier ma réservation et tout a été résolu rapidement.'\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) { }\n\n  ngOnInit(): void {\n    // Animation d'entrée pour la page\n    document.body.classList.add('accueil-page-active');\n\n    // Récupérer les informations de l'utilisateur\n    const userInfo = this.authService.getUserInfo();\n    if (userInfo) {\n      this.userName = userInfo.name || userInfo.username || 'Utilisateur';\n    }\n  }\n\n  ngOnDestroy(): void {\n    // Nettoyage de la classe lors de la destruction du composant\n    document.body.classList.remove('accueil-page-active');\n  }\n\n  navigateToSearchPrice(): void {\n    this.router.navigate(['/search-price']);\n  }\n\n  showMoreInfo(): void {\n    this.snackBar.open('Plus d\\'informations seront disponibles prochainement!', 'Fermer', {\n      duration: 3000,\n      panelClass: 'info-snackbar',\n      verticalPosition: 'bottom'\n    });\n  }\n\n  // Méthode pour générer un tableau d'étoiles pour les avis\n  getStars(rating: number): number[] {\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n    const stars = Array(fullStars).fill(1);\n\n    if (hasHalfStar) {\n      stars.push(0.5);\n    }\n\n    return stars;\n  }\n}\n", "<div class=\"accueil-container\">\n  <!-- Header Section with TravelEase Logo -->\n  <div class=\"travelease-header\">\n    <div class=\"travelease-logo\">\n      <i class=\"fas fa-plane\"></i> TravelEase\n    </div>\n  </div>\n\n  <!-- Search Flights Section -->\n  <div class=\"search-flights-section\">\n    <div class=\"search-flights-container\">\n      <h2 class=\"search-title\">Search Flights</h2>\n      <p class=\"search-subtitle\">Enter your travel details below</p>\n\n      <div class=\"search-form\">\n        <!-- From and To Fields Row -->\n        <div class=\"search-row\">\n          <div class=\"search-group\">\n            <label>From</label>\n            <div class=\"search-input-group\">\n              <div class=\"search-type\">\n                <span>From Type</span>\n                <div class=\"type-selector\">\n                  <i class=\"fas fa-map-marker-alt\"></i>\n                  <select>\n                    <option value=\"city\">City</option>\n                    <option value=\"airport\">Airport</option>\n                  </select>\n                </div>\n              </div>\n              <div class=\"location-input\">\n                <i class=\"fas fa-plane-departure\"></i>\n                <input type=\"text\" placeholder=\"City...\" />\n              </div>\n            </div>\n          </div>\n\n          <!-- Exchange Button -->\n          <div class=\"exchange-button\">\n            <button>\n              <i class=\"fas fa-exchange-alt\"></i>\n            </button>\n          </div>\n\n          <div class=\"search-group\">\n            <label>To</label>\n            <div class=\"search-input-group\">\n              <div class=\"search-type\">\n                <span>To Type</span>\n                <div class=\"type-selector\">\n                  <i class=\"fas fa-plane\"></i>\n                  <select>\n                    <option value=\"city\">City</option>\n                    <option value=\"airport\">Airport</option>\n                  </select>\n                </div>\n              </div>\n              <div class=\"location-input\">\n                <i class=\"fas fa-plane-arrival\"></i>\n                <input type=\"text\" placeholder=\"Airport...\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Date, Passengers and Class Row -->\n        <div class=\"search-row\">\n          <div class=\"search-group date-group\">\n            <label>Date</label>\n            <div class=\"date-input\">\n              <i class=\"far fa-calendar-alt\"></i>\n              <input type=\"date\" placeholder=\"01/05/2023\" />\n            </div>\n          </div>\n\n          <div class=\"search-group passengers-group\">\n            <label>Passengers</label>\n            <div class=\"passengers-input\">\n              <i class=\"fas fa-users\"></i>\n              <select>\n                <option value=\"1\">1</option>\n                <option value=\"2\">2</option>\n                <option value=\"3\">3</option>\n                <option value=\"4\">4</option>\n                <option value=\"5\">5</option>\n              </select>\n            </div>\n          </div>\n\n          <div class=\"search-group class-group\">\n            <label>Class</label>\n            <div class=\"class-input\">\n              <i class=\"fas fa-chair\"></i>\n              <select>\n                <option value=\"economy\">Economy</option>\n                <option value=\"business\">Business</option>\n                <option value=\"first\">First Class</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        <!-- Non-stop Flights and Search Button Row -->\n        <div class=\"search-row search-actions\">\n          <div class=\"nonstop-check\">\n            <label class=\"checkbox-container\">\n              <input type=\"checkbox\" />\n              <span class=\"checkmark\"></span>\n              Non-stop flights only\n            </label>\n          </div>\n\n          <button class=\"search-button\" (click)=\"navigateToSearchPrice()\">\n            <i class=\"fas fa-search\"></i> Search\n          </button>\n        </div>\n\n        <!-- Advanced Options -->\n        <div class=\"advanced-options\">\n          <button class=\"advanced-button\">\n            <i class=\"fas fa-cog\"></i> Advanced Options\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Features Section -->\n  <div class=\"features-section\">\n    <div class=\"features-background\">\n      <img src=\"assets/images/features-background.svg\" alt=\"Background pattern\">\n    </div>\n\n    <h2 class=\"section-title\">Pourquoi nous choisir</h2>\n    <p class=\"section-subtitle\">Nous offrons une expérience de réservation de vol simple et efficace</p>\n\n    <div class=\"features-grid\">\n      <div class=\"feature-card animate-fade-in animate-delay-100\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon blue-gradient\">\n            <i class=\"fas fa-search\"></i>\n          </div>\n          <h3 class=\"feature-title\">Recherche facile</h3>\n          <p class=\"feature-description\">Trouvez rapidement des vols avec notre moteur de recherche puissant</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card animate-fade-in animate-delay-200\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon orange-gradient\">\n            <i class=\"fas fa-exchange-alt\"></i>\n          </div>\n          <h3 class=\"feature-title\">Comparez les options</h3>\n          <p class=\"feature-description\">Comparez différentes options de vol pour trouver la meilleure offre</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card animate-fade-in animate-delay-300\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon teal-gradient\">\n            <i class=\"fas fa-credit-card\"></i>\n          </div>\n          <h3 class=\"feature-title\">Réservation sécurisée</h3>\n          <p class=\"feature-description\">Réservez vos vols en toute sécurité avec notre plateforme de confiance</p>\n        </div>\n      </div>\n\n      <div class=\"feature-card animate-fade-in animate-delay-400\">\n        <div class=\"feature-card-inner\">\n          <div class=\"feature-icon purple-gradient\">\n            <i class=\"fas fa-headset\"></i>\n          </div>\n          <h3 class=\"feature-title\">Support 24/7</h3>\n          <p class=\"feature-description\">Obtenez de l'aide à tout moment avec notre équipe de support dédiée</p>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Destinations Section -->\n  <div class=\"destinations-section\">\n    <h2 class=\"section-title\">Destinations populaires</h2>\n    <p class=\"section-subtitle\">Explorez nos destinations les plus recherchées</p>\n\n    <div class=\"destinations-grid\">\n      <div class=\"destination-card istanbul animate-fade-in\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Istanbul</h3>\n            <p>Turquie</p>\n            <span class=\"destination-code\">IST</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"destination-card tunis animate-fade-in animate-delay-100\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Tunis</h3>\n            <p>Tunisie</p>\n            <span class=\"destination-code\">TUN</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"destination-card paris animate-fade-in animate-delay-200\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Paris</h3>\n            <p>France</p>\n            <span class=\"destination-code\">CDG</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"destination-card dubai animate-fade-in animate-delay-300\">\n        <div class=\"destination-image\">\n          <div class=\"destination-overlay\"></div>\n          <div class=\"destination-content\">\n            <h3>Dubai</h3>\n            <p>Émirats Arabes Unis</p>\n            <span class=\"destination-code\">DXB</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- CTA Section -->\n  <div class=\"cta-section\">\n    <div class=\"cta-background\">\n      <img src=\"assets/images/cta-background.svg\" alt=\"CTA Background\">\n    </div>\n\n    <div class=\"cta-content\">\n      <h2 class=\"cta-title\">Prêt à commencer votre voyage?</h2>\n      <p class=\"cta-description\">Réservez votre vol maintenant et embarquez pour votre prochaine aventure</p>\n      <button mat-flat-button class=\"cta-button\" (click)=\"navigateToSearchPrice()\">\n        <i class=\"fas fa-search\"></i>\n        Rechercher des vols\n      </button>\n    </div>\n  </div>\n\n  <!-- Testimonials Section -->\n  <div class=\"testimonials-section\">\n    <h2 class=\"section-title\">Ce que disent nos clients</h2>\n    <p class=\"section-subtitle\">Découvrez les expériences de nos voyageurs satisfaits</p>\n\n    <div class=\"testimonials-container\">\n      <div class=\"testimonial-card animate-fade-in\">\n        <div class=\"testimonial-content\">\n          <div class=\"testimonial-rating\">\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n          </div>\n          <p class=\"testimonial-text\">\"Service exceptionnel! J'ai trouvé un vol à un prix imbattable et la réservation a été simple et rapide.\"</p>\n          <div class=\"testimonial-author\">\n            <div class=\"testimonial-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <div class=\"testimonial-info\">\n              <h4>Sophie Martin</h4>\n              <p>Paris, France</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"testimonial-card animate-fade-in animate-delay-100\">\n        <div class=\"testimonial-content\">\n          <div class=\"testimonial-rating\">\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star-half-alt\"></i>\n          </div>\n          <p class=\"testimonial-text\">\"Interface intuitive et options de vol nombreuses. J'ai pu comparer facilement et choisir la meilleure option pour mon budget.\"</p>\n          <div class=\"testimonial-author\">\n            <div class=\"testimonial-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <div class=\"testimonial-info\">\n              <h4>Thomas Dubois</h4>\n              <p>Lyon, France</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"testimonial-card animate-fade-in animate-delay-200\">\n        <div class=\"testimonial-content\">\n          <div class=\"testimonial-rating\">\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n            <i class=\"fas fa-star\"></i>\n          </div>\n          <p class=\"testimonial-text\">\"Le support client est remarquable. J'ai eu besoin d'aide pour modifier ma réservation et tout a été résolu rapidement.\"</p>\n          <div class=\"testimonial-author\">\n            <div class=\"testimonial-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n            <div class=\"testimonial-info\">\n              <h4>Marie Leroy</h4>\n              <p>Marseille, France</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAGA,SAASA,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;;;;;;;AAgCzF,OAAM,MAAOC,gBAAgB;EAiC3BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAFrB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAnClB,KAAAC,QAAQ,GAAW,EAAE;IAErB;IACA,KAAAC,YAAY,GAAG,CACb;MAAEC,IAAI,EAAE,UAAU;MAAEC,OAAO,EAAE,SAAS;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC5E;MAAEH,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,SAAS;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAW,CAAE,EACtE;MAAEH,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,QAAQ;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAW,CAAE,EACrE;MAAEH,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,qBAAqB;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAW,CAAE,CACnF;IAED;IACA,KAAAC,YAAY,GAAG,CACb;MACEJ,IAAI,EAAE,eAAe;MACrBK,QAAQ,EAAE,eAAe;MACzBC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE;KACP,EACD;MACEP,IAAI,EAAE,eAAe;MACrBK,QAAQ,EAAE,cAAc;MACxBC,MAAM,EAAE,GAAG;MACXC,IAAI,EAAE;KACP,EACD;MACEP,IAAI,EAAE,aAAa;MACnBK,QAAQ,EAAE,mBAAmB;MAC7BC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE;KACP,CACF;EAMG;EAEJC,QAAQA,CAAA;IACN;IACAC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAElD;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAClB,WAAW,CAACmB,WAAW,EAAE;IAC/C,IAAID,QAAQ,EAAE;MACZ,IAAI,CAACf,QAAQ,GAAGe,QAAQ,CAACb,IAAI,IAAIa,QAAQ,CAACE,QAAQ,IAAI,aAAa;;EAEvE;EAEAC,WAAWA,CAAA;IACT;IACAP,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACM,MAAM,CAAC,qBAAqB,CAAC;EACvD;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACvB,QAAQ,CAACwB,IAAI,CAAC,wDAAwD,EAAE,QAAQ,EAAE;MACrFC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,eAAe;MAC3BC,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEA;EACAC,QAAQA,CAACnB,MAAc;IACrB,MAAMoB,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACtB,MAAM,CAAC;IACpC,MAAMuB,WAAW,GAAGvB,MAAM,GAAG,CAAC,KAAK,CAAC;IACpC,MAAMwB,KAAK,GAAGC,KAAK,CAACL,SAAS,CAAC,CAACM,IAAI,CAAC,CAAC,CAAC;IAEtC,IAAIH,WAAW,EAAE;MACfC,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC;;IAGjB,OAAOH,KAAK;EACd;;;uBA9EWrC,gBAAgB,EAAAyC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBhD,gBAAgB;MAAAiD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnC7Bd,EAAA,CAAAgB,cAAA,aAA+B;UAIzBhB,EAAA,CAAAiB,SAAA,WAA4B;UAACjB,EAAA,CAAAkB,MAAA,mBAC/B;UAAAlB,EAAA,CAAAmB,YAAA,EAAM;UAIRnB,EAAA,CAAAgB,cAAA,aAAoC;UAEPhB,EAAA,CAAAkB,MAAA,qBAAc;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UAC5CnB,EAAA,CAAAgB,cAAA,WAA2B;UAAAhB,EAAA,CAAAkB,MAAA,uCAA+B;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAE9DnB,EAAA,CAAAgB,cAAA,cAAyB;UAIZhB,EAAA,CAAAkB,MAAA,YAAI;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACnBnB,EAAA,CAAAgB,cAAA,eAAgC;UAEtBhB,EAAA,CAAAkB,MAAA,iBAAS;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UACtBnB,EAAA,CAAAgB,cAAA,eAA2B;UACzBhB,EAAA,CAAAiB,SAAA,aAAqC;UACrCjB,EAAA,CAAAgB,cAAA,cAAQ;UACehB,EAAA,CAAAkB,MAAA,YAAI;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAClCnB,EAAA,CAAAgB,cAAA,kBAAwB;UAAAhB,EAAA,CAAAkB,MAAA,eAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAI9CnB,EAAA,CAAAgB,cAAA,eAA4B;UAC1BhB,EAAA,CAAAiB,SAAA,aAAsC;UAExCjB,EAAA,CAAAmB,YAAA,EAAM;UAKVnB,EAAA,CAAAgB,cAAA,eAA6B;UAEzBhB,EAAA,CAAAiB,SAAA,aAAmC;UACrCjB,EAAA,CAAAmB,YAAA,EAAS;UAGXnB,EAAA,CAAAgB,cAAA,eAA0B;UACjBhB,EAAA,CAAAkB,MAAA,UAAE;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACjBnB,EAAA,CAAAgB,cAAA,eAAgC;UAEtBhB,EAAA,CAAAkB,MAAA,eAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UACpBnB,EAAA,CAAAgB,cAAA,eAA2B;UACzBhB,EAAA,CAAAiB,SAAA,YAA4B;UAC5BjB,EAAA,CAAAgB,cAAA,cAAQ;UACehB,EAAA,CAAAkB,MAAA,YAAI;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAClCnB,EAAA,CAAAgB,cAAA,kBAAwB;UAAAhB,EAAA,CAAAkB,MAAA,eAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAI9CnB,EAAA,CAAAgB,cAAA,eAA4B;UAC1BhB,EAAA,CAAAiB,SAAA,aAAoC;UAEtCjB,EAAA,CAAAmB,YAAA,EAAM;UAMZnB,EAAA,CAAAgB,cAAA,cAAwB;UAEbhB,EAAA,CAAAkB,MAAA,YAAI;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACnBnB,EAAA,CAAAgB,cAAA,eAAwB;UACtBhB,EAAA,CAAAiB,SAAA,aAAmC;UAErCjB,EAAA,CAAAmB,YAAA,EAAM;UAGRnB,EAAA,CAAAgB,cAAA,eAA2C;UAClChB,EAAA,CAAAkB,MAAA,kBAAU;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACzBnB,EAAA,CAAAgB,cAAA,eAA8B;UAC5BhB,EAAA,CAAAiB,SAAA,aAA4B;UAC5BjB,EAAA,CAAAgB,cAAA,cAAQ;UACYhB,EAAA,CAAAkB,MAAA,SAAC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAC5BnB,EAAA,CAAAgB,cAAA,kBAAkB;UAAAhB,EAAA,CAAAkB,MAAA,SAAC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAC5BnB,EAAA,CAAAgB,cAAA,kBAAkB;UAAAhB,EAAA,CAAAkB,MAAA,SAAC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAC5BnB,EAAA,CAAAgB,cAAA,kBAAkB;UAAAhB,EAAA,CAAAkB,MAAA,SAAC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAC5BnB,EAAA,CAAAgB,cAAA,kBAAkB;UAAAhB,EAAA,CAAAkB,MAAA,SAAC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAKlCnB,EAAA,CAAAgB,cAAA,eAAsC;UAC7BhB,EAAA,CAAAkB,MAAA,aAAK;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UACpBnB,EAAA,CAAAgB,cAAA,eAAyB;UACvBhB,EAAA,CAAAiB,SAAA,aAA4B;UAC5BjB,EAAA,CAAAgB,cAAA,cAAQ;UACkBhB,EAAA,CAAAkB,MAAA,eAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UACxCnB,EAAA,CAAAgB,cAAA,kBAAyB;UAAAhB,EAAA,CAAAkB,MAAA,gBAAQ;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAC1CnB,EAAA,CAAAgB,cAAA,kBAAsB;UAAAhB,EAAA,CAAAkB,MAAA,mBAAW;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAOlDnB,EAAA,CAAAgB,cAAA,eAAuC;UAGjChB,EAAA,CAAAiB,SAAA,iBAAyB;UAEzBjB,EAAA,CAAAkB,MAAA,+BACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UAGVnB,EAAA,CAAAgB,cAAA,kBAAgE;UAAlChB,EAAA,CAAAoB,UAAA,mBAAAC,mDAAA;YAAA,OAASN,GAAA,CAAA/B,qBAAA,EAAuB;UAAA,EAAC;UAC7DgB,EAAA,CAAAiB,SAAA,aAA6B;UAACjB,EAAA,CAAAkB,MAAA,gBAChC;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAIXnB,EAAA,CAAAgB,cAAA,eAA8B;UAE1BhB,EAAA,CAAAiB,SAAA,aAA0B;UAACjB,EAAA,CAAAkB,MAAA,0BAC7B;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAOjBnB,EAAA,CAAAgB,cAAA,eAA8B;UAE1BhB,EAAA,CAAAiB,SAAA,gBAA0E;UAC5EjB,EAAA,CAAAmB,YAAA,EAAM;UAENnB,EAAA,CAAAgB,cAAA,eAA0B;UAAAhB,EAAA,CAAAkB,MAAA,8BAAqB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACpDnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,uFAAoE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAEpGnB,EAAA,CAAAgB,cAAA,gBAA2B;UAInBhB,EAAA,CAAAiB,SAAA,cAA6B;UAC/BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,eAA0B;UAAAhB,EAAA,CAAAkB,MAAA,yBAAgB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UAC/CnB,EAAA,CAAAgB,cAAA,cAA+B;UAAAhB,EAAA,CAAAkB,MAAA,4EAAmE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAI1GnB,EAAA,CAAAgB,cAAA,gBAA4D;UAGtDhB,EAAA,CAAAiB,SAAA,cAAmC;UACrCjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,eAA0B;UAAAhB,EAAA,CAAAkB,MAAA,6BAAoB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACnDnB,EAAA,CAAAgB,cAAA,cAA+B;UAAAhB,EAAA,CAAAkB,MAAA,iFAAmE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAI1GnB,EAAA,CAAAgB,cAAA,gBAA4D;UAGtDhB,EAAA,CAAAiB,SAAA,cAAkC;UACpCjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,eAA0B;UAAAhB,EAAA,CAAAkB,MAAA,6CAAqB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACpDnB,EAAA,CAAAgB,cAAA,cAA+B;UAAAhB,EAAA,CAAAkB,MAAA,8FAAsE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAI7GnB,EAAA,CAAAgB,cAAA,gBAA4D;UAGtDhB,EAAA,CAAAiB,SAAA,cAA8B;UAChCjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,eAA0B;UAAAhB,EAAA,CAAAkB,MAAA,qBAAY;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UAC3CnB,EAAA,CAAAgB,cAAA,cAA+B;UAAAhB,EAAA,CAAAkB,MAAA,gGAAmE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAO9GnB,EAAA,CAAAgB,cAAA,gBAAkC;UACNhB,EAAA,CAAAkB,MAAA,gCAAuB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACtDnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,4DAA8C;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAE9EnB,EAAA,CAAAgB,cAAA,gBAA+B;UAGzBhB,EAAA,CAAAiB,SAAA,gBAAuC;UACvCjB,EAAA,CAAAgB,cAAA,gBAAiC;UAC3BhB,EAAA,CAAAkB,MAAA,iBAAQ;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACjBnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,gBAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACdnB,EAAA,CAAAgB,cAAA,iBAA+B;UAAAhB,EAAA,CAAAkB,MAAA,YAAG;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAK/CnB,EAAA,CAAAgB,cAAA,gBAAsE;UAElEhB,EAAA,CAAAiB,SAAA,gBAAuC;UACvCjB,EAAA,CAAAgB,cAAA,gBAAiC;UAC3BhB,EAAA,CAAAkB,MAAA,cAAK;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACdnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,gBAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACdnB,EAAA,CAAAgB,cAAA,iBAA+B;UAAAhB,EAAA,CAAAkB,MAAA,YAAG;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAK/CnB,EAAA,CAAAgB,cAAA,gBAAsE;UAElEhB,EAAA,CAAAiB,SAAA,gBAAuC;UACvCjB,EAAA,CAAAgB,cAAA,gBAAiC;UAC3BhB,EAAA,CAAAkB,MAAA,cAAK;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACdnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,eAAM;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACbnB,EAAA,CAAAgB,cAAA,iBAA+B;UAAAhB,EAAA,CAAAkB,MAAA,YAAG;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAK/CnB,EAAA,CAAAgB,cAAA,gBAAsE;UAElEhB,EAAA,CAAAiB,SAAA,gBAAuC;UACvCjB,EAAA,CAAAgB,cAAA,gBAAiC;UAC3BhB,EAAA,CAAAkB,MAAA,cAAK;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACdnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,iCAAmB;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAC1BnB,EAAA,CAAAgB,cAAA,iBAA+B;UAAAhB,EAAA,CAAAkB,MAAA,YAAG;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAQnDnB,EAAA,CAAAgB,cAAA,gBAAyB;UAErBhB,EAAA,CAAAiB,SAAA,gBAAiE;UACnEjB,EAAA,CAAAmB,YAAA,EAAM;UAENnB,EAAA,CAAAgB,cAAA,gBAAyB;UACDhB,EAAA,CAAAkB,MAAA,iDAA8B;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACzDnB,EAAA,CAAAgB,cAAA,cAA2B;UAAAhB,EAAA,CAAAkB,MAAA,sFAAwE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACvGnB,EAAA,CAAAgB,cAAA,mBAA6E;UAAlChB,EAAA,CAAAoB,UAAA,mBAAAE,oDAAA;YAAA,OAASP,GAAA,CAAA/B,qBAAA,EAAuB;UAAA,EAAC;UAC1EgB,EAAA,CAAAiB,SAAA,cAA6B;UAC7BjB,EAAA,CAAAkB,MAAA,8BACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAKbnB,EAAA,CAAAgB,cAAA,gBAAkC;UACNhB,EAAA,CAAAkB,MAAA,kCAAyB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACxDnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,wEAAqD;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAErFnB,EAAA,CAAAgB,cAAA,gBAAoC;UAI5BhB,EAAA,CAAAiB,SAAA,cAA2B;UAK7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,6IAAyG;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACzInB,EAAA,CAAAgB,cAAA,gBAAgC;UAE5BhB,EAAA,CAAAiB,SAAA,cAA2B;UAC7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,gBAA8B;UACxBhB,EAAA,CAAAkB,MAAA,sBAAa;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACtBnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,sBAAa;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAM5BnB,EAAA,CAAAgB,cAAA,gBAAgE;UAG1DhB,EAAA,CAAAiB,SAAA,cAA2B;UAK7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,0IAA+H;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAC/JnB,EAAA,CAAAgB,cAAA,gBAAgC;UAE5BhB,EAAA,CAAAiB,SAAA,cAA2B;UAC7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,gBAA8B;UACxBhB,EAAA,CAAAkB,MAAA,sBAAa;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACtBnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,qBAAY;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAM3BnB,EAAA,CAAAgB,cAAA,iBAAgE;UAG1DhB,EAAA,CAAAiB,SAAA,cAA2B;UAK7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,cAA4B;UAAAhB,EAAA,CAAAkB,MAAA,uJAAwH;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACxJnB,EAAA,CAAAgB,cAAA,gBAAgC;UAE5BhB,EAAA,CAAAiB,SAAA,cAA2B;UAC7BjB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAgB,cAAA,gBAA8B;UACxBhB,EAAA,CAAAkB,MAAA,oBAAW;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACpBnB,EAAA,CAAAgB,cAAA,UAAG;UAAAhB,EAAA,CAAAkB,MAAA,0BAAiB;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;;;;;;mBD/SxB,CACVlE,OAAO,CAAC,QAAQ,EAAE,CAChBC,UAAU,CAAC,QAAQ,EAAE,CACnBC,KAAK,CAAC;UAAEoE,OAAO,EAAE;QAAC,CAAE,CAAC,EACrBnE,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;UAAEoE,OAAO,EAAE;QAAC,CAAE,CAAC,CAAC,CAChD,CAAC,CACH,CAAC,EACFtE,OAAO,CAAC,SAAS,EAAE,CACjBC,UAAU,CAAC,QAAQ,EAAE,CACnBC,KAAK,CAAC;UAAEqE,SAAS,EAAE,kBAAkB;UAAED,OAAO,EAAE;QAAC,CAAE,CAAC,EACpDnE,OAAO,CAAC,gBAAgB,EAAED,KAAK,CAAC;UAAEqE,SAAS,EAAE,eAAe;UAAED,OAAO,EAAE;QAAC,CAAE,CAAC,CAAC,CAC7E,CAAC,CACH,CAAC,EACFtE,OAAO,CAAC,eAAe,EAAE,CACvBC,UAAU,CAAC,QAAQ,EAAE,CACnBG,KAAK,CAAC,QAAQ,EAAE,CACdF,KAAK,CAAC;UAAEoE,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAkB,CAAE,CAAC,EACpDlE,OAAO,CAAC,OAAO,EAAE,CACfF,OAAO,CAAC,gBAAgB,EAAED,KAAK,CAAC;UAAEoE,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAAC,CAC7E,CAAC,CACH,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC,CACvB,CAAC,CACH,CAAC;MACH;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}