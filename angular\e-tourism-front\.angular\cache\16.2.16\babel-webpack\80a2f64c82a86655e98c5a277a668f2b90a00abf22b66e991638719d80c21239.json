{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\n\n/** Injection token to be used to override the default options for `mat-progress-bar`. */\nconst MAT_PROGRESS_BAR_DEFAULT_OPTIONS = new InjectionToken('MAT_PROGRESS_BAR_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatProgressBar`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_PROGRESS_BAR_LOCATION = new InjectionToken('mat-progress-bar-location', {\n  providedIn: 'root',\n  factory: MAT_PROGRESS_BAR_LOCATION_FACTORY\n});\n/** @docs-private */\nfunction MAT_PROGRESS_BAR_LOCATION_FACTORY() {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => _location ? _location.pathname + _location.search : ''\n  };\n}\n// Boilerplate for applying mixins to MatProgressBar.\n/** @docs-private */\nconst _MatProgressBarBase = mixinColor(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n}, 'primary');\nclass MatProgressBar extends _MatProgressBarBase {\n  constructor(elementRef, _ngZone, _changeDetectorRef, _animationMode, defaults) {\n    super(elementRef);\n    this._ngZone = _ngZone;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    /** Flag that indicates whether NoopAnimations mode is set to true. */\n    this._isNoopAnimation = false;\n    this._value = 0;\n    this._bufferValue = 0;\n    /**\n     * Event emitted when animation of the primary progress bar completes. This event will not\n     * be emitted when animations are disabled, nor will it be emitted for modes with continuous\n     * animations (indeterminate and query).\n     */\n    this.animationEnd = new EventEmitter();\n    this._mode = 'determinate';\n    /** Event handler for `transitionend` events. */\n    this._transitionendHandler = event => {\n      if (this.animationEnd.observers.length === 0 || !event.target || !event.target.classList.contains('mdc-linear-progress__primary-bar')) {\n        return;\n      }\n      if (this.mode === 'determinate' || this.mode === 'buffer') {\n        this._ngZone.run(() => this.animationEnd.next({\n          value: this.value\n        }));\n      }\n    };\n    this._isNoopAnimation = _animationMode === 'NoopAnimations';\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this.defaultColor = defaults.color;\n      }\n      this.mode = defaults.mode || this.mode;\n    }\n  }\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  get value() {\n    return this._value;\n  }\n  set value(v) {\n    this._value = clamp(coerceNumberProperty(v));\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Buffer value of the progress bar. Defaults to zero. */\n  get bufferValue() {\n    return this._bufferValue || 0;\n  }\n  set bufferValue(v) {\n    this._bufferValue = clamp(coerceNumberProperty(v));\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  get mode() {\n    return this._mode;\n  }\n  set mode(value) {\n    // Note that we don't technically need a getter and a setter here,\n    // but we use it to match the behavior of the existing mat-progress-bar.\n    this._mode = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  ngAfterViewInit() {\n    // Run outside angular so change detection didn't get triggered on every transition end\n    // instead only on the animation that we care about (primary value bar's transitionend)\n    this._ngZone.runOutsideAngular(() => {\n      this._elementRef.nativeElement.addEventListener('transitionend', this._transitionendHandler);\n    });\n  }\n  ngOnDestroy() {\n    this._elementRef.nativeElement.removeEventListener('transitionend', this._transitionendHandler);\n  }\n  /** Gets the transform style that should be applied to the primary bar. */\n  _getPrimaryBarTransform() {\n    return `scaleX(${this._isIndeterminate() ? 1 : this.value / 100})`;\n  }\n  /** Gets the `flex-basis` value that should be applied to the buffer bar. */\n  _getBufferBarFlexBasis() {\n    return `${this.mode === 'buffer' ? this.bufferValue : 100}%`;\n  }\n  /** Returns whether the progress bar is indeterminate. */\n  _isIndeterminate() {\n    return this.mode === 'indeterminate' || this.mode === 'query';\n  }\n  static {\n    this.ɵfac = function MatProgressBar_Factory(t) {\n      return new (t || MatProgressBar)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_PROGRESS_BAR_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatProgressBar,\n      selectors: [[\"mat-progress-bar\"]],\n      hostAttrs: [\"role\", \"progressbar\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", \"tabindex\", \"-1\", 1, \"mat-mdc-progress-bar\", \"mdc-linear-progress\"],\n      hostVars: 8,\n      hostBindings: function MatProgressBar_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-valuenow\", ctx._isIndeterminate() ? null : ctx.value)(\"mode\", ctx.mode);\n          i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._isNoopAnimation)(\"mdc-linear-progress--animation-ready\", !ctx._isNoopAnimation)(\"mdc-linear-progress--indeterminate\", ctx._isIndeterminate());\n        }\n      },\n      inputs: {\n        color: \"color\",\n        value: \"value\",\n        bufferValue: \"bufferValue\",\n        mode: \"mode\"\n      },\n      outputs: {\n        animationEnd: \"animationEnd\"\n      },\n      exportAs: [\"matProgressBar\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 7,\n      vars: 4,\n      consts: [[\"aria-hidden\", \"true\", 1, \"mdc-linear-progress__buffer\"], [1, \"mdc-linear-progress__buffer-bar\"], [1, \"mdc-linear-progress__buffer-dots\"], [\"aria-hidden\", \"true\", 1, \"mdc-linear-progress__bar\", \"mdc-linear-progress__primary-bar\"], [1, \"mdc-linear-progress__bar-inner\"], [\"aria-hidden\", \"true\", 1, \"mdc-linear-progress__bar\", \"mdc-linear-progress__secondary-bar\"]],\n      template: function MatProgressBar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵelement(4, \"span\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5);\n          i0.ɵɵelement(6, \"span\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"flex-basis\", ctx._getBufferBarFlexBasis());\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"transform\", ctx._getPrimaryBarTransform());\n        }\n      },\n      styles: [\"@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half))}100%{transform:translateX(var(--mdc-linear-progress-primary-full))}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full))}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-primary-full-neg))}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter-neg))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full-neg))}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}@media screen and (forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden}.mdc-linear-progress__buffer-dots{background-repeat:repeat-x;flex:auto;transform:rotate(180deg);-webkit-mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");animation:mdc-linear-progress-buffering 250ms infinite linear}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__bar{right:0;-webkit-transform-origin:center right;transform-origin:center right}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__buffer-dots,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}.mdc-linear-progress--closed{opacity:0}.mdc-linear-progress--closed-animation-off .mdc-linear-progress__buffer-dots{animation:none}.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar,.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar .mdc-linear-progress__bar-inner{animation:none}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mdc-linear-progress-track-height) * -2.5))}}.mdc-linear-progress__bar-inner{border-color:var(--mdc-linear-progress-active-indicator-color)}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill=''/%3E%3C/svg%3E\\\")}}.mdc-linear-progress{height:max(var(--mdc-linear-progress-track-height), var(--mdc-linear-progress-active-indicator-height))}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress{height:4px}}.mdc-linear-progress__bar{height:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__bar-inner{border-top-width:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__buffer{height:var(--mdc-linear-progress-track-height)}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-size:10px var(--mdc-linear-progress-track-height)}}.mdc-linear-progress__buffer{border-radius:var(--mdc-linear-progress-track-shape)}.mat-mdc-progress-bar{--mdc-linear-progress-active-indicator-height:4px;--mdc-linear-progress-track-height:4px;--mdc-linear-progress-track-shape:0}.mat-mdc-progress-bar{display:block;text-align:left;--mdc-linear-progress-primary-half: 83.67142%;--mdc-linear-progress-primary-full: 200.611057%;--mdc-linear-progress-secondary-quarter: 37.651913%;--mdc-linear-progress-secondary-half: 84.386165%;--mdc-linear-progress-secondary-full: 160.277782%;--mdc-linear-progress-primary-half-neg: -83.67142%;--mdc-linear-progress-primary-full-neg: -200.611057%;--mdc-linear-progress-secondary-quarter-neg: -37.651913%;--mdc-linear-progress-secondary-half-neg: -84.386165%;--mdc-linear-progress-secondary-full-neg: -160.277782%}[dir=rtl] .mat-mdc-progress-bar{text-align:right}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressBar, [{\n    type: Component,\n    args: [{\n      selector: 'mat-progress-bar',\n      exportAs: 'matProgressBar',\n      host: {\n        'role': 'progressbar',\n        'aria-valuemin': '0',\n        'aria-valuemax': '100',\n        // set tab index to -1 so screen readers will read the aria-label\n        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n        'tabindex': '-1',\n        '[attr.aria-valuenow]': '_isIndeterminate() ? null : value',\n        '[attr.mode]': 'mode',\n        'class': 'mat-mdc-progress-bar mdc-linear-progress',\n        '[class._mat-animation-noopable]': '_isNoopAnimation',\n        '[class.mdc-linear-progress--animation-ready]': '!_isNoopAnimation',\n        '[class.mdc-linear-progress--indeterminate]': '_isIndeterminate()'\n      },\n      inputs: ['color'],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-linear-progress__buffer\\\" aria-hidden=\\\"true\\\">\\n  <div\\n    class=\\\"mdc-linear-progress__buffer-bar\\\"\\n    [style.flex-basis]=\\\"_getBufferBarFlexBasis()\\\"></div>\\n  <div class=\\\"mdc-linear-progress__buffer-dots\\\"></div>\\n</div>\\n<div\\n  class=\\\"mdc-linear-progress__bar mdc-linear-progress__primary-bar\\\"\\n  aria-hidden=\\\"true\\\"\\n  [style.transform]=\\\"_getPrimaryBarTransform()\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n<div class=\\\"mdc-linear-progress__bar mdc-linear-progress__secondary-bar\\\" aria-hidden=\\\"true\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n\",\n      styles: [\"@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half))}100%{transform:translateX(var(--mdc-linear-progress-primary-full))}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full))}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-primary-full-neg))}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter-neg))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full-neg))}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}@media screen and (forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden}.mdc-linear-progress__buffer-dots{background-repeat:repeat-x;flex:auto;transform:rotate(180deg);-webkit-mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");animation:mdc-linear-progress-buffering 250ms infinite linear}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__bar{right:0;-webkit-transform-origin:center right;transform-origin:center right}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__buffer-dots,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}.mdc-linear-progress--closed{opacity:0}.mdc-linear-progress--closed-animation-off .mdc-linear-progress__buffer-dots{animation:none}.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar,.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar .mdc-linear-progress__bar-inner{animation:none}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mdc-linear-progress-track-height) * -2.5))}}.mdc-linear-progress__bar-inner{border-color:var(--mdc-linear-progress-active-indicator-color)}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill=''/%3E%3C/svg%3E\\\")}}.mdc-linear-progress{height:max(var(--mdc-linear-progress-track-height), var(--mdc-linear-progress-active-indicator-height))}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress{height:4px}}.mdc-linear-progress__bar{height:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__bar-inner{border-top-width:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__buffer{height:var(--mdc-linear-progress-track-height)}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-size:10px var(--mdc-linear-progress-track-height)}}.mdc-linear-progress__buffer{border-radius:var(--mdc-linear-progress-track-shape)}.mat-mdc-progress-bar{--mdc-linear-progress-active-indicator-height:4px;--mdc-linear-progress-track-height:4px;--mdc-linear-progress-track-shape:0}.mat-mdc-progress-bar{display:block;text-align:left;--mdc-linear-progress-primary-half: 83.67142%;--mdc-linear-progress-primary-full: 200.611057%;--mdc-linear-progress-secondary-quarter: 37.651913%;--mdc-linear-progress-secondary-half: 84.386165%;--mdc-linear-progress-secondary-full: 160.277782%;--mdc-linear-progress-primary-half-neg: -83.67142%;--mdc-linear-progress-primary-full-neg: -200.611057%;--mdc-linear-progress-secondary-quarter-neg: -37.651913%;--mdc-linear-progress-secondary-half-neg: -84.386165%;--mdc-linear-progress-secondary-full-neg: -160.277782%}[dir=rtl] .mat-mdc-progress-bar{text-align:right}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_PROGRESS_BAR_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    value: [{\n      type: Input\n    }],\n    bufferValue: [{\n      type: Input\n    }],\n    animationEnd: [{\n      type: Output\n    }],\n    mode: [{\n      type: Input\n    }]\n  });\n})();\n/** Clamps a value to be between two numbers, by default 0 and 100. */\nfunction clamp(v, min = 0, max = 100) {\n  return Math.max(min, Math.min(max, v));\n}\nclass MatProgressBarModule {\n  static {\n    this.ɵfac = function MatProgressBarModule_Factory(t) {\n      return new (t || MatProgressBarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatProgressBarModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressBarModule, [{\n    type: NgModule,\n    args: [{\n      exports: [MatProgressBar, MatCommonModule],\n      declarations: [MatProgressBar]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PROGRESS_BAR_DEFAULT_OPTIONS, MAT_PROGRESS_BAR_LOCATION, MAT_PROGRESS_BAR_LOCATION_FACTORY, MatProgressBar, MatProgressBarModule };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Optional", "Inject", "Input", "Output", "NgModule", "DOCUMENT", "mixinColor", "MatCommonModule", "ANIMATION_MODULE_TYPE", "coerceNumberProperty", "MAT_PROGRESS_BAR_DEFAULT_OPTIONS", "MAT_PROGRESS_BAR_LOCATION", "providedIn", "factory", "MAT_PROGRESS_BAR_LOCATION_FACTORY", "_document", "_location", "location", "getPathname", "pathname", "search", "_MatProgressBarBase", "constructor", "_elementRef", "MatProgressBar", "elementRef", "_ngZone", "_changeDetectorRef", "_animationMode", "defaults", "_isNoopAnimation", "_value", "_bufferValue", "animationEnd", "_mode", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "observers", "length", "target", "classList", "contains", "mode", "run", "next", "value", "color", "defaultColor", "v", "clamp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bufferValue", "ngAfterViewInit", "runOutsideAngular", "nativeElement", "addEventListener", "ngOnDestroy", "removeEventListener", "_getPrimaryBarTransform", "_isIndeterminate", "_getBufferBarFlexBasis", "ɵfac", "MatProgressBar_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "NgZone", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatProgressBar_HostBindings", "rf", "ctx", "ɵɵattribute", "ɵɵclassProp", "inputs", "outputs", "exportAs", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "MatProgressBar_Template", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "None", "undefined", "decorators", "min", "max", "Math", "MatProgressBarModule", "MatProgressBarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Desktop/e-tourism/angular/e-tourism-front/node_modules/@angular/material/fesm2022/progress-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\n\n/** Injection token to be used to override the default options for `mat-progress-bar`. */\nconst MAT_PROGRESS_BAR_DEFAULT_OPTIONS = new InjectionToken('MAT_PROGRESS_BAR_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatProgressBar`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_PROGRESS_BAR_LOCATION = new InjectionToken('mat-progress-bar-location', { providedIn: 'root', factory: MAT_PROGRESS_BAR_LOCATION_FACTORY });\n/** @docs-private */\nfunction MAT_PROGRESS_BAR_LOCATION_FACTORY() {\n    const _document = inject(DOCUMENT);\n    const _location = _document ? _document.location : null;\n    return {\n        // Note that this needs to be a function, rather than a property, because Angular\n        // will only resolve it once, but we want the current path on each call.\n        getPathname: () => (_location ? _location.pathname + _location.search : ''),\n    };\n}\n// Boilerplate for applying mixins to MatProgressBar.\n/** @docs-private */\nconst _MatProgressBarBase = mixinColor(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}, 'primary');\nclass MatProgressBar extends _MatProgressBarBase {\n    constructor(elementRef, _ngZone, _changeDetectorRef, _animationMode, defaults) {\n        super(elementRef);\n        this._ngZone = _ngZone;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        /** Flag that indicates whether NoopAnimations mode is set to true. */\n        this._isNoopAnimation = false;\n        this._value = 0;\n        this._bufferValue = 0;\n        /**\n         * Event emitted when animation of the primary progress bar completes. This event will not\n         * be emitted when animations are disabled, nor will it be emitted for modes with continuous\n         * animations (indeterminate and query).\n         */\n        this.animationEnd = new EventEmitter();\n        this._mode = 'determinate';\n        /** Event handler for `transitionend` events. */\n        this._transitionendHandler = (event) => {\n            if (this.animationEnd.observers.length === 0 ||\n                !event.target ||\n                !event.target.classList.contains('mdc-linear-progress__primary-bar')) {\n                return;\n            }\n            if (this.mode === 'determinate' || this.mode === 'buffer') {\n                this._ngZone.run(() => this.animationEnd.next({ value: this.value }));\n            }\n        };\n        this._isNoopAnimation = _animationMode === 'NoopAnimations';\n        if (defaults) {\n            if (defaults.color) {\n                this.color = this.defaultColor = defaults.color;\n            }\n            this.mode = defaults.mode || this.mode;\n        }\n    }\n    /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n    get value() {\n        return this._value;\n    }\n    set value(v) {\n        this._value = clamp(coerceNumberProperty(v));\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Buffer value of the progress bar. Defaults to zero. */\n    get bufferValue() {\n        return this._bufferValue || 0;\n    }\n    set bufferValue(v) {\n        this._bufferValue = clamp(coerceNumberProperty(v));\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Mode of the progress bar.\n     *\n     * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n     * 'determinate'.\n     * Mirrored to mode attribute.\n     */\n    get mode() {\n        return this._mode;\n    }\n    set mode(value) {\n        // Note that we don't technically need a getter and a setter here,\n        // but we use it to match the behavior of the existing mat-progress-bar.\n        this._mode = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    ngAfterViewInit() {\n        // Run outside angular so change detection didn't get triggered on every transition end\n        // instead only on the animation that we care about (primary value bar's transitionend)\n        this._ngZone.runOutsideAngular(() => {\n            this._elementRef.nativeElement.addEventListener('transitionend', this._transitionendHandler);\n        });\n    }\n    ngOnDestroy() {\n        this._elementRef.nativeElement.removeEventListener('transitionend', this._transitionendHandler);\n    }\n    /** Gets the transform style that should be applied to the primary bar. */\n    _getPrimaryBarTransform() {\n        return `scaleX(${this._isIndeterminate() ? 1 : this.value / 100})`;\n    }\n    /** Gets the `flex-basis` value that should be applied to the buffer bar. */\n    _getBufferBarFlexBasis() {\n        return `${this.mode === 'buffer' ? this.bufferValue : 100}%`;\n    }\n    /** Returns whether the progress bar is indeterminate. */\n    _isIndeterminate() {\n        return this.mode === 'indeterminate' || this.mode === 'query';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatProgressBar, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_PROGRESS_BAR_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatProgressBar, selector: \"mat-progress-bar\", inputs: { color: \"color\", value: \"value\", bufferValue: \"bufferValue\", mode: \"mode\" }, outputs: { animationEnd: \"animationEnd\" }, host: { attributes: { \"role\": \"progressbar\", \"aria-valuemin\": \"0\", \"aria-valuemax\": \"100\", \"tabindex\": \"-1\" }, properties: { \"attr.aria-valuenow\": \"_isIndeterminate() ? null : value\", \"attr.mode\": \"mode\", \"class._mat-animation-noopable\": \"_isNoopAnimation\", \"class.mdc-linear-progress--animation-ready\": \"!_isNoopAnimation\", \"class.mdc-linear-progress--indeterminate\": \"_isIndeterminate()\" }, classAttribute: \"mat-mdc-progress-bar mdc-linear-progress\" }, exportAs: [\"matProgressBar\"], usesInheritance: true, ngImport: i0, template: \"<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-linear-progress__buffer\\\" aria-hidden=\\\"true\\\">\\n  <div\\n    class=\\\"mdc-linear-progress__buffer-bar\\\"\\n    [style.flex-basis]=\\\"_getBufferBarFlexBasis()\\\"></div>\\n  <div class=\\\"mdc-linear-progress__buffer-dots\\\"></div>\\n</div>\\n<div\\n  class=\\\"mdc-linear-progress__bar mdc-linear-progress__primary-bar\\\"\\n  aria-hidden=\\\"true\\\"\\n  [style.transform]=\\\"_getPrimaryBarTransform()\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n<div class=\\\"mdc-linear-progress__bar mdc-linear-progress__secondary-bar\\\" aria-hidden=\\\"true\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n\", styles: [\"@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half))}100%{transform:translateX(var(--mdc-linear-progress-primary-full))}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full))}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-primary-full-neg))}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter-neg))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full-neg))}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}@media screen and (forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden}.mdc-linear-progress__buffer-dots{background-repeat:repeat-x;flex:auto;transform:rotate(180deg);-webkit-mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");animation:mdc-linear-progress-buffering 250ms infinite linear}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__bar{right:0;-webkit-transform-origin:center right;transform-origin:center right}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__buffer-dots,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}.mdc-linear-progress--closed{opacity:0}.mdc-linear-progress--closed-animation-off .mdc-linear-progress__buffer-dots{animation:none}.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar,.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar .mdc-linear-progress__bar-inner{animation:none}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mdc-linear-progress-track-height) * -2.5))}}.mdc-linear-progress__bar-inner{border-color:var(--mdc-linear-progress-active-indicator-color)}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill=''/%3E%3C/svg%3E\\\")}}.mdc-linear-progress{height:max(var(--mdc-linear-progress-track-height), var(--mdc-linear-progress-active-indicator-height))}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress{height:4px}}.mdc-linear-progress__bar{height:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__bar-inner{border-top-width:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__buffer{height:var(--mdc-linear-progress-track-height)}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-size:10px var(--mdc-linear-progress-track-height)}}.mdc-linear-progress__buffer{border-radius:var(--mdc-linear-progress-track-shape)}.mat-mdc-progress-bar{--mdc-linear-progress-active-indicator-height:4px;--mdc-linear-progress-track-height:4px;--mdc-linear-progress-track-shape:0}.mat-mdc-progress-bar{display:block;text-align:left;--mdc-linear-progress-primary-half: 83.67142%;--mdc-linear-progress-primary-full: 200.611057%;--mdc-linear-progress-secondary-quarter: 37.651913%;--mdc-linear-progress-secondary-half: 84.386165%;--mdc-linear-progress-secondary-full: 160.277782%;--mdc-linear-progress-primary-half-neg: -83.67142%;--mdc-linear-progress-primary-full-neg: -200.611057%;--mdc-linear-progress-secondary-quarter-neg: -37.651913%;--mdc-linear-progress-secondary-half-neg: -84.386165%;--mdc-linear-progress-secondary-full-neg: -160.277782%}[dir=rtl] .mat-mdc-progress-bar{text-align:right}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatProgressBar, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-progress-bar', exportAs: 'matProgressBar', host: {\n                        'role': 'progressbar',\n                        'aria-valuemin': '0',\n                        'aria-valuemax': '100',\n                        // set tab index to -1 so screen readers will read the aria-label\n                        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n                        'tabindex': '-1',\n                        '[attr.aria-valuenow]': '_isIndeterminate() ? null : value',\n                        '[attr.mode]': 'mode',\n                        'class': 'mat-mdc-progress-bar mdc-linear-progress',\n                        '[class._mat-animation-noopable]': '_isNoopAnimation',\n                        '[class.mdc-linear-progress--animation-ready]': '!_isNoopAnimation',\n                        '[class.mdc-linear-progress--indeterminate]': '_isIndeterminate()',\n                    }, inputs: ['color'], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-linear-progress__buffer\\\" aria-hidden=\\\"true\\\">\\n  <div\\n    class=\\\"mdc-linear-progress__buffer-bar\\\"\\n    [style.flex-basis]=\\\"_getBufferBarFlexBasis()\\\"></div>\\n  <div class=\\\"mdc-linear-progress__buffer-dots\\\"></div>\\n</div>\\n<div\\n  class=\\\"mdc-linear-progress__bar mdc-linear-progress__primary-bar\\\"\\n  aria-hidden=\\\"true\\\"\\n  [style.transform]=\\\"_getPrimaryBarTransform()\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n<div class=\\\"mdc-linear-progress__bar mdc-linear-progress__secondary-bar\\\" aria-hidden=\\\"true\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n\", styles: [\"@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half))}100%{transform:translateX(var(--mdc-linear-progress-primary-full))}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full))}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-primary-full-neg))}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter-neg))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full-neg))}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}@media screen and (forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden}.mdc-linear-progress__buffer-dots{background-repeat:repeat-x;flex:auto;transform:rotate(180deg);-webkit-mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");animation:mdc-linear-progress-buffering 250ms infinite linear}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__bar{right:0;-webkit-transform-origin:center right;transform-origin:center right}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__buffer-dots,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}.mdc-linear-progress--closed{opacity:0}.mdc-linear-progress--closed-animation-off .mdc-linear-progress__buffer-dots{animation:none}.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar,.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar .mdc-linear-progress__bar-inner{animation:none}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mdc-linear-progress-track-height) * -2.5))}}.mdc-linear-progress__bar-inner{border-color:var(--mdc-linear-progress-active-indicator-color)}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill=''/%3E%3C/svg%3E\\\")}}.mdc-linear-progress{height:max(var(--mdc-linear-progress-track-height), var(--mdc-linear-progress-active-indicator-height))}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress{height:4px}}.mdc-linear-progress__bar{height:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__bar-inner{border-top-width:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__buffer{height:var(--mdc-linear-progress-track-height)}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-size:10px var(--mdc-linear-progress-track-height)}}.mdc-linear-progress__buffer{border-radius:var(--mdc-linear-progress-track-shape)}.mat-mdc-progress-bar{--mdc-linear-progress-active-indicator-height:4px;--mdc-linear-progress-track-height:4px;--mdc-linear-progress-track-shape:0}.mat-mdc-progress-bar{display:block;text-align:left;--mdc-linear-progress-primary-half: 83.67142%;--mdc-linear-progress-primary-full: 200.611057%;--mdc-linear-progress-secondary-quarter: 37.651913%;--mdc-linear-progress-secondary-half: 84.386165%;--mdc-linear-progress-secondary-full: 160.277782%;--mdc-linear-progress-primary-half-neg: -83.67142%;--mdc-linear-progress-primary-full-neg: -200.611057%;--mdc-linear-progress-secondary-quarter-neg: -37.651913%;--mdc-linear-progress-secondary-half-neg: -84.386165%;--mdc-linear-progress-secondary-full-neg: -160.277782%}[dir=rtl] .mat-mdc-progress-bar{text-align:right}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_PROGRESS_BAR_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { value: [{\n                type: Input\n            }], bufferValue: [{\n                type: Input\n            }], animationEnd: [{\n                type: Output\n            }], mode: [{\n                type: Input\n            }] } });\n/** Clamps a value to be between two numbers, by default 0 and 100. */\nfunction clamp(v, min = 0, max = 100) {\n    return Math.max(min, Math.min(max, v));\n}\n\nclass MatProgressBarModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatProgressBarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatProgressBarModule, declarations: [MatProgressBar], exports: [MatProgressBar, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatProgressBarModule, imports: [MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatProgressBarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [MatProgressBar, MatCommonModule],\n                    declarations: [MatProgressBar],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PROGRESS_BAR_DEFAULT_OPTIONS, MAT_PROGRESS_BAR_LOCATION, MAT_PROGRESS_BAR_LOCATION_FACTORY, MatProgressBar, MatProgressBarModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACtK,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,UAAU,EAAEC,eAAe,QAAQ,wBAAwB;AACpE,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,oBAAoB,QAAQ,uBAAuB;;AAE5D;AACA,MAAMC,gCAAgC,GAAG,IAAIhB,cAAc,CAAC,kCAAkC,CAAC;AAC/F;AACA;AACA;AACA;AACA;AACA,MAAMiB,yBAAyB,GAAG,IAAIjB,cAAc,CAAC,2BAA2B,EAAE;EAAEkB,UAAU,EAAE,MAAM;EAAEC,OAAO,EAAEC;AAAkC,CAAC,CAAC;AACrJ;AACA,SAASA,iCAAiCA,CAAA,EAAG;EACzC,MAAMC,SAAS,GAAGpB,MAAM,CAACU,QAAQ,CAAC;EAClC,MAAMW,SAAS,GAAGD,SAAS,GAAGA,SAAS,CAACE,QAAQ,GAAG,IAAI;EACvD,OAAO;IACH;IACA;IACAC,WAAW,EAAEA,CAAA,KAAOF,SAAS,GAAGA,SAAS,CAACG,QAAQ,GAAGH,SAAS,CAACI,MAAM,GAAG;EAC5E,CAAC;AACL;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGf,UAAU,CAAC,MAAM;EACzCgB,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;AACJ,CAAC,EAAE,SAAS,CAAC;AACb,MAAMC,cAAc,SAASH,mBAAmB,CAAC;EAC7CC,WAAWA,CAACG,UAAU,EAAEC,OAAO,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,QAAQ,EAAE;IAC3E,KAAK,CAACJ,UAAU,CAAC;IACjB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAIrC,YAAY,CAAC,CAAC;IACtC,IAAI,CAACsC,KAAK,GAAG,aAAa;IAC1B;IACA,IAAI,CAACC,qBAAqB,GAAIC,KAAK,IAAK;MACpC,IAAI,IAAI,CAACH,YAAY,CAACI,SAAS,CAACC,MAAM,KAAK,CAAC,IACxC,CAACF,KAAK,CAACG,MAAM,IACb,CAACH,KAAK,CAACG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAC,kCAAkC,CAAC,EAAE;QACtE;MACJ;MACA,IAAI,IAAI,CAACC,IAAI,KAAK,aAAa,IAAI,IAAI,CAACA,IAAI,KAAK,QAAQ,EAAE;QACvD,IAAI,CAAChB,OAAO,CAACiB,GAAG,CAAC,MAAM,IAAI,CAACV,YAAY,CAACW,IAAI,CAAC;UAAEC,KAAK,EAAE,IAAI,CAACA;QAAM,CAAC,CAAC,CAAC;MACzE;IACJ,CAAC;IACD,IAAI,CAACf,gBAAgB,GAAGF,cAAc,KAAK,gBAAgB;IAC3D,IAAIC,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACiB,KAAK,EAAE;QAChB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACC,YAAY,GAAGlB,QAAQ,CAACiB,KAAK;MACnD;MACA,IAAI,CAACJ,IAAI,GAAGb,QAAQ,CAACa,IAAI,IAAI,IAAI,CAACA,IAAI;IAC1C;EACJ;EACA;EACA,IAAIG,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACd,MAAM;EACtB;EACA,IAAIc,KAAKA,CAACG,CAAC,EAAE;IACT,IAAI,CAACjB,MAAM,GAAGkB,KAAK,CAACxC,oBAAoB,CAACuC,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACrB,kBAAkB,CAACuB,YAAY,CAAC,CAAC;EAC1C;EACA;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACnB,YAAY,IAAI,CAAC;EACjC;EACA,IAAImB,WAAWA,CAACH,CAAC,EAAE;IACf,IAAI,CAAChB,YAAY,GAAGiB,KAAK,CAACxC,oBAAoB,CAACuC,CAAC,CAAC,CAAC;IAClD,IAAI,CAACrB,kBAAkB,CAACuB,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIR,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACR,KAAK;EACrB;EACA,IAAIQ,IAAIA,CAACG,KAAK,EAAE;IACZ;IACA;IACA,IAAI,CAACX,KAAK,GAAGW,KAAK;IAClB,IAAI,CAAClB,kBAAkB,CAACuB,YAAY,CAAC,CAAC;EAC1C;EACAE,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAAC1B,OAAO,CAAC2B,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC9B,WAAW,CAAC+B,aAAa,CAACC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAACpB,qBAAqB,CAAC;IAChG,CAAC,CAAC;EACN;EACAqB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjC,WAAW,CAAC+B,aAAa,CAACG,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAACtB,qBAAqB,CAAC;EACnG;EACA;EACAuB,uBAAuBA,CAAA,EAAG;IACtB,OAAQ,UAAS,IAAI,CAACC,gBAAgB,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACd,KAAK,GAAG,GAAI,GAAE;EACtE;EACA;EACAe,sBAAsBA,CAAA,EAAG;IACrB,OAAQ,GAAE,IAAI,CAAClB,IAAI,KAAK,QAAQ,GAAG,IAAI,CAACS,WAAW,GAAG,GAAI,GAAE;EAChE;EACA;EACAQ,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACjB,IAAI,KAAK,eAAe,IAAI,IAAI,CAACA,IAAI,KAAK,OAAO;EACjE;EACA;IAAS,IAAI,CAACmB,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFvC,cAAc,EAAxB/B,EAAE,CAAAuE,iBAAA,CAAwCvE,EAAE,CAACwE,UAAU,GAAvDxE,EAAE,CAAAuE,iBAAA,CAAkEvE,EAAE,CAACyE,MAAM,GAA7EzE,EAAE,CAAAuE,iBAAA,CAAwFvE,EAAE,CAAC0E,iBAAiB,GAA9G1E,EAAE,CAAAuE,iBAAA,CAAyHxD,qBAAqB,MAAhJf,EAAE,CAAAuE,iBAAA,CAA2KtD,gCAAgC;IAAA,CAA4D;EAAE;EAC3W;IAAS,IAAI,CAAC0D,IAAI,kBAD8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EACJ9C,cAAc;MAAA+C,SAAA;MAAAC,SAAA,WAA+L,aAAa,mBAAmB,GAAG,mBAAmB,KAAK,cAAc,IAAI;MAAAC,QAAA;MAAAC,YAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADxRnF,EAAE,CAAAqF,WAAA,kBAAAD,GAAA,CAAAlB,gBAAA,YAAAkB,GAAA,CAAAhC,KAAA,UAAAgC,GAAA,CAAAnC,IAAA;UAAFjD,EAAE,CAAAsF,WAAA,4BAAAF,GAAA,CAAA/C,gBAAA,2CAAA+C,GAAA,CAAA/C,gBAAA,wCAAA+C,GAAA,CAAAlB,gBAAA;QAAA;MAAA;MAAAqB,MAAA;QAAAlC,KAAA;QAAAD,KAAA;QAAAM,WAAA;QAAAT,IAAA;MAAA;MAAAuC,OAAA;QAAAhD,YAAA;MAAA;MAAAiD,QAAA;MAAAC,QAAA,GAAF1F,EAAE,CAAA2F,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAb,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFnF,EAAE,CAAAiG,cAAA,YACg7B,CAAC;UADn7BjG,EAAE,CAAAkG,SAAA,YACmiC,CAAC,YAAD,CAAC;UADtiClG,EAAE,CAAAmG,YAAA,CACqmC,CAAC;UADxmCnG,EAAE,CAAAiG,cAAA,YAC8vC,CAAC;UADjwCjG,EAAE,CAAAkG,SAAA,aACwzC,CAAC;UAD3zClG,EAAE,CAAAmG,YAAA,CACg0C,CAAC;UADn0CnG,EAAE,CAAAiG,cAAA,YACk6C,CAAC;UADr6CjG,EAAE,CAAAkG,SAAA,aAC49C,CAAC;UAD/9ClG,EAAE,CAAAmG,YAAA,CACo+C,CAAC;QAAA;QAAA,IAAAhB,EAAA;UADv+CnF,EAAE,CAAAoG,SAAA,EAC4hC,CAAC;UAD/hCpG,EAAE,CAAAqG,WAAA,eAAAjB,GAAA,CAAAjB,sBAAA,EAC4hC,CAAC;UAD/hCnE,EAAE,CAAAoG,SAAA,EAC6vC,CAAC;UADhwCpG,EAAE,CAAAqG,WAAA,cAAAjB,GAAA,CAAAnB,uBAAA,EAC6vC,CAAC;QAAA;MAAA;MAAAqC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAo6U;EAAE;AAC1wX;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGzG,EAAE,CAAA0G,iBAAA,CAGX3E,cAAc,EAAc,CAAC;IAC5G8C,IAAI,EAAEzE,SAAS;IACfuG,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEnB,QAAQ,EAAE,gBAAgB;MAAEoB,IAAI,EAAE;QAC7D,MAAM,EAAE,aAAa;QACrB,eAAe,EAAE,GAAG;QACpB,eAAe,EAAE,KAAK;QACtB;QACA;QACA,UAAU,EAAE,IAAI;QAChB,sBAAsB,EAAE,mCAAmC;QAC3D,aAAa,EAAE,MAAM;QACrB,OAAO,EAAE,0CAA0C;QACnD,iCAAiC,EAAE,kBAAkB;QACrD,8CAA8C,EAAE,mBAAmB;QACnE,4CAA4C,EAAE;MAClD,CAAC;MAAEtB,MAAM,EAAE,CAAC,OAAO,CAAC;MAAEiB,eAAe,EAAEnG,uBAAuB,CAACyG,MAAM;MAAEP,aAAa,EAAEjG,iBAAiB,CAACyG,IAAI;MAAEhB,QAAQ,EAAE,yyBAAyyB;MAAEO,MAAM,EAAE,CAAC,6kUAA6kU;IAAE,CAAC;EACxgW,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEzB,IAAI,EAAE7E,EAAE,CAACwE;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAE7E,EAAE,CAACyE;IAAO,CAAC,EAAE;MAAEI,IAAI,EAAE7E,EAAE,CAAC0E;IAAkB,CAAC,EAAE;MAAEG,IAAI,EAAEmC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC5IpC,IAAI,EAAEtE;MACV,CAAC,EAAE;QACCsE,IAAI,EAAErE,MAAM;QACZmG,IAAI,EAAE,CAAC5F,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAE8D,IAAI,EAAEmC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCpC,IAAI,EAAEtE;MACV,CAAC,EAAE;QACCsE,IAAI,EAAErE,MAAM;QACZmG,IAAI,EAAE,CAAC1F,gCAAgC;MAC3C,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEmC,KAAK,EAAE,CAAC;MACpCyB,IAAI,EAAEpE;IACV,CAAC,CAAC;IAAEiD,WAAW,EAAE,CAAC;MACdmB,IAAI,EAAEpE;IACV,CAAC,CAAC;IAAE+B,YAAY,EAAE,CAAC;MACfqC,IAAI,EAAEnE;IACV,CAAC,CAAC;IAAEuC,IAAI,EAAE,CAAC;MACP4B,IAAI,EAAEpE;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAAS+C,KAAKA,CAACD,CAAC,EAAE2D,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,GAAG,EAAE;EAClC,OAAOC,IAAI,CAACD,GAAG,CAACD,GAAG,EAAEE,IAAI,CAACF,GAAG,CAACC,GAAG,EAAE5D,CAAC,CAAC,CAAC;AAC1C;AAEA,MAAM8D,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAACjD,IAAI,YAAAkD,6BAAAhD,CAAA;MAAA,YAAAA,CAAA,IAAwF+C,oBAAoB;IAAA,CAAkD;EAAE;EAClL;IAAS,IAAI,CAACE,IAAI,kBA7C8EvH,EAAE,CAAAwH,gBAAA;MAAA3C,IAAA,EA6CSwC;IAAoB,EAA+E;EAAE;EAChN;IAAS,IAAI,CAACI,IAAI,kBA9C8EzH,EAAE,CAAA0H,gBAAA;MAAAC,OAAA,GA8CyC7G,eAAe;IAAA,EAAI;EAAE;AACpK;AACA;EAAA,QAAA2F,SAAA,oBAAAA,SAAA,KAhDoGzG,EAAE,CAAA0G,iBAAA,CAgDXW,oBAAoB,EAAc,CAAC;IAClHxC,IAAI,EAAElE,QAAQ;IACdgG,IAAI,EAAE,CAAC;MACCiB,OAAO,EAAE,CAAC7F,cAAc,EAAEjB,eAAe,CAAC;MAC1C+G,YAAY,EAAE,CAAC9F,cAAc;IACjC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASd,gCAAgC,EAAEC,yBAAyB,EAAEG,iCAAiC,EAAEU,cAAc,EAAEsF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}