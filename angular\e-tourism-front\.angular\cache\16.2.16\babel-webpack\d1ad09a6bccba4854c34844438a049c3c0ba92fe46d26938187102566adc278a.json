{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, inject, ViewChild, NgModule, Injector, TemplateRef, Injectable, Optional, SkipSelf } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3$1 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, PortalModule, ComponentPortal, TemplatePortal } from '@angular/cdk/portal';\nimport * as i1 from '@angular/cdk/platform';\nimport { take, takeUntil } from 'rxjs/operators';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport * as i3$2 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { OverlayModule, OverlayConfig } from '@angular/cdk/overlay';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nfunction SimpleSnackBar_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function SimpleSnackBar_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.action());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.data.action, \" \");\n  }\n}\nconst _c0 = [\"label\"];\nfunction MatSnackBarContainer_ng_template_4_Template(rf, ctx) {}\nconst MAX_TIMEOUT = /*#__PURE__*/Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n  constructor(containerInstance, _overlayRef) {\n    this._overlayRef = _overlayRef;\n    /** Subject for notifying the user that the snack bar has been dismissed. */\n    this._afterDismissed = new Subject();\n    /** Subject for notifying the user that the snack bar has opened and appeared. */\n    this._afterOpened = new Subject();\n    /** Subject for notifying the user that the snack bar action was called. */\n    this._onAction = new Subject();\n    /** Whether the snack bar was dismissed using the action button. */\n    this._dismissedByAction = false;\n    this.containerInstance = containerInstance;\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n  /** Dismisses the snack bar. */\n  dismiss() {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /** Marks the snackbar action clicked. */\n  dismissWithAction() {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n      this._onAction.next();\n      this._onAction.complete();\n      this.dismiss();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n  closeWithAction() {\n    this.dismissWithAction();\n  }\n  /** Dismisses the snack bar after some duration */\n  _dismissAfter(duration) {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n  /** Marks the snackbar as opened */\n  _open() {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n  /** Cleans up the DOM after closing. */\n  _finishDismiss() {\n    this._overlayRef.dispose();\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n    this._afterDismissed.next({\n      dismissedByAction: this._dismissedByAction\n    });\n    this._afterDismissed.complete();\n    this._dismissedByAction = false;\n  }\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n  afterDismissed() {\n    return this._afterDismissed;\n  }\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n  afterOpened() {\n    return this.containerInstance._onEnter;\n  }\n  /** Gets an observable that is notified when the snack bar action is called. */\n  onAction() {\n    return this._onAction;\n  }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = /*#__PURE__*/new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n  constructor() {\n    /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n    this.politeness = 'assertive';\n    /**\n     * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n     * component or template, the announcement message will default to the specified message.\n     */\n    this.announcementMessage = '';\n    /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n    this.duration = 0;\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** The horizontal position to place the snack bar. */\n    this.horizontalPosition = 'center';\n    /** The vertical position to place the snack bar. */\n    this.verticalPosition = 'bottom';\n  }\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nlet MatSnackBarLabel = /*#__PURE__*/(() => {\n  class MatSnackBarLabel {\n    static {\n      this.ɵfac = function MatSnackBarLabel_Factory(t) {\n        return new (t || MatSnackBarLabel)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatSnackBarLabel,\n        selectors: [[\"\", \"matSnackBarLabel\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-snack-bar-label\", \"mdc-snackbar__label\"]\n      });\n    }\n  }\n  return MatSnackBarLabel;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nlet MatSnackBarActions = /*#__PURE__*/(() => {\n  class MatSnackBarActions {\n    static {\n      this.ɵfac = function MatSnackBarActions_Factory(t) {\n        return new (t || MatSnackBarActions)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatSnackBarActions,\n        selectors: [[\"\", \"matSnackBarActions\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-snack-bar-actions\", \"mdc-snackbar__actions\"]\n      });\n    }\n  }\n  return MatSnackBarActions;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Directive that should be applied to each of the snack bar's action buttons. */\nlet MatSnackBarAction = /*#__PURE__*/(() => {\n  class MatSnackBarAction {\n    static {\n      this.ɵfac = function MatSnackBarAction_Factory(t) {\n        return new (t || MatSnackBarAction)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatSnackBarAction,\n        selectors: [[\"\", \"matSnackBarAction\", \"\"]],\n        hostAttrs: [1, \"mat-mdc-snack-bar-action\", \"mdc-snackbar__action\"]\n      });\n    }\n  }\n  return MatSnackBarAction;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet SimpleSnackBar = /*#__PURE__*/(() => {\n  class SimpleSnackBar {\n    constructor(snackBarRef, data) {\n      this.snackBarRef = snackBarRef;\n      this.data = data;\n    }\n    /** Performs the action on the snack bar. */\n    action() {\n      this.snackBarRef.dismissWithAction();\n    }\n    /** If the action button should be shown. */\n    get hasAction() {\n      return !!this.data.action;\n    }\n    static {\n      this.ɵfac = function SimpleSnackBar_Factory(t) {\n        return new (t || SimpleSnackBar)(i0.ɵɵdirectiveInject(MatSnackBarRef), i0.ɵɵdirectiveInject(MAT_SNACK_BAR_DATA));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: SimpleSnackBar,\n        selectors: [[\"simple-snack-bar\"]],\n        hostAttrs: [1, \"mat-mdc-simple-snack-bar\"],\n        exportAs: [\"matSnackBar\"],\n        decls: 3,\n        vars: 2,\n        consts: [[\"matSnackBarLabel\", \"\"], [\"matSnackBarActions\", \"\", 4, \"ngIf\"], [\"matSnackBarActions\", \"\"], [\"mat-button\", \"\", \"matSnackBarAction\", \"\", 3, \"click\"]],\n        template: function SimpleSnackBar_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtext(1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(2, SimpleSnackBar_div_2_Template, 3, 1, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.data.message, \"\\n\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.hasAction);\n          }\n        },\n        dependencies: [i2.NgIf, i3.MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n        styles: [\".mat-mdc-simple-snack-bar{display:flex}\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return SimpleSnackBar;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n */\nconst matSnackBarAnimations = {\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: /*#__PURE__*/trigger('state', [/*#__PURE__*/state('void, hidden', /*#__PURE__*/style({\n    transform: 'scale(0.8)',\n    opacity: 0\n  })), /*#__PURE__*/state('visible', /*#__PURE__*/style({\n    transform: 'scale(1)',\n    opacity: 1\n  })), /*#__PURE__*/transition('* => visible', /*#__PURE__*/animate('150ms cubic-bezier(0, 0, 0.2, 1)')), /*#__PURE__*/transition('* => void, * => hidden', /*#__PURE__*/animate('75ms cubic-bezier(0.4, 0.0, 1, 1)', /*#__PURE__*/style({\n    opacity: 0\n  })))])\n};\nlet uniqueId = 0;\n/**\n * Base class for snack bar containers.\n * @docs-private\n */\nlet _MatSnackBarContainerBase = /*#__PURE__*/(() => {\n  class _MatSnackBarContainerBase extends BasePortalOutlet {\n    constructor(_ngZone, _elementRef, _changeDetectorRef, _platform, /** The snack bar configuration. */\n    snackBarConfig) {\n      super();\n      this._ngZone = _ngZone;\n      this._elementRef = _elementRef;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._platform = _platform;\n      this.snackBarConfig = snackBarConfig;\n      this._document = inject(DOCUMENT);\n      this._trackedModals = new Set();\n      /** The number of milliseconds to wait before announcing the snack bar's content. */\n      this._announceDelay = 150;\n      /** Whether the component has been destroyed. */\n      this._destroyed = false;\n      /** Subject for notifying that the snack bar has announced to screen readers. */\n      this._onAnnounce = new Subject();\n      /** Subject for notifying that the snack bar has exited from view. */\n      this._onExit = new Subject();\n      /** Subject for notifying that the snack bar has finished entering the view. */\n      this._onEnter = new Subject();\n      /** The state of the snack bar animations. */\n      this._animationState = 'void';\n      /** Unique ID of the aria-live element. */\n      this._liveElementId = `mat-snack-bar-container-live-${uniqueId++}`;\n      /**\n       * Attaches a DOM portal to the snack bar container.\n       * @deprecated To be turned into a method.\n       * @breaking-change 10.0.0\n       */\n      this.attachDomPortal = portal => {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachDomPortal(portal);\n        this._afterPortalAttached();\n        return result;\n      };\n      // Use aria-live rather than a live role like 'alert' or 'status'\n      // because NVDA and JAWS have show inconsistent behavior with live roles.\n      if (snackBarConfig.politeness === 'assertive' && !snackBarConfig.announcementMessage) {\n        this._live = 'assertive';\n      } else if (snackBarConfig.politeness === 'off') {\n        this._live = 'off';\n      } else {\n        this._live = 'polite';\n      }\n      // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n      // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n      if (this._platform.FIREFOX) {\n        if (this._live === 'polite') {\n          this._role = 'status';\n        }\n        if (this._live === 'assertive') {\n          this._role = 'alert';\n        }\n      }\n    }\n    /** Attach a component portal as content to this snack bar container. */\n    attachComponentPortal(portal) {\n      this._assertNotAttached();\n      const result = this._portalOutlet.attachComponentPortal(portal);\n      this._afterPortalAttached();\n      return result;\n    }\n    /** Attach a template portal as content to this snack bar container. */\n    attachTemplatePortal(portal) {\n      this._assertNotAttached();\n      const result = this._portalOutlet.attachTemplatePortal(portal);\n      this._afterPortalAttached();\n      return result;\n    }\n    /** Handle end of animations, updating the state of the snackbar. */\n    onAnimationEnd(event) {\n      const {\n        fromState,\n        toState\n      } = event;\n      if (toState === 'void' && fromState !== 'void' || toState === 'hidden') {\n        this._completeExit();\n      }\n      if (toState === 'visible') {\n        // Note: we shouldn't use `this` inside the zone callback,\n        // because it can cause a memory leak.\n        const onEnter = this._onEnter;\n        this._ngZone.run(() => {\n          onEnter.next();\n          onEnter.complete();\n        });\n      }\n    }\n    /** Begin animation of snack bar entrance into view. */\n    enter() {\n      if (!this._destroyed) {\n        this._animationState = 'visible';\n        this._changeDetectorRef.detectChanges();\n        this._screenReaderAnnounce();\n      }\n    }\n    /** Begin animation of the snack bar exiting from view. */\n    exit() {\n      // It's common for snack bars to be opened by random outside calls like HTTP requests or\n      // errors. Run inside the NgZone to ensure that it functions correctly.\n      this._ngZone.run(() => {\n        // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n        // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n        // `MatSnackBar.open`).\n        this._animationState = 'hidden';\n        // Mark this element with an 'exit' attribute to indicate that the snackbar has\n        // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n        // test harness.\n        this._elementRef.nativeElement.setAttribute('mat-exit', '');\n        // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n        // long enough to visually read it either, so clear the timeout for announcing.\n        clearTimeout(this._announceTimeoutId);\n      });\n      return this._onExit;\n    }\n    /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n    ngOnDestroy() {\n      this._destroyed = true;\n      this._clearFromModals();\n      this._completeExit();\n    }\n    /**\n     * Waits for the zone to settle before removing the element. Helps prevent\n     * errors where we end up removing an element which is in the middle of an animation.\n     */\n    _completeExit() {\n      this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n        this._ngZone.run(() => {\n          this._onExit.next();\n          this._onExit.complete();\n        });\n      });\n    }\n    /**\n     * Called after the portal contents have been attached. Can be\n     * used to modify the DOM once it's guaranteed to be in place.\n     */\n    _afterPortalAttached() {\n      const element = this._elementRef.nativeElement;\n      const panelClasses = this.snackBarConfig.panelClass;\n      if (panelClasses) {\n        if (Array.isArray(panelClasses)) {\n          // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n          panelClasses.forEach(cssClass => element.classList.add(cssClass));\n        } else {\n          element.classList.add(panelClasses);\n        }\n      }\n      this._exposeToModals();\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live element if there is an\n     * `aria-modal` and the live element is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live element.\n     */\n    _exposeToModals() {\n      // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n      // `LiveAnnouncer` and any other usages.\n      //\n      // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n      // section of the DOM we need to look through. This should cover all the cases we support, but\n      // the selector can be expanded if it turns out to be too narrow.\n      const id = this._liveElementId;\n      const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n      for (let i = 0; i < modals.length; i++) {\n        const modal = modals[i];\n        const ariaOwns = modal.getAttribute('aria-owns');\n        this._trackedModals.add(modal);\n        if (!ariaOwns) {\n          modal.setAttribute('aria-owns', id);\n        } else if (ariaOwns.indexOf(id) === -1) {\n          modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n        }\n      }\n    }\n    /** Clears the references to the live element from any modals it was added to. */\n    _clearFromModals() {\n      this._trackedModals.forEach(modal => {\n        const ariaOwns = modal.getAttribute('aria-owns');\n        if (ariaOwns) {\n          const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n          if (newValue.length > 0) {\n            modal.setAttribute('aria-owns', newValue);\n          } else {\n            modal.removeAttribute('aria-owns');\n          }\n        }\n      });\n      this._trackedModals.clear();\n    }\n    /** Asserts that no content is already attached to the container. */\n    _assertNotAttached() {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Attempting to attach snack bar content after content is already attached');\n      }\n    }\n    /**\n     * Starts a timeout to move the snack bar content to the live region so screen readers will\n     * announce it.\n     */\n    _screenReaderAnnounce() {\n      if (!this._announceTimeoutId) {\n        this._ngZone.runOutsideAngular(() => {\n          this._announceTimeoutId = setTimeout(() => {\n            const inertElement = this._elementRef.nativeElement.querySelector('[aria-hidden]');\n            const liveElement = this._elementRef.nativeElement.querySelector('[aria-live]');\n            if (inertElement && liveElement) {\n              // If an element in the snack bar content is focused before being moved\n              // track it and restore focus after moving to the live region.\n              let focusedElement = null;\n              if (this._platform.isBrowser && document.activeElement instanceof HTMLElement && inertElement.contains(document.activeElement)) {\n                focusedElement = document.activeElement;\n              }\n              inertElement.removeAttribute('aria-hidden');\n              liveElement.appendChild(inertElement);\n              focusedElement?.focus();\n              this._onAnnounce.next();\n              this._onAnnounce.complete();\n            }\n          }, this._announceDelay);\n        });\n      }\n    }\n    static {\n      this.ɵfac = function _MatSnackBarContainerBase_Factory(t) {\n        return new (t || _MatSnackBarContainerBase)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(MatSnackBarConfig));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: _MatSnackBarContainerBase,\n        viewQuery: function _MatSnackBarContainerBase_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n          }\n        },\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return _MatSnackBarContainerBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nlet MatSnackBarContainer = /*#__PURE__*/(() => {\n  class MatSnackBarContainer extends _MatSnackBarContainerBase {\n    /** Applies the correct CSS class to the label based on its content. */\n    _afterPortalAttached() {\n      super._afterPortalAttached();\n      // Check to see if the attached component or template uses the MDC template structure,\n      // specifically the MDC label. If not, the container should apply the MDC label class to this\n      // component's label container, which will apply MDC's label styles to the attached view.\n      const label = this._label.nativeElement;\n      const labelClass = 'mdc-snackbar__label';\n      label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n    }\n    static {\n      this.ɵfac = /* @__PURE__ */function () {\n        let ɵMatSnackBarContainer_BaseFactory;\n        return function MatSnackBarContainer_Factory(t) {\n          return (ɵMatSnackBarContainer_BaseFactory || (ɵMatSnackBarContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatSnackBarContainer)))(t || MatSnackBarContainer);\n        };\n      }();\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatSnackBarContainer,\n        selectors: [[\"mat-snack-bar-container\"]],\n        viewQuery: function MatSnackBarContainer_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 7);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n          }\n        },\n        hostAttrs: [1, \"mdc-snackbar\", \"mat-mdc-snack-bar-container\", \"mdc-snackbar--open\"],\n        hostVars: 1,\n        hostBindings: function MatSnackBarContainer_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵsyntheticHostListener(\"@state.done\", function MatSnackBarContainer_animation_state_done_HostBindingHandler($event) {\n              return ctx.onAnimationEnd($event);\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵsyntheticHostProperty(\"@state\", ctx._animationState);\n          }\n        },\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 6,\n        vars: 3,\n        consts: [[1, \"mdc-snackbar__surface\"], [1, \"mat-mdc-snack-bar-label\"], [\"label\", \"\"], [\"aria-hidden\", \"true\"], [\"cdkPortalOutlet\", \"\"]],\n        template: function MatSnackBarContainer_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1, 2)(3, \"div\", 3);\n            i0.ɵɵtemplate(4, MatSnackBarContainer_ng_template_4_Template, 0, 0, \"ng-template\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(5, \"div\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵattribute(\"aria-live\", ctx._live)(\"role\", ctx._role)(\"id\", ctx._liveElementId);\n          }\n        },\n        dependencies: [i3$1.CdkPortalOutlet],\n        styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;--mdc-snackbar-container-shape:4px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape)}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size);font-family:var(--mdc-snackbar-supporting-text-font);font-weight:var(--mdc-snackbar-supporting-text-weight);line-height:var(--mdc-snackbar-supporting-text-line-height)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-snack-bar-button-color);--mat-mdc-button-persistent-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{background-color:currentColor;opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"],\n        encapsulation: 2,\n        data: {\n          animation: [matSnackBarAnimations.snackBarState]\n        }\n      });\n    }\n  }\n  return MatSnackBarContainer;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSnackBarModule = /*#__PURE__*/(() => {\n  class MatSnackBarModule {\n    static {\n      this.ɵfac = function MatSnackBarModule_Factory(t) {\n        return new (t || MatSnackBarModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatSnackBarModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [OverlayModule, PortalModule, CommonModule, MatButtonModule, MatCommonModule, MatCommonModule]\n      });\n    }\n  }\n  return MatSnackBarModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** @docs-private */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n  return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-snack-bar-default-options', {\n  providedIn: 'root',\n  factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY\n});\nlet _MatSnackBarBase = /*#__PURE__*/(() => {\n  class _MatSnackBarBase {\n    /** Reference to the currently opened snackbar at *any* level. */\n    get _openedSnackBarRef() {\n      const parent = this._parentSnackBar;\n      return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n    }\n    set _openedSnackBarRef(value) {\n      if (this._parentSnackBar) {\n        this._parentSnackBar._openedSnackBarRef = value;\n      } else {\n        this._snackBarRefAtThisLevel = value;\n      }\n    }\n    constructor(_overlay, _live, _injector, _breakpointObserver, _parentSnackBar, _defaultConfig) {\n      this._overlay = _overlay;\n      this._live = _live;\n      this._injector = _injector;\n      this._breakpointObserver = _breakpointObserver;\n      this._parentSnackBar = _parentSnackBar;\n      this._defaultConfig = _defaultConfig;\n      /**\n       * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n       * If there is a parent snack-bar service, all operations should delegate to that parent\n       * via `_openedSnackBarRef`.\n       */\n      this._snackBarRefAtThisLevel = null;\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom component for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param component Component to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromComponent(component, config) {\n      return this._attach(component, config);\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom template for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param template Template to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromTemplate(template, config) {\n      return this._attach(template, config);\n    }\n    /**\n     * Opens a snackbar with a message and an optional action.\n     * @param message The message to show in the snackbar.\n     * @param action The label for the snackbar action.\n     * @param config Additional configuration options for the snackbar.\n     */\n    open(message, action = '', config) {\n      const _config = {\n        ...this._defaultConfig,\n        ...config\n      };\n      // Since the user doesn't have access to the component, we can\n      // override the data to pass in our own message and action.\n      _config.data = {\n        message,\n        action\n      };\n      // Since the snack bar has `role=\"alert\"`, we don't\n      // want to announce the same message twice.\n      if (_config.announcementMessage === message) {\n        _config.announcementMessage = undefined;\n      }\n      return this.openFromComponent(this.simpleSnackBarComponent, _config);\n    }\n    /**\n     * Dismisses the currently-visible snack bar.\n     */\n    dismiss() {\n      if (this._openedSnackBarRef) {\n        this._openedSnackBarRef.dismiss();\n      }\n    }\n    ngOnDestroy() {\n      // Only dismiss the snack bar at the current level on destroy.\n      if (this._snackBarRefAtThisLevel) {\n        this._snackBarRefAtThisLevel.dismiss();\n      }\n    }\n    /**\n     * Attaches the snack bar container component to the overlay.\n     */\n    _attachSnackBarContainer(overlayRef, config) {\n      const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n      const injector = Injector.create({\n        parent: userInjector || this._injector,\n        providers: [{\n          provide: MatSnackBarConfig,\n          useValue: config\n        }]\n      });\n      const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n      const containerRef = overlayRef.attach(containerPortal);\n      containerRef.instance.snackBarConfig = config;\n      return containerRef.instance;\n    }\n    /**\n     * Places a new component or a template as the content of the snack bar container.\n     */\n    _attach(content, userConfig) {\n      const config = {\n        ...new MatSnackBarConfig(),\n        ...this._defaultConfig,\n        ...userConfig\n      };\n      const overlayRef = this._createOverlay(config);\n      const container = this._attachSnackBarContainer(overlayRef, config);\n      const snackBarRef = new MatSnackBarRef(container, overlayRef);\n      if (content instanceof TemplateRef) {\n        const portal = new TemplatePortal(content, null, {\n          $implicit: config.data,\n          snackBarRef\n        });\n        snackBarRef.instance = container.attachTemplatePortal(portal);\n      } else {\n        const injector = this._createInjector(config, snackBarRef);\n        const portal = new ComponentPortal(content, undefined, injector);\n        const contentRef = container.attachComponentPortal(portal);\n        // We can't pass this via the injector, because the injector is created earlier.\n        snackBarRef.instance = contentRef.instance;\n      }\n      // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n      // appropriate. This class is applied to the overlay element because the overlay must expand to\n      // fill the width of the screen for full width snackbars.\n      this._breakpointObserver.observe(Breakpoints.HandsetPortrait).pipe(takeUntil(overlayRef.detachments())).subscribe(state => {\n        overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n      });\n      if (config.announcementMessage) {\n        // Wait until the snack bar contents have been announced then deliver this message.\n        container._onAnnounce.subscribe(() => {\n          this._live.announce(config.announcementMessage, config.politeness);\n        });\n      }\n      this._animateSnackBar(snackBarRef, config);\n      this._openedSnackBarRef = snackBarRef;\n      return this._openedSnackBarRef;\n    }\n    /** Animates the old snack bar out and the new one in. */\n    _animateSnackBar(snackBarRef, config) {\n      // When the snackbar is dismissed, clear the reference to it.\n      snackBarRef.afterDismissed().subscribe(() => {\n        // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n        if (this._openedSnackBarRef == snackBarRef) {\n          this._openedSnackBarRef = null;\n        }\n        if (config.announcementMessage) {\n          this._live.clear();\n        }\n      });\n      if (this._openedSnackBarRef) {\n        // If a snack bar is already in view, dismiss it and enter the\n        // new snack bar after exit animation is complete.\n        this._openedSnackBarRef.afterDismissed().subscribe(() => {\n          snackBarRef.containerInstance.enter();\n        });\n        this._openedSnackBarRef.dismiss();\n      } else {\n        // If no snack bar is in view, enter the new snack bar.\n        snackBarRef.containerInstance.enter();\n      }\n      // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n      if (config.duration && config.duration > 0) {\n        snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n      }\n    }\n    /**\n     * Creates a new overlay and places it in the correct location.\n     * @param config The user-specified snack bar config.\n     */\n    _createOverlay(config) {\n      const overlayConfig = new OverlayConfig();\n      overlayConfig.direction = config.direction;\n      let positionStrategy = this._overlay.position().global();\n      // Set horizontal position.\n      const isRtl = config.direction === 'rtl';\n      const isLeft = config.horizontalPosition === 'left' || config.horizontalPosition === 'start' && !isRtl || config.horizontalPosition === 'end' && isRtl;\n      const isRight = !isLeft && config.horizontalPosition !== 'center';\n      if (isLeft) {\n        positionStrategy.left('0');\n      } else if (isRight) {\n        positionStrategy.right('0');\n      } else {\n        positionStrategy.centerHorizontally();\n      }\n      // Set horizontal position.\n      if (config.verticalPosition === 'top') {\n        positionStrategy.top('0');\n      } else {\n        positionStrategy.bottom('0');\n      }\n      overlayConfig.positionStrategy = positionStrategy;\n      return this._overlay.create(overlayConfig);\n    }\n    /**\n     * Creates an injector to be used inside of a snack bar component.\n     * @param config Config that was used to create the snack bar.\n     * @param snackBarRef Reference to the snack bar.\n     */\n    _createInjector(config, snackBarRef) {\n      const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n      return Injector.create({\n        parent: userInjector || this._injector,\n        providers: [{\n          provide: MatSnackBarRef,\n          useValue: snackBarRef\n        }, {\n          provide: MAT_SNACK_BAR_DATA,\n          useValue: config.data\n        }]\n      });\n    }\n    static {\n      this.ɵfac = function _MatSnackBarBase_Factory(t) {\n        return new (t || _MatSnackBarBase)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i2$1.LiveAnnouncer), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i3$2.BreakpointObserver), i0.ɵɵinject(_MatSnackBarBase, 12), i0.ɵɵinject(MAT_SNACK_BAR_DEFAULT_OPTIONS));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: _MatSnackBarBase,\n        factory: _MatSnackBarBase.ɵfac\n      });\n    }\n  }\n  return _MatSnackBarBase;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nlet MatSnackBar = /*#__PURE__*/(() => {\n  class MatSnackBar extends _MatSnackBarBase {\n    constructor(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig) {\n      super(overlay, live, injector, breakpointObserver, parentSnackBar, defaultConfig);\n      this.simpleSnackBarComponent = SimpleSnackBar;\n      this.snackBarContainerComponent = MatSnackBarContainer;\n      this.handsetCssClass = 'mat-mdc-snack-bar-handset';\n    }\n    static {\n      this.ɵfac = function MatSnackBar_Factory(t) {\n        return new (t || MatSnackBar)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i2$1.LiveAnnouncer), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i3$2.BreakpointObserver), i0.ɵɵinject(MatSnackBar, 12), i0.ɵɵinject(MAT_SNACK_BAR_DEFAULT_OPTIONS));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: MatSnackBar,\n        factory: MatSnackBar.ɵfac,\n        providedIn: MatSnackBarModule\n      });\n    }\n  }\n  return MatSnackBar;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, _MatSnackBarBase, _MatSnackBarContainerBase, matSnackBarAnimations };\n//# sourceMappingURL=snack-bar.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}