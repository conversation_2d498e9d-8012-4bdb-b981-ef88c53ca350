{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../navbar/navbar.component\";\nexport let MainLayoutComponent = /*#__PURE__*/(() => {\n  class MainLayoutComponent {\n    static {\n      this.ɵfac = function MainLayoutComponent_Factory(t) {\n        return new (t || MainLayoutComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MainLayoutComponent,\n        selectors: [[\"app-main-layout\"]],\n        decls: 5,\n        vars: 0,\n        consts: [[1, \"main-layout\"], [1, \"main-content\"], [1, \"content-container\"]],\n        template: function MainLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵelement(1, \"app-navbar\");\n            i0.ɵɵelementStart(2, \"main\", 1)(3, \"div\", 2);\n            i0.ɵɵelement(4, \"router-outlet\");\n            i0.ɵɵelementEnd()()();\n          }\n        },\n        dependencies: [i1.RouterOutlet, i2.NavbarComponent],\n        styles: [\".main-layout[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:100vh;width:100%;position:relative}.main-content[_ngcontent-%COMP%]{flex:1;padding-top:64px}.content-container[_ngcontent-%COMP%]{padding:var(--spacing-lg);max-width:1200px;margin:0 auto;width:100%}@media (max-width: 768px){.content-container[_ngcontent-%COMP%]{padding:var(--spacing-md)}}\"]\n      });\n    }\n  }\n  return MainLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}