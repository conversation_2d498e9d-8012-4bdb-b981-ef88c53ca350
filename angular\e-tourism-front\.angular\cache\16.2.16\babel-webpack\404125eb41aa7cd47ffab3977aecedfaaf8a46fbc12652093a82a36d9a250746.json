{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewEncapsulation } from '@angular/core';\nimport { FormGroup, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\nexport let SearchPriceComponent = class SearchPriceComponent {\n  constructor(fb, productService, router) {\n    this.fb = fb;\n    this.productService = productService;\n    this.router = router;\n    this.departureLocations = [];\n    this.arrivalLocations = [];\n    this.isLoading = false;\n    this.searchResults = [];\n    this.hasSearched = false;\n    this.errorMessage = '';\n    this.lastSearchId = '';\n    // Passenger type options\n    this.passengerTypes = [{\n      value: PassengerType.Adult,\n      label: 'Adult'\n    }, {\n      value: PassengerType.Child,\n      label: 'Child'\n    }, {\n      value: PassengerType.Infant,\n      label: 'Infant'\n    }];\n    // Flight class options\n    this.flightClasses = [{\n      value: FlightClassType.PROMO,\n      label: 'Promo'\n    }, {\n      value: FlightClassType.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClassType.BUSINESS,\n      label: 'Business'\n    }];\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required],\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required],\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required],\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required],\n      // Options de vol\n      flightClass: [0, Validators.required],\n      nonStop: [false],\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n  ngOnInit() {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight) {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n    header.appendChild(logo);\n    header.appendChild(title);\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n        generalInfo.appendChild(airlineInfo);\n      }\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n      routeSection.appendChild(routeVisual);\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {\n              hour: '2-digit',\n              minute: '2-digit'\n            });\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n          segmentsList.appendChild(segmentItem);\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n            segmentsList.appendChild(layover);\n          }\n        });\n        routeSection.appendChild(segmentsList);\n      }\n    }\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n        // ID de l'offre\n        const offerId = this.createInfoRow('Offer ID', offer.offerId || offer.id || 'N/A');\n        offerId.style.gridColumn = '1 / -1';\n        offerDetails.appendChild(offerId);\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n        offerItem.appendChild(offerDetails);\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n          const baggageList = document.createElement('ul');\n          baggageList.style.listStyle = 'none';\n          baggageList.style.padding = '0';\n          baggageList.style.margin = '0';\n          offer.baggageInformations.forEach(baggage => {\n            const baggageItem = document.createElement('li');\n            baggageItem.style.marginBottom = '5px';\n            baggageItem.innerHTML = `<i class=\"fas fa-suitcase\" style=\"color: #666; margin-right: 8px;\"></i> ${this.getBaggageTypeName(baggage.baggageType)}`;\n            baggageList.appendChild(baggageItem);\n          });\n          offerItem.appendChild(baggageList);\n        }\n        offersList.appendChild(offerItem);\n      });\n      offersSection.appendChild(offersList);\n    }\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n  // Méthode utilitaire pour créer une section\n  createSection(title, iconClass) {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n    return section;\n  }\n  // Méthode utilitaire pour créer une ligne d'information\n  createInfoRow(label, value) {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n    return row;\n  }\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight) {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n  preloadLocations() {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n  setupAutocomplete() {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.departureLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('departureLocation')?.setValue('');\n      });\n    });\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges.subscribe(locationType => {\n      this.productService.getLocationsByType(locationType).subscribe(locations => {\n        this.arrivalLocations = locations;\n        // Réinitialiser la sélection de localisation\n        this.searchForm.get('arrivalLocation')?.setValue('');\n      });\n    });\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n        // Si l'utilisateur tape quelque chose, filtrer les résultats\n        if (value.length > 0) {\n          // Filtrer les résultats par type et par texte\n          return this.productService.getLocationsByType(locationType).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(value.toLowerCase()) || location.code && location.code.toLowerCase().includes(value.toLowerCase()))));\n        } else {\n          // Si le champ est vide, afficher toutes les options du type sélectionné\n          return this.productService.getLocationsByType(locationType);\n        }\n      }\n      return of([]);\n    })).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n  }\n  displayLocation(location) {\n    if (!location) return '';\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n  onSearch() {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n    const formValue = this.searchForm.value;\n    // Créer la requête de recherche entièrement dynamique\n    const request = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [{\n        id: formValue.departureLocation?.id || '',\n        type: formValue.departureLocationType\n      }],\n      ArrivalLocations: [{\n        id: formValue.arrivalLocation?.id || '',\n        type: formValue.arrivalLocationType\n      }],\n      Passengers: [{\n        type: formValue.passengerType,\n        count: formValue.passengerCount\n      }],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n    this.productService.searchPrice(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.header.success) {\n          this.searchResults = response.body.flights;\n          // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n          console.log('Full API Response:', JSON.stringify(response, null, 2));\n          // Analyser les données de disponibilité\n          if (response.body && response.body.flights && response.body.flights.length > 0) {\n            console.group('Availability Analysis');\n            console.log('Total flights:', response.body.flights.length);\n            // Compter les vols avec des offres\n            const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n            console.log('Flights with offers:', flightsWithOffers.length);\n            // Analyser les valeurs de disponibilité\n            const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o => o.availability !== undefined ? o.availability : o.seatInfo ? o.seatInfo.availableSeatCount : 0));\n            console.log('Availability values:', availabilityValues);\n            // Compter les différentes valeurs de disponibilité\n            const availabilityCounts = availabilityValues.reduce((acc, val) => {\n              if (val !== undefined) {\n                acc[val] = (acc[val] || 0) + 1;\n              }\n              return acc;\n            }, {});\n            console.log('Availability counts:', availabilityCounts);\n            // Vérifier les vols réservables\n            const reservableFlights = flightsWithOffers.filter(f => f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true));\n            console.log('Reservable flights:', reservableFlights.length);\n            console.groupEnd();\n          }\n          // Vérifier si searchId existe dans le corps de la réponse\n          if (response.body && response.body.searchId) {\n            this.lastSearchId = response.body.searchId;\n            console.log('Search ID found in body.searchId:', this.lastSearchId);\n          }\n          // Vérifier si searchId existe dans l'en-tête de la réponse\n          else if (response.header && response.header.requestId) {\n            this.lastSearchId = response.header.requestId;\n            console.log('Search ID found in header.requestId:', this.lastSearchId);\n          }\n          // Vérifier d'autres emplacements possibles\n          else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n            this.lastSearchId = response.body.flights[0].id;\n            console.log('Using flight ID as search ID:', this.lastSearchId);\n          } else {\n            console.error('No search ID found in the response!');\n            console.log('Response structure:', Object.keys(response));\n            if (response.body) console.log('Body structure:', Object.keys(response.body));\n            if (response.header) console.log('Header structure:', Object.keys(response.header));\n          }\n        } else {\n          this.errorMessage = 'The search could not be completed. Please try again.';\n          if (response.header.messages && response.header.messages.length > 0) {\n            this.errorMessage = response.header.messages[0].message;\n          }\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = 'An error occurred during the search. Please try again.';\n        console.error('Error searching flights:', error);\n      }\n    });\n  }\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  // Formater la durée en heures et minutes\n  formatDuration(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n  // Formater la date pour l'affichage\n  formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight) {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n    const minOffer = flight.offers.reduce((min, offer) => offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight) {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability : offer.seatInfo ? offer.seatInfo.availableSeatCount : 0;\n    return availabilityValue > 0;\n  }\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations() {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations() {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation');\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations() {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString) {\n    if (!dateString) return 'N/A';\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType) {\n    switch (baggageType) {\n      case 1:\n        return 'Cabin Baggage';\n      case 2:\n        return 'Checked Baggage';\n      case 3:\n        return 'Hand Baggage';\n      default:\n        return 'Baggage';\n    }\n  }\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType) {\n    switch (passengerType) {\n      case 1:\n        return 'Adult';\n      case 2:\n        return 'Child';\n      case 3:\n        return 'Infant';\n      default:\n        return 'Passenger';\n    }\n  }\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment, nextSegment) {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date || !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n};\nSearchPriceComponent = __decorate([Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css'],\n  encapsulation: ViewEncapsulation.None\n})], SearchPriceComponent);", "map": {"version": 3, "names": ["Component", "ViewEncapsulation", "FormGroup", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "of", "FlightClassType", "LocationType", "PassengerType", "SearchPriceComponent", "constructor", "fb", "productService", "router", "departureLocations", "arrivalLocations", "isLoading", "searchResults", "hasSearched", "errorMessage", "lastSearchId", "passengerTypes", "value", "Adult", "label", "Child", "Infant", "flightClasses", "PROMO", "ECONOMY", "BUSINESS", "minDate", "Date", "toISOString", "split", "searchForm", "group", "productType", "required", "serviceTypes", "departureLocation", "departureLocationType", "arrivalLocation", "arrivalLocationType", "departureDate", "passengerCount", "min", "max", "passengerType", "flightClass", "nonStop", "culture", "currency", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightBaggageGetOption", "ngOnInit", "setupAutocomplete", "preloadLocations", "console", "log", "showAllDetails", "flight", "modalDiv", "document", "createElement", "style", "position", "top", "left", "width", "height", "backgroundColor", "zIndex", "display", "justifyContent", "alignItems", "modalContent", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "color", "fontFamily", "closeButton", "innerHTML", "right", "border", "background", "fontSize", "cursor", "transition", "on<PERSON><PERSON>ver", "onmouseout", "onclick", "body", "<PERSON><PERSON><PERSON><PERSON>", "header", "marginBottom", "paddingBottom", "borderBottom", "logo", "title", "textContent", "margin", "fontWeight", "append<PERSON><PERSON><PERSON>", "detailsContainer", "flexDirection", "gap", "generalInfo", "createSection", "items", "length", "item", "airline", "airlineInfo", "thumbnailFull", "airlineLogo", "src", "alt", "name", "marginRight", "airlineIcon", "airlineName", "internationalCode", "flightNumberRow", "createInfoRow", "flightNo", "flightDateRow", "flightDate", "toLocaleDateString", "durationRow", "formatDuration", "duration", "classRow", "code", "stopsRow", "stopCount", "routeSection", "routeVisual", "departure", "textAlign", "flex", "departureTime", "date", "toLocaleTimeString", "hour", "minute", "departureAirport", "marginTop", "airport", "departureCity", "city", "connectionLine", "line", "plane", "marginLeft", "arrival", "arrivalTime", "arrivalAirport", "arrivalCity", "segments", "segmentsTitle", "segmentsList", "for<PERSON>ach", "segment", "index", "segmentItem", "segmentHeader", "segmentTitle", "segmentDuration", "segmentRoute", "segmentDeparture", "depTime", "depAirport", "arrow", "segmentArrival", "arrTime", "arrAirport", "layover", "currentArrival", "getTime", "nextDeparture", "layoverTime", "Math", "floor", "offersSection", "offers", "offersList", "offer", "offerItem", "offerHeader", "offerTitle", "offerPrice", "price", "amount", "offerDetails", "gridTemplateColumns", "offerId", "id", "gridColumn", "availabilityValue", "availability", "undefined", "seatInfo", "availableSeatCount", "expiresOn", "expires", "toLocaleString", "brandedFare", "reservableInfo", "reservable", "baggageInformations", "baggageTitle", "baggageList", "listStyle", "baggage", "baggageItem", "getBaggageTypeName", "baggageType", "services", "servicesSection", "servicesList", "service", "serviceItem", "iconClass", "section", "section<PERSON><PERSON><PERSON>", "icon", "className", "sectionTitle", "row", "labelElement", "valueElement", "selectThisFlight", "searchId", "navigate", "queryParams", "error", "get", "getLocationsByType", "subscribe", "locations", "valueChanges", "locationType", "setValue", "pipe", "filter", "location", "toLowerCase", "includes", "displayLocation", "displayText", "type", "Airport", "onSearch", "invalid", "markFormGroupTouched", "formValue", "request", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "ArrivalLocations", "Passengers", "count", "showOnlyNonStopFlight", "additionalParameters", "getOptionsParameters", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "searchPrice", "next", "response", "success", "flights", "JSON", "stringify", "flightsWithOffers", "f", "availabilityValues", "flatMap", "o", "availabilityCounts", "reduce", "acc", "val", "reservableFlights", "some", "groupEnd", "requestId", "Object", "keys", "messages", "message", "formGroup", "values", "controls", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "minutes", "hours", "mins", "formatDate", "dateString", "weekday", "day", "month", "getMinPrice", "min<PERSON>ffer", "formattedAmount", "isFlightAvailable", "showAllDepartureLocations", "input", "getElementById", "focus", "dispatchEvent", "Event", "showAllArrivalLocations", "swapLocations", "patchValue", "formatExpirationDate", "getPassengerTypeName", "calculateLayoverTime", "currentSegment", "nextSegment", "diffMs", "diffMins", "__decorate", "selector", "templateUrl", "styleUrls", "encapsulation", "None"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\components\\product\\search-price\\search-price.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { debounceTime, distinctUntilChanged, switchMap, map } from 'rxjs/operators';\nimport { Observable, of } from 'rxjs';\nimport { ProductService } from '../../../services/product.service';\nimport { LocationOption } from '../../../models/price-search-request.model';\nimport { PriceSearchRequest } from '../../../models/price-search-request.model';\nimport { PriceSearchResponse, Flight } from '../../../models/price-search-response.model';\nimport { FlightClassType, LocationType, PassengerType } from '../../../models/enums.model';\n\n@Component({\n  selector: 'app-search-price',\n  templateUrl: './search-price.component.html',\n  styleUrls: ['./search-price.component.css', './search-card.css'],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchPriceComponent implements OnInit {\n  searchForm: FormGroup;\n  departureLocations: LocationOption[] = [];\n  arrivalLocations: LocationOption[] = [];\n  isLoading = false;\n  searchResults: Flight[] = [];\n  hasSearched = false;\n  errorMessage = '';\n  lastSearchId = '';\n\n  // Passenger type options\n  passengerTypes = [\n    { value: PassengerType.Adult, label: 'Adult' },\n    { value: PassengerType.Child, label: 'Child' },\n    { value: PassengerType.Infant, label: 'Infant' }\n  ];\n\n  // Flight class options\n  flightClasses = [\n    { value: FlightClassType.PROMO, label: 'Promo' },\n    { value: FlightClassType.ECONOMY, label: 'Economy' },\n    { value: FlightClassType.BUSINESS, label: 'Business' }\n  ];\n\n  // Date minimale (aujourd'hui)\n  minDate: string;\n\n  constructor(\n    private fb: FormBuilder,\n    private productService: ProductService,\n    private router: Router\n  ) {\n    // Initialiser la date minimale à aujourd'hui\n    this.minDate = new Date().toISOString().split('T')[0];\n\n    // Initialiser le formulaire avec tous les champs dynamiques\n    this.searchForm = this.fb.group({\n      // Champs principaux\n      productType: [3, Validators.required], // Flight par défaut\n      serviceTypes: [['1'], Validators.required],\n      departureLocation: ['', Validators.required],\n      departureLocationType: [2, Validators.required], // Type 2 (City) par défaut\n      arrivalLocation: ['', Validators.required],\n      arrivalLocationType: [5, Validators.required], // Type 5 (Airport) par défaut\n      departureDate: [this.minDate, Validators.required],\n      passengerCount: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      passengerType: [1, Validators.required], // Adult par défaut\n\n      // Options de vol\n      flightClass: [0, Validators.required], // PROMO par défaut\n      nonStop: [false],\n\n      // Options avancées\n      culture: ['en-US'],\n      currency: ['EUR'],\n      acceptPendingProviders: [false],\n      forceFlightBundlePackage: [false],\n      disablePackageOfferTotalPrice: [true],\n      calculateFlightFees: [false],\n\n      // Options de bagages\n      flightBaggageGetOption: [0]\n    });\n  }\n\n  ngOnInit(): void {\n    // Configurer les observables pour l'autocomplétion\n    this.setupAutocomplete();\n\n    // Pré-remplir les emplacements de départ et d'arrivée selon l'exemple\n    this.preloadLocations();\n\n    // Ajouter un écouteur pour les messages de la console\n    console.log('SearchPriceComponent initialized - Debugging availability issues');\n  }\n\n  // Méthode pour afficher tous les détails du vol avec un design professionnel\n  showAllDetails(flight: Flight): void {\n    // Créer une fenêtre modale pour afficher les détails\n    const modalDiv = document.createElement('div');\n    modalDiv.style.position = 'fixed';\n    modalDiv.style.top = '0';\n    modalDiv.style.left = '0';\n    modalDiv.style.width = '100%';\n    modalDiv.style.height = '100%';\n    modalDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';\n    modalDiv.style.zIndex = '1000';\n    modalDiv.style.display = 'flex';\n    modalDiv.style.justifyContent = 'center';\n    modalDiv.style.alignItems = 'center';\n\n    // Créer le contenu de la fenêtre modale\n    const modalContent = document.createElement('div');\n    modalContent.style.backgroundColor = 'white';\n    modalContent.style.padding = '30px';\n    modalContent.style.borderRadius = '12px';\n    modalContent.style.maxWidth = '90%';\n    modalContent.style.maxHeight = '90%';\n    modalContent.style.overflow = 'auto';\n    modalContent.style.position = 'relative';\n    modalContent.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';\n    modalContent.style.color = '#333';\n    modalContent.style.fontFamily = 'Arial, sans-serif';\n\n    // Ajouter un bouton de fermeture\n    const closeButton = document.createElement('button');\n    closeButton.innerHTML = '&times;';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '15px';\n    closeButton.style.right = '20px';\n    closeButton.style.border = 'none';\n    closeButton.style.background = 'none';\n    closeButton.style.fontSize = '28px';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.color = '#2989d8';\n    closeButton.style.transition = 'color 0.3s';\n    closeButton.onmouseover = () => closeButton.style.color = '#1e5799';\n    closeButton.onmouseout = () => closeButton.style.color = '#2989d8';\n    closeButton.onclick = () => document.body.removeChild(modalDiv);\n\n    // Ajouter un en-tête avec logo et titre\n    const header = document.createElement('div');\n    header.style.display = 'flex';\n    header.style.alignItems = 'center';\n    header.style.marginBottom = '25px';\n    header.style.paddingBottom = '15px';\n    header.style.borderBottom = '1px solid #eee';\n\n    const logo = document.createElement('div');\n    logo.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #2989d8; margin-right: 15px;\"></i>';\n\n    const title = document.createElement('h2');\n    title.textContent = 'Complete Flight Details';\n    title.style.margin = '0';\n    title.style.fontSize = '24px';\n    title.style.fontWeight = '600';\n    title.style.color = '#2989d8';\n\n    header.appendChild(logo);\n    header.appendChild(title);\n\n    // Créer le conteneur principal\n    const detailsContainer = document.createElement('div');\n    detailsContainer.style.display = 'flex';\n    detailsContainer.style.flexDirection = 'column';\n    detailsContainer.style.gap = '25px';\n\n    // 1. Section d'informations générales sur le vol\n    const generalInfo = this.createSection('Flight Information', 'fa-info-circle');\n\n    // Ajouter les informations de base du vol\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Informations de la compagnie aérienne\n      if (item.airline) {\n        const airlineInfo = document.createElement('div');\n        airlineInfo.style.display = 'flex';\n        airlineInfo.style.alignItems = 'center';\n        airlineInfo.style.marginBottom = '15px';\n\n        // Logo de la compagnie\n        if (item.airline.thumbnailFull) {\n          const airlineLogo = document.createElement('img');\n          airlineLogo.src = item.airline.thumbnailFull;\n          airlineLogo.alt = item.airline.name;\n          airlineLogo.style.height = '40px';\n          airlineLogo.style.marginRight = '15px';\n          airlineInfo.appendChild(airlineLogo);\n        } else {\n          const airlineIcon = document.createElement('div');\n          airlineIcon.innerHTML = '<i class=\"fas fa-plane\" style=\"font-size: 24px; color: #666; margin-right: 15px;\"></i>';\n          airlineInfo.appendChild(airlineIcon);\n        }\n\n        const airlineName = document.createElement('div');\n        airlineName.innerHTML = `<strong>${item.airline.name}</strong> (${item.airline.internationalCode})`;\n        airlineName.style.fontSize = '18px';\n        airlineInfo.appendChild(airlineName);\n\n        generalInfo.appendChild(airlineInfo);\n      }\n\n      // Numéro de vol et type\n      const flightNumberRow = this.createInfoRow('Flight Number', item.flightNo || 'N/A');\n      generalInfo.appendChild(flightNumberRow);\n\n      // Date du vol\n      const flightDateRow = this.createInfoRow('Flight Date', new Date(item.flightDate).toLocaleDateString());\n      generalInfo.appendChild(flightDateRow);\n\n      // Durée du vol\n      const durationRow = this.createInfoRow('Duration', this.formatDuration(item.duration));\n      generalInfo.appendChild(durationRow);\n\n      // Classe de vol\n      if (item.flightClass) {\n        const classRow = this.createInfoRow('Class', `${item.flightClass.name} (${item.flightClass.code})`);\n        generalInfo.appendChild(classRow);\n      }\n\n      // Nombre d'escales\n      const stopsRow = this.createInfoRow('Stops', item.stopCount === 0 ? 'Direct Flight' : `${item.stopCount} stop(s)`);\n      generalInfo.appendChild(stopsRow);\n    }\n\n    // 2. Section d'itinéraire\n    const routeSection = this.createSection('Route Details', 'fa-route');\n\n    if (flight.items && flight.items.length > 0) {\n      const item = flight.items[0];\n\n      // Créer la visualisation de l'itinéraire\n      const routeVisual = document.createElement('div');\n      routeVisual.style.display = 'flex';\n      routeVisual.style.alignItems = 'center';\n      routeVisual.style.justifyContent = 'space-between';\n      routeVisual.style.margin = '20px 0';\n      routeVisual.style.position = 'relative';\n\n      // Départ\n      const departure = document.createElement('div');\n      departure.style.textAlign = 'center';\n      departure.style.flex = '1';\n\n      if (item.departure) {\n        const departureTime = document.createElement('div');\n        departureTime.style.fontSize = '22px';\n        departureTime.style.fontWeight = 'bold';\n        departureTime.textContent = new Date(item.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const departureAirport = document.createElement('div');\n        departureAirport.style.fontSize = '16px';\n        departureAirport.style.marginTop = '5px';\n        departureAirport.innerHTML = `<strong>${item.departure.airport?.code || 'N/A'}</strong>`;\n\n        const departureCity = document.createElement('div');\n        departureCity.style.fontSize = '14px';\n        departureCity.style.color = '#666';\n        departureCity.textContent = item.departure.city?.name || 'N/A';\n\n        departure.appendChild(departureTime);\n        departure.appendChild(departureAirport);\n        departure.appendChild(departureCity);\n      }\n\n      // Ligne de connexion\n      const connectionLine = document.createElement('div');\n      connectionLine.style.flex = '2';\n      connectionLine.style.display = 'flex';\n      connectionLine.style.alignItems = 'center';\n      connectionLine.style.justifyContent = 'center';\n      connectionLine.style.padding = '0 20px';\n\n      const line = document.createElement('div');\n      line.style.height = '2px';\n      line.style.backgroundColor = '#ddd';\n      line.style.width = '100%';\n      line.style.position = 'relative';\n\n      const plane = document.createElement('div');\n      plane.innerHTML = '<i class=\"fas fa-plane\" style=\"color: #2989d8; font-size: 18px; transform: rotate(90deg);\"></i>';\n      plane.style.position = 'absolute';\n      plane.style.top = '-9px';\n      plane.style.left = '50%';\n      plane.style.marginLeft = '-9px';\n      plane.style.backgroundColor = 'white';\n      plane.style.padding = '0 5px';\n\n      line.appendChild(plane);\n      connectionLine.appendChild(line);\n\n      // Arrivée\n      const arrival = document.createElement('div');\n      arrival.style.textAlign = 'center';\n      arrival.style.flex = '1';\n\n      if (item.arrival) {\n        const arrivalTime = document.createElement('div');\n        arrivalTime.style.fontSize = '22px';\n        arrivalTime.style.fontWeight = 'bold';\n        arrivalTime.textContent = new Date(item.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n        const arrivalAirport = document.createElement('div');\n        arrivalAirport.style.fontSize = '16px';\n        arrivalAirport.style.marginTop = '5px';\n        arrivalAirport.innerHTML = `<strong>${item.arrival.airport?.code || 'N/A'}</strong>`;\n\n        const arrivalCity = document.createElement('div');\n        arrivalCity.style.fontSize = '14px';\n        arrivalCity.style.color = '#666';\n        arrivalCity.textContent = item.arrival.city?.name || 'N/A';\n\n        arrival.appendChild(arrivalTime);\n        arrival.appendChild(arrivalAirport);\n        arrival.appendChild(arrivalCity);\n      }\n\n      routeVisual.appendChild(departure);\n      routeVisual.appendChild(connectionLine);\n      routeVisual.appendChild(arrival);\n\n      routeSection.appendChild(routeVisual);\n\n      // Afficher les segments si c'est un vol avec escales\n      if (item.segments && item.segments.length > 1) {\n        const segmentsTitle = document.createElement('h4');\n        segmentsTitle.textContent = 'Flight Segments';\n        segmentsTitle.style.marginTop = '20px';\n        segmentsTitle.style.marginBottom = '15px';\n        segmentsTitle.style.fontSize = '16px';\n        segmentsTitle.style.fontWeight = '600';\n        routeSection.appendChild(segmentsTitle);\n\n        const segmentsList = document.createElement('div');\n        segmentsList.style.display = 'flex';\n        segmentsList.style.flexDirection = 'column';\n        segmentsList.style.gap = '15px';\n\n        item.segments.forEach((segment, index) => {\n          const segmentItem = document.createElement('div');\n          segmentItem.style.padding = '15px';\n          segmentItem.style.backgroundColor = '#f9f9f9';\n          segmentItem.style.borderRadius = '8px';\n          segmentItem.style.border = '1px solid #eee';\n\n          const segmentHeader = document.createElement('div');\n          segmentHeader.style.display = 'flex';\n          segmentHeader.style.justifyContent = 'space-between';\n          segmentHeader.style.marginBottom = '10px';\n          segmentHeader.style.paddingBottom = '10px';\n          segmentHeader.style.borderBottom = '1px solid #eee';\n\n          const segmentTitle = document.createElement('div');\n          segmentTitle.innerHTML = `<strong>Segment ${index + 1}</strong>: ${segment.airline?.name || 'Airline'} ${segment.flightNo}`;\n\n          const segmentDuration = document.createElement('div');\n          segmentDuration.textContent = this.formatDuration(segment.duration);\n          segmentDuration.style.color = '#666';\n\n          segmentHeader.appendChild(segmentTitle);\n          segmentHeader.appendChild(segmentDuration);\n          segmentItem.appendChild(segmentHeader);\n\n          // Ajouter les détails du segment (similaire à la visualisation principale)\n          const segmentRoute = document.createElement('div');\n          segmentRoute.style.display = 'flex';\n          segmentRoute.style.alignItems = 'center';\n          segmentRoute.style.justifyContent = 'space-between';\n\n          // Départ du segment\n          const segmentDeparture = document.createElement('div');\n          segmentDeparture.style.flex = '1';\n\n          if (segment.departure) {\n            const depTime = document.createElement('div');\n            depTime.style.fontWeight = 'bold';\n            depTime.textContent = new Date(segment.departure.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const depAirport = document.createElement('div');\n            depAirport.style.fontSize = '14px';\n            depAirport.textContent = `${segment.departure.airport?.code || 'N/A'} (${segment.departure.city?.name || 'N/A'})`;\n\n            segmentDeparture.appendChild(depTime);\n            segmentDeparture.appendChild(depAirport);\n          }\n\n          // Flèche\n          const arrow = document.createElement('div');\n          arrow.innerHTML = '<i class=\"fas fa-long-arrow-alt-right\" style=\"color: #2989d8; margin: 0 15px;\"></i>';\n\n          // Arrivée du segment\n          const segmentArrival = document.createElement('div');\n          segmentArrival.style.flex = '1';\n          segmentArrival.style.textAlign = 'right';\n\n          if (segment.arrival) {\n            const arrTime = document.createElement('div');\n            arrTime.style.fontWeight = 'bold';\n            arrTime.textContent = new Date(segment.arrival.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});\n\n            const arrAirport = document.createElement('div');\n            arrAirport.style.fontSize = '14px';\n            arrAirport.textContent = `${segment.arrival.airport?.code || 'N/A'} (${segment.arrival.city?.name || 'N/A'})`;\n\n            segmentArrival.appendChild(arrTime);\n            segmentArrival.appendChild(arrAirport);\n          }\n\n          segmentRoute.appendChild(segmentDeparture);\n          segmentRoute.appendChild(arrow);\n          segmentRoute.appendChild(segmentArrival);\n          segmentItem.appendChild(segmentRoute);\n\n          segmentsList.appendChild(segmentItem);\n\n          // Ajouter information d'escale si ce n'est pas le dernier segment\n          if (index < item.segments.length - 1) {\n            const layover = document.createElement('div');\n            layover.style.textAlign = 'center';\n            layover.style.padding = '10px';\n            layover.style.color = '#FF9800';\n            layover.style.fontSize = '14px';\n\n            // Calculer le temps d'escale\n            const currentArrival = new Date(segment.arrival?.date || 0).getTime();\n            const nextDeparture = new Date(item.segments[index + 1].departure?.date || 0).getTime();\n            const layoverTime = Math.floor((nextDeparture - currentArrival) / (1000 * 60));\n\n            layover.innerHTML = `<i class=\"fas fa-hourglass-half\"></i> ${this.formatDuration(layoverTime)} layover in ${segment.arrival?.city?.name || 'connecting city'}`;\n\n            segmentsList.appendChild(layover);\n          }\n        });\n\n        routeSection.appendChild(segmentsList);\n      }\n    }\n\n    // 3. Section des offres\n    const offersSection = this.createSection('Offers', 'fa-tag');\n\n    if (flight.offers && flight.offers.length > 0) {\n      const offersList = document.createElement('div');\n      offersList.style.display = 'flex';\n      offersList.style.flexDirection = 'column';\n      offersList.style.gap = '15px';\n\n      flight.offers.forEach((offer, index) => {\n        const offerItem = document.createElement('div');\n        offerItem.style.padding = '15px';\n        offerItem.style.backgroundColor = '#f9f9f9';\n        offerItem.style.borderRadius = '8px';\n        offerItem.style.border = '1px solid #eee';\n\n        // En-tête de l'offre\n        const offerHeader = document.createElement('div');\n        offerHeader.style.display = 'flex';\n        offerHeader.style.justifyContent = 'space-between';\n        offerHeader.style.marginBottom = '15px';\n        offerHeader.style.paddingBottom = '10px';\n        offerHeader.style.borderBottom = '1px solid #eee';\n\n        const offerTitle = document.createElement('div');\n        offerTitle.innerHTML = `<strong>Offer ${index + 1}</strong>`;\n        offerTitle.style.fontSize = '16px';\n\n        const offerPrice = document.createElement('div');\n        offerPrice.style.fontSize = '18px';\n        offerPrice.style.fontWeight = 'bold';\n        offerPrice.style.color = '#2989d8';\n        offerPrice.textContent = `${offer.price.amount} ${offer.price.currency}`;\n\n        offerHeader.appendChild(offerTitle);\n        offerHeader.appendChild(offerPrice);\n        offerItem.appendChild(offerHeader);\n\n        // Détails de l'offre\n        const offerDetails = document.createElement('div');\n        offerDetails.style.display = 'grid';\n        offerDetails.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n        offerDetails.style.gap = '10px';\n\n        // ID de l'offre\n        const offerId = this.createInfoRow('Offer ID', offer.offerId || offer.id || 'N/A');\n        offerId.style.gridColumn = '1 / -1';\n        offerDetails.appendChild(offerId);\n\n        // Disponibilité\n        const availabilityValue = offer.availability !== undefined ? offer.availability :\n                                 (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n        const availability = this.createInfoRow('Availability', availabilityValue > 0 ? 'Available' : 'Not available');\n        offerDetails.appendChild(availability);\n\n        // Date d'expiration\n        if (offer.expiresOn) {\n          const expires = this.createInfoRow('Expires On', new Date(offer.expiresOn).toLocaleString());\n          offerDetails.appendChild(expires);\n        }\n\n        // Tarif de marque\n        if (offer.brandedFare) {\n          const brandedFare = this.createInfoRow('Branded Fare', offer.brandedFare.name);\n          offerDetails.appendChild(brandedFare);\n        }\n\n        // Réservable\n        if (offer.reservableInfo) {\n          const reservable = this.createInfoRow('Reservable', offer.reservableInfo.reservable ? 'Yes' : 'No');\n          offerDetails.appendChild(reservable);\n        }\n\n        offerItem.appendChild(offerDetails);\n\n        // Bagages\n        if (offer.baggageInformations && offer.baggageInformations.length > 0) {\n          const baggageTitle = document.createElement('h4');\n          baggageTitle.textContent = 'Baggage Information';\n          baggageTitle.style.marginTop = '15px';\n          baggageTitle.style.marginBottom = '10px';\n          baggageTitle.style.fontSize = '14px';\n          baggageTitle.style.fontWeight = '600';\n          offerItem.appendChild(baggageTitle);\n\n          const baggageList = document.createElement('ul');\n          baggageList.style.listStyle = 'none';\n          baggageList.style.padding = '0';\n          baggageList.style.margin = '0';\n\n          offer.baggageInformations.forEach(baggage => {\n            const baggageItem = document.createElement('li');\n            baggageItem.style.marginBottom = '5px';\n            baggageItem.innerHTML = `<i class=\"fas fa-suitcase\" style=\"color: #666; margin-right: 8px;\"></i> ${this.getBaggageTypeName(baggage.baggageType)}`;\n            baggageList.appendChild(baggageItem);\n          });\n\n          offerItem.appendChild(baggageList);\n        }\n\n        offersList.appendChild(offerItem);\n      });\n\n      offersSection.appendChild(offersList);\n    }\n\n    // 4. Section des services\n    if (flight.items && flight.items[0] && flight.items[0].services && flight.items[0].services.length > 0) {\n      const servicesSection = this.createSection('Services', 'fa-concierge-bell');\n\n      const servicesList = document.createElement('ul');\n      servicesList.style.listStyle = 'none';\n      servicesList.style.padding = '0';\n      servicesList.style.margin = '0';\n      servicesList.style.display = 'grid';\n      servicesList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';\n      servicesList.style.gap = '10px';\n\n      flight.items[0].services.forEach(service => {\n        const serviceItem = document.createElement('li');\n        serviceItem.style.padding = '10px';\n        serviceItem.style.backgroundColor = '#f9f9f9';\n        serviceItem.style.borderRadius = '5px';\n        serviceItem.innerHTML = `<i class=\"fas fa-check\" style=\"color: #4CAF50; margin-right: 8px;\"></i> ${service.name || 'Service'}`;\n        servicesList.appendChild(serviceItem);\n      });\n\n      servicesSection.appendChild(servicesList);\n      detailsContainer.appendChild(servicesSection);\n    }\n\n    // Ajouter les sections au conteneur principal\n    detailsContainer.appendChild(generalInfo);\n    detailsContainer.appendChild(routeSection);\n    detailsContainer.appendChild(offersSection);\n\n    // Assembler la fenêtre modale\n    modalContent.appendChild(closeButton);\n    modalContent.appendChild(header);\n    modalContent.appendChild(detailsContainer);\n    modalDiv.appendChild(modalContent);\n\n    // Ajouter la fenêtre modale au document\n    document.body.appendChild(modalDiv);\n  }\n\n  // Méthode utilitaire pour créer une section\n  private createSection(title: string, iconClass: string): HTMLElement {\n    const section = document.createElement('div');\n    section.style.backgroundColor = '#f9f9f9';\n    section.style.borderRadius = '8px';\n    section.style.padding = '20px';\n    section.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';\n\n    const sectionHeader = document.createElement('div');\n    sectionHeader.style.display = 'flex';\n    sectionHeader.style.alignItems = 'center';\n    sectionHeader.style.marginBottom = '15px';\n\n    const icon = document.createElement('i');\n    icon.className = `fas ${iconClass}`;\n    icon.style.color = '#2989d8';\n    icon.style.fontSize = '18px';\n    icon.style.marginRight = '10px';\n\n    const sectionTitle = document.createElement('h3');\n    sectionTitle.textContent = title;\n    sectionTitle.style.margin = '0';\n    sectionTitle.style.fontSize = '18px';\n    sectionTitle.style.fontWeight = '600';\n\n    sectionHeader.appendChild(icon);\n    sectionHeader.appendChild(sectionTitle);\n    section.appendChild(sectionHeader);\n\n    return section;\n  }\n\n  // Méthode utilitaire pour créer une ligne d'information\n  private createInfoRow(label: string, value: string): HTMLElement {\n    const row = document.createElement('div');\n    row.style.marginBottom = '10px';\n\n    const labelElement = document.createElement('div');\n    labelElement.textContent = label;\n    labelElement.style.fontSize = '12px';\n    labelElement.style.color = '#666';\n    labelElement.style.marginBottom = '3px';\n\n    const valueElement = document.createElement('div');\n    valueElement.textContent = value;\n    valueElement.style.fontSize = '14px';\n\n    row.appendChild(labelElement);\n    row.appendChild(valueElement);\n\n    return row;\n  }\n\n  // Méthode pour sélectionner un vol et rediriger vers getoffers\n  selectThisFlight(flight: Flight): void {\n    if (flight && flight.offers && flight.offers.length > 0) {\n      // Obtenir l'ID de l'offre\n      let offerId = flight.offers[0].offerId || flight.offers[0].id;\n\n      // Obtenir l'ID de recherche\n      let searchId = this.lastSearchId;\n\n      // Si l'ID de recherche n'est pas disponible, utiliser l'ID du vol\n      if (!searchId) {\n        searchId = flight.id;\n      }\n\n      console.log('Navigating to get-offer with searchId:', searchId, 'and offerId:', offerId);\n\n      // Rediriger vers la page get-offer\n      this.router.navigate(['/get-offer'], {\n        queryParams: {\n          searchId: searchId,\n          offerId: offerId\n        }\n      });\n    } else {\n      console.error('No offers available for this flight:', flight);\n    }\n  }\n\n  preloadLocations(): void {\n    // Cette méthode est maintenant vide pour permettre à l'utilisateur de sélectionner\n    // toutes les valeurs sans valeurs par défaut\n    // Nous gardons la méthode au cas où nous voudrions ajouter des valeurs par défaut plus tard\n  }\n\n  setupAutocomplete(): void {\n    // Charger les locations par type par défaut\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value || 2;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n\n    this.productService.getLocationsByType(departureLocationType).subscribe(locations => {\n      this.departureLocations = locations;\n    });\n\n    this.productService.getLocationsByType(arrivalLocationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n    });\n\n    // Écouter les changements de type de localisation de départ\n    this.searchForm.get('departureLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.departureLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('departureLocation')?.setValue('');\n        });\n      });\n\n    // Écouter les changements de type de localisation d'arrivée\n    this.searchForm.get('arrivalLocationType')?.valueChanges\n      .subscribe(locationType => {\n        this.productService.getLocationsByType(locationType).subscribe(locations => {\n          this.arrivalLocations = locations;\n          // Réinitialiser la sélection de localisation\n          this.searchForm.get('arrivalLocation')?.setValue('');\n        });\n      });\n\n    // Autocomplétion pour le lieu de départ\n    this.searchForm.get('departureLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.departureLocations = locations;\n      });\n\n    // Autocomplétion pour le lieu d'arrivée\n    this.searchForm.get('arrivalLocation')?.valueChanges\n      .pipe(\n        debounceTime(300),\n        distinctUntilChanged(),\n        switchMap(value => {\n          if (typeof value === 'string') {\n            const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n            // Si l'utilisateur tape quelque chose, filtrer les résultats\n            if (value.length > 0) {\n              // Filtrer les résultats par type et par texte\n              return this.productService.getLocationsByType(locationType).pipe(\n                map(locations => locations.filter(location =>\n                  location.name.toLowerCase().includes(value.toLowerCase()) ||\n                  (location.code && location.code.toLowerCase().includes(value.toLowerCase()))\n                ))\n              );\n            } else {\n              // Si le champ est vide, afficher toutes les options du type sélectionné\n              return this.productService.getLocationsByType(locationType);\n            }\n          }\n          return of([]);\n        })\n      )\n      .subscribe(locations => {\n        this.arrivalLocations = locations;\n      });\n  }\n\n  displayLocation(location: LocationOption | null): string {\n    if (!location) return '';\n\n    let displayText = location.name;\n    if (location.code) {\n      displayText += ` (${location.code})`;\n    }\n    if (location.type === LocationType.Airport && location.city) {\n      displayText += ` - ${location.city}`;\n    }\n    return displayText;\n  }\n\n  onSearch(): void {\n    if (this.searchForm.invalid) {\n      this.markFormGroupTouched(this.searchForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.hasSearched = true;\n\n    const formValue = this.searchForm.value;\n\n    // Créer la requête de recherche entièrement dynamique\n    const request: PriceSearchRequest = {\n      ProductType: formValue.productType,\n      ServiceTypes: formValue.serviceTypes,\n      CheckIn: formValue.departureDate,\n      DepartureLocations: [\n        {\n          id: formValue.departureLocation?.id || '',\n          type: formValue.departureLocationType\n        }\n      ],\n      ArrivalLocations: [\n        {\n          id: formValue.arrivalLocation?.id || '',\n          type: formValue.arrivalLocationType\n        }\n      ],\n      Passengers: [\n        {\n          type: formValue.passengerType,\n          count: formValue.passengerCount\n        }\n      ],\n      showOnlyNonStopFlight: formValue.nonStop,\n      additionalParameters: {\n        getOptionsParameters: {\n          flightBaggageGetOption: formValue.flightBaggageGetOption\n        }\n      },\n      acceptPendingProviders: formValue.acceptPendingProviders,\n      forceFlightBundlePackage: formValue.forceFlightBundlePackage,\n      disablePackageOfferTotalPrice: formValue.disablePackageOfferTotalPrice,\n      calculateFlightFees: formValue.calculateFlightFees,\n      flightClasses: [formValue.flightClass],\n      Culture: formValue.culture,\n      Currency: formValue.currency\n    };\n\n    this.productService.searchPrice(request)\n      .subscribe({\n        next: (response: PriceSearchResponse) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body.flights;\n\n            // Afficher la structure complète de la réponse pour comprendre où se trouve l'ID de recherche\n            console.log('Full API Response:', JSON.stringify(response, null, 2));\n\n            // Analyser les données de disponibilité\n            if (response.body && response.body.flights && response.body.flights.length > 0) {\n              console.group('Availability Analysis');\n              console.log('Total flights:', response.body.flights.length);\n\n              // Compter les vols avec des offres\n              const flightsWithOffers = response.body.flights.filter(f => f.offers && f.offers.length > 0);\n              console.log('Flights with offers:', flightsWithOffers.length);\n\n              // Analyser les valeurs de disponibilité\n              const availabilityValues = flightsWithOffers.flatMap(f => f.offers.map(o =>\n                o.availability !== undefined ? o.availability : (o.seatInfo ? o.seatInfo.availableSeatCount : 0)\n              ));\n              console.log('Availability values:', availabilityValues);\n\n              // Compter les différentes valeurs de disponibilité\n              const availabilityCounts = availabilityValues.reduce((acc, val) => {\n                if (val !== undefined) {\n                  acc[val] = (acc[val] || 0) + 1;\n                }\n                return acc;\n              }, {} as Record<number, number>);\n              console.log('Availability counts:', availabilityCounts);\n\n              // Vérifier les vols réservables\n              const reservableFlights = flightsWithOffers.filter(f =>\n                f.offers.some(o => o.reservableInfo && o.reservableInfo.reservable === true)\n              );\n              console.log('Reservable flights:', reservableFlights.length);\n\n              console.groupEnd();\n            }\n\n            // Vérifier si searchId existe dans le corps de la réponse\n            if (response.body && response.body.searchId) {\n              this.lastSearchId = response.body.searchId;\n              console.log('Search ID found in body.searchId:', this.lastSearchId);\n            }\n            // Vérifier si searchId existe dans l'en-tête de la réponse\n            else if (response.header && response.header.requestId) {\n              this.lastSearchId = response.header.requestId;\n              console.log('Search ID found in header.requestId:', this.lastSearchId);\n            }\n            // Vérifier d'autres emplacements possibles\n            else if (response.body && response.body.flights && response.body.flights.length > 0 && response.body.flights[0].id) {\n              this.lastSearchId = response.body.flights[0].id;\n              console.log('Using flight ID as search ID:', this.lastSearchId);\n            } else {\n              console.error('No search ID found in the response!');\n              console.log('Response structure:', Object.keys(response));\n              if (response.body) console.log('Body structure:', Object.keys(response.body));\n              if (response.header) console.log('Header structure:', Object.keys(response.header));\n            }\n          } else {\n            this.errorMessage = 'The search could not be completed. Please try again.';\n            if (response.header.messages && response.header.messages.length > 0) {\n              this.errorMessage = response.header.messages[0].message;\n            }\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = 'An error occurred during the search. Please try again.';\n          console.error('Error searching flights:', error);\n        }\n      });\n  }\n\n  // Utilitaire pour marquer tous les champs comme touchés\n  markFormGroupTouched(formGroup: FormGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n\n  // Formater la durée en heures et minutes\n  formatDuration(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins}min`;\n  }\n\n  // Formater la date pour l'affichage\n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      weekday: 'short',\n      day: '2-digit',\n      month: 'short',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Obtenir le prix minimum pour un vol\n  getMinPrice(flight: Flight): string {\n    if (!flight.offers || flight.offers.length === 0) {\n      return 'N/A';\n    }\n\n    const minOffer = flight.offers.reduce((min, offer) =>\n      offer.price.amount < min.price.amount ? offer : min, flight.offers[0]);\n\n    return minOffer.price.formattedAmount || `${minOffer.price.amount} ${minOffer.price.currency}`;\n  }\n\n  // Vérifier si un vol est disponible en utilisant uniquement la valeur availability de l'API\n  isFlightAvailable(flight: Flight): boolean {\n    // Vérifier si le vol a des offres\n    if (!flight.offers || flight.offers.length === 0) {\n      return false;\n    }\n\n    // Considérer le vol comme disponible si availability > 0 ou si seatInfo.availableSeatCount > 0\n    const offer = flight.offers[0];\n    const availabilityValue = offer.availability !== undefined ? offer.availability :\n                             (offer.seatInfo ? offer.seatInfo.availableSeatCount : 0);\n    return availabilityValue > 0;\n  }\n\n  // Afficher toutes les options de départ lorsque l'utilisateur clique sur le champ\n  showAllDepartureLocations(): void {\n    const locationType = this.searchForm.get('departureLocationType')?.value || 2;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.departureLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('departureLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Afficher toutes les options d'arrivée lorsque l'utilisateur clique sur le champ\n  showAllArrivalLocations(): void {\n    const locationType = this.searchForm.get('arrivalLocationType')?.value || 5;\n    this.productService.getLocationsByType(locationType).subscribe(locations => {\n      this.arrivalLocations = locations;\n      // Forcer l'ouverture du menu d'autocomplétion\n      const input = document.getElementById('arrivalLocation') as HTMLInputElement;\n      if (input) {\n        input.focus();\n        input.dispatchEvent(new Event('input'));\n      }\n    });\n  }\n\n  // Échanger les emplacements de départ et d'arrivée\n  swapLocations(): void {\n    const departureLocation = this.searchForm.get('departureLocation')?.value;\n    const departureLocationType = this.searchForm.get('departureLocationType')?.value;\n    const arrivalLocation = this.searchForm.get('arrivalLocation')?.value;\n    const arrivalLocationType = this.searchForm.get('arrivalLocationType')?.value;\n\n    this.searchForm.patchValue({\n      departureLocation: arrivalLocation,\n      departureLocationType: arrivalLocationType,\n      arrivalLocation: departureLocation,\n      arrivalLocationType: departureLocationType\n    });\n  }\n\n  // Afficher la date d'expiration telle qu'elle est fournie par l'API\n  formatExpirationDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n\n    // Afficher la date au format local\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n\n  // Obtenir le nom du type de bagage\n  getBaggageTypeName(baggageType: number): string {\n    switch (baggageType) {\n      case 1: return 'Cabin Baggage';\n      case 2: return 'Checked Baggage';\n      case 3: return 'Hand Baggage';\n      default: return 'Baggage';\n    }\n  }\n\n  // Obtenir le nom du type de passager\n  getPassengerTypeName(passengerType: number): string {\n    switch (passengerType) {\n      case 1: return 'Adult';\n      case 2: return 'Child';\n      case 3: return 'Infant';\n      default: return 'Passenger';\n    }\n  }\n\n  // Calculer le temps d'escale entre deux segments\n  calculateLayoverTime(currentSegment: any, nextSegment: any): string {\n    if (!currentSegment || !currentSegment.arrival || !currentSegment.arrival.date ||\n        !nextSegment || !nextSegment.departure || !nextSegment.departure.date) {\n      return 'Unknown';\n    }\n\n    const arrivalTime = new Date(currentSegment.arrival.date).getTime();\n    const departureTime = new Date(nextSegment.departure.date).getTime();\n    const diffMs = departureTime - arrivalTime;\n    const diffMins = Math.floor(diffMs / (1000 * 60));\n\n    if (diffMins < 60) {\n      return `${diffMins}min`;\n    } else {\n      const hours = Math.floor(diffMins / 60);\n      const mins = diffMins % 60;\n      return `${hours}h ${mins}min`;\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,iBAAiB,QAAQ,eAAe;AACpE,SAAsBC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACnF,SAAqBC,EAAE,QAAQ,MAAM;AAKrC,SAASC,eAAe,EAAEC,YAAY,EAAEC,aAAa,QAAQ,6BAA6B;AAQnF,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EA2B/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IA5BhB,KAAAC,kBAAkB,GAAqB,EAAE;IACzC,KAAAC,gBAAgB,GAAqB,EAAE;IACvC,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAa,EAAE;IAC5B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,YAAY,GAAG,EAAE;IAEjB;IACA,KAAAC,cAAc,GAAG,CACf;MAAEC,KAAK,EAAEd,aAAa,CAACe,KAAK;MAAEC,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAEF,KAAK,EAAEd,aAAa,CAACiB,KAAK;MAAED,KAAK,EAAE;IAAO,CAAE,EAC9C;MAAEF,KAAK,EAAEd,aAAa,CAACkB,MAAM;MAAEF,KAAK,EAAE;IAAQ,CAAE,CACjD;IAED;IACA,KAAAG,aAAa,GAAG,CACd;MAAEL,KAAK,EAAEhB,eAAe,CAACsB,KAAK;MAAEJ,KAAK,EAAE;IAAO,CAAE,EAChD;MAAEF,KAAK,EAAEhB,eAAe,CAACuB,OAAO;MAAEL,KAAK,EAAE;IAAS,CAAE,EACpD;MAAEF,KAAK,EAAEhB,eAAe,CAACwB,QAAQ;MAAEN,KAAK,EAAE;IAAU,CAAE,CACvD;IAUC;IACA,IAAI,CAACO,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAErD;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACxB,EAAE,CAACyB,KAAK,CAAC;MAC9B;MACAC,WAAW,EAAE,CAAC,CAAC,EAAErC,UAAU,CAACsC,QAAQ,CAAC;MACrCC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,EAAEvC,UAAU,CAACsC,QAAQ,CAAC;MAC1CE,iBAAiB,EAAE,CAAC,EAAE,EAAExC,UAAU,CAACsC,QAAQ,CAAC;MAC5CG,qBAAqB,EAAE,CAAC,CAAC,EAAEzC,UAAU,CAACsC,QAAQ,CAAC;MAC/CI,eAAe,EAAE,CAAC,EAAE,EAAE1C,UAAU,CAACsC,QAAQ,CAAC;MAC1CK,mBAAmB,EAAE,CAAC,CAAC,EAAE3C,UAAU,CAACsC,QAAQ,CAAC;MAC7CM,aAAa,EAAE,CAAC,IAAI,CAACb,OAAO,EAAE/B,UAAU,CAACsC,QAAQ,CAAC;MAClDO,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC7C,UAAU,CAACsC,QAAQ,EAAEtC,UAAU,CAAC8C,GAAG,CAAC,CAAC,CAAC,EAAE9C,UAAU,CAAC+C,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChFC,aAAa,EAAE,CAAC,CAAC,EAAEhD,UAAU,CAACsC,QAAQ,CAAC;MAEvC;MACAW,WAAW,EAAE,CAAC,CAAC,EAAEjD,UAAU,CAACsC,QAAQ,CAAC;MACrCY,OAAO,EAAE,CAAC,KAAK,CAAC;MAEhB;MACAC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBC,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,wBAAwB,EAAE,CAAC,KAAK,CAAC;MACjCC,6BAA6B,EAAE,CAAC,IAAI,CAAC;MACrCC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAE5B;MACAC,sBAAsB,EAAE,CAAC,CAAC;KAC3B,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACAC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EACjF;EAEA;EACAC,cAAcA,CAACC,MAAc;IAC3B;IACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9CF,QAAQ,CAACG,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCJ,QAAQ,CAACG,KAAK,CAACE,GAAG,GAAG,GAAG;IACxBL,QAAQ,CAACG,KAAK,CAACG,IAAI,GAAG,GAAG;IACzBN,QAAQ,CAACG,KAAK,CAACI,KAAK,GAAG,MAAM;IAC7BP,QAAQ,CAACG,KAAK,CAACK,MAAM,GAAG,MAAM;IAC9BR,QAAQ,CAACG,KAAK,CAACM,eAAe,GAAG,oBAAoB;IACrDT,QAAQ,CAACG,KAAK,CAACO,MAAM,GAAG,MAAM;IAC9BV,QAAQ,CAACG,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC/BX,QAAQ,CAACG,KAAK,CAACS,cAAc,GAAG,QAAQ;IACxCZ,QAAQ,CAACG,KAAK,CAACU,UAAU,GAAG,QAAQ;IAEpC;IACA,MAAMC,YAAY,GAAGb,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDY,YAAY,CAACX,KAAK,CAACM,eAAe,GAAG,OAAO;IAC5CK,YAAY,CAACX,KAAK,CAACY,OAAO,GAAG,MAAM;IACnCD,YAAY,CAACX,KAAK,CAACa,YAAY,GAAG,MAAM;IACxCF,YAAY,CAACX,KAAK,CAACc,QAAQ,GAAG,KAAK;IACnCH,YAAY,CAACX,KAAK,CAACe,SAAS,GAAG,KAAK;IACpCJ,YAAY,CAACX,KAAK,CAACgB,QAAQ,GAAG,MAAM;IACpCL,YAAY,CAACX,KAAK,CAACC,QAAQ,GAAG,UAAU;IACxCU,YAAY,CAACX,KAAK,CAACiB,SAAS,GAAG,gCAAgC;IAC/DN,YAAY,CAACX,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCP,YAAY,CAACX,KAAK,CAACmB,UAAU,GAAG,mBAAmB;IAEnD;IACA,MAAMC,WAAW,GAAGtB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACpDqB,WAAW,CAACC,SAAS,GAAG,SAAS;IACjCD,WAAW,CAACpB,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvCmB,WAAW,CAACpB,KAAK,CAACE,GAAG,GAAG,MAAM;IAC9BkB,WAAW,CAACpB,KAAK,CAACsB,KAAK,GAAG,MAAM;IAChCF,WAAW,CAACpB,KAAK,CAACuB,MAAM,GAAG,MAAM;IACjCH,WAAW,CAACpB,KAAK,CAACwB,UAAU,GAAG,MAAM;IACrCJ,WAAW,CAACpB,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACnCL,WAAW,CAACpB,KAAK,CAAC0B,MAAM,GAAG,SAAS;IACpCN,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnCE,WAAW,CAACpB,KAAK,CAAC2B,UAAU,GAAG,YAAY;IAC3CP,WAAW,CAACQ,WAAW,GAAG,MAAMR,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IACnEE,WAAW,CAACS,UAAU,GAAG,MAAMT,WAAW,CAACpB,KAAK,CAACkB,KAAK,GAAG,SAAS;IAClEE,WAAW,CAACU,OAAO,GAAG,MAAMhC,QAAQ,CAACiC,IAAI,CAACC,WAAW,CAACnC,QAAQ,CAAC;IAE/D;IACA,MAAMoC,MAAM,GAAGnC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5CkC,MAAM,CAACjC,KAAK,CAACQ,OAAO,GAAG,MAAM;IAC7ByB,MAAM,CAACjC,KAAK,CAACU,UAAU,GAAG,QAAQ;IAClCuB,MAAM,CAACjC,KAAK,CAACkC,YAAY,GAAG,MAAM;IAClCD,MAAM,CAACjC,KAAK,CAACmC,aAAa,GAAG,MAAM;IACnCF,MAAM,CAACjC,KAAK,CAACoC,YAAY,GAAG,gBAAgB;IAE5C,MAAMC,IAAI,GAAGvC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CsC,IAAI,CAAChB,SAAS,GAAG,2FAA2F;IAE5G,MAAMiB,KAAK,GAAGxC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IAC1CuC,KAAK,CAACC,WAAW,GAAG,yBAAyB;IAC7CD,KAAK,CAACtC,KAAK,CAACwC,MAAM,GAAG,GAAG;IACxBF,KAAK,CAACtC,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC7Ba,KAAK,CAACtC,KAAK,CAACyC,UAAU,GAAG,KAAK;IAC9BH,KAAK,CAACtC,KAAK,CAACkB,KAAK,GAAG,SAAS;IAE7Be,MAAM,CAACS,WAAW,CAACL,IAAI,CAAC;IACxBJ,MAAM,CAACS,WAAW,CAACJ,KAAK,CAAC;IAEzB;IACA,MAAMK,gBAAgB,GAAG7C,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACtD4C,gBAAgB,CAAC3C,KAAK,CAACQ,OAAO,GAAG,MAAM;IACvCmC,gBAAgB,CAAC3C,KAAK,CAAC4C,aAAa,GAAG,QAAQ;IAC/CD,gBAAgB,CAAC3C,KAAK,CAAC6C,GAAG,GAAG,MAAM;IAEnC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IAE9E;IACA,IAAInD,MAAM,CAACoD,KAAK,IAAIpD,MAAM,CAACoD,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMC,IAAI,GAAGtD,MAAM,CAACoD,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,IAAIE,IAAI,CAACC,OAAO,EAAE;QAChB,MAAMC,WAAW,GAAGtD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDqD,WAAW,CAACpD,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClC4C,WAAW,CAACpD,KAAK,CAACU,UAAU,GAAG,QAAQ;QACvC0C,WAAW,CAACpD,KAAK,CAACkC,YAAY,GAAG,MAAM;QAEvC;QACA,IAAIgB,IAAI,CAACC,OAAO,CAACE,aAAa,EAAE;UAC9B,MAAMC,WAAW,GAAGxD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDuD,WAAW,CAACC,GAAG,GAAGL,IAAI,CAACC,OAAO,CAACE,aAAa;UAC5CC,WAAW,CAACE,GAAG,GAAGN,IAAI,CAACC,OAAO,CAACM,IAAI;UACnCH,WAAW,CAACtD,KAAK,CAACK,MAAM,GAAG,MAAM;UACjCiD,WAAW,CAACtD,KAAK,CAAC0D,WAAW,GAAG,MAAM;UACtCN,WAAW,CAACV,WAAW,CAACY,WAAW,CAAC;SACrC,MAAM;UACL,MAAMK,WAAW,GAAG7D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjD4D,WAAW,CAACtC,SAAS,GAAG,wFAAwF;UAChH+B,WAAW,CAACV,WAAW,CAACiB,WAAW,CAAC;;QAGtC,MAAMC,WAAW,GAAG9D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjD6D,WAAW,CAACvC,SAAS,GAAG,WAAW6B,IAAI,CAACC,OAAO,CAACM,IAAI,cAAcP,IAAI,CAACC,OAAO,CAACU,iBAAiB,GAAG;QACnGD,WAAW,CAAC5D,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnC2B,WAAW,CAACV,WAAW,CAACkB,WAAW,CAAC;QAEpCd,WAAW,CAACJ,WAAW,CAACU,WAAW,CAAC;;MAGtC;MACA,MAAMU,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC,eAAe,EAAEb,IAAI,CAACc,QAAQ,IAAI,KAAK,CAAC;MACnFlB,WAAW,CAACJ,WAAW,CAACoB,eAAe,CAAC;MAExC;MACA,MAAMG,aAAa,GAAG,IAAI,CAACF,aAAa,CAAC,aAAa,EAAE,IAAInG,IAAI,CAACsF,IAAI,CAACgB,UAAU,CAAC,CAACC,kBAAkB,EAAE,CAAC;MACvGrB,WAAW,CAACJ,WAAW,CAACuB,aAAa,CAAC;MAEtC;MACA,MAAMG,WAAW,GAAG,IAAI,CAACL,aAAa,CAAC,UAAU,EAAE,IAAI,CAACM,cAAc,CAACnB,IAAI,CAACoB,QAAQ,CAAC,CAAC;MACtFxB,WAAW,CAACJ,WAAW,CAAC0B,WAAW,CAAC;MAEpC;MACA,IAAIlB,IAAI,CAACrE,WAAW,EAAE;QACpB,MAAM0F,QAAQ,GAAG,IAAI,CAACR,aAAa,CAAC,OAAO,EAAE,GAAGb,IAAI,CAACrE,WAAW,CAAC4E,IAAI,KAAKP,IAAI,CAACrE,WAAW,CAAC2F,IAAI,GAAG,CAAC;QACnG1B,WAAW,CAACJ,WAAW,CAAC6B,QAAQ,CAAC;;MAGnC;MACA,MAAME,QAAQ,GAAG,IAAI,CAACV,aAAa,CAAC,OAAO,EAAEb,IAAI,CAACwB,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,GAAGxB,IAAI,CAACwB,SAAS,UAAU,CAAC;MAClH5B,WAAW,CAACJ,WAAW,CAAC+B,QAAQ,CAAC;;IAGnC;IACA,MAAME,YAAY,GAAG,IAAI,CAAC5B,aAAa,CAAC,eAAe,EAAE,UAAU,CAAC;IAEpE,IAAInD,MAAM,CAACoD,KAAK,IAAIpD,MAAM,CAACoD,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,MAAMC,IAAI,GAAGtD,MAAM,CAACoD,KAAK,CAAC,CAAC,CAAC;MAE5B;MACA,MAAM4B,WAAW,GAAG9E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACjD6E,WAAW,CAAC5E,KAAK,CAACQ,OAAO,GAAG,MAAM;MAClCoE,WAAW,CAAC5E,KAAK,CAACU,UAAU,GAAG,QAAQ;MACvCkE,WAAW,CAAC5E,KAAK,CAACS,cAAc,GAAG,eAAe;MAClDmE,WAAW,CAAC5E,KAAK,CAACwC,MAAM,GAAG,QAAQ;MACnCoC,WAAW,CAAC5E,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEvC;MACA,MAAM4E,SAAS,GAAG/E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/C8E,SAAS,CAAC7E,KAAK,CAAC8E,SAAS,GAAG,QAAQ;MACpCD,SAAS,CAAC7E,KAAK,CAAC+E,IAAI,GAAG,GAAG;MAE1B,IAAI7B,IAAI,CAAC2B,SAAS,EAAE;QAClB,MAAMG,aAAa,GAAGlF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnDiF,aAAa,CAAChF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrCuD,aAAa,CAAChF,KAAK,CAACyC,UAAU,GAAG,MAAM;QACvCuC,aAAa,CAACzC,WAAW,GAAG,IAAI3E,IAAI,CAACsF,IAAI,CAAC2B,SAAS,CAACI,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAErH,MAAMC,gBAAgB,GAAGvF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACtDsF,gBAAgB,CAACrF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACxC4D,gBAAgB,CAACrF,KAAK,CAACsF,SAAS,GAAG,KAAK;QACxCD,gBAAgB,CAAChE,SAAS,GAAG,WAAW6B,IAAI,CAAC2B,SAAS,CAACU,OAAO,EAAEf,IAAI,IAAI,KAAK,WAAW;QAExF,MAAMgB,aAAa,GAAG1F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnDyF,aAAa,CAACxF,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC+D,aAAa,CAACxF,KAAK,CAACkB,KAAK,GAAG,MAAM;QAClCsE,aAAa,CAACjD,WAAW,GAAGW,IAAI,CAAC2B,SAAS,CAACY,IAAI,EAAEhC,IAAI,IAAI,KAAK;QAE9DoB,SAAS,CAACnC,WAAW,CAACsC,aAAa,CAAC;QACpCH,SAAS,CAACnC,WAAW,CAAC2C,gBAAgB,CAAC;QACvCR,SAAS,CAACnC,WAAW,CAAC8C,aAAa,CAAC;;MAGtC;MACA,MAAME,cAAc,GAAG5F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACpD2F,cAAc,CAAC1F,KAAK,CAAC+E,IAAI,GAAG,GAAG;MAC/BW,cAAc,CAAC1F,KAAK,CAACQ,OAAO,GAAG,MAAM;MACrCkF,cAAc,CAAC1F,KAAK,CAACU,UAAU,GAAG,QAAQ;MAC1CgF,cAAc,CAAC1F,KAAK,CAACS,cAAc,GAAG,QAAQ;MAC9CiF,cAAc,CAAC1F,KAAK,CAACY,OAAO,GAAG,QAAQ;MAEvC,MAAM+E,IAAI,GAAG7F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC1C4F,IAAI,CAAC3F,KAAK,CAACK,MAAM,GAAG,KAAK;MACzBsF,IAAI,CAAC3F,KAAK,CAACM,eAAe,GAAG,MAAM;MACnCqF,IAAI,CAAC3F,KAAK,CAACI,KAAK,GAAG,MAAM;MACzBuF,IAAI,CAAC3F,KAAK,CAACC,QAAQ,GAAG,UAAU;MAEhC,MAAM2F,KAAK,GAAG9F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC3C6F,KAAK,CAACvE,SAAS,GAAG,iGAAiG;MACnHuE,KAAK,CAAC5F,KAAK,CAACC,QAAQ,GAAG,UAAU;MACjC2F,KAAK,CAAC5F,KAAK,CAACE,GAAG,GAAG,MAAM;MACxB0F,KAAK,CAAC5F,KAAK,CAACG,IAAI,GAAG,KAAK;MACxByF,KAAK,CAAC5F,KAAK,CAAC6F,UAAU,GAAG,MAAM;MAC/BD,KAAK,CAAC5F,KAAK,CAACM,eAAe,GAAG,OAAO;MACrCsF,KAAK,CAAC5F,KAAK,CAACY,OAAO,GAAG,OAAO;MAE7B+E,IAAI,CAACjD,WAAW,CAACkD,KAAK,CAAC;MACvBF,cAAc,CAAChD,WAAW,CAACiD,IAAI,CAAC;MAEhC;MACA,MAAMG,OAAO,GAAGhG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7C+F,OAAO,CAAC9F,KAAK,CAAC8E,SAAS,GAAG,QAAQ;MAClCgB,OAAO,CAAC9F,KAAK,CAAC+E,IAAI,GAAG,GAAG;MAExB,IAAI7B,IAAI,CAAC4C,OAAO,EAAE;QAChB,MAAMC,WAAW,GAAGjG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDgG,WAAW,CAAC/F,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCsE,WAAW,CAAC/F,KAAK,CAACyC,UAAU,GAAG,MAAM;QACrCsD,WAAW,CAACxD,WAAW,GAAG,IAAI3E,IAAI,CAACsF,IAAI,CAAC4C,OAAO,CAACb,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;UAACC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAC;QAAS,CAAC,CAAC;QAEjH,MAAMY,cAAc,GAAGlG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACpDiG,cAAc,CAAChG,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACtCuE,cAAc,CAAChG,KAAK,CAACsF,SAAS,GAAG,KAAK;QACtCU,cAAc,CAAC3E,SAAS,GAAG,WAAW6B,IAAI,CAAC4C,OAAO,CAACP,OAAO,EAAEf,IAAI,IAAI,KAAK,WAAW;QAEpF,MAAMyB,WAAW,GAAGnG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDkG,WAAW,CAACjG,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACnCwE,WAAW,CAACjG,KAAK,CAACkB,KAAK,GAAG,MAAM;QAChC+E,WAAW,CAAC1D,WAAW,GAAGW,IAAI,CAAC4C,OAAO,CAACL,IAAI,EAAEhC,IAAI,IAAI,KAAK;QAE1DqC,OAAO,CAACpD,WAAW,CAACqD,WAAW,CAAC;QAChCD,OAAO,CAACpD,WAAW,CAACsD,cAAc,CAAC;QACnCF,OAAO,CAACpD,WAAW,CAACuD,WAAW,CAAC;;MAGlCrB,WAAW,CAAClC,WAAW,CAACmC,SAAS,CAAC;MAClCD,WAAW,CAAClC,WAAW,CAACgD,cAAc,CAAC;MACvCd,WAAW,CAAClC,WAAW,CAACoD,OAAO,CAAC;MAEhCnB,YAAY,CAACjC,WAAW,CAACkC,WAAW,CAAC;MAErC;MACA,IAAI1B,IAAI,CAACgD,QAAQ,IAAIhD,IAAI,CAACgD,QAAQ,CAACjD,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMkD,aAAa,GAAGrG,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAClDoG,aAAa,CAAC5D,WAAW,GAAG,iBAAiB;QAC7C4D,aAAa,CAACnG,KAAK,CAACsF,SAAS,GAAG,MAAM;QACtCa,aAAa,CAACnG,KAAK,CAACkC,YAAY,GAAG,MAAM;QACzCiE,aAAa,CAACnG,KAAK,CAACyB,QAAQ,GAAG,MAAM;QACrC0E,aAAa,CAACnG,KAAK,CAACyC,UAAU,GAAG,KAAK;QACtCkC,YAAY,CAACjC,WAAW,CAACyD,aAAa,CAAC;QAEvC,MAAMC,YAAY,GAAGtG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClDqG,YAAY,CAACpG,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnC4F,YAAY,CAACpG,KAAK,CAAC4C,aAAa,GAAG,QAAQ;QAC3CwD,YAAY,CAACpG,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/BK,IAAI,CAACgD,QAAQ,CAACG,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAI;UACvC,MAAMC,WAAW,GAAG1G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACjDyG,WAAW,CAACxG,KAAK,CAACY,OAAO,GAAG,MAAM;UAClC4F,WAAW,CAACxG,KAAK,CAACM,eAAe,GAAG,SAAS;UAC7CkG,WAAW,CAACxG,KAAK,CAACa,YAAY,GAAG,KAAK;UACtC2F,WAAW,CAACxG,KAAK,CAACuB,MAAM,GAAG,gBAAgB;UAE3C,MAAMkF,aAAa,GAAG3G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACnD0G,aAAa,CAACzG,KAAK,CAACQ,OAAO,GAAG,MAAM;UACpCiG,aAAa,CAACzG,KAAK,CAACS,cAAc,GAAG,eAAe;UACpDgG,aAAa,CAACzG,KAAK,CAACkC,YAAY,GAAG,MAAM;UACzCuE,aAAa,CAACzG,KAAK,CAACmC,aAAa,GAAG,MAAM;UAC1CsE,aAAa,CAACzG,KAAK,CAACoC,YAAY,GAAG,gBAAgB;UAEnD,MAAMsE,YAAY,GAAG5G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClD2G,YAAY,CAACrF,SAAS,GAAG,mBAAmBkF,KAAK,GAAG,CAAC,cAAcD,OAAO,CAACnD,OAAO,EAAEM,IAAI,IAAI,SAAS,IAAI6C,OAAO,CAACtC,QAAQ,EAAE;UAE3H,MAAM2C,eAAe,GAAG7G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACrD4G,eAAe,CAACpE,WAAW,GAAG,IAAI,CAAC8B,cAAc,CAACiC,OAAO,CAAChC,QAAQ,CAAC;UACnEqC,eAAe,CAAC3G,KAAK,CAACkB,KAAK,GAAG,MAAM;UAEpCuF,aAAa,CAAC/D,WAAW,CAACgE,YAAY,CAAC;UACvCD,aAAa,CAAC/D,WAAW,CAACiE,eAAe,CAAC;UAC1CH,WAAW,CAAC9D,WAAW,CAAC+D,aAAa,CAAC;UAEtC;UACA,MAAMG,YAAY,GAAG9G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAClD6G,YAAY,CAAC5G,KAAK,CAACQ,OAAO,GAAG,MAAM;UACnCoG,YAAY,CAAC5G,KAAK,CAACU,UAAU,GAAG,QAAQ;UACxCkG,YAAY,CAAC5G,KAAK,CAACS,cAAc,GAAG,eAAe;UAEnD;UACA,MAAMoG,gBAAgB,GAAG/G,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACtD8G,gBAAgB,CAAC7G,KAAK,CAAC+E,IAAI,GAAG,GAAG;UAEjC,IAAIuB,OAAO,CAACzB,SAAS,EAAE;YACrB,MAAMiC,OAAO,GAAGhH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7C+G,OAAO,CAAC9G,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCqE,OAAO,CAACvE,WAAW,GAAG,IAAI3E,IAAI,CAAC0I,OAAO,CAACzB,SAAS,CAACI,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAElH,MAAM2B,UAAU,GAAGjH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChDgH,UAAU,CAAC/G,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClCsF,UAAU,CAACxE,WAAW,GAAG,GAAG+D,OAAO,CAACzB,SAAS,CAACU,OAAO,EAAEf,IAAI,IAAI,KAAK,KAAK8B,OAAO,CAACzB,SAAS,CAACY,IAAI,EAAEhC,IAAI,IAAI,KAAK,GAAG;YAEjHoD,gBAAgB,CAACnE,WAAW,CAACoE,OAAO,CAAC;YACrCD,gBAAgB,CAACnE,WAAW,CAACqE,UAAU,CAAC;;UAG1C;UACA,MAAMC,KAAK,GAAGlH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UAC3CiH,KAAK,CAAC3F,SAAS,GAAG,qFAAqF;UAEvG;UACA,MAAM4F,cAAc,GAAGnH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;UACpDkH,cAAc,CAACjH,KAAK,CAAC+E,IAAI,GAAG,GAAG;UAC/BkC,cAAc,CAACjH,KAAK,CAAC8E,SAAS,GAAG,OAAO;UAExC,IAAIwB,OAAO,CAACR,OAAO,EAAE;YACnB,MAAMoB,OAAO,GAAGpH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CmH,OAAO,CAAClH,KAAK,CAACyC,UAAU,GAAG,MAAM;YACjCyE,OAAO,CAAC3E,WAAW,GAAG,IAAI3E,IAAI,CAAC0I,OAAO,CAACR,OAAO,CAACb,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;cAACC,IAAI,EAAE,SAAS;cAAEC,MAAM,EAAC;YAAS,CAAC,CAAC;YAEhH,MAAM+B,UAAU,GAAGrH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAChDoH,UAAU,CAACnH,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAClC0F,UAAU,CAAC5E,WAAW,GAAG,GAAG+D,OAAO,CAACR,OAAO,CAACP,OAAO,EAAEf,IAAI,IAAI,KAAK,KAAK8B,OAAO,CAACR,OAAO,CAACL,IAAI,EAAEhC,IAAI,IAAI,KAAK,GAAG;YAE7GwD,cAAc,CAACvE,WAAW,CAACwE,OAAO,CAAC;YACnCD,cAAc,CAACvE,WAAW,CAACyE,UAAU,CAAC;;UAGxCP,YAAY,CAAClE,WAAW,CAACmE,gBAAgB,CAAC;UAC1CD,YAAY,CAAClE,WAAW,CAACsE,KAAK,CAAC;UAC/BJ,YAAY,CAAClE,WAAW,CAACuE,cAAc,CAAC;UACxCT,WAAW,CAAC9D,WAAW,CAACkE,YAAY,CAAC;UAErCR,YAAY,CAAC1D,WAAW,CAAC8D,WAAW,CAAC;UAErC;UACA,IAAID,KAAK,GAAGrD,IAAI,CAACgD,QAAQ,CAACjD,MAAM,GAAG,CAAC,EAAE;YACpC,MAAMmE,OAAO,GAAGtH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;YAC7CqH,OAAO,CAACpH,KAAK,CAAC8E,SAAS,GAAG,QAAQ;YAClCsC,OAAO,CAACpH,KAAK,CAACY,OAAO,GAAG,MAAM;YAC9BwG,OAAO,CAACpH,KAAK,CAACkB,KAAK,GAAG,SAAS;YAC/BkG,OAAO,CAACpH,KAAK,CAACyB,QAAQ,GAAG,MAAM;YAE/B;YACA,MAAM4F,cAAc,GAAG,IAAIzJ,IAAI,CAAC0I,OAAO,CAACR,OAAO,EAAEb,IAAI,IAAI,CAAC,CAAC,CAACqC,OAAO,EAAE;YACrE,MAAMC,aAAa,GAAG,IAAI3J,IAAI,CAACsF,IAAI,CAACgD,QAAQ,CAACK,KAAK,GAAG,CAAC,CAAC,CAAC1B,SAAS,EAAEI,IAAI,IAAI,CAAC,CAAC,CAACqC,OAAO,EAAE;YACvF,MAAME,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,aAAa,GAAGF,cAAc,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;YAE9ED,OAAO,CAAC/F,SAAS,GAAG,yCAAyC,IAAI,CAACgD,cAAc,CAACmD,WAAW,CAAC,eAAelB,OAAO,CAACR,OAAO,EAAEL,IAAI,EAAEhC,IAAI,IAAI,iBAAiB,EAAE;YAE9J2C,YAAY,CAAC1D,WAAW,CAAC0E,OAAO,CAAC;;QAErC,CAAC,CAAC;QAEFzC,YAAY,CAACjC,WAAW,CAAC0D,YAAY,CAAC;;;IAI1C;IACA,MAAMuB,aAAa,GAAG,IAAI,CAAC5E,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAE5D,IAAInD,MAAM,CAACgI,MAAM,IAAIhI,MAAM,CAACgI,MAAM,CAAC3E,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAM4E,UAAU,GAAG/H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAChD8H,UAAU,CAAC7H,KAAK,CAACQ,OAAO,GAAG,MAAM;MACjCqH,UAAU,CAAC7H,KAAK,CAAC4C,aAAa,GAAG,QAAQ;MACzCiF,UAAU,CAAC7H,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE7BjD,MAAM,CAACgI,MAAM,CAACvB,OAAO,CAAC,CAACyB,KAAK,EAAEvB,KAAK,KAAI;QACrC,MAAMwB,SAAS,GAAGjI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC/CgI,SAAS,CAAC/H,KAAK,CAACY,OAAO,GAAG,MAAM;QAChCmH,SAAS,CAAC/H,KAAK,CAACM,eAAe,GAAG,SAAS;QAC3CyH,SAAS,CAAC/H,KAAK,CAACa,YAAY,GAAG,KAAK;QACpCkH,SAAS,CAAC/H,KAAK,CAACuB,MAAM,GAAG,gBAAgB;QAEzC;QACA,MAAMyG,WAAW,GAAGlI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACjDiI,WAAW,CAAChI,KAAK,CAACQ,OAAO,GAAG,MAAM;QAClCwH,WAAW,CAAChI,KAAK,CAACS,cAAc,GAAG,eAAe;QAClDuH,WAAW,CAAChI,KAAK,CAACkC,YAAY,GAAG,MAAM;QACvC8F,WAAW,CAAChI,KAAK,CAACmC,aAAa,GAAG,MAAM;QACxC6F,WAAW,CAAChI,KAAK,CAACoC,YAAY,GAAG,gBAAgB;QAEjD,MAAM6F,UAAU,GAAGnI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChDkI,UAAU,CAAC5G,SAAS,GAAG,iBAAiBkF,KAAK,GAAG,CAAC,WAAW;QAC5D0B,UAAU,CAACjI,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAElC,MAAMyG,UAAU,GAAGpI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChDmI,UAAU,CAAClI,KAAK,CAACyB,QAAQ,GAAG,MAAM;QAClCyG,UAAU,CAAClI,KAAK,CAACyC,UAAU,GAAG,MAAM;QACpCyF,UAAU,CAAClI,KAAK,CAACkB,KAAK,GAAG,SAAS;QAClCgH,UAAU,CAAC3F,WAAW,GAAG,GAAGuF,KAAK,CAACK,KAAK,CAACC,MAAM,IAAIN,KAAK,CAACK,KAAK,CAACnJ,QAAQ,EAAE;QAExEgJ,WAAW,CAACtF,WAAW,CAACuF,UAAU,CAAC;QACnCD,WAAW,CAACtF,WAAW,CAACwF,UAAU,CAAC;QACnCH,SAAS,CAACrF,WAAW,CAACsF,WAAW,CAAC;QAElC;QACA,MAAMK,YAAY,GAAGvI,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAClDsI,YAAY,CAACrI,KAAK,CAACQ,OAAO,GAAG,MAAM;QACnC6H,YAAY,CAACrI,KAAK,CAACsI,mBAAmB,GAAG,uCAAuC;QAChFD,YAAY,CAACrI,KAAK,CAAC6C,GAAG,GAAG,MAAM;QAE/B;QACA,MAAM0F,OAAO,GAAG,IAAI,CAACxE,aAAa,CAAC,UAAU,EAAE+D,KAAK,CAACS,OAAO,IAAIT,KAAK,CAACU,EAAE,IAAI,KAAK,CAAC;QAClFD,OAAO,CAACvI,KAAK,CAACyI,UAAU,GAAG,QAAQ;QACnCJ,YAAY,CAAC3F,WAAW,CAAC6F,OAAO,CAAC;QAEjC;QACA,MAAMG,iBAAiB,GAAGZ,KAAK,CAACa,YAAY,KAAKC,SAAS,GAAGd,KAAK,CAACa,YAAY,GACrDb,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACC,kBAAkB,GAAG,CAAE;QACjF,MAAMH,YAAY,GAAG,IAAI,CAAC5E,aAAa,CAAC,cAAc,EAAE2E,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,eAAe,CAAC;QAC9GL,YAAY,CAAC3F,WAAW,CAACiG,YAAY,CAAC;QAEtC;QACA,IAAIb,KAAK,CAACiB,SAAS,EAAE;UACnB,MAAMC,OAAO,GAAG,IAAI,CAACjF,aAAa,CAAC,YAAY,EAAE,IAAInG,IAAI,CAACkK,KAAK,CAACiB,SAAS,CAAC,CAACE,cAAc,EAAE,CAAC;UAC5FZ,YAAY,CAAC3F,WAAW,CAACsG,OAAO,CAAC;;QAGnC;QACA,IAAIlB,KAAK,CAACoB,WAAW,EAAE;UACrB,MAAMA,WAAW,GAAG,IAAI,CAACnF,aAAa,CAAC,cAAc,EAAE+D,KAAK,CAACoB,WAAW,CAACzF,IAAI,CAAC;UAC9E4E,YAAY,CAAC3F,WAAW,CAACwG,WAAW,CAAC;;QAGvC;QACA,IAAIpB,KAAK,CAACqB,cAAc,EAAE;UACxB,MAAMC,UAAU,GAAG,IAAI,CAACrF,aAAa,CAAC,YAAY,EAAE+D,KAAK,CAACqB,cAAc,CAACC,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC;UACnGf,YAAY,CAAC3F,WAAW,CAAC0G,UAAU,CAAC;;QAGtCrB,SAAS,CAACrF,WAAW,CAAC2F,YAAY,CAAC;QAEnC;QACA,IAAIP,KAAK,CAACuB,mBAAmB,IAAIvB,KAAK,CAACuB,mBAAmB,CAACpG,MAAM,GAAG,CAAC,EAAE;UACrE,MAAMqG,YAAY,GAAGxJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;UACjDuJ,YAAY,CAAC/G,WAAW,GAAG,qBAAqB;UAChD+G,YAAY,CAACtJ,KAAK,CAACsF,SAAS,GAAG,MAAM;UACrCgE,YAAY,CAACtJ,KAAK,CAACkC,YAAY,GAAG,MAAM;UACxCoH,YAAY,CAACtJ,KAAK,CAACyB,QAAQ,GAAG,MAAM;UACpC6H,YAAY,CAACtJ,KAAK,CAACyC,UAAU,GAAG,KAAK;UACrCsF,SAAS,CAACrF,WAAW,CAAC4G,YAAY,CAAC;UAEnC,MAAMC,WAAW,GAAGzJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;UAChDwJ,WAAW,CAACvJ,KAAK,CAACwJ,SAAS,GAAG,MAAM;UACpCD,WAAW,CAACvJ,KAAK,CAACY,OAAO,GAAG,GAAG;UAC/B2I,WAAW,CAACvJ,KAAK,CAACwC,MAAM,GAAG,GAAG;UAE9BsF,KAAK,CAACuB,mBAAmB,CAAChD,OAAO,CAACoD,OAAO,IAAG;YAC1C,MAAMC,WAAW,GAAG5J,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;YAChD2J,WAAW,CAAC1J,KAAK,CAACkC,YAAY,GAAG,KAAK;YACtCwH,WAAW,CAACrI,SAAS,GAAG,2EAA2E,IAAI,CAACsI,kBAAkB,CAACF,OAAO,CAACG,WAAW,CAAC,EAAE;YACjJL,WAAW,CAAC7G,WAAW,CAACgH,WAAW,CAAC;UACtC,CAAC,CAAC;UAEF3B,SAAS,CAACrF,WAAW,CAAC6G,WAAW,CAAC;;QAGpC1B,UAAU,CAACnF,WAAW,CAACqF,SAAS,CAAC;MACnC,CAAC,CAAC;MAEFJ,aAAa,CAACjF,WAAW,CAACmF,UAAU,CAAC;;IAGvC;IACA,IAAIjI,MAAM,CAACoD,KAAK,IAAIpD,MAAM,CAACoD,KAAK,CAAC,CAAC,CAAC,IAAIpD,MAAM,CAACoD,KAAK,CAAC,CAAC,CAAC,CAAC6G,QAAQ,IAAIjK,MAAM,CAACoD,KAAK,CAAC,CAAC,CAAC,CAAC6G,QAAQ,CAAC5G,MAAM,GAAG,CAAC,EAAE;MACtG,MAAM6G,eAAe,GAAG,IAAI,CAAC/G,aAAa,CAAC,UAAU,EAAE,mBAAmB,CAAC;MAE3E,MAAMgH,YAAY,GAAGjK,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;MACjDgK,YAAY,CAAC/J,KAAK,CAACwJ,SAAS,GAAG,MAAM;MACrCO,YAAY,CAAC/J,KAAK,CAACY,OAAO,GAAG,GAAG;MAChCmJ,YAAY,CAAC/J,KAAK,CAACwC,MAAM,GAAG,GAAG;MAC/BuH,YAAY,CAAC/J,KAAK,CAACQ,OAAO,GAAG,MAAM;MACnCuJ,YAAY,CAAC/J,KAAK,CAACsI,mBAAmB,GAAG,uCAAuC;MAChFyB,YAAY,CAAC/J,KAAK,CAAC6C,GAAG,GAAG,MAAM;MAE/BjD,MAAM,CAACoD,KAAK,CAAC,CAAC,CAAC,CAAC6G,QAAQ,CAACxD,OAAO,CAAC2D,OAAO,IAAG;QACzC,MAAMC,WAAW,GAAGnK,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;QAChDkK,WAAW,CAACjK,KAAK,CAACY,OAAO,GAAG,MAAM;QAClCqJ,WAAW,CAACjK,KAAK,CAACM,eAAe,GAAG,SAAS;QAC7C2J,WAAW,CAACjK,KAAK,CAACa,YAAY,GAAG,KAAK;QACtCoJ,WAAW,CAAC5I,SAAS,GAAG,2EAA2E2I,OAAO,CAACvG,IAAI,IAAI,SAAS,EAAE;QAC9HsG,YAAY,CAACrH,WAAW,CAACuH,WAAW,CAAC;MACvC,CAAC,CAAC;MAEFH,eAAe,CAACpH,WAAW,CAACqH,YAAY,CAAC;MACzCpH,gBAAgB,CAACD,WAAW,CAACoH,eAAe,CAAC;;IAG/C;IACAnH,gBAAgB,CAACD,WAAW,CAACI,WAAW,CAAC;IACzCH,gBAAgB,CAACD,WAAW,CAACiC,YAAY,CAAC;IAC1ChC,gBAAgB,CAACD,WAAW,CAACiF,aAAa,CAAC;IAE3C;IACAhH,YAAY,CAAC+B,WAAW,CAACtB,WAAW,CAAC;IACrCT,YAAY,CAAC+B,WAAW,CAACT,MAAM,CAAC;IAChCtB,YAAY,CAAC+B,WAAW,CAACC,gBAAgB,CAAC;IAC1C9C,QAAQ,CAAC6C,WAAW,CAAC/B,YAAY,CAAC;IAElC;IACAb,QAAQ,CAACiC,IAAI,CAACW,WAAW,CAAC7C,QAAQ,CAAC;EACrC;EAEA;EACQkD,aAAaA,CAACT,KAAa,EAAE4H,SAAiB;IACpD,MAAMC,OAAO,GAAGrK,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7CoK,OAAO,CAACnK,KAAK,CAACM,eAAe,GAAG,SAAS;IACzC6J,OAAO,CAACnK,KAAK,CAACa,YAAY,GAAG,KAAK;IAClCsJ,OAAO,CAACnK,KAAK,CAACY,OAAO,GAAG,MAAM;IAC9BuJ,OAAO,CAACnK,KAAK,CAACiB,SAAS,GAAG,+BAA+B;IAEzD,MAAMmJ,aAAa,GAAGtK,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACnDqK,aAAa,CAACpK,KAAK,CAACQ,OAAO,GAAG,MAAM;IACpC4J,aAAa,CAACpK,KAAK,CAACU,UAAU,GAAG,QAAQ;IACzC0J,aAAa,CAACpK,KAAK,CAACkC,YAAY,GAAG,MAAM;IAEzC,MAAMmI,IAAI,GAAGvK,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCsK,IAAI,CAACC,SAAS,GAAG,OAAOJ,SAAS,EAAE;IACnCG,IAAI,CAACrK,KAAK,CAACkB,KAAK,GAAG,SAAS;IAC5BmJ,IAAI,CAACrK,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAC5B4I,IAAI,CAACrK,KAAK,CAAC0D,WAAW,GAAG,MAAM;IAE/B,MAAM6G,YAAY,GAAGzK,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IACjDwK,YAAY,CAAChI,WAAW,GAAGD,KAAK;IAChCiI,YAAY,CAACvK,KAAK,CAACwC,MAAM,GAAG,GAAG;IAC/B+H,YAAY,CAACvK,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpC8I,YAAY,CAACvK,KAAK,CAACyC,UAAU,GAAG,KAAK;IAErC2H,aAAa,CAAC1H,WAAW,CAAC2H,IAAI,CAAC;IAC/BD,aAAa,CAAC1H,WAAW,CAAC6H,YAAY,CAAC;IACvCJ,OAAO,CAACzH,WAAW,CAAC0H,aAAa,CAAC;IAElC,OAAOD,OAAO;EAChB;EAEA;EACQpG,aAAaA,CAAC3G,KAAa,EAAEF,KAAa;IAChD,MAAMsN,GAAG,GAAG1K,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACzCyK,GAAG,CAACxK,KAAK,CAACkC,YAAY,GAAG,MAAM;IAE/B,MAAMuI,YAAY,GAAG3K,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClD0K,YAAY,CAAClI,WAAW,GAAGnF,KAAK;IAChCqN,YAAY,CAACzK,KAAK,CAACyB,QAAQ,GAAG,MAAM;IACpCgJ,YAAY,CAACzK,KAAK,CAACkB,KAAK,GAAG,MAAM;IACjCuJ,YAAY,CAACzK,KAAK,CAACkC,YAAY,GAAG,KAAK;IAEvC,MAAMwI,YAAY,GAAG5K,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClD2K,YAAY,CAACnI,WAAW,GAAGrF,KAAK;IAChCwN,YAAY,CAAC1K,KAAK,CAACyB,QAAQ,GAAG,MAAM;IAEpC+I,GAAG,CAAC9H,WAAW,CAAC+H,YAAY,CAAC;IAC7BD,GAAG,CAAC9H,WAAW,CAACgI,YAAY,CAAC;IAE7B,OAAOF,GAAG;EACZ;EAEA;EACAG,gBAAgBA,CAAC/K,MAAc;IAC7B,IAAIA,MAAM,IAAIA,MAAM,CAACgI,MAAM,IAAIhI,MAAM,CAACgI,MAAM,CAAC3E,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAIsF,OAAO,GAAG3I,MAAM,CAACgI,MAAM,CAAC,CAAC,CAAC,CAACW,OAAO,IAAI3I,MAAM,CAACgI,MAAM,CAAC,CAAC,CAAC,CAACY,EAAE;MAE7D;MACA,IAAIoC,QAAQ,GAAG,IAAI,CAAC5N,YAAY;MAEhC;MACA,IAAI,CAAC4N,QAAQ,EAAE;QACbA,QAAQ,GAAGhL,MAAM,CAAC4I,EAAE;;MAGtB/I,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEkL,QAAQ,EAAE,cAAc,EAAErC,OAAO,CAAC;MAExF;MACA,IAAI,CAAC9L,MAAM,CAACoO,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;QACnCC,WAAW,EAAE;UACXF,QAAQ,EAAEA,QAAQ;UAClBrC,OAAO,EAAEA;;OAEZ,CAAC;KACH,MAAM;MACL9I,OAAO,CAACsL,KAAK,CAAC,sCAAsC,EAAEnL,MAAM,CAAC;;EAEjE;EAEAJ,gBAAgBA,CAAA;IACd;IACA;IACA;EAAA;EAGFD,iBAAiBA,CAAA;IACf;IACA,MAAMlB,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAACiN,GAAG,CAAC,uBAAuB,CAAC,EAAE9N,KAAK,IAAI,CAAC;IACtF,MAAMqB,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAACiN,GAAG,CAAC,qBAAqB,CAAC,EAAE9N,KAAK,IAAI,CAAC;IAElF,IAAI,CAACV,cAAc,CAACyO,kBAAkB,CAAC5M,qBAAqB,CAAC,CAAC6M,SAAS,CAACC,SAAS,IAAG;MAClF,IAAI,CAACzO,kBAAkB,GAAGyO,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAAC3O,cAAc,CAACyO,kBAAkB,CAAC1M,mBAAmB,CAAC,CAAC2M,SAAS,CAACC,SAAS,IAAG;MAChF,IAAI,CAACxO,gBAAgB,GAAGwO,SAAS;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAACpN,UAAU,CAACiN,GAAG,CAAC,uBAAuB,CAAC,EAAEI,YAAY,CACvDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAAC7O,cAAc,CAACyO,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAACzO,kBAAkB,GAAGyO,SAAS;QACnC;QACA,IAAI,CAACpN,UAAU,CAACiN,GAAG,CAAC,mBAAmB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAACvN,UAAU,CAACiN,GAAG,CAAC,qBAAqB,CAAC,EAAEI,YAAY,CACrDF,SAAS,CAACG,YAAY,IAAG;MACxB,IAAI,CAAC7O,cAAc,CAACyO,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;QACzE,IAAI,CAACxO,gBAAgB,GAAGwO,SAAS;QACjC;QACA,IAAI,CAACpN,UAAU,CAACiN,GAAG,CAAC,iBAAiB,CAAC,EAAEM,QAAQ,CAAC,EAAE,CAAC;MACtD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEJ;IACA,IAAI,CAACvN,UAAU,CAACiN,GAAG,CAAC,mBAAmB,CAAC,EAAEI,YAAY,CACnDG,IAAI,CACH1P,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACmB,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMmO,YAAY,GAAG,IAAI,CAACtN,UAAU,CAACiN,GAAG,CAAC,uBAAuB,CAAC,EAAE9N,KAAK,IAAI,CAAC;QAC7E;QACA,IAAIA,KAAK,CAAC+F,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACzG,cAAc,CAACyO,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9DvP,GAAG,CAACmP,SAAS,IAAIA,SAAS,CAACK,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAAChI,IAAI,CAACiI,WAAW,EAAE,CAACC,QAAQ,CAACzO,KAAK,CAACwO,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACjH,IAAI,IAAIiH,QAAQ,CAACjH,IAAI,CAACkH,WAAW,EAAE,CAACC,QAAQ,CAACzO,KAAK,CAACwO,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAClP,cAAc,CAACyO,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAOpP,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAiP,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACzO,kBAAkB,GAAGyO,SAAS;IACrC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACpN,UAAU,CAACiN,GAAG,CAAC,iBAAiB,CAAC,EAAEI,YAAY,CACjDG,IAAI,CACH1P,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACmB,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMmO,YAAY,GAAG,IAAI,CAACtN,UAAU,CAACiN,GAAG,CAAC,qBAAqB,CAAC,EAAE9N,KAAK,IAAI,CAAC;QAC3E;QACA,IAAIA,KAAK,CAAC+F,MAAM,GAAG,CAAC,EAAE;UACpB;UACA,OAAO,IAAI,CAACzG,cAAc,CAACyO,kBAAkB,CAACI,YAAY,CAAC,CAACE,IAAI,CAC9DvP,GAAG,CAACmP,SAAS,IAAIA,SAAS,CAACK,MAAM,CAACC,QAAQ,IACxCA,QAAQ,CAAChI,IAAI,CAACiI,WAAW,EAAE,CAACC,QAAQ,CAACzO,KAAK,CAACwO,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACjH,IAAI,IAAIiH,QAAQ,CAACjH,IAAI,CAACkH,WAAW,EAAE,CAACC,QAAQ,CAACzO,KAAK,CAACwO,WAAW,EAAE,CAAE,CAC7E,CAAC,CACH;SACF,MAAM;UACL;UACA,OAAO,IAAI,CAAClP,cAAc,CAACyO,kBAAkB,CAACI,YAAY,CAAC;;;MAG/D,OAAOpP,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAiP,SAAS,CAACC,SAAS,IAAG;MACrB,IAAI,CAACxO,gBAAgB,GAAGwO,SAAS;IACnC,CAAC,CAAC;EACN;EAEAS,eAAeA,CAACH,QAA+B;IAC7C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAII,WAAW,GAAGJ,QAAQ,CAAChI,IAAI;IAC/B,IAAIgI,QAAQ,CAACjH,IAAI,EAAE;MACjBqH,WAAW,IAAI,KAAKJ,QAAQ,CAACjH,IAAI,GAAG;;IAEtC,IAAIiH,QAAQ,CAACK,IAAI,KAAK3P,YAAY,CAAC4P,OAAO,IAAIN,QAAQ,CAAChG,IAAI,EAAE;MAC3DoG,WAAW,IAAI,MAAMJ,QAAQ,CAAChG,IAAI,EAAE;;IAEtC,OAAOoG,WAAW;EACpB;EAEAG,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjO,UAAU,CAACkO,OAAO,EAAE;MAC3B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACnO,UAAU,CAAC;MAC1C;;IAGF,IAAI,CAACnB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACG,YAAY,GAAG,EAAE;IACtB,IAAI,CAACD,WAAW,GAAG,IAAI;IAEvB,MAAMqP,SAAS,GAAG,IAAI,CAACpO,UAAU,CAACb,KAAK;IAEvC;IACA,MAAMkP,OAAO,GAAuB;MAClCC,WAAW,EAAEF,SAAS,CAAClO,WAAW;MAClCqO,YAAY,EAAEH,SAAS,CAAChO,YAAY;MACpCoO,OAAO,EAAEJ,SAAS,CAAC3N,aAAa;MAChCgO,kBAAkB,EAAE,CAClB;QACEhE,EAAE,EAAE2D,SAAS,CAAC/N,iBAAiB,EAAEoK,EAAE,IAAI,EAAE;QACzCsD,IAAI,EAAEK,SAAS,CAAC9N;OACjB,CACF;MACDoO,gBAAgB,EAAE,CAChB;QACEjE,EAAE,EAAE2D,SAAS,CAAC7N,eAAe,EAAEkK,EAAE,IAAI,EAAE;QACvCsD,IAAI,EAAEK,SAAS,CAAC5N;OACjB,CACF;MACDmO,UAAU,EAAE,CACV;QACEZ,IAAI,EAAEK,SAAS,CAACvN,aAAa;QAC7B+N,KAAK,EAAER,SAAS,CAAC1N;OAClB,CACF;MACDmO,qBAAqB,EAAET,SAAS,CAACrN,OAAO;MACxC+N,oBAAoB,EAAE;QACpBC,oBAAoB,EAAE;UACpBzN,sBAAsB,EAAE8M,SAAS,CAAC9M;;OAErC;MACDJ,sBAAsB,EAAEkN,SAAS,CAAClN,sBAAsB;MACxDC,wBAAwB,EAAEiN,SAAS,CAACjN,wBAAwB;MAC5DC,6BAA6B,EAAEgN,SAAS,CAAChN,6BAA6B;MACtEC,mBAAmB,EAAE+M,SAAS,CAAC/M,mBAAmB;MAClD7B,aAAa,EAAE,CAAC4O,SAAS,CAACtN,WAAW,CAAC;MACtCkO,OAAO,EAAEZ,SAAS,CAACpN,OAAO;MAC1BiO,QAAQ,EAAEb,SAAS,CAACnN;KACrB;IAED,IAAI,CAACxC,cAAc,CAACyQ,WAAW,CAACb,OAAO,CAAC,CACrClB,SAAS,CAAC;MACTgC,IAAI,EAAGC,QAA6B,IAAI;QACtC,IAAI,CAACvQ,SAAS,GAAG,KAAK;QACtB,IAAIuQ,QAAQ,CAAClL,MAAM,CAACmL,OAAO,EAAE;UAC3B,IAAI,CAACvQ,aAAa,GAAGsQ,QAAQ,CAACpL,IAAI,CAACsL,OAAO;UAE1C;UACA5N,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4N,IAAI,CAACC,SAAS,CAACJ,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEpE;UACA,IAAIA,QAAQ,CAACpL,IAAI,IAAIoL,QAAQ,CAACpL,IAAI,CAACsL,OAAO,IAAIF,QAAQ,CAACpL,IAAI,CAACsL,OAAO,CAACpK,MAAM,GAAG,CAAC,EAAE;YAC9ExD,OAAO,CAACzB,KAAK,CAAC,uBAAuB,CAAC;YACtCyB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEyN,QAAQ,CAACpL,IAAI,CAACsL,OAAO,CAACpK,MAAM,CAAC;YAE3D;YACA,MAAMuK,iBAAiB,GAAGL,QAAQ,CAACpL,IAAI,CAACsL,OAAO,CAAC7B,MAAM,CAACiC,CAAC,IAAIA,CAAC,CAAC7F,MAAM,IAAI6F,CAAC,CAAC7F,MAAM,CAAC3E,MAAM,GAAG,CAAC,CAAC;YAC5FxD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8N,iBAAiB,CAACvK,MAAM,CAAC;YAE7D;YACA,MAAMyK,kBAAkB,GAAGF,iBAAiB,CAACG,OAAO,CAACF,CAAC,IAAIA,CAAC,CAAC7F,MAAM,CAAC5L,GAAG,CAAC4R,CAAC,IACtEA,CAAC,CAACjF,YAAY,KAAKC,SAAS,GAAGgF,CAAC,CAACjF,YAAY,GAAIiF,CAAC,CAAC/E,QAAQ,GAAG+E,CAAC,CAAC/E,QAAQ,CAACC,kBAAkB,GAAG,CAAE,CACjG,CAAC;YACFrJ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgO,kBAAkB,CAAC;YAEvD;YACA,MAAMG,kBAAkB,GAAGH,kBAAkB,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAI;cAChE,IAAIA,GAAG,KAAKpF,SAAS,EAAE;gBACrBmF,GAAG,CAACC,GAAG,CAAC,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEhC,OAAOD,GAAG;YACZ,CAAC,EAAE,EAA4B,CAAC;YAChCtO,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmO,kBAAkB,CAAC;YAEvD;YACA,MAAMI,iBAAiB,GAAGT,iBAAiB,CAAChC,MAAM,CAACiC,CAAC,IAClDA,CAAC,CAAC7F,MAAM,CAACsG,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACzE,cAAc,IAAIyE,CAAC,CAACzE,cAAc,CAACC,UAAU,KAAK,IAAI,CAAC,CAC7E;YACD3J,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuO,iBAAiB,CAAChL,MAAM,CAAC;YAE5DxD,OAAO,CAAC0O,QAAQ,EAAE;;UAGpB;UACA,IAAIhB,QAAQ,CAACpL,IAAI,IAAIoL,QAAQ,CAACpL,IAAI,CAAC6I,QAAQ,EAAE;YAC3C,IAAI,CAAC5N,YAAY,GAAGmQ,QAAQ,CAACpL,IAAI,CAAC6I,QAAQ;YAC1CnL,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC1C,YAAY,CAAC;;UAErE;UAAA,KACK,IAAImQ,QAAQ,CAAClL,MAAM,IAAIkL,QAAQ,CAAClL,MAAM,CAACmM,SAAS,EAAE;YACrD,IAAI,CAACpR,YAAY,GAAGmQ,QAAQ,CAAClL,MAAM,CAACmM,SAAS;YAC7C3O,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC1C,YAAY,CAAC;;UAExE;UAAA,KACK,IAAImQ,QAAQ,CAACpL,IAAI,IAAIoL,QAAQ,CAACpL,IAAI,CAACsL,OAAO,IAAIF,QAAQ,CAACpL,IAAI,CAACsL,OAAO,CAACpK,MAAM,GAAG,CAAC,IAAIkK,QAAQ,CAACpL,IAAI,CAACsL,OAAO,CAAC,CAAC,CAAC,CAAC7E,EAAE,EAAE;YAClH,IAAI,CAACxL,YAAY,GAAGmQ,QAAQ,CAACpL,IAAI,CAACsL,OAAO,CAAC,CAAC,CAAC,CAAC7E,EAAE;YAC/C/I,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC1C,YAAY,CAAC;WAChE,MAAM;YACLyC,OAAO,CAACsL,KAAK,CAAC,qCAAqC,CAAC;YACpDtL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE2O,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAC,CAAC;YACzD,IAAIA,QAAQ,CAACpL,IAAI,EAAEtC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE2O,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAACpL,IAAI,CAAC,CAAC;YAC7E,IAAIoL,QAAQ,CAAClL,MAAM,EAAExC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2O,MAAM,CAACC,IAAI,CAACnB,QAAQ,CAAClL,MAAM,CAAC,CAAC;;SAEtF,MAAM;UACL,IAAI,CAAClF,YAAY,GAAG,sDAAsD;UAC1E,IAAIoQ,QAAQ,CAAClL,MAAM,CAACsM,QAAQ,IAAIpB,QAAQ,CAAClL,MAAM,CAACsM,QAAQ,CAACtL,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAAClG,YAAY,GAAGoQ,QAAQ,CAAClL,MAAM,CAACsM,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO;;;MAG7D,CAAC;MACDzD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnO,SAAS,GAAG,KAAK;QACtB,IAAI,CAACG,YAAY,GAAG,wDAAwD;QAC5E0C,OAAO,CAACsL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACN;EAEA;EACAmB,oBAAoBA,CAACuC,SAAoB;IACvCJ,MAAM,CAACK,MAAM,CAACD,SAAS,CAACE,QAAQ,CAAC,CAACtI,OAAO,CAACuI,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAYjT,SAAS,EAAE;QAChC,IAAI,CAACuQ,oBAAoB,CAAC0C,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA;EACAvK,cAAcA,CAACyK,OAAe;IAC5B,MAAMC,KAAK,GAAGtH,IAAI,CAACC,KAAK,CAACoH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,KAAK,KAAKC,IAAI,KAAK;EAC/B;EAEA;EACAC,UAAUA,CAACC,UAAkB;IAC3B,MAAMjK,IAAI,GAAG,IAAIrH,IAAI,CAACsR,UAAU,CAAC;IACjC,OAAOjK,IAAI,CAACd,kBAAkB,CAAC,OAAO,EAAE;MACtCgL,OAAO,EAAE,OAAO;MAChBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACdlK,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACAkK,WAAWA,CAAC1P,MAAc;IACxB,IAAI,CAACA,MAAM,CAACgI,MAAM,IAAIhI,MAAM,CAACgI,MAAM,CAAC3E,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd,MAAMsM,QAAQ,GAAG3P,MAAM,CAACgI,MAAM,CAACkG,MAAM,CAAC,CAACpP,GAAG,EAAEoJ,KAAK,KAC/CA,KAAK,CAACK,KAAK,CAACC,MAAM,GAAG1J,GAAG,CAACyJ,KAAK,CAACC,MAAM,GAAGN,KAAK,GAAGpJ,GAAG,EAAEkB,MAAM,CAACgI,MAAM,CAAC,CAAC,CAAC,CAAC;IAExE,OAAO2H,QAAQ,CAACpH,KAAK,CAACqH,eAAe,IAAI,GAAGD,QAAQ,CAACpH,KAAK,CAACC,MAAM,IAAImH,QAAQ,CAACpH,KAAK,CAACnJ,QAAQ,EAAE;EAChG;EAEA;EACAyQ,iBAAiBA,CAAC7P,MAAc;IAC9B;IACA,IAAI,CAACA,MAAM,CAACgI,MAAM,IAAIhI,MAAM,CAACgI,MAAM,CAAC3E,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;;IAGd;IACA,MAAM6E,KAAK,GAAGlI,MAAM,CAACgI,MAAM,CAAC,CAAC,CAAC;IAC9B,MAAMc,iBAAiB,GAAGZ,KAAK,CAACa,YAAY,KAAKC,SAAS,GAAGd,KAAK,CAACa,YAAY,GACrDb,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACC,kBAAkB,GAAG,CAAE;IACjF,OAAOJ,iBAAiB,GAAG,CAAC;EAC9B;EAEA;EACAgH,yBAAyBA,CAAA;IACvB,MAAMrE,YAAY,GAAG,IAAI,CAACtN,UAAU,CAACiN,GAAG,CAAC,uBAAuB,CAAC,EAAE9N,KAAK,IAAI,CAAC;IAC7E,IAAI,CAACV,cAAc,CAACyO,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACzO,kBAAkB,GAAGyO,SAAS;MACnC;MACA,MAAMwE,KAAK,GAAG7P,QAAQ,CAAC8P,cAAc,CAAC,mBAAmB,CAAqB;MAC9E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAC,uBAAuBA,CAAA;IACrB,MAAM3E,YAAY,GAAG,IAAI,CAACtN,UAAU,CAACiN,GAAG,CAAC,qBAAqB,CAAC,EAAE9N,KAAK,IAAI,CAAC;IAC3E,IAAI,CAACV,cAAc,CAACyO,kBAAkB,CAACI,YAAY,CAAC,CAACH,SAAS,CAACC,SAAS,IAAG;MACzE,IAAI,CAACxO,gBAAgB,GAAGwO,SAAS;MACjC;MACA,MAAMwE,KAAK,GAAG7P,QAAQ,CAAC8P,cAAc,CAAC,iBAAiB,CAAqB;MAC5E,IAAID,KAAK,EAAE;QACTA,KAAK,CAACE,KAAK,EAAE;QACbF,KAAK,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;;IAE3C,CAAC,CAAC;EACJ;EAEA;EACAE,aAAaA,CAAA;IACX,MAAM7R,iBAAiB,GAAG,IAAI,CAACL,UAAU,CAACiN,GAAG,CAAC,mBAAmB,CAAC,EAAE9N,KAAK;IACzE,MAAMmB,qBAAqB,GAAG,IAAI,CAACN,UAAU,CAACiN,GAAG,CAAC,uBAAuB,CAAC,EAAE9N,KAAK;IACjF,MAAMoB,eAAe,GAAG,IAAI,CAACP,UAAU,CAACiN,GAAG,CAAC,iBAAiB,CAAC,EAAE9N,KAAK;IACrE,MAAMqB,mBAAmB,GAAG,IAAI,CAACR,UAAU,CAACiN,GAAG,CAAC,qBAAqB,CAAC,EAAE9N,KAAK;IAE7E,IAAI,CAACa,UAAU,CAACmS,UAAU,CAAC;MACzB9R,iBAAiB,EAAEE,eAAe;MAClCD,qBAAqB,EAAEE,mBAAmB;MAC1CD,eAAe,EAAEF,iBAAiB;MAClCG,mBAAmB,EAAEF;KACtB,CAAC;EACJ;EAEA;EACA8R,oBAAoBA,CAACjB,UAAkB;IACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B;IACA,MAAMjK,IAAI,GAAG,IAAIrH,IAAI,CAACsR,UAAU,CAAC;IACjC,OAAOjK,IAAI,CAACgE,cAAc,EAAE;EAC9B;EAEA;EACAU,kBAAkBA,CAACC,WAAmB;IACpC,QAAQA,WAAW;MACjB,KAAK,CAAC;QAAE,OAAO,eAAe;MAC9B,KAAK,CAAC;QAAE,OAAO,iBAAiB;MAChC,KAAK,CAAC;QAAE,OAAO,cAAc;MAC7B;QAAS,OAAO,SAAS;;EAE7B;EAEA;EACAwG,oBAAoBA,CAACxR,aAAqB;IACxC,QAAQA,aAAa;MACnB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,WAAW;;EAE/B;EAEA;EACAyR,oBAAoBA,CAACC,cAAmB,EAAEC,WAAgB;IACxD,IAAI,CAACD,cAAc,IAAI,CAACA,cAAc,CAACxK,OAAO,IAAI,CAACwK,cAAc,CAACxK,OAAO,CAACb,IAAI,IAC1E,CAACsL,WAAW,IAAI,CAACA,WAAW,CAAC1L,SAAS,IAAI,CAAC0L,WAAW,CAAC1L,SAAS,CAACI,IAAI,EAAE;MACzE,OAAO,SAAS;;IAGlB,MAAMc,WAAW,GAAG,IAAInI,IAAI,CAAC0S,cAAc,CAACxK,OAAO,CAACb,IAAI,CAAC,CAACqC,OAAO,EAAE;IACnE,MAAMtC,aAAa,GAAG,IAAIpH,IAAI,CAAC2S,WAAW,CAAC1L,SAAS,CAACI,IAAI,CAAC,CAACqC,OAAO,EAAE;IACpE,MAAMkJ,MAAM,GAAGxL,aAAa,GAAGe,WAAW;IAC1C,MAAM0K,QAAQ,GAAGhJ,IAAI,CAACC,KAAK,CAAC8I,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIC,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,KAAK;KACxB,MAAM;MACL,MAAM1B,KAAK,GAAGtH,IAAI,CAACC,KAAK,CAAC+I,QAAQ,GAAG,EAAE,CAAC;MACvC,MAAMzB,IAAI,GAAGyB,QAAQ,GAAG,EAAE;MAC1B,OAAO,GAAG1B,KAAK,KAAKC,IAAI,KAAK;;EAEjC;CACD;AAxgCY3S,oBAAoB,GAAAqU,UAAA,EANhCjV,SAAS,CAAC;EACTkV,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,8BAA8B,EAAE,mBAAmB,CAAC;EAChEC,aAAa,EAAEpV,iBAAiB,CAACqV;CAClC,CAAC,C,EACW1U,oBAAoB,CAwgChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}