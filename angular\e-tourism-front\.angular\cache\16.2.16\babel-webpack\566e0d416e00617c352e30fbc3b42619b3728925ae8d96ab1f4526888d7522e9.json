{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n// Material Modules\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { AccueilComponent } from './components/accueil/accueil.component';\nimport { SearchPriceComponent } from './components/product/search-price/search-price.component';\nimport { GetOfferComponent } from './components/product/get-offer/get-offer.component';\nimport { BookingTransactionComponent } from './components/booking/booking-transaction/booking-transaction.component';\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\nimport { MainLayoutComponent } from './layout/main-layout/main-layout.component';\nimport { NavbarComponent } from './layout/navbar/navbar.component';\nimport { SidebarComponent } from './layout/sidebar/sidebar.component';\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, LoginComponent, AccueilComponent, SearchPriceComponent, GetOfferComponent, BookingTransactionComponent, MainLayoutComponent, NavbarComponent, SidebarComponent],\n  imports: [BrowserModule, AppRoutingModule, FormsModule, ReactiveFormsModule, HttpClientModule, BrowserAnimationsModule,\n  // Material Modules\n  MatAutocompleteModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatButtonModule, MatIconModule, MatCardModule, MatProgressSpinnerModule, MatProgressBarModule, MatToolbarModule, MatSidenavModule, MatListModule, MatMenuModule, MatDividerModule, MatTabsModule, MatChipsModule, MatBadgeModule, MatTooltipModule, MatSnackBarModule, MatExpansionModule, MatStepperModule],\n  providers: [{\n    provide: HTTP_INTERCEPTORS,\n    useClass: AuthInterceptor,\n    multi: true\n  }],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "FormsModule", "ReactiveFormsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "BrowserAnimationsModule", "MatAutocompleteModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatDatepickerModule", "MatNativeDateModule", "MatButtonModule", "MatIconModule", "MatCardModule", "MatProgressSpinnerModule", "MatProgressBarModule", "MatToolbarModule", "MatSidenavModule", "MatListModule", "MatMenuModule", "MatDividerModule", "MatTabsModule", "MatChipsModule", "MatBadgeModule", "MatTooltipModule", "MatSnackBarModule", "MatExpansionModule", "MatStepperModule", "AppRoutingModule", "AppComponent", "LoginComponent", "AccueilComponent", "SearchPriceComponent", "GetOfferComponent", "BookingTransactionComponent", "AuthInterceptor", "MainLayoutComponent", "NavbarComponent", "SidebarComponent", "AppModule", "__decorate", "declarations", "imports", "providers", "provide", "useClass", "multi", "bootstrap"], "sources": ["C:\\Users\\<USER>\\Desktop\\e-tourism\\angular\\e-tourism-front\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n\n// Material Modules\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatStepperModule } from '@angular/material/stepper';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { AccueilComponent } from './components/accueil/accueil.component';\nimport { SearchPriceComponent } from './components/product/search-price/search-price.component';\nimport { GetOfferComponent } from './components/product/get-offer/get-offer.component';\nimport { BookingTransactionComponent } from './components/booking/booking-transaction/booking-transaction.component';\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\nimport { MainLayoutComponent } from './layout/main-layout/main-layout.component';\nimport { NavbarComponent } from './layout/navbar/navbar.component';\nimport { SidebarComponent } from './layout/sidebar/sidebar.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    AccueilComponent,\n    SearchPriceComponent,\n    GetOfferComponent,\n    BookingTransactionComponent,\n    MainLayoutComponent,\n    NavbarComponent,\n    SidebarComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    HttpClientModule,\n    BrowserAnimationsModule,\n    // Material Modules\n    MatAutocompleteModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCardModule,\n    MatProgressSpinnerModule,\n    MatProgressBarModule,\n    MatToolbarModule,\n    MatSidenavModule,\n    MatListModule,\n    MatMenuModule,\n    MatDividerModule,\n    MatTabsModule,\n    MatChipsModule,\n    MatBadgeModule,\n    MatTooltipModule,\n    MatSnackBarModule,\n    MatExpansionModule,\n    MatStepperModule\n  ],\n  providers: [\n    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E;AACA,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,oBAAoB,QAAQ,0DAA0D;AAC/F,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,2BAA2B,QAAQ,wEAAwE;AACpH,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,mBAAmB,QAAQ,4CAA4C;AAChF,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,gBAAgB,QAAQ,oCAAoC;AAmD9D,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EAjDrB1C,QAAQ,CAAC;EACR2C,YAAY,EAAE,CACZZ,YAAY,EACZC,cAAc,EACdC,gBAAgB,EAChBC,oBAAoB,EACpBC,iBAAiB,EACjBC,2BAA2B,EAC3BE,mBAAmB,EACnBC,eAAe,EACfC,gBAAgB,CACjB;EACDI,OAAO,EAAE,CACP3C,aAAa,EACb6B,gBAAgB,EAChB5B,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChBE,uBAAuB;EACvB;EACAC,qBAAqB,EACrBC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,wBAAwB,EACxBC,oBAAoB,EACpBC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,iBAAiB,EACjBC,kBAAkB,EAClBC,gBAAgB,CACjB;EACDgB,SAAS,EAAE,CACT;IAAEC,OAAO,EAAEzC,iBAAiB;IAAE0C,QAAQ,EAAEV,eAAe;IAAEW,KAAK,EAAE;EAAI,CAAE,CACvE;EACDC,SAAS,EAAE,CAAClB,YAAY;CACzB,CAAC,C,EACWU,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}