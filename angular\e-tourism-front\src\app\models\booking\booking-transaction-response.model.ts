import { BeginTransactionResponse } from './begin-transaction-response.model';
import { CommitTransactionResponse } from './commit-transaction-response.model';
import { SetReservationInfoResponse } from './set-reservation-info-response.model';

/**
 * Main wrapper model for all booking transaction responses
 * Used for the /booking-transaction endpoint with action parameter
 */
export interface BookingTransactionResponse {
    action: 'begin' | 'info' | 'commit';
    beginResponse?: BeginTransactionResponse;
    infoResponse?: SetReservationInfoResponse;
    commitResponse?: CommitTransactionResponse;
}
