/**
 * Model for Commit Transaction Request
 * Used to finalize a booking transaction with payment information
 */
export interface CommitTransactionRequest {
    transactionId: string;
    paymentOption?: number;
    paymentInformation?: PaymentInformation;
    vccInformation?: VCCInformation;
}

export interface PaymentInformation {
    accountName?: string;
    paymentTypeId?: number;
    paymentPrice?: Price;
    installmentCount?: string;
    paymentDate?: string;
    receiptType?: string;
    reference?: string;
    paymentToken?: string;
}

export interface VCCInformation {
    vccNo: string;
    vccExpDate: string;
    vccSecNo: string;
    vccHolderName: string;
}

export interface Price {
    amount: number;
    currency: string;
}
